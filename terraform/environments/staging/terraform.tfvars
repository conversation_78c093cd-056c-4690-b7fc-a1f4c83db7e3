# Staging Environment Configuration

# Core infrastructure settings
# project_id is supplied by GitHub Actions via TF_VAR_project_id environment variable
# project_number is supplied by GitHub Actions via TF_VAR_project_number environment variable
region      = "us-central1"
zone        = "us-central1-a"
root_domain = "vedavivi.app"

# backend_origin_url will be provided during deployment via TF_VAR_backend_origin_url
# websocket_origin_url will be provided during deployment via TF_VAR_websocket_origin_url
# cloudflare_api_token will be provided securely during deployment via TF_VAR_cloudflare_api_token
# cloudflare_account_id will be provided securely during deployment via TF_VAR_cloudflare_account_id

# Resource creation control (can be overridden via environment variables)
# TF_VAR_create_dns_records - Set to true/false to control DNS record creation
# TF_VAR_create_page_rules - Set to true/false to control page rule creation
# Defaults: create_dns_records=true, create_page_rules=false for staging

# Cloudflare zone configuration for staging
zone_config = {
  jump_start = false  # Don't auto-import DNS records for staging
  paused     = false  # Zone is active
  type       = "full" # Full zone management
}

# CDN Cache Configuration for Staging
# Staging environment uses more aggressive caching for testing
cache_config = {
  # Frontend static assets - Long cache for performance testing
  static_assets_edge_ttl    = 86400 # 1 day (shorter than production for testing)
  static_assets_browser_ttl = 86400 # 1 day

  # HTML files - Minimum TTL for Free plan compliance
  html_edge_ttl    = 7200 # 2 hours (Free plan minimum)
  html_browser_ttl = 7200 # 2 hours (Free plan minimum)

  # API caching - DISABLED for staging (Free plan limitations)
  api_cache_enabled    = false      # Disabled due to Free plan advanced feature limitations
  api_edge_ttl         = 120        # 2 minutes (Free plan minimum)
  api_browser_ttl      = 0          # No browser cache for APIs
  api_cache_by_device  = false      # Not available on Free plan
  api_cache_key_fields = ["accept"] # Simplified for Free plan

  # GraphQL caching - DISABLED for staging (Free plan limitations)
  graphql_cache_enabled   = false # Disabled due to Free plan advanced feature limitations
  graphql_edge_ttl        = 120   # 2 minutes (Free plan minimum)
  graphql_browser_ttl     = 0     # No browser cache for GraphQL
  graphql_cache_by_device = false # Simple caching for staging

  # Global settings
  development_mode  = false # Enable caching in staging
  cache_level       = "aggressive"
  browser_cache_ttl = 3600 # 1 hour default browser cache
}