variable "project_id" {
  description = "The Google Cloud project ID"
  type        = string
}

variable "region" {
  description = "The default region for resources"
  type        = string
  default     = "us-central1"
}

variable "root_domain" {
  description = "The root domain for the application (e.g., vedavivi.app)"
  type        = string
}

variable "backend_origin_url" {
  description = "The origin URL for backend API services (e.g., Cloud Run instance)"
  type        = string
}

variable "websocket_origin_url" {
  description = "The origin URL for WebSocket connections (e.g., Cloud Run instance)"
  type        = string
}

variable "cloudflare_api_token" {
  description = "Cloudflare API token with Zone:Edit permissions"
  type        = string
  sensitive   = true
}

variable "cloudflare_account_id" {
  description = "Cloudflare account ID"
  type        = string
  sensitive   = true
}

variable "cloudflare_zone_id" {
  description = "Existing Cloudflare Zone ID. If provided, will use existing zone instead of creating new one."
  type        = string
  default     = ""
}

variable "zone_config" {
  description = "Cloudflare zone configuration settings"
  type = object({
    jump_start = optional(bool, false)    # Whether to automatically attempt to fetch existing DNS records
    paused     = optional(bool, false)    # Whether the zone is paused (traffic bypasses Cloudflare)
    type       = optional(string, "full") # Zone type: full, partial, secondary
  })
  default = {}
}

variable "create_dns_records" {
  description = "Whether to create DNS records. Can be overridden via TF_VAR_create_dns_records environment variable."
  type        = bool
  default     = false # Default to not creating DNS records for production (import existing ones)
}

variable "create_page_rules" {
  description = "Whether to create page rules. Can be overridden via TF_VAR_create_page_rules environment variable."
  type        = bool
  default     = false # Default to not creating page rules (import existing ones)
}

# Core Infrastructure Resource Creation Control
variable "create_network_resources" {
  description = "Whether to create network resources (VPC, subnets). Can be overridden via TF_VAR_create_network_resources environment variable."
  type        = bool
  default     = false # Default to using existing network resources
}

variable "create_database_resources" {
  description = "Whether to create database resources (Cloud SQL). Can be overridden via TF_VAR_create_database_resources environment variable."
  type        = bool
  default     = false # Default to using existing database resources
}

variable "create_redis_resources" {
  description = "Whether to create Redis resources (Memorystore). Can be overridden via TF_VAR_create_redis_resources environment variable."
  type        = bool
  default     = false # Default to using existing Redis resources
}

variable "create_storage_resources" {
  description = "Whether to create storage resources (KMS, buckets). Can be overridden via TF_VAR_create_storage_resources environment variable."
  type        = bool
  default     = false # Default to using existing storage resources
}

# Core Infrastructure Variables
variable "zone" {
  description = "The default zone for zonal resources"
  type        = string
  default     = "us-central1-a"
}

variable "project_number" {
  description = "The Google Cloud project number"
  type        = string
}

# Import shared cache configuration variables
# This reduces duplication and ensures consistency across environments
