# Production Environment Configuration

# Core infrastructure settings
# project_id is supplied by GitHub Actions via TF_VAR_project_id environment variable
# project_number is supplied by GitHub Actions via TF_VAR_project_number environment variable
region      = "us-central1"
zone        = "us-central1-a"
root_domain = "vedavivi.app" # Main domain for production

# backend_origin_url will be provided during deployment via TF_VAR_backend_origin_url
# websocket_origin_url will be provided during deployment via TF_VAR_websocket_origin_url
# cloudflare_api_token will be provided securely during deployment via TF_VAR_cloudflare_api_token
# cloudflare_account_id will be provided securely during deployment via TF_VAR_cloudflare_account_id

# Resource creation control (can be overridden via environment variables)
# TF_VAR_create_dns_records - Set to true/false to control DNS record creation
# TF_VAR_create_page_rules - Set to true/false to control page rule creation
# Defaults: create_dns_records=false, create_page_rules=false for production

# Cloudflare zone configuration for production
zone_config = {
  jump_start = true   # Auto-import existing DNS records for production
  paused     = false  # Zone is active
  type       = "full" # Full zone management
}

# CDN Cache Configuration for Production
# Production environment uses conservative caching for reliability
cache_config = {
  # Frontend static assets - Maximum cache for performance
  static_assets_edge_ttl    = ******** # 1 year
  static_assets_browser_ttl = ******** # 1 year

  # HTML files - Moderate cache for content updates
  html_edge_ttl    = 1800 # 30 minutes
  html_browser_ttl = 1800 # 30 minutes

  # API caching - DISABLED by default for production safety
  # Can be enabled via environment variables when needed
  api_cache_enabled    = false
  api_edge_ttl         = 300  # 5 minutes when enabled
  api_browser_ttl      = 0    # No browser cache for APIs
  api_cache_by_device  = true # Device-specific caching when enabled
  api_cache_key_fields = ["host", "authorization", "user-agent"]

  # GraphQL caching - DISABLED by default for production safety
  # Can be enabled via environment variables when needed
  graphql_cache_enabled   = false
  graphql_edge_ttl        = 60   # 1 minute when enabled
  graphql_browser_ttl     = 0    # No browser cache for GraphQL
  graphql_cache_by_device = true # Device-specific caching when enabled

  # Global settings
  development_mode  = false # Always disable in production
  cache_level       = "aggressive"
  browser_cache_ttl = 14400 # 4 hours default browser cache
}
