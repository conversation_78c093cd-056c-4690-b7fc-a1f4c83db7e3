# A2A Platform - Production Environment

terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 6.35.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = "~> 6.35.0"
    }
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 4.0"
    }
  }
  required_version = ">= 1.12.0"
}

provider "google" {
  project = var.project_id
  region  = var.region
}

provider "google-beta" {
  project = var.project_id
  region  = var.region
}

provider "cloudflare" {
  api_token = var.cloudflare_api_token
}

# Core Infrastructure Modules
# Network module for VPC and subnets
module "network" {
  source                   = "../../modules/network"
  project_id               = var.project_id
  region                   = var.region
  network_name             = "a2a-production-vpc"
  subnet_name              = "a2a-production-subnet"
  subnet_cidr              = "10.1.0.0/24"
  create_resources         = var.create_network_resources
  existing_resources_exist = !var.create_network_resources
  enable_cloud_nat         = true # Enable Cloud NAT for outbound internet access
}

# Database module for Cloud SQL
module "database" {
  source                   = "../../modules/database"
  project_id               = var.project_id
  region                   = var.region
  environment              = "production"
  db_tier                  = "db-g1-small"
  db_version               = "POSTGRES_14"
  db_name                  = "a2a-platform"
  create_resources         = var.create_database_resources
  existing_resources_exist = !var.create_database_resources
  depends_on               = [module.network]
}

# Redis module for caching and message broker
module "redis" {
  source                   = "../../modules/redis"
  project_id               = var.project_id
  region                   = var.region
  environment              = "production"
  memory_size_gb           = 2
  redis_version            = "REDIS_6_X"
  network_name             = module.network.network_name
  tier                     = "STANDARD_HA"
  kms_key_name             = module.storage.kms_key_id
  create_resources         = var.create_redis_resources
  existing_resources_exist = !var.create_redis_resources
  depends_on               = [module.network, module.storage]
}

# Storage module for KMS and buckets
module "storage" {
  source                   = "../../modules/storage"
  project_id               = var.project_id
  region                   = var.region
  environment              = "production"
  name_prefix              = "a2a-production"
  use_cmek                 = true
  safe_cmek                = true
  create_resources         = var.create_storage_resources
  module_create_resources  = var.create_storage_resources
  existing_resources_exist = !var.create_storage_resources
}

# Cloudflare R2 Storage for static assets
module "r2_storage" {
  source = "../../modules/cloudflare-r2"

  cloudflare_account_id = var.cloudflare_account_id
  cloudflare_zone_id    = var.cloudflare_zone_id
  bucket_name           = "a2a-platform-web-assets-production"
  environment           = "production"
  location              = "WNAM" # Western North America for optimal performance

  # Custom domain for R2 bucket access through CDN
  custom_domain = "assets"

  # CORS configuration for web application (production security)
  cors_origins = [
    "https://${var.root_domain}",
    "https://api.${var.root_domain}",
    "https://cloudflare.com",
    "https://*.cloudflare.com"
  ]

  # Optional: Create deployment token for CI/CD
  create_deploy_token = false # Managed separately for security

  tags = {
    Environment = "production"
    Project     = "a2a-platform"
    ManagedBy   = "terraform"
  }
}

# Local values for cache configuration with environment variable overrides
locals {
  # Merge base cache config with environment variable overrides
  effective_cache_config = merge(var.cache_config, {
    # Override API cache settings if environment variables are provided
    api_cache_enabled     = var.cache_api_enabled != "" ? tobool(var.cache_api_enabled) : var.cache_config.api_cache_enabled
    graphql_cache_enabled = var.cache_graphql_enabled != "" ? tobool(var.cache_graphql_enabled) : var.cache_config.graphql_cache_enabled
    development_mode      = var.cache_development_mode != "" ? tobool(var.cache_development_mode) : var.cache_config.development_mode

    # Override TTL settings if environment variables are provided
    static_assets_edge_ttl = var.cache_static_assets_ttl != "" ? tonumber(var.cache_static_assets_ttl) : var.cache_config.static_assets_edge_ttl
    html_edge_ttl          = var.cache_html_ttl != "" ? tonumber(var.cache_html_ttl) : var.cache_config.html_edge_ttl
    api_edge_ttl           = var.cache_api_ttl != "" ? tonumber(var.cache_api_ttl) : var.cache_config.api_edge_ttl
    graphql_edge_ttl       = var.cache_graphql_ttl != "" ? tonumber(var.cache_graphql_ttl) : var.cache_config.graphql_edge_ttl
  })
}

# Data source to get the current project number
data "google_project" "current" {
  project_id = var.project_id
}

# Cloudflare CDN with R2 integration
module "cloudflare_cdn" {
  source                = "../../modules/cloudflare-cdn"
  root_domain           = var.root_domain
  environment           = "production"
  storage_origin_url    = module.r2_storage.public_url # Use R2 bucket URL
  backend_origin_url    = var.backend_origin_url
  websocket_origin_url  = var.websocket_origin_url
  enable_websockets     = true
  cloudflare_account_id = var.cloudflare_account_id
  cloudflare_zone_id    = var.cloudflare_zone_id # Use existing zone instead of creating new one
  cache_config          = local.effective_cache_config
  create_dns_records    = var.create_dns_records # Controlled via TF_VAR_create_dns_records env var
  create_page_rules     = var.create_page_rules  # Controlled via TF_VAR_create_page_rules env var
  # Auto-detect Cloud Run service URLs
  auto_detect_origins = true
  gcp_project_id      = var.project_id
  gcp_region          = var.region
}

# IAM binding for Cloud Run to access Cloud SQL
resource "google_project_iam_member" "cloud_run_cloudsql_client" {
  project = var.project_id
  role    = "roles/cloudsql.client"
  member  = "serviceAccount:${data.google_project.current.number}-<EMAIL>"
}

# Core Infrastructure Outputs
output "database_instance_name" {
  value       = module.database.instance_name
  description = "Name of the Cloud SQL database instance"
}

output "database_connection_name" {
  value       = module.database.connection_name
  description = "Connection name for the Cloud SQL database instance"
  sensitive   = true
}

output "redis_instance_name" {
  value       = module.redis.redis_instance_name
  description = "Name of the Redis instance"
}

output "redis_host" {
  value       = module.redis.redis_host
  description = "IP address of the Redis instance"
  sensitive   = true
}

output "network_name" {
  value       = module.network.network_name
  description = "Name of the VPC network"
}

output "subnet_name" {
  value       = module.network.subnet_name
  description = "Name of the subnet"
}

# CDN and Storage Outputs
output "r2_bucket_name" {
  value       = module.r2_storage.bucket_name
  description = "Name of the R2 bucket for static assets"
}

output "r2_bucket_url" {
  value       = module.r2_storage.bucket_url
  description = "Direct R2 bucket URL"
  sensitive   = true
}

output "r2_public_url" {
  value       = module.r2_storage.public_url
  description = "Public URL for R2 bucket access"
  sensitive   = true
}

output "web_url" {
  value       = module.cloudflare_cdn.web_url
  description = "Web application URL"
}

output "api_url" {
  value       = module.cloudflare_cdn.api_url
  description = "API backend URL"
}

output "websocket_url" {
  value       = module.cloudflare_cdn.websocket_url
  description = "WebSocket URL"
}

# Legacy outputs for backward compatibility (deprecated)
output "gcs_bucket_name" {
  value       = module.r2_storage.bucket_name
  description = "DEPRECATED: Use r2_bucket_name instead. Name of the storage bucket."
}

output "service_account_email" {
  value       = ""
  description = "DEPRECATED: Not applicable with R2. Service account email."
}
