# Development Environment Configuration

# project_id    = "clean-algebra-459903-b1"
project_name  = "vedavivi"
unique_suffix = "dev-12345"
region        = "us-central1"
zone          = "us-central1-a"
machine_type  = "e2-small"
database_tier = "db-f1-micro"

# Override any default variables specific to dev
db_flags = [
  {
    name  = "log_min_duration_statement"
    value = "1000"
  }
]

# CDN Configuration for Development
root_domain  = "vedavivi.app"
gcs_location = "US"

# backend_origin_url will be provided during deployment via TF_VAR_backend_origin_url
# websocket_origin_url will be provided during deployment via TF_VAR_websocket_origin_url
# cloudflare_api_token will be provided securely during deployment via TF_VAR_cloudflare_api_token
# cloudflare_account_id will be provided securely during deployment via TF_VAR_cloudflare_account_id

# Cloudflare zone configuration for development
zone_config = {
  jump_start = false  # Don't auto-import DNS records for dev
  paused     = false  # Zone is active
  type       = "full" # Full zone management
}

# CDN Cache Configuration for Development
# Development environment uses minimal caching for faster iteration
cache_config = {
  # Static assets - shorter cache for development
  static_assets_edge_ttl    = 3600 # 1 hour
  static_assets_browser_ttl = 3600 # 1 hour

  # HTML files - very short cache for development
  html_edge_ttl    = 60 # 1 minute
  html_browser_ttl = 60 # 1 minute

  # API caching - disabled for development
  api_cache_enabled = false

  # GraphQL caching - disabled for development
  graphql_cache_enabled = false

  # Global cache settings for development
  development_mode  = true    # Enable development mode (bypass cache)
  cache_level       = "basic" # Basic cache level
  browser_cache_ttl = 300     # 5 minutes browser cache
}