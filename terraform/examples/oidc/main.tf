/**
 * # GCP Workload Identity Federation for GitHub Actions
 *
 * This example demonstrates how to use the OIDC module to set up Workload Identity Federation
 * for GitHub Actions workflows.
 */

provider "google" {
  project = var.project_id
  region  = var.region
}

# Get project information
data "google_project" "project" {
  project_id = var.project_id
}

# Use the OIDC module
module "github_oidc" {
  source = "../../modules/oidc"

  project_id     = var.project_id
  project_number = data.google_project.project.number

  # GitHub repository in format "owner/repo"
  github_repo = var.github_repo

  # JWKS is now fetched automatically from GitHub's endpoint

  # Optional: Custom IDs for the pool and provider
  pool_id     = "github-actions-pool"
  provider_id = "github-actions-provider"

  # Service account ID and roles
  service_account_id = "github-actions-sa"
  service_account_roles = [
    "roles/run.admin",              # For Cloud Run deployments
    "roles/storage.admin",          # For GCS operations
    "roles/artifactregistry.admin", # For container operations
    "roles/iam.serviceAccountUser"  # To act as service accounts
  ]
} 