/**
 * # OIDC Example Outputs
 */

output "workload_identity_provider" {
  description = "Workload Identity Provider resource path"
  value       = module.github_oidc.workload_identity_provider
}

output "service_account_email" {
  description = "Email of the created service account for GitHub Actions"
  value       = module.github_oidc.service_account_email
}

output "github_workflow_identity_setup" {
  description = "Example GitHub workflow identity setup for use in GitHub Actions"
  value       = <<-EOT
    # Add this to your GitHub Actions workflow YAML:
    
    jobs:
      deploy:
        # ... other job configuration
        permissions:
          contents: 'read'
          id-token: 'write' # Required for OIDC
          
        steps:
          - name: Authenticate to Google Cloud
            uses: google-github-actions/auth@v2
            with:
              workload_identity_provider: '${module.github_oidc.workload_identity_provider}'
              service_account: '${module.github_oidc.service_account_email}'
              
          # ... continue with other steps
  EOT
} 