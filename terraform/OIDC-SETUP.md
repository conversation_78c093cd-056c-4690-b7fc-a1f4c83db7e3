# Setting Up OIDC Authentication for GitHub Actions with GCP

This document explains how to set up OIDC authentication for GitHub Actions workflows with Google Cloud Platform using Workload Identity Federation.

## Prerequisites

- Google Cloud Platform project
- GitHub repository
- Node.js v12+ (for JWKS generation)
- Terraform

## Step 1: Generate JSON Web Key Set (JWKS)

First, generate the JSON Web Key Set that will be used by the OIDC provider:

```bash
# Navigate to the scripts directory
cd terraform/scripts/oidc

# Make the script executable
chmod +x generate_jwks.sh

# Generate the JWKS
./generate_jwks.sh
```

This will create a private key and J<PERSON><PERSON> file in the `terraform/scripts/oidc/keys` directory.

## Step 2: Apply Terraform Configuration

Next, apply the Terraform configuration to set up the Workload Identity Federation:

```bash
# Navigate to the example directory
cd terraform/examples/oidc

# Create a terraform.tfvars file
cat <<EOF > terraform.tfvars
project_id  = "your-gcp-project-id"
github_repo = "blkops-collective/a2a-platform"
EOF

# Initialize Terraform
terraform init

# Apply the configuration
terraform apply
```

## Step 3: Update GitHub Actions Workflow

The GitHub Actions workflow needs to be updated to use the Workload Identity Federation for authentication. This has already been done in the `.github/workflows/deploy.yml` file, which includes:

```yaml
- name: Authenticate to GCP
  uses: google-github-actions/auth@v2
  with:
    workload_identity_provider: 'projects/PROJECT_ID/locations/global/workloadIdentityPools/github-actions-pool/providers/github-actions-provider'
    service_account: 'github-actions-sa@PROJECT_ID.iam.gserviceaccount.com'
```

## Step 4: Configure GitHub Repository Permissions

Ensure the GitHub Actions workflow has the necessary permissions:

```yaml
permissions:
  contents: 'read'
  id-token: 'write' # Required for OIDC
```

## Security Considerations

- The private key in `keys/private.pem` is sensitive and should not be committed to version control
- For production environments, consider rotating keys periodically
- Use least privilege principle when assigning IAM roles to the service account

## Troubleshooting

- **403 Errors**: Check that the service account has the necessary IAM roles
- **401 Errors**: Verify the OIDC configuration and that the repository matches the IAM policy
- **JWT Issues**: Make sure the JWKS file is properly formatted and accessible to GCP

## Additional Resources

- [GitHub Actions OIDC Documentation](https://docs.github.com/en/actions/deployment/security-hardening-your-deployments/configuring-openid-connect-in-google-cloud-platform)
- [GCP Workload Identity Federation Documentation](https://cloud.google.com/iam/docs/workload-identity-federation)
- [Terraform GCP Provider Documentation](https://registry.terraform.io/providers/hashicorp/google/latest/docs) 