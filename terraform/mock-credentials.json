{"type": "service_account", "project_id": "local-test-project", "private_key_id": "mock-key-id-12345", "private_key": "-----BEGIN PRIVATE KEY-----\nMIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCt1Lxee2UEbPmH\npHYiM5Kn4nQpEtlSFB1rWYJP8OoJ8Sr2qWOZbLqbsV+bUmMbqTRpaVoYDuXw8Z/d\ngfpZzBA/zWDI+BrUMRJNqDGnDpQkgYWJiKR8f2HX+5S8GifTy8GLXqLI7JArGtA6\nrSwkKZkf2oDeBJVlP0OqXO/I3I/jLHY2LxdYYTrL6KSbhiVS8OGCVJPKJjUnOQtb\nUlHHhbFHqRKXLHdvZW8uh2/Snrf+LBgbIbN1cRhGhgZk7KR4XQF5QIbJQfOA1f3r\nQmBBKbUX9Sw3ALQXxfyPvnvKx3W0vn0Uc27xJCMOQXdtBXqQqbIzMmFkFsW8NJOH\nL3mdiEkHAgMBAAECggEABmWd0r0GCfr8wJ0h11lfi2wkiGFzLb/3AXT/6o9t+3QI\nF+LpbjL0zRbyQdQzqb4SDaeEQzqFqXhSrjmvRAhjYbWBGnkn/ZCXquPP2R7Cjt/9\nW9TC1vqGQ7uv4i8MxHmWF/J9WN+K9U2eSg9BXQ7ziMU7w1p6PVV0NkUpCQ1loAoN\n9Xt/FcT0Vljm4ltJHM3WZdCvj5YAB0QkPzzsMVZJ5zWveW9VmJfwCJ7R7L83jkYd\nt4anmL7f9R1kWmBx9OhzYJ5E/5dfiGNYQvY0L5P/WrbQVpUZLBNXQYpcIwnk2ycp\nBKKNc9I9e7D5uZKeL9oCmcbT1cUxfGcLhLEQqyvAyQKBgQDlHSJjaULY+KJn790/\nLOR1KkJtYRjbHbJwBHN/5yOGM68SrguJBgLvwHp2mGkJ1R1BqbFNQitjuBRGE30L\nzNWFRlXLfzZfBJYNnqLzCKdzGRY2BQnN2K+8MIKHQgckcFpRvXNPeTrMADvn0KBQ\nD0cKYxc5dpwTKI0Fp0Fpl9kfAwKBgQDCH4gUoKCLIcnEOrAKqqxodqILnVpQVY2r\nM/+Ip4JRUDPyAXYfddCRUvMxx1wQQDVZ7VvpzBLTEopngU7wWDEFJQMRl6IECF7t\nn8iTYlIm7yPxfzXvCgP8N0fY8Gh5u925+6Yz4+u06p09Ovz7LeTK57a4oVWg4acA\nDTPGSU67rQKBgDe71AJIvpDR/mKA9BjQQPJEFQQwPcpZczpXu3j6Gb/ncZPnCwNV\nIHQFVG0UjU3QpAz0HmqYYkYl6Rdww+xIlXXVRFtpBYAQs2OJgDRHhvvb8LgpQN8X\nGqcGU3v9wDW/c9UR/8bw9U6Nj38Fv5MvOIKlhCr0+Hk+KqTtT+aINa0LAoGACIBo\nQG2GHKxda75HZZTb5D6vQVhfBV7u8yrbG5bv0pZ3YwOhLADQgLTgVbqvOQJCGiWy\nzAh3k8M4wzYJLBP1QFEzDBR4q9hyaEOkRPV/wJKJYuLK+O88AHT+IVz54q2OZhvw\nsvwcIPMDEWbifuifvDHopNAy1bKrUEZRSQYT7NECgYAcBkQFMevOGPw3fLi/UTi+\nIOAFFlc7zxe9kQ9lzAcU5Lgjn3aT4MnxFQKcGa3wQiGQdWRnD9UAg45gY48ZFN+j\n9tBJR4We6xlN5OxzC2JgKuVT3IyxnwZ5dHGFAbBjIRw+xKWj9Xu5jS0t0D+AEl4Z\nTj0RXN4OkZ1soVW11tChtA==\n-----END PRIVATE KEY-----\n", "client_email": "*******", "client_id": "123456789012345678901", "auth_uri": "https://accounts.google.com/o/oauth2/auth", "token_uri": "https://oauth2.googleapis.com/token", "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs", "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/local-test%40local-test-project.iam.gserviceaccount.com", "universe_domain": "googleapis.com"}