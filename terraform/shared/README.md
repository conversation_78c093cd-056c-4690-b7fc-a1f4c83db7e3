# Shared Terraform Configurations

This directory contains shared Terraform configurations that are reused across multiple environments to reduce duplication and ensure consistency.

## Files

### `cache-variables.tf`
Contains the centralized cache configuration variable definitions used by both staging and production environments.

**Purpose**: 
- Eliminates duplication of the complex `cache_config` object type definition
- Ensures consistency across environments
- Reduces maintenance overhead when updating cache variable types

**Usage**:
Each environment directory contains a symlink to this file:
```bash
# In terraform/environments/staging/
cache-variables.tf -> ../../shared/cache-variables.tf

# In terraform/environments/production/
cache-variables.tf -> ../../shared/cache-variables.tf
```

**Variables Defined**:
- `cache_config`: Complete cache configuration object with all frontend and API settings
- `cache_api_enabled`: Environment variable override for API caching
- `cache_graphql_enabled`: Environment variable override for GraphQL caching
- `cache_development_mode`: Environment variable override for development mode
- `cache_static_assets_ttl`: Environment variable override for static assets TTL
- `cache_html_ttl`: Environment variable override for HTML TTL
- `cache_api_ttl`: Environment variable override for API TTL
- `cache_graphql_ttl`: Environment variable override for GraphQL TTL

## Benefits

1. **Single Source of Truth**: Cache variable types are defined once and shared
2. **Consistency**: All environments use identical variable definitions
3. **Maintainability**: Updates to cache variable types only need to be made in one place
4. **Type Safety**: Ensures all environments have the same type constraints and defaults

## Adding New Shared Variables

When adding new shared variable definitions:

1. Add the variable to the appropriate file in this directory
2. Create symlinks in each environment that needs the variable:
   ```bash
   cd terraform/environments/<environment>/
   ln -sf ../../shared/<filename>.tf <filename>.tf
   ```
3. Update environment-specific `terraform.tfvars` files with actual values
4. Update documentation to reflect the new shared variables

## Validation

After making changes to shared variables, validate all environments:

```bash
# Validate staging
cd terraform/environments/staging
terraform validate

# Validate production  
cd terraform/environments/production
terraform validate
```

## Best Practices

- Keep shared variables generic and environment-agnostic
- Use descriptive variable names and comprehensive descriptions
- Include sensible defaults where appropriate
- Document any breaking changes in variable definitions
- Test changes across all environments before committing
