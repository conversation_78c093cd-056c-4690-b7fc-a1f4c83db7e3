# Shared cache configuration variable definitions
# This file contains the cache_config variable definition that can be sourced by both environments
# to reduce duplication and ensure consistency

variable "cache_config" {
  description = "CDN cache configuration settings"
  type = object({
    # Frontend static assets caching
    static_assets_edge_ttl    = optional(number, 31536000) # 1 year default
    static_assets_browser_ttl = optional(number, 31536000) # 1 year default

    # HTML files caching
    html_edge_ttl    = optional(number, 7200) # 2 hours default (Free plan minimum)
    html_browser_ttl = optional(number, 7200) # 2 hours default (Free plan minimum)

    # API caching configuration
    api_cache_enabled    = optional(bool, false)              # Disabled by default
    api_edge_ttl         = optional(number, 120)              # 2 minutes (Free plan minimum)
    api_browser_ttl      = optional(number, 0)                # No browser cache for APIs
    api_cache_by_device  = optional(bool, false)              # Cache by device type (paid plans only)
    api_cache_key_fields = optional(list(string), ["accept"]) # Cache key fields (paid plans only)

    # GraphQL specific caching
    graphql_cache_enabled   = optional(bool, false) # Disabled by default
    graphql_edge_ttl        = optional(number, 120) # 2 minutes (Free plan minimum)
    graphql_browser_ttl     = optional(number, 0)   # No browser cache for GraphQL
    graphql_cache_by_device = optional(bool, false) # Cache by device type (paid plans only)

    # Global cache settings
    development_mode  = optional(bool, false)          # Bypass cache in development
    cache_level       = optional(string, "aggressive") # Cache level: basic, simplified, aggressive
    browser_cache_ttl = optional(number, 14400)        # Default browser cache (4 hours)
  })
  default = {}
}

# Environment variable overrides for cache configuration
variable "cache_api_enabled" {
  description = "Override API cache enabled setting via environment variable"
  type        = string
  default     = ""
}

variable "cache_graphql_enabled" {
  description = "Override GraphQL cache enabled setting via environment variable"
  type        = string
  default     = ""
}

variable "cache_development_mode" {
  description = "Override development mode setting via environment variable"
  type        = string
  default     = ""
}

variable "cache_static_assets_ttl" {
  description = "Override static assets TTL via environment variable"
  type        = string
  default     = ""
}

variable "cache_html_ttl" {
  description = "Override HTML TTL via environment variable"
  type        = string
  default     = ""
}

variable "cache_api_ttl" {
  description = "Override API TTL via environment variable"
  type        = string
  default     = ""
}

variable "cache_graphql_ttl" {
  description = "Override GraphQL TTL via environment variable"
  type        = string
  default     = ""
}
