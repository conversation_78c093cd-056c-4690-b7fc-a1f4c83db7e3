# Local .terraform directories
**/.terraform/*

# Terraform provider binaries (explicitly exclude all provider binaries)
**/.terraform/providers/**
**/terraform-provider-*

# .tfstate files
*.tfstate
*.tfstate.*
terraform.tfstate.d/

# Auto-generated backend configurations
backend.tf

# Crash log files
crash.log
crash.*.log

# Exclude all .tfvars files, which might contain sensitive data
*.tfvars
*.tfvars.json

# Include example tfvars files and our environment tfvars
!example.tfvars
!example.tfvars.json
!environments/*/terraform.tfvars

# Override files
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# CLI configuration files
.terraformrc
terraform.rc

# Local development files
*.tfplan
# Note: .terraform.lock.hcl files should be committed for reproducible builds

# Ignore local test modules and fixtures
test/fixtures/*/.terraform/
test/fixtures/**/.terraform/
test/**/.terraform/