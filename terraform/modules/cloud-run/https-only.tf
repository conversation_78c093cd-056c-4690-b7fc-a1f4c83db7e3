/**
 * Terraform module for Google Cloud Run with HTTPS-only configuration.
 * 
 * This module creates a Google Cloud Run service configured for HTTPS-only ingress,
 * ensuring secure communication between the load balancer and the service.
 */

variable "project_id" {
  description = "The Google Cloud project ID"
  type        = string
}

variable "region" {
  description = "The Google Cloud region"
  type        = string
}

variable "name" {
  description = "The name of the Cloud Run service"
  type        = string
}

variable "image" {
  description = "The container image to deploy"
  type        = string
}

variable "env_vars" {
  description = "Environment variables to set in the container"
  type = list(object({
    name  = string
    value = string
  }))
  default = []
}

variable "service_account_email" {
  description = "The service account email to run the container as"
  type        = string
  default     = ""
}

# Cloud Run service with HTTPS-only configuration
resource "google_cloud_run_service" "default" {
  name     = var.name
  location = var.region

  template {
    spec {
      containers {
        image = var.image

        dynamic "env" {
          for_each = var.env_vars
          content {
            name  = env.value.name
            value = env.value.value
          }
        }
      }

      service_account_name = var.service_account_email
    }

    metadata {
      annotations = {
        # Force HTTPS-only ingress
        "run.googleapis.com/ingress" = "internal-and-cloud-load-balancing"

        # Use the latest TLS version
        "run.googleapis.com/session-affinity" = "true"
      }
    }
  }

  traffic {
    percent         = 100
    latest_revision = true
  }

  # Restrict the service to only be invoked via HTTPS
  metadata {
    annotations = {
      "run.googleapis.com/ingress" = "internal-and-cloud-load-balancing"
    }
  }
}

# IAM policy for the Cloud Run service to allow public access
resource "google_cloud_run_service_iam_member" "public" {
  location = google_cloud_run_service.default.location
  service  = google_cloud_run_service.default.name
  role     = "roles/run.invoker"
  member   = "allUsers"
}

# Create a serverless NEG backend for the Cloud Run service
resource "google_compute_backend_service" "default" {
  name                  = "${var.name}-backend"
  load_balancing_scheme = "EXTERNAL_MANAGED"

  backend {
    group = google_compute_region_network_endpoint_group.default.id
  }
}

# Create a Network Endpoint Group for the Cloud Run service
resource "google_compute_region_network_endpoint_group" "default" {
  name                  = "${var.name}-neg"
  network_endpoint_type = "SERVERLESS"
  region                = var.region

  cloud_run {
    service = google_cloud_run_service.default.name
  }
}

output "service_url" {
  description = "The URL of the Cloud Run service"
  value       = google_cloud_run_service.default.status[0].url
}

output "backend_service_id" {
  description = "The ID of the backend service"
  value       = google_compute_backend_service.default.id
}

output "backend_service_name" {
  description = "The name of the backend service"
  value       = google_compute_backend_service.default.name
}
