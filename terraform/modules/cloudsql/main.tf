/**
 * # Cloud SQL Module
 *
 * This module creates a Google Cloud SQL instance with databases and users.
 * 
 * ## Security Notes
 * 
 * 1. For security reasons, this module disables public IP by default (`ipv4_enabled = false`).
 *    Database instances should never be exposed to the public internet.
 *    Instead, use private networking via VPC and Cloud SQL Proxy for secure access.
 * 
 * 2. This module enforces SSL/TLS for all database connections through:
 *    - `ssl = on`: Database flag that enables SSL support
 *    - `ssl_min_protocol_version = TLSv1.2`: Database flag that sets minimum TLS version to 1.2
 *    - `ssl_mode = ENCRYPTED_ONLY`: IP configuration setting that forces clients to connect using SSL
 * 
 *    Note: The 'require_ssl' parameter in ip_configuration is not supported in Google provider v6.0+, 
 *    so we use the `ssl_mode` parameter in the ip_configuration block instead.
 * 
 * ## SSL/TLS Enforcement
 * 
 * This module implements a defense-in-depth approach to SSL enforcement:
 * 1. Database flags are used to enforce SSL at the database engine level
 * 2. Connection strings for applications should specify SSL requirements
 * 3. We recommend using Cloud SQL Auth Proxy for the most secure connections
 */

locals {
  # Ensure temporary file logging, connection logging, and disconnection logging are enabled by default
  default_database_flags = concat(var.database_flags, [
    {
      name  = "log_temp_files"
      value = "0" # Log all temporary files (0 means log all)
    },
    {
      name  = "log_connections"
      value = "on" # Log all connections to the database
    },
    {
      name  = "log_disconnections"
      value = "on" # Log all disconnections from the database
    },
    {
      name  = "log_checkpoints"
      value = "on" # Log each checkpoint
    },
    {
      name  = "log_lock_waits"
      value = "on" # Log long lock waits
    },
    {
      name  = "ssl"
      value = "on" # Enforce SSL connections
    },
    {
      name  = "ssl_min_protocol_version"
      value = "TLSv1.2" # Enforce minimum TLS version 
    },
    {
      name  = "cloudsql.iam_authentication"
      value = "on"
    }
  ])

  # Add pgaudit flags only if audit logging is enabled
  audit_flags = var.enable_audit_logging ? [
    {
      name  = "pgaudit.log"
      value = "ddl,write" # Log data definition and write operations
    },
    {
      name  = "pgaudit.log_catalog"
      value = "on" # Log catalog operations
    }
  ] : []

  # Combine all flags
  all_database_flags = concat(local.default_database_flags, local.audit_flags)
}

# Database instance with comprehensive security flags
# trivy:ignore:AVD-GCP-0014 - log_temp_files is set to "0" in default_database_flags
# trivy:ignore:AVD-GCP-0016 - log_connections is set to "on" in default_database_flags  
# trivy:ignore:AVD-GCP-0020 - log_lock_waits is set to "on" in default_database_flags
# trivy:ignore:AVD-GCP-0022 - log_disconnections is set to "on" in default_database_flags
# trivy:ignore:AVD-GCP-0024 - backup_configuration is configured conditionally via variables
# trivy:ignore:AVD-GCP-0025 - log_checkpoints is set to "on" in default_database_flags
resource "google_sql_database_instance" "instance" {
  name             = var.instance_name
  project          = var.project_id
  region           = var.region
  database_version = var.database_version

  settings {
    tier              = var.tier
    disk_size         = var.disk_size
    availability_type = var.availability_type

    dynamic "database_flags" {
      for_each = local.all_database_flags
      content {
        name  = database_flags.value.name
        value = database_flags.value.value
      }
    }

    # Only include backup configuration if enabled
    dynamic "backup_configuration" {
      for_each = var.backup_configuration.enabled ? [1] : []
      content {
        enabled                        = var.backup_configuration.enabled
        start_time                     = var.backup_configuration.start_time
        location                       = var.backup_configuration.location
        point_in_time_recovery_enabled = var.backup_configuration.point_in_time_recovery_enabled
        transaction_log_retention_days = var.backup_configuration.transaction_log_retention_days

        # Only include backup retention settings if retained_backups is not null
        dynamic "backup_retention_settings" {
          for_each = var.backup_configuration.retained_backups != null ? [1] : []
          content {
            retained_backups = var.backup_configuration.retained_backups
            retention_unit   = var.backup_configuration.retention_unit
          }
        }
      }
    }

    # trivy:ignore:AVD-GCP-0015
    ip_configuration {
      # For security reasons, default to false to prevent public internet exposure
      ipv4_enabled    = var.ip_configuration.ipv4_enabled
      private_network = var.ip_configuration.private_network
      ssl_mode        = "ENCRYPTED_ONLY"
      # Note: SSL is enforced through ssl_mode above
      # The require_ssl parameter was removed in Google provider v6.0+

      dynamic "authorized_networks" {
        for_each = var.ip_configuration.authorized_networks
        content {
          name            = authorized_networks.value.name
          value           = authorized_networks.value.value
          expiration_time = authorized_networks.value.expiration_time
        }
      }
    }
  }

  deletion_protection = var.deletion_protection
}

# Create default database if specified
resource "google_sql_database" "default_database" {
  count    = var.database_name != null ? 1 : 0
  name     = var.database_name
  instance = google_sql_database_instance.instance.name
  project  = var.project_id
}

# Create default user if specified
resource "google_sql_user" "default_user" {
  count       = var.db_user != null && var.db_password != null ? 1 : 0
  name        = var.db_user
  password_wo = var.db_password
  instance    = google_sql_database_instance.instance.name
  project     = var.project_id
}

# Create additional databases
resource "google_sql_database" "database" {
  for_each = toset(var.databases)

  name     = each.value
  instance = google_sql_database_instance.instance.name
  project  = var.project_id
}

# Create additional users - modified to avoid using sensitive values in for_each
# Instead of using for_each with sensitive data, we use count and access elements by index
resource "google_sql_user" "user" {
  count = length(var.users)

  name        = var.users[count.index].name
  password_wo = var.users[count.index].password
  instance    = google_sql_database_instance.instance.name
  project     = var.project_id
} 