/**
 * # Cloud SQL Module Outputs
 */

output "instance_name" {
  description = "The name of the Cloud SQL instance"
  value       = google_sql_database_instance.instance.name
}

output "connection_name" {
  description = "The connection name of the instance to be used in connection strings"
  value       = google_sql_database_instance.instance.connection_name
}

output "instance_connection_name" {
  description = "Alias for connection_name output"
  value       = google_sql_database_instance.instance.connection_name
}

output "instance_self_link" {
  description = "The URI of the instance"
  value       = google_sql_database_instance.instance.self_link
}

output "instance_ip_address" {
  description = "The IPv4 address of the master database instance"
  value       = google_sql_database_instance.instance.ip_address
}

output "instance_first_ip_address" {
  description = "The first IPv4 address of the addresses assigned to the master database instance"
  value       = google_sql_database_instance.instance.first_ip_address
}

output "instance_public_ip_address" {
  description = "The public IPv4 address of the master database instance"
  value       = google_sql_database_instance.instance.public_ip_address
}

output "instance_private_ip_address" {
  description = "The private IPv4 address of the master database instance"
  value       = google_sql_database_instance.instance.private_ip_address
}

output "database_name" {
  description = "Name of the default database"
  value       = var.database_name != null ? var.database_name : null
}

output "database_user" {
  description = "Name of the default database user"
  value       = var.db_user != null ? var.db_user : null
}

output "databases" {
  description = "List of all databases created"
  value = concat(
    var.database_name != null ? [var.database_name] : [],
    [for db in google_sql_database.database : db.name]
  )
}

output "users" {
  description = "List of all user names created"
  value = concat(
    var.db_user != null ? [var.db_user] : [],
    [for user in google_sql_user.user : user.name]
  )
  sensitive = false
} 