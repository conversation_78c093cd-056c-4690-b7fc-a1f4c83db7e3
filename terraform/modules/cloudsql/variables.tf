/**
 * # Cloud SQL Module Variables
 */

variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "region" {
  description = "The region for the CloudSQL instance"
  type        = string
  default     = "us-central1"
}

variable "instance_name" {
  description = "Name of the CloudSQL instance"
  type        = string
}

variable "database_version" {
  description = "The database version to use"
  type        = string
  default     = "POSTGRES_14"
}

variable "tier" {
  description = "The machine type to use"
  type        = string
  default     = "db-g1-small"
}

variable "disk_size" {
  description = "The size of the disk in GB"
  type        = number
  default     = 10
}

variable "availability_type" {
  description = "The availability type for the master instance. Can be either 'REGIONAL' or 'ZONAL'"
  type        = string
  default     = "ZONAL"
}

variable "database_flags" {
  description = "List of database flags to set on the instance"
  type = list(object({
    name  = string
    value = string
  }))
  default = []
}

variable "enable_audit_logging" {
  description = "Whether to enable PostgreSQL audit logging using pgaudit"
  type        = bool
  default     = false
}

variable "backup_configuration" {
  description = "The backup_configuration settings for the instance"
  type = object({
    enabled                        = bool
    start_time                     = string
    location                       = optional(string)
    point_in_time_recovery_enabled = optional(bool)
    transaction_log_retention_days = optional(number)
    retained_backups               = optional(number)
    retention_unit                 = optional(string)
  })
  default = {
    enabled    = true
    start_time = "02:00"
  }
}

variable "ip_configuration" {
  description = "The IP configuration for the instance"
  type = object({
    authorized_networks = list(object({
      name            = string
      value           = string
      expiration_time = optional(string)
    }))
    ipv4_enabled    = bool
    private_network = optional(string)
    require_ssl     = optional(bool, true)
  })
  default = {
    authorized_networks = []
    ipv4_enabled        = false # Disable public IP for security
    require_ssl         = true
  }
}

variable "deletion_protection" {
  description = "Whether or not to allow Terraform to destroy the instance"
  type        = bool
  default     = false
}

variable "database_name" {
  description = "The name of the default database to create"
  type        = string
  default     = null
}

variable "db_user" {
  description = "The name of the default user to create"
  type        = string
  default     = null
}

variable "db_password" {
  description = "The password for the default user"
  type        = string
  default     = null
  sensitive   = true
}

variable "databases" {
  description = "List of additional databases to create"
  type        = list(string)
  default     = []
}

variable "users" {
  description = "List of additional users to create"
  type = list(object({
    name     = string
    password = string
  }))
  default   = []
  sensitive = true
} 