/**
 * # Redis Variables
 *
 * Variables for the Redis module
 */

variable "project_id" {
  description = "The ID of the project in which resources will be provisioned."
  type        = string
}

variable "environment" {
  description = "The environment (dev, staging, prod)."
  type        = string
}

variable "region" {
  description = "The region in which resources will be provisioned."
  type        = string
}

variable "create_resources" {
  description = "Whether to create new resources or use existing ones."
  type        = bool
  default     = false
}

variable "existing_resources_exist" {
  description = "Whether existing resources already exist that we should reference."
  type        = bool
  default     = true
}

variable "network_name" {
  description = "The name of the network in which the Redis instance will be created."
  type        = string
}

variable "tier" {
  description = "The service tier of the instance. BASIC or STANDARD_HA"
  type        = string
  default     = "BASIC"
}

variable "memory_size_gb" {
  description = "Redis memory size in GB"
  type        = number
  default     = 1
}

variable "redis_version" {
  description = "The version of Redis software."
  type        = string
  default     = "REDIS_6_X"
}

variable "kms_key_name" {
  description = "The KMS key name to use for encrypting secrets."
  type        = string
  default     = null
}

variable "dependencies" {
  description = "List of dependencies that must be created before this module can be deployed"
  type        = list(any)
  default     = []
} 