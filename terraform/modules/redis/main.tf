/**
 * # Redis Module
 *
 * This module creates and manages Google Cloud Memorystore for Redis instances.
 * Used for caching and messaging broker functionality.
 */

locals {
  instance_name = "a2a-${var.environment}-redis"

  # Set default values for Redis instance outputs when it doesn't exist
  redis_host = var.create_resources ? (
    length(google_redis_instance.default) > 0 ? google_redis_instance.default[0].host : "localhost"
    ) : (
    var.existing_resources_exist ? try(data.google_redis_instance.existing[0].host, "localhost") : "localhost"
  )

  redis_port = var.create_resources ? (
    length(google_redis_instance.default) > 0 ? google_redis_instance.default[0].port : 6379
    ) : (
    var.existing_resources_exist ? try(data.google_redis_instance.existing[0].port, 6379) : 6379
  )

  redis_connection_string = "redis://${local.redis_host}:${local.redis_port}/0"
}

# Look up existing Redis instance if not creating a new one
data "google_redis_instance" "existing" {
  count   = var.existing_resources_exist ? 1 : 0
  name    = local.instance_name
  region  = var.region
  project = var.project_id
}

# Redis instance
resource "google_redis_instance" "default" {
  count              = var.create_resources ? 1 : 0
  name               = local.instance_name
  tier               = var.tier
  memory_size_gb     = var.memory_size_gb
  region             = var.region
  project            = var.project_id
  authorized_network = var.network_name
  connect_mode       = "PRIVATE_SERVICE_ACCESS"
  redis_version      = var.redis_version
  display_name       = "A2A Platform Redis (${var.environment})"

  labels = {
    environment = var.environment
    managed_by  = "terraform"
  }

  maintenance_policy {
    weekly_maintenance_window {
      day = "SUNDAY"
      start_time {
        hours   = 2
        minutes = 0
      }
    }
  }

  # For production, enable high availability memory settings
  redis_configs = var.environment == "prod" ? {
    "maxmemory-policy" = "allkeys-lru"
  } : {}

  # Prevent recreation of existing resources
  lifecycle {
    prevent_destroy = true
    ignore_changes  = all
  }
}

# Store Redis Host in Secret Manager
resource "google_secret_manager_secret" "redis_host" {
  count     = var.create_resources ? 1 : 0
  project   = var.project_id
  secret_id = "a2a-${var.environment}-redis-host"

  replication {
    dynamic "auto" {
      for_each = var.kms_key_name != null ? [1] : []
      content {
        customer_managed_encryption {
          kms_key_name = var.kms_key_name
        }
      }
    }

    dynamic "auto" {
      for_each = var.kms_key_name == null ? [1] : []
      content {}
    }
  }

  labels = {
    environment = var.environment
    managed_by  = "terraform"
  }
}

resource "google_secret_manager_secret_version" "redis_host_version" {
  count          = var.create_resources ? 1 : 0
  secret         = google_secret_manager_secret.redis_host[0].id
  secret_data_wo = local.redis_host
}

# Store Redis URL in Secret Manager
resource "google_secret_manager_secret" "redis_url" {
  count     = var.create_resources ? 1 : 0
  project   = var.project_id
  secret_id = "a2a-${var.environment}-redis-url"

  replication {
    dynamic "auto" {
      for_each = var.kms_key_name != null ? [1] : []
      content {
        customer_managed_encryption {
          kms_key_name = var.kms_key_name
        }
      }
    }

    dynamic "auto" {
      for_each = var.kms_key_name == null ? [1] : []
      content {}
    }
  }

  labels = {
    environment = var.environment
    managed_by  = "terraform"
  }
}

resource "google_secret_manager_secret_version" "redis_url_version" {
  count          = var.create_resources ? 1 : 0
  secret         = google_secret_manager_secret.redis_url[0].id
  secret_data_wo = local.redis_connection_string
} 