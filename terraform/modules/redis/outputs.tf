/**
 * # Redis Outputs
 *
 * Outputs for the Redis module
 * 
 * Note: This is a placeholder file and will be implemented in a future update.
 */

output "redis_instance_name" {
  description = "The name of the Redis instance"
  value       = local.instance_name
}

output "redis_host" {
  description = "The IP address of the Redis instance"
  value       = local.redis_host
  sensitive   = true
}

output "redis_port" {
  description = "The port of the Redis instance"
  value       = local.redis_port
}

output "redis_current_location_id" {
  description = "The current zone of the Redis instance"
  value = var.create_resources ? (
    length(google_redis_instance.default) > 0 ? google_redis_instance.default[0].location_id : null
    ) : (
    var.existing_resources_exist ? try(data.google_redis_instance.existing[0].location_id, null) : null
  )
}

output "connection_string" {
  description = "The connection string for the Redis instance"
  value       = local.redis_connection_string
  sensitive   = true
}

output "redis_host_secret_id" {
  description = "The Secret Manager secret ID for the Redis host"
  value = var.create_resources ? (
    length(google_secret_manager_secret.redis_host) > 0 ? google_secret_manager_secret.redis_host[0].secret_id : "a2a-${var.environment}-redis-host"
  ) : "a2a-${var.environment}-redis-host"
}

output "redis_url_secret_id" {
  description = "The Secret Manager secret ID for the Redis URL"
  value = var.create_resources ? (
    length(google_secret_manager_secret.redis_url) > 0 ? google_secret_manager_secret.redis_url[0].secret_id : "a2a-${var.environment}-redis-url"
  ) : "a2a-${var.environment}-redis-url"
}

output "redis_id" {
  description = "The ID of the Redis instance"
  value = var.create_resources ? (
    length(google_redis_instance.default) > 0 ? google_redis_instance.default[0].id : null
    ) : (
    var.existing_resources_exist ? try(data.google_redis_instance.existing[0].id, null) : null
  )
} 