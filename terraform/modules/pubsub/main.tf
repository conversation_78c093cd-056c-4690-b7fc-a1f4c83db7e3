/**
 * # PubSub Module
 *
 * This module creates and manages Pub/Sub resources.
 * It provisions topics and subscriptions for asynchronous messaging.
 */

locals {
  topic_name = "a2a-${var.environment}-events"
}

resource "google_pubsub_topic" "main" {
  count   = var.create_resources ? 1 : 0
  name    = local.topic_name
  project = var.project_id

  labels = {
    environment = var.environment
    managed_by  = "terraform"
  }

  message_retention_duration = "604800s" # 7 days

  # Prevent recreation
  lifecycle {
    prevent_destroy = true
    ignore_changes  = all
  }
} 