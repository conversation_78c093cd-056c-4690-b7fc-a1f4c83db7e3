/**
 * # PubSub Outputs
 *
 * Outputs for the PubSub module
 * 
 * Note: This is a placeholder file and will be implemented in a future update.
 */

output "topic_name" {
  description = "The name of the Pub/Sub topic"
  value       = var.create_resources ? google_pubsub_topic.main[0].name : "a2a-${var.environment}-events"
}

output "topic_id" {
  description = "The ID of the Pub/Sub topic"
  value       = var.create_resources ? google_pubsub_topic.main[0].id : "projects/${var.project_id}/topics/a2a-${var.environment}-events"
} 