/**
 * # PubSub Variables
 *
 * Variables for the PubSub module
 */

variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "region" {
  description = "The GCP region to deploy resources"
  type        = string
}

variable "environment" {
  description = "The deployment environment (e.g. dev, staging, prod)"
  type        = string
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "The environment must be one of: dev, staging, prod."
  }
}

variable "create_resources" {
  description = "Whether to create new resources or just reference existing ones"
  type        = bool
  default     = false
}

variable "existing_resources_exist" {
  description = "Whether to look up existing resources using data sources"
  type        = bool
  default     = true
}

variable "dependencies" {
  description = "List of dependencies that must be created before this module can be deployed"
  type        = list(any)
  default     = []
} 