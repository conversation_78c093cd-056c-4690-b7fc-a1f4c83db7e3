/**
 * # Compute Module Variables
 *
 * Variables used by the compute module.
 */

variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "region" {
  description = "The GCP region for compute resources"
  type        = string
}

variable "zone" {
  description = "The GCP zone for zonal compute resources"
  type        = string
}

variable "environment" {
  description = "The environment (dev, staging, prod)"
  type        = string
}

variable "machine_type" {
  description = "The machine type for compute instances"
  type        = string
  default     = "e2-medium"
}

variable "boot_disk_size" {
  description = "Size of the boot disk in GB"
  type        = number
  default     = 20
}

variable "network_name" {
  description = "The name of the network to attach instances to"
  type        = string
  default     = "default"
}

variable "subnet_name" {
  description = "The name of the subnetwork to attach instances to"
  type        = string
  default     = "default"
}

variable "service_account_email" {
  description = "The email of the service account to attach to instances"
  type        = string
  default     = ""
}

variable "enable_external_ip" {
  description = "Whether to enable external IP for instances"
  type        = bool
  default     = false
}

variable "startup_script" {
  description = "Startup script to run on instances"
  type        = string
  default     = ""
}

variable "min_instances" {
  description = "Minimum number of instances in the group"
  type        = number
  default     = 1
}

variable "max_instances" {
  description = "Maximum number of VM instances to run"
  type        = number
  default     = 3
}

variable "initial_instances" {
  description = "Initial number of instances in the group"
  type        = number
  default     = 1
}

variable "max_surge_fixed" {
  description = "The maximum number of instances that can be created above the target during update. For regional instance groups, this must be either 0 or at least the number of zones in the region."
  type        = number
  default     = 3 # Setting a default that should work with most regions (which commonly have 3 zones)
}

variable "create_resources" {
  description = "Whether to create resources that may already exist"
  type        = bool
  default     = true
}

variable "existing_resources_exist" {
  description = "Whether existing resources (which would be referenced via data sources when create_resources=false) actually exist"
  type        = bool
  default     = false
}

# Cloud Run API Service variables
variable "api_container_image" {
  description = "Container image for the API service"
  type        = string
  default     = "us-docker.pkg.dev/cloudrun/container/hello:latest" # Default placeholder image
}

variable "api_cpu" {
  description = "CPU allocation for the API service"
  type        = string
  default     = "1000m" # 1 vCPU
}

variable "api_memory" {
  description = "Memory allocation for the API service"
  type        = string
  default     = "512Mi" # 512 MB
}

variable "api_min_instances" {
  description = "Minimum number of instances for the API service"
  type        = number
  default     = 0
}

variable "api_max_instances" {
  description = "Maximum number of instances for the API service"
  type        = number
  default     = 10
}

variable "api_env_vars" {
  description = "Environment variables for the API service"
  type        = map(string)
  default     = {}
}

variable "api_secret_env_vars" {
  description = "Secret environment variables for the API service"
  type = map(object({
    secret  = string
    version = string
  }))
  default = {}
}

# Cloud Run Workers Service variables
variable "workers_container_image" {
  description = "Container image for the Workers service"
  type        = string
  default     = "us-docker.pkg.dev/cloudrun/container/hello:latest" # Default placeholder image
}

variable "workers_cpu" {
  description = "CPU allocation for the Workers service"
  type        = string
  default     = "1000m" # 1 vCPU
}

variable "workers_memory" {
  description = "Memory allocation for the Workers service"
  type        = string
  default     = "512Mi" # 512 MB
}

variable "workers_min_instances" {
  description = "Minimum number of instances for the Workers service"
  type        = number
  default     = 0
}

variable "workers_max_instances" {
  description = "Maximum number of instances for the Workers service"
  type        = number
  default     = 5
}

variable "workers_env_vars" {
  description = "Environment variables for the Workers service"
  type        = map(string)
  default     = {}
}

variable "workers_secret_env_vars" {
  description = "Secret environment variables for the Workers service"
  type = map(object({
    secret  = string
    version = string
  }))
  default = {}
}

# Cloud Run Messaging Service variables - REMOVED
# Messaging service variables have been removed as the service is no longer deployed.
# Messaging functionality is handled by the main API service and RQ workers.

# VPC Connector for Cloud Run
variable "vpc_connector_id" {
  description = "The ID of the VPC connector for Cloud Run"
  type        = string
  default     = null
}

variable "vpc_connector_cidr" {
  description = "The CIDR range for the VPC connector"
  type        = string
  default     = "********/28"
} 