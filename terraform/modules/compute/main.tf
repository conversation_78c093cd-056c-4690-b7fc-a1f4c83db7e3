/**
 * # Compute Module
 *
 * This module creates and manages Google Compute Engine and Cloud Run resources.
 */

locals {
  instance_name_prefix = "a2a-${var.environment}-vm"

  # Service names
  api_service_name     = "a2a-${var.environment}-api"
  workers_service_name = "a2a-${var.environment}-workers"

  # Define default zones based on the region (fallback if data lookup fails)
  default_zones = ["${var.region}-a", "${var.region}-b", "${var.region}-c"]

  # Try to get available zones, but use default_zones if there's a permission error or if not creating resources
  region_zones = var.create_resources ? try(data.google_compute_zones.available[0].names, local.default_zones) : local.default_zones

  # For regional MIGs, max_surge must be either 0 or at least the number of zones
  safe_max_surge = var.max_surge_fixed != 0 ? max(var.max_surge_fixed, length(local.region_zones)) : 0
}

# Get available zones in the region (this may fail with permission errors)
data "google_compute_zones" "available" {
  count   = var.create_resources ? 1 : 0
  project = var.project_id
  region  = var.region
  status  = "UP"
}

# Look up existing health check if not creating a new one
data "google_compute_health_check" "existing" {
  count   = var.existing_resources_exist ? 1 : 0
  name    = "${local.instance_name_prefix}-health-check"
  project = var.project_id
}

# Compute instance template
resource "google_compute_instance_template" "default" {
  project      = var.project_id
  name_prefix  = "${local.instance_name_prefix}-template-"
  machine_type = var.machine_type
  region       = var.region

  // Boot disk
  disk {
    source_image = "debian-cloud/debian-11"
    auto_delete  = true
    boot         = true
    disk_size_gb = var.boot_disk_size
    disk_type    = "pd-standard"
  }

  // Networking
  network_interface {
    network    = var.network_name
    subnetwork = var.subnet_name

    # If access_config is defined, the instance will have external IP
    dynamic "access_config" {
      for_each = var.enable_external_ip ? [1] : []
      content {
        # Ephemeral external IP
      }
    }
  }

  service_account {
    email  = var.service_account_email
    scopes = ["cloud-platform"]
  }

  metadata = {
    startup-script = var.startup_script
  }

  tags = ["a2a-platform", var.environment]

  lifecycle {
    create_before_destroy = true
  }

  # Only create if create_resources is true
  count = var.create_resources ? 1 : 0
}

# Managed instance group
resource "google_compute_region_instance_group_manager" "default" {
  name               = "${local.instance_name_prefix}-mig"
  base_instance_name = local.instance_name_prefix
  region             = var.region
  target_size        = var.initial_instances
  count              = var.create_resources ? 1 : 0

  version {
    instance_template = google_compute_instance_template.default[0].id
  }

  named_port {
    name = "http"
    port = 8080
  }

  # Only add auto_healing_policies if we have a health check
  dynamic "auto_healing_policies" {
    for_each = (var.create_resources || var.existing_resources_exist) ? [1] : []
    content {
      health_check      = length(google_compute_health_check.default) > 0 ? google_compute_health_check.default[0].id : (length(data.google_compute_health_check.existing) > 0 ? data.google_compute_health_check.existing[0].id : null)
      initial_delay_sec = 300
    }
  }

  update_policy {
    type                  = "PROACTIVE"
    minimal_action        = "REPLACE"
    max_surge_fixed       = local.safe_max_surge
    max_unavailable_fixed = 0
    replacement_method    = "SUBSTITUTE"
  }

  # Prevent resource conflicts with pre-existing instance group
  lifecycle {
    create_before_destroy = true
    prevent_destroy       = true
    ignore_changes        = all
  }
}

# Autoscaler
resource "google_compute_region_autoscaler" "default" {
  name   = "${local.instance_name_prefix}-autoscaler"
  region = var.region
  target = var.create_resources ? google_compute_region_instance_group_manager.default[0].id : null
  count  = var.create_resources ? 1 : 0

  autoscaling_policy {
    max_replicas    = var.max_instances
    min_replicas    = var.min_instances
    cooldown_period = 60

    cpu_utilization {
      target = 0.7
    }
  }
}

# Health check
resource "google_compute_health_check" "default" {
  name                = "${local.instance_name_prefix}-health-check"
  check_interval_sec  = 5
  timeout_sec         = 5
  healthy_threshold   = 2
  unhealthy_threshold = 10

  http_health_check {
    request_path = "/health"
    port         = "8080"
  }

  # Prevent resource conflicts with pre-existing resources
  lifecycle {
    # Make resource resilient to recreation attempts
    create_before_destroy = true

    # Prevent terraform from destroying the resource
    prevent_destroy = true

    # Ignore all changes to prevent recreation of the resource
    ignore_changes = all
  }

  # Only create if create_resources is true
  count = (var.create_resources && !var.existing_resources_exist) ? 1 : 0
}

# Cloud Run Services for containerized workloads

# API Service
resource "google_cloud_run_v2_service" "api" {
  count    = var.create_resources ? 1 : 0
  name     = local.api_service_name
  location = var.region
  ingress  = "INGRESS_TRAFFIC_ALL"
  project  = var.project_id

  template {
    containers {
      image = var.api_container_image

      resources {
        limits = {
          cpu    = var.api_cpu
          memory = var.api_memory
        }
        cpu_idle = true
      }

      env {
        name = "DATABASE_URL"
        value_source {
          secret_key_ref {
            secret  = "a2a-${var.environment}-db-connection-string"
            version = "latest"
          }
        }
      }

      env {
        name = "REDIS_URL"
        value_source {
          secret_key_ref {
            secret  = "a2a-${var.environment}-redis-url"
            version = "latest"
          }
        }
      }

      # Add more environment variables as needed
      dynamic "env" {
        for_each = var.api_env_vars
        content {
          name  = env.key
          value = env.value
        }
      }

      # Add more secret environment variables as needed
      dynamic "env" {
        for_each = var.api_secret_env_vars
        content {
          name = env.key
          value_source {
            secret_key_ref {
              secret  = env.value.secret
              version = env.value.version
            }
          }
        }
      }
    }

    scaling {
      min_instance_count = var.api_min_instances
      max_instance_count = var.api_max_instances
    }

    service_account = var.service_account_email

    vpc_access {
      connector = var.vpc_connector_id
      egress    = "PRIVATE_RANGES_ONLY"
    }
  }

  lifecycle {
    ignore_changes = [
      template[0].containers[0].image,
      client,
      client_version
    ]
  }
}

# Workers Service
resource "google_cloud_run_v2_service" "workers" {
  count    = var.create_resources ? 1 : 0
  name     = local.workers_service_name
  location = var.region
  ingress  = "INGRESS_TRAFFIC_INTERNAL_ONLY"
  project  = var.project_id

  template {
    containers {
      image = var.workers_container_image

      resources {
        limits = {
          cpu    = var.workers_cpu
          memory = var.workers_memory
        }
        cpu_idle = true
      }

      env {
        name = "DATABASE_URL"
        value_source {
          secret_key_ref {
            secret  = "a2a-${var.environment}-db-connection-string"
            version = "latest"
          }
        }
      }

      env {
        name = "REDIS_URL"
        value_source {
          secret_key_ref {
            secret  = "a2a-${var.environment}-redis-url"
            version = "latest"
          }
        }
      }

      # Add more environment variables as needed
      dynamic "env" {
        for_each = var.workers_env_vars
        content {
          name  = env.key
          value = env.value
        }
      }

      # Add more secret environment variables as needed
      dynamic "env" {
        for_each = var.workers_secret_env_vars
        content {
          name = env.key
          value_source {
            secret_key_ref {
              secret  = env.value.secret
              version = env.value.version
            }
          }
        }
      }
    }

    scaling {
      min_instance_count = var.workers_min_instances
      max_instance_count = var.workers_max_instances
    }

    service_account = var.service_account_email

    vpc_access {
      connector = var.vpc_connector_id
      egress    = "PRIVATE_RANGES_ONLY"
    }
  }

  lifecycle {
    ignore_changes = [
      template[0].containers[0].image,
      client,
      client_version
    ]
  }
}

# Messaging Service - REMOVED
# The messaging service has been removed to simplify the infrastructure.
# Messaging functionality is handled by the main API service and RQ workers.

# IAM for invoking the API service
resource "google_cloud_run_service_iam_member" "api_public" {
  count    = var.create_resources ? 1 : 0
  location = var.region
  project  = var.project_id
  service  = google_cloud_run_v2_service.api[0].name
  role     = "roles/run.invoker"
  member   = "allUsers"
}

# Create the VPC connector for Cloud Run services to access VPC resources
resource "google_vpc_access_connector" "connector" {
  count         = var.create_resources ? 1 : 0
  name          = "a2a-${var.environment}-vpc-connector"
  region        = var.region
  project       = var.project_id
  ip_cidr_range = var.vpc_connector_cidr
  network       = var.network_name

  # Recommended minimum for production
  machine_type  = var.environment == "prod" ? "e2-standard-4" : "e2-micro"
  min_instances = var.environment == "prod" ? 2 : 1
  max_instances = var.environment == "prod" ? 10 : 3

  lifecycle {
    prevent_destroy = true
    ignore_changes  = all
  }
} 