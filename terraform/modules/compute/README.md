# Compute Module for A2A Platform

This module creates and manages Google Compute Engine resources for the A2A Platform.

## Features

- Creates compute instance templates with configurable machine types and disk sizes
- Sets up regional managed instance groups with auto-healing and autoscaling
- Implements safe update policies for zero-downtime deployments
- Configurable network settings and service accounts

## Regional MIG Configuration

This module automatically configures the update policy for regional managed instance groups according to Google Cloud best practices:

- The `max_surge_fixed` parameter is automatically adjusted to be at least equal to the number of zones in the region
- This ensures that updates can roll out properly across availability zones
- You can set `max_surge_fixed = 0` to disable surge during updates, or any value >= number of zones

## Usage

```hcl
module "compute" {
  source       = "./modules/compute"
  project_id   = var.project_id
  region       = var.region
  zone         = var.zone
  environment  = terraform.workspace
  machine_type = "e2-medium"
  
  # Optional: Override the default max_surge value (must be 0 or >= zones in region)
  max_surge_fixed = 4
}
```

## Variables

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| project_id | The GCP project ID | string | n/a | yes |
| region | The GCP region for compute resources | string | n/a | yes |
| zone | The GCP zone for zonal compute resources | string | n/a | yes |
| environment | The environment (dev, staging, prod) | string | n/a | yes |
| machine_type | The machine type for compute instances | string | `"e2-medium"` | no |
| boot_disk_size | Size of the boot disk in GB | number | `20` | no |
| network_name | The name of the network to attach instances to | string | `"default"` | no |
| subnet_name | The name of the subnetwork to attach instances to | string | `"default"` | no |
| service_account_email | The email of the service account to attach to instances | string | `""` | no |
| enable_external_ip | Whether to enable external IP for instances | bool | `false` | no |
| startup_script | Startup script to run on instances | string | `""` | no |
| min_instances | Minimum number of instances in the group | number | `1` | no |
| max_instances | Maximum number of instances in the group | number | `3` | no |
| initial_instances | Initial number of instances in the group | number | `1` | no |
| max_surge_fixed | Maximum number of instances that can be created above the target | number | `3` | no | 