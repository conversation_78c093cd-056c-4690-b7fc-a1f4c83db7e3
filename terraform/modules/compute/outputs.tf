/**
 * # Compute Module Outputs
 *
 * Outputs from the compute module.
 */

output "instance_template_id" {
  description = "The ID of the instance template"
  value       = length(google_compute_instance_template.default) > 0 ? google_compute_instance_template.default[0].id : null
}

output "instance_group_manager_id" {
  description = "The ID of the instance group manager"
  value       = length(google_compute_region_instance_group_manager.default) > 0 ? google_compute_region_instance_group_manager.default[0].id : null
}

output "instance_group_manager_self_link" {
  description = "Self link of the instance group manager"
  value       = var.create_resources ? google_compute_region_instance_group_manager.default[0].self_link : null
}

output "autoscaler_id" {
  description = "ID of the autoscaler"
  value       = var.create_resources ? google_compute_region_autoscaler.default[0].id : null
}

output "health_check_id" {
  description = "ID of the health check"
  value       = length(google_compute_health_check.default) > 0 ? google_compute_health_check.default[0].id : (length(data.google_compute_health_check.existing) > 0 ? data.google_compute_health_check.existing[0].id : null)
}

output "instance_group" {
  description = "The managed instance group"
  value       = var.create_resources ? google_compute_region_instance_group_manager.default[0].instance_group : null
}

# These outputs are mapped in the parent module but actual values
# would require additional data sources to extract from the instance group
output "instance_ids" {
  description = "The IDs of the compute instances (requires additional data source)"
  value       = []
}

output "instance_names" {
  description = "The names of the compute instances (requires additional data source)"
  value       = []
}

output "instance_ips" {
  description = "The IPs of the compute instances (requires additional data source)"
  value       = []
}

output "service_account_email" {
  description = "The service account email used by compute resources"
  value       = var.service_account_email
}

# Cloud Run outputs
output "api_service_url" {
  description = "The URL of the API service"
  value       = length(google_cloud_run_v2_service.api) > 0 ? google_cloud_run_v2_service.api[0].uri : null
}

output "api_service_id" {
  description = "The ID of the API service"
  value       = length(google_cloud_run_v2_service.api) > 0 ? google_cloud_run_v2_service.api[0].id : null
}

output "api_service_name" {
  description = "The name of the API service"
  value       = local.api_service_name
}

output "workers_service_id" {
  description = "The ID of the Workers service"
  value       = length(google_cloud_run_v2_service.workers) > 0 ? google_cloud_run_v2_service.workers[0].id : null
}

output "workers_service_name" {
  description = "The name of the Workers service"
  value       = local.workers_service_name
}

# Messaging service outputs removed - service has been decommissioned

output "vpc_connector_id" {
  description = "The ID of the VPC connector"
  value       = length(google_vpc_access_connector.connector) > 0 ? google_vpc_access_connector.connector[0].id : var.vpc_connector_id
}

output "vpc_connector_name" {
  description = "The name of the VPC connector"
  value       = length(google_vpc_access_connector.connector) > 0 ? google_vpc_access_connector.connector[0].name : var.vpc_connector_id
} 