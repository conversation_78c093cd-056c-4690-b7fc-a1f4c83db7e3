/**
 * Terraform module for Google Cloud Load Balancer with SSL configuration.
 * 
 * This module creates a Google Cloud Load Balancer with HTTPS/SSL support,
 * configures certificates, and sets up HTTP to HTTPS redirects.
 */

variable "project_id" {
  description = "The Google Cloud project ID"
  type        = string
}

variable "region" {
  description = "The Google Cloud region"
  type        = string
}

variable "name" {
  description = "The name of the load balancer"
  type        = string
}

variable "domain" {
  description = "The domain name for the SSL certificate"
  type        = string
}

variable "backend_service_name" {
  description = "The name of the backend service"
  type        = string
}

# Certificate Manager certificate
resource "google_certificate_manager_certificate" "default" {
  name        = "${var.name}-cert"
  description = "SSL certificate for ${var.domain}"

  managed {
    domains            = [var.domain, "*.${var.domain}"]
    dns_authorizations = []
  }

  labels = {
    "environment" = terraform.workspace
  }
}

# Certificate Manager certificate map
resource "google_certificate_manager_certificate_map" "default" {
  name        = "${var.name}-cert-map"
  description = "Certificate map for ${var.domain}"

  labels = {
    "environment" = terraform.workspace
  }
}

# Certificate Manager certificate map entry
resource "google_certificate_manager_certificate_map_entry" "default" {
  name        = "${var.name}-cert-map-entry"
  description = "Certificate map entry for ${var.domain}"
  map         = google_certificate_manager_certificate_map.default.name

  certificates = [google_certificate_manager_certificate.default.id]

  hostname = var.domain
  matcher  = "PRIMARY"
}

# External IP address
resource "google_compute_global_address" "default" {
  name = "${var.name}-ip"
}

# HTTPS target proxy
resource "google_compute_target_https_proxy" "default" {
  name            = "${var.name}-https-proxy"
  url_map         = google_compute_url_map.https.id
  certificate_map = "//certificatemanager.googleapis.com/${google_certificate_manager_certificate_map.default.id}"
}

# HTTP target proxy (for redirects)
resource "google_compute_target_http_proxy" "default" {
  name    = "${var.name}-http-proxy"
  url_map = google_compute_url_map.http.id
}

# HTTPS forwarding rule
resource "google_compute_global_forwarding_rule" "https" {
  name       = "${var.name}-https-rule"
  target     = google_compute_target_https_proxy.default.id
  port_range = "443"
  ip_address = google_compute_global_address.default.address
}

# HTTP forwarding rule (for redirects)
resource "google_compute_global_forwarding_rule" "http" {
  name       = "${var.name}-http-rule"
  target     = google_compute_target_http_proxy.default.id
  port_range = "80"
  ip_address = google_compute_global_address.default.address
}

# URL map for HTTPS
resource "google_compute_url_map" "https" {
  name            = "${var.name}-https-url-map"
  default_service = var.backend_service_name
}

# URL map for HTTP (redirects to HTTPS)
resource "google_compute_url_map" "http" {
  name = "${var.name}-http-url-map"

  default_url_redirect {
    https_redirect         = true
    redirect_response_code = "MOVED_PERMANENTLY_DEFAULT" # 301
    strip_query            = false
  }
}

output "load_balancer_ip" {
  description = "The IP address of the load balancer"
  value       = google_compute_global_address.default.address
}

output "certificate_id" {
  description = "The ID of the certificate"
  value       = google_certificate_manager_certificate.default.id
}

output "certificate_map_id" {
  description = "The ID of the certificate map"
  value       = google_certificate_manager_certificate_map.default.id
}
