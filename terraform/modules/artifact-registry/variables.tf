variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "region" {
  description = "The GCP region to deploy resources"
  type        = string
}

variable "environment" {
  description = "The deployment environment (e.g. dev, staging, prod)"
  type        = string
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "The environment must be one of: dev, staging, prod."
  }
}

variable "service_account_email" {
  description = "The service account email that will have writer access to the repository (e.g. GitHub Actions SA)"
  type        = string
}

variable "compute_service_account" {
  description = "The service account email that will have reader access to the repository (e.g. Cloud Run SA)"
  type        = string
}

variable "create_resources" {
  description = "Whether to create new resources or just reference existing ones"
  type        = bool
  default     = false
}

variable "existing_resources_exist" {
  description = "Whether to look up existing resources using data sources"
  type        = bool
  default     = true
}

variable "dependencies" {
  description = "List of dependencies that must be created before this module can be deployed"
  type        = list(any)
  default     = []
} 