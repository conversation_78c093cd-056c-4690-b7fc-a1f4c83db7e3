output "repository_id" {
  description = "The ID of the Artifact Registry repository"
  value       = local.effective_repository_id
}

output "repository_name" {
  description = "The name of the Artifact Registry repository"
  value       = local.repository_name
}

output "repository_location" {
  description = "The location of the Artifact Registry repository"
  value       = var.region
}

output "repository_url" {
  description = "The URL of the Artifact Registry repository"
  value       = "${var.region}-docker.pkg.dev/${var.project_id}/${local.effective_repository_id}"
}

output "full_repository_url" {
  description = "The full URL of the Artifact Registry repository (including hostname)"
  value       = "${var.region}-docker.pkg.dev/${var.project_id}/${local.repository_name}"
} 