/**
 * # Artifact Registry Module
 *
 * This module creates and manages Google Cloud Artifact Registry repositories for container images.
 * It provisions repositories for storing Docker images that will be deployed to Cloud Run.
 */

locals {
  repository_name = "a2a-${var.environment}-containers"
  # Determine if we need to create a new repository
  create_new_repository = var.create_resources && !var.existing_resources_exist
}

# First check if the repository already exists
data "google_artifact_registry_repository" "existing" {
  count         = var.existing_resources_exist ? 1 : 0
  provider      = google-beta
  project       = var.project_id
  location      = var.region
  repository_id = local.repository_name
}

# Only create the repository if it doesn't exist and we're supposed to create resources
resource "google_artifact_registry_repository" "containers" {
  count         = local.create_new_repository ? 1 : 0
  provider      = google-beta
  project       = var.project_id
  location      = var.region
  repository_id = local.repository_name
  description   = "A2A Platform Container Registry for ${title(var.environment)} Environment"
  format        = "DOCKER"

  docker_config {
    immutable_tags = var.environment == "prod" ? true : false
  }

  # Disable virtual repository config as it has compatibility issues
  # with the current provider version

  labels = {
    environment = var.environment
    managed_by  = "terraform"
  }

  # Prevent recreation
  lifecycle {
    prevent_destroy = true
    ignore_changes = [
      docker_config
    ]
  }
}

# Determine the repository ID to use for IAM bindings
locals {
  effective_repository_id = var.existing_resources_exist ? (
    length(data.google_artifact_registry_repository.existing) > 0 ? data.google_artifact_registry_repository.existing[0].repository_id : local.repository_name
    ) : (
    local.create_new_repository ? google_artifact_registry_repository.containers[0].repository_id : local.repository_name
  )
}

# IAM policy for repository
resource "google_artifact_registry_repository_iam_member" "github_actions_writer" {
  provider   = google-beta
  project    = var.project_id
  location   = var.region
  repository = local.effective_repository_id
  role       = "roles/artifactregistry.writer"
  member     = "serviceAccount:${var.service_account_email}"

  depends_on = [google_artifact_registry_repository.containers, data.google_artifact_registry_repository.existing]
}

resource "google_artifact_registry_repository_iam_member" "cloud_run_reader" {
  provider   = google-beta
  project    = var.project_id
  location   = var.region
  repository = local.effective_repository_id
  role       = "roles/artifactregistry.reader"
  member     = "serviceAccount:${var.compute_service_account}"

  depends_on = [google_artifact_registry_repository.containers, data.google_artifact_registry_repository.existing]
} 