# Import blocks for existing Cloudflare resources
# Uncomment and configure these import blocks when you need to import existing resources

# Example import blocks - uncomment and modify as needed:

# Import existing DNS records
# import {
#   to = cloudflare_record.web[0]
#   id = "${var.cloudflare_zone_id}/${var.environment == "production" ? "@" : var.environment}"
# }

# import {
#   to = cloudflare_record.api[0]
#   id = "${var.cloudflare_zone_id}/${var.environment == "production" ? "api" : "api-${var.environment}"}"
# }

# import {
#   to = cloudflare_record.websocket[0]
#   id = "${var.cloudflare_zone_id}/${var.environment == "production" ? "ws" : "ws-${var.environment}"}"
# }

# Import existing page rules (requires page rule IDs from Cloudflare dashboard)
# import {
#   to = cloudflare_page_rule.static_assets[0]
#   id = "${var.cloudflare_zone_id}/PAGE_RULE_ID_HERE"
# }

# import {
#   to = cloudflare_page_rule.html_cache[0]
#   id = "${var.cloudflare_zone_id}/PAGE_RULE_ID_HERE"
# }

# import {
#   to = cloudflare_page_rule.api_bypass[0]
#   id = "${var.cloudflare_zone_id}/PAGE_RULE_ID_HERE"
# }

# Import existing Cloudflare Workers (only if create_workers=true)
# import {
#   to = cloudflare_workers_script.spa_router[0]
#   id = "${var.cloudflare_account_id}/WORKER_SCRIPT_NAME"
# }

# import {
#   to = cloudflare_workers_route.spa_router[0]
#   id = "${var.cloudflare_zone_id}/WORKER_ROUTE_ID"
# }

# import {
#   to = cloudflare_page_rule.api_cache[0]
#   id = "${var.cloudflare_zone_id}/PAGE_RULE_ID_HERE"
# }

# import {
#   to = cloudflare_page_rule.graphql_cache[0]
#   id = "${var.cloudflare_zone_id}/PAGE_RULE_ID_HERE"
# }

# Instructions:
# 1. To import existing resources, uncomment the relevant import blocks above
# 2. Replace PAGE_RULE_ID_HERE with actual page rule IDs from Cloudflare dashboard
# 3. Set create_dns_records = false and/or create_page_rules = false in your terraform.tfvars
# 4. Run terraform plan to see what will be imported
# 5. Run terraform apply to complete the import
#
# To find page rule IDs:
# - Go to Cloudflare dashboard > Your domain > Rules > Page Rules
# - Click on a rule to see its ID in the URL or use the Cloudflare API
#
# Alternative: Use the import script:
# ./terraform/scripts/import_cloudflare_resources.sh
