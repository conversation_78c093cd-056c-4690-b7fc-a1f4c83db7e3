variable "root_domain" {
  description = "The root domain for all services (e.g., vedavivi.app)"
  type        = string
}

variable "environment" {
  description = "The environment (production, staging, development)"
  type        = string
  validation {
    condition     = contains(["production", "staging", "development"], var.environment)
    error_message = "Environment must be one of: production, staging, development."
  }
}

variable "cloudflare_plan" {
  description = "The Cloudflare plan to use"
  type        = string
  default     = "free" # Using free tier as per specification requirements
  validation {
    condition     = contains(["free", "pro", "business", "enterprise"], var.cloudflare_plan)
    error_message = "Cloudflare plan must be one of: free, pro, business, enterprise."
  }
}

variable "cloudflare_account_id" {
  description = "The Cloudflare account ID"
  type        = string
}

variable "cloudflare_zone_id" {
  description = "Existing Cloudflare Zone ID. If provided, will use existing zone instead of creating new one."
  type        = string
  default     = ""
}

variable "auto_detect_origins" {
  description = "Whether to automatically detect origin URLs from Cloud Run services"
  type        = bool
  default     = true
}

variable "gcp_project_id" {
  description = "GCP Project ID for auto-detecting Cloud Run services. Required when auto_detect_origins is true."
  type        = string
  default     = ""
  validation {
    condition     = var.gcp_project_id != null
    error_message = "gcp_project_id cannot be null."
  }
}

variable "gcp_region" {
  description = "GCP Region for auto-detecting Cloud Run services. Required when auto_detect_origins is true."
  type        = string
  default     = "us-central1"
  validation {
    condition     = var.gcp_region != null && var.gcp_region != ""
    error_message = "gcp_region cannot be null or empty."
  }
}

variable "zone_config" {
  description = "Cloudflare zone configuration settings"
  type = object({
    jump_start = optional(bool, false)    # Whether to automatically attempt to fetch existing DNS records
    paused     = optional(bool, false)    # Whether the zone is paused (traffic bypasses Cloudflare)
    type       = optional(string, "full") # Zone type: full, partial, secondary
  })
  default = {}
  validation {
    condition     = contains(["full", "partial", "secondary"], lookup(var.zone_config, "type", "full"))
    error_message = "Zone type must be one of: full, partial, secondary."
  }
}

variable "create_dns_records" {
  description = "Whether to create DNS records. Set to false if records already exist and you want to import them instead."
  type        = bool
  default     = true
}

variable "create_page_rules" {
  description = "Whether to create page rules. Set to false if rules already exist and you want to import them instead. Default is true."
  type        = bool
  default     = true
}

variable "create_workers" {
  description = "Whether to create Cloudflare Workers for SPA routing. Set to false if workers already exist or are not needed."
  type        = bool
  default     = false # Default to not creating workers to avoid conflicts
}



variable "storage_origin_url" {
  description = "The URL of the storage service to use as origin for static assets (e.g., Cloudflare R2 bucket URL)"
  type        = string

  validation {
    condition     = can(regex("^https://[a-zA-Z0-9.-]+", var.storage_origin_url))
    error_message = "storage_origin_url must be a valid HTTPS URL."
  }
}



variable "backend_origin_url" {
  description = "The origin URL for backend API services (e.g., Cloud Run instance). If empty, API DNS record will not be created."
  type        = string
  default     = ""

  validation {
    condition     = var.backend_origin_url == "" || can(regex("^https?://[a-zA-Z0-9.-]+", var.backend_origin_url))
    error_message = "backend_origin_url must be a valid HTTP/HTTPS URL or empty string."
  }
}

variable "websocket_origin_url" {
  description = "The origin URL for WebSocket connections (e.g., Cloud Run instance). If empty, WebSocket DNS record will not be created."
  type        = string
  default     = ""

  validation {
    condition     = var.websocket_origin_url == "" || can(regex("^(https?|wss?)://[a-zA-Z0-9.-]+", var.websocket_origin_url))
    error_message = "websocket_origin_url must be a valid HTTP/HTTPS/WS/WSS URL or empty string."
  }
}

variable "enable_websockets" {
  description = "Whether to enable WebSocket support"
  type        = bool
  default     = true
}

# Cache Configuration Variables
variable "cache_config" {
  description = "CDN cache configuration settings"
  type = object({
    # Frontend static assets caching
    static_assets_edge_ttl    = optional(number, 31536000) # 1 year default
    static_assets_browser_ttl = optional(number, 31536000) # 1 year default

    # HTML files caching
    html_edge_ttl    = optional(number, 7200) # 2 hours default (Free plan minimum)
    html_browser_ttl = optional(number, 7200) # 2 hours default (Free plan minimum)

    # API caching configuration
    api_cache_enabled    = optional(bool, false)              # Disabled by default
    api_edge_ttl         = optional(number, 120)              # 2 minutes (Free plan minimum)
    api_browser_ttl      = optional(number, 0)                # No browser cache for APIs
    api_cache_by_device  = optional(bool, false)              # Cache by device type (paid plans only)
    api_cache_key_fields = optional(list(string), ["accept"]) # Cache key fields (paid plans only)

    # GraphQL specific caching
    graphql_cache_enabled   = optional(bool, false) # Disabled by default
    graphql_edge_ttl        = optional(number, 120) # 2 minutes (Free plan minimum)
    graphql_browser_ttl     = optional(number, 0)   # No browser cache for GraphQL
    graphql_cache_by_device = optional(bool, false) # Cache by device type (paid plans only)

    # Global cache settings
    development_mode  = optional(bool, false)          # Bypass cache in development
    cache_level       = optional(string, "aggressive") # Cache level: basic, simplified, aggressive
    browser_cache_ttl = optional(number, 14400)        # Default browser cache (4 hours)
  })
  default = {}
}
