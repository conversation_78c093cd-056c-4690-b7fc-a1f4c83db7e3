output "zone_id" {
  description = "The Cloudflare Zone ID"
  value       = local.zone_id
}

output "web_url" {
  description = "The web application URL"
  value       = local.web_url
}

output "api_url" {
  description = "The API URL"
  value       = local.api_url
}

output "websocket_url" {
  description = "The WebSocket URL"
  value       = local.websocket_url
}

output "web_domain" {
  description = "The web domain name"
  value       = local.web_domain
}

output "api_domain" {
  description = "The API domain name"
  value       = local.api_domain
}

output "ws_domain" {
  description = "The WebSocket domain name"
  value       = local.ws_domain
}

output "detected_backend_url" {
  description = "Auto-detected backend URL from Cloud Run"
  value       = local.detected_backend_url
}

output "detected_websocket_url" {
  description = "Auto-detected WebSocket URL from Cloud Run"
  value       = local.detected_websocket_url
}

output "effective_backend_url" {
  description = "Effective backend URL (detected or provided)"
  value       = local.effective_backend_url
}

output "effective_websocket_url" {
  description = "Effective WebSocket URL (detected or provided)"
  value       = local.effective_websocket_url
}

output "cache_config" {
  description = "Complete applied cache configuration for observability and debugging"
  value = {
    # Global cache settings
    development_mode  = var.cache_config.development_mode
    cache_level       = var.cache_config.cache_level
    browser_cache_ttl = var.cache_config.browser_cache_ttl

    # Frontend static assets caching
    static_assets = {
      edge_ttl    = var.cache_config.static_assets_edge_ttl
      browser_ttl = var.cache_config.static_assets_browser_ttl
    }

    # HTML files caching
    html = {
      edge_ttl    = var.cache_config.html_edge_ttl
      browser_ttl = var.cache_config.html_browser_ttl
    }

    # API caching configuration
    api = {
      cache_enabled    = var.cache_config.api_cache_enabled
      edge_ttl         = var.cache_config.api_edge_ttl
      browser_ttl      = var.cache_config.api_browser_ttl
      cache_by_device  = var.cache_config.api_cache_by_device
      cache_key_fields = var.cache_config.api_cache_key_fields
    }

    # GraphQL specific caching
    graphql = {
      cache_enabled   = var.cache_config.graphql_cache_enabled
      edge_ttl        = var.cache_config.graphql_edge_ttl
      browser_ttl     = var.cache_config.graphql_browser_ttl
      cache_by_device = var.cache_config.graphql_cache_by_device
    }
  }
}

output "name_servers" {
  description = "The Cloudflare name servers for the domain"
  value       = var.cloudflare_zone_id != "" ? tolist(data.cloudflare_zone.existing[0].name_servers) : tolist(cloudflare_zone.main[0].name_servers)
}

output "zone_config" {
  description = "Complete zone configuration for observability and debugging - reflects actual deployed state"
  value = var.cloudflare_zone_id != "" ? {
    # Values from existing zone (data source has limited attributes)
    zone_id      = tostring(data.cloudflare_zone.existing[0].id)
    zone_name    = tostring(data.cloudflare_zone.existing[0].name)
    account_id   = tostring(data.cloudflare_zone.existing[0].account_id)
    plan         = tostring(data.cloudflare_zone.existing[0].plan)
    paused       = tobool(data.cloudflare_zone.existing[0].paused)
    status       = tostring(data.cloudflare_zone.existing[0].status)
    name_servers = tolist(data.cloudflare_zone.existing[0].name_servers)
    source       = "existing_zone"
    # Note: type and jump_start not available from data source, using defaults
    type       = "full"
    jump_start = false
    } : {
    # Values from created zone (resource has all attributes)
    zone_id      = tostring(cloudflare_zone.main[0].id)
    zone_name    = tostring(cloudflare_zone.main[0].zone)
    account_id   = tostring(cloudflare_zone.main[0].account_id)
    plan         = tostring(cloudflare_zone.main[0].plan)
    type         = tostring(cloudflare_zone.main[0].type)
    jump_start   = tobool(cloudflare_zone.main[0].jump_start)
    paused       = tobool(cloudflare_zone.main[0].paused)
    status       = tostring(cloudflare_zone.main[0].status)
    name_servers = tolist(cloudflare_zone.main[0].name_servers)
    source       = "created_zone"
  }
}



output "cache_rules_summary" {
  description = "Summary of active cache rules for debugging"
  value = {
    static_assets_rule = {
      target   = "${local.web_domain}/assets/*"
      priority = 1
      active   = true
    }
    html_cache_rule = {
      target   = "${local.web_domain}/*.html"
      priority = 2
      active   = true
    }
    api_cache_rule = {
      target   = "${local.api_domain}/*"
      priority = 3
      active   = var.cache_config.api_cache_enabled
      type     = var.cache_config.api_cache_enabled ? "cache_everything" : "bypass"
    }
    graphql_cache_rule = {
      target   = "${local.api_domain}/graphql*"
      priority = 4
      active   = var.cache_config.graphql_cache_enabled
      type     = var.cache_config.graphql_cache_enabled ? "cache_everything" : "bypass"
    }
  }
}

output "cache_performance_info" {
  description = "Cache configuration performance information"
  value = {
    # TTL information for performance analysis
    max_edge_ttl = max(
      var.cache_config.static_assets_edge_ttl,
      var.cache_config.html_edge_ttl,
      var.cache_config.api_cache_enabled ? var.cache_config.api_edge_ttl : 0,
      var.cache_config.graphql_cache_enabled ? var.cache_config.graphql_edge_ttl : 0
    )
    min_edge_ttl = min(
      var.cache_config.static_assets_edge_ttl,
      var.cache_config.html_edge_ttl,
      var.cache_config.api_cache_enabled ? var.cache_config.api_edge_ttl : var.cache_config.static_assets_edge_ttl,
      var.cache_config.graphql_cache_enabled ? var.cache_config.graphql_edge_ttl : var.cache_config.static_assets_edge_ttl
    )

    # Cache strategy summary
    total_cached_endpoints = (
      2 + # static_assets + html (always cached)
      (var.cache_config.api_cache_enabled ? 1 : 0) +
      (var.cache_config.graphql_cache_enabled ? 1 : 0)
    )
    total_bypassed_endpoints = (
      (var.cache_config.api_cache_enabled ? 0 : 1) +
      (var.cache_config.graphql_cache_enabled ? 0 : 1)
    )

    # Performance flags
    has_device_specific_caching = (
      var.cache_config.api_cache_by_device ||
      var.cache_config.graphql_cache_by_device
    )
    development_mode_active = var.cache_config.development_mode
  }
}

output "dns_records_status" {
  description = "Status of DNS record creation - shows which records were created vs skipped"
  value = {
    create_dns_records_enabled = var.create_dns_records
    web_record = {
      created = var.create_dns_records
      domain  = local.web_domain
      reason  = var.create_dns_records ? "Created for static web hosting (with lifecycle protection)" : "Skipped - create_dns_records=false (records may be imported)"
    }
    api_record = {
      created = var.create_dns_records && var.backend_origin_url != ""
      domain  = local.api_domain
      reason = !var.create_dns_records ? "Skipped - create_dns_records=false (records may be imported)" : (
        var.backend_origin_url != "" ? "Created with backend origin (with lifecycle protection)" : "Skipped - backend_origin_url is empty"
      )
    }
    websocket_record = {
      created = var.create_dns_records && var.enable_websockets && var.websocket_origin_url != ""
      domain  = local.ws_domain
      reason = !var.create_dns_records ? "Skipped - create_dns_records=false (records may be imported)" : (
        var.enable_websockets ? (
          var.websocket_origin_url != "" ? "Created with WebSocket origin (with lifecycle protection)" : "Skipped - websocket_origin_url is empty"
        ) : "Skipped - WebSockets disabled"
      )
    }
    spectrum_application = {
      created = var.enable_websockets && var.cloudflare_plan != "free" && var.websocket_origin_url != ""
      domain  = local.ws_domain
      reason = var.enable_websockets ? (
        var.cloudflare_plan != "free" ? (
          var.websocket_origin_url != "" ? "Created with paid plan and WebSocket origin" : "Skipped - websocket_origin_url is empty"
        ) : "Skipped - requires paid plan"
      ) : "Skipped - WebSockets disabled"
    }
  }
}

output "page_rules_status" {
  description = "Status of page rule creation - shows which rules were created vs skipped"
  value = {
    create_page_rules_enabled = var.create_page_rules
    static_assets_rule = {
      created = local.should_create_page_rules
      target  = "${local.web_domain}/assets/*"
      reason  = var.create_page_rules ? "Created for static asset caching" : "Skipped - create_page_rules=false (rules may be imported)"
    }
    html_cache_rule = {
      created = local.should_create_page_rules
      target  = "${local.web_domain}/*.html"
      reason  = var.create_page_rules ? "Created for HTML caching" : "Skipped - create_page_rules=false (rules may be imported)"
    }
    api_cache_rule = {
      created = local.should_create_page_rules && var.cache_config.api_cache_enabled && var.cloudflare_plan != "free"
      target  = "${local.api_domain}/*"
      reason = !var.create_page_rules ? "Skipped - create_page_rules=false (rules may be imported)" : (
        var.cache_config.api_cache_enabled ? (
          var.cloudflare_plan != "free" ? "Created for API caching (paid plan)" : "Skipped - requires paid plan"
        ) : "Skipped - API caching disabled"
      )
    }
    api_bypass_rule = {
      created = local.should_create_page_rules && (!var.cache_config.api_cache_enabled || !var.cache_config.graphql_cache_enabled)
      target  = "${local.api_domain}/*"
      reason = !var.create_page_rules ? "Skipped - create_page_rules=false (rules may be imported)" : (
        (!var.cache_config.api_cache_enabled || !var.cache_config.graphql_cache_enabled) ? "Created for API/GraphQL bypass" : "Skipped - caching enabled"
      )
    }
    graphql_cache_rule = {
      created = local.should_create_page_rules && var.cache_config.graphql_cache_enabled && var.cloudflare_plan != "free"
      target  = "${local.api_domain}/graphql*"
      reason = !var.create_page_rules ? "Skipped - create_page_rules=false (rules may be imported)" : (
        var.cache_config.graphql_cache_enabled ? (
          var.cloudflare_plan != "free" ? "Created for GraphQL caching (paid plan)" : "Skipped - requires paid plan"
        ) : "Skipped - GraphQL caching disabled"
      )
    }
  }
}



output "configuration_warnings" {
  description = "Configuration warnings for missing or incomplete settings"
  value = {
    missing_backend_origin   = var.backend_origin_url == "" ? "⚠️  API DNS record not created - set backend_origin_url to enable API routing" : null
    missing_websocket_origin = var.enable_websockets && var.websocket_origin_url == "" ? "⚠️  WebSocket DNS record not created - set websocket_origin_url to enable WebSocket routing" : null
    free_plan_limitations    = var.cloudflare_plan == "free" ? "ℹ️  Using Free plan - advanced caching features and Spectrum disabled" : null
    websockets_disabled      = !var.enable_websockets ? "ℹ️  WebSocket support is disabled" : null
    workers_disabled         = !var.create_workers ? "ℹ️  Cloudflare Workers creation is disabled - set create_workers=true to enable SPA routing" : null
    lifecycle_protection     = "ℹ️  DNS records have lifecycle protection to prevent conflicts with existing records"
  }
}

output "workers_status" {
  description = "Status of Cloudflare Workers creation"
  value = {
    create_workers_enabled = var.create_workers
    spa_router_worker = {
      created = var.create_workers
      name    = var.create_workers ? "${var.environment}-spa-router" : null
      reason  = var.create_workers ? "Created for SPA routing functionality" : "Skipped - create_workers=false (workers may be imported or not needed)"
    }
    spa_router_route = {
      created = var.create_workers
      pattern = var.create_workers ? "${local.web_domain}/*" : null
      reason  = var.create_workers ? "Created to handle SPA routing for all web routes" : "Skipped - create_workers=false (routes may be imported or not needed)"
    }
  }
}
