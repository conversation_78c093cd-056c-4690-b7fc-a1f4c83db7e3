# Cloudflare CDN Module for A2A Platform

# Validation check for auto_detect_origins dependencies
locals {
  validate_auto_detect_config = var.auto_detect_origins && var.gcp_project_id == "" ? tobool("ERROR: gcp_project_id must not be empty when auto_detect_origins is true") : true
}

# Data sources to get Cloud Run service URLs
data "google_cloud_run_service" "api" {
  count    = var.auto_detect_origins ? 1 : 0
  name     = "api-${var.environment}"
  location = var.gcp_region
  project  = var.gcp_project_id
}

data "google_cloud_run_service" "workers" {
  count    = var.auto_detect_origins && var.enable_websockets ? 1 : 0
  name     = "workers-${var.environment}"
  location = var.gcp_region
  project  = var.gcp_project_id
}

# Domain calculation based on environment
locals {
  # Calculate domain names based on environment
  web_domain = var.environment == "production" ? var.root_domain : "${var.environment}.${var.root_domain}"
  api_domain = var.environment == "production" ? "api.${var.root_domain}" : "api-${var.environment}.${var.root_domain}"
  ws_domain  = var.environment == "production" ? "ws.${var.root_domain}" : "ws-${var.environment}.${var.root_domain}"

  # Storage origin URL for R2 or other storage services
  effective_storage_url = var.storage_origin_url

  # Auto-detect origin URLs from Cloud Run services or use provided URLs
  detected_backend_url   = var.auto_detect_origins && length(data.google_cloud_run_service.api) > 0 ? data.google_cloud_run_service.api[0].status[0].url : ""
  detected_websocket_url = var.auto_detect_origins && var.enable_websockets && length(data.google_cloud_run_service.workers) > 0 ? data.google_cloud_run_service.workers[0].status[0].url : ""

  # Use detected URLs if available, otherwise fall back to provided URLs
  effective_backend_url   = local.detected_backend_url != "" ? local.detected_backend_url : var.backend_origin_url
  effective_websocket_url = local.detected_websocket_url != "" ? local.detected_websocket_url : var.websocket_origin_url

  # Calculate full URLs
  web_url       = "https://${local.web_domain}"
  api_url       = "https://${local.api_domain}"
  websocket_url = "wss://${local.ws_domain}"
}

# Conditional zone management based on cloudflare_zone_id variable
# Create new zone only if zone_id is not provided (requires Zone:Create permission)
# Using lookup() for safe access to zone_config attributes with defaults
resource "cloudflare_zone" "main" {
  count      = var.cloudflare_zone_id == "" ? 1 : 0
  account_id = var.cloudflare_account_id
  zone       = var.root_domain
  jump_start = lookup(var.zone_config, "jump_start", false)
  paused     = lookup(var.zone_config, "paused", false)
  plan       = var.cloudflare_plan
  type       = lookup(var.zone_config, "type", "full")
}

# Use existing zone when zone_id is provided
data "cloudflare_zone" "existing" {
  count   = var.cloudflare_zone_id != "" ? 1 : 0
  zone_id = var.cloudflare_zone_id
}

# Local value to reference the zone regardless of creation method
locals {
  zone_id = var.cloudflare_zone_id != "" ? data.cloudflare_zone.existing[0].id : cloudflare_zone.main[0].id
}

# DNS Records for Web Application
# Points to the storage service URL (Cloudflare R2, GCS, etc.)
resource "cloudflare_record" "web" {
  count   = var.create_dns_records ? 1 : 0
  zone_id = local.zone_id
  name    = var.environment == "production" ? "@" : var.environment
  content = replace(local.effective_storage_url, "https://", "")
  type    = "CNAME"
  proxied = true
  ttl     = 1

  lifecycle {
    # Prevent destruction if the record already exists
    prevent_destroy = true
    # Uncomment to ignore external changes if managing DNS externally:
    # ignore_changes = [content, proxied]
  }
}

# DNS Records for API
resource "cloudflare_record" "api" {
  count   = var.create_dns_records ? 1 : 0
  zone_id = local.zone_id
  name    = var.environment == "production" ? "api" : "api-${var.environment}"
  content = local.effective_backend_url != "" ? replace(replace(local.effective_backend_url, "https://", ""), "http://", "") : "vedavivi.app"
  type    = "CNAME"
  proxied = true
  ttl     = 1

  lifecycle {
    # Create before destroy to handle updates gracefully
    create_before_destroy = true
    # Allow content changes when auto_detect_origins is enabled
    # Uncomment to ignore external changes if managing DNS externally:
    # ignore_changes = [content, proxied]
  }
}

# DNS Records for WebSocket
resource "cloudflare_record" "websocket" {
  count   = var.create_dns_records && var.enable_websockets ? 1 : 0
  zone_id = local.zone_id
  name    = var.environment == "production" ? "ws" : "ws-${var.environment}"
  content = local.effective_websocket_url != "" ? replace(replace(local.effective_websocket_url, "https://", ""), "http://", "") : "vedavivi.app"
  type    = "CNAME"
  proxied = true
  ttl     = 1

  lifecycle {
    # Create before destroy to handle updates gracefully
    create_before_destroy = true
    # Ignore content changes to allow external management of origin URL
    ignore_changes = [content]
  }
}

# Data sources for existing DNS records (when create_dns_records = false)
data "cloudflare_record" "existing_web" {
  count    = !var.create_dns_records ? 1 : 0
  zone_id  = local.zone_id
  hostname = local.web_domain
  type     = "CNAME"
}

data "cloudflare_record" "existing_api" {
  count    = !var.create_dns_records ? 1 : 0
  zone_id  = local.zone_id
  hostname = local.api_domain
  type     = "CNAME"
}

data "cloudflare_record" "existing_websocket" {
  count    = !var.create_dns_records && var.enable_websockets ? 1 : 0
  zone_id  = local.zone_id
  hostname = local.ws_domain
  type     = "CNAME"
}

# Conditional resource creation logic
# Default to creating page rules unless explicitly disabled via create_page_rules = false
# This allows for graceful handling of existing resources through import workflows
locals {
  # Default to creating page rules unless explicitly disabled
  should_create_page_rules = var.create_page_rules != false
}

# Standard lifecycle configuration for all page rules
# All page rules use create_before_destroy for graceful updates
# Uncomment ignore_changes lines to ignore external modifications to imported rules

# Page Rules for Configurable Caching
resource "cloudflare_page_rule" "static_assets" {
  count    = local.should_create_page_rules ? 1 : 0
  zone_id  = local.zone_id
  target   = "${local.web_domain}/assets/*"
  priority = 1

  actions {
    cache_level       = "cache_everything"
    edge_cache_ttl    = var.cache_config.static_assets_edge_ttl
    browser_cache_ttl = var.cache_config.static_assets_browser_ttl
  }

  lifecycle {
    create_before_destroy = true
    # Uncomment to ignore external changes to imported rules:
    # ignore_changes = ["priority", "actions"]
  }
}



resource "cloudflare_page_rule" "html_cache" {
  count    = local.should_create_page_rules ? 1 : 0
  zone_id  = local.zone_id
  target   = "${local.web_domain}/*.html"
  priority = 2

  actions {
    cache_level       = "cache_everything"
    edge_cache_ttl    = var.cache_config.html_edge_ttl
    browser_cache_ttl = var.cache_config.html_browser_ttl
  }

  lifecycle {
    create_before_destroy = true
    # Uncomment to ignore external changes to imported rules:
    # ignore_changes = ["priority", "actions"]
  }
}

# API Caching Rules - Simplified for Free plan compatibility
resource "cloudflare_page_rule" "api_cache" {
  count    = local.should_create_page_rules && var.cache_config.api_cache_enabled && var.cloudflare_plan != "free" ? 1 : 0
  zone_id  = local.zone_id
  target   = "${local.api_domain}/*"
  priority = 3

  actions {
    cache_level       = "cache_everything"
    edge_cache_ttl    = var.cache_config.api_edge_ttl
    browser_cache_ttl = var.cache_config.api_browser_ttl
    # Advanced caching features removed for Free plan compatibility:
    # - cache_by_device_type (not available on Free plan)
    # - cache_key_fields (not available on Free plan)
  }

  lifecycle {
    create_before_destroy = true
    # Uncomment to ignore external changes to imported rules:
    # ignore_changes = ["priority", "actions"]
  }
}

# Combined API Bypass Rule - Consolidates API and GraphQL bypass for Free plan limit (3 rules max)
# This single rule handles both API and GraphQL bypass when caching is disabled
resource "cloudflare_page_rule" "api_bypass" {
  count    = local.should_create_page_rules && (!var.cache_config.api_cache_enabled || !var.cache_config.graphql_cache_enabled) ? 1 : 0
  zone_id  = local.zone_id
  target   = "${local.api_domain}/*" # Covers both /api/* and /api/graphql*
  priority = 3

  actions {
    cache_level = "bypass"
  }

  lifecycle {
    create_before_destroy = true
    # Uncomment to ignore external changes to imported rules:
    # ignore_changes = ["priority", "actions"]
  }
}

# GraphQL Specific Caching Rules - Only for paid plans with caching enabled
resource "cloudflare_page_rule" "graphql_cache" {
  count    = local.should_create_page_rules && var.cache_config.graphql_cache_enabled && var.cloudflare_plan != "free" ? 1 : 0
  zone_id  = local.zone_id
  target   = "${local.api_domain}/graphql*"
  priority = 4

  actions {
    cache_level       = "cache_everything"
    edge_cache_ttl    = var.cache_config.graphql_edge_ttl
    browser_cache_ttl = var.cache_config.graphql_browser_ttl
    # Advanced caching features removed for Free plan compatibility:
    # - cache_by_device_type (not available on Free plan)
    # - cache_key_fields (not available on Free plan)
  }

  lifecycle {
    create_before_destroy = true
    # Uncomment to ignore external changes to imported rules:
    # ignore_changes = ["priority", "actions"]
  }
}

# WebSocket Support via Spectrum (requires paid plan and valid WebSocket origin)
# Spectrum is not available on Free plan, so we disable it for Free plan compatibility
# Also requires a valid websocket_origin_url to be configured
resource "cloudflare_spectrum_application" "websockets" {
  count    = var.enable_websockets && var.cloudflare_plan != "free" && var.websocket_origin_url != "" ? 1 : 0
  zone_id  = local.zone_id
  protocol = "tcp/443"
  tls      = "flexible"

  dns {
    type = "CNAME"
    name = local.ws_domain
  }

  origin_dns {
    name = var.websocket_origin_url != "" ? regex("^(?:https?://)?([^:/]+)", var.websocket_origin_url)[1] : ""
  }

  origin_port = 443
}

# SPA Routing Worker for client-side routing
resource "cloudflare_workers_script" "spa_router" {
  count      = var.create_workers ? 1 : 0
  account_id = var.cloudflare_account_id
  name       = "${var.environment}-spa-router"
  content    = file("${path.module}/spa-worker.js")
}

# Worker Route for SPA routing
resource "cloudflare_workers_route" "spa_router" {
  count       = var.create_workers ? 1 : 0
  zone_id     = local.zone_id
  pattern     = "${local.web_domain}/*"
  script_name = cloudflare_workers_script.spa_router[0].name
}

# Security Settings
resource "cloudflare_zone_settings_override" "security" {
  zone_id = local.zone_id

  settings {
    ssl                      = "flexible"
    always_use_https         = "on"
    min_tls_version          = "1.2"
    opportunistic_encryption = "on"
    tls_1_3                  = "on"
    automatic_https_rewrites = "on"
    # http2 removed - read-only setting, cannot be configured via API
    # zero_rtt removed - may be read-only on some plans
    security_level = "medium"
  }
}

# Cache Configuration - Configurable
resource "cloudflare_zone_settings_override" "caching" {
  zone_id = local.zone_id

  settings {
    browser_cache_ttl = var.cache_config.browser_cache_ttl
    cache_level       = var.cache_config.cache_level
    always_online     = "on"
    development_mode  = var.cache_config.development_mode ? "on" : "off"
  }
}


