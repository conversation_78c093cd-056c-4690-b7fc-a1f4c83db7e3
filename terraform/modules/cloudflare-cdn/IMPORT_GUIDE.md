# Cloudflare Resources Import Guide

This guide explains how to handle existing DNS records and page rules when migrating to Terraform management.

## Problem

When you have existing resources in Cloudflare and try to create them with Terraform, you'll get errors like:

```
Error: expected DNS record to not already be present but already exists
Error: failed to create page rule: Page Rule limit has been met (1008)
```

## Solution Options

### Option 1: Quick Setup (Recommended)

Use the setup script to configure imports for your environment:

```bash
# Run the setup script
./terraform/scripts/setup_imports.sh
```

The script will:
1. Ask what you want to import (DNS records, page rules, or both)
2. Automatically configure your terraform.tfvars file
3. Provide the exact import commands for your environment
4. Guide you through the next steps

### Option 2: Automated Import (Advanced)

Use the full import script for automatic detection and import:

```bash
# Run the import script
./terraform/scripts/import_cloudflare_resources.sh
```

### Option 3: Manual Import

If you prefer to import manually:

```bash
# Navigate to your environment directory
cd terraform/environments/staging  # or production

# Initialize and select workspace
terraform init
terraform workspace select staging || terraform workspace new staging

# Import DNS records (replace ZONE_ID and record names)
terraform import "module.cloudflare_cdn.cloudflare_record.web[0]" "ZONE_ID/staging"
terraform import "module.cloudflare_cdn.cloudflare_record.api[0]" "ZONE_ID/api-staging"
terraform import "module.cloudflare_cdn.cloudflare_record.websocket[0]" "ZONE_ID/ws-staging"

# Import page rules (replace ZONE_ID and PAGE_RULE_ID from Cloudflare dashboard)
terraform import "module.cloudflare_cdn.cloudflare_page_rule.static_assets[0]" "ZONE_ID/PAGE_RULE_ID"
terraform import "module.cloudflare_cdn.cloudflare_page_rule.html_cache[0]" "ZONE_ID/PAGE_RULE_ID"
terraform import "module.cloudflare_cdn.cloudflare_page_rule.api_bypass[0]" "ZONE_ID/PAGE_RULE_ID"

# For production, use these record names instead:
# terraform import "module.cloudflare_cdn.cloudflare_record.web[0]" "ZONE_ID/@"
# terraform import "module.cloudflare_cdn.cloudflare_record.api[0]" "ZONE_ID/api"
# terraform import "module.cloudflare_cdn.cloudflare_record.websocket[0]" "ZONE_ID/ws"
```

### Option 4: Delete and Recreate

⚠️ **Warning: This will cause downtime!**

If you don't mind temporary downtime, you can delete the existing records and page rules in Cloudflare and let Terraform create new ones.

## Configuration

### Using Imported Resources

After importing, add this to your `terraform.tfvars`:

```hcl
# Disable resource creation since they're imported
create_dns_records = false
create_page_rules = false
```

### Creating New Resources

If you want Terraform to create new resources (default behavior):

```hcl
# Enable resource creation (this is the default)
create_dns_records = true
create_page_rules = true
```

## Domain Name Patterns

The module creates DNS records with these patterns:

### Production Environment
- Web: `@` (root domain, e.g., `vedavivi.app`)
- API: `api` (e.g., `api.vedavivi.app`)
- WebSocket: `ws` (e.g., `ws.vedavivi.app`)

### Non-Production Environments
- Web: `{environment}` (e.g., `staging.vedavivi.app`)
- API: `api-{environment}` (e.g., `api-staging.vedavivi.app`)
- WebSocket: `ws-{environment}` (e.g., `ws-staging.vedavivi.app`)

## Finding Your Zone ID

You can find your Cloudflare Zone ID in:

1. **Cloudflare Dashboard**: Go to your domain → Overview → Zone ID (right sidebar)
2. **CLI**: `curl -X GET "https://api.cloudflare.com/client/v4/zones" -H "Authorization: Bearer YOUR_API_TOKEN"`
3. **Environment Variable**: It should be set as `CLOUDFLARE_ZONE_ID` in your GitHub secrets

## Troubleshooting

### Import Failed
If import fails, the record might:
- Not exist in Cloudflare
- Already be imported in Terraform state
- Have a different name than expected

Check the actual record names in Cloudflare dashboard.

### Plan Shows Changes After Import
This is normal. Run `terraform apply` to align the imported records with your configuration.

### Record Names Don't Match
Verify the environment and domain configuration in your `terraform.tfvars` matches what's in Cloudflare.

## Example Workflow

```bash
# 1. Import existing records
./terraform/scripts/import_cloudflare_dns.sh

# 2. Update terraform.tfvars
echo 'create_dns_records = false' >> terraform/environments/staging/terraform.tfvars

# 3. Plan and apply
cd terraform/environments/staging
terraform plan
terraform apply
```

## Module Variables

- `create_dns_records` (bool): Whether to create DNS records. Set to `false` for imported records.
- `create_page_rules` (bool): Whether to create page rules. Set to `false` for imported rules.
- `root_domain` (string): Your root domain (e.g., "vedavivi.app")
- `environment` (string): Environment name (staging, production, etc.)
- `cloudflare_zone_id` (string): Cloudflare Zone ID

## Next Steps

After successfully importing or creating DNS records and page rules:

1. Verify the records work by visiting your domains
2. Test page rule functionality (caching, redirects, etc.)
3. Set up monitoring for DNS resolution and CDN performance
4. Configure additional cache rules and security settings
5. Test your application deployment pipeline
