// Cloudflare Worker for SPA routing
// This worker serves index.html for all routes that don't match static assets
// Replaces any existing worker functionality

addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

async function handleRequest(request) {
  const url = new URL(request.url)
  const pathname = url.pathname

  // Root path - redirect to index.html (preserve existing behavior)
  if (pathname === '/') {
    return Response.redirect(new URL('/index.html', url.origin).toString(), 302)
  }

  // If the request is for a static asset (has file extension), let it pass through
  if (pathname.includes('.') || pathname.startsWith('/assets/')) {
    return fetch(request)
  }

  // For SPA routes (/pa/*), serve index.html
  if (pathname.startsWith('/pa/')) {
    const indexUrl = new URL('/index.html', url.origin)
    const indexRequest = new Request(indexUrl.toString(), {
      method: 'GET',
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
      }
    })

    const response = await fetch(indexRequest)

    // Return the index.html content with 200 status for SPA routing
    return new Response(response.body, {
      status: 200,
      statusText: 'OK',
      headers: {
        'Content-Type': 'text/html',
        'Cache-Control': 'public, max-age=300'
      }
    })
  }

  // For all other routes, let them pass through normally
  return fetch(request)
}
