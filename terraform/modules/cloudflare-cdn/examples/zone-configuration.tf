# Example: Cloudflare Zone Configuration
# This example shows how to configure Cloudflare zones similar to your example

# Variables for the examples
variable "cloudflare_account_id" {
  description = "The Cloudflare account ID"
  type        = string
}

variable "storage_origin_url" {
  description = "The URL of the storage service to use as origin for static assets"
  type        = string
}

variable "backend_origin_url" {
  description = "The origin URL for backend API services"
  type        = string
}

variable "websocket_origin_url" {
  description = "The origin URL for WebSocket connections"
  type        = string
}

# Example 1: Basic zone configuration (similar to your example)
module "cloudflare_cdn_basic" {
  source = "../"

  # Required variables
  root_domain           = "mydomain.com"
  environment           = "production"
  cloudflare_account_id = "your-account-id"
  storage_origin_url    = "https://your-storage-bucket-url.com"
  backend_origin_url    = "" # Set to your backend URL (e.g., "https://api.example.com") or leave empty
  websocket_origin_url  = "" # Set to your WebSocket URL (e.g., "https://ws.example.com") or leave empty

  # Zone configuration (similar to your example)
  zone_config = {
    jump_start = false  # Similar to your example
    paused     = true   # Similar to your example
    type       = "full" # Similar to your example
  }

  # Plan configuration
  cloudflare_plan = "free" # Similar to your example
}

# Example 2: Production zone configuration
module "cloudflare_cdn_production" {
  source = "../"

  # Required variables
  root_domain           = "vedavivi.app"
  environment           = "production"
  cloudflare_account_id = var.cloudflare_account_id
  storage_origin_url    = var.storage_origin_url
  backend_origin_url    = var.backend_origin_url
  websocket_origin_url  = var.websocket_origin_url

  # Production zone configuration
  zone_config = {
    jump_start = true   # Auto-import existing DNS records
    paused     = false  # Zone is active
    type       = "full" # Full zone management
  }

  # Production plan
  cloudflare_plan = "pro"

  # Production cache configuration
  cache_config = {
    # Static assets - long cache for production
    static_assets_edge_ttl    = ******** # 1 year
    static_assets_browser_ttl = ******** # 1 year

    # HTML files - moderate cache for production
    html_edge_ttl    = 3600 # 1 hour
    html_browser_ttl = 3600 # 1 hour

    # API caching - enabled for production
    api_cache_enabled = true
    api_edge_ttl      = 300 # 5 minutes
    api_browser_ttl   = 0   # No browser cache

    # Global settings
    development_mode = false
    cache_level      = "aggressive"
  }
}

# Example 3: Staging zone configuration
module "cloudflare_cdn_staging" {
  source = "../"

  # Required variables
  root_domain           = "vedavivi.app"
  environment           = "staging"
  cloudflare_account_id = var.cloudflare_account_id
  storage_origin_url    = var.storage_origin_url
  backend_origin_url    = var.backend_origin_url
  websocket_origin_url  = var.websocket_origin_url

  # Staging zone configuration
  zone_config = {
    jump_start = false  # Don't auto-import for staging
    paused     = false  # Zone is active
    type       = "full" # Full zone management
  }

  # Free plan for staging
  cloudflare_plan = "free"

  # Staging cache configuration
  cache_config = {
    # Static assets - shorter cache for staging
    static_assets_edge_ttl    = 3600 # 1 hour
    static_assets_browser_ttl = 3600 # 1 hour

    # HTML files - short cache for staging
    html_edge_ttl    = 300 # 5 minutes
    html_browser_ttl = 300 # 5 minutes

    # API caching - disabled for staging
    api_cache_enabled = false

    # Global settings
    development_mode = false
    cache_level      = "basic"
  }
}

# Outputs to show zone information
output "basic_zone_info" {
  description = "Basic zone configuration information"
  value = {
    zone_id      = module.cloudflare_cdn_basic.zone_id
    zone_config  = module.cloudflare_cdn_basic.zone_config
    name_servers = module.cloudflare_cdn_basic.name_servers
  }
}

output "production_zone_info" {
  description = "Production zone configuration information"
  value = {
    zone_id      = module.cloudflare_cdn_production.zone_id
    zone_config  = module.cloudflare_cdn_production.zone_config
    name_servers = module.cloudflare_cdn_production.name_servers
  }
}

output "staging_zone_info" {
  description = "Staging zone configuration information"
  value = {
    zone_id      = module.cloudflare_cdn_staging.zone_id
    zone_config  = module.cloudflare_cdn_staging.zone_config
    name_servers = module.cloudflare_cdn_staging.name_servers
  }
}
