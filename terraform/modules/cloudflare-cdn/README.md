## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.0.0 |
| <a name="requirement_cloudflare"></a> [cloudflare](#requirement\_cloudflare) | ~> 4.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_cloudflare"></a> [cloudflare](#provider\_cloudflare) | 4.52.0 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [cloudflare_page_rule.api_bypass](https://registry.terraform.io/providers/cloudflare/cloudflare/latest/docs/resources/page_rule) | resource |
| [cloudflare_page_rule.api_cache](https://registry.terraform.io/providers/cloudflare/cloudflare/latest/docs/resources/page_rule) | resource |
| [cloudflare_page_rule.graphql_bypass](https://registry.terraform.io/providers/cloudflare/cloudflare/latest/docs/resources/page_rule) | resource |
| [cloudflare_page_rule.graphql_cache](https://registry.terraform.io/providers/cloudflare/cloudflare/latest/docs/resources/page_rule) | resource |
| [cloudflare_page_rule.html_cache](https://registry.terraform.io/providers/cloudflare/cloudflare/latest/docs/resources/page_rule) | resource |
| [cloudflare_page_rule.static_assets](https://registry.terraform.io/providers/cloudflare/cloudflare/latest/docs/resources/page_rule) | resource |
| [cloudflare_record.api](https://registry.terraform.io/providers/cloudflare/cloudflare/latest/docs/resources/record) | resource |
| [cloudflare_record.web](https://registry.terraform.io/providers/cloudflare/cloudflare/latest/docs/resources/record) | resource |
| [cloudflare_record.websocket](https://registry.terraform.io/providers/cloudflare/cloudflare/latest/docs/resources/record) | resource |
| [cloudflare_spectrum_application.websockets](https://registry.terraform.io/providers/cloudflare/cloudflare/latest/docs/resources/spectrum_application) | resource |
| [cloudflare_zone.main](https://registry.terraform.io/providers/cloudflare/cloudflare/latest/docs/resources/zone) | resource |
| [cloudflare_zone_settings_override.caching](https://registry.terraform.io/providers/cloudflare/cloudflare/latest/docs/resources/zone_settings_override) | resource |
| [cloudflare_zone_settings_override.security](https://registry.terraform.io/providers/cloudflare/cloudflare/latest/docs/resources/zone_settings_override) | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_backend_origin_url"></a> [backend\_origin\_url](#input\_backend\_origin\_url) | The origin URL for backend API services (e.g., Cloud Run instance) | `string` | n/a | yes |
| <a name="input_cache_config"></a> [cache\_config](#input\_cache\_config) | CDN cache configuration settings | <pre>object({<br/>    # Frontend static assets caching<br/>    static_assets_edge_ttl    = optional(number, 31536000) # 1 year default<br/>    static_assets_browser_ttl = optional(number, 31536000) # 1 year default<br/><br/>    # HTML files caching<br/>    html_edge_ttl    = optional(number, 300) # 5 minutes default<br/>    html_browser_ttl = optional(number, 300) # 5 minutes default<br/><br/>    # API caching configuration<br/>    api_cache_enabled    = optional(bool, false)            # Disabled by default<br/>    api_edge_ttl         = optional(number, 60)             # 1 minute default when enabled<br/>    api_browser_ttl      = optional(number, 0)              # No browser cache for APIs<br/>    api_cache_by_device  = optional(bool, false)            # Cache by device type<br/>    api_cache_key_fields = optional(list(string), ["host"]) # Cache key fields<br/><br/>    # GraphQL specific caching<br/>    graphql_cache_enabled   = optional(bool, false) # Disabled by default<br/>    graphql_edge_ttl        = optional(number, 30)  # 30 seconds default when enabled<br/>    graphql_browser_ttl     = optional(number, 0)   # No browser cache for GraphQL<br/>    graphql_cache_by_device = optional(bool, false) # Cache by device type<br/><br/>    # Global cache settings<br/>    development_mode  = optional(bool, false)          # Bypass cache in development<br/>    cache_level       = optional(string, "aggressive") # Cache level: basic, simplified, aggressive<br/>    browser_cache_ttl = optional(number, 14400)        # Default browser cache (4 hours)<br/>  })</pre> | `{}` | no |
| <a name="input_cloudflare_account_id"></a> [cloudflare\_account\_id](#input\_cloudflare\_account\_id) | The Cloudflare account ID | `string` | n/a | yes |
| <a name="input_cloudflare_plan"></a> [cloudflare\_plan](#input\_cloudflare\_plan) | The Cloudflare plan to use | `string` | `"free"` | no |
| <a name="input_enable_websockets"></a> [enable\_websockets](#input\_enable\_websockets) | Whether to enable WebSocket support | `bool` | `true` | no |
| <a name="input_environment"></a> [environment](#input\_environment) | The environment (production, staging, development) | `string` | n/a | yes |
| <a name="input_gcs_bucket_url"></a> [gcs\_bucket\_url](#input\_gcs\_bucket\_url) | The URL of the Google Cloud Storage bucket to use as origin | `string` | n/a | yes |
| <a name="input_root_domain"></a> [root\_domain](#input\_root\_domain) | The root domain for all services (e.g., vedavivi.app) | `string` | n/a | yes |
| <a name="input_websocket_origin_url"></a> [websocket\_origin\_url](#input\_websocket\_origin\_url) | The origin URL for WebSocket connections (e.g., Cloud Run instance) | `string` | n/a | yes |
| <a name="input_zone_config"></a> [zone\_config](#input\_zone\_config) | Cloudflare zone configuration settings | <pre>object({<br/>    jump_start = optional(bool, false)    # Whether to automatically attempt to fetch existing DNS records<br/>    paused     = optional(bool, false)    # Whether the zone is paused (traffic bypasses Cloudflare)<br/>    type       = optional(string, "full") # Zone type: full, partial, secondary<br/>  })</pre> | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_api_domain"></a> [api\_domain](#output\_api\_domain) | The API domain name |
| <a name="output_api_url"></a> [api\_url](#output\_api\_url) | The API URL |
| <a name="output_cache_config"></a> [cache\_config](#output\_cache\_config) | Complete applied cache configuration for observability and debugging |
| <a name="output_cache_performance_info"></a> [cache\_performance\_info](#output\_cache\_performance\_info) | Cache configuration performance information |
| <a name="output_cache_rules_summary"></a> [cache\_rules\_summary](#output\_cache\_rules\_summary) | Summary of active cache rules for debugging |
| <a name="output_name_servers"></a> [name\_servers](#output\_name\_servers) | The Cloudflare name servers for the domain |
| <a name="output_web_domain"></a> [web\_domain](#output\_web\_domain) | The web domain name |
| <a name="output_web_url"></a> [web\_url](#output\_web\_url) | The web application URL |
| <a name="output_websocket_url"></a> [websocket\_url](#output\_websocket\_url) | The WebSocket URL |
| <a name="output_ws_domain"></a> [ws\_domain](#output\_ws\_domain) | The WebSocket domain name |
| <a name="output_zone_config"></a> [zone\_config](#output\_zone\_config) | Complete zone configuration for observability and debugging |
| <a name="output_zone_id"></a> [zone\_id](#output\_zone\_id) | The Cloudflare Zone ID |
