# Cloudflare DNS Records Error Handling Guide

## Problem: "expected DNS record to not already be present but already exists"

This error occurs when Terraform tries to create DNS records that already exist in Cloudflare. This is common when:

1. Records were created manually in the Cloudflare dashboard
2. Records were created by a previous Terraform run that wasn't properly imported
3. Multiple Terraform configurations are managing the same zone

## Solution Options

### Option 1: Import Existing Records (Recommended)

Use the provided import script to bring existing DNS records under Terraform management:

```bash
# Run the automated import script for staging
./scripts/import-cloudflare-dns.sh staging

# Or for production
./scripts/import-cloudflare-dns.sh production

# Verify the import
cd terraform/environments/staging  # or production
terraform plan
```

### Option 2: Use Existing Records Pattern

Set `TF_VAR_create_dns_records=false` in your environment to reference existing records without creating new ones:

```bash
# In environment variables or .env file
export TF_VAR_create_dns_records=false
```

This approach uses data sources to reference existing records instead of creating new ones.

### Option 3: Manual Import (Advanced)

If the automated script doesn't work, manually import records:

```bash
cd terraform/environments/staging  # or production

# Get the record IDs from Cloudflare API or dashboard
# Import format: terraform import resource_name zone_id/record_id

# Example for staging environment:
terraform import module.cloudflare_cdn.cloudflare_record.web[0] f59ced378f5a75475654ad18ece8cffc/RECORD_ID_HERE
terraform import module.cloudflare_cdn.cloudflare_record.api[0] f59ced378f5a75475654ad18ece8cffc/RECORD_ID_HERE
terraform import module.cloudflare_cdn.cloudflare_record.websocket[0] f59ced378f5a75475654ad18ece8cffc/RECORD_ID_HERE
```

## Verification

After implementing any solution, verify with:

```bash
cd terraform/environments/staging  # or production
terraform plan

# Should show no changes or only expected changes
# No "will be created" for DNS records that already exist
```

## Record Naming Convention

The module uses this naming pattern:

- **Production**:
  - Web: `vedavivi.app`
  - API: `api.vedavivi.app`
  - WebSocket: `ws.vedavivi.app`

- **Staging/Dev**:
  - Web: `{environment}.vedavivi.app` (e.g., `staging.vedavivi.app`)
  - API: `api-{environment}.vedavivi.app` (e.g., `api-staging.vedavivi.app`)
  - WebSocket: `ws-{environment}.vedavivi.app` (e.g., `ws-staging.vedavivi.app`)

## Environment Variables

Key variables that control DNS record management:

```bash
# Control whether to create new records
export TF_VAR_create_dns_records=false  # Set to false to use existing records

# Cloudflare credentials
export CLOUDFLARE_API_TOKEN="your_token_here"
export CLOUDFLARE_ZONE_ID="your_zone_id_here"
export CLOUDFLARE_ACCOUNT_ID="your_account_id_here"

# Domain configuration
export TF_VAR_root_domain="vedavivi.app"  # Root domain
```

## Troubleshooting

### Error: "Record not found" during import
- Verify the record exists in Cloudflare dashboard
- Check the record name matches the expected pattern
- Ensure API token has correct permissions

### Error: "already exists" even with TF_VAR_create_dns_records=false
- Check that data sources are using correct record names
- Verify the existing records match expected naming convention
- Run the import script to properly manage existing records

### Error: Permission denied
- Ensure Cloudflare API token has Zone:Read and Zone:Edit permissions
- Verify the token is correctly set in environment variables
