/**
 * # Network Module Outputs
 */

output "network_id" {
  description = "The ID of the VPC network"
  value = length(google_compute_network.network) > 0 ? google_compute_network.network[0].id : (
    length(data.google_compute_network.existing) > 0 ? data.google_compute_network.existing[0].id : null
  )
}

output "network_name" {
  description = "The name of the VPC network"
  value = length(google_compute_network.network) > 0 ? google_compute_network.network[0].name : (
    length(data.google_compute_network.existing) > 0 ? data.google_compute_network.existing[0].name : var.network_name
  )
}

output "network_self_link" {
  description = "The URI of the VPC network"
  value = length(google_compute_network.network) > 0 ? google_compute_network.network[0].self_link : (
    length(data.google_compute_network.existing) > 0 ? data.google_compute_network.existing[0].self_link : null
  )
}

output "subnet_id" {
  description = "The ID of the default subnet"
  value = length(google_compute_subnetwork.default_subnet) > 0 ? google_compute_subnetwork.default_subnet[0].id : (
    length(data.google_compute_subnetwork.existing) > 0 ? data.google_compute_subnetwork.existing[0].id : null
  )
}

output "subnet_self_link" {
  description = "The URI of the default subnet"
  value = length(google_compute_subnetwork.default_subnet) > 0 ? google_compute_subnetwork.default_subnet[0].self_link : (
    length(data.google_compute_subnetwork.existing) > 0 ? data.google_compute_subnetwork.existing[0].self_link : null
  )
}

output "subnet_name" {
  description = "The name of the default subnet"
  value = length(google_compute_subnetwork.default_subnet) > 0 ? google_compute_subnetwork.default_subnet[0].name : (
    length(data.google_compute_subnetwork.existing) > 0 ? data.google_compute_subnetwork.existing[0].name : var.subnet_name
  )
}

output "subnet_region" {
  description = "The region of the default subnet"
  value = length(google_compute_subnetwork.default_subnet) > 0 ? google_compute_subnetwork.default_subnet[0].region : (
    length(data.google_compute_subnetwork.existing) > 0 ? data.google_compute_subnetwork.existing[0].region : var.region
  )
}

output "subnet_cidr" {
  description = "The IP CIDR range of the default subnet"
  value = length(google_compute_subnetwork.default_subnet) > 0 ? google_compute_subnetwork.default_subnet[0].ip_cidr_range : (
    length(data.google_compute_subnetwork.existing) > 0 ? data.google_compute_subnetwork.existing[0].ip_cidr_range : var.subnet_cidr
  )
}

output "subnet_ids" {
  description = "Map of subnet names to their IDs"
  value       = { for name, subnet in google_compute_subnetwork.subnets : name => subnet.id }
}

output "subnet_self_links" {
  description = "Map of subnet names to their self_links"
  value       = { for name, subnet in google_compute_subnetwork.subnets : name => subnet.self_link }
}

output "subnet_ip_cidr_ranges" {
  description = "Map of subnet names to their primary IP CIDR ranges"
  value       = { for name, subnet in google_compute_subnetwork.subnets : name => subnet.ip_cidr_range }
}

output "subnet_secondary_ip_ranges" {
  description = "Map of subnet names to their secondary IP ranges"
  value = {
    for name, subnet in google_compute_subnetwork.subnets :
    name => [
      for range in subnet.secondary_ip_range : {
        range_name    = range.range_name
        ip_cidr_range = range.ip_cidr_range
      }
    ]
  }
}

output "firewall_rule_ids" {
  description = "Map of firewall rule names to their IDs"
  value       = { for name, rule in google_compute_firewall.rules : name => rule.id }
}

output "firewall_rule_self_links" {
  description = "Map of firewall rule names to their self_links"
  value       = { for name, rule in google_compute_firewall.rules : name => rule.self_link }
}

output "nat_router_name" {
  description = "The name of the Cloud Router for NAT"
  value       = length(google_compute_router.nat_router) > 0 ? google_compute_router.nat_router[0].name : null
}

output "nat_gateway_name" {
  description = "The name of the Cloud NAT gateway"
  value       = length(google_compute_router_nat.nat_gateway) > 0 ? google_compute_router_nat.nat_gateway[0].name : null
}