/**
 * # Network Module Variables
 */

variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "network_name" {
  description = "The name of the network"
  type        = string
}

variable "auto_create_subnetworks" {
  description = "Whether to create subnetworks automatically"
  type        = bool
  default     = false
}

variable "delete_default_routes_on_create" {
  description = "Whether to delete the default routes when the network is created"
  type        = bool
  default     = false
}

variable "description" {
  description = "Description of the network"
  type        = string
  default     = "Managed by Terraform"
}

variable "routing_mode" {
  description = "The network-wide routing mode to use. Possible values are 'GLOBAL' or 'REGIONAL'"
  type        = string
  default     = "REGIONAL"
}

variable "region" {
  description = "The region for the subnet"
  type        = string
  default     = "us-central1"
}

variable "subnet_cidr" {
  description = "The CIDR range for the subnet"
  type        = string
  default     = "10.0.0.0/24"
}

variable "subnet_name" {
  description = "Name for the subnet"
  type        = string
  default     = null
}

variable "subnets" {
  description = "List of additional subnetworks to create"
  type        = any
  default     = []
}

variable "firewall_rules" {
  description = "List of firewall rules to create"
  type = list(object({
    name        = string
    description = optional(string)
    direction   = optional(string)
    priority    = optional(number)
    allow = optional(list(object({
      protocol = string
      ports    = optional(list(string))
    })))
    deny = optional(list(object({
      protocol = string
      ports    = optional(list(string))
    })))
    source_ranges           = optional(list(string))
    source_tags             = optional(list(string))
    source_service_accounts = optional(list(string))
    target_tags             = optional(list(string))
    target_service_accounts = optional(list(string))
    disabled                = optional(bool)
  }))
  default = []
}

variable "create_resources" {
  description = "Whether to create resources that may already exist (like networks and subnets)"
  type        = bool
  default     = true
}

variable "existing_resources_exist" {
  description = "Whether existing resources (which would be referenced via data sources when create_resources=false) actually exist"
  type        = bool
  default     = false
}

variable "enable_cloud_nat" {
  description = "Whether to create Cloud NAT for outbound internet access from private subnets"
  type        = bool
  default     = true
}