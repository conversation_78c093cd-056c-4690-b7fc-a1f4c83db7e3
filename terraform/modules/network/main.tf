/**
 * # Network Module
 *
 * This module creates a Google Cloud VPC network with subnets and firewall rules.
 */

locals {
  # Set default values for optional fields
  default_subnets = [for subnet in var.subnets : {
    name                     = subnet.name
    ip_cidr_range            = subnet.ip_cidr_range
    region                   = subnet.region
    description              = lookup(subnet, "description", "Subnet ${subnet.name}")
    secondary_ip_ranges      = lookup(subnet, "secondary_ip_ranges", [])
    private_ip_google_access = lookup(subnet, "private_ip_google_access", true)
    log_config               = lookup(subnet, "log_config", null)
  }]

  default_firewall_rules = [for rule in var.firewall_rules : {
    name                    = rule.name
    description             = lookup(rule, "description", "Firewall rule ${rule.name}")
    direction               = lookup(rule, "direction", "INGRESS")
    priority                = lookup(rule, "priority", 1000)
    allow                   = lookup(rule, "allow", [])
    deny                    = lookup(rule, "deny", [])
    source_ranges           = lookup(rule, "source_ranges", [])
    source_tags             = lookup(rule, "source_tags", [])
    source_service_accounts = lookup(rule, "source_service_accounts", [])
    target_tags             = lookup(rule, "target_tags", [])
    target_service_accounts = lookup(rule, "target_service_accounts", [])
    disabled                = lookup(rule, "disabled", false)
  }]
}

# Look up existing network if not creating a new one
data "google_compute_network" "existing" {
  count   = var.existing_resources_exist ? 1 : 0
  name    = var.network_name
  project = var.project_id
}

# Look up existing subnet if not creating a new one
data "google_compute_subnetwork" "existing" {
  count   = var.existing_resources_exist && var.subnet_name != null ? 1 : 0
  name    = var.subnet_name
  region  = var.region
  project = var.project_id
}

# Create the VPC network
resource "google_compute_network" "network" {
  name                            = var.network_name
  project                         = var.project_id
  auto_create_subnetworks         = var.auto_create_subnetworks
  delete_default_routes_on_create = var.delete_default_routes_on_create
  description                     = var.description
  routing_mode                    = var.routing_mode

  # Prevent resource conflicts with pre-existing networks
  lifecycle {
    # Make resource resilient to recreation attempts
    create_before_destroy = true
    # Prevent terraform from destroying the resource
    prevent_destroy = true
    # Ignore all changes to prevent recreation of the resource
    ignore_changes = all
  }

  # Only create if create_resources is true
  count = (var.create_resources && !var.existing_resources_exist) ? 1 : 0
}

# Create the default subnet if subnet_cidr is specified
resource "google_compute_subnetwork" "default_subnet" {
  count = var.subnet_cidr != null && (var.create_resources && !var.existing_resources_exist) ? 1 : 0

  name                     = var.subnet_name != null ? var.subnet_name : "${var.network_name}-subnet"
  project                  = var.project_id
  network                  = length(google_compute_network.network) > 0 ? google_compute_network.network[0].self_link : (length(data.google_compute_network.existing) > 0 ? data.google_compute_network.existing[0].self_link : null)
  ip_cidr_range            = var.subnet_cidr
  region                   = var.region
  description              = "Default subnet for ${var.network_name}"
  private_ip_google_access = true

  # Enable VPC flow logs for better security and monitoring
  log_config {
    aggregation_interval = "INTERVAL_5_SEC"
    flow_sampling        = 0.5
    metadata             = "INCLUDE_ALL_METADATA"
  }

  # Prevent resource conflicts with pre-existing subnets
  lifecycle {
    # Make resource resilient to recreation attempts
    create_before_destroy = true

    # Ignore changes to non-critical fields
    ignore_changes = [
      description,
      log_config
    ]
  }
}

# Create additional subnets
resource "google_compute_subnetwork" "subnets" {
  for_each = (var.create_resources && !var.existing_resources_exist) ? { for subnet in local.default_subnets : subnet.name => subnet } : {}

  name                     = each.value.name
  project                  = var.project_id
  network                  = length(google_compute_network.network) > 0 ? google_compute_network.network[0].self_link : (length(data.google_compute_network.existing) > 0 ? data.google_compute_network.existing[0].self_link : null)
  ip_cidr_range            = each.value.ip_cidr_range
  region                   = each.value.region
  description              = each.value.description
  private_ip_google_access = each.value.private_ip_google_access

  dynamic "secondary_ip_range" {
    for_each = each.value.secondary_ip_ranges
    content {
      range_name    = secondary_ip_range.value.range_name
      ip_cidr_range = secondary_ip_range.value.ip_cidr_range
    }
  }

  dynamic "log_config" {
    for_each = each.value.log_config != null ? [each.value.log_config] : []
    content {
      aggregation_interval = log_config.value.aggregation_interval
      flow_sampling        = log_config.value.flow_sampling
      metadata             = log_config.value.metadata
    }
  }
}

# Create firewall rules
resource "google_compute_firewall" "rules" {
  for_each = (var.create_resources && !var.existing_resources_exist) ? { for rule in local.default_firewall_rules : rule.name => rule } : {}

  name        = each.value.name
  project     = var.project_id
  network     = length(google_compute_network.network) > 0 ? google_compute_network.network[0].self_link : (length(data.google_compute_network.existing) > 0 ? data.google_compute_network.existing[0].self_link : null)
  description = each.value.description
  direction   = each.value.direction
  priority    = each.value.priority
  disabled    = each.value.disabled

  dynamic "allow" {
    for_each = each.value.allow != null ? each.value.allow : []
    content {
      protocol = allow.value.protocol
      ports    = lookup(allow.value, "ports", null)
    }
  }

  dynamic "deny" {
    for_each = each.value.deny != null ? each.value.deny : []
    content {
      protocol = deny.value.protocol
      ports    = lookup(deny.value, "ports", null)
    }
  }

  source_ranges           = each.value.source_ranges != null && length(each.value.source_ranges) > 0 ? each.value.source_ranges : null
  source_tags             = each.value.source_tags != null && length(each.value.source_tags) > 0 ? each.value.source_tags : null
  source_service_accounts = each.value.source_service_accounts != null && length(each.value.source_service_accounts) > 0 ? each.value.source_service_accounts : null
  target_tags             = each.value.target_tags != null && length(each.value.target_tags) > 0 ? each.value.target_tags : null
  target_service_accounts = each.value.target_service_accounts != null && length(each.value.target_service_accounts) > 0 ? each.value.target_service_accounts : null
}

# Create Cloud Router for Cloud NAT
resource "google_compute_router" "nat_router" {
  count   = var.enable_cloud_nat && (var.create_resources && !var.existing_resources_exist) ? 1 : 0
  name    = "${var.network_name}-nat-router"
  project = var.project_id
  region  = var.region
  network = length(google_compute_network.network) > 0 ? google_compute_network.network[0].self_link : (length(data.google_compute_network.existing) > 0 ? data.google_compute_network.existing[0].self_link : null)

  lifecycle {
    create_before_destroy = true
  }
}

# Create Cloud NAT for outbound internet access
resource "google_compute_router_nat" "nat_gateway" {
  count  = var.enable_cloud_nat && (var.create_resources && !var.existing_resources_exist) ? 1 : 0
  name   = "${var.network_name}-nat-gateway"
  router = google_compute_router.nat_router[0].name
  region = var.region

  nat_ip_allocate_option             = "AUTO_ONLY"
  source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"

  log_config {
    enable = true
    filter = "ERRORS_ONLY"
  }

  lifecycle {
    create_before_destroy = true
  }
}