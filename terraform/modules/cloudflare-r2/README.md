# Cloudflare R2 Storage Module

This Terraform module creates and manages Cloudflare R2 storage buckets for static website hosting with CDN integration.

## 🚀 Features

- **R2 Bucket Creation**: Creates R2 bucket with configurable location
- **Custom Domain Support**: Optional custom domain configuration with DNS records
- **CORS Configuration**: Configurable CORS rules for web applications
- **Lifecycle Management**: Optional lifecycle rules for object expiration
- **API Token Management**: Optional deployment token creation with IP restrictions
- **CDN Integration**: Native Cloudflare CDN integration

## 🔒 Security Features

- **Private by Default**: R2 buckets are private by default
- **Custom Domain Access**: Access through custom domains with Cloudflare proxy
- **IP-Restricted Tokens**: Deployment tokens with IP allowlists
- **CORS Controls**: Configurable CORS policies

## 📋 Usage

### Basic Usage

```hcl
module "r2_storage" {
  source = "../../modules/cloudflare-r2"
  
  cloudflare_account_id = "your-account-id"
  bucket_name          = "my-static-website"
  environment          = "production"
  
  cors_origins = [
    "https://example.com",
    "https://*.example.com"
  ]
}
```

### With Custom Domain

```hcl
module "r2_storage" {
  source = "../../modules/cloudflare-r2"
  
  cloudflare_account_id = "your-account-id"
  cloudflare_zone_id   = "your-zone-id"
  bucket_name          = "my-static-website"
  environment          = "production"
  custom_domain        = "assets"  # Creates assets.example.com
  
  cors_origins = [
    "https://example.com",
    "https://*.example.com"
  ]
}
```

### With Deployment Token

```hcl
module "r2_storage" {
  source = "../../modules/cloudflare-r2"
  
  cloudflare_account_id = "your-account-id"
  bucket_name          = "my-static-website"
  environment          = "production"
  
  create_deploy_token = true
  allowed_ips = [
    "***********/24",  # GitHub Actions IP range
    "************"     # Your CI/CD server
  ]
}
```

### With Lifecycle Rules

```hcl
module "r2_storage" {
  source = "../../modules/cloudflare-r2"
  
  cloudflare_account_id = "your-account-id"
  bucket_name          = "my-static-website"
  environment          = "production"
  
  lifecycle_rules = [
    {
      id              = "delete-old-builds"
      enabled         = true
      prefix          = "builds/"
      expiration_days = 30
    },
    {
      id              = "delete-temp-files"
      enabled         = true
      prefix          = "temp/"
      expiration_days = 7
    }
  ]
}
```

## 🔧 Migration from GCS

To migrate from Google Cloud Storage to Cloudflare R2:

1. **Deploy R2 bucket** alongside existing GCS setup
2. **Sync data** from GCS to R2 using tools like `rclone`
3. **Update CDN configuration** to point to R2 bucket
4. **Test thoroughly** before removing GCS resources

### Migration Script Example

```bash
# Install rclone
curl https://rclone.org/install.sh | sudo bash

# Configure rclone for GCS and R2
rclone config

# Sync data from GCS to R2
rclone sync gcs:your-gcs-bucket r2:your-r2-bucket --progress
```

## 📊 Outputs

| Output | Description |
|--------|-------------|
| `bucket_name` | Name of the R2 bucket |
| `bucket_url` | Direct R2 bucket URL |
| `public_url` | Public URL for accessing assets |
| `custom_domain_url` | Custom domain URL (if configured) |
| `deploy_token` | API token for deployment (sensitive) |

## 🌍 Supported Locations

- `WNAM` - Western North America (default)
- `ENAM` - Eastern North America
- `WEUR` - Western Europe
- `EEUR` - Eastern Europe
- `APAC` - Asia-Pacific
- `OC` - Oceania

## 💰 Cost Benefits vs GCS

- **No Egress Fees**: R2 doesn't charge for egress to Cloudflare CDN
- **Competitive Pricing**: Lower storage costs compared to GCS
- **Simplified Architecture**: No need for proxy services
- **Reduced Complexity**: Fewer moving parts to maintain

## 🔄 Integration with Cloudflare CDN

The R2 bucket integrates natively with Cloudflare CDN:

```hcl
# In your CDN configuration
resource "cloudflare_record" "web" {
  zone_id = var.cloudflare_zone_id
  name    = var.environment == "production" ? "@" : var.environment
  content = module.r2_storage.public_url
  type    = "CNAME"
  proxied = true
}
```

## 📚 Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.12.0 |
| cloudflare | ~> 4.0 |

## 🤝 Contributing

When contributing to this module:

1. Follow Terraform best practices
2. Update documentation for any new variables
3. Test with multiple environments
4. Ensure backward compatibility
