# Cloudflare R2 Storage Module Variables

variable "cloudflare_account_id" {
  description = "The Cloudflare account ID"
  type        = string
}

variable "cloudflare_zone_id" {
  description = "The Cloudflare zone ID for custom domain configuration"
  type        = string
  default     = ""
}

variable "bucket_name" {
  description = "Name of the R2 bucket"
  type        = string

  validation {
    condition     = can(regex("^[a-z0-9][a-z0-9-]*[a-z0-9]$", var.bucket_name)) && length(var.bucket_name) >= 3 && length(var.bucket_name) <= 63
    error_message = "Bucket name must be 3-63 characters, start and end with alphanumeric characters, and contain only lowercase letters, numbers, and hyphens."
  }
}

variable "environment" {
  description = "Environment name (dev, staging, production)"
  type        = string

  validation {
    condition     = contains(["dev", "staging", "production"], var.environment)
    error_message = "Environment must be one of: dev, staging, production."
  }
}

variable "location" {
  description = "R2 bucket location"
  type        = string
  default     = "WNAM"

  validation {
    condition = contains([
      "WNAM", "ENAM", "WEUR", "EEUR", "APAC", "OC"
    ], var.location)
    error_message = "Location must be one of: WNAM (Western North America), ENAM (Eastern North America), WEUR (Western Europe), EEUR (Eastern Europe), APAC (Asia-Pacific), OC (Oceania)."
  }
}

variable "custom_domain" {
  description = "Custom domain for R2 bucket access (e.g., 'assets' for assets.example.com)"
  type        = string
  default     = ""
}

variable "cors_origins" {
  description = "List of allowed CORS origins"
  type        = list(string)
  default     = ["*"]
}

variable "create_deploy_token" {
  description = "Whether to create an API token for deployment access"
  type        = bool
  default     = false
}

variable "allowed_ips" {
  description = "List of IP addresses allowed to use the deployment token"
  type        = list(string)
  default     = []
}

variable "token_not_before" {
  description = "Token validity start time (RFC3339 format)"
  type        = string
  default     = null
}

variable "token_expires_on" {
  description = "Token expiration time (RFC3339 format)"
  type        = string
  default     = null
}

variable "lifecycle_rules" {
  description = "List of lifecycle rules for the R2 bucket"
  type = list(object({
    id              = string
    enabled         = bool
    prefix          = optional(string)
    expiration_days = optional(number)
  }))
  default = []
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}
