# Cloudflare R2 Storage Module for A2A Platform

terraform {
  required_providers {
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 4.0"
    }
  }
  required_version = ">= 1.12.0"
}

# Use existing R2 bucket (don't create new one)
# The bucket already exists and is managed outside Terraform
locals {
  bucket_name = var.bucket_name
}

# Custom domain for R2 bucket (already exists, don't create)
# The DNS record already exists and is managed outside Terraform
locals {
  custom_domain_exists = var.custom_domain != ""
}

# R2 API Token for deployment access
resource "cloudflare_api_token" "r2_deploy" {
  count = var.create_deploy_token ? 1 : 0
  name  = "r2-deploy-${var.environment}"

  policy {
    permission_groups = [
      data.cloudflare_api_token_permission_groups.all[0].r2["com.cloudflare.api.account.zone.r2.bucket"]
    ]
    resources = {
      "com.cloudflare.api.account.*" = "*"
    }
  }

  condition {
    request_ip {
      in     = var.allowed_ips
      not_in = []
    }
  }

  not_before = var.token_not_before
  expires_on = var.token_expires_on
}

# Data source for API token permission groups (only when creating deploy token)
data "cloudflare_api_token_permission_groups" "all" {
  count = var.create_deploy_token ? 1 : 0
}

# Note: CORS and lifecycle configurations for R2 buckets are not yet supported
# by the Cloudflare Terraform provider. These will need to be configured
# manually through the Cloudflare dashboard or API until provider support is added.
#
# CORS configuration should allow:
# - Origins: var.cors_origins
# - Methods: GET, HEAD, OPTIONS
# - Headers: *, ETag, Content-Length, Content-Type
# - Max Age: 3600 seconds
#
# Lifecycle rules should be configured based on var.lifecycle_rules

# Local values for computed attributes
locals {
  # R2 bucket URL for CDN origin (correct format for S3 API)
  bucket_url = "https://${var.cloudflare_account_id}.r2.cloudflarestorage.com/${local.bucket_name}"

  # Custom domain URL if configured
  custom_domain_url = var.custom_domain != "" ? "https://${var.custom_domain}" : ""

  # Public URL for accessing assets
  public_url = var.custom_domain != "" ? local.custom_domain_url : local.bucket_url
}
