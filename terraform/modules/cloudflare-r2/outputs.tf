# Cloudflare R2 Storage Module Outputs

output "bucket_name" {
  description = "Name of the R2 bucket"
  value       = local.bucket_name
}

output "bucket_url" {
  description = "Direct R2 bucket URL"
  value       = local.bucket_url
  sensitive   = true
}

output "public_url" {
  description = "Public URL for accessing assets (custom domain if configured, otherwise bucket URL)"
  value       = local.public_url
  sensitive   = true
}

output "custom_domain_url" {
  description = "Custom domain URL (empty if not configured)"
  value       = local.custom_domain_url
}

output "custom_domain_record" {
  description = "Custom domain DNS record details (managed outside Terraform)"
  value = var.custom_domain != "" ? {
    name    = var.custom_domain
    content = local.bucket_name
    type    = "CNAME"
    proxied = true
    note    = "DNS record exists but is managed outside Terraform"
  } : null
}

output "deploy_token" {
  description = "API token for deployment access (sensitive)"
  value       = var.create_deploy_token ? cloudflare_api_token.r2_deploy[0].value : null
  sensitive   = true
}

output "deploy_token_id" {
  description = "API token ID for deployment access"
  value       = var.create_deploy_token ? cloudflare_api_token.r2_deploy[0].id : null
}

output "s3_endpoint" {
  description = "S3 API endpoint for R2 bucket (for AWS CLI and SDK usage)"
  value       = "https://${var.cloudflare_account_id}.r2.cloudflarestorage.com"
  sensitive   = true
}

output "cors_configuration" {
  description = "CORS configuration details (manual configuration required)"
  value = {
    allowed_origins = var.cors_origins
    allowed_methods = ["GET", "HEAD", "OPTIONS"]
    max_age_seconds = 3600
    note            = "CORS must be configured manually via Cloudflare dashboard until Terraform provider support is added"
  }
}
