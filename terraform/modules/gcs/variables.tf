/**
 * # Google Cloud Storage Module Variables
 */

variable "project_id" {
  description = "The GCP project ID where the bucket will be created"
  type        = string
}

variable "location" {
  description = "The location/region where the bucket will be created"
  type        = string
  default     = "us-central1"
}

variable "bucket_name" {
  description = "The name of the GCS bucket. Must be globally unique."
  type        = string
}

variable "storage_class" {
  description = "The storage class of the bucket"
  type        = string
  default     = "STANDARD"
}

variable "versioning" {
  description = "Whether to enable object versioning"
  type        = bool
  default     = false
}

variable "versioning_enabled" {
  description = "Legacy alias for versioning variable"
  type        = bool
  default     = false
}

variable "force_destroy" {
  description = "When deleting a bucket, this boolean option will delete all contained objects"
  type        = bool
  default     = false
}

variable "lifecycle_rules" {
  description = "List of lifecycle rules to configure"
  type = list(object({
    action = object({
      type          = string
      storage_class = optional(string)
    })
    condition = object({
      age                   = optional(number)
      created_before        = optional(string)
      with_state            = optional(string)
      matches_storage_class = optional(list(string))
      num_newer_versions    = optional(number)
    })
  }))
  default = []
} 