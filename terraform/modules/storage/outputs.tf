/**
 * # Storage Outputs
 *
 * Outputs for the Storage module
 */

output "bucket_name" {
  description = "The name of the storage bucket"
  value       = var.create_resources ? google_storage_bucket.default[0].name : (var.existing_resources_exist ? data.google_storage_bucket.existing[0].name : "a2a-${var.environment}-storage")
}

output "bucket_url" {
  description = "The URL of the storage bucket"
  value       = var.create_resources ? google_storage_bucket.default[0].url : (var.existing_resources_exist ? data.google_storage_bucket.existing[0].url : "gs://a2a-${var.environment}-storage")
}

output "kms_key_id" {
  description = "The ID of the KMS key used for encryption (if CMEK is enabled)"
  value       = var.use_cmek && (var.create_resources || var.existing_resources_exist) && length(google_kms_crypto_key.storage_key) > 0 ? google_kms_crypto_key.storage_key[0].id : null
}

output "encryption_enabled" {
  description = "Whether customer-managed encryption is enabled"
  value       = var.use_cmek
} 