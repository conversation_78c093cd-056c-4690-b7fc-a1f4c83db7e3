/**
 * # Storage Variables
 *
 * Variables for the Storage module
 */

variable "project_id" {
  description = "The Google Cloud project ID"
  type        = string
}

variable "region" {
  description = "The Google Cloud region"
  type        = string
}

variable "environment" {
  description = "The deployment environment (dev, staging, prod)"
  type        = string
}

variable "use_cmek" {
  description = "Whether to use a Customer-Managed Encryption Key (CMEK) for the storage bucket"
  type        = bool
  default     = false
}

variable "safe_cmek" {
  description = "Whether to apply CMEK even if permissions might not be ready (set to false if you get KMS permission errors)"
  type        = bool
  default     = true
}

variable "create_resources" {
  description = "Whether to create resources that may already exist (like buckets and KMS keyrings)"
  type        = bool
  default     = true
}

variable "existing_resources_exist" {
  description = "Whether existing resources (which would be referenced via data sources when create_resources=false) actually exist"
  type        = bool
  default     = false
}

variable "module_create_resources" {
  description = "Module-specific flag to override the global create_resources flag"
  type        = bool
  default     = null # When null, use the global create_resources value
}

variable "name_prefix" {
  description = "Prefix for resource names (usually includes app name and environment)"
  type        = string
  default     = null
} 