/**
 * # Storage Module
 *
 * This module creates and manages storage resources.
 * 
 * Note: This module implements storage resources for the A2A Platform.
 */

locals {
  # Use module_create_resources if set, otherwise use global create_resources
  should_create_resources = var.module_create_resources != null ? var.module_create_resources : var.create_resources
}

# Look up existing KMS keyring
data "google_kms_key_ring" "existing" {
  count    = var.existing_resources_exist && var.use_cmek ? 1 : 0
  name     = "${var.name_prefix}-storage-keyring"
  location = var.region
  project  = var.project_id
}

# Look up existing storage bucket
data "google_storage_bucket" "existing" {
  count   = var.create_resources ? 0 : (var.existing_resources_exist ? 1 : 0)
  name    = "${var.name_prefix}-storage"
  project = var.project_id
}

# Create a KMS keyring for storage encryption if requested
resource "google_kms_key_ring" "storage_keyring" {
  count    = var.use_cmek && (local.should_create_resources && !var.existing_resources_exist) ? 1 : 0
  name     = "a2a-${var.environment}-storage-keyring"
  location = var.region
  project  = var.project_id

  lifecycle {
    # Prevent destroy of existing keyrings
    prevent_destroy = true

    # Ignore all changes to prevent recreation of the resource
    ignore_changes = all
  }
}

# Create a KMS key for storage encryption if requested
resource "google_kms_crypto_key" "storage_key" {
  count    = var.use_cmek && (local.should_create_resources && !var.existing_resources_exist) ? 1 : 0
  name     = "a2a-${var.environment}-storage-key"
  key_ring = length(google_kms_key_ring.storage_keyring) > 0 ? google_kms_key_ring.storage_keyring[0].id : (length(data.google_kms_key_ring.existing) > 0 ? data.google_kms_key_ring.existing[0].id : null)
  purpose  = "ENCRYPT_DECRYPT"

  version_template {
    algorithm        = "GOOGLE_SYMMETRIC_ENCRYPTION"
    protection_level = "SOFTWARE"
  }
}

# Create a storage bucket
resource "google_storage_bucket" "default" {
  name          = "a2a-${var.environment}-storage"
  project       = var.project_id
  location      = var.region
  force_destroy = var.environment != "prod"
  count         = local.should_create_resources ? 1 : 0

  # Enable uniform bucket-level access for better security
  uniform_bucket_level_access = true

  versioning {
    enabled = true
  }

  # Enable customer-managed encryption key if provided
  dynamic "encryption" {
    for_each = var.use_cmek && var.safe_cmek ? [1] : []
    content {
      default_kms_key_name = google_kms_crypto_key.storage_key[0].id
    }
  }

  # Add lifecycle to handle failures gracefully
  lifecycle {
    # Prevent resource conflicts with pre-existing buckets
    create_before_destroy = true

    # Ignore changes to encryption to avoid recreation issues
    ignore_changes = [
      encryption
    ]
  }
} 