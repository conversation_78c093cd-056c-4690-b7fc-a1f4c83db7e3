# OIDC Provider Module for GitHub Actions

This module configures a Workload Identity Federation OIDC provider for GitHub Actions, enabling secure authentication from GitHub workflows to Google Cloud resources.

## Features

- Creates a Workload Identity Pool and Provider for GitHub Actions
- Configures attribute mapping for GitHub token claims
- Implements secure attribute conditions to restrict access
- Sets up a service account with appropriate permissions
- Supports importing existing resources to prevent conflicts

## Import Existing Resources

This module includes special handling for cases where the Workload Identity Pool or Provider already exist:

- The `import_existing_resources` variable (default: `true`) enables skipping creation of resources that already exist
- Existing resources are detected using data sources and referenced directly
- This approach is compatible with all Terraform versions (no import blocks needed)
- Destroy protection is enabled by default with `prevent_destroy = true` in lifecycle blocks

This approach allows you to:
1. Create new resources if they don't exist
2. Skip creation and use existing resources if they do exist
3. Prevent accidental destruction of critical identity resources

## Attribute Conditions

For security, the module restricts which GitHub workflows can authenticate:

- Only the specified GitHub repository is allowed (`github_repo` variable)
- Only workflows running from:
  - The `main` branch (`refs/heads/main`)
  - Tagged releases (`refs/tags/*`)

## Usage

```hcl
module "github_oidc" {
  source         = "./modules/oidc"
  project_id     = var.project_id
  project_number = var.project_number
  github_repo    = "your-org/your-repo"
  
  # Optional: Control import behavior
  import_existing_resources = true
}
```

## Output

The module provides these outputs for use in GitHub Actions:

```hcl
output "workload_identity_provider" {
  value = module.github_oidc.workload_identity_provider
  # Use this in GitHub Actions with google-github-actions/auth@v1
}

output "service_account_email" {
  value = module.github_oidc.service_account_email
  # Use this in GitHub Actions with google-github-actions/auth@v1
}
```

## GitHub Actions Setup

Add these secrets to your GitHub repository:

- `WORKLOAD_IDENTITY_PROVIDER`: The full provider path (from `workload_identity_provider` output)
- `GCP_SERVICE_ACCOUNT`: The service account email (from `service_account_email` output)

Then use them in your GitHub Actions workflow:

```yaml
- uses: google-github-actions/auth@v1
  with:
    workload_identity_provider: ${{ secrets.WORKLOAD_IDENTITY_PROVIDER }}
    service_account: ${{ secrets.GCP_SERVICE_ACCOUNT }}
``` 