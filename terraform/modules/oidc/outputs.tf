/**
 * # OIDC Provider Module Outputs
 */

output "workload_identity_pool_id" {
  description = "ID of the Workload Identity Pool"
  value       = local.pool_id
}

output "workload_identity_pool_provider_id" {
  description = "ID of the Workload Identity Pool Provider"
  value       = local.provider_id
}

output "service_account_email" {
  description = "Email of the service account"
  value = length(google_service_account.github_actions) > 0 ? google_service_account.github_actions[0].email : (
    length(data.google_service_account.existing_github_actions_sa) > 0 ? data.google_service_account.existing_github_actions_sa[0].email :
    "${var.service_account_id}@${var.project_id}.iam.gserviceaccount.com"
  )
}

output "workload_identity_provider" {
  description = "Full identifier of the Workload Identity Provider"
  value       = "projects/${var.project_number}/locations/global/workloadIdentityPools/${local.pool_id}/providers/${local.provider_id}"
} 