/**
 * # OIDC Provider Module Variables
 */

variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "project_number" {
  description = "The GCP project number"
  type        = string
}

# JWKS is now fetched automatically from GitHub's endpoint

variable "pool_id" {
  description = "ID for the Workload Identity Pool"
  type        = string
  default     = ""
}

variable "provider_id" {
  description = "ID for the Workload Identity Provider"
  type        = string
  default     = ""
}

variable "service_account_id" {
  description = "ID for the GitHub Actions service account"
  type        = string
  default     = "github-actions"
}

variable "github_repo" {
  description = "The GitHub repository in owner/repo format."
  type        = string
}

variable "service_account_roles" {
  description = "IAM roles to assign to the service account"
  type        = list(string)
  default = [
    # Using more specific roles instead of broad editor role
    "roles/storage.admin",          # For terraform state management
    "roles/compute.admin",          # For managing compute resources
    "roles/iam.serviceAccountUser", # For using service accounts
    "roles/artifactregistry.admin"  # For managing container images
  ]
}

variable "import_existing_resources" {
  description = "Whether to import existing Workload Identity Pool and Provider resources"
  type        = bool
  default     = true
}

variable "create_resources" {
  description = "Whether to create the Workload Identity Pool and Provider resources"
  type        = bool
  default     = true
}

variable "prevent_destroy" {
  description = "This variable is kept for backward compatibility but is not used. Destroy prevention is hardcoded in the lifecycle blocks."
  type        = bool
  default     = true
}

variable "existing_resources_exist" {
  description = "Flag to indicate if some base resources (like the OIDC pool or SA) might already exist."
  type        = bool
  default     = false
} 