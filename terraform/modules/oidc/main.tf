/**
 * # OIDC Provider Module
 *
 * This module configures a Workload Identity Federation OIDC provider for GitHub Actions.
 * 
 * ## Security Features
 * 
 * - Implements attribute conditions to restrict which GitHub workflows can assume the role
 * - Only allows authentication from specific repositories
 * - Only permits workflows running from the main branch or tagged releases
 * - Maps GitHub token claims to Google attributes for fine-grained access control
 */

locals {
  pool_id     = var.pool_id != "" ? var.pool_id : "github-pool"
  provider_id = var.provider_id != "" ? var.provider_id : "github-provider"

  # Only create member binding if project_number is provided
  workload_identity_member = var.project_number != "" ? [
    "principalSet://iam.googleapis.com/projects/${var.project_number}/locations/global/workloadIdentityPools/${local.pool_id}/attribute.repository/${var.github_repo}"
  ] : []
}

# Workload Identity Pool
resource "google_iam_workload_identity_pool" "github_pool" {
  count                     = (var.create_resources && !var.existing_resources_exist) ? 1 : 0
  project                   = var.project_id
  workload_identity_pool_id = local.pool_id
  display_name              = "GitHub Actions Pool"
  description               = "Identity pool for GitHub Actions workflows"

  lifecycle {
    # Prevent destroy of existing pools
    prevent_destroy = true

    # Ignore changes to description and display_name to prevent unnecessary updates
    ignore_changes = [
      description,
      display_name,
    ]
  }
}

# OIDC Provider with GitHub's JWKS endpoint and attribute conditions
resource "google_iam_workload_identity_pool_provider" "github_provider" {
  count                              = (var.create_resources && !var.existing_resources_exist) ? 1 : 0
  project                            = var.project_id
  workload_identity_pool_id          = local.pool_id
  workload_identity_pool_provider_id = local.provider_id
  display_name                       = "GitHub Actions Provider"
  description                        = "OIDC identity pool provider for GitHub Actions"

  # Use OIDC with GitHub's JWKS endpoint
  oidc {
    issuer_uri = "https://token.actions.githubusercontent.com"
  }

  # Map GitHub token claims to Google attributes
  attribute_mapping = {
    "google.subject"             = "assertion.sub"
    "attribute.actor"            = "assertion.actor"
    "attribute.repository"       = "assertion.repository"
    "attribute.repository_owner" = "assertion.repository_owner"
    "attribute.workflow"         = "assertion.workflow"
    "attribute.ref"              = "assertion.ref"
  }

  # Add attribute conditions for security
  attribute_condition = "attribute.repository == \"${var.github_repo}\""

  lifecycle {
    # Prevent destroy of existing providers
    prevent_destroy = true

    # Ignore changes to description and display_name to prevent unnecessary updates
    ignore_changes = [
      description,
      display_name,
    ]
  }
}

# Service Account for GitHub
resource "google_service_account" "github_actions" {
  project      = var.project_id
  account_id   = var.service_account_id
  display_name = "GitHub Actions Service Account"
  description  = "Service account for GitHub Actions workflows"

  lifecycle {
    # Prevent resource conflicts with pre-existing service accounts
    ignore_changes = [
      description,
      display_name
    ]
  }

  # Only create if create_resources is true and existing_resources_exist is false
  count = (var.create_resources && !var.existing_resources_exist) ? 1 : 0
}

# IAM binding to allow the Workload Identity Pool to impersonate the Service Account
resource "google_service_account_iam_binding" "workload_identity_binding" {
  count              = var.project_number != "" ? 1 : 0 # Binding should exist if project_number is known
  service_account_id = (var.create_resources && !var.existing_resources_exist && length(google_service_account.github_actions) > 0) ? google_service_account.github_actions[0].name : "projects/${var.project_id}/serviceAccounts/${var.service_account_id}@${var.project_id}.iam.gserviceaccount.com"
  role               = "roles/iam.workloadIdentityUser"
  members            = local.workload_identity_member
}

data "google_service_account" "existing_github_actions_sa" {
  count      = var.existing_resources_exist ? 1 : 0 # Only fetch if we expect it to exist
  project    = var.project_id
  account_id = var.service_account_id
} 