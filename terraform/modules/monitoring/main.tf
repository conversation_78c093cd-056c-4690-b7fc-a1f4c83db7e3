/**
 * Terraform module for comprehensive Google Cloud Monitoring.
 * 
 * This module sets up monitoring dashboards, alerting policies, and log-based metrics
 * for the A2A Platform infrastructure including Cloud Run services, databases, and networking.
 */

# Variables are defined in variables.tf to avoid duplication

# Dashboard for Application Overview
resource "google_monitoring_dashboard" "application_overview" {
  dashboard_json = jsonencode({
    displayName = "A2A Platform - ${var.environment} Overview"
    mosaicLayout = {
      tiles = [
        {
          width  = 6
          height = 4
          widget = {
            title = "Cloud Run Request Rate"
            xyChart = {
              dataSets = [
                {
                  timeSeriesQuery = {
                    timeSeriesFilter = {
                      filter = "resource.type=\"cloud_run_revision\" AND metric.type=\"run.googleapis.com/request_count\""
                      aggregation = {
                        alignmentPeriod    = "60s"
                        perSeriesAligner   = "ALIGN_RATE"
                        crossSeriesReducer = "REDUCE_SUM"
                        groupByFields      = ["resource.labels.service_name"]
                      }
                    }
                  }
                  plotType = "LINE"
                }
              ]
              yAxis = {
                label = "Requests/sec"
                scale = "LINEAR"
              }
            }
          }
        },
        {
          width  = 6
          height = 4
          widget = {
            title = "Cloud Run Response Latency (95th percentile)"
            xyChart = {
              dataSets = [
                {
                  timeSeriesQuery = {
                    timeSeriesFilter = {
                      filter = "resource.type=\"cloud_run_revision\" AND metric.type=\"run.googleapis.com/request_latencies\""
                      aggregation = {
                        alignmentPeriod    = "60s"
                        perSeriesAligner   = "ALIGN_DELTA"
                        crossSeriesReducer = "REDUCE_PERCENTILE_95"
                        groupByFields      = ["resource.labels.service_name"]
                      }
                    }
                  }
                  plotType = "LINE"
                }
              ]
              yAxis = {
                label = "Latency (ms)"
                scale = "LINEAR"
              }
            }
          }
        },
        {
          width  = 6
          height = 4
          widget = {
            title = "Cloud Run Error Rate"
            xyChart = {
              dataSets = [
                {
                  timeSeriesQuery = {
                    timeSeriesFilter = {
                      filter = "resource.type=\"cloud_run_revision\" AND metric.type=\"run.googleapis.com/request_count\" AND metric.labels.response_code_class!=\"2xx\""
                      aggregation = {
                        alignmentPeriod    = "60s"
                        perSeriesAligner   = "ALIGN_RATE"
                        crossSeriesReducer = "REDUCE_SUM"
                        groupByFields      = ["resource.labels.service_name", "metric.labels.response_code_class"]
                      }
                    }
                  }
                  plotType = "LINE"
                }
              ]
              yAxis = {
                label = "Error Rate"
                scale = "LINEAR"
              }
            }
          }
        },
        {
          width  = 6
          height = 4
          widget = {
            title = "Cloud Run CPU Utilization"
            xyChart = {
              dataSets = [
                {
                  timeSeriesQuery = {
                    timeSeriesFilter = {
                      filter = "resource.type=\"cloud_run_revision\" AND metric.type=\"run.googleapis.com/container/cpu/utilizations\""
                      aggregation = {
                        alignmentPeriod    = "60s"
                        perSeriesAligner   = "ALIGN_MEAN"
                        crossSeriesReducer = "REDUCE_MEAN"
                        groupByFields      = ["resource.labels.service_name"]
                      }
                    }
                  }
                  plotType = "LINE"
                }
              ]
              yAxis = {
                label = "CPU Utilization"
                scale = "LINEAR"
              }
            }
          }
        },
        {
          width  = 6
          height = 4
          widget = {
            title = "Cloud Run Memory Utilization"
            xyChart = {
              dataSets = [
                {
                  timeSeriesQuery = {
                    timeSeriesFilter = {
                      filter = "resource.type=\"cloud_run_revision\" AND metric.type=\"run.googleapis.com/container/memory/utilizations\""
                      aggregation = {
                        alignmentPeriod    = "60s"
                        perSeriesAligner   = "ALIGN_MEAN"
                        crossSeriesReducer = "REDUCE_MEAN"
                        groupByFields      = ["resource.labels.service_name"]
                      }
                    }
                  }
                  plotType = "LINE"
                }
              ]
              yAxis = {
                label = "Memory Utilization"
                scale = "LINEAR"
              }
            }
          }
        },
        {
          width  = 6
          height = 4
          widget = {
            title = "Cloud Run Instance Count"
            xyChart = {
              dataSets = [
                {
                  timeSeriesQuery = {
                    timeSeriesFilter = {
                      filter = "resource.type=\"cloud_run_revision\" AND metric.type=\"run.googleapis.com/container/instance_count\""
                      aggregation = {
                        alignmentPeriod    = "60s"
                        perSeriesAligner   = "ALIGN_MEAN"
                        crossSeriesReducer = "REDUCE_SUM"
                        groupByFields      = ["resource.labels.service_name"]
                      }
                    }
                  }
                  plotType = "LINE"
                }
              ]
              yAxis = {
                label = "Instance Count"
                scale = "LINEAR"
              }
            }
          }
        }
      ]
    }
  })
}

# Dashboard for Database Monitoring (if Cloud SQL is configured)
resource "google_monitoring_dashboard" "database_overview" {
  count = var.cloudsql_instance_id != "" ? 1 : 0

  dashboard_json = jsonencode({
    displayName = "A2A Platform - ${var.environment} Database"
    mosaicLayout = {
      tiles = [
        {
          width  = 6
          height = 4
          widget = {
            title = "Database CPU Utilization"
            xyChart = {
              dataSets = [
                {
                  timeSeriesQuery = {
                    timeSeriesFilter = {
                      filter = "resource.type=\"cloudsql_database\" AND metric.type=\"cloudsql.googleapis.com/database/cpu/utilization\""
                      aggregation = {
                        alignmentPeriod  = "60s"
                        perSeriesAligner = "ALIGN_MEAN"
                      }
                    }
                  }
                  plotType = "LINE"
                }
              ]
              yAxis = {
                label = "CPU Utilization"
                scale = "LINEAR"
              }
            }
          }
        },
        {
          width  = 6
          height = 4
          widget = {
            title = "Database Memory Utilization"
            xyChart = {
              dataSets = [
                {
                  timeSeriesQuery = {
                    timeSeriesFilter = {
                      filter = "resource.type=\"cloudsql_database\" AND metric.type=\"cloudsql.googleapis.com/database/memory/utilization\""
                      aggregation = {
                        alignmentPeriod  = "60s"
                        perSeriesAligner = "ALIGN_MEAN"
                      }
                    }
                  }
                  plotType = "LINE"
                }
              ]
              yAxis = {
                label = "Memory Utilization"
                scale = "LINEAR"
              }
            }
          }
        },
        {
          width  = 6
          height = 4
          widget = {
            title = "Database Connections"
            xyChart = {
              dataSets = [
                {
                  timeSeriesQuery = {
                    timeSeriesFilter = {
                      filter = "resource.type=\"cloudsql_database\" AND metric.type=\"cloudsql.googleapis.com/database/postgresql/num_backends\""
                      aggregation = {
                        alignmentPeriod  = "60s"
                        perSeriesAligner = "ALIGN_MEAN"
                      }
                    }
                  }
                  plotType = "LINE"
                }
              ]
              yAxis = {
                label = "Active Connections"
                scale = "LINEAR"
              }
            }
          }
        },
        {
          width  = 6
          height = 4
          widget = {
            title = "Database Disk Utilization"
            xyChart = {
              dataSets = [
                {
                  timeSeriesQuery = {
                    timeSeriesFilter = {
                      filter = "resource.type=\"cloudsql_database\" AND metric.type=\"cloudsql.googleapis.com/database/disk/utilization\""
                      aggregation = {
                        alignmentPeriod  = "60s"
                        perSeriesAligner = "ALIGN_MEAN"
                      }
                    }
                  }
                  plotType = "LINE"
                }
              ]
              yAxis = {
                label = "Disk Utilization"
                scale = "LINEAR"
              }
            }
          }
        }
      ]
    }
  })
}

# Log-based metric for application errors
resource "google_logging_metric" "application_errors" {
  name   = "a2a_application_errors_${var.environment}"
  filter = <<-EOT
    resource.type="cloud_run_revision"
    severity>=ERROR
    OR (jsonPayload.level="error" OR jsonPayload.severity="error")
  EOT

  metric_descriptor {
    metric_kind  = "GAUGE"
    value_type   = "INT64"
    unit         = "1"
    display_name = "A2A Application Errors"
  }

  value_extractor = "EXTRACT(1)"
}

# Log-based metric for GraphQL errors
resource "google_logging_metric" "graphql_errors" {
  name   = "a2a_graphql_errors_${var.environment}"
  filter = <<-EOT
    resource.type="cloud_run_revision"
    (jsonPayload.message:"GraphQL" OR jsonPayload.path:"graphql")
    AND (severity>=ERROR OR jsonPayload.level="error")
  EOT

  metric_descriptor {
    metric_kind  = "GAUGE"
    value_type   = "INT64"
    unit         = "1"
    display_name = "A2A GraphQL Errors"
  }

  value_extractor = "EXTRACT(1)"
}

# Log-based metric for authentication failures
resource "google_logging_metric" "auth_failures" {
  name   = "a2a_auth_failures_${var.environment}"
  filter = <<-EOT
    resource.type="cloud_run_revision"
    (jsonPayload.message:"unauthorized" OR jsonPayload.message:"authentication" OR jsonPayload.message:"forbidden")
    AND (severity>=WARNING OR jsonPayload.level="warning" OR jsonPayload.level="error")
  EOT

  metric_descriptor {
    metric_kind  = "GAUGE"
    value_type   = "INT64"
    unit         = "1"
    display_name = "A2A Authentication Failures"
  }

  value_extractor = "EXTRACT(1)"
}

# Log-based metric for slow query detection
resource "google_logging_metric" "slow_queries" {
  name   = "a2a_slow_queries_${var.environment}"
  filter = <<-EOT
    resource.type="cloud_run_revision"
    (jsonPayload.message:"query" OR jsonPayload.message:"SELECT" OR jsonPayload.message:"UPDATE" OR jsonPayload.message:"INSERT")
    AND (jsonPayload.duration>5000 OR jsonPayload.execution_time>5000)
  EOT

  metric_descriptor {
    metric_kind  = "GAUGE"
    value_type   = "INT64"
    unit         = "1"
    display_name = "A2A Slow Queries"
  }

  value_extractor = "EXTRACT(1)"
}

# Log-based metric for Redis connection issues
resource "google_logging_metric" "redis_errors" {
  name   = "a2a_redis_errors_${var.environment}"
  filter = <<-EOT
    resource.type="cloud_run_revision"
    (jsonPayload.message:"redis" OR jsonPayload.message:"cache")
    AND (severity>=ERROR OR jsonPayload.level="error")
  EOT

  metric_descriptor {
    metric_kind  = "GAUGE"
    value_type   = "INT64"
    unit         = "1"
    display_name = "A2A Redis Errors"
  }

  value_extractor = "EXTRACT(1)"
}

# Log-based metric for WebSocket connection issues
resource "google_logging_metric" "websocket_errors" {
  name   = "a2a_websocket_errors_${var.environment}"
  filter = <<-EOT
    resource.type="cloud_run_revision"
    (jsonPayload.message:"websocket" OR jsonPayload.message:"ws" OR jsonPayload.path:"subscription")
    AND (severity>=ERROR OR jsonPayload.level="error")
  EOT

  metric_descriptor {
    metric_kind  = "GAUGE"
    value_type   = "INT64"
    unit         = "1"
    display_name = "A2A WebSocket Errors"
  }

  value_extractor = "EXTRACT(1)"
}