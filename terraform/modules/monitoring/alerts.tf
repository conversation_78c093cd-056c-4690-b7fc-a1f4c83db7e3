/**
 * Alert policies for the A2A Platform monitoring.
 * 
 * This file defines alert policies for various system components including
 * Cloud Run services, databases, and application-specific metrics.
 */

# High error rate alert for Cloud Run services
resource "google_monitoring_alert_policy" "high_error_rate" {
  display_name = "A2A ${var.environment} - High Error Rate"
  combiner     = "OR"

  conditions {
    display_name = "Error rate above threshold"

    condition_threshold {
      filter          = "resource.type=\"cloud_run_revision\" AND metric.type=\"run.googleapis.com/request_count\" AND metric.labels.response_code_class!=\"2xx\""
      comparison      = "COMPARISON_GT"
      threshold_value = var.alert_thresholds.error_rate_threshold
      duration        = var.alert_durations.default_duration

      trigger {
        count = 1
      }

      aggregations {
        alignment_period     = "60s"
        per_series_aligner   = "ALIGN_RATE"
        cross_series_reducer = "REDUCE_SUM"
        group_by_fields      = ["resource.labels.service_name"]
      }
    }
  }

  notification_channels = var.notification_channels

  documentation {
    content   = <<-EOT
      ## High Error Rate Alert
      
      The error rate for Cloud Run services has exceeded 5% for the past 5 minutes.
      
      This indicates potential issues with the application or infrastructure.
      
      ### Remediation Steps:
      1. Check Cloud Run service logs for error details
      2. Verify recent deployments or configuration changes
      3. Check database connectivity and performance
      4. Review application metrics and resource utilization
    EOT
    mime_type = "text/markdown"
  }
}

# High CPU utilization alert for Cloud Run
resource "google_monitoring_alert_policy" "high_cpu_utilization" {
  display_name = "A2A ${var.environment} - High CPU Utilization"
  combiner     = "OR"

  conditions {
    display_name = "CPU utilization above threshold"

    condition_threshold {
      filter          = "resource.type=\"cloud_run_revision\" AND metric.type=\"run.googleapis.com/container/cpu/utilizations\""
      comparison      = "COMPARISON_GT"
      threshold_value = var.alert_thresholds.cpu_utilization_threshold
      duration        = var.alert_durations.default_duration

      trigger {
        count = 1
      }

      aggregations {
        alignment_period     = "60s"
        per_series_aligner   = "ALIGN_MEAN"
        cross_series_reducer = "REDUCE_MEAN"
        group_by_fields      = ["resource.labels.service_name"]
      }
    }
  }

  notification_channels = var.notification_channels

  documentation {
    content   = <<-EOT
      ## High CPU Utilization Alert
      
      Cloud Run service CPU utilization has exceeded 80% for the past 5 minutes.
      
      This may indicate the need for scaling or performance optimization.
      
      ### Remediation Steps:
      1. Check current request volume and patterns
      2. Consider increasing CPU allocation or concurrency settings
      3. Review application code for performance bottlenecks
      4. Monitor scaling behavior and adjust if necessary
    EOT
    mime_type = "text/markdown"
  }
}

# High memory utilization alert for Cloud Run
resource "google_monitoring_alert_policy" "high_memory_utilization" {
  display_name = "A2A ${var.environment} - High Memory Utilization"
  combiner     = "OR"

  conditions {
    display_name = "Memory utilization above threshold"

    condition_threshold {
      filter          = "resource.type=\"cloud_run_revision\" AND metric.type=\"run.googleapis.com/container/memory/utilizations\""
      comparison      = "COMPARISON_GT"
      threshold_value = var.alert_thresholds.memory_utilization_threshold
      duration        = var.alert_durations.default_duration

      trigger {
        count = 1
      }

      aggregations {
        alignment_period     = "60s"
        per_series_aligner   = "ALIGN_MEAN"
        cross_series_reducer = "REDUCE_MEAN"
        group_by_fields      = ["resource.labels.service_name"]
      }
    }
  }

  notification_channels = var.notification_channels

  documentation {
    content   = <<-EOT
      ## High Memory Utilization Alert
      
      Cloud Run service memory utilization has exceeded 85% for the past 5 minutes.
      
      This may indicate memory leaks or the need for increased memory allocation.
      
      ### Remediation Steps:
      1. Check for memory leaks in application code
      2. Consider increasing memory allocation for the service
      3. Review memory usage patterns and optimize if necessary
      4. Check for large data processing operations
    EOT
    mime_type = "text/markdown"
  }
}

# High response latency alert
resource "google_monitoring_alert_policy" "high_response_latency" {
  display_name = "A2A ${var.environment} - High Response Latency"
  combiner     = "OR"

  conditions {
    display_name = "Response latency above threshold (95th percentile)"

    condition_threshold {
      filter          = "resource.type=\"cloud_run_revision\" AND metric.type=\"run.googleapis.com/request_latencies\""
      comparison      = "COMPARISON_GT"
      threshold_value = var.alert_thresholds.response_latency_threshold
      duration        = var.alert_durations.default_duration

      trigger {
        count = 1
      }

      aggregations {
        alignment_period     = "60s"
        per_series_aligner   = "ALIGN_DELTA"
        cross_series_reducer = "REDUCE_PERCENTILE_95"
        group_by_fields      = ["resource.labels.service_name"]
      }
    }
  }

  notification_channels = var.notification_channels

  documentation {
    content   = <<-EOT
      ## High Response Latency Alert
      
      Response latency (95th percentile) has exceeded 2 seconds for the past 5 minutes.
      
      This indicates potential performance issues with the application or dependencies.
      
      ### Remediation Steps:
      1. Check database query performance and connection pool status
      2. Review external API call latencies
      3. Check for resource contention or scaling issues
      4. Analyze slow request patterns in application logs
    EOT
    mime_type = "text/markdown"
  }
}

# Database CPU utilization alert (if Cloud SQL is configured)
resource "google_monitoring_alert_policy" "database_high_cpu" {
  count = var.cloudsql_instance_id != "" ? 1 : 0

  display_name = "A2A ${var.environment} - Database High CPU"
  combiner     = "OR"

  conditions {
    display_name = "Database CPU utilization above 80%"

    condition_threshold {
      filter          = "resource.type=\"cloudsql_database\" AND resource.labels.database_id=\"${var.project_id}:${var.cloudsql_instance_id}\" AND metric.type=\"cloudsql.googleapis.com/database/cpu/utilization\""
      comparison      = "COMPARISON_GT"
      threshold_value = 0.8    # 80% CPU utilization
      duration        = "300s" # Alert after 5 minutes

      trigger {
        count = 1
      }

      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_MEAN"
      }
    }
  }

  notification_channels = var.notification_channels

  documentation {
    content   = <<-EOT
      ## Database High CPU Alert
      
      Cloud SQL database CPU utilization has exceeded 80% for the past 5 minutes.
      
      This may indicate inefficient queries or the need for database scaling.
      
      ### Remediation Steps:
      1. Check for long-running or inefficient queries
      2. Review database performance insights
      3. Consider increasing database instance size
      4. Optimize database indexes and query patterns
    EOT
    mime_type = "text/markdown"
  }
}

# Database connection count alert (if Cloud SQL is configured)
resource "google_monitoring_alert_policy" "database_high_connections" {
  count = var.cloudsql_instance_id != "" ? 1 : 0

  display_name = "A2A ${var.environment} - Database High Connection Count"
  combiner     = "OR"

  conditions {
    display_name = "Database connections above 80% of limit"

    condition_threshold {
      filter          = "resource.type=\"cloudsql_database\" AND resource.labels.database_id=\"${var.project_id}:${var.cloudsql_instance_id}\" AND metric.type=\"cloudsql.googleapis.com/database/postgresql/num_backends\""
      comparison      = "COMPARISON_GT"
      threshold_value = 80     # Adjust based on your connection limit
      duration        = "300s" # Alert after 5 minutes

      trigger {
        count = 1
      }

      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_MEAN"
      }
    }
  }

  notification_channels = var.notification_channels

  documentation {
    content   = <<-EOT
      ## Database High Connection Count Alert
      
      Database connection count has exceeded 80% of the configured limit.
      
      This may indicate connection leaks or the need for connection pool tuning.
      
      ### Remediation Steps:
      1. Check for connection leaks in application code
      2. Review connection pool configuration
      3. Monitor connection usage patterns
      4. Consider increasing max connections if appropriate
    EOT
    mime_type = "text/markdown"
  }
}

# Application error rate alert based on log-based metric
resource "google_monitoring_alert_policy" "application_errors" {
  display_name = "A2A ${var.environment} - Application Errors"
  combiner     = "OR"

  conditions {
    display_name = "Application error rate increased"

    condition_threshold {
      filter          = "metric.type=\"logging.googleapis.com/user/${google_logging_metric.application_errors.name}\""
      comparison      = "COMPARISON_GT"
      threshold_value = 10    # More than 10 errors per minute
      duration        = "60s" # Alert after 1 minute

      trigger {
        count = 1
      }

      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_RATE"
      }
    }
  }

  notification_channels = var.notification_channels

  documentation {
    content   = <<-EOT
      ## Application Errors Alert
      
      Application error rate has increased above the threshold.
      
      This indicates potential issues with application logic or dependencies.
      
      ### Remediation Steps:
      1. Check application logs for error details and patterns
      2. Review recent code deployments
      3. Check external service dependencies
      4. Monitor related system metrics for correlation
    EOT
    mime_type = "text/markdown"
  }

  depends_on = [google_logging_metric.application_errors]
}

# GraphQL error rate alert
resource "google_monitoring_alert_policy" "graphql_errors" {
  display_name = "A2A ${var.environment} - GraphQL Errors"
  combiner     = "OR"

  conditions {
    display_name = "GraphQL error rate increased"

    condition_threshold {
      filter          = "metric.type=\"logging.googleapis.com/user/${google_logging_metric.graphql_errors.name}\""
      comparison      = "COMPARISON_GT"
      threshold_value = 5     # More than 5 GraphQL errors per minute
      duration        = "60s" # Alert after 1 minute

      trigger {
        count = 1
      }

      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_RATE"
      }
    }
  }

  notification_channels = var.notification_channels

  documentation {
    content   = <<-EOT
      ## GraphQL Errors Alert
      
      GraphQL error rate has increased above the threshold.
      
      This indicates potential issues with GraphQL queries or resolvers.
      
      ### Remediation Steps:
      1. Check GraphQL error logs for specific query issues
      2. Review resolver implementations and database queries
      3. Check GraphQL schema validation
      4. Monitor client-side query patterns
    EOT
    mime_type = "text/markdown"
  }

  depends_on = [google_logging_metric.graphql_errors]
}

# Authentication failure alert
resource "google_monitoring_alert_policy" "auth_failures" {
  display_name = "A2A ${var.environment} - Authentication Failures"
  combiner     = "OR"

  conditions {
    display_name = "Authentication failure rate increased"

    condition_threshold {
      filter          = "metric.type=\"logging.googleapis.com/user/${google_logging_metric.auth_failures.name}\""
      comparison      = "COMPARISON_GT"
      threshold_value = 20     # More than 20 auth failures per minute
      duration        = "300s" # Alert after 5 minutes

      trigger {
        count = 1
      }

      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_RATE"
      }
    }
  }

  notification_channels = var.notification_channels

  documentation {
    content   = <<-EOT
      ## Authentication Failures Alert
      
      Authentication failure rate has increased significantly.
      
      This may indicate security issues or problems with the authentication system.
      
      ### Remediation Steps:
      1. Check authentication service logs and status
      2. Review failed authentication patterns for potential attacks
      3. Verify Clerk service status and configuration
      4. Check network connectivity to authentication providers
    EOT
    mime_type = "text/markdown"
  }

  depends_on = [google_logging_metric.auth_failures]
}

# Cloud Run service availability alert
resource "google_monitoring_alert_policy" "service_availability" {
  display_name = "A2A ${var.environment} - Service Availability"
  combiner     = "OR"

  conditions {
    display_name = "Service has no healthy instances"

    condition_threshold {
      filter          = "resource.type=\"cloud_run_revision\" AND metric.type=\"run.googleapis.com/container/instance_count\""
      comparison      = "COMPARISON_LT"
      threshold_value = 1 # At least 1 instance should be running
      duration        = var.alert_durations.quick_alert_duration

      trigger {
        count = 1
      }

      aggregations {
        alignment_period     = "60s"
        per_series_aligner   = "ALIGN_MEAN"
        cross_series_reducer = "REDUCE_SUM"
        group_by_fields      = ["resource.labels.service_name"]
      }
    }
  }

  notification_channels = var.notification_channels

  documentation {
    content   = <<-EOT
      ## Service Availability Alert
      
      One or more Cloud Run services have no healthy instances running.
      
      This indicates a critical service outage that requires immediate attention.
      
      ### Remediation Steps:
      1. Check Cloud Run service status and deployment logs
      2. Verify service configuration and environment variables
      3. Check for resource quota issues or billing problems
      4. Review recent deployments that may have caused the issue
    EOT
    mime_type = "text/markdown"
  }
}

# Load balancer backend failure alert
resource "google_monitoring_alert_policy" "backend_failures" {
  display_name = "A2A ${var.environment} - Backend Service Failures"
  combiner     = "OR"

  conditions {
    display_name = "High backend failure rate"

    condition_threshold {
      filter          = "resource.type=\"gce_backend_service\" AND metric.type=\"loadbalancing.googleapis.com/https/backend_request_count\" AND metric.labels.response_code!~\"2.*\""
      comparison      = "COMPARISON_GT"
      threshold_value = 0.1 # 10% backend failure rate
      duration        = var.alert_durations.default_duration

      trigger {
        count = 1
      }

      aggregations {
        alignment_period     = "60s"
        per_series_aligner   = "ALIGN_RATE"
        cross_series_reducer = "REDUCE_SUM"
        group_by_fields      = ["resource.labels.backend_service_name"]
      }
    }
  }

  notification_channels = var.notification_channels

  documentation {
    content   = <<-EOT
      ## Backend Service Failures Alert
      
      Backend services are experiencing a high failure rate (>10%).
      
      This indicates issues with backend connectivity or health.
      
      ### Remediation Steps:
      1. Check backend service health checks
      2. Verify backend instance availability and health
      3. Check network connectivity between load balancer and backends
      4. Review backend service configuration and capacity
    EOT
    mime_type = "text/markdown"
  }
}

# Request rate anomaly detection
resource "google_monitoring_alert_policy" "request_rate_anomaly" {
  display_name = "A2A ${var.environment} - Unusual Request Rate"
  combiner     = "OR"

  conditions {
    display_name = "Request rate significantly different from normal"

    condition_threshold {
      filter          = "resource.type=\"cloud_run_revision\" AND metric.type=\"run.googleapis.com/request_count\""
      comparison      = "COMPARISON_GT"
      threshold_value = 100 # Requests per second threshold
      duration        = var.alert_durations.default_duration

      trigger {
        count = 1
      }

      aggregations {
        alignment_period     = "60s"
        per_series_aligner   = "ALIGN_RATE"
        cross_series_reducer = "REDUCE_SUM"
        group_by_fields      = ["resource.labels.service_name"]
      }
    }
  }

  notification_channels = var.notification_channels

  documentation {
    content   = <<-EOT
      ## Unusual Request Rate Alert
      
      Request rate is significantly higher than normal patterns.
      
      This could indicate a traffic spike, DDoS attack, or service issues.
      
      ### Remediation Steps:
      1. Check if this is expected traffic (marketing campaign, etc.)
      2. Monitor service performance and scaling behavior
      3. Check for signs of malicious traffic or attacks
      4. Consider rate limiting or traffic shaping if necessary
    EOT
    mime_type = "text/markdown"
  }
}