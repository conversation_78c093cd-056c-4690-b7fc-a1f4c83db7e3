/**
 * Variables for the monitoring module.
 */

variable "project_id" {
  description = "The Google Cloud project ID"
  type        = string
  validation {
    condition     = length(var.project_id) > 0
    error_message = "Project ID must not be empty."
  }
}

variable "environment" {
  description = "Environment name (staging, production, dev)"
  type        = string
  validation {
    condition     = contains(["staging", "production", "dev"], var.environment)
    error_message = "Environment must be one of: staging, production, dev."
  }
}

variable "notification_channels" {
  description = "List of notification channel IDs for alerts. Create these in Google Cloud Console first."
  type        = list(string)
  default     = []
}

variable "cloud_run_services" {
  description = "List of Cloud Run service names to monitor"
  type        = list(string)
  default     = ["api-staging", "workers-staging"]
}

variable "cloudsql_instance_id" {
  description = "Cloud SQL instance ID to monitor (without project prefix)"
  type        = string
  default     = ""
}

variable "alert_thresholds" {
  description = "Configurable thresholds for alerts"
  type = object({
    error_rate_threshold         = optional(number, 0.05) # 5% error rate
    cpu_utilization_threshold    = optional(number, 0.8)  # 80% CPU
    memory_utilization_threshold = optional(number, 0.85) # 85% memory
    response_latency_threshold   = optional(number, 2000) # 2 seconds in ms
    db_cpu_threshold             = optional(number, 0.8)  # 80% DB CPU
    db_connections_threshold     = optional(number, 80)   # 80 connections
    application_errors_threshold = optional(number, 10)   # 10 errors/min
    graphql_errors_threshold     = optional(number, 5)    # 5 GraphQL errors/min
    auth_failures_threshold      = optional(number, 20)   # 20 auth failures/min
    backend_failure_threshold    = optional(number, 0.1)  # 10% backend failure rate
    request_rate_threshold       = optional(number, 100)  # 100 requests/sec threshold
    slow_query_threshold         = optional(number, 5)    # 5 slow queries/min
    redis_error_threshold        = optional(number, 10)   # 10 Redis errors/min
    websocket_error_threshold    = optional(number, 5)    # 5 WebSocket errors/min
  })
  default = {}
}

variable "alert_durations" {
  description = "Configurable durations for alerts before triggering"
  type = object({
    default_duration     = optional(string, "300s") # 5 minutes
    quick_alert_duration = optional(string, "60s")  # 1 minute for critical alerts
    auth_alert_duration  = optional(string, "300s") # 5 minutes for auth alerts
  })
  default = {}
}

variable "enable_ssl_monitoring" {
  description = "Whether to enable SSL certificate monitoring"
  type        = bool
  default     = false
}

variable "certificate_ids" {
  description = "List of SSL certificate IDs to monitor (required if enable_ssl_monitoring is true)"
  type        = list(string)
  default     = []
  validation {
    condition     = length(var.certificate_ids) > 0 || !var.enable_ssl_monitoring
    error_message = "certificate_ids must contain at least one certificate ID when enable_ssl_monitoring is true."
  }
}

variable "certificate_expiry_threshold_days" {
  description = "Alert when SSL certificate is within this many days of expiration"
  type        = number
  default     = 30
}