/**
 * Terraform module for SSL certificate monitoring.
 * 
 * This module sets up monitoring and alerting for SSL certificates,
 * including expiration alerts and performance monitoring.
 */

# Variables are defined in variables.tf

# Alert policy for certificate expiration
resource "google_monitoring_alert_policy" "certificate_expiration" {
  count = var.enable_ssl_monitoring && length(var.certificate_ids) > 0 ? 1 : 0

  display_name = "SSL Certificate Expiration Alert"
  combiner     = "OR"

  conditions {
    display_name = "SSL Certificate expiring soon"

    condition_threshold {
      filter          = "resource.type=\"certificatemanager.googleapis.com/Certificate\" AND (${join(" OR ", formatlist("resource.labels.certificate_id=\"%s\"", var.certificate_ids))}) AND metric.type=\"certificatemanager.googleapis.com/certificate/expiration_time\""
      comparison      = "COMPARISON_LT"
      threshold_value = var.certificate_expiry_threshold_days * 86400 # Convert days to seconds
      duration        = "0s"

      trigger {
        count = 1
      }

      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_MEAN"
      }
    }
  }

  notification_channels = var.notification_channels

  documentation {
    content   = <<-EOT
      ## SSL Certificate Expiration Alert
      
      One or more SSL certificates are about to expire within ${var.certificate_expiry_threshold_days} days.
      
      Please take immediate action to renew the certificates to prevent service disruption.
      
      ### Remediation Steps:
      1. Check the certificate details in Certificate Manager
      2. Renew the certificate if it's not set for auto-renewal
      3. Verify the renewal process completed successfully
    EOT
    mime_type = "text/markdown"
  }
}

# Alert policy for SSL handshake latency
resource "google_monitoring_alert_policy" "ssl_handshake_latency" {
  count = var.enable_ssl_monitoring && length(var.certificate_ids) > 0 ? 1 : 0

  display_name = "SSL Handshake Latency Alert"
  combiner     = "OR"

  conditions {
    display_name = "SSL Handshake taking too long"

    condition_threshold {
      filter          = "resource.type=\"https_lb_rule\" AND metric.type=\"loadbalancing.googleapis.com/https/frontend/ssl_handshake_latency\""
      comparison      = "COMPARISON_GT"
      threshold_value = 0.2    # 200ms
      duration        = "300s" # Alert after 5 minutes of high latency

      trigger {
        count = 1
      }

      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_PERCENTILE_99"
      }
    }
  }

  notification_channels = var.notification_channels

  documentation {
    content   = <<-EOT
      ## SSL Handshake Latency Alert
      
      SSL handshake latency is exceeding the threshold of 200ms (p99).
      
      This may indicate issues with SSL configuration, certificate chain, or infrastructure problems.
      
      ### Remediation Steps:
      1. Check for recent load balancer or certificate changes
      2. Verify SSL certificate chain is correct and complete
      3. Check for network issues between load balancer and backends
      4. Consider optimizing SSL configuration for better performance
    EOT
    mime_type = "text/markdown"
  }
}

# Alert policy for SSL throughput
resource "google_monitoring_alert_policy" "ssl_throughput" {
  count = var.enable_ssl_monitoring && length(var.certificate_ids) > 0 ? 1 : 0

  display_name = "SSL Throughput Alert"
  combiner     = "OR"

  conditions {
    display_name = "SSL Throughput below threshold"

    condition_threshold {
      filter          = "resource.type=\"https_lb_rule\" AND metric.type=\"loadbalancing.googleapis.com/https/frontend/request_count\""
      comparison      = "COMPARISON_LT"
      threshold_value = 0.5    # Less than 50% of expected throughput
      duration        = "300s" # Alert after 5 minutes of low throughput

      trigger {
        count = 1
      }

      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_RATE"
      }
    }
  }

  notification_channels = var.notification_channels

  documentation {
    content   = <<-EOT
      ## SSL Throughput Alert
      
      SSL throughput is below the expected threshold.
      
      This may indicate issues with the load balancer, backend services, or client connectivity problems.
      
      ### Remediation Steps:
      1. Check backend service health
      2. Verify load balancer configuration
      3. Check for network issues or routing problems
      4. Analyze client connectivity patterns for unusual behavior
    EOT
    mime_type = "text/markdown"
  }
}
