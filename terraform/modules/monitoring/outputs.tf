/**
 * Outputs for the monitoring module.
 */

output "dashboard_urls" {
  description = "URLs of the created monitoring dashboards"
  value = {
    application_overview = "https://console.cloud.google.com/monitoring/dashboards/custom/${google_monitoring_dashboard.application_overview.id}?project=${var.project_id}"
    database_overview    = var.cloudsql_instance_id != "" ? "https://console.cloud.google.com/monitoring/dashboards/custom/${google_monitoring_dashboard.database_overview[0].id}?project=${var.project_id}" : null
  }
}

output "alert_policy_names" {
  description = "Names of the created alert policies"
  value = concat([
    google_monitoring_alert_policy.high_error_rate.name,
    google_monitoring_alert_policy.high_cpu_utilization.name,
    google_monitoring_alert_policy.high_memory_utilization.name,
    google_monitoring_alert_policy.high_response_latency.name,
    google_monitoring_alert_policy.application_errors.name,
    google_monitoring_alert_policy.graphql_errors.name,
    google_monitoring_alert_policy.auth_failures.name,
    google_monitoring_alert_policy.service_availability.name,
    google_monitoring_alert_policy.backend_failures.name,
    google_monitoring_alert_policy.request_rate_anomaly.name
    ],
    # Add database alert policies if they exist
    var.cloudsql_instance_id != "" ? [
      google_monitoring_alert_policy.database_high_cpu[0].name,
      google_monitoring_alert_policy.database_high_connections[0].name
    ] : [],
    # Add SSL alert policies if they exist
    var.enable_ssl_monitoring && length(var.certificate_ids) > 0 ? [
      google_monitoring_alert_policy.certificate_expiration[0].name,
      google_monitoring_alert_policy.ssl_handshake_latency[0].name,
      google_monitoring_alert_policy.ssl_throughput[0].name
  ] : [])
}

output "log_based_metrics" {
  description = "Names of the created log-based metrics"
  value = [
    google_logging_metric.application_errors.name,
    google_logging_metric.graphql_errors.name,
    google_logging_metric.auth_failures.name,
    google_logging_metric.slow_queries.name,
    google_logging_metric.redis_errors.name,
    google_logging_metric.websocket_errors.name
  ]
}

output "monitoring_workspace_url" {
  description = "URL to the Google Cloud Monitoring workspace"
  value       = "https://console.cloud.google.com/monitoring?project=${var.project_id}"
}