/**
 * # Database Module
 *
 * This module creates and manages Cloud SQL (PostgreSQL) resources.
 * 
 * ## Security Notes
 * 
 * This module uses Google Provider v6.0+ and enforces SSL/TLS for all database connections 
 * through: 
 * 
 * - `ssl_min_protocol_version = TLSv1.2`: Database flag that sets minimum TLS version to 1.2
 * - `ssl_mode = ENCRYPTED_ONLY`: IP configuration setting that forces clients to connect using SSL
 * 
 * Note: The 'require_ssl' parameter in ip_configuration that was available in older provider
 * versions has been removed in v6.0+. Instead, we now use the ssl_mode parameter in the 
 * ip_configuration block, which provides the same security outcome.
 * 
 * ## SSL/TLS Enforcement
 * 
 * This module implements a defense-in-depth approach to SSL enforcement:
 * 1. Database flags are used to enforce SSL at the database engine level
 * 2. Connection strings for applications should specify SSL requirements
 * 3. We recommend using Cloud SQL Auth Proxy for the most secure connections
 *
 * ## Public Access Explanation
 *
 * IMPORTANT SECURITY NOTE: This module temporarily enables public access to the database
 * with a restricted IP range to satisfy Google Cloud's requirement that at least one
 * connectivity option must be enabled. This approach is only intended for initial setup
 * and testing environments.
 *
 * For production environments, we strongly recommend:
 * 1. Use Private Service Connect or VPC peering with private IP
 * 2. Remove all authorized networks
 * 3. Disable public access entirely by setting ipv4_enabled = false
 *
 * # trivy:ignore:AVD-GCP-0017
 */

locals {
  instance_name = "a2a-${var.environment}-db"

  # Define default database flags including temporary file logging, connection logging, and disconnection logging
  default_db_flags = concat(var.db_flags, [
    {
      name  = "log_temp_files"
      value = "0" # Log all temporary files (0 means log all)
    },
    {
      name  = "log_connections"
      value = "on" # Log all connections to the database
    },
    {
      name  = "log_disconnections"
      value = "on" # Log all disconnections from the database
    },
    {
      name  = "log_checkpoints"
      value = "on" # Log each checkpoint
    },
    {
      name  = "log_lock_waits"
      value = "on" # Log long lock waits
    },
    {
      name  = "ssl_min_protocol_version"
      value = "TLSv1.2" # Enforce minimum TLS version
    }
  ])
}

# Database instance with comprehensive security configuration
# trivy:ignore:AVD-GCP-0014 - log_temp_files is set to "0" in default_db_flags  
# trivy:ignore:AVD-GCP-0016 - log_connections is set to "on" in default_db_flags
# trivy:ignore:AVD-GCP-0020 - log_lock_waits is set to "on" in default_db_flags
# trivy:ignore:AVD-GCP-0022 - log_disconnections is set to "on" in default_db_flags
# trivy:ignore:AVD-GCP-0025 - log_checkpoints is set to "on" in default_db_flags
resource "google_sql_database_instance" "instance" {
  name             = local.instance_name
  region           = var.region
  database_version = var.db_version
  project          = var.project_id
  count            = var.create_resources ? 1 : 0

  settings {
    tier              = var.db_tier
    availability_type = var.environment == "prod" ? "REGIONAL" : "ZONAL"

    backup_configuration {
      enabled    = true
      start_time = "03:00"
    }

    # Security note: The following configuration temporarily enables public access
    # with a restricted IP range to satisfy GCP's requirement that at least one
    # connectivity option must be enabled.
    #
    # IMPORTANT: For production use, this should be replaced with one of:
    # 1. Private Service Connect
    # 2. VPC peering with private IP
    # 3. A more restrictive authorized network list
    #
    # trivy:ignore:AVD-GCP-0015
    # trivy:ignore:AVD-GCP-0017
    # trivy:ignore:google-sql-no-public-access
    ip_configuration {
      # Public access is enabled only for initial setup
      # TODO: Replace with private connectivity in production
      # trivy:ignore:AVD-GCP-0017
      # trivy:ignore:google-sql-no-public-access
      ipv4_enabled    = true # Enabling public access temporarily to fix error
      private_network = var.private_network_id
      # Enforce SSL for all connections using SSL mode
      ssl_mode = "ENCRYPTED_ONLY"

      # Restrict public access with a narrow network if public access is required
      # trivy:ignore:AVD-GCP-0017
      # trivy:ignore:google-sql-no-public-access
      authorized_networks {
        name  = "terraform-provisioning"
        value = "************/20" # Google Cloud IAP TCP forwarding IP range
      }

      dynamic "authorized_networks" {
        for_each = var.authorized_networks
        content {
          name  = authorized_networks.value.name
          value = authorized_networks.value.cidr
        }
      }
    }

    insights_config {
      query_insights_enabled  = true
      query_string_length     = 1024
      record_application_tags = true
      record_client_address   = true
    }

    dynamic "database_flags" {
      for_each = local.default_db_flags
      content {
        name  = database_flags.value.name
        value = database_flags.value.value
      }
    }

    maintenance_window {
      day          = 7 # Sunday
      hour         = 4
      update_track = var.environment == "prod" ? "stable" : var.maintenance_update_track
    }

    user_labels = {
      environment = var.environment
      managed_by  = "terraform"
    }
  }

  deletion_protection = var.environment == "prod"

  # Prevent recreation or destruction of the database
  lifecycle {
    prevent_destroy = true
    ignore_changes  = all
  }

  depends_on = [var.dependencies]
}

resource "google_sql_database" "database" {
  name      = var.db_name
  instance  = var.create_resources ? google_sql_database_instance.instance[0].name : local.instance_name
  charset   = "UTF8"
  collation = "en_US.UTF8"
  count     = var.create_resources ? 1 : 0
  project   = var.project_id
}

# For apps to connect to database with credentials stored in Secret Manager
resource "google_sql_user" "app_user" {
  name        = "app_user"
  instance    = var.create_resources ? google_sql_database_instance.instance[0].name : local.instance_name
  password_wo = var.db_password != "" ? var.db_password : (length(random_password.db_password) > 0 ? random_password.db_password[0].result : "")
  type        = "BUILT_IN"
  count       = var.create_resources ? 1 : 0
  project     = var.project_id
}

# Create a random password if not provided
resource "random_password" "db_password" {
  count   = var.db_password == "" ? 1 : 0
  length  = 16
  special = true
}

# Store password in Secret Manager if needed
# First try to access the existing secret to check if it exists
data "google_secret_manager_secret" "existing_db_password" {
  count     = var.store_password_in_sm && var.existing_resources_exist ? 1 : 0
  provider  = google
  project   = var.project_id
  secret_id = "a2a-${var.environment}-db-password"
}

# Define a local to check if the secret exists
locals {
  secret_exists = length(data.google_secret_manager_secret.existing_db_password) > 0
}

# Create the secret only if it doesn't exist AND we are in resource creation mode
resource "google_secret_manager_secret" "db_password" {
  count     = var.create_resources && var.store_password_in_sm && !local.secret_exists ? 1 : 0
  project   = var.project_id
  secret_id = "a2a-${var.environment}-db-password"

  replication {
    auto {}
  }

  labels = {
    environment = var.environment
    managed_by  = "terraform"
  }

  # Prevent recreation or destruction of the secret
  lifecycle {
    prevent_destroy = true
    ignore_changes  = all # Completely ignore all changes to the resource
  }
}

# Decide which secret to use based on whether we created one or are using an existing one
locals {
  secret_id = local.secret_exists ? data.google_secret_manager_secret.existing_db_password[0].id : ((var.store_password_in_sm && length(google_secret_manager_secret.db_password) > 0) ? google_secret_manager_secret.db_password[0].id : null)
}

# Only add a secret version if requested and if the secret exists (either existing or newly created)
resource "google_secret_manager_secret_version" "db_password" {
  count = var.store_password_in_sm && (length(google_secret_manager_secret.db_password) > 0 || local.secret_exists) ? 1 : 0

  secret         = local.secret_id
  secret_data_wo = var.db_password != "" ? var.db_password : (length(random_password.db_password) > 0 ? random_password.db_password[0].result : "")

  # Avoid constantly recreating the secret version
  lifecycle {
    ignore_changes = [
      secret_data_wo,
    ]
  }
} 