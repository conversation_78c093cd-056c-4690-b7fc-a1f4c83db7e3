/**
 * # Database Module Outputs
 *
 * Outputs from the database module.
 */

output "instance_name" {
  description = "The name of the database instance"
  value       = local.instance_name
}

output "connection_name" {
  description = "The connection name of the database instance"
  value       = var.create_resources ? google_sql_database_instance.instance[0].connection_name : null
}

output "private_ip" {
  description = "The private IP of the database instance"
  value       = var.create_resources ? google_sql_database_instance.instance[0].private_ip_address : null
}

output "public_ip" {
  description = "The public IP of the database instance"
  value       = var.create_resources ? google_sql_database_instance.instance[0].public_ip_address : null
}

output "self_link" {
  description = "The self link of the database instance"
  value       = var.create_resources ? google_sql_database_instance.instance[0].self_link : null
}

output "database_name" {
  description = "The name of the database"
  value       = var.db_name
}

output "database_user" {
  description = "The name of the database user"
  value       = "app_user"
}

output "database_password_secret_id" {
  description = "The ID of the secret containing the database password"
  value       = var.store_password_in_sm ? "a2a-${var.environment}-db-password" : null
}

output "app_user_name" {
  description = "The name of the application user"
  value       = var.create_resources ? google_sql_user.app_user[0].name : "app_user"
}

output "database_connection_string" {
  description = "The PostgreSQL connection string (without password)"
  value       = var.create_resources ? "postgresql://${google_sql_user.app_user[0].name}@${google_sql_database_instance.instance[0].private_ip_address}/${google_sql_database.database[0].name}" : null
} 