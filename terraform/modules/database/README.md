# Database Module for A2A Platform

This module creates and manages Cloud SQL (PostgreSQL) database resources for the A2A Platform.

## Features

- Creates PostgreSQL database instances with configurable tiers and versions
- Implements security best practices including SSL enforcement
- Configurable maintenance windows and backup schedules
- Secret management for database credentials
- Built-in audit logging and query insights

## Maintenance Updates

Google Cloud provides two release channels for Cloud SQL updates:

1. **Stable track**: Receives updates after they've been validated in production by the canary track
2. **Canary track**: Receives updates sooner, providing earlier access to new features

The module configures the maintenance settings based on environment:
- Production environments use the "stable" track for maximum reliability
- Non-production environments can use either "stable" or "canary" track (configurable)

> **Note:** As of 2023, Google Cloud no longer supports the "preview" track for Cloud SQL maintenance windows. Use "canary" instead.

## Usage

```hcl
module "database" {
  source       = "./modules/database"
  project_id   = var.project_id
  region       = var.region
  environment  = terraform.workspace
  db_tier      = "db-custom-2-4096"
  db_version   = "POSTGRES_15"
  db_name      = "a2a_platform"
  
  # Use stable track for all environments
  maintenance_update_track = "stable"
  
  # Or use environment-based defaults (prod=stable, non-prod=canary)
  # maintenance_update_track = "canary"
}
```

## Variables

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| project_id | The GCP project ID | string | n/a | yes |
| region | The GCP region for the database | string | n/a | yes |
| environment | The environment (dev, staging, prod) | string | n/a | yes |
| db_tier | The machine tier for the database instance | string | n/a | yes |
| db_version | The database version | string | `"POSTGRES_15"` | no |
| db_name | The name of the database to create | string | `"a2a_platform"` | no |
| db_password | The password for the database user | string | `""` (random) | no |
| db_flags | Database flags for configuration | list(map(string)) | `[]` | no |
| public_access | Whether to enable public access to the database | bool | `false` | no |
| private_network_id | The ID of the private network for private IP | string | `""` | no |
| authorized_networks | List of authorized networks that can access the database | list(object) | `[]` | no |
| store_password_in_sm | Whether to store the password in Secret Manager | bool | `true` | no |
| maintenance_update_track | The update track for maintenance | string | `"stable"` | no | 