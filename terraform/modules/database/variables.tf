/**
 * # Database Module Variables
 *
 * Variables used by the database module.
 */

variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "region" {
  description = "The GCP region for the database"
  type        = string
}

variable "environment" {
  description = "The environment (dev, staging, prod)"
  type        = string
}

variable "db_tier" {
  description = "The machine tier for the database instance"
  type        = string
}

variable "db_version" {
  description = "The database version"
  type        = string
  default     = "POSTGRES_15"
}

variable "db_name" {
  description = "The name of the database to create"
  type        = string
  default     = "a2a_platform"
}

variable "db_password" {
  description = "The password for the database user (leave empty to generate random)"
  type        = string
  default     = ""
  sensitive   = true
}

variable "db_flags" {
  description = "Database flags for configuration"
  type        = list(map(string))
  default     = []
}

variable "public_access" {
  description = "Whether to enable public access to the database"
  type        = bool
  default     = false
}

variable "private_network_id" {
  description = "The ID of the private network for private IP"
  type        = string
  default     = ""
}

variable "authorized_networks" {
  description = "List of authorized networks that can access the database"
  type = list(object({
    name = string
    cidr = string
  }))
  default = []
}

variable "store_password_in_sm" {
  description = "Whether to store the database password in Secret Manager"
  type        = bool
  default     = true
}

variable "dependencies" {
  description = "Dependencies that should be created before database instance"
  type        = list(any)
  default     = []
}

variable "maintenance_update_track" {
  description = "The update track for maintenance. Can be 'canary' or 'stable'. Use 'canary' to receive earlier updates, 'stable' for later updates."
  type        = string
  default     = "stable"
  validation {
    condition     = contains(["canary", "stable"], var.maintenance_update_track)
    error_message = "The maintenance_update_track must be either 'canary' or 'stable'."
  }
}

variable "create_resources" {
  description = "Whether to create new resources or use existing ones"
  type        = bool
  default     = true
}

variable "existing_resources_exist" {
  description = "Whether existing resources exist that can be referenced"
  type        = bool
  default     = false
} 