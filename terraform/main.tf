/**
 * # Vedavivi A2A Platform Infrastructure
 *
 * This Terraform configuration manages the infrastructure for the Vedavivi A2A Platform.
 */

# Configure terraform for GCP
terraform {
  # Backend configuration is defined externally to allow for overrides
  # in CI/CD workflows via -backend-config parameters
  # Backend configuration is moved to backend.tf

  # Define required providers
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 6.35.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = "~> 6.35.0"
    }
  }

  # Define required Terraform version
  required_version = ">= 1.12.0"
}

# Define local values
locals {
  # Determine machine type based on environment
  machine_type = var.machine_type != null ? var.machine_type : lookup(var.machine_types, terraform.workspace, "e2-medium")

  # Network configuration
  subnet_cidr = "10.0.0.0/24"

  # Firewall rules
  firewall_rules = [
    {
      name          = "a2a-${terraform.workspace}-allow-http"
      description   = "Allow HTTP traffic"
      direction     = "INGRESS"
      priority      = 1000
      source_ranges = ["0.0.0.0/0"]
      allow = [
        {
          protocol = "tcp"
          ports    = ["80", "443", "8080"]
        }
      ]
    },
    {
      name          = "a2a-${terraform.workspace}-allow-https"
      description   = "Allow HTTPS traffic"
      direction     = "INGRESS"
      priority      = 900
      source_ranges = ["0.0.0.0/0"]
      allow = [
        {
          protocol = "tcp"
          ports    = ["443"]
        }
      ]
    },
    {
      name          = "a2a-${terraform.workspace}-allow-ssh"
      description   = "Allow SSH traffic"
      direction     = "INGRESS"
      priority      = 1001
      source_ranges = ["0.0.0.0/0"]
      allow = [
        {
          protocol = "tcp"
          ports    = ["22"]
        }
      ]
    }
  ]
  skip_data_source_errors = true # Added to allow override
}

# Configure the Google Cloud provider
provider "google" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}

provider "google-beta" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}

# GitHub Actions OIDC Authentication
module "github_oidc" {
  source                   = "./modules/oidc"
  project_id               = var.project_id
  project_number           = var.project_number
  service_account_id       = "github-actions"
  github_repo              = "blkops-collective/a2a-platform"
  create_resources         = var.create_resources
  existing_resources_exist = var.existing_resources_exist
}

# Load modules based on environment
module "compute" {
  source                   = "./modules/compute"
  project_id               = var.project_id
  region                   = var.region
  zone                     = var.zone
  environment              = terraform.workspace
  machine_type             = local.machine_type
  network_name             = module.network.network_name
  subnet_name              = module.network.subnet_name
  service_account_email    = module.github_oidc.service_account_email
  min_instances            = lookup(var.min_instances, terraform.workspace, 1)
  max_instances            = lookup(var.max_instances, terraform.workspace, 3)
  create_resources         = var.create_resources
  existing_resources_exist = var.existing_resources_exist
}

module "database" {
  source                   = "./modules/database"
  project_id               = var.project_id
  region                   = var.region
  environment              = terraform.workspace
  db_tier                  = var.database_tier != null ? var.database_tier : lookup(var.db_tiers, terraform.workspace, "db-f1-micro")
  db_version               = var.db_version
  db_name                  = var.db_name
  create_resources         = var.create_resources
  existing_resources_exist = var.existing_resources_exist
  depends_on               = [module.network]
}

module "storage" {
  source                   = "./modules/storage"
  project_id               = var.project_id
  region                   = var.region
  environment              = terraform.workspace
  name_prefix              = "a2a-${terraform.workspace}"
  use_cmek                 = true
  safe_cmek                = var.safe_cmek
  create_resources         = var.create_resources
  module_create_resources  = var.storage_create_resources
  existing_resources_exist = var.existing_resources_exist
}

module "network" {
  source                   = "./modules/network"
  project_id               = var.project_id
  network_name             = "a2a-${terraform.workspace}-network"
  subnet_name              = "a2a-${terraform.workspace}-subnet"
  subnet_cidr              = local.subnet_cidr
  region                   = var.region
  firewall_rules           = local.firewall_rules
  create_resources         = var.create_resources
  existing_resources_exist = var.existing_resources_exist
}

module "pubsub" {
  source      = "./modules/pubsub"
  project_id  = var.project_id
  region      = var.region
  environment = terraform.workspace
}

# Redis module for message broking and caching
module "redis" {
  source                   = "./modules/redis"
  project_id               = var.project_id
  region                   = var.region
  environment              = terraform.workspace
  memory_size_gb           = lookup(var.redis_memory_size, terraform.workspace, 1)
  redis_version            = "REDIS_6_X"
  network_name             = module.network.network_name
  tier                     = terraform.workspace == "prod" ? "STANDARD_HA" : "BASIC"
  kms_key_name             = module.storage.kms_key_id
  create_resources         = var.create_resources
  existing_resources_exist = var.existing_resources_exist
  depends_on               = [module.network, module.storage]
}

# Artifact Registry for container images
module "artifact_registry" {
  source                   = "./modules/artifact-registry"
  project_id               = var.project_id
  region                   = var.region
  environment              = terraform.workspace
  service_account_email    = module.github_oidc.service_account_email
  compute_service_account  = module.compute.service_account_email
  create_resources         = var.artifact_registry_create ? true : var.create_resources
  existing_resources_exist = var.existing_resources_exist
  depends_on               = [module.github_oidc, module.compute]
}

# Note: GCS Static Hosting has been replaced with Cloudflare R2 storage
# See environment-specific configurations in terraform/environments/