# Terraform Testing Guide

This document provides guidance on running and troubleshooting Terraform tests for this project.

## Testing Modes

Our testing framework supports two primary modes:

1. **Mock Mode** (Safe) - Does not create real infrastructure, tests structure and validation only
2. **Integration Mode** (Use with caution) - Creates real infrastructure in GCP, requires credentials

## Running Tests

### Prerequisites

- Go installed (1.16+)
- Terraform installed (1.12+)
- For integration tests: GCP credentials configured

### Basic Usage

Run tests in mock mode (safe, no real infrastructure):

```bash
./scripts/run_tests.sh --mock
```

Run tests in integration mode (creates real resources in GCP):

```bash
# First authenticate with GCP
gcloud auth application-default login

# Then run integration tests
./scripts/run_tests.sh --integration
```

Run with debug output:

```bash
./scripts/run_tests.sh --mock --debug
```

## Troubleshooting Common Issues

### OAuth Errors

Error: `oauth2: "invalid_grant" "reauth related error (invalid_rapt)"`

This occurs when the test is trying to create real GCP resources without proper authentication. Ensure:

1. You're running in mock mode if you don't intend to create real resources:
   ```bash
   ./scripts/run_tests.sh --mock
   ```

2. The `TERRATEST_MOCK` environment variable is set to `true`

3. The test functions are properly checking for mock mode:
   ```go
   if os.Getenv("TERRATEST_MOCK") == "true" {
     t.Skip("Skipping real infrastructure test in mock mode")
   }
   ```

### Missing Backend Configuration

Error: `Backend block not found` or `"" does not contain "bucket = \"vedavivi-tf-state-a2a\""`

This happens when the backend.tf file is missing or doesn't contain the expected configuration.

Fix by running:

```bash
./scripts/setup-backend.sh --ci
```

### Module Not Found

Error: `Could not find module X`

This usually indicates a path issue in the test. Verify:

1. The module path is correct
2. The module directory exists
3. You're running the tests from the right directory

## CI/CD Testing

In GitHub Actions, we run tests in mock mode to avoid creating actual infrastructure:

```yaml
- name: Run Mock Tests
  run: |
    echo "Running mock tests (safe mode, no real infrastructure creation)"
    ./terraform/scripts/run_tests.sh --mock
```

## Adding New Tests

When adding new tests:

1. Always implement proper mock mode checking:

```go
func TestMyNewModule(t *testing.T) {
    // Skip this test in mock mode
    if os.Getenv("TERRATEST_MOCK") == "true" {
        t.Skip("Skipping real infrastructure test in mock mode")
        return
    }
    
    // Proceed with real infrastructure testing
    // ...
}
```

2. Use the `CreateTestAndCleanup` utility for consistent setup/teardown:

```go
moduleDir := "../modules/mymodule"
if !CreateTestAndCleanup(t, moduleDir) {
    return // Test was skipped in mock mode
}
```

## Test Structure

- `test_helpers.go` - Common utility functions
- `utils_test.go` - Test setup and helper functions
- `mock_test.go` - Tests that don't require real infrastructure
- Module-specific tests (e.g., `network_test.go`, `cloudsql_test.go`) - Create real infrastructure if not in mock mode 