# Terraform Environment Variables

This document describes the environment variables that can be used to control Terraform behavior for DNS and page rule creation.

## 🎛️ Control Variables

### `TF_VAR_create_dns_records`

Controls whether Terraform creates DNS records in Cloudflare.

**Values:**
- `true`: Create new DNS records
- `false`: Skip DNS record creation (use for importing existing records)

**Default Values:**
- **Staging**: `true` (creates new records)
- **Production**: `false` (imports existing records)

### `TF_VAR_create_page_rules`

Controls whether Terraform creates page rules in Cloudflare.

**Values:**
- `true`: Create new page rules
- `false`: Skip page rule creation (use for importing existing rules)

**Default Values:**
- **Staging**: `false` (imports existing rules)
- **Production**: `false` (imports existing rules)

### `TF_VAR_create_workers`

Controls whether Terraform creates Cloudflare Workers for SPA routing.

**Values:**
- `true`: Create new Cloudflare Workers and routes
- `false`: Skip worker creation (use for importing existing workers or if not needed)

**Default Values:**
- **All Environments**: `false` (skips worker creation to avoid conflicts)

## 🔐 Authentication Variables

### `TF_VAR_cdn_service_account_email`

**Required for CI/CD pipelines.** Specifies the service account email that Cloudflare uses to access GCS buckets.

**Format:** Must be a valid email address (validated by regex)
**Example:** `<EMAIL>`

**Source:** Typically sourced from `GCP_SERVICE_ACCOUNT` GitHub Actions secret

**Validation:**
- Must match email format: `<EMAIL>`
- Cannot be null or empty
- Validated during `terraform plan/validate`

**Usage in GitHub Actions:**
```yaml
env:
  TF_VAR_cdn_service_account_email: ${{ secrets.GCP_SERVICE_ACCOUNT }}
```

**Error Prevention:**
- Catches typos in service account emails early
- Prevents IAM binding failures during deployment
- Provides clear error messages with expected format

## 📋 Usage Examples

### Create DNS Records, Page Rules, and Workers
```bash
export TF_VAR_create_dns_records=true
export TF_VAR_create_page_rules=true
export TF_VAR_create_workers=true
terraform plan
terraform apply
```

### Import Existing Resources (Skip Creation)
```bash
export TF_VAR_create_dns_records=false
export TF_VAR_create_page_rules=false
export TF_VAR_create_workers=false
terraform plan
terraform apply
```

### Create DNS Records Only (Import Page Rules and Workers)
```bash
export TF_VAR_create_dns_records=true
export TF_VAR_create_page_rules=false
export TF_VAR_create_workers=false
terraform plan
terraform apply
```

### GitHub Actions Usage

GitHub Actions workflows pull values from GitHub Actions secrets with sensible defaults:

```yaml
env:
  # Resource creation control from GitHub Actions secrets mapped directly to TF_VAR
  TF_VAR_create_dns_records: ${{ secrets.CREATE_DNS_RECORDS || 'true' }}   # Default: create DNS records
  TF_VAR_create_page_rules: ${{ secrets.CREATE_PAGE_RULES || 'false' }}    # Default: import existing page rules
  TF_VAR_create_workers: ${{ secrets.CREATE_WORKERS || 'false' }}          # Default: import existing workers
  # Authentication variables (required)
  TF_VAR_cdn_service_account_email: ${{ secrets.GCP_SERVICE_ACCOUNT }}     # Required: CDN service account email
```

**GitHub Actions Secrets Configuration:**

Set these secrets in your GitHub repository:
- **Repository → Settings → Secrets and variables → Actions → Repository secrets**

| Secret Name | Recommended Value | Description |
|-------------|-------------------|-------------|
| `CREATE_DNS_RECORDS` | `"true"` for staging, `"false"` for production | Controls DNS record creation |
| `CREATE_PAGE_RULES` | `"false"` | Controls page rule creation (usually import existing) |
| `CREATE_WORKERS` | `"false"` | Controls Cloudflare Workers creation (usually import existing) |

**Environment-Specific Fallbacks:**
```yaml
# Manual deployment with environment-specific fallbacks
TF_VAR_create_dns_records: ${{ secrets.CREATE_DNS_RECORDS || (github.event.inputs.environment == 'production' && 'false' || 'true') }}
TF_VAR_create_page_rules: ${{ secrets.CREATE_PAGE_RULES || 'false' }}
TF_VAR_create_workers: ${{ secrets.CREATE_WORKERS || 'false' }}
```

## 🔧 Common Scenarios

### New Environment Setup
```bash
# Create all resources from scratch
export TF_VAR_create_dns_records=true
export TF_VAR_create_page_rules=true
export TF_VAR_create_workers=true
```

### Existing Environment (Import Mode)
```bash
# Import existing resources
export TF_VAR_create_dns_records=false
export TF_VAR_create_page_rules=false
export TF_VAR_create_workers=false
```

### Staging Environment (Fresh Setup)
```bash
# Create DNS records, import page rules
export TF_VAR_create_dns_records=true
export TF_VAR_create_page_rules=false
```

### Production Environment (Import Existing)
```bash
# Import both DNS records and page rules
export TF_VAR_create_dns_records=false
export TF_VAR_create_page_rules=false
```

## 🔧 GitHub Actions Secrets Setup

### Setting Up Repository Secrets

1. **Navigate to Repository Settings**:
   ```
   Repository → Settings → Secrets and variables → Actions → Repository secrets
   ```

2. **Add New Repository Secrets**:
   - Click "New repository secret"
   - Add the following secrets:

   | Secret Name | Value | When to Use |
   |-------------|-------|-------------|
   | `CREATE_DNS_RECORDS` | `"true"` | For new environments or when DNS records need to be created |
   | `CREATE_DNS_RECORDS` | `"false"` | For existing environments where DNS records should be imported |
   | `CREATE_PAGE_RULES` | `"true"` | When creating new page rules from scratch |
   | `CREATE_PAGE_RULES` | `"false"` | When importing existing page rules (recommended) |
   | `CREATE_WORKERS` | `"true"` | When creating new Cloudflare Workers from scratch |
   | `CREATE_WORKERS` | `"false"` | When importing existing workers or not using workers (recommended) |
   | `GCP_SERVICE_ACCOUNT` | `<EMAIL>` | **Required**: Service account email for CDN access to GCS |

### Environment-Specific Configuration

For different behavior per environment, you can use environment-specific secrets:

1. **Environment Secrets** (Repository → Settings → Environments → [environment] → Add secret):
   - `staging` environment: `CREATE_DNS_RECORDS = "true"`
   - `production` environment: `CREATE_DNS_RECORDS = "false"`

2. **Or use conditional logic** in workflows (current implementation):
   - Secrets take precedence over conditional defaults
   - Fallback logic handles missing secrets gracefully

## ⚠️ Important Notes

1. **DNS Records**: If set to `false`, existing DNS records must be imported manually
2. **Page Rules**: If set to `false`, existing page rules must be imported manually
3. **Workers**: If set to `false`, existing Cloudflare Workers must be imported manually or are not needed
4. **Conflicts**: Setting to `true` when resources already exist will cause errors
4. **GitHub Actions Secrets**: Take precedence over hardcoded defaults in workflows
5. **Environment Defaults**: Sensible fallbacks are provided when secrets are not configured
6. **GCP_SERVICE_ACCOUNT**: **Required for CI/CD** - Must be a valid email format or deployment will fail
7. **Email Validation**: Service account emails are validated during `terraform plan` to catch typos early

## 🔗 Related Documentation

- [Import Guide](modules/cloudflare-cdn/IMPORT_GUIDE.md)
- [Import Scripts](scripts/import_cloudflare_resources.sh)
- [CDN Module Documentation](modules/cloudflare-cdn/README.md)
