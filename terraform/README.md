# A2A Platform Infrastructure

This directory contains the Terraform configurations for managing the infrastructure of the A2A Platform on Google Cloud Platform (GCP).

## Overview

The infrastructure is designed to support:
- Isolated environments (dev, staging, production)
- Automated deployments and validation via GitHub Actions
- Secure management of credentials using Workload Identity Federation
- Reliable storage of Terraform state in Google Cloud Storage

## Prerequisites

- [Terraform](https://www.terraform.io/downloads.html) ≥ 1.0.0
- [Google Cloud SDK](https://cloud.google.com/sdk/docs/install)
- Service Account with appropriate permissions

## Directory Structure

```
terraform/
├── main.tf                  # Main Terraform configuration
├── variables.tf             # Input variables
├── outputs.tf               # Output variables
├── environments/            # Environment-specific variables
│   ├── dev/
│   │   └── terraform.tfvars # Dev environment values
│   ├── staging/
│   │   └── terraform.tfvars # Staging environment values
│   └── production/
│       └── terraform.tfvars # Production environment values
└── modules/                 # Reusable Terraform modules
    ├── compute/             # Compute Engine resources
    ├── database/            # Cloud SQL resources
    ├── storage/             # Cloud Storage resources
    ├── networking/          # VPC and networking resources
    └── pubsub/              # Pub/Sub resources
```

## Initial Setup

### 1. GCP Project and State Storage

```bash
# Log in to Google Cloud
gcloud auth login

# Set the active project
gcloud config set project YOUR_PROJECT_ID

# Create a GCS bucket for Terraform state
gsutil mb -l us-central1 gs://vedavivi-tf-state-unique/ # Replace with your bucket name
gsutil versioning set on gs://vedavivi-tf-state-unique/

# Enable required GCP APIs
gcloud services enable compute.googleapis.com
gcloud services enable servicenetworking.googleapis.com
gcloud services enable sqladmin.googleapis.com
gcloud services enable secretmanager.googleapis.com
gcloud services enable iam.googleapis.com
```

### 2. Workload Identity Federation

Follow these steps to set up Workload Identity Federation for GitHub Actions:

**Option 1: Use the automated setup script (recommended):**

```bash
# Run the setup script
cd terraform
./scripts/setup_github_workload_identity.sh -p YOUR_PROJECT_ID -g YOUR_GITHUB_ORG/YOUR_REPO

# See detailed instructions
cat docs/workload_identity_setup.md
```

**Option 2: Manual setup:**

```bash
# Create a Workload Identity Pool
gcloud iam workload-identity-pools create "github-pool" \
  --project="YOUR_PROJECT_ID" \
  --location="global" \
  --display-name="GitHub Actions Pool"

# Create a Workload Identity Provider
gcloud iam workload-identity-pools providers create-oidc "github-provider" \
  --project="YOUR_PROJECT_ID" \
  --location="global" \
  --workload-identity-pool="github-pool" \
  --display-name="GitHub Actions Provider" \
  --attribute-mapping="google.subject=assertion.sub,attribute.actor=assertion.actor,attribute.repository=assertion.repository" \
  --issuer-uri="https://token.actions.githubusercontent.com"

# Create a Service Account for GitHub Actions
gcloud iam service-accounts create "github-actions" \
  --project="YOUR_PROJECT_ID" \
  --display-name="GitHub Actions Service Account"

# Grant necessary roles to the Service Account
gcloud projects add-iam-policy-binding "YOUR_PROJECT_ID" \
  --member="serviceAccount:github-actions@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/editor"

# Allow the Workload Identity Provider to impersonate the Service Account
gcloud iam service-accounts add-iam-policy-binding "github-actions@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
  --project="YOUR_PROJECT_ID" \
  --role="roles/iam.workloadIdentityUser" \
  --member="principalSet://iam.googleapis.com/projects/YOUR_PROJECT_NUMBER/locations/global/workloadIdentityPools/github-pool/attribute.repository/YOUR_GITHUB_ORG/YOUR_REPO"
```

### Required Service Account Permissions

The service account used by Terraform (either locally or via GitHub Actions) needs the following IAM roles:

```bash
# Run the permission setup script
./scripts/fix_sa_permissions.sh SERVICE_ACCOUNT_EMAIL PROJECT_ID
```

This will grant the following roles needed for Terraform operations:

- `roles/editor` (or more granular roles for production use)
- `roles/cloudsql.admin` (for Cloud SQL operations)
- `roles/secretmanager.admin` (for managing secrets)
- `roles/compute.admin` (for Compute Engine operations)
- `roles/storage.admin` (for GCS bucket operations)
- `roles/pubsub.admin` (for Pub/Sub operations)
- `roles/iam.serviceAccountUser` (for service account impersonation)
- `roles/iam.serviceAccountAdmin` (for managing service accounts)
- `roles/iam.workloadIdentityPoolAdmin` (for creating and managing Workload Identity Pools)
- `roles/iam.securityAdmin` (for managing IAM policies)
- `roles/compute.networkAdmin` (for network operations)
- `roles/cloudkms.admin` (for Cloud KMS operations)
- `roles/resourcemanager.projectIamAdmin` (for managing IAM policies)

If you encounter permission errors like the following:
- `Error 403: The client is not authorized to make this request`
- `Permission 'secretmanager.secrets.create' denied for resource 'projects/***'`

Run the `fix_sa_permissions.sh` script to grant the necessary permissions.

### 3. GitHub Repository Secrets

Add the following secrets to your GitHub repository:

- `GCP_PROJECT_ID`: Your GCP project ID
- `GCP_TF_STATE_BUCKET`: Name of the GCS bucket for Terraform state
- `GCP_SA_EMAIL`: Service account email for GitHub Actions
- `WIF_PROVIDER`: Workload Identity Provider resource name

### 4. GitHub Environments

Create the following GitHub environments:
- `staging`: For automatic deployments from the `main` branch
- `production`: For manual production deployments (with required approvals)
- `dev` (optional): For feature branch deployments

## Local Development

### Initialize Terraform

```bash
cd terraform
# Use the backend switching script (recommended)
./scripts/switch_backend.sh local
terraform init -reconfigure

# OR manually set up local backend
cp backend.tf.local backend.tf
terraform init -reconfigure

# OR use remote backend if you need to collaborate on state
./scripts/switch_backend.sh gcs YOUR_STATE_BUCKET terraform/state/dev
terraform init -reconfigure
```

### Select Workspace

```bash
# Create and/or select the dev workspace
terraform workspace select dev || terraform workspace new dev
```

### Plan and Apply

```bash
# Generate execution plan
terraform plan -var-file=environments/dev/terraform.tfvars -out=plan.out

# Apply changes
terraform apply plan.out
```

## GitHub Actions Workflow

The repository includes multiple GitHub Actions workflows for Terraform management:

### 1. Terraform Module Tests (`.github/workflows/terraform-tests.yml`)
- Runs on PRs and pushes to `main` that modify Terraform files
- Performs syntax validation, formatting checks, and security scanning using Trivy
- Executes mock tests to validate module functionality
- Automatically deploys to GCP when PRs are merged to `main`

### 2. Manual Terraform Deploy (`.github/workflows/terraform-manual-deploy.yml`)
- Triggered manually from the GitHub UI
- Allows deployment to any environment (dev/staging/production)
- Provides safety controls:
  - Requires explicit "YES" confirmation
  - Shows plan summary before applying
  - Uses environment-specific credentials
- Usage instructions:
  1. Go to the "Actions" tab in GitHub
  2. Select "Manual Terraform Deploy" workflow
  3. Click "Run workflow"
  4. Select environment from dropdown (dev/staging/production)
  5. Type "YES" in the confirmation field
  6. Click "Run workflow" to start deployment
  7. Review the plan output before it automatically applies

### 3. Drift Detection
- Scheduled checks to detect infrastructure drift
- Notifies team when actual infrastructure differs from code

### Resource Conflict Errors

If you encounter errors like "The resource already exists" (Error 409), try one of these approaches:

1. **Use the GitHub Actions Import Workflow:**
   We've created a specialized workflow for importing existing resources:
   1. Run the configuration script:
      ```bash
      ./terraform/scripts/configure_gha_for_existing_resources.sh
      ```
   2. Commit and push the changes (creates `terraform-import.yml`)
   3. Run the "Import Existing Terraform Resources" workflow from GitHub Actions UI
   4. Then run your regular deployment workflow
   
   This workflow will:
   - Try to import all existing resources into Terraform state
   - Configure the main workflow to skip creation of resources that already exist
   - All steps use `continue-on-error: true` to ensure the workflow completes even if some imports fail

2. **Use the import script to import multiple resources at once:**
   ```bash
   # Make the script executable
   chmod +x terraform/scripts/import_existing_resources.sh
   
   # Run the script and follow the prompts
   ./terraform/scripts/import_existing_resources.sh
   
   # Or specify project and environment directly
   ./terraform/scripts/import_existing_resources.sh YOUR_PROJECT_ID dev
   ```
   This script will attempt to import all commonly conflicting resources in one operation.

3. **Import specific resources manually:**
   ```bash
   # For health checks
   terraform import module.compute.google_compute_health_check.default projects/YOUR_PROJECT_ID/global/healthChecks/a2a-dev-vm-health-check
   
   # For service accounts
   terraform import module.github_oidc.google_service_account.github_actions projects/YOUR_PROJECT_ID/serviceAccounts/github-actions@YOUR_PROJECT_ID.iam.gserviceaccount.com
   
   # For networks
   terraform import module.network.google_compute_network.network projects/YOUR_PROJECT_ID/global/networks/a2a-dev-network
   ```

4. **Use create_resources=false to skip creation:**
   When calling workflows, you can set the `create_resources` environment variable to false:
   ```bash
   TF_VAR_create_resources=false terraform apply
   ```
   
5. **Use targeting to apply only specific resources:**
   ```bash
   # Generate a plan for only specific resources
   terraform plan -target=module.compute.google_compute_instance_template.default
   
   # Apply only specific resources
   terraform apply -target=module.compute.google_compute_instance_template.default
   ```

## Adding Resources

When adding new GCP resources:

1. Create resource definitions in appropriate modules
2. Update module parameters in `main.tf`
3. Add necessary variables to `variables.tf`
4. Update environment-specific values in `environments/*/terraform.tfvars`
5. Document any new resources in this README

## Best Practices

- Use descriptive naming conventions for resources
- Keep modules small and focused on a single responsibility
- Use remote state for sensitive outputs
- Enable versioning for state storage
- Implement least privilege for service accounts
- Regularly check for drift between code and actual infrastructure

## Troubleshooting

If you encounter issues:

1. Ensure you have the correct permissions on GCP
   - If you see "Permission 'iam.workloadIdentityPools.create' denied", run the fix_sa_permissions.sh script to add the required IAM admin roles
   - For other permission errors, check the error message for the specific permission needed and add it using `gcloud projects add-iam-policy-binding`
2. Verify GitHub repository secrets are set correctly
3. Check Terraform state is accessible and not corrupted
4. Review GitHub Actions workflow logs for any errors

### Cloud KMS Permission Errors

If you encounter the error "Permission denied on Cloud KMS key", this indicates that the Cloud Storage service account doesn't have permission to use your KMS keys:

1. **Run the permission fix script** which adds the necessary KMS permissions:
   ```bash
   ./terraform/scripts/fix_sa_permissions.sh YOUR_PROJECT_ID
   ```

2. **Disable CMEK temporarily** by setting the `safe_cmek` parameter to `false`:
   ```bash
   # This is already set in main.tf but you can override it
   TF_VAR_safe_cmek=false terraform apply
   ```

3. **Grant permissions manually** if the script fails:
   ```bash
   # Get the Cloud Storage service account
   STORAGE_SA=$(gcloud storage service-agent --project=YOUR_PROJECT_ID --format='value(email)')
   
   # Grant the encryption/decryption role
   gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
     --member="serviceAccount:$STORAGE_SA" \
     --role="roles/cloudkms.cryptoKeyEncrypterDecrypter"
   ```

### Common Issues and Solutions

#### Terraform Init Errors with Parallelism Flag

If you see an error like this:
```
TF_CLI_ARGS value: "-parallelism=1"
CLI command args: []string{"init", "-parallelism=1", "-reconfigure"}
Error: Failed to parse command-line flags
flag provided but not defined: -parallelism
```

This occurs because the `-parallelism` flag is not supported by the `terraform init` command, only by commands like `plan` and `apply`.

**Solution:**
1. **Never use global TF_CLI_ARGS** for flags that aren't supported by all commands
2. Add flags directly to specific commands instead:
   ```bash
   # Good:
   terraform plan -parallelism=1 -var="..."
   terraform apply -parallelism=1 -auto-approve
   
   # Bad:
   export TF_CLI_ARGS="-parallelism=1"
   terraform init  # This will fail
   ```
3. To fix existing workflows, run:
   ```bash
   ./terraform/scripts/configure_gha_for_existing_resources.sh
   ```
   This will update all workflows to use correct command-specific flags. 

## Resource Creation Strategy (create_resources and existing_resources_exist)

The Terraform configurations in this repository utilize two primary boolean variables to manage how resources are handled:

- `create_resources`: This variable (defaulting to `false` in `variables.tf`) dictates whether Terraform should attempt to create new infrastructure resources. When set to `false`, Terraform will generally rely on data sources to find and use pre-existing resources.
- `existing_resources_exist`: This variable (defaulting to `false` in `variables.tf` but set to `true` in CI/CD workflows like GitHub Actions) informs Terraform whether the resources it might try to find via data sources (when `create_resources` is `false`) are actually expected to be present. If `create_resources` is `false` and `existing_resources_exist` is `true`, data sources will be active. If `existing_resources_exist` is `false`, data sources will typically be disabled (count = 0).

**In CI/CD (GitHub Actions):**

- Typically, for environments like `staging` and `production`, the workflow sets:
  - `TF_VAR_create_resources=false`
  - `TF_VAR_existing_resources_exist=true`
- This strategy ensures that the CI/CD pipeline attempts to manage and update existing infrastructure rather than creating it from scratch, preventing accidental resource duplication or errors if foundational resources are already in place.

**Local Development:**

- When running `terraform plan` or `apply` locally without these `TF_VAR_` overrides:
  - `create_resources` will default to `false`.
  - `existing_resources_exist` will default to `false`.
- This means Terraform will attempt to read data for existing resources (due to `create_resources=false`) but the data sources themselves might be disabled if their `count` depends on `existing_resources_exist` being true. To effectively plan against an existing environment locally when `create_resources` is `false`, you would typically need to pass `-var="existing_resources_exist=true"`.
- If you intend to create resources from scratch locally (e.g., for a new `dev` environment not yet in the state backend), you would pass `-var="create_resources=true"` and `-var="existing_resources_exist=false"`.

This dual-variable approach provides flexibility for both managing established environments and bootstrapping new ones. 