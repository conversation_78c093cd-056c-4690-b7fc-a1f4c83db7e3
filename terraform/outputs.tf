/**
 * # Terraform Outputs
 *
 * This file defines all outputs from the Terraform configuration.
 */

output "environment" {
  description = "The current environment (workspace)"
  value       = terraform.workspace
}

# GitHub OIDC outputs
output "github_service_account_email" {
  description = "The email of the service account for GitHub Actions"
  value       = module.github_oidc.service_account_email
}

output "github_workload_identity_provider" {
  description = "The Workload Identity Provider for GitHub Actions"
  value       = module.github_oidc.workload_identity_provider
}

# Consolidated GitHub OIDC output
output "github_oidc" {
  description = "All GitHub OIDC provider outputs"
  value = {
    service_account_email      = module.github_oidc.service_account_email
    workload_identity_provider = module.github_oidc.workload_identity_provider
  }
}

# Compute outputs
output "compute_instance_ids" {
  description = "The IDs of the compute instances"
  value       = module.compute.instance_ids
}

output "compute_instance_names" {
  description = "The names of the compute instances"
  value       = module.compute.instance_names
}

output "compute_instance_ips" {
  description = "The IPs of the compute instances"
  value       = module.compute.instance_ips
}

# Database outputs
output "database_instance_name" {
  description = "The name of the database instance"
  value       = module.database.instance_name
}

output "database_connection_name" {
  description = "The connection name of the database instance"
  value       = module.database.connection_name
}

output "database_private_ip" {
  description = "The private IP of the database instance"
  value       = module.database.private_ip
}

# Storage outputs
output "storage_bucket_name" {
  description = "The name of the storage bucket"
  value       = module.storage.bucket_name
}

output "storage_bucket_url" {
  description = "The URL of the storage bucket"
  value       = module.storage.bucket_url
}

# Network outputs
output "network_name" {
  description = "The name of the VPC network"
  value       = module.network.network_name
}

# PubSub outputs
output "pubsub_topic_id" {
  description = "The ID of the PubSub topic"
  value       = module.pubsub.topic_id
}

# Redis outputs
output "redis_host" {
  description = "The hostname or IP address of the Redis instance"
  value       = module.redis.redis_host
  sensitive   = true
}

output "redis_url" {
  description = "The Redis connection URL"
  value       = module.redis.connection_string
  sensitive   = true
}

output "redis_host_secret_id" {
  description = "The Secret Manager secret ID for the Redis host"
  value       = module.redis.redis_host_secret_id
}

output "redis_url_secret_id" {
  description = "The Secret Manager secret ID for the Redis URL"
  value       = module.redis.redis_url_secret_id
}

# Artifact Registry outputs
output "artifact_registry_url" {
  description = "The URL of the Artifact Registry repository"
  value       = module.artifact_registry.repository_url
}

output "artifact_registry_repository_id" {
  description = "The ID of the Artifact Registry repository"
  value       = module.artifact_registry.repository_id
}

# Cloud Run Services outputs
output "api_service_url" {
  description = "The URL of the API service"
  value       = module.compute.api_service_url
}

output "api_service_name" {
  description = "The name of the API service"
  value       = module.compute.api_service_name
}

output "workers_service_name" {
  description = "The name of the Workers service"
  value       = module.compute.workers_service_name
}

# Messaging service output removed - service has been decommissioned

output "vpc_connector_name" {
  description = "The name of the VPC connector"
  value       = module.compute.vpc_connector_name
}

# Uncomment when GCS module is used
# output "gcs_bucket_name" {
#   description = "The name of the GCS bucket created"
#   value       = module.gcs.bucket_name
# }

# Uncomment when CloudSQL module is used
# output "cloudsql_instance_name" {
#   description = "The name of the Cloud SQL instance created"
#   value       = module.cloudsql.instance_name
# }
# 
# output "cloudsql_connection_name" {
#   description = "The connection name of the Cloud SQL instance created"
#   value       = module.cloudsql.connection_name
# } 