#!/bin/bash
# <PERSON>ript to set up OIDC with local JWKS for GCP Workload Identity Federation

set -e

# Check for required dependencies
if ! command -v python3 &>/dev/null; then
    echo "Python 3 is required but not installed. Please install Python 3."
    exit 1
fi

if ! python3 -c "import cryptography" &>/dev/null; then
    echo "Python cryptography package is required but not installed."
    echo "Install it with: pip install cryptography"
    exit 1
fi

# Define directories
SCRIPT_DIR="$(dirname "$(realpath "$0")")"
KEYS_DIR="${SCRIPT_DIR}/oidc/keys"

# Parse command line arguments
PROJECT_ID=""
GITHUB_REPO=""

print_usage() {
    echo "Usage: $0 --project-id=PROJECT_ID --github-repo=OWNER/REPO"
    echo
    echo "Options:"
    echo "  --project-id=ID     Your GCP project ID"
    echo "  --github-repo=REPO  Your GitHub repository in format owner/repo"
    echo "  --help              Display this help message"
}

for arg in "$@"; do
    case $arg in
        --project-id=*)
            PROJECT_ID="${arg#*=}"
            shift
            ;;
        --github-repo=*)
            GITHUB_REPO="${arg#*=}"
            shift
            ;;
        --help)
            print_usage
            exit 0
            ;;
        *)
            echo "Unknown option: $arg"
            print_usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [ -z "$PROJECT_ID" ]; then
    echo "Error: --project-id is required"
    print_usage
    exit 1
fi

if [ -z "$GITHUB_REPO" ]; then
    echo "Error: --github-repo is required"
    print_usage
    exit 1
fi

# Generate JWKS using the Python script
echo "Generating JWKS for OIDC authentication..."
"${SCRIPT_DIR}/generate_jwks.py" --output-dir="${KEYS_DIR}"

echo "Getting project number for $PROJECT_ID..."
PROJECT_NUMBER=$(gcloud projects describe "$PROJECT_ID" --format="value(projectNumber)")

# Create a Terraform variables file for the OIDC provider
cat > "${SCRIPT_DIR}/oidc/terraform.tfvars" << EOF
project_id     = "${PROJECT_ID}"
project_number = "${PROJECT_NUMBER}"
github_repo    = "${GITHUB_REPO}"
jwks_json_path = "${KEYS_DIR}/jwks.json"
EOF

echo "==============================================================="
echo "OIDC Configuration Complete"
echo "==============================================================="
echo "JWKS file: ${KEYS_DIR}/jwks.json"
echo "Terraform variables: ${SCRIPT_DIR}/oidc/terraform.tfvars"
echo
echo "Next steps:"
echo "1. Make sure the terraform/modules/oidc module files are created"
echo "2. Add the github_oidc module to your main.tf"
echo "3. Run terraform plan/apply to create the OIDC provider"
echo "4. Update your GitHub Actions workflow to use the generated provider"
echo "===============================================================" 