#!/bin/bash
# remove_from_state.sh - Remove existing resources from Terraform state
#
# Usage: ./remove_from_state.sh [ENVIRONMENT]
#
# This script removes resources from Terraform state that already exist in GCP
# and can't be recreated. After running this, those resources will be treated
# as external data sources instead of managed resources.

set -e

# Color output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Get environment
if [ -z "$1" ]; then
  echo -e "${YELLOW}Enter the environment (dev/staging/prod):${NC}"
  read ENVIRONMENT
else
  ENVIRONMENT=$1
fi

# Verify the inputs
echo -e "${GREEN}Removing resources from state for environment:${NC} ${YELLOW}$ENVIRONMENT${NC}"
echo ""

# Change to terraform directory
cd "$(dirname "$0")/.."

# Select the workspace
echo -e "${GREEN}Selecting Terraform workspace...${NC}"
terraform workspace select $ENVIRONMENT || terraform workspace new $ENVIRONMENT

# Remove resources from state
echo -e "${GREEN}Removing resources from Terraform state...${NC}"
echo -e "${YELLOW}This will NOT delete the actual resources, just remove them from Terraform's state.${NC}"
echo -e "${YELLOW}They will be treated as external data sources in future runs.${NC}"

echo -e "\n${YELLOW}Removing network...${NC}"
terraform state rm module.network.google_compute_network.network[0] || echo "Not in state"

echo -e "\n${YELLOW}Removing subnet...${NC}"
terraform state rm module.network.google_compute_subnetwork.default_subnet[0] || echo "Not in state"

echo -e "\n${YELLOW}Removing health check...${NC}"
terraform state rm module.compute.google_compute_health_check.default[0] || echo "Not in state"

echo -e "\n${YELLOW}Removing service account...${NC}"
terraform state rm module.github_oidc.google_service_account.github_actions[0] || echo "Not in state"

echo -e "\n${YELLOW}Removing workload identity pool...${NC}"
terraform state rm module.github_oidc.google_iam_workload_identity_pool.github_pool[0] || echo "Not in state"

echo -e "\n${YELLOW}Removing workload identity provider...${NC}"
terraform state rm module.github_oidc.google_iam_workload_identity_pool_provider.github_provider[0] || echo "Not in state"

echo -e "\n${YELLOW}Removing KMS keyring...${NC}"
terraform state rm module.storage.google_kms_key_ring.storage_keyring[0] || echo "Not in state"

echo -e "\n${YELLOW}Removing storage bucket...${NC}"
terraform state rm module.storage.google_storage_bucket.default[0] || echo "Not in state"

echo -e "\n${GREEN}✅ Resources removed from state!${NC}"
echo -e "${YELLOW}Now run terraform plan with -var=\"create_resources=false\" to use data sources instead.${NC}"
echo ""
echo -e "${GREEN}Example command:${NC}"
echo -e "terraform plan -var=\"project_id=YOUR_PROJECT_ID\" -var=\"create_resources=false\" -var=\"safe_cmek=false\"" 