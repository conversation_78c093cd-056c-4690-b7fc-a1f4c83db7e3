#!/bin/bash
# import_existing_resources.sh - Import existing resources into Terraform state
#
# Usage: ./import_existing_resources.sh [PROJECT_ID] [ENVIRONMENT]
#
# If PROJECT_ID and ENVIRONMENT are not provided, they will be prompted for.

set -e

# Color output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Get project ID
if [ -z "$1" ]; then
  echo -e "${YELLOW}Enter the GCP project ID:${NC}"
  read PROJECT_ID
else
  PROJECT_ID=$1
fi

# Get environment
if [ -z "$2" ]; then
  echo -e "${YELLOW}Enter the environment (dev/staging/prod):${NC}"
  read ENVIRONMENT
else
  ENVIRONMENT=$2
fi

# Verify the inputs
echo -e "${GREEN}Importing resources for:${NC}"
echo -e "Project ID: ${YELLOW}$PROJECT_ID${NC}"
echo -e "Environment: ${YELLOW}$ENVIRONMENT${NC}"
echo ""

# Check if the user is logged in to gcloud
echo -e "${GREEN}Verifying gcloud auth...${NC}"
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q "@"; then
  echo -e "${RED}You are not logged in to gcloud. Please run 'gcloud auth login' first.${NC}"
  exit 1
fi

# Change to terraform directory
cd "$(dirname "$0")/.."

# Select the workspace
echo -e "${GREEN}Selecting Terraform workspace...${NC}"
terraform workspace select $ENVIRONMENT || terraform workspace new $ENVIRONMENT

# Import resources
echo -e "${GREEN}Importing existing resources...${NC}"

# 1. Import network
echo -e "${YELLOW}Importing network...${NC}"
terraform import -var="project_id=$PROJECT_ID" -var="create_resources=false" -var="safe_cmek=false" \
  "module.network.google_compute_network.network[0]" "projects/$PROJECT_ID/global/networks/a2a-$ENVIRONMENT-network" || true

# 2. Import subnet
echo -e "${YELLOW}Importing subnet...${NC}"
terraform import -var="project_id=$PROJECT_ID" -var="create_resources=false" -var="safe_cmek=false" \
  "module.network.google_compute_subnetwork.default_subnet[0]" "projects/$PROJECT_ID/regions/$(terraform output -raw region)/subnetworks/a2a-$ENVIRONMENT-subnet" || true

# 3. Import health check
echo -e "${YELLOW}Importing health check...${NC}"
terraform import -var="project_id=$PROJECT_ID" -var="create_resources=false" -var="safe_cmek=false" \
  "module.compute.google_compute_health_check.default[0]" "projects/$PROJECT_ID/global/healthChecks/a2a-$ENVIRONMENT-vm-health-check" || true

# 4. Import service account
echo -e "${YELLOW}Importing service account...${NC}"
terraform import -var="project_id=$PROJECT_ID" -var="create_resources=false" -var="safe_cmek=false" \
  "module.github_oidc.google_service_account.github_actions[0]" "projects/$PROJECT_ID/serviceAccounts/github-actions@$PROJECT_ID.iam.gserviceaccount.com" || true

# 5. Import OIDC pool
echo -e "${YELLOW}Importing workload identity pool...${NC}"
POOL_ID=$(terraform output -raw github_oidc_pool_id 2>/dev/null || echo "github-pool")
terraform import -var="project_id=$PROJECT_ID" -var="create_resources=false" -var="safe_cmek=false" \
  "module.github_oidc.google_iam_workload_identity_pool.github_pool[0]" "projects/$PROJECT_ID/locations/global/workloadIdentityPools/$POOL_ID" || true

# 6. Import OIDC provider
echo -e "${YELLOW}Importing workload identity provider...${NC}"
PROVIDER_ID=$(terraform output -raw github_oidc_provider_id 2>/dev/null || echo "github-provider")
terraform import -var="project_id=$PROJECT_ID" -var="create_resources=false" -var="safe_cmek=false" \
  "module.github_oidc.google_iam_workload_identity_pool_provider.github_provider[0]" "projects/$PROJECT_ID/locations/global/workloadIdentityPools/$POOL_ID/providers/$PROVIDER_ID" || true

# 7. Import storage bucket
echo -e "${YELLOW}Importing storage bucket...${NC}"
# Check if the bucket exists before trying to import it
if gsutil ls -b gs://a2a-$ENVIRONMENT-storage &>/dev/null; then
  echo -e "${GREEN}Bucket exists, importing...${NC}"
  terraform import -var="project_id=$PROJECT_ID" -var="create_resources=false" -var="safe_cmek=false" \
    "module.storage.google_storage_bucket.default[0]" "a2a-$ENVIRONMENT-storage" || true
else
  echo -e "${YELLOW}Bucket does not exist. It will be created by Terraform.${NC}"
  # When running terraform later, you'll need to use var.create_resources=true for storage
fi

# 8. Import KMS keyring if it exists
echo -e "${YELLOW}Importing KMS keyring...${NC}"
# Check if the keyring exists
if gcloud kms keyrings describe a2a-$ENVIRONMENT-storage-keyring --location=$(terraform output -raw region 2>/dev/null || echo "us-central1") &>/dev/null; then
  echo -e "${GREEN}KMS keyring exists, importing...${NC}"
  terraform import -var="project_id=$PROJECT_ID" -var="create_resources=false" -var="safe_cmek=false" \
    "module.storage.google_kms_key_ring.storage_keyring[0]" "projects/$PROJECT_ID/locations/$(terraform output -raw region 2>/dev/null || echo "us-central1")/keyRings/a2a-$ENVIRONMENT-storage-keyring" || true
else
  echo -e "${YELLOW}KMS keyring does not exist. It will be created by Terraform.${NC}"
  # When running terraform later, you'll need to use var.create_resources=true for KMS
fi

echo -e "\n${GREEN}✅ Import operations completed!${NC}"
echo -e "${YELLOW}Some import operations may have failed if resources don't exist or are already in state.${NC}"
echo -e "${GREEN}Run 'terraform plan' to see if there are any remaining differences.${NC}"

# Plan with create_resources=false
echo -e "${GREEN}Running terraform plan with create_resources=false...${NC}"
terraform plan -var="project_id=$PROJECT_ID" -var="create_resources=false" -var="safe_cmek=false"

echo -e "${GREEN}Import completed. If there are any errors, you may need to fix them manually.${NC}"
echo -e "${YELLOW}To use the remote backend, run: rm backend.tf && terraform init -reconfigure${NC}" 