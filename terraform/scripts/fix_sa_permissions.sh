#!/bin/bash
# fix_sa_permissions.sh - Grant necessary permissions to the Terraform service account
#
# Usage: ./fix_sa_permissions.sh [SERVICE_ACCOUNT_EMAIL] [PROJECT_ID]
#
# If SERVICE_ACCOUNT_EMAIL and PROJECT_ID are not provided, they will be prompted for.

set -e

# Color output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Get service account email
if [ -z "$1" ]; then
  echo -e "${YELLOW}Enter the service account email (e.g., <EMAIL>):${NC}"
  read SERVICE_ACCOUNT
else
  SERVICE_ACCOUNT=$1
fi

# Get project ID
if [ -z "$2" ]; then
  echo -e "${YELLOW}Enter the GCP project ID:${NC}"
  read PROJECT_ID
else
  PROJECT_ID=$2
fi

# Verify the inputs
echo -e "${GREEN}Granting permissions to:${NC}"
echo -e "Service Account: ${YELLOW}$SERVICE_ACCOUNT${NC}"
echo -e "Project: ${YELLOW}$PROJECT_ID${NC}"
echo ""

# Check if the user is logged in to gcloud
echo -e "${GREEN}Verifying gcloud auth...${NC}"
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q "@"; then
  echo -e "${RED}You are not logged in to gcloud. Please run 'gcloud auth login' first.${NC}"
  exit 1
fi

# Required roles for Terraform automation
ROLES=(
  # Basic roles
  "roles/viewer"
  "roles/editor"  # Note: This is a broad role, consider using more specific roles in production
  
  # Cloud SQL
  "roles/cloudsql.admin"
  
  # Secret Manager
  "roles/secretmanager.admin"
  
  # Compute Engine
  "roles/compute.admin"
  
  # Storage
  "roles/storage.admin"
  
  # Pub/Sub
  "roles/pubsub.admin"
  
  # IAM
  "roles/iam.serviceAccountUser"
  "roles/iam.serviceAccountAdmin"
  "roles/iam.workloadIdentityPoolAdmin"  # For creating and managing Workload Identity Pools
  
  # Security
  "roles/iam.securityAdmin"
  
  # Network
  "roles/compute.networkAdmin"
  
  # Additional roles that may be needed
  "roles/cloudkms.admin"  # For CMEK
  "roles/cloudkms.cryptoKeyEncrypterDecrypter"  # For using KMS keys
  "roles/resourcemanager.projectIamAdmin"  # For setting IAM policies
)

# Grant roles
echo -e "${GREEN}Granting roles to $SERVICE_ACCOUNT...${NC}"
for ROLE in "${ROLES[@]}"; do
  echo -e "Granting ${YELLOW}$ROLE${NC}..."
  gcloud projects add-iam-policy-binding "$PROJECT_ID" \
    --member="serviceAccount:$SERVICE_ACCOUNT" \
    --role="$ROLE" \
    --quiet
done

# Give the Cloud Storage service account access to the KMS keys
echo -e "\n${GREEN}Granting Cloud Storage service account permission to use KMS keys...${NC}"
STORAGE_SA="$(gcloud storage service-agent --project=$PROJECT_ID --format='value(email)')"
if [ -z "$STORAGE_SA" ]; then
  # Fallback to the standard service account pattern if the command fails
  STORAGE_SA="service-$(gcloud projects describe $PROJECT_ID --format='value(projectNumber)')@gs-project-accounts.iam.gserviceaccount.com"
fi

echo -e "Cloud Storage service account: ${YELLOW}$STORAGE_SA${NC}"
gcloud projects add-iam-policy-binding "$PROJECT_ID" \
  --member="serviceAccount:$STORAGE_SA" \
  --role="roles/cloudkms.cryptoKeyEncrypterDecrypter" \
  --quiet

# Verify service account permissions
echo -e "\n${GREEN}Service account permissions updated. Verifying...${NC}"
gcloud projects get-iam-policy "$PROJECT_ID" \
  --format="table(bindings.role,bindings.members)" \
  --flatten="bindings[].members" \
  --filter="bindings.members:$SERVICE_ACCOUNT"

echo -e "\n${GREEN}✅ Permissions successfully granted!${NC}"
echo -e "${YELLOW}NOTE: It may take a few minutes for permission changes to propagate through the GCP system.${NC}" 