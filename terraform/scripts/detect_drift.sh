#!/bin/bash
# Terraform Drift Detection Script
# This script detects drift in the Terraform-managed infrastructure

set -e

# Check if environment is provided
if [ $# -ne 1 ]; then
  echo "Usage: $0 <environment>"
  echo "Where environment is one of: dev, staging, prod"
  exit 1
fi

ENVIRONMENT=$1
TERRAFORM_DIR="$(dirname "$(dirname "$(realpath "$0")")")"
VAR_FILE="${TERRAFORM_DIR}/environments/${ENVIRONMENT}/terraform.tfvars"
LOG_FILE="/tmp/terraform-drift-${ENVIRONMENT}-$(date +%Y%m%d%H%M%S).log"

# Check if var file exists
if [ ! -f "$VAR_FILE" ]; then
  echo "Error: Variable file not found: $VAR_FILE"
  exit 1
fi

echo "Starting drift detection for ${ENVIRONMENT} environment..."
echo "Terraform directory: ${TERRAFORM_DIR}"
echo "Using var file: ${VAR_FILE}"
echo "Log file: ${LOG_FILE}"

# Change to Terraform directory
cd "$TERRAFORM_DIR"

# Initialize Terraform
echo "Initializing Terraform..."
terraform init -input=false > "$LOG_FILE" 2>&1

# Select or create workspace
echo "Selecting workspace ${ENVIRONMENT}..."
terraform workspace select "$ENVIRONMENT" >> "$LOG_FILE" 2>&1 || terraform workspace new "$ENVIRONMENT" >> "$LOG_FILE" 2>&1

# Run plan with detailed exit code
echo "Running Terraform plan to detect drift..."
terraform plan -var-file="$VAR_FILE" -detailed-exitcode -input=false >> "$LOG_FILE" 2>&1
PLAN_EXIT_CODE=$?

# Check exit code
# 0 = No changes
# 1 = Error
# 2 = Changes detected
if [ $PLAN_EXIT_CODE -eq 0 ]; then
  echo "No drift detected in ${ENVIRONMENT} environment."
  exit 0
elif [ $PLAN_EXIT_CODE -eq 1 ]; then
  echo "Error running Terraform plan. Check the log file: $LOG_FILE"
  exit 1
elif [ $PLAN_EXIT_CODE -eq 2 ]; then
  echo "DRIFT DETECTED in ${ENVIRONMENT} environment!"
  echo "Details can be found in the log file: $LOG_FILE"
  
  # You could add notification logic here (Slack, email, etc.)
  # For example:
  # curl -X POST -H 'Content-type: application/json' --data "{\"text\":\"⚠️ Terraform drift detected in ${ENVIRONMENT} environment!\"}" $SLACK_WEBHOOK_URL
  
  exit 2
else
  echo "Unknown exit code: $PLAN_EXIT_CODE"
  exit 1
fi 