#!/bin/bash
# run_tests.sh - Run Terraform tests with proper setup
#
# Usage: ./terraform/scripts/run_tests.sh [--mock] [--integration]
#   --mock: Run mock tests that don't require real infrastructure
#   --integration: Run integration tests that deploy actual infrastructure
#   --debug: Enable debug output

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TERRAFORM_DIR="$(dirname "$SCRIPT_DIR")"
TEST_DIR="$TERRAFORM_DIR/test"

# Default mode is to run mock tests only (safer default)
RUN_MOCK=true
RUN_INTEGRATION=false
DEBUG=false

# Parse command line arguments
while [[ "$#" -gt 0 ]]; do
  case $1 in
    --mock) RUN_MOCK=true ;;
    --integration) RUN_INTEGRATION=true; RUN_MOCK=false ;;
    --debug) DEBUG=true ;;
    --all) RUN_MOCK=true; RUN_INTEGRATION=true ;;
    *) echo "Unknown parameter: $1"; exit 1 ;;
  esac
  shift
done

# Echo with color
echo_color() {
  local color=$1
  local message=$2
  
  case $color in
    "green") echo -e "\033[0;32m$message\033[0m" ;;
    "yellow") echo -e "\033[1;33m$message\033[0m" ;;
    "red") echo -e "\033[0;31m$message\033[0m" ;;
    *) echo "$message" ;;
  esac
}

# Print header
echo_color "green" "======================================================"
echo_color "green" "Vedavivi Terraform Module Tests"
echo_color "green" "======================================================"
echo_color "yellow" "Test Directory: $TEST_DIR"

# Set up the backend for testing - always use CI backend
"$SCRIPT_DIR/setup-backend.sh" --ci

# Set up the environment based on test mode
if [[ "$RUN_MOCK" == "true" && "$RUN_INTEGRATION" == "false" ]]; then
  echo_color "yellow" "Mode: Mock (Safe testing without real infrastructure)"
  export TERRATEST_MOCK=true
  # Skip creating real infrastructure
  export TF_SKIP_CREDENTIALS_VALIDATION=true
  export TF_SKIP_PROVIDER_REGISTRATION=true
  # Other safety measures
  export TF_VAR_skip_delete_network=true
  export TF_VAR_skip_delete_bucket=true
elif [[ "$RUN_INTEGRATION" == "true" && "$RUN_MOCK" == "false" ]]; then
  echo_color "yellow" "Mode: Integration (Creating REAL infrastructure - USE WITH CAUTION)"
  export TERRATEST_MOCK=false
  unset TF_SKIP_CREDENTIALS_VALIDATION
  unset TF_SKIP_PROVIDER_REGISTRATION
  # Check if we have valid credentials
  if ! gcloud auth application-default print-access-token &>/dev/null; then
    echo_color "red" "ERROR: No valid GCP credentials found."
    echo_color "yellow" "Run 'gcloud auth application-default login' first."
    exit 1
  fi
else
  echo_color "yellow" "Mode: Combined (Running both mock and integration tests)"
  # No special environment setup - each test will handle its own environment
fi

echo_color "green" "======================================================"

# Change to the test directory
cd "$TEST_DIR"

# Download dependencies
echo_color "yellow" "Downloading Go dependencies..."
go mod tidy

# Run tests with appropriate filters
if [[ "$DEBUG" == "true" ]]; then
  VERBOSITY="-v"
else
  VERBOSITY="-v"  # Always use -v for now for better debugging
fi

if [[ "$RUN_MOCK" == "true" && "$RUN_INTEGRATION" == "false" ]]; then
  echo_color "yellow" "Running mock tests only..."
  TERRATEST_MOCK=true go test $VERBOSITY -run "TestMock|Mock"
elif [[ "$RUN_INTEGRATION" == "true" && "$RUN_MOCK" == "false" ]]; then
  echo_color "yellow" "Running integration tests only..."
  TERRATEST_MOCK=false go test $VERBOSITY -run "TestGCS|TestNetwork|TestCloudSQL" -timeout 30m
else
  echo_color "yellow" "Running all tests..."
  go test $VERBOSITY
fi

# Return to the original directory
cd - > /dev/null 