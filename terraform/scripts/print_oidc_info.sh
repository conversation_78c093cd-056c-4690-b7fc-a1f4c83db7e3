#!/bin/bash
set -e

# Script to print OIDC provider information for GitHub Actions workflows
echo "Retrieving OIDC provider information from Terraform state..."

cd "$(dirname "$0")/.."

# Check if terraform state exists
if [[ ! -f ".terraform/terraform.tfstate" && ! -f "terraform.tfstate" ]]; then
  echo "Error: Terraform state not found. Run 'terraform init' first."
  exit 1
fi

# Get the OIDC provider information from terraform output
echo "======= OIDC PROVIDER INFORMATION ======="
terraform output -json | jq -r '
  if has("github_oidc") then
    .github_oidc.value | 
    "Workload Identity Provider: " + .workload_identity_provider + "\n" +
    "Service Account Email: " + .service_account_email
  else
    "Error: github_oidc output not found in Terraform state"
  end
'
echo "========================================"

echo ""
echo "Instructions:"
echo "1. Add the above values as GitHub repository secrets:"
echo "   - WORKLOAD_IDENTITY_PROVIDER: The full provider path"
echo "   - GCP_SERVICE_ACCOUNT: The service account email"
echo ""
echo "2. Make sure your GitHub Actions workflow uses these secrets:"
echo '```yaml'
echo 'uses: google-github-actions/auth@v1'
echo 'with:'
echo '  workload_identity_provider: ${{ secrets.WORKLOAD_IDENTITY_PROVIDER }}'
echo '  service_account: ${{ secrets.GCP_SERVICE_ACCOUNT }}'
echo '```'
echo ""
echo "3. Ensure your workflow is running from the main branch or a tagged release"
echo "   (as configured in the attribute conditions)" 