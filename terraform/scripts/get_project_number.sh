#!/bin/bash
# get_project_number.sh - Automatically get and export GCP project number
# 
# This script gets the GCP project number for a given project ID
# and exports it as TF_VAR_project_number for use with Terraform.
#
# Usage:
#   source ./scripts/get_project_number.sh [project_id]
#
# If project_id is not provided, it will use the current gcloud project.

set -e

# Get the current directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &>/dev/null && pwd)"
TERRAFORM_DIR="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Parse arguments
PROJECT_ID="${1:-}"

# If no project ID provided, get the current project
if [ -z "$PROJECT_ID" ]; then
    PROJECT_ID=$(gcloud config get-value project 2>/dev/null)
    if [ -z "$PROJECT_ID" ]; then
        echo -e "${RED}Error: No project ID provided and no active gcloud project.${NC}"
        echo "Usage: source $0 [project_id]"
        return 1
    fi
fi

echo -e "${BLUE}Getting project number for $PROJECT_ID...${NC}"

# Get the project number
PROJECT_NUMBER=$(gcloud projects describe "$PROJECT_ID" --format="value(projectNumber)")

if [ -z "$PROJECT_NUMBER" ]; then
    echo -e "${RED}Error: Could not get project number for $PROJECT_ID.${NC}"
    echo "Make sure you have the right permissions and the project exists."
    return 1
fi

# Export the project number for Terraform
export TF_VAR_project_number="$PROJECT_NUMBER"

echo -e "${GREEN}Exported TF_VAR_project_number=$PROJECT_NUMBER${NC}"
echo ""
echo -e "${BLUE}You can now run Terraform commands without being prompted for project_number:${NC}"
echo "cd $TERRAFORM_DIR && terraform plan" 