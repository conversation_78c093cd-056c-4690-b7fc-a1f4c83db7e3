#!/bin/bash
# configure_gha_for_existing_resources.sh - Update GitHub Actions workflow for handling existing resources
#
# Usage: ./configure_gha_for_existing_resources.sh

set -e

# Color output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if we're in the right directory
if [ ! -d ".github/workflows" ]; then
  if [ -d "../.github/workflows" ]; then
    cd ..
  else
    echo -e "${RED}Error: Cannot find .github/workflows directory. Please run this script from the repository root.${NC}"
    exit 1
  fi
fi

echo -e "${GREEN}Updating GitHub Actions workflow configuration to handle existing resources...${NC}"

# Create a modified version of terraform-gcp.yml that adds TF_CLI_ARGS to skip the resource creation failures
if [ -f ".github/workflows/terraform-gcp.yml" ]; then
  # Create a backup
  cp .github/workflows/terraform-gcp.yml .github/workflows/terraform-gcp.yml.bak
  
  # Add a warning comment about not using global TF_CLI_ARGS
  # Use a more portable approach that works on both GNU and BSD sed
  TEMP_FILE=$(mktemp)
  awk '/env:/ {print; print "  # WARNING: Do not add TF_CLI_ARGS here - it causes issues with terraform init"; print "  # Add flags like parallelism directly to the specific commands instead"; next} {print}' .github/workflows/terraform-gcp.yml > "$TEMP_FILE"
  mv "$TEMP_FILE" .github/workflows/terraform-gcp.yml
  
  # Remove the global TF_CLI_ARGS setting and instead modify the specific commands
  # Add parallelism directly to terraform plan
  TEMP_FILE=$(mktemp)
  awk '{gsub(/terraform plan \\/, "terraform plan -parallelism=1 \\"); print}' .github/workflows/terraform-gcp.yml > "$TEMP_FILE"
  mv "$TEMP_FILE" .github/workflows/terraform-gcp.yml
  
  # Add parallelism directly to terraform apply
  TEMP_FILE=$(mktemp)
  awk '{gsub(/terraform apply -auto-approve/, "terraform apply -parallelism=1 -auto-approve"); print}' .github/workflows/terraform-gcp.yml > "$TEMP_FILE"
  mv "$TEMP_FILE" .github/workflows/terraform-gcp.yml
  
  # First check if the variables already exist in the file
  if grep -q "TF_VAR_create_resources" .github/workflows/terraform-gcp.yml; then
    echo -e "${YELLOW}Environment variables already exist, skipping addition...${NC}"
  else
    # Add the variables only if they don't exist - using awk for portability
    TEMP_FILE=$(mktemp)
    awk '{gsub(/TF_VAR_project_id: \${{ secrets.GCP_PROJECT_ID }}/, "TF_VAR_project_id: ${{ secrets.GCP_PROJECT_ID }}\n          TF_VAR_create_resources: \"false\"\n          TF_VAR_safe_cmek: \"false\""); print}' .github/workflows/terraform-gcp.yml > "$TEMP_FILE"
    mv "$TEMP_FILE" .github/workflows/terraform-gcp.yml
  fi
  
  echo -e "${GREEN}Updated terraform-gcp.yml with configuration for existing resources.${NC}"
else
  echo -e "${YELLOW}Warning: terraform-gcp.yml not found.${NC}"
fi

echo -e "${GREEN}Creating import job to run first...${NC}"

# Create a specialized workflow file for importing existing resources
cat > .github/workflows/terraform-import.yml << EOF
name: "Import Existing Terraform Resources"

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to import resources for'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - prod

env:
  TF_LOG: INFO
  TF_VERSION: "1.12.0"
  GCP_PROJECT_ID: \${{ secrets.GCP_PROJECT_ID }}

permissions:
  contents: read
  id-token: write # For Workload Identity Federation

jobs:
  terraform-import:
    name: "Terraform Import"
    runs-on: ubuntu-latest
    environment: \${{ inputs.environment }}
    
    defaults:
      run:
        shell: bash
        working-directory: ./terraform
    
    steps:
      # Checkout the repository
      - name: Checkout
        uses: actions/checkout@v4

      # Setup Terraform
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: \${{ env.TF_VERSION }}

      # Authenticate to GCP
      - id: auth
        name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: \${{ secrets.WORKLOAD_IDENTITY_PROVIDER }}
          service_account: \${{ secrets.GCP_SA_EMAIL }}
          create_credentials_file: true
          export_environment_variables: true

      # Set up Cloud SDK
      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      # Initialize Terraform with local backend
      - name: Terraform Init with Local Backend
        id: init-local
        run: |
          cp backend.tf.local backend.tf
          terraform init -reconfigure

      # Select workspace
      - name: Select Terraform Workspace
        run: |
          terraform workspace select \${{ inputs.environment }} || terraform workspace new \${{ inputs.environment }}

      # Import all resources
      - name: Import Network
        continue-on-error: true
        run: |
          terraform import module.network.google_compute_network.network projects/\${{ env.GCP_PROJECT_ID }}/global/networks/a2a-\${{ inputs.environment }}-network

      - name: Import Subnet
        continue-on-error: true
        run: |
          terraform import module.network.google_compute_subnetwork.default_subnet[0] projects/\${{ env.GCP_PROJECT_ID }}/regions/\${{ vars.REGION || 'us-central1' }}/subnetworks/a2a-\${{ inputs.environment }}-subnet

      - name: Import Health Check
        continue-on-error: true
        run: |
          terraform import module.compute.google_compute_health_check.default projects/\${{ env.GCP_PROJECT_ID }}/global/healthChecks/a2a-\${{ inputs.environment }}-vm-health-check

      - name: Import Service Account
        continue-on-error: true
        run: |
          terraform import module.github_oidc.google_service_account.github_actions projects/\${{ env.GCP_PROJECT_ID }}/serviceAccounts/github-actions@\${{ env.GCP_PROJECT_ID }}.iam.gserviceaccount.com

      - name: Import OIDC Pool
        continue-on-error: true
        run: |
          terraform import module.github_oidc.google_iam_workload_identity_pool.github_pool[0] projects/\${{ env.GCP_PROJECT_ID }}/locations/global/workloadIdentityPools/github-pool

      - name: Import OIDC Provider
        continue-on-error: true
        run: |
          terraform import module.github_oidc.google_iam_workload_identity_pool_provider.github_provider[0] projects/\${{ env.GCP_PROJECT_ID }}/locations/global/workloadIdentityPools/github-pool/providers/github-provider

      - name: Import KMS Keyring
        continue-on-error: true
        run: |
          terraform import module.storage.google_kms_key_ring.storage_keyring[0] projects/\${{ env.GCP_PROJECT_ID }}/locations/\${{ vars.REGION || 'us-central1' }}/keyRings/a2a-\${{ inputs.environment }}-storage-keyring

      - name: Import Storage Bucket
        continue-on-error: true
        run: |
          terraform import module.storage.google_storage_bucket.default a2a-\${{ inputs.environment }}-storage

      # Initialize with GCS backend
      - name: Terraform Init with GCS Backend
        id: init-gcs
        run: |
          rm -f backend.tf
          terraform init -reconfigure -backend-config="bucket=\${{ secrets.GCP_TF_STATE_BUCKET }}" -backend-config="prefix=terraform/state/\${{ inputs.environment }}"

      # Show plan after imports
      - name: Terraform Plan
        run: |
          terraform plan -parallelism=1 -var="project_id=\${{ env.GCP_PROJECT_ID }}" -var="create_resources=false" -var="safe_cmek=false"
EOF

echo -e "${GREEN}Created terraform-import.yml workflow.${NC}"

# Also update the import workflow if it exists
if [ -f ".github/workflows/terraform-import.yml" ]; then
  # Create a backup
  cp .github/workflows/terraform-import.yml .github/workflows/terraform-import.yml.bak
  
  # Add a warning comment about not using global TF_CLI_ARGS
  # Use a more portable approach that works on both GNU and BSD sed
  TEMP_FILE=$(mktemp)
  awk '/env:/ {print; print "  # WARNING: Do not add TF_CLI_ARGS here - it causes issues with terraform init"; print "  # Add flags like parallelism directly to the specific commands instead"; next} {print}' .github/workflows/terraform-import.yml > "$TEMP_FILE"
  mv "$TEMP_FILE" .github/workflows/terraform-import.yml
  
  echo -e "${GREEN}Updated terraform-import.yml with warnings.${NC}"
fi

echo -e "\n${GREEN}✅ Configuration updated!${NC}"
echo -e "${YELLOW}Run the 'Import Existing Terraform Resources' workflow first, then run the regular deployment.${NC}" 