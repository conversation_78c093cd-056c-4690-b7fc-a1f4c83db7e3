#!/bin/bash
set -e

# Script to set up GCP for Terraform and fix OIDC provider issues
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &>/dev/null && pwd)"
TERRAFORM_DIR="$(dirname "$SCRIPT_DIR")"
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

cd "$TERRAFORM_DIR"

echo -e "${BLUE}=== A2A Platform GCP Setup Script ===${NC}"
echo "This script will help set up GCP for Terraform and fix OIDC provider issues."

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}Error: gcloud CLI is not installed. Please install it first.${NC}"
    echo "Visit: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Check if terraform is installed
if ! command -v terraform &> /dev/null; then
    echo -e "${RED}Error: terraform is not installed. Please install it first.${NC}"
    echo "Visit: https://developer.hashicorp.com/terraform/downloads"
    exit 1
fi

# Ensure the user is logged in to gcloud
echo -e "${BLUE}Checking authentication...${NC}"
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" 2>/dev/null | grep -q "@"; then
    echo -e "${RED}You are not logged in to gcloud. Please login:${NC}"
    gcloud auth login
fi

# Get the current project
CURRENT_PROJECT=$(gcloud config get-value project 2>/dev/null)
if [[ -z "$CURRENT_PROJECT" ]]; then
    echo -e "${RED}No GCP project is currently set.${NC}"
    echo "Available projects:"
    gcloud projects list --format="table(projectId,name)"
    echo ""
    read -p "Enter the project ID to use: " PROJECT_ID
    gcloud config set project "$PROJECT_ID"
    CURRENT_PROJECT="$PROJECT_ID"
fi

echo -e "${GREEN}Using GCP project: $CURRENT_PROJECT${NC}"

# Get the project number (needed for OIDC setup)
PROJECT_NUMBER=$(gcloud projects describe "$CURRENT_PROJECT" --format="value(projectNumber)")
echo "Project number: $PROJECT_NUMBER"

# Create a tfvars file with the project info
echo -e "${BLUE}Creating terraform.auto.tfvars with project information...${NC}"
cat > terraform.auto.tfvars <<EOL
project_id     = "$CURRENT_PROJECT"
project_number = "$PROJECT_NUMBER"
EOL
echo -e "${GREEN}Created terraform.auto.tfvars with project information${NC}"

# Enable required APIs
echo -e "${BLUE}Enabling required GCP APIs...${NC}"
APIS_TO_ENABLE=(
  "iam.googleapis.com"
  "iamcredentials.googleapis.com"
  "cloudresourcemanager.googleapis.com"
  "compute.googleapis.com"
  "servicenetworking.googleapis.com"
  "sqladmin.googleapis.com"
  "secretmanager.googleapis.com"
  "storage.googleapis.com"
  "artifactregistry.googleapis.com"
)

for api in "${APIS_TO_ENABLE[@]}"; do
  echo "Enabling $api..."
  gcloud services enable "$api"
done
echo -e "${GREEN}Required APIs enabled${NC}"

# Create a GCS bucket for Terraform state if it doesn't exist
BUCKET_NAME="${CURRENT_PROJECT}-tf-state"
echo -e "${BLUE}Checking for Terraform state bucket...${NC}"
if ! gsutil ls -b "gs://${BUCKET_NAME}" &>/dev/null; then
  echo "Creating GCS bucket for Terraform state: $BUCKET_NAME"
  gsutil mb -l us-central1 "gs://${BUCKET_NAME}"
  gsutil versioning set on "gs://${BUCKET_NAME}"
  echo -e "${GREEN}Created GCS bucket for Terraform state: $BUCKET_NAME${NC}"
else
  echo -e "${GREEN}Using existing GCS bucket for Terraform state: $BUCKET_NAME${NC}"
fi

# Switch to local backend for development
echo -e "${BLUE}Switching to local backend for development...${NC}"
bash "$SCRIPT_DIR/switch_backend.sh" local

# Initialize Terraform with local backend
echo -e "${BLUE}Initializing Terraform...${NC}"
terraform init -reconfigure

# Run plan (this will create the OIDC provider and service account)
echo -e "${BLUE}Running Terraform plan...${NC}"
terraform plan -out=tfplan.out -var="import_existing_resources=true" -var="create_resources=false"

echo -e "${BLUE}Would you like to apply these changes to create the OIDC provider? (y/n)${NC}"
read -r APPLY
if [[ "$APPLY" == "y" || "$APPLY" == "Y" ]]; then
  # Apply Terraform changes, targeting only the OIDC module
  echo -e "${BLUE}Applying Terraform changes (targeting OIDC module)...${NC}"
  terraform apply -target=module.github_oidc tfplan.out
  
  # Print OIDC information
  echo -e "${BLUE}OIDC provider information:${NC}"
  bash "$SCRIPT_DIR/print_oidc_info.sh"
  
  # Additional instructions for GitHub Actions
  echo -e "${BLUE}Setting up GitHub Actions...${NC}"
  echo "1. Add the following secrets to your GitHub repository:"
  echo "   - GCP_PROJECT_ID: $CURRENT_PROJECT"
  echo "   - GCP_TF_STATE_BUCKET: $BUCKET_NAME"
  echo "   - GCP_PROJECT_ID_DEV: $CURRENT_PROJECT (if used)"
else
  echo -e "${RED}Terraform apply skipped. You will need to apply manually to create the OIDC provider.${NC}"
fi

echo -e "${GREEN}Setup complete! Follow the instructions above to finish configuring GitHub Actions.${NC}" 