#!/bin/bash
# terraform-apply-skip-exists.sh - Apply Terraform configuration without recreating existing resources
#
# Usage: ./terraform-apply-skip-exists.sh [ENVIRONMENT] [PROJECT_ID]
#
# This script provides a way to apply Terraform configurations without
# trying to recreate resources that already exist in GCP.

set -e

# Color output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Get environment
if [ -z "$1" ]; then
  echo -e "${YELLOW}Enter the environment (dev/staging/prod):${NC}"
  read ENVIRONMENT
else
  ENVIRONMENT=$1
fi

# Get project ID
if [ -z "$2" ]; then
  echo -e "${YELLOW}Enter the GCP project ID:${NC}"
  read PROJECT_ID
else
  PROJECT_ID=$2
fi

# Change to terraform directory
cd "$(dirname "$0")/.."

# Select workspace
echo -e "${GREEN}Selecting Terraform workspace...${NC}"
terraform workspace select $ENVIRONMENT || terraform workspace new $ENVIRONMENT

# Get project number
echo -e "${GREEN}Getting project number...${NC}"
PROJECT_NUMBER=$(gcloud projects describe $PROJECT_ID --format="value(projectNumber)")
echo "Project number: $PROJECT_NUMBER"

# Run plan first with create_resources=false
echo -e "${GREEN}Running Terraform plan...${NC}"
terraform plan \
  -var="project_id=$PROJECT_ID" \
  -var="project_number=$PROJECT_NUMBER" \
  -var="create_resources=false" \
  -var="safe_cmek=false" \
  -out=tfplan.$ENVIRONMENT

# Ask for confirmation
echo ""
echo -e "${YELLOW}Review the plan above. Do you want to apply these changes? (y/n)${NC}"
read CONFIRM

if [[ "$CONFIRM" != "y" && "$CONFIRM" != "Y" ]]; then
  echo -e "${RED}Apply cancelled.${NC}"
  exit 0
fi

# Apply the plan
echo -e "${GREEN}Applying Terraform plan...${NC}"
terraform apply -auto-approve tfplan.$ENVIRONMENT

echo -e "${GREEN}✅ Terraform apply completed!${NC}"
echo -e "${YELLOW}Note: Resources that already exist in GCP were skipped.${NC}" 