#!/bin/bash

# Terraform Resource Creation Control Script
# This script helps set environment variables to control DNS and page rule creation

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}🎛️  Terraform Resource Creation Control${NC}"
    echo "========================================"
}

print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --create-all          Create both DNS records and page rules"
    echo "  --import-all          Import both DNS records and page rules (skip creation)"
    echo "  --create-dns          Create DNS records only"
    echo "  --import-dns          Import DNS records only"
    echo "  --create-page-rules   Create page rules only"
    echo "  --import-page-rules   Import page rules only"
    echo "  --staging-defaults    Set staging environment defaults"
    echo "  --production-defaults Set production environment defaults"
    echo "  --show-current        Show current environment variable values"
    echo "  --help                Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --create-all                    # Create all resources"
    echo "  $0 --import-all                    # Import all existing resources"
    echo "  $0 --create-dns --import-page-rules # Create DNS, import page rules"
    echo "  $0 --staging-defaults              # Use staging defaults"
}

show_current() {
    print_header
    echo "Current Environment Variables:"
    echo "=============================="
    echo "TF_VAR_create_dns_records: ${TF_VAR_create_dns_records:-<not set>}"
    echo "TF_VAR_create_page_rules: ${TF_VAR_create_page_rules:-<not set>}"
    echo ""
    echo "Default Values by Environment:"
    echo "=============================="
    echo "Staging:    DNS=true,  Page Rules=false"
    echo "Production: DNS=false, Page Rules=false"
    echo ""
    echo "GitHub Actions Secrets:"
    echo "======================"
    echo "CREATE_DNS_RECORDS: Set in repository secrets"
    echo "CREATE_PAGE_RULES: Set in repository secrets"
}

set_env_vars() {
    local dns_records="$1"
    local page_rules="$2"
    local description="$3"

    # Set Terraform variables
    export TF_VAR_create_dns_records="$dns_records"
    export TF_VAR_create_page_rules="$page_rules"

    print_status "Set environment variables for: $description"
    echo "  TF_VAR_create_dns_records=$dns_records"
    echo "  TF_VAR_create_page_rules=$page_rules"
    echo ""
    print_warning "These variables are set for the current shell session only."
    print_warning "To persist them, add to your ~/.bashrc or ~/.zshrc:"
    echo "  export TF_VAR_create_dns_records=$dns_records"
    echo "  export TF_VAR_create_page_rules=$page_rules"
    echo ""
    echo "For GitHub Actions, set these repository secrets:"
    echo "  CREATE_DNS_RECORDS=$dns_records"
    echo "  CREATE_PAGE_RULES=$page_rules"
}

main() {
    if [[ $# -eq 0 ]]; then
        show_usage
        exit 1
    fi

    case "$1" in
        --create-all)
            set_env_vars "true" "true" "Create all resources"
            ;;
        --import-all)
            set_env_vars "false" "false" "Import all existing resources"
            ;;
        --create-dns)
            export TF_VAR_create_dns_records="true"
            print_status "Set TF_VAR_create_dns_records=true"
            ;;
        --import-dns)
            export TF_VAR_create_dns_records="false"
            print_status "Set TF_VAR_create_dns_records=false"
            ;;
        --create-page-rules)
            export TF_VAR_create_page_rules="true"
            print_status "Set TF_VAR_create_page_rules=true"
            ;;
        --import-page-rules)
            export TF_VAR_create_page_rules="false"
            print_status "Set TF_VAR_create_page_rules=false"
            ;;
        --staging-defaults)
            set_env_vars "true" "false" "Staging environment defaults"
            ;;
        --production-defaults)
            set_env_vars "false" "false" "Production environment defaults"
            ;;
        --show-current)
            show_current
            ;;
        --help)
            show_usage
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
}

main "$@"
