#!/bin/bash
# setup_github_workload_identity.sh - Create a Workload Identity Provider for GitHub Actions
#
# This script sets up a Workload Identity Provider for GitHub Actions in a GCP project
# and configures the necessary permissions to allow GitHub Actions to impersonate a service account.
#
# Usage:
#   ./setup_github_workload_identity.sh -p PROJECT_ID -g GITHUB_ORG/REPO [-r ROLES] [-n NAME_PREFIX] [-s SERVICE_ACCOUNT]

set -e

# Script to create Workload Identity Federation for GitHub Actions
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &>/dev/null && pwd)"
TERRAFORM_DIR="$(dirname "$SCRIPT_DIR")"
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Default values
POOL_ID="github-pool"
PROVIDER_ID="github-provider"
SA_NAME="github-actions"
GITHUB_REPO="blkops-collective/a2a-platform"

# Display help message
usage() {
  echo "Usage: $0 [options]"
  echo
  echo "This script creates Workload Identity Federation for GitHub Actions."
  echo
  echo "Options:"
  echo "  -p, --project <project_id>   GCP Project ID (required)"
  echo "  -r, --repo <owner/repo>      GitHub repository (default: $GITHUB_REPO)"
  echo "  --pool <pool_id>             Workload Identity Pool ID (default: $POOL_ID)"
  echo "  --provider <provider_id>     Workload Identity Provider ID (default: $PROVIDER_ID)"
  echo "  --sa <service_account>       Service Account name (default: $SA_NAME)"
  echo "  -h, --help                   Show this help message"
  echo
  exit 1
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    -p|--project)
      PROJECT_ID="$2"
      shift 2
      ;;
    -r|--repo)
      GITHUB_REPO="$2"
      shift 2
      ;;
    --pool)
      POOL_ID="$2"
      shift 2
      ;;
    --provider)
      PROVIDER_ID="$2"
      shift 2
      ;;
    --sa)
      SA_NAME="$2"
      shift 2
      ;;
    -h|--help)
      usage
      ;;
    *)
      echo -e "${RED}Unknown option: $1${NC}" >&2
      usage
      ;;
  esac
done

# Validate required parameters
if [ -z "$PROJECT_ID" ]; then
  echo -e "${RED}Error: Project ID is required.${NC}" >&2
  usage
fi

# Get project number
echo -e "${BLUE}Getting project number for $PROJECT_ID...${NC}"
PROJECT_NUMBER=$(gcloud projects describe "$PROJECT_ID" --format="value(projectNumber)")
echo -e "${GREEN}Project number: $PROJECT_NUMBER${NC}"

# Check if the user is logged in
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" 2>/dev/null | grep -q "@"; then
  echo -e "${RED}Error: You are not logged in to gcloud. Please run 'gcloud auth login' first.${NC}" >&2
  exit 1
fi

# Enable required APIs
echo -e "${BLUE}Enabling required APIs...${NC}"
gcloud services enable iam.googleapis.com --project="$PROJECT_ID"
gcloud services enable iamcredentials.googleapis.com --project="$PROJECT_ID"

# 1. Create Workload Identity Pool if it doesn't exist
echo -e "${BLUE}Creating Workload Identity Pool '$POOL_ID'...${NC}"
if gcloud iam workload-identity-pools describe "$POOL_ID" --project="$PROJECT_ID" --location="global" &>/dev/null; then
  echo -e "${YELLOW}Workload Identity Pool '$POOL_ID' already exists.${NC}"
else
  gcloud iam workload-identity-pools create "$POOL_ID" \
    --project="$PROJECT_ID" \
    --location="global" \
    --display-name="GitHub Actions Pool"
  echo -e "${GREEN}Workload Identity Pool created successfully.${NC}"
fi

# 2. Create Workload Identity Provider if it doesn't exist
echo -e "${BLUE}Creating Workload Identity Provider '$PROVIDER_ID'...${NC}"
if gcloud iam workload-identity-pools providers describe "$PROVIDER_ID" \
  --project="$PROJECT_ID" \
  --location="global" \
  --workload-identity-pool="$POOL_ID" &>/dev/null; then
  echo -e "${YELLOW}Workload Identity Provider '$PROVIDER_ID' already exists.${NC}"
else
  gcloud iam workload-identity-pools providers create-oidc "$PROVIDER_ID" \
    --project="$PROJECT_ID" \
    --location="global" \
    --workload-identity-pool="$POOL_ID" \
    --display-name="GitHub Actions Provider" \
    --attribute-mapping="google.subject=assertion.sub,attribute.actor=assertion.actor,attribute.repository=assertion.repository,attribute.repository_owner=assertion.repository_owner,attribute.workflow=assertion.workflow,attribute.ref=assertion.ref" \
    --issuer-uri="https://token.actions.githubusercontent.com" \
    --attribute-condition="attribute.repository == \"${GITHUB_REPO}\" && (attribute.ref == \"refs/heads/main\" || attribute.ref.startsWith(\"refs/tags/\"))"
  echo -e "${GREEN}Workload Identity Provider created successfully.${NC}"
fi

# 3. Create Service Account if it doesn't exist
echo -e "${BLUE}Creating Service Account '$SA_NAME'...${NC}"
if gcloud iam service-accounts describe "$SA_NAME@$PROJECT_ID.iam.gserviceaccount.com" --project="$PROJECT_ID" &>/dev/null; then
  echo -e "${YELLOW}Service Account '$SA_NAME' already exists.${NC}"
else
  gcloud iam service-accounts create "$SA_NAME" \
    --project="$PROJECT_ID" \
    --display-name="GitHub Actions Service Account"
  echo -e "${GREEN}Service Account created successfully.${NC}"
fi

# 4. Grant permissions to Service Account
echo -e "${BLUE}Granting roles to Service Account...${NC}"
# Storage Admin for Terraform state
gcloud projects add-iam-policy-binding "$PROJECT_ID" \
  --member="serviceAccount:$SA_NAME@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/storage.admin"

# Compute Admin for managing resources
gcloud projects add-iam-policy-binding "$PROJECT_ID" \
  --member="serviceAccount:$SA_NAME@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/compute.admin"

# Service Account User for impersonation
gcloud projects add-iam-policy-binding "$PROJECT_ID" \
  --member="serviceAccount:$SA_NAME@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/iam.serviceAccountUser"

# Artifact Registry Admin for containers
gcloud projects add-iam-policy-binding "$PROJECT_ID" \
  --member="serviceAccount:$SA_NAME@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/artifactregistry.admin"

# Cloud SQL Admin for database management
gcloud projects add-iam-policy-binding "$PROJECT_ID" \
  --member="serviceAccount:$SA_NAME@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/cloudsql.admin"

echo -e "${GREEN}Roles granted successfully.${NC}"

# 5. Allow Workload Identity Pool to impersonate Service Account
echo -e "${BLUE}Allowing Workload Identity Pool to impersonate Service Account...${NC}"
gcloud iam service-accounts add-iam-policy-binding "$SA_NAME@$PROJECT_ID.iam.gserviceaccount.com" \
  --project="$PROJECT_ID" \
  --role="roles/iam.workloadIdentityUser" \
  --member="principalSet://iam.googleapis.com/projects/$PROJECT_NUMBER/locations/global/workloadIdentityPools/$POOL_ID/attribute.repository/$GITHUB_REPO"

echo -e "${GREEN}Workload Identity Federation setup completed successfully.${NC}"

# 6. Print information for GitHub Actions setup
echo
echo -e "${BLUE}==== GITHUB ACTIONS SETUP INFORMATION ====${NC}"
echo -e "Add the following secrets to your GitHub repository:"
echo 
echo -e "${YELLOW}WIF_PROVIDER${NC}"
echo -e "projects/$PROJECT_NUMBER/locations/global/workloadIdentityPools/$POOL_ID/providers/$PROVIDER_ID"
echo
echo -e "${YELLOW}GCP_SA_EMAIL${NC}"
echo -e "$SA_NAME@$PROJECT_ID.iam.gserviceaccount.com"
echo
echo -e "${YELLOW}GCP_PROJECT_ID${NC}"
echo -e "$PROJECT_ID"
echo
echo -e "${YELLOW}GCP_TF_STATE_BUCKET${NC}"
echo -e "${PROJECT_ID}-tf-state"
echo
echo -e "${BLUE}Use these secrets in your GitHub Actions workflow:${NC}"
echo -e "```yaml"
echo -e "- uses: google-github-actions/auth@v2"
echo -e "  with:"
echo -e "    workload_identity_provider: \${{ secrets.WIF_PROVIDER }}"
echo -e "    service_account: \${{ secrets.GCP_SA_EMAIL }}"
echo -e "```"
echo
echo -e "${GREEN}All done!${NC}" 