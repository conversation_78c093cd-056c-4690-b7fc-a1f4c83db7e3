#!/bin/bash
# update_oidc_condition.sh - Update the attribute condition for an existing OIDC provider
#
# This script updates the attribute condition for an existing Workload Identity Pool OIDC provider
# to allow GitHub Actions to authenticate from any branch, not just main or tags.
#
# Usage:
#   ./update_oidc_condition.sh [project_id] [pool_id] [provider_id]
#
# If project_id is not provided, it will use the current gcloud project.
# Default pool_id is "github-pool" and default provider_id is "github-provider".

set -e

# Get the current directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &>/dev/null && pwd)"
TERRAFORM_DIR="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Parse arguments
PROJECT_ID="${1:-}"
POOL_ID="${2:-github-pool}"
PROVIDER_ID="${3:-github-provider}"
GITHUB_REPO="${4:-blkops-collective/a2a-platform}"

# If no project ID provided, get the current project
if [ -z "$PROJECT_ID" ]; then
    PROJECT_ID=$(gcloud config get-value project 2>/dev/null)
    if [ -z "$PROJECT_ID" ]; then
        echo -e "${RED}Error: No project ID provided and no active gcloud project.${NC}"
        echo "Usage: $0 [project_id] [pool_id] [provider_id] [github_repo]"
        exit 1
    fi
fi

echo -e "${BLUE}Updating OIDC provider attribute condition in project $PROJECT_ID...${NC}"
echo -e "Pool ID: $POOL_ID"
echo -e "Provider ID: $PROVIDER_ID"
echo -e "GitHub Repo: $GITHUB_REPO"

# Check if the provider exists
if ! gcloud iam workload-identity-pools providers describe "$PROVIDER_ID" \
  --project="$PROJECT_ID" \
  --location="global" \
  --workload-identity-pool="$POOL_ID" &>/dev/null; then
  echo -e "${RED}Error: Provider $PROVIDER_ID does not exist in pool $POOL_ID.${NC}"
  exit 1
fi

# Get the current attribute condition
CURRENT_CONDITION=$(gcloud iam workload-identity-pools providers describe "$PROVIDER_ID" \
  --project="$PROJECT_ID" \
  --location="global" \
  --workload-identity-pool="$POOL_ID" \
  --format="value(attributeCondition)")

echo -e "${YELLOW}Current attribute condition: $CURRENT_CONDITION${NC}"

# Set the new attribute condition
NEW_CONDITION="attribute.repository == \"$GITHUB_REPO\""
echo -e "${BLUE}New attribute condition: $NEW_CONDITION${NC}"

# Update the provider
echo -e "${BLUE}Updating provider...${NC}"
gcloud iam workload-identity-pools providers update-oidc "$PROVIDER_ID" \
  --project="$PROJECT_ID" \
  --location="global" \
  --workload-identity-pool="$POOL_ID" \
  --attribute-condition="$NEW_CONDITION"

echo -e "${GREEN}OIDC provider updated successfully.${NC}"
echo -e "${BLUE}You may need to wait a few minutes for the changes to propagate.${NC}" 