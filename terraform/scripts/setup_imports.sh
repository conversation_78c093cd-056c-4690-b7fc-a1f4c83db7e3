#!/bin/bash

# Setup Import Configuration Script
# This script helps configure Terraform imports for existing Cloudflare resources

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Function to get user input with default
get_input() {
    local prompt="$1"
    local default="$2"
    local result

    if [[ -n "$default" ]]; then
        read -p "$prompt [$default]: " result
        result="${result:-$default}"
    else
        read -p "$prompt: " result
    fi

    echo "$result"
}

# Function to check if a configuration line exists in the tfvars file
config_exists() {
    local file="$1"
    local config_line="$2"

    if [[ -f "$file" ]]; then
        grep -q "^[[:space:]]*$config_line" "$file"
    else
        return 1
    fi
}

# Function to add configuration line if it doesn't exist
add_config_if_missing() {
    local file="$1"
    local config_line="$2"
    local comment="$3"

    if config_exists "$file" "$config_line"; then
        print_warning "Configuration '$config_line' already exists in $file"
        return 0
    fi

    # Add configuration
    echo "" >> "$file"
    if [[ -n "$comment" ]]; then
        echo "# $comment" >> "$file"
    fi
    echo "$config_line" >> "$file"
    print_status "Added '$config_line' to $file"
}

# Function to show current configuration status
show_current_config() {
    local file="$1"

    print_header "Current Configuration Status"

    if [[ ! -f "$file" ]]; then
        print_warning "Configuration file $file does not exist yet"
        return
    fi

    local dns_exists=$(config_exists "$file" "create_dns_records = false" && echo "true" || echo "false")
    local page_rules_exists=$(config_exists "$file" "create_page_rules = false" && echo "true" || echo "false")

    print_status "DNS record creation disabled: $dns_exists"
    print_status "Page rule creation disabled: $page_rules_exists"

    if [[ "$dns_exists" == "true" || "$page_rules_exists" == "true" ]]; then
        echo ""
        print_status "Existing import configuration found in $file"
    fi
}

main() {
    print_header "Cloudflare Import Configuration Setup"

    # Get environment and configuration
    ENVIRONMENT=$(get_input "Enter environment (staging/production)" "staging")

    # Determine terraform directory
    TERRAFORM_DIR="terraform/environments/$ENVIRONMENT"
    if [[ ! -d "$TERRAFORM_DIR" ]]; then
        print_error "Terraform environment directory not found: $TERRAFORM_DIR"
        exit 1
    fi

    print_status "Setting up import configuration for environment: $ENVIRONMENT"

    # Create terraform.tfvars entries
    TFVARS_FILE="$TERRAFORM_DIR/terraform.tfvars"

    # Show current configuration status
    show_current_config "$TFVARS_FILE"

    # Ask what to import
    echo ""
    echo "What would you like to import?"
    echo "1. DNS records only"
    echo "2. Page rules only"
    echo "3. Both DNS records and page rules"
    echo "4. Just disable creation (manual import later)"

    read -p "Choose option (1-4): " IMPORT_CHOICE

    case $IMPORT_CHOICE in
        1)
            print_status "Configuring for DNS records import..."
            add_config_if_missing "$TFVARS_FILE" "create_dns_records = false" "Import configuration - disable DNS record creation"

            print_status "Now run: cd $TERRAFORM_DIR && terraform plan"
            print_status "Then use terraform import commands for DNS records"
            ;;
        2)
            print_status "Configuring for page rules import..."
            add_config_if_missing "$TFVARS_FILE" "create_page_rules = false" "Import configuration - disable page rule creation"

            print_status "Now run: cd $TERRAFORM_DIR && terraform plan"
            print_status "Then use terraform import commands for page rules"
            ;;
        3)
            print_status "Configuring for both DNS records and page rules import..."
            # Check if we need to add a section header
            if ! config_exists "$TFVARS_FILE" "create_dns_records = false" && ! config_exists "$TFVARS_FILE" "create_page_rules = false"; then
                echo "" >> "$TFVARS_FILE"
                echo "# Import configuration - disable resource creation" >> "$TFVARS_FILE"
            fi

            add_config_if_missing "$TFVARS_FILE" "create_dns_records = false" ""
            add_config_if_missing "$TFVARS_FILE" "create_page_rules = false" ""

            print_status "Import configuration updated in $TFVARS_FILE"
            print_status "Now run: cd $TERRAFORM_DIR && terraform plan"
            print_status "Then use terraform import commands for both DNS records and page rules"
            ;;
        4)
            print_status "Configuring for manual import later..."
            # Check if we need to add a section header
            if ! config_exists "$TFVARS_FILE" "create_dns_records = false" && ! config_exists "$TFVARS_FILE" "create_page_rules = false"; then
                echo "" >> "$TFVARS_FILE"
                echo "# Manual import configuration - disable all resource creation" >> "$TFVARS_FILE"
            fi

            add_config_if_missing "$TFVARS_FILE" "create_dns_records = false" ""
            add_config_if_missing "$TFVARS_FILE" "create_page_rules = false" ""

            print_status "Configuration updated to disable resource creation"
            print_status "You can manually import resources later using terraform import commands"
            ;;
        *)
            print_error "Invalid choice. Please run the script again."
            exit 1
            ;;
    esac

    print_header "Next Steps"
    print_status "1. Review the changes in $TFVARS_FILE"
    print_status "2. Navigate to the terraform directory: cd $TERRAFORM_DIR"
    print_status "3. Run terraform plan to see the current state"
    print_status "4. Use terraform import commands to import existing resources"
    print_status "5. Run terraform apply to manage the imported resources"

    print_header "Import Commands Reference"

    if [[ "$ENVIRONMENT" == "production" ]]; then
        WEB_RECORD="@"
        API_RECORD="api"
        WS_RECORD="ws"
    else
        WEB_RECORD="$ENVIRONMENT"
        API_RECORD="api-$ENVIRONMENT"
        WS_RECORD="ws-$ENVIRONMENT"
    fi

    print_status "DNS Records (replace ZONE_ID with your actual zone ID):"
    echo "  terraform import 'module.cloudflare_cdn.cloudflare_record.web[0]' 'ZONE_ID/$WEB_RECORD'"
    echo "  terraform import 'module.cloudflare_cdn.cloudflare_record.api[0]' 'ZONE_ID/$API_RECORD'"
    echo "  terraform import 'module.cloudflare_cdn.cloudflare_record.websocket[0]' 'ZONE_ID/$WS_RECORD'"

    print_status "Page Rules (replace ZONE_ID and PAGE_RULE_ID with actual values):"
    echo "  terraform import 'module.cloudflare_cdn.cloudflare_page_rule.static_assets[0]' 'ZONE_ID/PAGE_RULE_ID'"
    echo "  terraform import 'module.cloudflare_cdn.cloudflare_page_rule.html_cache[0]' 'ZONE_ID/PAGE_RULE_ID'"
    echo "  terraform import 'module.cloudflare_cdn.cloudflare_page_rule.api_bypass[0]' 'ZONE_ID/PAGE_RULE_ID'"

    print_status "Find page rule IDs in Cloudflare dashboard: Rules > Page Rules"

    print_status "Cloudflare Workers (only if create_workers=true):"
    echo "  terraform import 'module.cloudflare_cdn.cloudflare_workers_script.spa_router[0]' 'ACCOUNT_ID/WORKER_NAME'"
    echo "  terraform import 'module.cloudflare_cdn.cloudflare_workers_route.spa_router[0]' 'ZONE_ID/ROUTE_ID'"

    print_status "Find worker IDs in Cloudflare dashboard: Workers & Pages > Overview"
}

# Run main function
main "$@"
