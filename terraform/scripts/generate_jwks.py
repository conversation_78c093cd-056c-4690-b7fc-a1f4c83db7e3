#!/usr/bin/env python3
"""
Generate JSON Web Key Sets (JWKS) for OIDC authentication with GCP Workload Identity Federation.
This script creates RSA key pairs and formats them as JWKS for use with GCP.
"""

import argparse
import json
import os
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from datetime import datetime, timedelta, timezone
import uuid
import base64

def int_to_base64url(value):
    """Convert an integer to base64url format"""
    value_hex = format(value, 'x')
    # Ensure even length
    if len(value_hex) % 2 == 1:
        value_hex = '0' + value_hex
    
    value_bytes = bytes.fromhex(value_hex)
    encoded = base64.urlsafe_b64encode(value_bytes).rstrip(b'=')
    return encoded.decode('ascii')

def generate_key(key_id=None, key_size=2048):
    """Generate an RSA key pair and format as JWK"""
    if key_id is None:
        key_id = str(uuid.uuid4())
    
    # Generate RSA key pair
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=key_size
    )
    
    # Get the public key in the format needed for JWK
    public_numbers = private_key.public_key().public_numbers()
    
    # Create the JWK
    jwk = {
        "kty": "RSA",
        "use": "sig",  # signature
        "alg": "RS256",
        "kid": key_id,
        "n": int_to_base64url(public_numbers.n),  # modulus
        "e": int_to_base64url(public_numbers.e),  # exponent
    }
    
    # Serialize the private key for storage
    private_pem = private_key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.PKCS8,
        encryption_algorithm=serialization.NoEncryption()
    )
    
    return jwk, private_pem

def generate_jwks(num_keys=2, output_dir="./keys", key_size=2048):
    """Generate a JWKS with the specified number of keys"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    keys = []
    
    for i in range(num_keys):
        key_id = f"key-{i+1}"
        jwk, private_pem = generate_key(key_id, key_size)
        
        # Save the private key
        with open(os.path.join(output_dir, f"{key_id}.pem"), "wb") as f:
            f.write(private_pem)
        
        keys.append(jwk)
    
    # Create the JWKS
    jwks = {"keys": keys}
    
    # Save the JWKS
    with open(os.path.join(output_dir, "jwks.json"), "w") as f:
        json.dump(jwks, f, indent=2)
    
    return jwks

def main():
    parser = argparse.ArgumentParser(description="Generate JWKS for OIDC authentication")
    parser.add_argument("--num-keys", type=int, default=2, help="Number of keys to generate")
    parser.add_argument("--output-dir", type=str, default="./keys", help="Directory to save keys")
    parser.add_argument("--key-size", type=int, default=2048, help="RSA key size")
    
    args = parser.parse_args()
    
    jwks = generate_jwks(args.num_keys, args.output_dir, args.key_size)
    
    print(f"Generated JWKS with {len(jwks['keys'])} keys in {args.output_dir}")
    print(f"JWKS file: {os.path.join(args.output_dir, 'jwks.json')}")

if __name__ == "__main__":
    main() 