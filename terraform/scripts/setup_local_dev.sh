#!/bin/bash
# Local Development Setup Script
# This script sets up a local development environment for Terraform

set -e

# Default environment
ENVIRONMENT=${1:-dev}
TERRAFORM_DIR="$(dirname "$(dirname "$(realpath "$0")")")"
VAR_FILE="${TERRAFORM_DIR}/environments/${ENVIRONMENT}/terraform.tfvars"

# Print welcome message
echo "======================================================"
echo "Vedavivi Terraform Local Development Setup"
echo "======================================================"
echo "Environment: ${ENVIRONMENT}"
echo "Terraform directory: ${TERRAFORM_DIR}"
echo "Variable file: ${VAR_FILE}"
echo "======================================================"

# Flag to track if we should run with mock credentials
USE_MOCK_CREDENTIALS=false

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo "Warning: gcloud CLI is not installed."
    echo "You have a few options:"
    echo "1. Install gcloud CLI from: https://cloud.google.com/sdk/docs/install"
    echo "2. Continue with mock credentials (limited functionality)"
    echo "3. Use service account JSON credentials"
    echo
    
    read -p "How would you like to proceed? (1/2/3): " cloud_option
    case $cloud_option in
        1)
            echo "Please install gcloud CLI and run this script again."
            exit 0
            ;;
        2)
            echo "Continuing with mock credentials..."
            USE_MOCK_CREDENTIALS=true
            export TF_SKIP_CREDENTIALS_VALIDATION=true
            # Create and activate local backend immediately when using mock credentials
            BACKEND_CONFIG_FILE="$TERRAFORM_DIR/backend.tf"
            cat > "$BACKEND_CONFIG_FILE" << EOF
# Local backend configuration for development (auto-generated)
terraform {
  backend "local" {
    path = "terraform.tfstate.d/${ENVIRONMENT}/terraform.tfstate"
  }
}
EOF
            echo "Created and activated local backend configuration at: $BACKEND_CONFIG_FILE"
            ;;
        3)
            read -p "Enter the path to your service account JSON file: " sa_path
            if [ -f "$sa_path" ]; then
                export GOOGLE_APPLICATION_CREDENTIALS="$sa_path"
                echo "Using service account from: $sa_path"
            else
                echo "Error: Service account file not found: $sa_path"
                exit 1
            fi
            ;;
        *)
            echo "Invalid option. Exiting."
            exit 1
            ;;
    esac
fi

# Check if terraform is installed
if ! command -v terraform &> /dev/null; then
    echo "Error: terraform is not installed. Please install it first."
    echo "Installation options:"
    echo "1. Official website: https://www.terraform.io/downloads.html"
    echo "2. Use Homebrew (macOS): brew tap hashicorp/tap && brew install hashicorp/tap/terraform"
    echo "3. Use tfenv (version manager): brew install tfenv"
    exit 1
fi

# Check if var file exists
if [ ! -f "$VAR_FILE" ]; then
  echo "Error: Variable file not found: $VAR_FILE"
  echo "Creating environments directory structure..."
  mkdir -p "${TERRAFORM_DIR}/environments/${ENVIRONMENT}"
  
  # Create example var file
  cat > "$VAR_FILE" << EOL
project_id      = "test-project-id"
project_name    = "vedavivi"
region          = "us-central1"
zone            = "us-central1-a"
machine_type    = "e2-medium"
database_tier   = "db-f1-micro"
service_account = ""
EOL
  
  echo "Created example variable file at: $VAR_FILE"
  echo "Please edit this file with your actual values before proceeding."
  exit 0
fi

# Extract project ID from var file
PROJECT_ID=$(grep "project_id" "$VAR_FILE" | cut -d '=' -f2 | tr -d ' "')
echo "Project ID: $PROJECT_ID"

# Ask for confirmation
read -p "Do you want to proceed with setting up the local environment for ${ENVIRONMENT}? (y/n) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Setup canceled."
    exit 0
fi

# Authenticate with gcloud if we're not using mock credentials
if [ "$USE_MOCK_CREDENTIALS" = false ] && command -v gcloud &> /dev/null; then
    echo "Authenticating with gcloud..."
    gcloud auth application-default login
    
    # Set the active project
    echo "Setting active project to $PROJECT_ID..."
    gcloud config set project "$PROJECT_ID"
elif [ "$USE_MOCK_CREDENTIALS" = true ]; then
    echo "Using mock credentials - skipping GCP authentication"
fi

# Change to Terraform directory
cd "$TERRAFORM_DIR"

# Initialize Terraform
echo "Initializing Terraform..."
terraform init

# Select or create workspace
echo "Selecting workspace ${ENVIRONMENT}..."
terraform workspace select "$ENVIRONMENT" || terraform workspace new "$ENVIRONMENT"

# Run a test plan
echo "Running a test plan..."
if [ "$USE_MOCK_CREDENTIALS" = true ]; then
    # With mock credentials, just do validation
    terraform validate
    echo "Validation completed. Skipping planning phase due to mock credentials."
else
    terraform plan -var-file="$VAR_FILE"
fi

echo "======================================================"
echo "Setup complete!"
if [ "$USE_MOCK_CREDENTIALS" = true ]; then
    echo "Note: You're using mock credentials with limited functionality."
    echo "To apply changes in a real environment, you'll need to authenticate with GCP."
else
    echo "You're ready to start developing."
    echo "To apply changes, run:"
    echo "cd $TERRAFORM_DIR && terraform apply -var-file=$VAR_FILE"
fi
echo "======================================================" 