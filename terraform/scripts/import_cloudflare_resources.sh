#!/bin/bash

# Import Existing Cloudflare Resources Script
# This script helps import existing DNS records and page rules into Terraform state

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Check if required tools are installed
check_dependencies() {
    print_header "Checking Dependencies"

    if ! command -v terraform &> /dev/null; then
        print_error "Terraform is not installed or not in PATH"
        exit 1
    fi

    if ! command -v jq &> /dev/null; then
        print_error "jq is not installed. Please install jq to parse JSON responses."
        exit 1
    fi

    print_status "All dependencies are available"
}

# Function to get user input with default
get_input() {
    local prompt="$1"
    local default="$2"
    local result

    if [[ -n "$default" ]]; then
        read -p "$prompt [$default]: " result
        result="${result:-$default}"
    else
        read -p "$prompt: " result
    fi

    echo "$result"
}

# Main function
main() {
    print_header "Cloudflare Resources Import Tool"

    check_dependencies

    # Get environment and configuration
    ENVIRONMENT=$(get_input "Enter environment (staging/production)" "staging")
    ROOT_DOMAIN=$(get_input "Enter root domain" "vedavivi.app")
    ZONE_ID=$(get_input "Enter Cloudflare Zone ID" "")

    if [[ -z "$ZONE_ID" ]]; then
        print_error "Zone ID is required"
        exit 1
    fi

    # Calculate domain names based on environment
    if [[ "$ENVIRONMENT" == "production" ]]; then
        WEB_DOMAIN="$ROOT_DOMAIN"
        API_DOMAIN="api.$ROOT_DOMAIN"
        WS_DOMAIN="ws.$ROOT_DOMAIN"
    else
        WEB_DOMAIN="$ENVIRONMENT.$ROOT_DOMAIN"
        API_DOMAIN="api-$ENVIRONMENT.$ROOT_DOMAIN"
        WS_DOMAIN="ws-$ENVIRONMENT.$ROOT_DOMAIN"
    fi

    print_status "Domain configuration:"
    print_status "  Web: $WEB_DOMAIN"
    print_status "  API: $API_DOMAIN"
    print_status "  WebSocket: $WS_DOMAIN"

    # Change to terraform environment directory
    TERRAFORM_DIR="terraform/environments/$ENVIRONMENT"
    if [[ ! -d "$TERRAFORM_DIR" ]]; then
        print_error "Terraform environment directory not found: $TERRAFORM_DIR"
        exit 1
    fi

    cd "$TERRAFORM_DIR"
    print_status "Changed to directory: $TERRAFORM_DIR"

    # Initialize terraform if needed
    if [[ ! -f ".terraform/terraform.tfstate" ]]; then
        print_status "Initializing Terraform..."
        terraform init
    fi

    # Select workspace
    print_status "Selecting Terraform workspace: $ENVIRONMENT"
    terraform workspace select "$ENVIRONMENT" || terraform workspace new "$ENVIRONMENT"

    print_header "Importing DNS Records"

    # Import web DNS record
    print_status "Importing web DNS record for $WEB_DOMAIN..."
    if [[ "$ENVIRONMENT" == "production" ]]; then
        RECORD_NAME="@"
    else
        RECORD_NAME="$ENVIRONMENT"
    fi

    # Try to import web record
    terraform import "module.cloudflare_cdn.cloudflare_record.web[0]" "$ZONE_ID/$RECORD_NAME" || {
        print_warning "Failed to import web record. It may not exist or may already be imported."
    }

    # Import API DNS record
    print_status "Importing API DNS record for $API_DOMAIN..."
    if [[ "$ENVIRONMENT" == "production" ]]; then
        API_RECORD_NAME="api"
    else
        API_RECORD_NAME="api-$ENVIRONMENT"
    fi

    terraform import "module.cloudflare_cdn.cloudflare_record.api[0]" "$ZONE_ID/$API_RECORD_NAME" || {
        print_warning "Failed to import API record. It may not exist or may already be imported."
    }

    # Import WebSocket DNS record
    print_status "Importing WebSocket DNS record for $WS_DOMAIN..."
    if [[ "$ENVIRONMENT" == "production" ]]; then
        WS_RECORD_NAME="ws"
    else
        WS_RECORD_NAME="ws-$ENVIRONMENT"
    fi

    terraform import "module.cloudflare_cdn.cloudflare_record.websocket[0]" "$ZONE_ID/$WS_RECORD_NAME" || {
        print_warning "Failed to import WebSocket record. It may not exist or may already be imported."
    }

    print_header "Importing Page Rules"

    # Import page rules (you'll need to get the page rule IDs from Cloudflare dashboard)
    print_status "Importing page rules..."
    print_warning "Page rule import requires manual page rule IDs from Cloudflare dashboard."
    print_status "To import page rules manually, use:"
    print_status "  terraform import 'module.cloudflare_cdn.cloudflare_page_rule.static_assets[0]' \$ZONE_ID/\$PAGE_RULE_ID"
    print_status "  terraform import 'module.cloudflare_cdn.cloudflare_page_rule.html_cache[0]' \$ZONE_ID/\$PAGE_RULE_ID"
    print_status "  terraform import 'module.cloudflare_cdn.cloudflare_page_rule.api_bypass[0]' \$ZONE_ID/\$PAGE_RULE_ID"
    print_status ""
    print_status "You can find page rule IDs in the Cloudflare dashboard under Rules > Page Rules"

    print_header "Import Complete"

    print_status "Running terraform plan to check state..."
    terraform plan -var="create_dns_records=false" -var="create_page_rules=false" || {
        print_warning "Terraform plan failed. You may need to adjust the configuration."
    }

    print_header "Next Steps"
    print_status "1. Review the terraform plan output above"
    print_status "2. If resources were successfully imported, update your terraform.tfvars:"
    print_status "   create_dns_records = false"
    print_status "   create_page_rules = false"
    print_status "3. For page rules, manually import them using the commands shown above"
    print_status "4. Run 'terraform apply' to manage the imported resources"
    print_status ""
    print_status "To use imported resources in future deployments, add this to your terraform.tfvars:"
    print_status "create_dns_records = false"
    print_status "create_page_rules = false"
}

# Run main function
main "$@"
