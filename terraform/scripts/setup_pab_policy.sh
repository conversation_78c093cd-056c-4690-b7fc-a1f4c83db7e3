#!/bin/bash
# setup_pab_policy.sh - Create a Principal Access Boundary policy and bind to a principal set
#
# This script creates a Principal Access Boundary policy that restricts what resources
# a service account can access, providing an additional layer of security beyond IAM.
#
# Usage:
#   ./setup_pab_policy.sh -p PROJECT_ID -s SERVICE_ACCOUNT [-n POLICY_NAME] [-r RESOURCES]

set -e

# Default values
POLICY_NAME="github-actions-pab"
RESOURCES="//"  # Default to all resources

# Parse command-line options
while getopts "p:s:n:r:h" opt; do
  case $opt in
    p) PROJECT_ID="$OPTARG" ;;
    s) SERVICE_ACCOUNT="$OPTARG" ;;
    n) POLICY_NAME="$OPTARG" ;;
    r) RESOURCES="$OPTARG" ;;
    h) 
      echo "Usage: $0 -p PROJECT_ID -s SERVICE_ACCOUNT [-n POLICY_NAME] [-r RESOURCES]"
      echo
      echo "Options:"
      echo "  -p PROJECT_ID       GCP Project ID (required)"
      echo "  -s SERVICE_ACCOUNT  Service account email (required)"
      echo "  -n POLICY_NAME      Name for the PAB policy (default: github-actions-pab)"
      echo "  -r RESOURCES        Comma-separated list of resources (default: all resources)"
      echo "  -h                  Show this help message"
      echo
      echo "Example:"
      echo "  $0 -p my-project -s <EMAIL> -r //storage.googleapis.com/,//compute.googleapis.com/"
      exit 0
      ;;
    \?) echo "Invalid option: -$OPTARG" >&2; exit 1 ;;
  esac
done

# Check required arguments
if [ -z "$PROJECT_ID" ]; then
  echo "Error: Project ID (-p) is required" >&2
  exit 1
fi

if [ -z "$SERVICE_ACCOUNT" ]; then
  echo "Error: Service Account (-s) is required" >&2
  exit 1
fi

# Convert resources to an array
IFS=',' read -ra RESOURCE_ARRAY <<< "$RESOURCES"

# Get project number
echo "Getting project number for $PROJECT_ID..."
PROJECT_NUMBER=$(gcloud projects describe "$PROJECT_ID" --format="value(projectNumber)")
if [ -z "$PROJECT_NUMBER" ]; then
  echo "Error: Failed to get project number" >&2
  exit 1
fi

# Principal set for the GitHub Actions service account
PRINCIPAL_SET="principalSet://iam.googleapis.com/projects/$PROJECT_NUMBER/locations/global/workloadIdentityPools/github-pool/attribute.repository"

echo "Creating Principal Access Boundary policy..."
echo "Project: $PROJECT_ID ($PROJECT_NUMBER)"
echo "Policy Name: $POLICY_NAME"
echo "Service Account: $SERVICE_ACCOUNT"
echo "Principal Set: $PRINCIPAL_SET"

# Create a temporary policy file
TEMP_POLICY_FILE=$(mktemp)

# Create the policy JSON
cat > "$TEMP_POLICY_FILE" << EOF
{
  "name": "policies/$POLICY_NAME",
  "displayName": "GitHub Actions PAB Policy",
  "description": "Principal Access Boundary for GitHub Actions",
  "resourcePolicies": [
    {
      "resourcePolicy": {
        "resources": [
EOF

# Add resources to the policy
for resource in "${RESOURCE_ARRAY[@]}"; do
  cat >> "$TEMP_POLICY_FILE" << EOF
          "$resource",
EOF
done

# Complete the policy JSON
cat >> "$TEMP_POLICY_FILE" << EOF
        ]
      }
    }
  ]
}
EOF

# Create the policy
echo -e "\n[1/3] Creating PAB policy..."
POLICY_PATH=$(gcloud access-context-manager policies create \
  --organization="$PROJECT_NUMBER" \
  --title="$POLICY_NAME" \
  --format="value(name)")

if [ -z "$POLICY_PATH" ]; then
  echo "Error: Failed to create policy. Check permissions or if a policy already exists." >&2
  echo "You may need to delete existing policy or use list-policies to check."
  exit 1
fi

POLICY_ID=$(echo "$POLICY_PATH" | cut -d'/' -f2)
echo "Created Policy ID: $POLICY_ID"

# Update the policy with resource rules
echo -e "\n[2/3] Updating policy with resource rules..."
gcloud access-context-manager policies update "$POLICY_ID" \
  --policy-file="$TEMP_POLICY_FILE"

# Bind the policy to the principal set
echo -e "\n[3/3] Binding policy to principal set..."
gcloud access-context-manager principal-access-bounds bindings create \
  --principal="$PRINCIPAL_SET" \
  --access-boundary-policy="$POLICY_PATH" \
  --organization="$PROJECT_NUMBER"

# Clean up temporary file
rm "$TEMP_POLICY_FILE"

echo -e "\n✅ Setup completed successfully!"
echo "Principal Access Boundary policy $POLICY_NAME has been created and bound to $SERVICE_ACCOUNT"
echo "This restricts the service account to only access resources defined in the policy."
echo

# Output command to check bindings
echo "To check existing bindings, run:"
echo "gcloud access-context-manager principal-access-bounds bindings list --organization=$PROJECT_NUMBER"
echo

# Output command to delete binding if needed
echo "To remove the binding, run:"
echo "gcloud access-context-manager principal-access-bounds bindings delete BINDING_ID --organization=$PROJECT_NUMBER" 