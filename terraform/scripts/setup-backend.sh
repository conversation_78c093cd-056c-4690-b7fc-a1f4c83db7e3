#!/bin/bash
# setup-backend.sh - Configure the Terraform backend based on environment
# 
# Usage: ./scripts/setup-backend.sh [--local] [--ci] [--bucket BUCKET_NAME]
#   --local: Use local backend (default for development)
#   --ci: Use GCS backend for CI
#   --bucket: Specify a custom bucket name (default: vedavivi-tf-state-a2a)

set -e

# Default values
USE_LOCAL=false
USE_CI=false
BUCKET_NAME="vedavivi-tf-state-a2a"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TF_DIR="$(dirname "$SCRIPT_DIR")"

# Parse command line arguments
while [[ "$#" -gt 0 ]]; do
  case $1 in
    --local) USE_LOCAL=true ;;
    --ci) USE_CI=true ;;
    --bucket) BUCKET_NAME="$2"; shift ;;
    *) echo "Unknown parameter: $1"; exit 1 ;;
  esac
  shift
done

# Default to local if no option specified
if [[ "$USE_LOCAL" == "false" && "$USE_CI" == "false" ]]; then
  USE_LOCAL=true
fi

# Clear any existing backend configuration
rm -f "$TF_DIR/backend.tf"

if [[ "$USE_LOCAL" == "true" ]]; then
  echo "Setting up local backend for development..."
  cp "$TF_DIR/backend.tf.local" "$TF_DIR/backend.tf"
  echo "Local backend configured successfully."
elif [[ "$USE_CI" == "true" ]]; then
  echo "Setting up GCS backend for CI with bucket: $BUCKET_NAME..."
  cat > "$TF_DIR/backend.tf" << EOF
# GCS backend configuration for Terraform state (auto-generated for CI)
terraform {
  backend "gcs" {
    bucket = "$BUCKET_NAME"
    prefix = "terraform/state"
  }
}
EOF
  echo "GCS backend configured successfully."
fi

echo "Terraform backend configuration complete." 