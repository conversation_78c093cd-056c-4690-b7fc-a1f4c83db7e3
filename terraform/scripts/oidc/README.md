# OIDC JWKS Setup for GCP Workload Identity Federation

This directory contains scripts for generating JSON Web Key Sets (JWKS) required for setting up OIDC authentication with GCP Workload Identity Federation.

## What is JWKS?

A JSON Web Key Set (JWKS) is a set of keys containing the public keys that should be used to verify any JSON Web Token (JWT) issued by an authorization server. In the context of GitHub Actions and GCP Workload Identity Federation, these keys are used to establish trust between GitHub and GCP.

## Usage

### Prerequisites

- Node.js (v12 or later)

### Generating JWKS

1. Run the generation script:

```bash
node generate_jwks.js
```

2. This will create:
   - `keys/private.pem`: The private key (keep this secure)
   - `keys/jwks.json`: The JWKS file containing the public key info

### Using with Terraform

The JWKS file is used in the OIDC Terraform module. The default path is set in the module's variables.

```hcl
module "github_oidc" {
  source          = "../modules/oidc"
  project_id      = var.project_id
  project_number  = var.project_number
  github_repo     = "blkops-collective/a2a-platform"
  jwks_json_path  = "../scripts/oidc/keys/jwks.json"  # Path is relative to the Terraform root
}
```

## Security Considerations

- The private key is sensitive and should not be committed to version control
- For production environments, consider using a more secure key management solution
- Rotate keys periodically for enhanced security 