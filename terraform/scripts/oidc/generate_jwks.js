/**
 * Generate JSON Web Key Set (JWKS) for GitHub Actions OIDC
 * 
 * This script generates a public/private key pair and formats it as a JWKS
 * for use with GCP Workload Identity Federation.
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// Create directory if it doesn't exist
const keysDir = path.join(__dirname, 'keys');
if (!fs.existsSync(keysDir)) {
  fs.mkdirSync(keysDir, { recursive: true });
}

// Generate RSA key pair
console.log('Generating RSA key pair...');
const { publicKey, privateKey } = crypto.generateKeyPairSync('rsa', {
  modulusLength: 2048,
  publicKeyEncoding: {
    type: 'spki',
    format: 'pem'
  },
  privateKeyEncoding: {
    type: 'pkcs8',
    format: 'pem'
  }
});

// Save private key
fs.writeFileSync(path.join(keysDir, 'private.pem'), privateKey);
console.log('Private key saved to keys/private.pem');

// Export public key components for JWKS
const jwk = crypto.createPublicKey(publicKey).export({ format: 'jwk' });

// Add required attributes for JWKS
const kid = crypto.randomBytes(16).toString('hex');
jwk.kid = kid;
jwk.use = 'sig';
jwk.alg = 'RS256';

// Create JWKS structure
const jwks = {
  keys: [jwk]
};

// Save JWKS
fs.writeFileSync(path.join(keysDir, 'jwks.json'), JSON.stringify(jwks, null, 2));
console.log('JWKS saved to keys/jwks.json');

// Output success message
console.log('JWKS generation complete!');
console.log(`Key ID (kid): ${kid}`); 