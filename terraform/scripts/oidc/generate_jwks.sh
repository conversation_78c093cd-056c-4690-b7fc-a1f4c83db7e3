#!/bin/bash

# Generate JSON Web Key Set (JWKS) for GitHub Actions OIDC
# This is a wrapper script for the Node.js JWKS generator

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
NODE_SCRIPT="${SCRIPT_DIR}/generate_jwks.js"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "Error: Node.js is required but not installed."
    echo "Please install Node.js and try again."
    exit 1
fi

# Run the Node.js script
echo "Generating JWKS..."
node "$NODE_SCRIPT"

# Check if the script executed successfully
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ JWKS generation completed successfully!"
    echo "You can now use this JWKS with your Terraform OIDC module."
    echo ""
    echo "Next steps:"
    echo "1. Run Terraform to apply your GCP Workload Identity Federation configuration"
    echo "2. Configure your GitHub Actions workflow to use the created identity"
else
    echo "❌ JWKS generation failed."
    exit 1
fi 