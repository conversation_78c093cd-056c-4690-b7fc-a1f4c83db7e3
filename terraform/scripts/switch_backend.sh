#!/bin/bash
set -e

# <PERSON>ript to properly switch Terraform backends by handling main.tf backend declarations
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &>/dev/null && pwd)"
TERRAFORM_DIR="$(dirname "$SCRIPT_DIR")"
MAIN_TF="$TERRAFORM_DIR/main.tf"
BACKEND_LOCAL="$TERRAFORM_DIR/backend.tf.local"
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

cd "$TERRAFORM_DIR"

usage() {
  echo "Usage: $0 [local|gcs|restore]"
  echo "  local   - Switch to local backend (for development)"
  echo "  gcs     - Switch to GCS backend (for production)"
  echo "  restore - Restore main.tf to original state"
  exit 1
}

# Check args
if [ $# -lt 1 ]; then
  usage
fi

MODE="$1"
BUCKET_NAME="${2:-}"
PREFIX="${3:-terraform/state}"

# Backup main.tf if no backup exists
if [ ! -f "${MAIN_TF}.bak" ]; then
  echo -e "${BLUE}Creating backup of main.tf...${NC}"
  cp "$MAIN_TF" "${MAIN_TF}.bak"
fi

case "$MODE" in
  local)
    echo -e "${BLUE}Switching to local backend...${NC}"
    
    # Comment out the backend block in main.tf
    sed -i'.tmp' 's/^  backend "gcs" {.*$/  # Backend configuration is moved to backend.tf/g' "$MAIN_TF"
    rm -f "${MAIN_TF}.tmp"
    
    # Copy local backend config
    cp -f "$BACKEND_LOCAL" "$TERRAFORM_DIR/backend.tf"
    
    echo -e "${GREEN}Switched to local backend. Run 'terraform init -reconfigure' to apply changes.${NC}"
    ;;
    
  gcs)
    if [ -z "$BUCKET_NAME" ]; then
      echo -e "${RED}Error: GCS bucket name is required for GCS backend.${NC}"
      echo "Usage: $0 gcs BUCKET_NAME [PREFIX]"
      exit 1
    fi
    
    echo -e "${BLUE}Switching to GCS backend...${NC}"
    
    # Comment out the backend block in main.tf
    sed -i'.tmp' 's/^  backend "gcs" {.*$/  # Backend configuration is moved to backend.tf/g' "$MAIN_TF"
    rm -f "${MAIN_TF}.tmp"
    
    # Create GCS backend config
    cat > "$TERRAFORM_DIR/backend.tf" <<EOL
# GCS backend configuration (auto-generated)
terraform {
  backend "gcs" {
    bucket = "${BUCKET_NAME}"
    prefix = "${PREFIX}"
  }
}
EOL
    
    echo -e "${GREEN}Switched to GCS backend with bucket ${BUCKET_NAME}.${NC}"
    echo -e "Run 'terraform init -reconfigure' to apply changes."
    ;;
    
  restore)
    echo -e "${BLUE}Restoring original main.tf...${NC}"
    
    if [ -f "${MAIN_TF}.bak" ]; then
      cp "${MAIN_TF}.bak" "$MAIN_TF"
      echo -e "${GREEN}Restored main.tf to original state.${NC}"
    else
      echo -e "${RED}No backup found. Cannot restore.${NC}"
      exit 1
    fi
    
    # Remove backend.tf if it exists
    if [ -f "$TERRAFORM_DIR/backend.tf" ]; then
      rm -f "$TERRAFORM_DIR/backend.tf"
    fi
    ;;
    
  *)
    echo -e "${RED}Invalid mode: $MODE${NC}"
    usage
    ;;
esac 