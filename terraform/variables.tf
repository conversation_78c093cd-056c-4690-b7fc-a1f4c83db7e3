/**
 * # Terraform Variables
 *
 * This file defines all variables used in the Terraform configuration.
 */

variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "project_number" {
  description = "The GCP project number (required for GitHub OIDC authentication)"
  type        = string
  default     = "" # Empty default to prevent prompt - will be overridden in runtime
}

variable "project_name" {
  description = "A prefix name for resources"
  type        = string
  default     = "vedavivi"
}

variable "unique_suffix" {
  description = "A unique suffix for the GCS state bucket"
  type        = string
  default     = "vedavivi-a2a"
}

variable "region" {
  description = "The GCP region for resources"
  type        = string
  default     = "us-central1"
}

variable "zone" {
  description = "The GCP zone for zonal resources"
  type        = string
  default     = "us-central1-a"
}

variable "machine_type" {
  description = "The machine type for compute instances (overrides machine_types map)"
  type        = string
  default     = null
}

variable "machine_types" {
  description = "Map of environment to machine type"
  type        = map(string)
  default = {
    dev     = "e2-small"
    staging = "e2-medium"
    prod    = "e2-standard-2"
  }
}

variable "database_tier" {
  description = "The database tier to use (overrides db_tiers map)"
  type        = string
  default     = null
}

variable "db_tiers" {
  description = "Database tiers for each environment"
  type        = map(string)
  default = {
    dev     = "db-f1-micro"
    staging = "db-g1-small"
    prod    = "db-n1-standard-1"
  }
}

variable "db_version" {
  description = "The database version to use"
  type        = string
  default     = "POSTGRES_15"
}

variable "db_name" {
  description = "The name of the database"
  type        = string
  default     = "a2a-platform"
}

variable "service_account_email" {
  description = "The email of the service account to use for resources"
  type        = string
  default     = ""
}

variable "min_instances" {
  description = "Minimum number of compute instances per environment"
  type        = map(number)
  default = {
    dev     = 1
    staging = 1
    prod    = 2
  }
}

variable "max_instances" {
  description = "Maximum number of compute instances per environment"
  type        = map(number)
  default = {
    dev     = 1
    staging = 2
    prod    = 5
  }
}

variable "db_flags" {
  description = "Database flags for configuration"
  type        = list(map(string))
  default     = []
}

variable "enable_high_availability" {
  description = "Enable high availability for database"
  type        = map(bool)
  default = {
    dev     = false
    staging = false
    prod    = true
  }
}

variable "import_existing_resources" {
  description = "Whether to import existing resources like Workload Identity Pool and Provider"
  type        = bool
  default     = true
}

variable "create_resources" {
  description = "Whether to create resources that may already exist"
  type        = bool
  default     = false
}

variable "existing_resources_exist" {
  description = "Whether existing resources (referenced by data sources when create_resources=false) actually exist"
  type        = bool
  default     = false
}

variable "storage_create_resources" {
  description = "Whether to create storage resources specifically (overrides create_resources)"
  type        = bool
  default     = null # When null, use the global create_resources value
}

variable "safe_cmek" {
  description = "Whether to use Customer Managed Encryption Keys in a safe mode that avoids resource recreation"
  type        = bool
  default     = true
}

variable "terraform_targets" {
  description = "List of specific resources to target with Terraform (for apply and destroy)"
  type        = list(string)
  default     = []
}

variable "redis_memory_size" {
  description = "Redis memory size in GB by environment"
  type        = map(number)
  default = {
    dev     = 1
    staging = 1
    prod    = 2
  }
}

variable "alternative_zone" {
  description = "The alternative zone for HA resources (like Redis HA)"
  type        = string
  default     = null
}

variable "artifact_registry_create" {
  description = "Whether to create the Artifact Registry repository for each environment"
  type        = bool
  default     = false
}

