package test

import (
	"fmt"
	"os"
	"strings"
	"testing"

	"github.com/gruntwork-io/terratest/modules/random"
	"github.com/gruntwork-io/terratest/modules/terraform"
)

func TestMainIntegration(t *testing.T) {
	t.<PERSON>()

	// Check if we're running in mock mode (using environment variable set in run_tests.sh)
	_, mockMode := os.LookupEnv("TERRATEST_MOCK")
	if mockMode {
		t.Skip("Skipping TestMainIntegration in mock mode - requires valid GCP credentials")
	}

	// Generate a random suffix for resources to avoid conflicts in tests
	uniqueID := strings.ToLower(random.UniqueId())

	// Note: The backend "gcs" bucket in main.tf uses a static bucket name "vedavivi-tf-state"
	// as variables are not allowed in the terraform backend configuration.
	// In a real environment, you would set the actual bucket name in the backend config.

	// Construct the terraform options with default retryable errors
	terraformOptions := terraform.WithDefaultRetryableErrors(t, &terraform.Options{
		// Path to the test fixture instead of main code
		TerraformDir: "./fixtures/test-config",
		// Explicitly use the terraform binary
		TerraformBinary: "terraform",

		// Variables to pass to the Terraform code
		Vars: map[string]interface{}{
			"project_id": fmt.Sprintf("test-project-%s", uniqueID),
			"region":     "us-central1",
		},
	})

	// Run a syntactic validation of the Terraform code
	terraform.Init(t, terraformOptions)

	// Output test notice
	t.Log("Integration test validated Terraform code syntax without GCP credentials")
}
