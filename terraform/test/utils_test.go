package test

import (
	"os"
	"testing"
)

func init() {
	// Enable this env var at init time to ensure all tests
	// have credentials validation skipped
	os.Setenv("TF_SKIP_CREDENTIALS_VALIDATION", "true")
}

// skipIfMockMode is a utility function to skip tests when in mock mode
func skipIfMockMode(t *testing.T) bool {
	if os.Getenv("TERRATEST_MOCK") == "true" {
		t.<PERSON><PERSON>("Skipping test in mock mode - requires GCP credentials")
		return true
	}
	return false
}

// CreateTestAndCleanup creates a test provider file and ensures it's cleaned up
// This pattern ensures that cleanup runs even if the test fails
func CreateTestAndCleanup(t *testing.T, moduleDir string) bool {
	// Skip the test if in mock mode
	if skipIfMockMode(t) {
		return false
	}

	// Create test provider file
	err := CreateTestProviderFile(t, moduleDir)
	if err != nil {
		t.Fatalf("Failed to create test provider file: %v", err)
	}

	// Register cleanup to run on both success and failure
	t.Cleanup(func() {
		RemoveTestProviderFile(t, moduleDir)
	})

	return true
}
