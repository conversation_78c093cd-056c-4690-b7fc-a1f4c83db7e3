package test

import (
	"fmt"
	"strings"
	"testing"

	"github.com/gruntwork-io/terratest/modules/random"
	"github.com/gruntwork-io/terratest/modules/terraform"
	"github.com/stretchr/testify/assert"
)

func TestNetworkModule(t *testing.T) {
	t.<PERSON>()

	// Skip this test in mock mode and create provider file
	moduleDir := "../modules/network"
	CreateTestAndCleanup(t, moduleDir)

	// Generate a random name for the VPC to avoid conflicts in tests
	uniqueID := strings.ToLower(random.UniqueId())
	networkName := fmt.Sprintf("terratest-network-%s", uniqueID)

	// Construct the terraform options with default retryable errors
	terraformOptions := terraform.WithDefaultRetryableErrors(t, &terraform.Options{
		// Path to the Terraform code
		TerraformDir: moduleDir,
		// Explicitly use the terraform binary
		TerraformBinary: "terraform",

		// Variables to pass to the Terraform code
		Vars: map[string]interface{}{
			"project_id":   "test-project",
			"network_name": networkName,
			"region":       "us-central1",
			"subnet_cidr":  "10.0.0.0/24",
		},
	})

	// Defer the destruction of resources to the end of the test
	defer terraform.Destroy(t, terraformOptions)

	// Initialize and apply the Terraform code
	terraform.InitAndApply(t, terraformOptions)

	// Run `terraform output` to get the values of output variables
	networkID := terraform.Output(t, terraformOptions, "network_id")
	subnetID := terraform.Output(t, terraformOptions, "subnet_id")

	// Verify network outputs
	assert.Contains(t, networkID, networkName)
	assert.Contains(t, subnetID, "subnet")
}
