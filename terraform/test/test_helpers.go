package test

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/gruntwork-io/terratest/modules/terraform"
)

// CreateTerraformOptions creates Terraform options with consistent settings
// for all tests, ensuring that Terraform is used as the binary
func CreateTerraformOptions(t *testing.T, terraformDir string, vars map[string]interface{}) *terraform.Options {
	options := &terraform.Options{
		TerraformDir:    terraformDir,
		TerraformBinary: "terraform", // Explicitly use terraform, not OpenTofu
		Vars:            vars,
	}

	return terraform.WithDefaultRetryableErrors(t, options)
}

// CreateTestProviderFile creates a provider.tf file in the specified directory
// that uses mock credentials for testing
func CreateTestProviderFile(t *testing.T, moduleDir string) error {
	// For tests, we create a provider with minimal configuration
	// The google provider doesn't support skip_credentials_validation
	const providerContent = `
# Test provider configuration - automatically generated for tests
provider "google" {
  project = "test-project"
  region  = "us-central1"
}
`
	providerPath := filepath.Join(moduleDir, "provider.tf")
	return os.WriteFile(providerPath, []byte(providerContent), 0644)
}

// RemoveTestProviderFile removes the test provider file
func RemoveTestProviderFile(t *testing.T, moduleDir string) {
	providerPath := filepath.Join(moduleDir, "provider.tf")
	os.Remove(providerPath)
}
