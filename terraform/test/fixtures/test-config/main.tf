/**
 * Test Configuration
 * Provides base resources for Terraform module tests
 */

terraform {
  required_version = ">= 1.12.0"

  # Use local backend for tests to avoid GCP authentication issues
  backend "local" {}

  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 4.0"
    }
  }
}

provider "google" {
  project = var.project_id
  region  = var.region

  # Disable API scopes to prevent authentication errors
  scopes = []

  # Skip client config to avoid authentication checks
  skip_credentials_validation = true

  # These settings make the provider more lenient for testing
  impersonate_service_account = ""
}

variable "project_id" {
  description = "The GCP project ID to use for tests"
  type        = string
}

variable "region" {
  description = "The GCP region to use for tests"
  type        = string
  default     = "us-central1"
}

output "project_id" {
  value = var.project_id
}

output "region" {
  value = var.region
} 