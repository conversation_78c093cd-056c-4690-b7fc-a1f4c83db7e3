# Terraform Module Tests

This directory contains tests for the Terraform modules using [Terratest](https://terratest.gruntwork.io/), a Go library that provides utilities for testing infrastructure code.

## Prerequisites

To run these tests, you'll need:

1. [Go](https://golang.org/doc/install) (version 1.21 or later)
2. [Terraform](https://www.terraform.io/downloads.html) (version 1.0.0 or later)
3. Google Cloud Platform account with:
   - A project with the necessary APIs enabled
   - Appropriate permissions to create resources
   - Service account credentials configured (`gcloud auth application-default login`)

## Test Structure

The tests are organized by module:

- `network_test.go`: Tests for the VPC network module
- `gcs_test.go`: Tests for the Cloud Storage module
- `cloudsql_test.go`: Tests for the Cloud SQL module

Each test follows a similar pattern:

1. Create random resource names to avoid conflicts
2. Define Terraform options with test variables
3. Initialize and apply the Terraform code
4. Verify the outputs match expectations
5. Clean up resources when done (automatically handled by `defer terraform.Destroy()`)

## Running the Tests

Use the provided script to run all tests or a specific test:

```bash
# Run all tests
./scripts/run_tests.sh

# Run a specific test
./scripts/run_tests.sh network_test.go
```

Note: The tests deploy actual resources in your GCP project, which may incur costs. The tests handle cleanup, but be aware that failures might leave resources running.

### Mock Mode

If you don't have Terraform installed or want to run basic checks without deploying real resources, you can use the mock mode:

```bash
# Run in mock mode (skips Terraform installation check)
./scripts/run_tests.sh --mock

# Run a specific test in mock mode
./scripts/run_tests.sh --mock network_test.go
```

In mock mode, the script will:
- Skip the Terraform installation check
- Only run tests prefixed with `TestMock` or the main integration test
- Set the `TERRATEST_MOCK` environment variable for tests to check

This is useful for:
- CI environments where you don't want to install Terraform
- Quick validation of file structure and basic checks
- Running syntax validation without deploying resources

## Test Timeouts

The tests have a 30-minute timeout by default. If you need to adjust this, modify the `-timeout` parameter in the `run_tests.sh` script:

```bash
go test -v -timeout 60m ./...  # Set timeout to 60 minutes
```

## Troubleshooting

### OpenTofu vs Terraform

The test files explicitly set `TerraformBinary: "terraform"` to ensure they use Terraform and not OpenTofu. If you encounter binary path issues, the `run_tests.sh` script exports the `TERRATEST_TFPATH` environment variable to help Terratest locate your Terraform binary.

If you still encounter issues with Terratest looking for "tofu" instead of "terraform", check that your Go environment and Terratest version are properly set up. You might need to clear Go caches or reinstall dependencies if the issue persists.

## Adding New Tests

To add a test for a new module:

1. Create a new test file (e.g., `new_module_test.go`)
2. Import the necessary packages
3. Define a test function with a unique name
4. Generate random resource names to avoid conflicts
5. Define Terraform options with appropriate variables (including `TerraformBinary: "terraform"`)
6. Initialize and apply the Terraform code
7. Verify outputs using assertions
8. Ensure resources are properly destroyed using `defer terraform.Destroy()` 