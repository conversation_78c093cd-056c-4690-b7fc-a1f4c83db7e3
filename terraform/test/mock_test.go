package test

import (
	"os"
	"regexp"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestMockModuleStructure verifies the structure of Terraform modules
// without actually running Terraform
func TestMockModuleStructure(t *testing.T) {
	// Skip this test unless in mock mode
	if os.Getenv("TERRATEST_MOCK") != "true" {
		t.Skip("Skipping mock test in normal mode")
	}

	// Test the network module structure
	t.Run("NetworkModule", func(t *testing.T) {
		// Check that the network module files exist
		assert.FileExists(t, "../modules/network/main.tf")
		assert.FileExists(t, "../modules/network/variables.tf")
		assert.FileExists(t, "../modules/network/outputs.tf")
	})

	// Test the GCS module structure
	t.Run("GCSModule", func(t *testing.T) {
		// Check that the GCS module files exist
		assert.FileExists(t, "../modules/gcs/main.tf")
		assert.FileExists(t, "../modules/gcs/variables.tf")
		assert.FileExists(t, "../modules/gcs/outputs.tf")
	})

	// Test the CloudSQL module structure
	t.Run("CloudSQLModule", func(t *testing.T) {
		// Check that the CloudSQL module files exist
		assert.FileExists(t, "../modules/cloudsql/main.tf")
		assert.FileExists(t, "../modules/cloudsql/variables.tf")
		assert.FileExists(t, "../modules/cloudsql/outputs.tf")
	})

	// Test the Redis module structure - skipping for now as it's not implemented yet
	t.Run("RedisModule", func(t *testing.T) {
		// Skip test as Redis module is not implemented yet (future feature)
		t.Skip("Redis module is planned but not implemented yet")
	})

	// Test the PubSub module structure - skipping for now as it's not implemented yet
	t.Run("PubSubModule", func(t *testing.T) {
		// Skip test as PubSub module is not implemented yet (future feature)
		t.Skip("PubSub module is planned but not implemented yet")
	})
}

// TestMockMainConfiguration verifies the main Terraform configuration
// This replaces TestMainIntegration when in mock mode
func TestMockMainConfiguration(t *testing.T) {
	// Skip this test unless in mock mode
	if os.Getenv("TERRATEST_MOCK") != "true" {
		t.Skip("Skipping mock test in normal mode")
	}

	// Check that the main configuration files exist
	assert.FileExists(t, "../main.tf")
	assert.FileExists(t, "../variables.tf")

	// Basic syntax validation
	t.Run("BackendConfig", func(t *testing.T) {
		// Verify the backend configuration doesn't use variables
		backendFile, err := os.ReadFile("../backend.tf")
		assert.NoError(t, err, "Failed to read backend.tf")

		// Convert to string for easier assertion
		backendContent := string(backendFile)

		// Use regex to extract just the backend block
		backendRegex := regexp.MustCompile(`terraform\s*\{[^{]*backend\s*"gcs"\s*\{[^}]*\}`)
		backendBlock := backendRegex.FindString(backendContent)

		// Make sure we found the backend block
		assert.NotEmpty(t, backendBlock, "Backend block not found in backend.tf")

		// Check that the bucket name is static (no variables)
		assert.Contains(t, backendContent, "bucket = \"vedavivi-tf-state-a2a\"")
		assert.NotContains(t, backendContent, "${var.", "Variables should not be used in backend configuration")
	})
}
