package test

import (
	"fmt"
	"os"
	"strings"
	"testing"

	"github.com/gruntwork-io/terratest/modules/random"
	"github.com/gruntwork-io/terratest/modules/terraform"
	"github.com/stretchr/testify/assert"
)

func TestCloudSQLModule(t *testing.T) {
	t.<PERSON>()

	// Skip this test in mock mode
	if os.Getenv("TERRATEST_MOCK") == "true" {
		t.<PERSON><PERSON>("Skipping CloudSQL test in mock mode - requires valid GCP credentials")
		return
	}

	// Set up real test with actual infrastructure
	moduleDir := "../modules/cloudsql"

	// Create a test provider file for the real test
	err := CreateTestProviderFile(t, moduleDir)
	if err != nil {
		t.Fatalf("Failed to create test provider file: %v", err)
	}

	// Clean up the provider file when the test is done
	t.Cleanup(func() {
		RemoveTestProviderFile(t, moduleDir)
	})

	// Generate a random name for the instance to avoid conflicts in tests
	uniqueID := strings.ToLower(random.UniqueId())
	instanceName := fmt.Sprintf("terratest-sql-%s", uniqueID)

	// Construct the terraform options with default retryable errors
	terraformOptions := terraform.WithDefaultRetryableErrors(t, &terraform.Options{
		// Path to the Terraform code
		TerraformDir: moduleDir,
		// Explicitly use the terraform binary
		TerraformBinary: "terraform",

		// Variables to pass to the Terraform code
		Vars: map[string]interface{}{
			"project_id":          "test-project",
			"instance_name":       instanceName,
			"database_version":    "POSTGRES_13",
			"region":              "us-central1",
			"tier":                "db-f1-micro",
			"availability_type":   "ZONAL",
			"deletion_protection": false,
			"database_name":       "testdb",
			"db_user":             "testuser",
			"db_password":         fmt.Sprintf("Password%s!", uniqueID),
		},
	})

	// Defer the destruction of resources to the end of the test
	defer terraform.Destroy(t, terraformOptions)

	// Initialize and apply the Terraform code
	terraform.InitAndApply(t, terraformOptions)

	// Run `terraform output` to get the values of output variables
	outputInstanceName := terraform.Output(t, terraformOptions, "instance_name")
	connectionName := terraform.Output(t, terraformOptions, "connection_name")
	databaseName := terraform.Output(t, terraformOptions, "database_name")
	databaseUser := terraform.Output(t, terraformOptions, "database_user")

	// Verify CloudSQL outputs
	assert.Equal(t, instanceName, outputInstanceName)
	assert.Contains(t, connectionName, "test-project")
	assert.Contains(t, connectionName, "us-central1")
	assert.Equal(t, "testdb", databaseName)
	assert.Equal(t, "testuser", databaseUser)
}
