package test

import (
	"fmt"
	"strings"
	"testing"

	"github.com/gruntwork-io/terratest/modules/random"
	"github.com/gruntwork-io/terratest/modules/terraform"
	"github.com/stretchr/testify/assert"
)

func TestGCSModule(t *testing.T) {
	t.<PERSON>()

	// Skip this test in mock mode and create provider file
	moduleDir := "../modules/gcs"
	CreateTestAndCleanup(t, moduleDir)

	// Generate a random bucket name to avoid conflicts in tests
	uniqueID := strings.ToLower(random.UniqueId())
	bucketName := fmt.Sprintf("terratest-bucket-%s", uniqueID)

	// Construct the terraform options with default retryable errors
	terraformOptions := terraform.WithDefaultRetryableErrors(t, &terraform.Options{
		// Path to the Terraform code
		TerraformDir: moduleDir,
		// Explicitly use the terraform binary
		TerraformBinary: "terraform",

		// Variables to pass to the Terraform code
		Vars: map[string]interface{}{
			"project_id":    "test-project",
			"bucket_name":   bucketName,
			"location":      "us-central1",
			"force_destroy": true,
			"versioning":    true,
		},
	})

	// Defer the destruction of resources to the end of the test
	defer terraform.Destroy(t, terraformOptions)

	// Initialize and apply the Terraform code
	terraform.InitAndApply(t, terraformOptions)

	// Run `terraform output` to get the values of output variables
	outputBucketName := terraform.Output(t, terraformOptions, "bucket_name")
	outputURL := terraform.Output(t, terraformOptions, "bucket_url")

	// Verify GCS outputs
	assert.Equal(t, bucketName, outputBucketName)
	assert.Contains(t, outputURL, bucketName)
}
