# Setting Up Workload Identity Federation for GitHub Actions

This guide explains how to use the `setup_github_workload_identity.sh` script to configure Workload Identity Federation between your GitHub repository and Google Cloud Platform.

## Prerequisites

1. [Google Cloud SDK](https://cloud.google.com/sdk/docs/install) installed and configured
2. `gcloud` authenticated with an account that has admin permissions on the project
3. Required APIs enabled in your GCP project:
   - IAM API (`iam.googleapis.com`)
   - IAM Credentials API (`iamcredentials.googleapis.com`)
   - Security Token Service API (`sts.googleapis.com`)

## Running the Script

The script is located in the `terraform/scripts` directory:

```bash
cd terraform
./scripts/setup_github_workload_identity.sh -p PROJECT_ID -g GITHUB_ORG/REPO
```

### Required Parameters

- `-p PROJECT_ID`: Your Google Cloud project ID
- `-g GITHUB_ORG/REPO`: Your GitHub repository in format 'org/repo'

### Optional Parameters

- `-r ROLES`: Comma-separated list of roles to grant (default: `roles/editor`)
- `-n NAME_PREFIX`: Prefix for resource names (default: `github`)
- `-s SERVICE_ACCOUNT`: Custom service account name (default: `NAME_PREFIX-actions`)

## Example Commands

### Basic Usage

```bash
./scripts/setup_github_workload_identity.sh -p vedavivi-dev -g blkops-collective/a2a-platform
```

### Custom Roles

```bash
./scripts/setup_github_workload_identity.sh \
  -p vedavivi-dev \
  -g blkops-collective/a2a-platform \
  -r roles/storage.admin,roles/compute.admin
```

### Custom Resource Names

```bash
./scripts/setup_github_workload_identity.sh \
  -p vedavivi-dev \
  -g blkops-collective/a2a-platform \
  -n a2a \
  -s a2a-terraform-deployer
```

## Using the Script Output

After running the script, you'll receive output like:

```
✅ Setup completed successfully!

📋 GitHub Actions Configuration:
------------------------------------
WORKLOAD_IDENTITY_PROVIDER: projects/************/locations/global/workloadIdentityPools/github-pool/providers/github-provider
SERVICE_ACCOUNT: <EMAIL>
PROJECT_ID: vedavivi-dev
```

Add these values as **repository secrets** in GitHub:

1. Go to your repository on GitHub
2. Navigate to Settings → Secrets and variables → Actions
3. Add the following secrets:
   - `WORKLOAD_IDENTITY_PROVIDER`: The full provider path shown in the output
   - `GCP_PROJECT_ID_DEV`: Your project ID
   - `GCP_SA_EMAIL`: The service account email

## Troubleshooting

### API Not Enabled

If you see an error about APIs not being enabled:

```bash
gcloud services enable iam.googleapis.com iamcredentials.googleapis.com sts.googleapis.com
```

### Permission Denied

If you see permission errors, ensure your account has the following roles:
- `roles/iam.serviceAccountAdmin`
- `roles/iam.workloadIdentityPoolAdmin`
- `roles/resourcemanager.projectIamAdmin`

### Existing Resources

If resources already exist, the script will handle this gracefully for the service account, but you may need to delete existing identity pools manually if you want to recreate them:

```bash
gcloud iam workload-identity-pools delete NAME_PREFIX-pool --location=global --project=PROJECT_ID
``` 