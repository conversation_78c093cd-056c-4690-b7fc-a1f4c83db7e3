# GCP Project Overview

This document provides an overview of the GCP infrastructure provisioned for the <PERSON>edavi<PERSON> project across different environments.

## Project Structure

Vedavivi uses three separate GCP projects for different environments:

1. **Development (`vedavivi-dev`)**: For feature development and testing
2. **Staging (`vedavivi-staging`)**: For integration testing and pre-production validation
3. **Production (`vedavivi-production`)**: For the live production environment

## Environment Configuration

| Resource | Development | Staging | Production |
|----------|------------|---------|------------|
| **Region** | us-central1 | us-central1 | us-central1 |
| **Compute** | e2-small | e2-medium | e2-standard-2 |
| **Database** | db-f1-micro | db-g1-small | db-custom-2-7680 |
| **Redis** | 1GB Basic | 1GB Basic | 4GB Basic |
| **Backups** | Basic | Basic | Enhanced |
| **High Availability** | No | No | Yes |

## Infrastructure Components

### Network Infrastructure

- **VPC Networks**: Isolated virtual networks for each environment
- **Subnets**: Dedicated subnets with appropriate IP ranges
- **Firewall Rules**:
  - SSH access (port 22)
  - Web traffic (ports 80, 443)
  - Internal communication between resources

### Compute Resources

- **Compute Engine Instances**: Running Debian 11
- **Machine Types**: Sized appropriately for each environment
- **Startup Scripts**: Basic configuration for web servers

### Database Resources

- **Cloud SQL**: PostgreSQL 13 databases
- **Availability**: Regional for production, zonal for others
- **Backups**: Daily backups with 7-day retention
- **Security**: SSL connections required

### Storage Resources

- **Cloud Storage Buckets**: For application data
- **Versioning**: Enabled on all buckets
- **Lifecycle Policies**: 90-day retention for objects

### Cache Infrastructure

- **Cloud Memorystore (Redis)**: For caching and session management
- **Memory**: Sized according to environment needs
- **Configuration**: LRU eviction policy

### Messaging Infrastructure

- **Pub/Sub Topics**: For event-driven architecture
- **Subscriptions**: Created based on application needs
- **Message Retention**: 7 days
- **Retry Policy**: Configurable with exponential backoff

## Security Measures

- **Service Accounts**: Following principle of least privilege
- **Workload Identity Federation**: For secure GitHub Actions integration
- **Private Networking**: Where appropriate
- **Encryption**: At rest and in transit

## CI/CD Integration

- **GitHub Actions**: Automated deployment pipeline
- **Terraform State**: Stored in dedicated GCS buckets
- **Environment Promotion**: Changes flow from dev → staging → production
- **Production Safeguards**: Manual approval required

## Monitoring and Maintenance

- **Drift Detection**: Regular checks for configuration drift
- **Logs**: Centralized logging with retention policies
- **Alerts**: Set up for critical resources and performance thresholds
- **Billing Alerts**: To prevent unexpected costs 