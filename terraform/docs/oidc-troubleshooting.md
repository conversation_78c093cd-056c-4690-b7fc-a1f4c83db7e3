# Troubleshooting OIDC Authentication for GitHub Actions

This guide helps resolve issues with Workload Identity Federation authentication in GitHub Actions workflows.

## Common Errors

### Error: "The target service indicated by the 'audience' parameters is invalid"

This error typically indicates one of the following issues:

1. The Workload Identity Provider has been deleted or disabled in GCP
2. The Workload Identity Provider doesn't exist
3. The service account may not have proper permissions

## Resolution Steps

### 1. Check if the OIDC Provider Exists

Run the following command to check if the Workload Identity Pool and Provider exist:

```bash
gcloud iam workload-identity-pools list --location=global
```

To check a specific pool and its providers:

```bash
gcloud iam workload-identity-pools providers list \
  --workload-identity-pool=github-pool \
  --location=global
```

### 2. Create/Recreate the OIDC Provider

We've created a script to automate this process:

```bash
cd terraform
./scripts/setup_gcp_terraform.sh
```

This script will:
- Enable required GCP APIs
- Create a GCS bucket for Terraform state if needed
- Run Terraform to create/recreate the OIDC provider
- Output the provider information needed for GitHub Actions

### 3. Update GitHub Repository Secrets

After running the setup script, update these GitHub repository secrets:

- `WORKLOAD_IDENTITY_PROVIDER`: The full provider path
- `GCP_SERVICE_ACCOUNT`: The service account email
- `GCP_PROJECT_ID`: Your GCP project ID
- `GCP_TF_STATE_BUCKET`: The GCS bucket for Terraform state

### 4. Attribute Conditions

Check that your GitHub workflow matches the attribute conditions defined in the OIDC provider:

1. Repository name must match the `github_repo` variable
2. Workflow must run from:
   - The `main` branch (`refs/heads/main`)
   - OR a tagged release (`refs/tags/*`)

## Manual Testing

You can test the OIDC provider configuration with:

```bash
gcloud iam workload-identity-pools create-cred-config \
  projects/PROJECT_NUMBER/locations/global/workloadIdentityPools/github-pool/providers/github-provider \
  --service-account=github-actions@PROJECT_ID.iam.gserviceaccount.com \
  --credential-source-file=token.json \
  --output-file=oidc_config.json
```

Where `token.json` contains a GitHub Actions OIDC token structure.

## Debugging Tips

1. Check GitHub Actions logs for specific error messages
2. Verify that the service account exists and has proper roles
3. Confirm the Workload Identity Pool and Provider exist
4. Validate that GitHub workflow meets the attribute conditions

## Common Terraform Errors

### Error: Duplicate backend configuration

If you see this error:
```
Error: Duplicate backend configuration

  on main.tf line 11, in terraform:
  11:   backend "gcs" {}

A module may have only one backend configuration. The backend was previously configured
at backend.tf:4,3-18.
```

This happens because there's a backend declaration in both `main.tf` and `backend.tf`. 
To fix this, use our backend switching script:

```bash
# Switch to local backend
./scripts/switch_backend.sh local

# OR switch to GCS backend
./scripts/switch_backend.sh gcs YOUR_BUCKET_NAME

# After making changes, run:
terraform init -reconfigure
```

The script properly comments out the backend declaration in `main.tf` and sets up the appropriate backend configuration. 