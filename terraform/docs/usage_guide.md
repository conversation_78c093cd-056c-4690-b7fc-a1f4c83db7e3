# Terraform Infrastructure Usage Guide

This guide explains how to use the Terraform infrastructure for Vedavivi's GCP resources.

## Initial Setup

### Prerequisites

Before you begin, ensure you have:

1. GCP access with appropriate permissions
2. Terraform (version >= 1.0.0) installed locally
3. Google Cloud SDK (gcloud) installed and configured
4. Git access to the repository

### First-time Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/your-org/your-repo.git
   cd your-repo
   ```

2. Run the setup script:
   ```bash
   ./terraform/scripts/setup_local_dev.sh
   ```
   This will:
   - Authenticate with GCP
   - Set the active project
   - Initialize Terraform
   - Create a workspace for your environment
   - Run a test plan

## Day-to-Day Operations

### Making Infrastructure Changes

1. Create a new feature branch:
   ```bash
   git checkout -b feature/add-new-resource
   ```

2. Make your changes to the Terraform files.

3. Test your changes:
   ```bash
   cd terraform
   terraform plan -var-file=environments/dev/terraform.tfvars
   ```

4. Apply changes locally if needed:
   ```bash
   terraform apply -var-file=environments/dev/terraform.tfvars
   ```

5. Commit and push your changes:
   ```bash
   git add .
   git commit -m "Add new resource X"
   git push origin feature/add-new-resource
   ```

6. Create a Pull Request to merge to `main`.

### The CI/CD Pipeline

When you create a PR, the GitHub Actions workflow will:

1. Run `terraform validate` and `terraform fmt` checks
2. Run a plan against the dev environment and post it to the PR
3. Allow for feedback and discussion

When your PR is merged to `main`:

1. Changes will be automatically applied to the staging environment
2. You can monitor the deployment in the GitHub Actions tab

For production deployments:

1. Go to the GitHub Actions tab
2. Find the "Terraform for GCP" workflow
3. Click "Run workflow"
4. Select "prod" from the environment dropdown
5. Click "Run workflow"
6. Approve the deployment when prompted

### Testing New Infrastructure

To verify your infrastructure is working correctly:

1. For compute instances:
   ```bash
   gcloud compute ssh instance-name --project=project-id
   ```

2. For databases:
   ```bash
   gcloud sql connect instance-name --user=admin --project=project-id
   ```

3. For storage buckets:
   ```bash
   gsutil ls gs://bucket-name
   ```

## Maintenance Tasks

### Detecting Infrastructure Drift

Run the drift detection script:
```bash
./terraform/scripts/detect_drift.sh staging
```

### Refreshing State

If Terraform state gets out of sync:
```bash
cd terraform
terraform refresh -var-file=environments/ENV/terraform.tfvars
```

### Managing Workspaces

List workspaces:
```bash
terraform workspace list
```

Switch workspace:
```bash
terraform workspace select ENV
```

## Troubleshooting

### Common Issues

#### Authentication Errors

If you see authentication errors:
```bash
gcloud auth application-default login
```

#### State Lock Issues

If a state lock persists:
```bash
terraform force-unlock LOCK_ID
```

#### Plan Shows Unexpected Changes

Try refreshing the state:
```bash
terraform refresh -var-file=environments/ENV/terraform.tfvars
```

### Getting Help

For more help:

1. Check the [Terraform documentation](https://www.terraform.io/docs)
2. Check the [GCP documentation](https://cloud.google.com/docs)
3. Contact the infrastructure team

## Security Best Practices

1. **Never** commit sensitive information like credentials
2. Use Workload Identity Federation for GitHub Actions
3. Follow the principle of least privilege
4. Regularly rotate credentials
5. Enable audit logging for resources 