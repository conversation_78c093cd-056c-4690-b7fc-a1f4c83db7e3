# GCP Project Setup Guide

This guide outlines the steps to set up your GCP projects and configure them for use with Terraform and GitHub Actions.

## 1. Create GCP Projects

Create three separate projects for each environment:

1. **Development (Dev)**: `vedavivi-dev`
2. **Staging**: `vedavivi-staging`
3. **Production**: `vedavivi-production`

```bash
# For each environment (replace ENV with dev, staging, production)
gcloud projects create vedavivi-ENV --name="Vedavivi ENV"
```

## 2. Enable Required APIs

For each project, enable the necessary APIs:

```bash
# Set the project
gcloud config set project vedavivi-ENV

# Enable required APIs
gcloud services enable compute.googleapis.com
gcloud services enable sqladmin.googleapis.com
gcloud services enable iam.googleapis.com
gcloud services enable storage.googleapis.com
gcloud services enable redis.googleapis.com
gcloud services enable pubsub.googleapis.com
gcloud services enable cloudresourcemanager.googleapis.com
gcloud services enable iam.googleapis.com
gcloud services enable serviceusage.googleapis.com
gcloud services enable iamcredentials.googleapis.com
gcloud services enable workloadidentity.googleapis.com
```

## 3. Create Terraform State Bucket

Create a GCS bucket to store Terraform state in each project:

```bash
# Replace ENV with dev, staging, or production and UNIQUE_SUFFIX with a globally unique identifier
gsutil mb -l us-central1 gs://vedavivi-tf-state-ENV-UNIQUE_SUFFIX/

# Enable versioning on the bucket
gsutil versioning set on gs://vedavivi-tf-state-ENV-UNIQUE_SUFFIX/
```

## 4. Set Up Workload Identity Federation

Set up Workload Identity Federation to allow GitHub Actions to authenticate to GCP without using service account keys:

```bash
# Create a Workload Identity Pool
gcloud iam workload-identity-pools create github-pool \
  --location="global" \
  --description="Pool for GitHub Actions" \
  --display-name="GitHub Pool"

# Get the Workload Identity Pool ID
WORKLOAD_IDENTITY_POOL_ID=$(gcloud iam workload-identity-pools describe github-pool \
  --location="global" \
  --format="value(name)")

# Create a Workload Identity Provider in the Pool
gcloud iam workload-identity-pools providers create-oidc github-provider \
  --location="global" \
  --workload-identity-pool="github-pool" \
  --display-name="GitHub provider" \
  --attribute-mapping="google.subject=assertion.sub,attribute.actor=assertion.actor,attribute.repository=assertion.repository" \
  --issuer-uri="https://token.actions.githubusercontent.com"
```

## 5. Create Service Accounts

Create a service account in each project for GitHub Actions to use:

```bash
# Create the service account
gcloud iam service-accounts create github-actions \
  --display-name="GitHub Actions Service Account" \
  --description="Used by GitHub Actions for Terraform deployments"

# Grant necessary roles (adjust according to your needs)
gcloud projects add-iam-policy-binding vedavivi-ENV \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/editor"

# Add additional specific roles as needed
gcloud projects add-iam-policy-binding vedavivi-ENV \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/storage.admin"

gcloud projects add-iam-policy-binding vedavivi-ENV \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/compute.admin"

gcloud projects add-iam-policy-binding vedavivi-ENV \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/cloudsql.admin"
```

## 6. Allow the Workload Identity Provider to Impersonate the Service Account

```bash
gcloud iam service-accounts add-iam-policy-binding \
  <EMAIL> \
  --role="roles/iam.workloadIdentityUser" \
  --member="principalSet://iam.googleapis.com/${WORKLOAD_IDENTITY_POOL_ID}/attribute.repository/YOUR_GITHUB_ORG/YOUR_GITHUB_REPO"
```

## 7. Configure GitHub Repository Secrets

In your GitHub repository, add the following secrets:

- `GCP_PROJECT_ID_DEV`: `vedavivi-dev`
- `GCP_PROJECT_ID_STAGING`: `vedavivi-staging`
- `GCP_PROJECT_ID_PRODUCTION`: `vedavivi-production`
- `WORKLOAD_IDENTITY_PROVIDER`: The full identifier of your Workload Identity Provider

## 8. Configure GitHub Environments

In your GitHub repository settings, create three environments:

1. **dev**: No protection rules
2. **staging**: No protection rules
3. **production**: Add protection rules
   - Required reviewers: Add your team members
   - Wait timer: 10 minutes

## 9. Create VPC Service Controls (Optional but Recommended for Production)

For production, consider setting up VPC Service Controls for additional security:

```bash
# Create a service perimeter for the production project
gcloud access-context-manager perimeters create vedavivi-prod-perimeter \
  --title="Vedavivi Production" \
  --resources=projects/vedavivi-prod \
  --restricted-services=storage.googleapis.com,sqladmin.googleapis.com,redis.googleapis.com
```

## 10. Set Up Billing Alerts

Set up billing alerts to prevent unexpected costs:

```bash
# Create a budget for each project
gcloud billing budgets create \
  --billing-account=YOUR_BILLING_ACCOUNT_ID \
  --display-name="Vedavivi ENV Budget" \
  --budget-amount=1000USD \
  --threshold-rules=threshold-percent=0.5 \
  --threshold-rules=threshold-percent=0.75 \
  --threshold-rules=threshold-percent=0.9 \
  --threshold-rules=threshold-percent=1.0 \
  --projects=vedavivi-ENV
``` 