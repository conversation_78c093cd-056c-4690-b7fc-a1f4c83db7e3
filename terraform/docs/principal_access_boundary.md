# Principal Access Boundaries for GitHub Actions

This document explains how to use Principal Access Boundaries (PABs) to enhance security for GitHub Actions service accounts.

## What are Principal Access Boundaries?

Principal Access Boundaries (PABs) provide an additional layer of security beyond IAM permissions. While IAM defines "who can do what," PABs define "who can access which resources." This allows you to:

- Limit a service account to access only specific resources
- Provide defense-in-depth security against compromised credentials
- Enforce the principle of least privilege

## Setting Up Principal Access Boundaries

We've provided a script to easily set up PABs for your GitHub Actions service accounts:

### Prerequisites

1. **Access Context Manager API** must be enabled:
   ```bash
   gcloud services enable accesscontextmanager.googleapis.com
   ```

2. **Organization Admin** permissions or at least:
   - Access Context Manager Admin
   - Organization Policy Administrator

### Creating a PAB Policy

Run the script with the following parameters:

```bash
./scripts/setup_pab_policy.sh -p PROJECT_ID -s SERVICE_ACCOUNT_EMAIL [-n POLICY_NAME] [-r RESOURCE_LIST]
```

Parameters:
- `-p PROJECT_ID`: Your GCP project ID (required)
- `-s SERVICE_ACCOUNT_EMAIL`: The service account email to restrict (required)
- `-n POLICY_NAME`: Optional name for the PAB policy (default: "github-actions-pab")
- `-r RESOURCE_LIST`: Comma-separated list of resources to allow (default: all resources)

### Example Usage

#### Basic Usage (All Resources)

```bash
./scripts/setup_pab_policy.sh \
  -p vedavivi-dev \
  -s <EMAIL>
```

#### Restricted Resource Access

Limit access to only GCS and Compute resources:

```bash
./scripts/setup_pab_policy.sh \
  -p vedavivi-dev \
  -s <EMAIL> \
  -r "//storage.googleapis.com/,//compute.googleapis.com/"
```

#### Custom Policy Name

```bash
./scripts/setup_pab_policy.sh \
  -p vedavivi-dev \
  -s <EMAIL> \
  -n "github-terraform-pab"
```

## Resource Format

When specifying resources with the `-r` parameter, use the following format:

- `//SERVICE_NAME/`: Restricts to a specific service (e.g., `//storage.googleapis.com/`)
- `//SERVICE_NAME/projects/PROJECT_ID/...`: Restricts to specific resources

Examples:
- `//storage.googleapis.com/projects/PROJECT_ID/buckets/BUCKET_NAME`: For a specific bucket
- `//compute.googleapis.com/projects/PROJECT_ID/zones/ZONE`: For specific compute zone

## Managing PAB Policies

### Listing Policies and Bindings

```bash
# List PAB policies
gcloud access-context-manager policies list --organization=ORGANIZATION_ID

# List PAB bindings
gcloud access-context-manager principal-access-bounds bindings list --organization=ORGANIZATION_ID
```

### Deleting a PAB Binding

```bash
gcloud access-context-manager principal-access-bounds bindings delete BINDING_ID --organization=ORGANIZATION_ID
```

### Deleting a PAB Policy

```bash
gcloud access-context-manager policies delete POLICY_ID
```

## Security Best Practices

1. **Narrowly Scope Resources**: Only allow access to resources that are actually needed
2. **Use With IAM**: PABs complement IAM; they don't replace it
3. **Regularly Audit**: Periodically review which resources are allowed
4. **Test Before Deployment**: Test in non-production environments first 