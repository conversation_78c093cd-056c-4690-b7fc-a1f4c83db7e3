# ADR-001: A2A Platform Architecture Overview

## Status

Accepted

## Date

2025-05-04

## Context

The Agent-to-Agent (A2A) Platform requires a robust architecture that can support:
- Flexible agent interactions and delegations
- Secure execution environments
- Scalable communication between agents
- Appropriate data persistence strategies for different types of information
- Modern CI/CD practices

This ADR captures the high-level architectural decisions for building the A2A Platform based on the requirements outlined in the PRD and User Stories.

## Decision

We will implement an architecture with the following key components:

### 1. Agent Roles & Interaction Model

* **User Agent (Orchestrator):** Each end-user is assigned a dedicated User Agent. This agent acts as the primary interface, receiving high-level objectives from the user (via the frontend). Its core responsibility is to interpret the objective, maintain user context/preferences (memory), and orchestrate the execution by delegating tasks.
* **Specialized Agents (Delegates):** A pool of specialized agents exists, each designed for specific tasks (e.g., `TicketScraperAgent`, `CalendarAgent`, `SummarizationAgent`). These agents receive task requests, execute them, and return results. They encapsulate specific capabilities and can be independently developed, deployed, and potentially vetted/rated.
* **Interaction Flow:** User -> Frontend -> User Agent -> [Delegation via Communication Backbone] -> Specialized Agent(s) -> [Results via Communication Backbone] -> User Agent -> Frontend -> User.

#### Agent Roles Flowchart

```mermaid
graph TD
    subgraph User Interaction
        UI[User Interface]
    end

    subgraph Agent Core
        UA["User Agent (Orchestrator)"]
        SA{{"Specialized Agents (Delegates)"}}
    end

    subgraph Communication
        CB[Communication Bus]
    end

    UI -- High-Level Objective --> CB
    CB -- Objective --> UA

    UA -- Interprets Objective --> UA
    UA -- Maintains Context/Memory --> UA
    UA -- Delegates Tasks --> CB
    CB -- Specific Task Request --> SA

    SA -- Executes Task --> SA
    SA -- Returns Result/Status --> CB
    CB -- Result/Status --> UA

    UA -- Processes Results --> UA
    UA -- Updates UI/User --> CB
    CB -- Status/Result Update --> UI

    style UA fill:#9cf,stroke:#333,stroke-width:2px,color:#333
    style SA fill:#6fc,stroke:#333,stroke-width:2px,stroke-dasharray: 5 5,color:#333
    style CB fill:#fcc,stroke:#333,stroke-width:1px,color:#333
    style UI fill:#ccf,stroke:#333,stroke-width:1px,color:#333
```

#### Agent Delegation Sequence Diagram

```mermaid
sequenceDiagram
    participant UA as User Agent (Orchestrator)
    participant CB as Communication Bus
    participant SA1 as Specialized Agent 1 (e.g., Search)
    participant SA2 as Specialized Agent 2 (e.g., Filter)

    UA->>UA: Receive & Interpret Objective
    UA->>UA: Plan Execution Steps (Requires Search & Filter)

    UA->>+CB: Publish Task 1 (Search Request)
    CB->>+SA1: Deliver Task 1
    SA1->>SA1: Execute Search Task
    SA1->>+CB: Publish Result 1 (Search Results)
    CB->>+UA: Deliver Result 1
    UA->>UA: Process Search Results

    opt Task 2 Depends on Result 1
        UA->>+CB: Publish Task 2 (Filter Request w/ Results)
        CB->>+SA2: Deliver Task 2
        SA2->>SA2: Execute Filter Task
        SA2->>+CB: Publish Result 2 (Filtered Results)
        CB->>+UA: Deliver Result 2
        UA->>UA: Process Filtered Results
    end

    UA->>UA: Consolidate Results, Update Objective Status
    UA->>-CB: Ack Result 1 (Optional)
    UA->>-CB: Ack Result 2 (Optional)
```

### 2. Communication Backbone

* **Asynchronous & Event-Driven:** An asynchronous message queue system (e.g., Google Cloud Pub/Sub, Kafka) will serve as the central communication hub.
    * User objectives are sent as messages to the User Agent's queue.
    * Delegated tasks are published as messages to queues monitored by Specialized Agents.
    * Results and status updates are published back to the relevant User Agent queue or shared topics.
* **Decoupling & Resilience:** This approach decouples agents, allowing them to operate independently and asynchronously. It enhances system resilience, as the failure of one agent type is less likely to cascade. It directly supports the reactive nature required for agents.
* **Protocol:** Standardized message formats/schemas will be defined for inter-agent communication, potentially drawing from concepts in Google's A2A protocol or using simple REST/gRPC for specific synchronous needs if required.

### 3. Data Persistence Layer

A layered approach using purpose-built databases, ensuring strict data isolation per user where applicable:

* **Objectives & Short-Term Memory:** A **[PostgreSQL Database](https://www.postgresql.org/)** will store user objectives (parameters, status, metadata) and conversational/intermediate state (short-term memory). Chosen for reliability, transactional integrity, and rich feature set including JSON support for flexible schemas when needed.
* **Long-Term Semantic Memory:** A **Vector Database** (e.g., [Vertex AI Vector Search](https://cloud.google.com/vertex-ai) or [Pinecone](https://www.pinecone.io/)) will store embeddings of past interactions and learned information, enabling semantic search capabilities for richer agent memory.
* **Personal Knowledge Graphs:** A dedicated **[Graph Database](https://neo4j.com/)** (e.g., Neo4j) per user will store structured entities and relationships derived from user interactions and objectives, enabling complex reasoning and personalization.

### 4. Execution Environment & Deployment

* **Containerization:** All agents (User and Specialized) will be packaged as container images (using Docker).
* **CI/CD:** **[Dagger](https://dagger.io/)** will be used to define reproducible build environments and pipelines for building, testing, and packaging agent containers.
* **Hosting & Orchestration:** Agents will be deployed on a scalable container platform (e.g., **[Google Cloud Run](https://cloud.google.com/run)** for event-driven workloads, potentially GKE for more complex needs). User Agents may run as dedicated instances per user, while Specialized Agents can scale as shared services based on load.
* **Security:** Execution leverages **[Google ADK's](https://cloud.google.com/adk)** security features (sandboxing, guardrails, identity) within the containers. Secure authentication/authorization (e.g., IAM, service accounts, tokens) will be enforced for all internal API calls and database access.

### 5. Key Technologies Summary

* **Frontend:** ReactJS, TypeScript, ShadCN
* **Backend/Agents:** Python, Golang, Google ADK
* **Communication:** Message Queue ([Pub/Sub](https://cloud.google.com/pubsub) or [Kafka](https://kafka.apache.org/)), potentially REST/gRPC
* **Databases:** PostgreSQL (primary data store), Vector DB (Vertex AI Vector Search/Pinecone), Graph DB (Neo4j)
* **CI/CD & Build:** Dagger, Docker
* **Cloud Platform:** Google Cloud (Cloud Run/GKE, IAM, Logging, Monitoring, Secret Manager)

## Consequences

### Positive

- **Modularity and Flexibility:** The separation of User Agents and Specialized Agents allows for independent development, deployment, and scaling of different agent capabilities.
- **Resilience:** The asynchronous, message-based communication reduces tight coupling and enhances system resilience.
- **Scalability:** The containerized deployment model with Cloud Run/GKE provides a strong foundation for scaling the platform as user demand grows.
- **Purpose-built Storage:** Using different database technologies for different data types allows us to optimize for specific access patterns and query needs.
- **Security by Design:** Leveraging Google ADK's security features and cloud-native security practices addresses critical requirements around secure execution and data isolation.
- **PostgreSQL Benefits:** Improved data reliability, ACID compliance, and rich feature set compared to document stores.
- **Language Diversity:** Supporting both Python and Golang enables using the right tool for specific agent implementation needs.

### Negative

- **Operational Complexity:** Managing multiple database systems and a distributed message queue increases operational overhead.
- **Learning Curve:** The team will need to become proficient with multiple technologies and patterns.
- **Development Coordination:** Careful attention to API design and message schemas will be needed to ensure proper communication between components.
- **Potential Costs:** Using multiple specialized cloud services may increase hosting costs compared to a simpler architecture.
- **Cross-language Integration:** Additional complexity from integrating components written in different programming languages.

### Risks & Mitigations

- **Risk:** Performance bottlenecks in inter-agent communication
  - **Mitigation:** Implement monitoring, load testing, and optimization of message processing

- **Risk:** Data synchronization challenges across multiple databases
  - **Mitigation:** Implement clear data ownership boundaries and consistent event-driven update patterns

- **Risk:** Security vulnerabilities in the communication backbone
  - **Mitigation:** Implement comprehensive authentication, encryption, and access controls for all communication channels
