## **VEDAVIVI: MVP Backend Technical Plan**

### **1. Introduction**

This document outlines the technical plan and specifications for the backend systems supporting the Minimum Viable Product (MVP) of the **VEDAVIVI** platform. The primary goal of the MVP is to deliver the core value proposition: providing an individual with a personalized, intelligent agent to simplify daily life and empower them, focusing initially on capabilities like chat interaction, objective/task assistance, and context awareness through select integrations.

The guiding principle throughout this MVP plan is **"simpler and cost-effective,"** ensuring we build a robust foundation without unnecessary complexity, allowing for rapid iteration and validation.

Key technology choices and standards for the MVP backend include:

* **Primary Database:** PostgreSQL for structured relational data.
* **Authentication & User Management:** Clerk.com.
* **Primary Client API:** GraphQL (including Queries, Mutations, and Subscriptions for real-time). Investigation into tooling like Apollo Platform or GraphQL Hive is required.
* **Real-time Communication:** Via GraphQL Subscriptions over WebSockets.
* **Caching/Ephemeral State:** Redis (for WebSocket connections, webhook idempotency).
* **File Storage:** Cloud Object Storage (AWS S3, Google Cloud Storage, or Cloudflare R2) served via CDN from Day 1\.
* **Asynchronous Processing:** Persistent Message Queues (e.g., AWS SQS, Google Pub/Sub) for webhooks, user data cleanup, and async Agent-to-Agent (A2A) tasks.
* **Internal Communication (MVP):** Direct HTTP/REST APIs (OpenAPI defined) for synchronous calls; Message Queues for asynchronous initiation; GraphQL Subscriptions/WebSockets potentially for async responses. Agent-to-Agent (A2A) payloads use the provided a2a-schema.json structure.
* **API Standard (External Callbacks/Internal Sync):** OpenAPI Specification for incoming webhooks, OAuth callbacks, and internal synchronous HTTP APIs.
* **Deployment:** Cloud-native, containerized deployment assumed.

*(See Section 5: Cross-Cutting Concerns & Future Considerations for planned Post-MVP technologies like Kafka, gRPC, Protobuf, etc.)*

### **2. Core Backend Functional Areas (MVP Scope & Implementation)**

*(Content reflects decisions from v1.4, emphasizing GraphQL for client interactions)*

#### **2.1. User Profile Management *(Using Clerk.com)***

* **Goal (MVP):** Manage essential user identity (via Clerk) and basic VEDAVIVI-specific preferences.
* **Key Features/Decisions (MVP):** Core profile managed by Clerk.com. VEDAVIVI-specific data (timezone, preferences) stored in VEDAVIVI's users table, managed via GraphQL API. Minimal VEDAVIVI users record created upon first Clerk authentication (triggered by user.created webhook). Users read/update VEDAVIVI-specific profile data via GraphQL. Self-service deletion via Clerk triggers VEDAVIVI's async cleanup job via user.deleted webhook.
* **Backend Implementation:** Webhook handler for Clerk events. Logic to create/update minimal VEDAVIVI users record. GraphQL resolvers for VEDAVIVI-specific fields. Async job queue/worker for deletion cleanup. Simplified PostgreSQL users table.
* **Deferred Post-MVP:** Complex profile fields beyond basic VEDAVIVI needs.

#### **2.2. User Authentication and Authorization *(Using Clerk.com)***

* **Goal (MVP):** Securely authenticate users via methods managed by Clerk and authorize access to VEDAVIVI resources.
* **Key Features/Decisions (MVP):** Clerk.com manages auth flows (Google, GitHub, Email+Link). Clerk manages sessions (JWTs). VEDAVIVI backend validates Clerk session tokens for GraphQL requests/subscriptions. Terminal Client Auth via web-generated VEDAVIVI CLI Tokens (hashed storage), managed via GraphQL, validated by VEDAVIVI backend. Authorization is OBAC based on clerk\_user\_id. Agents identified via custom claims in Clerk session token.
* **Backend Implementation:** Clerk Backend SDK integration for token validation. Middleware extracts user info. VEDAVIVI backend manages cli\_tokens table and validation. Agent background auth workflow implemented (VEDAVIVI JWT based on Clerk session). Logic abstracted.
* **Deferred Post-MVP:** Advanced Clerk features (Roles, MFA).

#### **2.3. Assistant Management (Includes Internal Tasks)**

* **Goal (MVP):** Create and configure the user's single personal assistant and manage its associated tasks internally.
* **Key Features/Decisions (MVP):** One assistant per user. Mandatory creation step post-login (GraphQL createAssistant). Users update via GraphQL updateAssistant. Objectives defined via chat (persisted via context/summaries). No predefined personality. "Lazy Connection" for external services (OAuth flow). Assistant deleted via async user deletion. Internal Task Management via tasks table; interaction via chat.
* **Backend Implementation:** GraphQL resolvers for assistant CRUD. assistants table. Internal APIs for agent task management. external\_credentials table (encrypted). Async job for deletion. (Fast Follow: GraphQL generateAssistantBackstory query).
* **Deferred Post-MVP:** Multiple assistants, advanced configs, explicit connection UI, external task managers.

#### **2.4. File Uploads (CDN Day 1\)**

* **Goal (MVP):** Allow user uploads, store securely, serve efficiently via CDN.
* **Key Features/Decisions (MVP):** Object Storage. Direct uploads via presigned URLs (GraphQL requestFileUploadUrl). Metadata stored (file\_metadata table, confirmed via GraphQL completeFileUpload). Backend validates type/size. Sync backend avatar resizing. Access via CDN Day 1 using signed CDN URLs (retrieved via GraphQL).
* **Backend Implementation:** GraphQL mutations/resolvers for upload workflow. API endpoint/logic generating signed CDN URLs. Image processing logic. CDN configuration. file\_metadata table.
* **Deferred Post-MVP:** File uploads from CLI, advanced file management, virus scanning.

#### **2.5. Chat Interface Support**

* **Goal (MVP):** Enable real-time, persistent chat between user and assistant.
* **Key Features/Decisions (MVP):** Real-time via **GraphQL Subscriptions** over WebSockets. Clients send messages via **GraphQL mutation sendMessage**. Backend manages WebSocket connections for GraphQL, authenticated via Clerk or VEDAVIVI CLI Token. Connections tracked in **Redis**. L7 ALB handles initial connection. Context management as defined. History stored in chat\_messages, conversations tables.
* **Backend Implementation:** GraphQL Subscription implementation. Redis integration. Message routing. conversations, chat\_messages tables.
* **Deferred Post-MVP:** Complex context management, agent state machines.

#### **2.6. VEDAVIVI Agent-to-Agent (A2A) Communication**

* **Goal (MVP):** Enable basic communication between user's PA and internal SAs.
* **Scope (MVP):** Internal PA \<-\> SA only.
* **Protocol/Data Format:** Adopts structures from a2a-schema.json, including standardized user context propagation.
* **User Context Propagation:** All A2A communication includes a `user_context` block containing `user_id`, `initiating_agent_id`, and `request_timestamp`. This enables SAs to operate within the correct user context and with appropriate permissions.
* **Transport (MVP):** Supports both: Synchronous (Internal HTTP POST APIs, **OpenAPI defined**) and Asynchronous (Message Queue). Async responses via WebSocket (GraphQL Subscriptions) or stored for polling.
* **Discovery (MVP):** Simple internal registered\_agents table.
* **Authentication/Authorization (MVP):** Internal service-to-service auth. Propagate user\_id and agent\_id. SAs operate on data in requests.
* **Backend Implementation:** Internal APIs (OpenAPI), message queue integration, agent registry table/logic.
* **Deferred Post-MVP:** Agent Marketplace, external agent communication, complex async responses, delegated SA auth.

#### **2.7. (Agent) Webhook Support (Incoming Only)**

* **Goal (MVP):** Allow agents to react reliably to incoming events from Slack, Calendly, Google Calendar.
* **Architecture:** Queue-based. Endpoint (**OpenAPI defined**) validates URL & source security, queues payload, returns 200 OK. Workers consume, check idempotency (**Redis**), execute logic, handle retries.
* **Security Validation (MVP):** Source-specific (Slack/Calendly signatures; Google headers). HTTPS mandatory.
* **Backend Implementation:** Webhook receiver endpoint (OpenAPI). Queue integration. Workers. Redis for idempotency. webhook\_registrations table (encrypted secrets).
* **Deferred Post-MVP:** Generic webhooks, outgoing webhooks, user config UI.

#### **2.8. Terminal Client Support**

* **Goal (MVP):** Allow core chat and basic management via CLI.
* **Authentication:** Via web-generated **VEDAVIVI CLI Tokens**. Backend API (**GraphQL**) manages token CRUD; backend middleware validates tokens (hashed).
* **Supported Functionality (MVP via CLI):** Chat (GraphQL Subscriptions/Mutations over WebSockets using CLI Token auth). View/Update User/Assistant (GraphQL queries/mutations).
* **Backend Implementation:** GraphQL API serves CLI needs. Validation middleware for VEDAVIVI CLI Tokens.
* **Deferred Post-MVP:** Initial signup via CLI, file uploads from CLI, complex config via CLI.

#### **2.9. User Data Transparency**

* **Goal (MVP):** Build trust by allowing users to view their core explicit data.
* **Data Scope (MVP):** User Profile (Clerk \+ VEDAVIVI DB), Assistant Config, Active Objectives, Connected Services list.
* **Interface (MVP):** Web application UI, data fetched via **GraphQL query myDataOverview**.
* **Backend Implementation:** GraphQL query resolver aggregates data from Clerk and VEDAVIVI DB.
* **Deferred Post-MVP:** Transparency into learned data, data export feature, CLI access.

#### **2.10. Assistant Objective Management (Includes Internal Tasks)**

* **Goal (MVP):** Explicitly track objectives identified via chat; manage associated tasks internally.
* **Objectives:** Defined via chat, stored explicitly by agent logic in assistant\_objectives table (status managed via chat). Visible via Data Transparency query. Passed to AI context.
* **Tasks:** Managed **internally**. Stored in tasks table. Agent logic performs CRUD via internal APIs based on chat/planning. Enables dynamic prioritization.
* **User Interaction (MVP):** Via **conversation** with the agent.
* **Backend Implementation:** assistant\_objectives table, tasks table, internal APIs (likely OpenAPI defined) for agent task management. Agent logic for extraction/management.
* **Deferred Post-MVP:** External task manager integration, dedicated UI for objectives/tasks.

### **3. Architecture Diagram (MVP)**

```mermaid
graph LR
    %% Define Actors and Clients
    User(User)
    subgraph Clients
        WebUI[Web UI]
        CLI[CLI Client]
        Webhook[Webhook]
    end

    %% External Services
    Clerk[Clerk.com AuthN/AuthZ]

    %% Backend System Boundary
    subgraph Vedavivi_Backend_Cloud_Environment
        direction LR

        CDN[CDN]
        GraphqlGateway[GraphQL Gateway Queries, Mutations, Subscriptions]

        subgraph CommunicationServices
            A2AService[A2A Comm Service Internal PA-SA, Sync/Async]
            MsgQueue[(Message Queue SQS/PubSub)]
        end

        subgraph TaskMgmtServices
            TaskMgmtService[Task Mgmt Service Internal Tasks]
            TaskMgmtPostgresDB[(PostgreSQL DB)]
            TaskMgmtRedisCache[(Redis Cache)]
        end

        subgraph AssistantServices
            AssistantService[Assistant Service Manages Assistant Config, Objectives]
            AssistantPostgresDB[(PostgreSQL DB)]
            AssistantRedisCache[(Redis Cache)]
        end

        subgraph ConsumerServices
            AuthService[Auth Service Validates Tokens, Manages CLI Tokens]
            AuthPostgresDB[(PostgreSQL DB)]
        end

        subgraph ObjectStore
            ObjectStorage[(Cloud Object Storage S3/GCS/R2)]
        end

        %% AsyncProcessor[Async Job Processor]

    end

    %% Connections
    User --> WebUI
    User --> CLI
    User -- Auth --> Clerk
    User -- HTTPS/OpenAPI --> Webhook
    WebUI -- HTTPS/WSS --> CDN
    CLI -- HTTPS/WSS --> CDN
    Webhook -- webhook --> CDN

    CDN[CDN] -- Req/Res <--> GraphqlGateway
    CDN[CDN] -- webhook --> A2AService

    GraphqlGateway -- Validates Session <--> AuthService
    GraphqlGateway -- Query <--> AssistantService
    GraphqlGateway -- Query <--> TaskMgmtService

    AuthService -- Uses --> Clerk
    AuthService -- Reads/Writes User Data --> AuthPostgresDB
    AuthService -- Reads/Writes Tokens --> AuthPostgresDB
    AuthService -- Consume/Produce <--> MsgQueue

    AssistantService -- Assistant/Objective Data <--> AssistantPostgresDB
    AssistantService -- Interacts --> TaskMgmtService
    AssistantService -- Consume/Produce --> MsgQueue
    AssistantService -- Reads/Writes --> ObjectStorage
    AssistantService -- Reads/Writes --> AssistantRedisCache[(Redis Cache)]

    TaskMgmtService -- Reads/Writes Task Data <--> TaskMgmtPostgresDB
    TaskMgmtService -- Conume/Produce <--> MsgQueue
    TaskMgmtService -- Reads/Writes --> ObjectStorage
    TaskMgmtService -- Reads/Writes --> TaskMgmtRedisCache[(Redis Cache)]

    A2AService -- Consume/Produce <--> MsgQueue
    %%A2AService -- Reads Agent Registry --> PostgresDB

    %%AsyncProcessor -- Reads Jobs --> MsgQueue
    %%AsyncProcessor -- Processes Job (e.g., User Cleanup) --> PostgresDB

    %% Data Store Connections from Services (Simplified)
    %%Services -- Reads/Writes --> PostgresDB
    %%Services -- Reads/Writes --> RedisCache
    %%Services -- Reads/Writes --> ObjectStorage
    %%Services -- Sends/Receives Messages --> MsgQueue

    ObjectStorage -- Served Via --> CDN
```

### **4. Primary API: GraphQL (MVP)**

* **Standard:** GraphQL is the primary API interface for client applications (Web, CLI) to query data, perform mutations, and receive real-time updates via **Subscriptions** (over WebSockets). Sending chat messages is done via a GraphQL mutation.
* **Tooling:** Investigation into server-side GraphQL tooling (e.g., **Apollo Platform**, **GraphQL Hive**) is required.
* **Schema Overview:** The GraphQL schema defines the capabilities exposed to clients. Key components include:
  * **Core Object Types:** User, Assistant, Conversation, ChatMessage, FileMetadata, AssistantObjective, Task, ExternalCredentialInfo, CliToken. (Includes related types like MessagePart interface, TextPart, FilePart, FileInfo, UserDataOverview).
  * **Key Queries:** me, myAssistant, conversation, myConversations, conversationMessages, myObjectives, myTasks, myCliTokens, myExternalCredentials, myDataOverview, generateAssistantBackstory.
  * **Key Mutations:** createAssistant, updateAssistant, updateMyA2AProfile, requestFileUploadUrl, completeFileUpload, createCliToken, revokeCliToken, deleteMyAccount, sendMessage.
  * **Key Subscriptions:** newMessages(conversationId: UUID\!).
  * *(Note: The full Schema Definition Language (SDL) provides the detailed specification and will be maintained alongside the implementation).*

### **4. Data Model (MVP \- PostgreSQL & Redis)**

*(This section details the PostgreSQL table schemas using the standard table format requested. Primary Key Convention: id UUID unless otherwise noted. Encryption/Hashing noted where critical.)*

**users table**
(Stores VEDAVIVI-specific data linked to Clerk user identity)

| Column Name | Data Type | Constraints | Notes |
| :---- | :---- | :---- | :---- |
| id | UUID | PRIMARY KEY, DEFAULT gen\_random\_uuid() | Unique identifier for the VEDAVIVI user record. |
| clerk\_user\_id | TEXT | UNIQUE, NOT NULL | ID from Clerk.com for user management. |
| timezone | TEXT | NULL | User's preferred timezone. |
| preferences | JSONB | NULL, DEFAULT '{}'::jsonb | VEDAVIVI-specific preferences in JSON format. |
| settings | JSONB | NULL, DEFAULT '{}'::jsonb | VEDAVIVI-specific settings in JSON format. |
| created\_at | TIMESTAMPTZ | NOT NULL, DEFAULT now() | Timestamp of VEDAVIVI user record creation. |
| updated\_at | TIMESTAMPTZ | NOT NULL, DEFAULT now() | Timestamp of the last update to the VEDAVIVI record. |
| *Indexes:* |  | UNIQUE(clerk\_user\_id) |  |

**assistants table**
(Stores configuration for the user's single assistant)

| Column Name | Data Type | Constraints | Notes |
| :---- | :---- | :---- | :---- |
| id | UUID | PRIMARY KEY, DEFAULT gen\_random\_uuid() | Unique identifier for the assistant. |
| user\_id | UUID | NOT NULL, UNIQUE, REFERENCES users(id) ON DELETE CASCADE | The owner user (VEDAVIVI ID). UNIQUE enforces 1-to-1. |
| name | TEXT | NOT NULL | User-defined name for the assistant. |
| backstory | TEXT | NOT NULL | User-defined backstory for the assistant. |
| avatar\_file\_id | UUID | NULL, REFERENCES file\_metadata(id) ON DELETE SET NULL | Optional link to the assistant's avatar file. |
| configuration | JSONB | NULL, DEFAULT '{}'::jsonb | For future settings. |
| created\_at | TIMESTAMPTZ | NOT NULL, DEFAULT now() | Timestamp of creation. |
| updated\_at | TIMESTAMPTZ | NOT NULL, DEFAULT now() | Timestamp of last update (needs trigger/logic). |
| *Indexes:* |  | UNIQUE(user\_id) |  |

**conversations table**
(Groups related chat messages)

| Column Name | Data Type | Constraints | Notes |
| :---- | :---- | :---- | :---- |
| id | UUID | PRIMARY KEY, DEFAULT gen\_random\_uuid() | Unique identifier for the conversation thread. |
| user\_id | UUID | NOT NULL, REFERENCES users(id) ON DELETE CASCADE | The user participant (VEDAVIVI ID). |
| assistant\_id | UUID | NOT NULL, REFERENCES assistants(id) ON DELETE CASCADE | The assistant participant. |
| created\_at | TIMESTAMPTZ | NOT NULL, DEFAULT now() | Timestamp when the conversation started. |
| last\_message\_at | TIMESTAMPTZ | NULL | Timestamp of the last message (useful for sorting). |
| *Indexes:* |  | (user\_id, assistant\_id, last\_message\_at), (last\_message\_at) | For efficiently finding and sorting conversations. |

**chat_messages table**
(Stores individual chat messages)

| Column Name | Data Type | Constraints | Notes |
| :---- | :---- | :---- | :---- |
| id | UUID | PRIMARY KEY, DEFAULT gen\_random\_uuid() | Unique identifier for the message. |
| conversation\_id | UUID | NOT NULL, REFERENCES conversations(id) ON DELETE CASCADE | Links message to a specific conversation. |
| sender\_role | TEXT | NOT NULL, CHECK (sender\_role IN ('user', 'agent')) | Who sent the message ('user' or 'agent'). |
| content | JSONB | NOT NULL | Message content (structured, e.g., {"parts": \[...\]}). |
| timestamp | TIMESTAMPTZ | NOT NULL, DEFAULT now() | Timestamp when the message occurred. |
| metadata | JSONB | NULL, DEFAULT '{}'::jsonb | Optional technical or AI metadata for the message. |
| *Indexes:* |  | (conversation\_id, timestamp) | Crucial for ordering messages within a conversation. |

**file_metadata table**
(Tracks uploaded files stored in object storage)

| Column Name | Data Type | Constraints | Notes |
| :---- | :---- | :---- | :---- |
| id | UUID | PRIMARY KEY, DEFAULT gen\_random\_uuid() | Unique identifier for this file metadata record. |
| user\_id | UUID | NOT NULL, REFERENCES users(id) ON DELETE CASCADE | The user who uploaded the file (VEDAVIVI ID). |
| object\_key | TEXT | NOT NULL, UNIQUE | Unique path/key of the file in the object store (S3/R2/GCS). |
| original\_filename | TEXT | NOT NULL | Original filename provided by the user. |
| mime\_type | TEXT | NOT NULL | MIME type of the file (e.g., 'image/png'). |
| file\_size\_bytes | BIGINT | NOT NULL, CHECK (file\_size\_bytes \>= 0\) | File size in bytes. |
| upload\_status | TEXT | NOT NULL, CHECK (upload\_status IN ('pending', 'confirmed', 'failed')) | Status of the upload confirmation by the backend. |
| purpose | TEXT | NULL, CHECK (purpose IN ('user\_avatar', 'assistant\_avatar', 'chat\_file')) | Primary intended use of the file. |
| associated\_conversation\_id | UUID | NULL, REFERENCES conversations(id) ON DELETE CASCADE | If a chat file, links to the relevant conversation. |
| processed\_variants | JSONB | NULL, DEFAULT '{}'::jsonb | Stores object keys for generated variants (e.g., thumbnails). |
| created\_at | TIMESTAMPTZ | NOT NULL, DEFAULT now() | Timestamp of metadata record creation. |
| updated\_at | TIMESTAMPTZ | NOT NULL, DEFAULT now() | Timestamp of last update (needs trigger/logic). |
| *Indexes:* |  | UNIQUE(object\_key), (user\_id), (associated\_conversation\_id) |  |

**assistant_objectives table**
(Stores objectives defined via chat)

| Column Name | Data Type | Constraints | Notes |
| :---- | :---- | :---- | :---- |
| id | UUID | PRIMARY KEY, DEFAULT gen\_random\_uuid() | Unique identifier for the objective. |
| assistant\_id | UUID | NOT NULL, REFERENCES assistants(id) ON DELETE CASCADE | The assistant this objective belongs to. |
| objective\_text | TEXT | NOT NULL | The textual description of the objective. |
| status | TEXT | NOT NULL, DEFAULT 'active', CHECK (status IN ('active', 'completed', 'cancelled')) | Current status (managed via chat). |
| created\_at | TIMESTAMPTZ | NOT NULL, DEFAULT now() | Timestamp of creation. |
| updated\_at | TIMESTAMPTZ | NOT NULL, DEFAULT now() | Timestamp of last update (e.g., status change). |
| completed\_at | TIMESTAMPTZ | NULL | Timestamp when status became 'completed'. |
| metadata | JSONB | NULL, DEFAULT '{}'::jsonb | Flexible storage for any future objective metadata. |
| *Indexes:* |  | (assistant\_id, status) | For efficient retrieval of active objectives. |

**tasks table**
(Stores internal tasks managed by the assistant)

| Column Name | Data Type | Constraints | Notes |
| :---- | :---- | :---- | :---- |
| id | UUID | PRIMARY KEY, DEFAULT gen\_random\_uuid() | Unique identifier for the task. |
| assistant\_id | UUID | NOT NULL, REFERENCES assistants(id) ON DELETE CASCADE | The assistant managing this task. |
| objective\_id | UUID | NULL, REFERENCES assistant\_objectives(id) ON DELETE SET NULL | Optional link to the objective this task supports. |
| description | TEXT | NOT NULL | Description of the task content. |
| status | TEXT | NOT NULL, DEFAULT 'todo', CHECK (status IN ('todo', 'in\_progress', 'done', 'cancelled')) | Current status of the task (managed by agent). |
| priority | INTEGER | NULL, CHECK (priority BETWEEN 1 AND 5\) | Optional priority level (e.g., 1=Highest). |
| due\_date | TIMESTAMPTZ | NULL | Optional due date/time for the task. |
| completed\_at | TIMESTAMPTZ | NULL | Timestamp when status became 'done'. |
| created\_at | TIMESTAMPTZ | NOT NULL, DEFAULT now() | Timestamp of task creation. |
| updated\_at | TIMESTAMPTZ | NOT NULL, DEFAULT now() | Timestamp of last update (needs trigger/logic). |
| metadata | JSONB | NULL, DEFAULT '{}'::jsonb | Flexible storage for additional task metadata. |
| *Indexes:* |  | (assistant\_id), (assistant\_id, status), (assistant\_id, due\_date), (objective\_id) | Supports various task queries by the agent. |

**external_credentials table**
(Stores encrypted credentials for external services)

| Column Name | Data Type | Constraints | Notes |
| :---- | :---- | :---- | :---- |
| id | UUID | PRIMARY KEY, DEFAULT gen\_random\_uuid() | Unique identifier for this credential set. |
| user\_id | UUID | NOT NULL, REFERENCES users(id) ON DELETE CASCADE | The user these credentials belong to (VEDAVIVI ID). |
| service\_name | TEXT | NOT NULL | Identifier for the external service (e.g., 'google\_calendar'). |
| external\_user\_id | TEXT | NULL | The user's ID within the external service (if available). |
| account\_identifier | TEXT | NULL | User-friendly identifier (e.g., email associated with connection). |
| access\_token\_encrypted | TEXT | NULL | **ENCRYPTED** OAuth access token or API key. |
| refresh\_token\_encrypted | TEXT | NULL | **ENCRYPTED** OAuth refresh token. |
| scopes | TEXT\[\] | NULL | Array of permissions/scopes granted by the user. |
| expires\_at | TIMESTAMPTZ | NULL | Expiry time for the access\_token. |
| revoked | BOOLEAN | NOT NULL, DEFAULT false | Flag if user has revoked access via VEDAVIVI platform. |
| created\_at | TIMESTAMPTZ | NOT NULL, DEFAULT now() | Timestamp of record creation. |
| updated\_at | TIMESTAMPTZ | NOT NULL, DEFAULT now() | Timestamp of last update (e.g., token refresh). |
| *Indexes:* |  | UNIQUE(user\_id, service\_name) | Enforces one connection per service per user for MVP. |
| *Security Note:* |  | Requires application-level encryption for token columns. |  |

**cli_tokens table**
(Stores hashed tokens for VEDAVIVI terminal client authentication)

| Column Name | Data Type | Constraints | Notes |
| :---- | :---- | :---- | :---- |
| id | UUID | PRIMARY KEY, DEFAULT gen\_random\_uuid() | Unique identifier for the CLI token record. |
| user\_id | UUID | NOT NULL, REFERENCES users(id) ON DELETE CASCADE | The user who generated/owns this token (VEDAVIVI ID). |
| token\_hash | TEXT | NOT NULL, UNIQUE | **HASHED** (e.g., bcrypt) value of the generated CLI token. |
| token\_prefix | TEXT | NOT NULL | Short, non-sensitive prefix (e.g., first 8 chars) for display. |
| description | TEXT | NULL | Optional user-provided description (e.g., "Laptop CLI"). |
| last\_used\_at | TIMESTAMPTZ | NULL | Timestamp when the token was last successfully used. |
| expires\_at | TIMESTAMPTZ | NULL | Optional expiry date (non-expiring for MVP). |
| revoked\_at | TIMESTAMPTZ | NULL | Timestamp when the user explicitly revoked the token. |
| created\_at | TIMESTAMPTZ | NOT NULL, DEFAULT now() | Timestamp of token record creation. |
| updated\_at | TIMESTAMPTZ | NOT NULL, DEFAULT now() | Timestamp of last update (needs trigger/logic). |
| *Indexes:* |  | UNIQUE(token\_hash), (user\_id), (user\_id, revoked\_at) |  |
| *Security Note:* |  | Store only a strong hash of the token, never the plaintext. |  |

**webhook_registrations table**
(Manages configuration for incoming webhooks)

| Column Name | Data Type | Constraints | Notes |
| :---- | :---- | :---- | :---- |
| id | UUID | PRIMARY KEY, DEFAULT gen\_random\_uuid() | Unique identifier for this registration. |
| user\_id | UUID | NOT NULL, REFERENCES users(id) ON DELETE CASCADE | The user associated with this webhook (VEDAVIVI ID). |
| assistant\_id | UUID | NOT NULL, REFERENCES assistants(id) ON DELETE CASCADE | The assistant designated to receive events. |
| source | TEXT | NOT NULL, CHECK (source IN ('slack', 'calendly', 'google\_calendar')) | The external service expected to send events. |
| target\_url\_path | TEXT | NOT NULL, UNIQUE | The unique path component identifying this webhook endpoint. |
| signing\_secret\_encrypted | TEXT | NULL | **ENCRYPTED** signing secret (for Slack, Calendly). |
| google\_channel\_id | TEXT | NULL | Google Calendar watch channel ID (for validation). |
| google\_resource\_id | TEXT | NULL | Google Calendar watch resource ID (for validation). |
| google\_channel\_token | TEXT | NULL | Optional validation token sent during Google Calendar watch. |
| status | TEXT | NOT NULL, DEFAULT 'active', CHECK (status IN ('active', 'inactive', 'failed')) | Status of the registration (e.g., active, disabled by user). |
| expires\_at | TIMESTAMPTZ | NULL | Expiry time for the registration (e.g., for Google channels). |
| created\_at | TIMESTAMPTZ | NOT NULL, DEFAULT now() | Timestamp of registration creation. |
| updated\_at | TIMESTAMPTZ | NOT NULL, DEFAULT now() | Timestamp of last update (e.g., status change, renewal). |
| *Indexes:* |  | UNIQUE(target\_url\_path), (assistant\_id), (user\_id), (source, status, expires\_at) | Supports lookup and finding channels needing renewal. |
| *Security Note:* |  | Requires application-level encryption for secrets. |  |

**registered_agents table**
(Internal registry for Specialized Agents - managed by VEDAVIVI team for MVP)

| Column Name | Data Type | Constraints | Notes |
| :---- | :---- | :---- | :---- |
| agent\_definition\_id | TEXT | PRIMARY KEY | Unique identifier for the SA type/version (e.g., 'summarizer\_v1'). |
| name | TEXT | NOT NULL | Human-readable name of the SA type. |
| description | TEXT | NULL | Description of the SA's purpose. |
| version | TEXT | NOT NULL | Version of this agent definition. |
| endpoint\_url | TEXT | NULL | Internal URL for synchronous HTTP calls (if supported). |
| async\_queue\_name | TEXT | NULL | Message queue name for asynchronous calls (if supported). |
| capabilities | JSONB | NULL, DEFAULT '{}'::jsonb | Agent capabilities (e.g., { "streaming": true } based on A2A schema). |
| skills | JSONB | NULL, DEFAULT '\[\]'::jsonb | List of skills (based on AgentSkill structure from A2A schema). |
| authentication\_info | JSONB | NULL, DEFAULT '{}'::jsonb | Info on how internal services should authenticate (if needed). |
| status | TEXT | NOT NULL, DEFAULT 'active', CHECK (status IN ('active', 'inactive')) | Whether this agent type is currently available. |
| created\_at | TIMESTAMPTZ | NOT NULL, DEFAULT now() | Timestamp of record creation. |
| updated\_at | TIMESTAMPTZ | NOT NULL, DEFAULT now() | Timestamp of last update (needs trigger/logic). |
| *Indexes:* |  | (status) | To quickly find active agents. |

**Redis Usage (MVP):**

* **WebSocket Connections:** Track active GraphQL Subscription connections state (User ID \-\> Server/Connection ID).
* **Webhook Idempotency:** Store processed event identifiers (processed\_event:{source}:{event\_id\_hash}) with a TTL (e.g., 3-4 days).

### **5. Cross-Cutting Concerns & Future Considerations**

* **Authentication & Authorization:** Uses Clerk.com (Google, GitHub, Email+Link) for primary user auth & core profile. **VEDAVIVI** CLI Tokens (hashed) for terminal access. Backend validates Clerk session tokens (for GraphQL/WebSockets) or **VEDAVIVI** CLI Tokens. Authorization is OBAC via Clerk User ID mapped to internal **VEDAVIVI** User ID. Agents act with user permissions but identified via custom claims in Clerk session token.
* **API Standards:** **GraphQL** is primary for clients (Queries, Mutations, Subscriptions). **OpenAPI** used for external callbacks/webhooks (e.g., Clerk, Google, Slack, Calendly) and internal synchronous HTTP APIs (e.g., Agent-to-Agent sync calls, internal agent task APIs).
  * **OpenAPI Endpoint Definitions (MVP):** The following endpoints will be defined using OpenAPI:
    * **External Callbacks/Webhooks:**

| Method | Path | Authentication | Notes |
| ----- | ----- | ----- | ----- |
| POST | /api/v1/webhooks/clerk | Clerk Signature | Handles incoming Clerk.com events. |
| GET | /api/v1/connections/oauth/callback/{service} | None (State param) | Handles OAuth redirects from external services. |
| POST | /webhooks/incoming/{target\_url\_path} | Source-Specific | Handles incoming webhooks (Slack, Calendly, Google Cal). |

    * **Internal Synchronous APIs:**

| Method | Path | Authentication | Notes |
| ----- | ----- | ----- | ----- |
| POST | /internal/agents/{agent\_definition\_id}/invoke | Internal Service Auth | Sync Agent-to-Agent calls (A2A Schema payload). |
| POST | /internal/assistants/my/objectives | Internal Service Auth | Agent logic creates objective. |
| GET | /internal/assistants/my/objectives | Internal Service Auth | Agent logic retrieves objectives. |
| PUT | /internal/objectives/{objective\_id} | Internal Service Auth | Agent logic updates objective status. |
| POST | /internal/assistants/my/tasks | Internal Service Auth | Agent logic creates task. |
| GET | /internal/assistants/my/tasks | Internal Service Auth | Agent logic retrieves tasks. |
| PUT | /internal/tasks/{task\_id} | Internal Service Auth | Agent logic updates task. |
| DELETE | /internal/tasks/{task\_id} | Internal Service Auth | Agent logic deletes task. |


* **A2A Protocol:** Internal agent communication payloads use a2a-schema.json.
* **Security Principles:** HTTPS mandatory. Application-level encryption/hashing for stored credentials/secrets/tokens. Secure key management required. Source-specific validation for webhooks. Rate limiting recommended.
* **Asynchronous Processing:** Message queues and background workers used for webhooks, user deletion cleanup, async Agent-to-Agent tasks.
* **Future Architectural Considerations (Post-MVP):** Plan to evaluate/implement Kafka (communication bus), gRPC/Protobuf (internal services/A2A)

## **6. GraphQL Schema (MVP Example)**

Below is a representative GraphQL SDL schema based on the data model and relationships defined in Section 4. This schema covers the core entities and their relationships for the VEDAVIVI MVP backend:

```graphql
# User
 type User {
   id: ID!
   clerkUserId: String!
   timezone: String
   preferences: JSON
   settings: JSON
   assistant: Assistant
   conversations: [Conversation!]
   cliTokens: [CliToken!]
   externalCredentials: [ExternalCredential!]
 }

# Assistant
 type Assistant {
   id: ID!
   user: User!
   name: String!
   backstory: String!
   avatarFile: FileMetadata
   configuration: JSON
   objectives: [AssistantObjective!]
   tasks: [Task!]
 }

# Conversation
 type Conversation {
   id: ID!
   user: User!
   assistant: Assistant!
   createdAt: DateTime!
   lastMessageAt: DateTime
   messages: [ChatMessage!]
 }

# Chat Message
 type ChatMessage {
   id: ID!
   conversation: Conversation!
   senderRole: String!
   content: JSON!
   timestamp: DateTime!
   metadata: JSON
 }

# File Metadata
 type FileMetadata {
   id: ID!
   user: User!
   objectKey: String!
   originalFilename: String!
   mimeType: String!
   fileSizeBytes: Int!
   uploadStatus: String!
   purpose: String
   associatedConversation: Conversation
   processedVariants: JSON
   createdAt: DateTime!
   updatedAt: DateTime!
 }

# Assistant Objective
 type AssistantObjective {
   id: ID!
   assistant: Assistant!
   objectiveText: String!
   status: String!
   createdAt: DateTime!
   updatedAt: DateTime!
   completedAt: DateTime
   metadata: JSON
   tasks: [Task!]
 }

# Task
 type Task {
   id: ID!
   assistant: Assistant!
   objective: AssistantObjective
   description: String!
   status: String!
   priority: Int
   dueDate: DateTime
   completedAt: DateTime
   createdAt: DateTime!
   updatedAt: DateTime!
   metadata: JSON
 }

# External Credential
 type ExternalCredential {
   id: ID!
   user: User!
   serviceName: String!
   externalUserId: String
   accountIdentifier: String
   scopes: [String!]
   expiresAt: DateTime
   revoked: Boolean!
   createdAt: DateTime!
   updatedAt: DateTime!
 }

# CLI Token
 type CliToken {
   id: ID!
   user: User!
   tokenPrefix: String!
   description: String
   lastUsedAt: DateTime
   expiresAt: DateTime
   revokedAt: DateTime
   createdAt: DateTime!
   updatedAt: DateTime!
 }

# Root Query/Mutation Example
 type Query {
   me: User
   myAssistant: Assistant
   myConversations: [Conversation!]
   conversation(id: ID!): Conversation
   myObjectives: [AssistantObjective!]
   myTasks: [Task!]
   myCliTokens: [CliToken!]
   myExternalCredentials: [ExternalCredential!]
   myDataOverview: JSON
 }

 type Mutation {
   createAssistant(name: String!, backstory: String!): Assistant!
   updateAssistant(id: ID!, name: String, backstory: String, configuration: JSON): Assistant!
   sendMessage(conversationId: ID!, content: JSON!): ChatMessage!
   createCliToken(description: String): CliToken!
   revokeCliToken(id: ID!): Boolean!
   deleteMyAccount: Boolean!
   requestFileUploadUrl(filename: String!, mimeType: String!): JSON!
   completeFileUpload(fileId: ID!): FileMetadata!
 }

scalar DateTime
scalar JSON
```

---

### **7. Key Deferred Items (Post-MVP)**

* Generic incoming/outgoing webhook support.
* Integration with external task managers.
* Full Agent Marketplace UI and external developer onboarding.
* Communication with external, self-hosted agents.
* Data export ("Download my data") feature.
* Transparency into learned/inferred user data/knowledge graphs.
* Advanced Role-Based Access Control (RBAC) leveraging Clerk's capabilities.
* File uploads from the terminal client.
* Initial user signup directly via terminal client.
* Phone Authentication via Clerk.
* Advanced asynchronous A2A response mechanisms.
* Full implementation of knowledge graphs, vector databases.

### **8. Conclusion**

This revised MVP backend plan for **VEDAVIVI** details a comprehensive technical strategy centered around GraphQL as the primary client API, leveraging Clerk.com for user management/authentication, and employing robust patterns for internal/external communication and data persistence. It adheres to the "simpler and cost-effective" principle while establishing a strong foundation for delivering the core value proposition and enabling future evolution towards the full VEDAVIVI vision.
