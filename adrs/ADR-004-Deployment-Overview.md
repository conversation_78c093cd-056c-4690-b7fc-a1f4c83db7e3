## **Vedavivi MVP - Deployment Overview**

**(Based on ISO/IEC/IEEE 42010 Viewpoint Template)**

**Version:** 1.0 **Date:** May 12, 2025

### **1. Deployment Viewpoint**

Vedavivi MVP Deployment Viewpoint

**Synonyms:** Physical Viewpoint, Infrastructure Viewpoint

### **2. Overview**

This viewpoint describes the physical environment and infrastructure into which the Vedavivi MVP backend software components are deployed. It covers the mapping of logical components to runtime artifacts, the choice of cloud platform and services, containerization strategy, and network considerations relevant to deployment. The strategy is explicitly "cloud-native and containerized."  

### **3. Setup and Stakeholders**

#### **3.1 Infrastructure Setup**

#### **1. Deployment Environment**
- **Platform:** Google Cloud Platform (GCP) will be used as the cloud platform for hosting the backend.
- **Specific Services:** Services such as Google Kubernetes Engine (GKE) for container orchestration, Google Cloud Storage for object storage, and Cloud SQL for managed PostgreSQL databases could be utilized.

#### **2. Containerization & Orchestration**
- **Containerization Strategy:** Backend services will be packaged into Docker containers.
- **Orchestration:** Google Kubernetes Engine (GKE) will manage and orchestrate these containers, ensuring scalability and reliability.

#### **3. Component-to-Infrastructure Mapping**
- Backend components will map to physical compute instances in GCP, including containers for services like API Gateway, authentication, and backend logic.
- Managed services such as Cloud SQL (for databases) and Memorystore (for caching) will complement the infrastructure.

#### **4. Scalability & Availability (Infrastructure)**
- **Scalability:** Horizontal scaling will be supported via Kubernetes auto-scaling, allowing pods to scale based on demand.
- **Availability:** GCP’s load balancing and multi-zone deployments will ensure redundancy and high availability.

#### **5. Network Configuration**
- The deployment will include Virtual Private Cloud (VPC) configurations with subnets for isolating services.
- Firewalls will be configured to allow/deny access based on roles, and a CDN will be used for delivering static assets.

#### **6. Operational Feasibility & Cost**
- Using Terraform for Infrastructure as Code (IaC) will ensure repeatable, manageable, and version-controlled deployments.
- By leveraging managed GCP services, operational overhead will be reduced, and costs can be optimized by using features like committed use discounts.

#### **7. Resource Provisioning & Management**
- Infrastructure resources will be provisioned and managed using Terraform scripts stored in the GitHub repository.
- GitHub Actions will automate the deployment pipeline, applying Terraform configurations directly to GCP.

#### **8. Monitoring & Logging Infrastructure**
- GCP’s Stackdriver (now called Operations Suite) will be used for monitoring and logging system health, application performance, and debugging.
- Alerts will be configured for critical thresholds to notify the DevOps team proactively.

#### **3.2 GitHub Actions Integration for Infrastructure Setup**

#### **Continuous Integration/Continuous Deployment (CI/CD)**
GitHub Actions will serve as the primary tool for automating the CI/CD pipeline. It will handle tasks such as:
- Running tests, linters, and static analysis on code changes.
- Building Docker images for backend services and pushing them to a container registry (e.g., Google Container Registry).
- Executing Terraform scripts for provisioning and updating infrastructure resources on Google Cloud Platform (GCP).

#### **Terraform Automation**
- GitHub Actions workflows will include steps to initialize, plan, and apply Terraform configurations.
- This ensures infrastructure changes are version-controlled and auditable.

#### **Static Asset Deployment**
For frontend or static assets:
- GitHub Actions will upload built assets to Google Cloud Storage.
- A step will be included to invalidate CDN caches to deliver the latest assets to end users.

#### **Monitoring and Notifications**
- GitHub Actions can be configured to integrate with monitoring tools or send notifications (e.g., Slack, email) for deployment statuses, build failures, or other critical events.

#### **3.3 Typical Stakeholders**

* **Developers (Internal Vedavivi Team / DevOps):** To understand where and how their services will run, and to create deployment scripts/configurations.  
* **Operations Team / Site Reliability Engineers (SREs):** For deploying, managing, monitoring, and troubleshooting the production environment.  
* **System Architects:** To ensure the deployment architecture meets non-functional requirements like scalability, availability, and performance.  
* **Security Teams:** To assess the security of the deployment environment, network configurations, and infrastructure hardening.  
* **Owners/Management (Vedavivi):** To understand infrastructure costs, operational aspects, and scalability provisions.

### **4. Model Kind: Deployment Diagrams**

#### **4.1 CI Sequence Diagram**

```mermaid
sequenceDiagram
    participant Dev as Developer
    participant GitRepo as Git Repository
    participant CI as GHA Platform
    participant ContainerRegistry as Container Registry

    Dev->>+GitRepo: Push Code Changes (e.g., to feature branch or merge to develop)
    GitRepo->>CI: Trigger Pipeline (Webhook)
    CI->>CI: Checkout Code from GitRepo
    CI->>CI: Install Dependencies
    CI->>CI: Run Linters & Static Analysis
    CI->>CI: Run Unit Tests
    alt Tests Pass
        CI->>CI: Build Docker Image
        CI->>+ContainerRegistry: Push Docker Image (with tags like commit SHA, branch name)
        ContainerRegistry-->>CI: Confirm Image Push
        CI-->>GitRepo: Report CI Status (Success)
        GitRepo-->>Dev: Notify CI Success
    else Tests Fail
        CI-->>GitRepo: Report CI Status (Failure)
        GitRepo-->>Dev: Notify CI Failure
    end
```

#### **4.2 CI Process Description**

The **CI (Continuous Integration) Sequence Diagram** outlines the process of ensuring code changes are automatically validated and prepared for deployment. Here's a step-by-step description:

1. **Developer Pushes Code Changes**:
   - A developer pushes code changes (e.g., to a feature branch or merges to the development branch) to the Git repository.

2. **Triggering the CI Pipeline**:
   - The push event triggers the CI pipeline via a webhook.

3. **Pipeline Steps**:
   - The CI system (e.g., GitHub Actions) performs the following:
     1. **Code Checkout**: Retrieves the latest code from the Git repository.
     2. **Dependency Installation**: Installs required dependencies.
     3. **Linters & Static Analysis**: Runs tools to check for code quality issues, style violations, and potential bugs.
     4. **Unit Tests**: Executes unit tests for the codebase.

4. **Conditional Outcomes**:
   - If the tests pass:
     - The pipeline builds a Docker image of the code.
     - The Docker image is pushed to a container registry (e.g., Docker Hub) with appropriate tags (e.g., commit SHA, branch name).
     - The CI system reports a success status back to the Git repository, notifying the developer of the successful pipeline run.
   - If the tests fail:
     - The CI system reports a failure status, and the developer is notified.

#### **4.3 CD Sequence Diagram**

```mermaid
sequenceDiagram
    participant CI_Platform as GHA Platform
    participant ContainerRegistry as Container Registry
    participant StagingEnv as Staging Environment
    participant ProdEnv as Production Environment
    participant OpsTeam as Operations/Dev Team (Optional Approval)

    %% Trigger from successful CI or specific branch merge
    CI_Platform->>CI_Platform: CD Pipeline Triggered (e.g., for 'develop' branch)
    CI_Platform->>+ContainerRegistry: Fetch Latest Image (e.g., 'develop' tag)
    ContainerRegistry-->>-CI_Platform: Provide Image Details

    %% Deploy to Staging
    CI_Platform->>+StagingEnv: Deploy New Image Version
    StagingEnv-->>-CI_Platform: Deployment Status
    alt Staging Deployment Successful
        CI_Platform->>StagingEnv: Run Automated Tests (Integration, Smoke Tests)
        StagingEnv-->>CI_Platform: Test Results
        alt Staging Tests Pass
            CI_Platform->>CI_Platform: Staging Validation Complete

            %% Production Deployment Trigger (e.g., merge to 'main', manual trigger)
            opt Manual Approval or Trigger for Production
                OpsTeam->>CI_Platform: Approve/Trigger Production Deployment
            end
            CI_Platform->>+ContainerRegistry: Fetch Stable Image (e.g., 'main' tag or tagged release)
            ContainerRegistry-->>-CI_Platform: Provide Image Details
            CI_Platform->>+ProdEnv: Deploy New Image Version (e.g., Rolling Update, Blue/Green)
            ProdEnv-->>-CI_Platform: Deployment Status
            alt Production Deployment Successful
                 CI_Platform->>ProdEnv: Run Health Checks / Smoke Tests
                 ProdEnv-->>CI_Platform: Health Status
                 CI_Platform->>OpsTeam: Notify Production Deployment Success
            else Production Deployment Failed
                 CI_Platform->>ProdEnv: Initiate Rollback (Optional)
                 ProdEnv-->>CI_Platform: Rollback Status
                 CI_Platform->>OpsTeam: Notify Production Deployment Failure / Rollback
            end
        else Staging Tests Fail
            CI_Platform->>OpsTeam: Notify Staging Test Failure
        end
    else Staging Deployment Failed
        CI_Platform->>OpsTeam: Notify Staging Deployment Failure
    end
```

#### **4.4 CD Process Description**

The **CD (Continuous Deployment) Sequence Diagram** describes how validated code from the CI pipeline is deployed to staging and production environments. Here's the process:

1. **Triggering the CD Pipeline**:
   - The CD pipeline is triggered by a successful CI pipeline run or a merge to a specific branch (e.g., `develop` or `main`).

2. **Fetching the Docker Image**:
   - The CI/CD platform fetches the latest Docker image (e.g., tagged with `develop` or `main`) from the container registry.

3. **Staging Deployment**:
   - The image is deployed to the **Staging Environment**.
   - The deployment status is reported back to the CI/CD system.
   - Automated tests (integration tests, smoke tests) are run in the staging environment:
     - If the tests pass:
       - The staging validation is marked as complete.
     - If the tests fail:
       - The pipeline notifies the operations/dev team of the failure.

4. **Production Deployment** (Conditional):
   - Upon successful staging validation:
     - The deployment to **Production** is triggered. This can require **manual approval** from the operations team, depending on the setup.
     - The latest stable Docker image (e.g., tagged `main` or a tagged release) is fetched from the container registry.
     - The production deployment follows strategies like **rolling updates** or **blue/green deployments**.
     - Post-deployment health checks or smoke tests are run to verify the deployment's success.
     - If the deployment fails, a **rollback** may be initiated (optional).
     - Notifications are sent to the operations/dev team about the status of the production deployment.

#### **4.5 CD Static Assets**

```mermaid
sequenceDiagram
    participant Dev as Developer
    participant GitRepo as Git Repository
    participant CI_Platform as CI/CD Platform
    participant BuildEnv as Build Environment (within CI Platform)
    participant ObjectStore as Cloud Object Store (S3/GCS/R2)
    participant CDN as CDN Service

    Dev->>+GitRepo: Push Code/Asset Changes (e.g., frontend source, images)
    GitRepo->>+CI_Platform: Trigger Pipeline (Webhook)
    CI_Platform->>+BuildEnv: Checkout Code from GitRepo
    BuildEnv->>BuildEnv: Install Dependencies (e.g., npm install)
    BuildEnv->>BuildEnv: Run Tests (e.g., npm test)

    alt Optional Build Step (e.g., for React/Vue/Angular)
        BuildEnv->>BuildEnv: Build Static Assets (e.g., npm run build)
    end

    alt Build & Tests Pass
        %% Deployment to Object Store
        BuildEnv->>+ObjectStore: Sync/Upload Built Assets (or source assets if no build)
        ObjectStore-->>-BuildEnv: Upload Confirmation
        BuildEnv-->>-CI_Platform: Asset Deployment Complete

        %% CDN Cache Invalidation
        CI_Platform->>+CDN: Request Cache Invalidation (for updated paths/*)
        CDN-->>-CI_Platform: Invalidation Accepted/In Progress
        CI_Platform-->>GitRepo: Report CI/CD Status (Success)
        GitRepo-->>Dev: Notify CI/CD Success
    else Build or Tests Fail
        BuildEnv-->>CI_Platform: Report Failure
        CI_Platform-->>-GitRepo: Report CI/CD Status (Failure)
        GitRepo-->>Dev: Notify CI/CD Failure
    end
```

#### **4.6 CD Static Assets Description**

The **Static Assets Sequence Diagram** outlines how code and asset changes (e.g., frontend source files, images) are processed, built, and deployed to a cloud object store and served via a CDN. Below is the detailed step-by-step process.

1. **Developer Pushes Code/Asset Changes**:
   - A developer pushes changes (e.g., frontend source files, images) to the Git repository.

2. **Triggering the CI/CD Pipeline**:
   - The push event triggers the CI/CD pipeline via a webhook.

3. **Pipeline Execution**:
   - The CI/CD platform performs the following steps:
     1. **Code Checkout**: Retrieves the latest code from the Git repository.
     2. **Dependency Installation**: Installs required dependencies (e.g., using `npm install`).
     3. **Run Tests**: Executes unit tests (e.g., using `npm test`).

4. **Optional Build Step**:
   - If applicable (e.g., for React, Vue, Angular projects), the pipeline builds the static assets using commands like `npm run build`.

5. **Build Outcome**:
   - If the build and tests pass:
     - The built/static assets are synchronized or uploaded to a **Cloud Object Store** (e.g., AWS S3, Google Cloud Storage, or Cloudflare R2).
     - The CI/CD platform confirms the upload.
     - A request is sent to the **CDN** to invalidate the cache for updated paths, ensuring the latest assets are served.
   - If the build or tests fail:
     - The pipeline reports the failure, and the developer is notified.

6. **Notifications**:
   - The CI/CD platform sends notifications about the pipeline status (success or failure) back to the Git repository.
   - Developers are notified accordingly.

This description is based on the **Static Assets Sequence Diagram** referenced in the repository. If additional details are needed, please refer to the specific diagram or provide further clarification.

#### **5 Deployment Diagram Conventions**

#### **5.1 Infrastructure Specification List Conventions**

* **5.1.1 Model kind languages or notations:**    
  * Categories & Items:  
    * **Compute:** Container hosting service (e.g., Google Cloud Run, AWS Fargate, or a Kubernetes cluster).  
    * **Database:** Managed PostgreSQL service (e.g., AWS RDS PostgreSQL, Google Cloud SQL for PostgreSQL).    
    * **Caching:** Managed Redis service (e.g., AWS ElastiCache for Redis, Google Cloud Memorystore for Redis).    
    * **Messaging:** Managed Message Queue service (e.g., AWS SQS, Google Pub/Sub).    
    * **Storage:** Cloud Object Storage (e.g., AWS S3, Google Cloud Storage, Cloudflare R2).    
    * **CDN:** Content Delivery Network integrated with Object Storage.    
    * **Identity Management (External):** Clerk.com.    
    * **Container Registry:** (e.g., Docker Hub, GCR, ECR). 

* **5.1.2 Model kind languages or notations:**  
  * Nodes represent physical or virtual hardware, container hosts, or managed services. Artifacts represent packaged software (e.g., Docker containers).  
  * The diagram will illustrate:  
    * A Cloud Provider (e.g., AWS, GCP).  
    * Container Orchestration/Hosting Environment (e.g., Kubernetes, Cloud Run, AWS Fargate).  
    * Containers for each backend service (API Gateway, Auth Service, Assistant Service, etc.).  
    * Managed services: PostgreSQL instance, Redis instance, Message Queue service (e.g., SQS, Pub/Sub), Object Storage (e.g., S3, GCS, R2), CDN.    
    * External services like Clerk.com.    
    * High-level network connections (e.g., internet to API Gateway, services to database).  
* **5.1.3 Model kind metamodel:**  
  * **Entities:** `Node` (a computational resource), `Artifact` (a deployed piece of software, e.g., a container image), `CommunicationPath` (a connection between nodes), `ManagedService` (a cloud provider service).  
  * **Attributes:** For `Node`: `Type` (e.g., server, container host). For `Artifact`: `Name`, `Version`. For `ManagedService`: `ServiceType` (e.g., PostgreSQL, Redis).  
  * **Relationships:** `hostsArtifact` (Node to Artifact), `connectsTo` (between Nodes or Node to ManagedService).  

### **6. Notes**

* The MVP deployment will prioritize simplicity and leveraging managed services to reduce operational overhead.  
* Detailed network security configurations (e.g., specific firewall rules beyond high-level access control) are part of a more detailed design phase but the viewpoint establishes the landscape.  
* Cost optimization of the infrastructure will be an ongoing consideration post-MVP deployment.    

### **7. Sources**

* **Primary Source:** Vedavivi Backend Summary (MVP Technical Plan)  
* ISO/IEC/IEEE 42010:2011, Systems and software engineering \- Architecture description.  
* Architecture Viewpoint Template for ISO/IEC/IEEE 42010 (the template for this document).
