# **VEDAVIVI: MVP Frontend Technical Plan**

### **1\. Introduction**

This document outlines the technical plan and specifications for the frontend systems supporting the Minimum Viable Product (MVP) of the VEDAVIVI platform. It is designed to complement the "VEDAVIVI: MVP Backend Technical Plan v1.5" and ensure a cohesive and efficient full-stack architecture.

The primary goal of the MVP frontend is to deliver a responsive, intuitive, and engaging user experience for the core value proposition: providing an individual with a personalized, intelligent agent to simplify daily life and empower them. For the MVP, while the long-term vision includes a rich, proactive, and deeply personalized experience as outlined in the VEDAVIVI Business Plan, the frontend will prioritize **simplicity and core functionality** to align with the "simpler and cost-effective" principle, enabling rapid development, iteration, and validation. Showcasing the core value will be achieved through clear, straightforward UI/UX.

The founding team ( @adslaton and @joemocha ) possesses deep expertise across the selected frontend and backend technologies, ensuring a strong foundation for MVP development.

### **2\. Core Frontend Technologies & Standards**

The following technology choices and standards will be adopted for the MVP frontend:

* **Primary Framework/Library:** ReactJS (Leveraging its component-based architecture, strong community support, and rich ecosystem)
* **Styling:** Tailwind CSS (For utility-first CSS, enabling rapid UI development).
  * **Maintainability Note:** To ensure consistency and maintainability with Tailwind CSS as the project grows, the team will establish clear conventions for its usage. This may include adopting a consistent class ordering, leveraging @apply for common reusable patterns within components where appropriate (while still favoring utility-first), and potentially integrating linting tools or plugins that help manage Tailwind classes.
* **UI Components:** ShadCN UI (A collection of beautifully designed, accessible, and customizable components built on Tailwind CSS and Radix UI).
  * **Team Proficiency:** The team has prior experience with ShadCN UI and possesses the capability to develop bespoke React components if specific customization needs fall outside the library's standard offerings, ensuring it won't become a development bottleneck.
* **Language:** TypeScript (For static typing, improving code quality, maintainability, and developer experience)
* **API Communication:** GraphQL (To align with the backend's primary client API, enabling efficient data fetching and mutations. We will utilize a GraphQL client library such as Apollo Client for managing data, caching, and real-time updates via Subscriptions)
* **Build Tool/Development Server:** Vite (For its fast development server startup and optimized build process)
* **Testing:**
  * **Unit/Integration Testing:** Jest (A widely-used JavaScript testing framework) with React Testing Library.
  * **End-to-End Testing:** Cypress (For robust end-to-end testing of user flows).
* **State Management:** Zustand (A small, fast, and scalable bearbones state-management solution using simplified flux principles. Chosen for MVP given the anticipated needs of the chat interface and real-time features.)
* **Routing:** React Router (For declarative routing in a single-page application).
  * **Modularity for Future Lines of Business:** The application will use React Router with a URL prefix strategy to ensure clear separation and routing for different application areas, facilitating modular expansion:
    * /pa/ for Personal Assistant features.
    * /a2a/ for future A2A Platform UIs.
    * /am/ for future Agent Marketplace UIs.

### **3\. Frontend Functional Areas & Architecture (MVP Scope)**

This section outlines the key frontend components and how they will interact with the backend GraphQL API. A modular approach will be favored in component design to support future scalability and integration of new lines of business (A2A Platform, Agent Marketplace).

#### **3.1. User Authentication and Profile Management (Leveraging Clerk.com)**

* **Goal (MVP):** Provide secure and seamless user sign-up, login, and basic profile management.
* **Frontend Implementation:**
  * Integrate with Clerk.com's frontend libraries/SDKs.
  * Manage user sessions and JWTs provided by Clerk on the client-side securely.
  * UI components for VEDAVIVI-specific profile data (e.g., timezone) via GraphQL.
  * UI for generating and managing VEDAVIVI CLI Tokens via GraphQL.
  * **Security Note:** While Clerk.com handles significant authentication logic, frontend interactions with its SDKs and JWT management will adhere to security best practices. For MVP, specific additional frontend audit points for Clerk integration are not planned beyond standard practices.

#### **3.2. Assistant Management**

* **Goal (MVP):** Allow users to create and configure their single personal assistant.
* **Frontend Implementation:** (As previously detailed: UI for creation, name/backstory updates, generateAssistantBackstory query).

#### **3.3. File Uploads (Interacting with CDN)**

* **Goal (MVP):** Enable users to upload files (e.g., avatars) securely.
* **Frontend Implementation:** (As previously detailed: pre-signed URL requests, direct client upload, confirmation).

#### **3.4. Chat Interface**

* **Goal (MVP):** Provide a real-time, persistent chat interface.
* **Frontend Implementation:**
  * Responsive chat UI, message history, input fields, etc.
  * GraphQL client (e.g., Apollo Client) for sendMessage (mutation) and newMessages (subscription).
  * **Robust WebSocket Connection Management:** The frontend will implement strategies for:
    * Graceful handling of connection failures (e.g., using retry logic with exponential backoff).
    * Responding to specific error messages/states from the backend (e.g., server overload signals affecting subscriptions).
    * Managing application state during offline browser periods (e.g., visual indicators, potentially queuing outgoing messages for when the connection is restored).
  * Display different message content types.
  * **Inline Transparency for Learned Information:** When the agent extracts new information from user interaction, this will be presented inline within the chat. Users will have immediate options to confirm, correct, or dismiss/remove this learned information, enhancing trust and control.
  * **Displaying Proactive Agent Actions:** Proactive actions taken by the assistant will be clearly indicated within the chat interface, likely through dedicated system messages or distinct speech bubbles within the relevant conversation thread, providing immediate context to the user.

#### **3.5. User Data Transparency**

* **Goal (MVP):** Allow users to view their core explicit data.
* **Frontend Implementation:** (As previously detailed: dedicated UI section, myDataOverview GraphQL query).

#### **3.6. Assistant Objective Management (View via Data Transparency)**

* **Goal (MVP):** Allow users to see objectives identified via chat.
* **Frontend Implementation:** (As previously detailed: visibility through myDataOverview and myObjectives queries).

#### **3.7. Terminal Client Token Management**

* **Goal (MVP):** Allow users to generate and manage CLI tokens.
* **Frontend Implementation:** (As previously detailed: UI for createCliToken, myCliTokens, revokeCliToken).

### **4\. State Management**

* **Local Component State:** React's useState and useReducer.
* **Global State:** Zustand will be used for managing global application state, providing a simple yet scalable solution suitable for the MVP's real-time features and beyond.
* **GraphQL Cache:** The chosen GraphQL client (e.g., Apollo Client) will serve as the primary cache for server-side data.

### **5\. Build and Deployment**

* **Build Tool:** Vite.
* **Deployment:** Static site deployment to a CDN.
* **CI/CD:** Pipeline for automated testing, building, and deployment facilitated by GitHub Actions.
* **Managing Technical Debt:** Frontend-specific technical debt will be identified, tracked, and managed using the GitHub Project system. Prioritization of addressing critical technical debt will be important to maintain development velocity for future iterations.

### **6\. Testing Strategy**

* **Unit Tests:** Jest and React Testing Library.
* **Integration Tests:** Jest and React Testing Library.
* **End-to-End (E2E) Tests:** Cypress.
* **Automated UI Testing:** A key goal is to achieve comprehensive automated testing for the UI.
* **Accessibility Testing:** As part of treating accessibility as a first-class citizen (see Section 7.2), automated accessibility testing (e.g., using axe-core integrated with Cypress/Jest) will be a core part of the pipeline.
* **Code Coverage:** Tracked using tools like Istanbul.

### **7\. Cross-Cutting Concerns**

* **Error Handling:** Comprehensive error handling for API requests and UI rendering.
* **Accessibility (a11y):**
  * **First-Class Citizen:** Accessibility will be treated as a first-class citizen and a core requirement for all UI development, in line with VEDAVIVI's mission to serve "every individual."
  * Adherence to WCAG guidelines, semantic HTML, ARIA attributes, and keyboard navigability will be prioritized. ShadCN UI's accessibility features will be leveraged.
  * Automated testing (as mentioned in Section 6\) and supplementary manual testing will be conducted.
* **Performance:**
  * Code splitting, lazy loading, memoization.
  * **GraphQL Query Optimization:** Strategies will include:
    * Limiting query depth.
    * Utilizing persisted queries (if supported by backend GraphQL tooling like Apollo Platform/GraphQL Hive, which is under investigation for the backend).
    * Ensuring effective use of the GraphQL client cache.
    * Careful component data fetching strategies to avoid over-fetching.
* **Security:** Secure JWT handling, protection against common web vulnerabilities, HTTPS communication.
* **Handling API Evolution:** The frontend will leverage GraphQL's built-in deprecation features (e.g., @deprecated directive notifications) to manage and adapt to backend API changes. Client libraries may also assist in identifying usage of deprecated fields.

### **8\. Development Velocity & Post-MVP Iteration**

* Initial frontend development for the MVP will be driven by the founding team's existing capacity.
* The pace of post-MVP frontend feature additions, significant expansions (like full UIs for Agent Marketplace or A2A Platform), and addressing accumulated technical debt will be influenced by resource allocation decisions made after evaluating MVP reception and overall business priorities, as per the VEDAVIVI Business Plan.

### **9\. Future Considerations (Post-MVP)**

* **Advanced UI for Agent Marketplace:** Full UIs for users to browse, select, and manage specialized agents.
* **Dedicated UIs for A2A Platform:** Interfaces for enterprise users or those self-hosting.
* **Internal Tooling Frontend:** Development of user interfaces for internal tools to track Key Performance Indicators (KPIs), costs by customer, platform analytics, and other operational metrics. This will likely involve creating dashboards and data visualization components.
* **Considerations for Third-Party Agent UI Integration (Agent Marketplace DX):** If third-party agents are ever to render UI components within VEDAVIVI, early thought will be given to:
  * Sandboxing & Security (e.g., iframes, restricted web components).
  * Styling/Theming Consistency.
  * Secure Communication Protocols.
  * Performance Overhead Management.
  * Data Access Controls.
  * Future need for Developer Guidelines/SDKs for such UI contributions.
* **Internationalization (i18n) and Localization (l10n).**
* **Web Workers for computationally intensive tasks.**
* **Progressive Web App (PWA) Features.**
* **More Sophisticated UI for File Management.**
* **UI for External Task Manager Integration.**
* **Potential for a "Proactivity Diary" UI:** As an alternative or supplement to inline notifications for agent proactive steps.
