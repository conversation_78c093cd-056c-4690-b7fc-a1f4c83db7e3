services:
  backend:
    build: ./apps/backend
    ports:
      - "8000:8000"
    environment:
      - AI_DEFAULT_PROVIDER=${AI_DEFAULT_PROVIDER:-openai}
      - AI_RATE_LIMIT_CALLS=${AI_RATE_LIMIT_CALLS:-50}
      - AI_RATE_LIMIT_WINDOW=${AI_RATE_LIMIT_WINDOW:-60}
      - AI_RESPONSE_ENABLED=${AI_RESPONSE_ENABLED:-true}
      - AI_RESPONSE_TIMEOUT=${AI_RESPONSE_TIMEOUT:-30}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - CDN_URL=${CDN_URL}
      - CLERK_API_KEY=${CLERK_API_KEY}
      - CLERK_JWT_PUBLIC_KEY=${CLERK_JWT_PUBLIC_KEY}
      - CLERK_WEBHOOK_SECRET=${CLERK_WEBHOOK_SECRET}
      - CORS_ORIGINS=${CORS_ORIGINS}
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}
      - DATABASE_ASYNC_URL=postgresql+asyncpg://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}
      - DEBUG=${DEBUG}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - LOG_LEVEL=${LOG_LEVEL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - PUBSUB_PROJECT_ID=${PUBSUB_PROJECT_ID}
      - REDIS_URL=${REDIS_URL}
      - RQ_DEFAULT_TIMEOUT=${RQ_DEFAULT_TIMEOUT:-180}
      - RQ_DEFAULT_TTL=${RQ_DEFAULT_TTL:-86400}
      - RQ_RESULT_TTL=${RQ_RESULT_TTL:-3600}
      - RQ_WORKER_COUNT=${RQ_WORKER_COUNT:-2}
      - STORAGE_BUCKET=${STORAGE_BUCKET}
    volumes:
      - ./apps/backend:/app
      - backend_data:/app/.data
    depends_on:
      - db
      - redis

  frontend:
    build:
      context: ./apps/web
      args:
        - VITE_GRAPHQL_API_URL=${VITE_GRAPHQL_API_URL}
        - VITE_CLERK_PUBLISHABLE_KEY=${VITE_CLERK_PUBLISHABLE_KEY}
        - VITE_API_URL=${VITE_API_URL}
        - VITE_WS_URL=${VITE_WS_URL}
        - VITE_ASSETS_URL=${VITE_ASSETS_URL}
        - VITE_DEPLOY_ENV=${VITE_DEPLOY_ENV:-development}
        - VITE_ENABLE_WEBSOCKET=${VITE_ENABLE_WEBSOCKET:-true}
        - VITE_ENABLE_ANALYTICS=${VITE_ENABLE_ANALYTICS:-false}
    environment:
      - VITE_CLERK_PUBLISHABLE_KEY=${VITE_CLERK_PUBLISHABLE_KEY}
      - VITE_GRAPHQL_API_URL=${VITE_GRAPHQL_API_URL}
      - VITE_API_URL=${VITE_API_URL}
      - VITE_WS_URL=${VITE_WS_URL}
      - VITE_ASSETS_URL=${VITE_ASSETS_URL}
      - VITE_DEPLOY_ENV=${VITE_DEPLOY_ENV:-development}
      - VITE_ENABLE_WEBSOCKET=${VITE_ENABLE_WEBSOCKET:-true}
      - VITE_ENABLE_ANALYTICS=${VITE_ENABLE_ANALYTICS:-false}
    ports:
      - "5173:5173"
    depends_on:
      - backend

  db:
    image: postgres:14
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_USER=${POSTGRES_USER}
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"

  rq-worker:
    build: ./apps/backend
    command: python -m a2a_platform.workers.rq_worker --queues default a2a-messages --workers ${RQ_WORKER_COUNT:-2}
    environment:
      - CDN_URL=${CDN_URL}
      - CLERK_API_KEY=${CLERK_API_KEY}
      - CLERK_JWT_PUBLIC_KEY=${CLERK_JWT_PUBLIC_KEY}
      - CLERK_WEBHOOK_SECRET=${CLERK_WEBHOOK_SECRET}
      - CORS_ORIGINS=${CORS_ORIGINS}
      - DATABASE_URL=postgresql+asyncpg://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}
      - DEBUG=${DEBUG}
      - LOG_LEVEL=${LOG_LEVEL}
      - PUBSUB_PROJECT_ID=${PUBSUB_PROJECT_ID}
      - REDIS_URL=${REDIS_URL}
      - RQ_DEFAULT_TIMEOUT=${RQ_DEFAULT_TIMEOUT:-180}
      - RQ_DEFAULT_TTL=${RQ_DEFAULT_TTL:-86400}
      - RQ_RESULT_TTL=${RQ_RESULT_TTL:-3600}
      - RQ_WORKER_COUNT=${RQ_WORKER_COUNT:-2}
      - STORAGE_BUCKET=${STORAGE_BUCKET}
    volumes:
      - ./apps/backend:/app
    depends_on:
      - redis
      - db

volumes:
  postgres_data:
  backend_data:
