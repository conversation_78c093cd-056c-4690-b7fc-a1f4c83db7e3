# GitHub Environment Variables Configuration

This document describes the GitHub repository variables and secrets that need to be configured for proper deployment of the A2A Platform.

## Environment Variables (Per Environment Configuration)

These variables should be set at the **environment level** (staging/production) in GitHub. They control CORS and host validation for each specific environment.

### CORS Configuration

- **Variable Name**: `CORS_ORIGINS`
- **Description**: Comma-separated list of allowed CORS origins for the API
- **Staging Example**: `https://www-staging.vedavivi.app,https://staging.vedavivi.app`
- **Production Example**: `https://vedavivi.app,https://www.vedavivi.app`

### Trusted Hosts Configuration

- **Variable Name**: `TRUSTED_HOSTS`
- **Description**: Comma-separated list of allowed host headers for the API
- **Staging Example**: `api-staging.vedavivi.app,www-staging.vedavivi.app,staging.vedavivi.app`
- **Production Example**: `api.vedavivi.app,vedavivi.app,www.vedavivi.app`

## How to Configure

### Setting Environment Variables (Recommended)

1. Go to your GitHub repository
2. Navigate to **Settings** → **Environments**
3. Select the environment (staging or production)
4. In the **Environment variables** section, click **Add variable**
5. Add the variables for each environment:

**For Staging Environment:**
- `CORS_ORIGINS`: `https://www-staging.vedavivi.app,https://staging.vedavivi.app`
- `TRUSTED_HOSTS`: `api-staging.vedavivi.app,www-staging.vedavivi.app,staging.vedavivi.app`

**For Production Environment:**
- `CORS_ORIGINS`: `https://vedavivi.app,https://www.vedavivi.app`
- `TRUSTED_HOSTS`: `api.vedavivi.app,vedavivi.app,www.vedavivi.app`

## Usage in Workflows

The deployment workflow (`.github/workflows/deploy-api.yml`) will:

1. Check if the environment-specific variable is set
2. Use the configured value if available
3. Fall back to sensible defaults if not configured

This allows for flexible configuration while ensuring the system works out-of-the-box with reasonable defaults.

## Security Notes

- **CORS_ORIGINS**: These are not sensitive but should be carefully configured to only allow legitimate frontend domains
- **TRUSTED_HOSTS**: These control which Host headers are accepted and should match your actual domain configuration
- Always use HTTPS URLs in production environments
- Avoid using wildcards (`*`) in production CORS configurations

## Troubleshooting

### CORS Errors
If you see CORS policy errors:
1. Verify the frontend domain is included in `CORS_ORIGINS_*` variables
2. Ensure the API has been redeployed after changing variables
3. Check browser developer tools for the exact origin being blocked

### Host Header Errors
If you see "Invalid host header" errors:
1. Verify the API domain is included in `TRUSTED_HOSTS_*` variables
2. Ensure the domain matches exactly (including subdomains)
3. Check that the CDN/proxy is forwarding the correct Host header
