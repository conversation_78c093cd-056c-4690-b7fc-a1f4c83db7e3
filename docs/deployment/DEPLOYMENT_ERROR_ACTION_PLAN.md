# Action Plan to Address Deployment Errors

Based on analysis of the GitHub Actions workflows, here's a comprehensive plan to address deployment errors:

> **Note**: The `act` (local GitHub Actions testing) hooks have been removed from the precommit configuration to streamline the development workflow. Local testing of workflows can still be performed manually using the scripts provided.

## Current Status Analysis

### Issues Identified:

1. **Missing Reusable Workflow**: The workflows reference `./.github/workflows/reusable-deploy-service.yml` which doesn't exist
2. **Missing Composite Actions**: References to composite actions in `.github/actions/` that may not exist:
   - `gcp-auth-setup`
   - `docker-build-push` 
   - `gcloud-run-deploy`
3. **Inconsistent Parameter Naming**: Mixed use of `service-name`/`service_name` across workflows
4. **Potential Docker Image Issues**: Workflows may fail on missing Docker images during local testing

## Action Plan

### Phase 1: Fix Immediate Issues (Critical)

#### 1.1 Create Missing .secrets File
```bash
# Copy the template and customize for your environment
cp .secrets.template .secrets
# Edit .secrets with appropriate test values
```

#### 1.2 Ensure .secrets is Gitignored
```bash
echo ".secrets" >> .gitignore
```

#### 1.3 Fix Workflow Parameter Naming Consistency
- **Issue**: `deploy-workers.yml` uses `service_name` while others use `service-name`
- **Fix**: Standardize on `service-name` (kebab-case) across all workflows

### Phase 2: Create Missing Infrastructure (High Priority)

#### 2.1 Create Reusable Deployment Workflow
Create `.github/workflows/reusable-deploy-service.yml` with:
- `workflow_call` trigger
- Generic inputs for service configuration
- Steps for: build, push to staging, deploy to staging, manual approval, promote to production
- Conditional logic for migrations (API service only)

#### 2.2 Create Composite Actions
Create the following composite actions in `.github/actions/`:

**a) gcp-auth-setup/action.yml**
- Authenticate with GCP using Workload Identity
- Set up gcloud CLI

**b) docker-build-push/action.yml** 
- Build Docker image
- Push to Artifact Registry
- Handle caching

**c) gcloud-run-deploy/action.yml**
- Deploy to Cloud Run
- Configure service settings
- Handle environment variables and secrets

### Phase 3: Improve Local Testing (Medium Priority)

#### 3.1 Update run-act.sh for Better Error Handling
- Add validation for required files
- Provide better error messages
- Support for different testing modes

#### 3.2 Create Mock Docker Images for Testing
- Build minimal Docker images for local testing
- Avoid pulling large production images during development

#### 3.3 Add Testing Documentation
- Document how to test workflows locally
- Provide troubleshooting guide

### Phase 4: Optional Local Testing (Manual Only)

#### 4.1 Manual Workflow Testing
- Use `./scripts/run-act.sh` for manual workflow testing when needed
- Local testing is no longer part of the precommit process
- Provide clearer failure messages

#### 4.2 Add Workflow Validation
- Validate YAML syntax
- Check for common misconfigurations
- Ensure consistent naming

## Implementation Order

### Immediate (Do First):
1. Copy `.secrets.template` to `.secrets` and ensure it's gitignored
2. Fix parameter naming in `deploy-workers.yml` (change `service_name` to `service-name`)
3. Make scripts executable: `chmod +x scripts/*.sh`

### Next Steps:
1. Create the reusable workflow file
2. Create the composite action files
3. Test one workflow at a time with act

### Testing Strategy:
1. Start with `--dry-run` flag to validate syntax
2. Use minimal Docker images for faster testing
3. Test staging jobs only initially
4. Gradually enable full workflow testing

## Quick Start Commands

```bash
# 1. Set up for local testing
cp .secrets.template .secrets
chmod +x scripts/*.sh

# 2. Run diagnosis to see current status
./scripts/diagnose-deploy-errors.sh

# 3. Run full check (including pre-commit)
./scripts/run-full-check.sh

# 4. Test specific workflow
./scripts/run-act.sh -w deploy-web.yml --dry-run
```

## Expected Timeline

- **Phase 1**: 30 minutes (immediate fixes)
- **Phase 2**: 2-3 hours (create missing files)
- **Phase 3**: 1-2 hours (improve testing)
- **Phase 4**: 1 hour (enhance integration)

**Total estimated time**: 4-6 hours

## Success Criteria

- All workflows pass `act --dry-run` tests
- Pre-commit hooks run without errors
- Consistent naming across all workflows
- Clear documentation for local testing
- Robust error handling and reporting