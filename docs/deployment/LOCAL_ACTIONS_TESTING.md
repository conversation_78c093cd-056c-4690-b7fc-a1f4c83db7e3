# Testing GitHub Actions Workflows Locally (Manual)

This guide explains how to manually test GitHub Actions workflows locally using [act](https://github.com/nektos/act).

> **Note**: Local GitHub Actions testing using `act` has been removed from the precommit hooks to streamline the development workflow. This testing is now available only for manual use when needed.

## Installation

### macOS
```bash
brew install act
```

### Linux
```bash
curl -s https://raw.githubusercontent.com/nektos/act/master/install.sh | sudo bash
```

### Windows
```powershell
choco install act-cli
```

## Usage

### Configuration

Create an `.actrc` file in the root of your repository with the following content:

```bash
# Act configuration file
# See https://github.com/nektos/act#configuration

# Use medium-sized runner image (has more pre-installed software)
-P ubuntu-latest=ghcr.io/catthehacker/ubuntu:act-latest

# Bind secrets from environment variables
--secret-file=.secrets

# Default event
--eventpath=.github/workflows/event.json
```

Create a `.secrets` file in the root of your repository with any required secrets:

```
# Secrets for act
# Replace these with actual values for local testing

GCP_PROJECT_ID=your-gcp-project-id
GCP_SA_EMAIL=<EMAIL>
GCP_TF_STATE_BUCKET=your-terraform-state-bucket
WORKLOAD_IDENTITY_PROVIDER=projects/123456789/locations/global/workloadIdentityPools/github-pool/providers/github-provider
STAGING_DB_CONNECTION_STRING=postgresql://user:password@host:port/database
PRODUCTION_DB_CONNECTION_STRING=postgresql://user:password@host:port/database
```

This file is already added to `.gitignore` to prevent committing sensitive information.

### Sample Event

Create a sample event file at `.github/workflows/event.json`:

```json
{
  "repository": {
    "name": "a2a-platform",
    "owner": {
      "name": "blkops-collective"
    }
  },
  "event_name": "workflow_dispatch",
  "inputs": {
    "environment": "staging"
  }
}
```

### Running Workflows

We've created a helper script to simplify running act with our configuration. The script handles setting up the necessary local infrastructure for testing.

```bash
# Run a job from a workflow
./scripts/run-act.sh -w <workflow-file> -j <job-name>

# Example: run the terraform-plan job from deploy-infra-manual.yml
./scripts/run-act.sh -w deploy-infra-manual.yml -j terraform-plan -i environment=staging

# Run with debug output
./scripts/run-act.sh -w <workflow-file> -j <job-name> --debug

# Do a dry-run to see what would happen
./scripts/run-act.sh -w <workflow-file> -j <job-name> --dry-run
```

## Testing Infrastructure Workflows

When testing infrastructure workflows locally, note that they require cloud credentials and may not work fully in local environments. For testing purposes, you can use the manual deployment workflows:

- `deploy-infra-manual.yml` - Manual infrastructure deployment workflow
- `deploy-infra-auto.yml` - Automated infrastructure deployment workflow

## Interpreting Terraform Errors

When running Terraform workflows locally, you'll see errors like:

```
Error when reading or editing Network Not Found : a2a-staging-network: Get "https://compute.googleapis.com/compute/v1/projects/local-test-project/global/networks/a2a-staging-network?alt=json&prettyPrint=false": private key should be a PEM or plain PKCS1 or PKCS8; parse error: x509: failed to parse private key
```

These are expected when testing locally since:
1. We're using mock credentials that aren't valid for real GCP API calls
2. The resources we're trying to look up don't exist in our mock environment

Despite these errors, successful validation (`terraform validate`) confirms that our Terraform code is syntactically correct. In real CI/CD environments with proper credentials, these errors won't occur.

## Additional Options

Run `./scripts/run-act.sh --help` to see all available options:

```
Usage: run-act.sh [options]

Run GitHub Actions workflows locally using act.

Options:
  -w, --workflow FILE     Specify the workflow file to run (required)
  -j, --job JOB           Specify a particular job to run
  -e, --event EVENT       Specify the event type (default: workflow_dispatch)
  -i, --input KEY=VALUE   Specify input values for workflow_dispatch (can be used multiple times)
  -d, --debug             Enable debug mode
  -n, --dry-run           Show what would be executed without actually running it
  -a, --arch ARCH         Specify container architecture (default: linux/amd64)
  -h, --help              Show this help message
```

## Limitations

- Some GitHub Actions features might not work locally
- Authentication to external services needs to be handled differently
- Limited GitHub context variables are available

## Troubleshooting

- If you encounter Docker errors, make sure Docker is running
- For permission issues, try running with `sudo` on Linux
- For M1/M2 Macs, always use `--container-architecture linux/amd64`
- Check your `.secrets` file for proper formatting if you get authentication errors