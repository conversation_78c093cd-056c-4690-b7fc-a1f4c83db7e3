# Deployment Strategy Update: Main Branch → Staging

## 🎯 Changes Made

Updated GitHub Actions workflows to deploy to **staging** environment when code is merged into the `main` branch, instead of production.

## 📝 Modified Workflows

### 1. Terraform CDN Infrastructure (`.github/workflows/terraform-cdn.yml`)

**Changes:**
- ✅ Added environment determination logic
- ✅ Push to `main` branch now deploys to **staging**
- ✅ Manual workflow dispatch allows choosing environment (staging/production)
- ✅ Pull requests only run plan (no deployment)
- ✅ All terraform commands updated to use correct working directory

**Behavior:**
- **Push to main**: Deploys to staging
- **Pull requests**: Plan only (no deployment) 
- **Manual dispatch**: Choose environment (staging/production)

### 2. Frontend Asset Deployment (`.github/workflows/frontend-cdn-deploy.yml`)

**Changes:**
- ✅ Removed logic that deployed to production on main branch pushes
- ✅ Push to `main` branch now deploys to **staging**
- ✅ Added conditions to prevent deployment on pull requests
- ✅ Manual workflow dispatch allows choosing environment (staging/production)

**Behavior:**
- **Push to main**: Deploys to staging
- **Pull requests**: Build only (no deployment)
- **Manual dispatch**: Choose environment (staging/production)

## 🔄 Deployment Flow

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Pull Request  │    │   Merge to Main │    │ Manual Dispatch │
│                 │    │                 │    │                 │
│ • Build only    │    │ • Deploy to     │    │ • Choose env    │
│ • No deployment │────▶│   STAGING       │    │ • staging/prod  │
│ • Terraform plan│    │ • Auto-deploy   │    │ • Full deploy   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🎯 Production Deployment

To deploy to production, use **manual workflow dispatch**:

1. Go to GitHub Actions
2. Select the workflow
3. Click "Run workflow"
4. Choose "production" environment
5. Click "Run workflow"

## 🛡️ Safety Improvements

- **Pull requests** no longer trigger deployments (build/plan only)
- **Staging-first approach** prevents accidental production deployments
- **Explicit production deployments** via manual workflow dispatch
- **Environment isolation** through proper terraform working directories

## ✅ Validation

Both workflows pass YAML lint validation and follow GitHub Actions best practices.
