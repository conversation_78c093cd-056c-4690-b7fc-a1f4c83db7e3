# Web Development Container Permissions Guide

## Overview

This document explains how to deal with permission issues when using Docker for frontend development.

## Problem

When running the frontend development environment in Docker, you may encounter permissions errors:
- `EACCES: Permission denied while installing PACKAGE_NAME_GOES_HERE`
- Issues when running `bun install` or other commands that need to write to `/app/node_modules`

These issues occur because:
1. The container runs as the `bunjs` user (UID 1001)
2. The mounted volumes may have different ownership on the host system

## Solution

We've implemented several improvements to handle permissions correctly:

1. **Updated docker-compose.dev.yml**:
   - No longer uses root user for any services
   - Uses enhanced entrypoint scripts for permission checking

2. **Permission Checking Scripts**:
   - Added `/app/fix-permissions.sh` for runtime permission checking
   - Enhanced entrypoint scripts for both frontend and storybook services

3. **Fix Permissions Script**:
   - `./scripts/fix-web-permissions.sh` sets correct permissions on host directories

## Usage

If you encounter permission issues:

1. Run the fix permissions script:
   ```bash
   ./scripts/fix-web-permissions.sh
   ```

2. Restart your containers:
   ```bash
   docker-compose -f docker-compose.dev.yml down
   docker-compose -f docker-compose.dev.yml up -d
   ```

3. Run bun commands normally:
   ```bash
   docker-compose -f docker-compose.dev.yml exec frontend bun install
   ```

## Best Practices

- Never run containers as root
- Use the provided scripts to manage permissions
- If persistent issues occur, report them to the team

## Troubleshooting

If you still encounter permission issues after following these steps:

1. Check that directories have proper permissions:
   ```bash
   ls -la ./apps/web/node_modules
   ```

2. Try recreating node_modules from scratch:
   ```bash
   rm -rf ./apps/web/node_modules
   mkdir -p ./apps/web/node_modules
   chmod -R 777 ./apps/web/node_modules
   docker-compose -f docker-compose.dev.yml restart frontend
   ```
