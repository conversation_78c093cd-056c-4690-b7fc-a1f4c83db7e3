# Registering Specialized Agents

This document outlines how to register new Specialized Agents (SAs) into the A2A Platform using the provided administration script. This process is typically performed by backend administrators or developers responsible for managing the available SAs in the system.

## Prerequisites

1.  **Access to the Backend Environment:** You need access to an environment where the A2A Platform backend code is deployed or accessible, and where you can execute Python scripts.
2.  **Database Connectivity:** The script requires access to the platform's PostgreSQL database. Ensure that the environment variables or configuration files used by the backend to connect to the database are correctly set up and accessible to the script (specifically, the `a2a_platform.db.session.SessionLocal` should be correctly configured).
3.  **Python Environment:** A Python environment with all necessary dependencies for the backend application installed (e.g., `SQLAlchemy`, `Pydantic`, and any database drivers).
4.  **Agent Configuration File:** You will need a JSON file containing the definitions of the agent(s) you wish to register.

## The `register_agent.py` Script

The primary tool for registering agents is the `apps/backend/scripts/register_agent.py` script.

### Usage

The script is run from the command line:

```bash
python apps/backend/scripts/register_agent.py <path_to_config_file.json> [--dry-run]
```

**Arguments:**

*   `<path_to_config_file.json>` (Required): The path to the JSON file containing the agent configuration(s).
*   `--dry-run` (Optional): If this flag is provided, the script will validate the agent configurations against the schema but will **not** write any data to the database. This is useful for checking your configuration file before committing changes.

### Agent Configuration File Format

The JSON configuration file can contain either a single agent definition object or a list of agent definition objects. Each agent definition object must adhere to the following schema:

```json
{
  "agent_definition_id": "string (required, unique)",
  "name": "string (required)",
  "version": "string (required)",
  "description": "string (optional)",
  "endpoint_url": "string (URL, optional)",
  "async_queue_name": "string (optional)",
  "capabilities": {
    "additionalProp1": {},
    "additionalProp2": {}
  },
  "skills": [
    {
      "name": "string (required)",
      "parameters_schema": {
        "additionalProp1": {}
      },
      "description": "string (optional)"
    }
  ],
  "authentication_info": {
    "additionalProp1": {}
  },
  "status": "string (optional, 'active' or 'inactive', defaults to 'active')"
}
```

**Field Descriptions:**

*   `agent_definition_id` (string, required, unique): A unique identifier for this agent type and version (e.g., "summarizer_v1.2", "calendar_gcp_agent_v0.9"). This is the primary key.
*   `name` (string, required): A human-readable name for the agent (e.g., "Advanced Text Summarizer").
*   `version` (string, required): The version of this agent definition (e.g., "1.2.0", "0.9.beta").
*   `description` (string, optional): A brief description of the agent's purpose and functionality.
*   `endpoint_url` (string, URL, optional): If the agent communicates synchronously via HTTP, provide its internal endpoint URL (e.g., "http://sa-summarizer-service.internal:80/invoke").
*   `async_queue_name` (string, optional): If the agent communicates asynchronously via a message queue, provide the name of the queue it listens to (e.g., "sa_calendar_tasks_production").
    *   *Note: An agent can have an `endpoint_url`, an `async_queue_name`, or both.*
*   `capabilities` (object, optional): A JSON object describing various capabilities or fixed parameters of the agent (e.g., `{"streaming_support": true, "max_input_tokens": 10000}`).
*   `skills` (array of objects, optional): A list of specific skills the agent offers. Each skill object has:
    *   `name` (string, required): The name of the skill (e.g., "generateSummary", "scheduleMeeting").
    *   `parameters_schema` (object, optional): A JSON object describing the schema for parameters this skill accepts (e.g., `{"target_language": {"type": "string", "enum": ["en", "es", "fr"]}}`). This can be used for validation or generating UIs.
    *   `description` (string, optional): A description of the skill.
*   `authentication_info` (object, optional): A JSON object containing information about how other internal services should authenticate with this agent if needed (e.g., `{"type": "service_account_jwt", "audience": "agent-summarizer"}`).
*   `status` (string, optional): The status of the agent. Must be either `"active"` or `"inactive"`. Defaults to `"active"` if not provided. Inactive agents will typically not be discoverable or usable by Personal Assistants.

### Example Configuration File (`example_agents.json`)

```json
[
  {
    "agent_definition_id": "text_analyzer_v1.0",
    "name": "Advanced Text Analyzer",
    "version": "1.0.0",
    "description": "Provides sentiment analysis and keyword extraction.",
    "endpoint_url": "http://text-analyzer-service.internal/api/analyze",
    "capabilities": {
      "max_text_length": 50000,
      "supported_languages": ["en", "es"]
    },
    "skills": [
      {
        "name": "sentiment_analysis",
        "description": "Returns the sentiment of the provided text."
      },
      {
        "name": "keyword_extraction",
        "description": "Extracts key phrases from the text.",
        "parameters_schema": {
          "max_keywords": {"type": "integer", "default": 10}
        }
      }
    ],
    "authentication_info": {"scheme": "bearer", "token_type": "internal_service_jwt"},
    "status": "active"
  },
  {
    "agent_definition_id": "image_processor_v0.9-beta",
    "name": "Image Processing Service (Beta)",
    "version": "0.9.beta",
    "async_queue_name": "image_processing_jobs",
    "capabilities": {
      "supported_formats": ["jpeg", "png"],
      "max_resolution": "16MP"
    },
    "skills": [
      {
        "name": "resize_image",
        "parameters_schema": {
          "width": {"type": "integer"},
          "height": {"type": "integer"}
        }
      },
      {
        "name": "apply_filter",
        "parameters_schema": {
          "filter_name": {"type": "string", "enum": ["grayscale", "sepia"]}
        }
      }
    ],
    "status": "inactive"
  }
]
```

### Running the Script

1.  Navigate to the root of the backend application directory (e.g., `cd apps/backend`).
2.  Ensure your Python environment is activated.
3.  Execute the script:
    ```bash
    python scripts/register_agent.py path/to/your/example_agents.json
    ```
4.  The script will output the progress for each agent configuration, indicating whether validation passed and if the registration was successful (or would be, in `--dry-run` mode).
5.  A final summary will show the total number of successful and failed registrations.

### Troubleshooting

*   **Import Errors:** Ensure `SRC_DIR` in the script correctly points to your `src` directory and that your Python environment includes all backend dependencies.
*   **Database Connection Errors:** Verify that `a2a_platform.db.session.SessionLocal` is correctly implemented and that the application has the necessary credentials and network access to the database. The script will print an error message if it cannot establish a session.
*   **Validation Errors:** If Pydantic `ValidationError` occurs, the script will print the specific fields that failed validation and why. Correct your JSON configuration file accordingly.
*   **Duplicate Agent ID Errors:** If you try to register an agent with an `agent_definition_id` that already exists, the service layer will raise a `ValueError`, which the script will report.