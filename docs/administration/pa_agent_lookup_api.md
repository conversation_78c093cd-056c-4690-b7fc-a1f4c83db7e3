# Internal API: Specialized Agent Lookup for Personal Assistants

This document describes the internal REST API endpoints that Personal Assistants (PAs) can use to discover and retrieve information about registered Specialized Agents (SAs). This is crucial for enabling Agent-to-Agent (A2A) communication.

## Base URL

All endpoints described here are prefixed with: `/internal/agents`

*(The full base URL will depend on your service deployment, e.g., `http://a2a-backend-service.internal-cluster.local/internal/agents`)*

## Authentication

All requests to these internal API endpoints **must** be authenticated using the platform's standard internal service-to-service authentication mechanism. Refer to the main A2A platform security documentation for details on how to generate and include the necessary authentication tokens/headers. Unauthorized requests will be rejected.

## API Endpoints

### 1. List Registered Specialized Agents

Retrieves a list of specialized agents, with optional filters and pagination.

*   **Endpoint:** `GET /`
    *   (Full path example: `/internal/agents/`)
*   **Summary:** List Registered Specialized Agents
*   **Query Parameters:**
    *   `status` (string, optional): Filter by agent status.
        *   Allowed values: `"active"`, `"inactive"`.
        *   If not provided, agents of any status might be returned (or a default, like "active", might apply depending on service implementation; current service defaults to no status filter if this parameter is omitted).
    *   `capability` (string, optional, can be repeated): Filter by capability names. Agents returned must possess ALL specified capabilities.
        *   Example: `?capability=can_summarize&capability=streaming_output`
    *   `skill` (string, optional, can be repeated): Filter by skill names. Agents returned must possess ALL specified skills.
        *   Example: `?skill=generate_report&skill=data_visualization`
    *   `skip` (integer, optional, default: `0`): Number of records to skip for pagination. Must be `>= 0`.
    *   `limit` (integer, optional, default: `100`): Maximum number of records to return. Must be `>= 1` and `<= 200` (or as defined by API limits).
*   **Successful Response (200 OK):**
    *   Content-Type: `application/json`
    *   Body: `RegisteredAgentListResponse` schema
        ```json
        {
          "items": [
            // Array of RegisteredAgentRead objects
            {
              "agent_definition_id": "string",
              "name": "string",
              "description": "string",
              "version": "string",
              "endpoint_url": "string (URL) | null",
              "async_queue_name": "string | null",
              "capabilities": {},
              "skills": [
                {
                  "name": "string",
                  "parameters_schema": {},
                  "description": "string"
                }
              ],
              "authentication_info": {},
              "status": "string ('active' or 'inactive')",
              "created_at": "datetime (ISO 8601)",
              "updated_at": "datetime (ISO 8601)"
            }
            // ... more items
          ],
          "total": "integer (total number of matching items before pagination)"
        }
        ```
*   **Error Responses:**
    *   `422 Unprocessable Entity`: If query parameters are invalid (e.g., bad `status` value).
        ```json
        {
          "detail": "Invalid status value. Allowed values are 'active' or 'inactive'."
        }
        ```
    *   (Other standard HTTP errors like 401 Unauthorized, 500 Internal Server Error)

### 2. Get a Specific Specialized Agent by ID

Retrieves details for a single specialized agent by its unique `agent_definition_id`.

*   **Endpoint:** `GET /{agent_definition_id}`
    *   (Full path example: `/internal/agents/summarizer_v1.0`)
*   **Summary:** Get a Specific Specialized Agent by ID
*   **Path Parameters:**
    *   `agent_definition_id` (string, required): The unique definition ID of the agent to retrieve.
*   **Successful Response (200 OK):**
    *   Content-Type: `application/json`
    *   Body: `RegisteredAgentRead` schema
        ```json
        {
          "agent_definition_id": "summarizer_v1.0",
          "name": "Text Summarizer",
          "description": "Summarizes text efficiently.",
          "version": "1.0.0",
          "endpoint_url": "http://summarizer-service/invoke",
          "async_queue_name": null,
          "capabilities": {"max_chars": 10000},
          "skills": [{"name": "summarize"}],
          "authentication_info": {},
          "status": "active",
          "created_at": "2023-10-26T10:00:00Z",
          "updated_at": "2023-10-27T12:30:00Z"
        }
        ```
*   **Error Responses:**
    *   `404 Not Found`: If no agent with the specified `agent_definition_id` exists.
        ```json
        {
          "detail": "Agent with definition ID 'non_existent_agent_id' not found."
        }
        ```
    *   (Other standard HTTP errors)

## Schema Definitions (Pydantic based)

### `AgentSkill`

```json
{
  "name": "string (required)",
  "parameters_schema": "object (optional, JSON schema for skill parameters)",
  "description": "string (optional)"
}
```

### `RegisteredAgentRead` (for single agent and items in list)

```json
{
  "agent_definition_id": "string (required)",
  "name": "string (required)",
  "description": "string (optional)",
  "version": "string (required)",
  "endpoint_url": "string (URL, optional, null if not present)",
  "async_queue_name": "string (optional, null if not present)",
  "capabilities": "object (optional)",
  "skills": "array[AgentSkill] (optional)",
  "authentication_info": "object (optional)",
  "status": "string (required, 'active' or 'inactive')",
  "created_at": "datetime (ISO 8601 string, required)",
  "updated_at": "datetime (ISO 8601 string, required)"
}
```

### `RegisteredAgentListResponse` (for list endpoint)

```json
{
  "items": "array[RegisteredAgentRead] (required)",
  "total": "integer (required, total matching items before pagination)"
}
```

## Important Notes for PA Developers

*   **Service Discovery:** These endpoints are intended for internal service discovery. PAs should use them to find the necessary details (`endpoint_url`, `async_queue_name`, `capabilities`, `skills`, `authentication_info`) of SAs they need to interact with.
*   **Caching:** Consider implementing caching on the PA side for frequently accessed or relatively static agent information to reduce load on this API, adhering to appropriate cache invalidation strategies.
*   **Error Handling:** Implement robust error handling in PAs for API responses, including 404s, 422s, and potential 5xx server errors.
*   **Filtering Complexity:** The current implementation of `capabilities` and `skills` filtering in the service layer is basic (key existence for capabilities, placeholder for skills). If more complex JSON querying is implemented in the future (e.g., checking specific values within capabilities, or advanced array operations for skills), this API documentation will be updated. For now, rely on filtering by `status` and then perform more complex capability/skill matching on the PA side if needed after retrieving a broader list.