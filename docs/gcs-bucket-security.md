# 🛡️ GCS Bucket Security Configuration

This document explains the security configuration for the Google Cloud Storage buckets used in the CDN migration.

## 🔒 Security Measures Implemented

### GCS Bucket Configuration

1. **Uniform Bucket-Level Access**: Enabled to ensure IAM policies are consistent across the bucket
   ```terraform
   uniform_bucket_level_access = true
   ```

2. **Public Access Prevention**: Explicitly enforced to prevent public access
   ```terraform
   public_access_prevention = "enforced"
   ```

3. **Encryption**: Optional KMS encryption available
   ```terraform
   dynamic "encryption" {
     for_each = var.kms_key_name != null ? [1] : []
     content {
       default_kms_key_name = var.kms_key_name
     }
   }
   ```

### Access Control

1. **CDN Service Account Access**: Only the Cloudflare CDN service account can read assets
   ```terraform
   resource "google_storage_bucket_iam_binding" "cloudflare_access" {
     bucket = google_storage_bucket.web_assets.name
     role   = "roles/storage.objectViewer"
     members = [
       "serviceAccount:${var.cdn_service_account}"
     ]
   }
   ```

2. **Deployment Service Account**: A dedicated service account for CI/CD pipelines
   ```terraform
   resource "google_service_account" "cdn_deployer" {
     account_id   = "${var.environment}-cdn-deployer"
     display_name = "CDN Deployer for ${var.environment}"
     description  = "Service account for deploying static assets to the CDN"
   }
   
   resource "google_storage_bucket_iam_member" "deployer_access" {
     bucket = google_storage_bucket.web_assets.name
     role   = "roles/storage.objectAdmin"
     member = "serviceAccount:${google_service_account.cdn_deployer.email}"
   }
   ```

## 🔄 Access Flow

1. **End Users**: Request content through Cloudflare CDN
2. **Cloudflare CDN**: Authenticates with GCS using service account credentials
3. **GCS Bucket**: Validates requests come from authorized service accounts only
4. **Content Delivery**: Served through Cloudflare's global edge network

## 🚫 What's Blocked

- ❌ Direct public access to GCS bucket
- ❌ Unauthenticated requests to storage objects
- ❌ Access from unauthorized service accounts
- ❌ Broad permissions beyond what's needed

## ✅ Security Best Practices Applied

- ✅ Principle of least privilege
- ✅ Service account-based authentication
- ✅ Uniform bucket-level access
- ✅ Public access prevention
- ✅ Optional encryption at rest