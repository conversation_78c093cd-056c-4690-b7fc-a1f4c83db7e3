# HTTPS/WSS Security Implementation Guide

This document provides information about the HTTPS/WSS security implementation in the A2A Platform, including how to set up and test secure connections.

## 🔒 Overview

All client-backend communication in the A2A Platform uses HTTPS/WSS encryption to protect data in transit from interception and tampering. This includes:

- HTTPS for all API endpoints (GraphQL/REST)
- WSS for all WebSocket connections (GraphQL subscriptions)
- Strict security headers with HSTS enforcement
- Certificate monitoring and auto-renewal

## 🚀 Local Development with HTTPS

To enable HTTPS in local development:

1. Generate SSL certificates for localhost:

```bash
./scripts/generate-ssl-certs.sh
```

2. Add the following to your `.env` file:

```
# Enable HTTPS for frontend development
VITE_USE_HTTPS=true

# Optionally enable HTTPS for backend development
ENFORCE_HTTPS=true
```

3. Restart your development server:

```bash
docker compose -f docker-compose.dev.yml down
docker compose -f docker-compose.dev.yml up
```

## 🔍 Testing HTTPS Enforcement

To verify HTTPS enforcement:

```bash
# Run backend tests
./scripts/run-backend-tests.sh apps/backend/tests/integration/test_https_enforcement.py

# Run frontend E2E tests
./scripts/run-frontend-tests.sh --e2e --spec cypress/e2e/https-enforcement.cy.ts
```

## 🔧 Infrastructure Configuration

The HTTPS infrastructure is configured using Terraform:

- Load balancers terminate SSL with proper certificates
- Cloud Run services are configured for HTTPS-only ingress
- Certificate Manager handles auto-renewal
- Monitoring alerts for certificate expiration

## 📝 Certificate Monitoring

We monitor SSL certificates for expiration with:

```bash
./scripts/monitor-ssl-certificates.sh
```

This script is also run automatically via CI/CD to ensure certificates are renewed before expiration.

## 🔒 Security Headers

The following security headers are included in all responses:

- `Strict-Transport-Security: max-age=31536000; includeSubDomains; preload` (HSTS)
- `Content-Security-Policy` with HTTPS/WSS enforcement
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `Referrer-Policy: strict-origin-when-cross-origin`
- `X-XSS-Protection: 1; mode=block`

## 📚 Additional Resources

- [ADR-002 Backend Overview](../../adrs/ADR-002-Backend-Overview.md) - See Security Principles section
- [WebSocket Security Analysis](../../reports/20250522050123_websocket_security_analysis.md)
- [OWASP Security Headers](https://owasp.org/www-project-secure-headers/)
