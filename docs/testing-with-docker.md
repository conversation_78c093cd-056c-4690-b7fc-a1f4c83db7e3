# Testing with Docker

This document describes how to run tests in the A2A Platform project. Docker is the default testing environment.

## Overview

The A2A Platform provides a streamlined workflow for running tests using Docker as the default environment. This approach ensures consistent test environments across different development machines, with a separate configuration for CI/CD pipelines.

## Prerequisites

- <PERSON><PERSON> and Docker Compose installed
- The A2A Platform repository cloned

## Test Environment Configuration

The test environment is configured using environment variables defined in the Docker Compose files and loaded from `.env.test` at the root of the project. These environment variables include:

- Database settings
- Redis settings
- Auth settings
- CORS settings
- File storage settings
- Pub/Sub settings

## Running Tests

### Using the run-backend-tests.sh Script

The easiest way to run tests is to use the `run-backend-tests.sh` script:

```bash
./scripts/run-backend-tests.sh
```

This will:
1. Use the `.env.test` file for environment variables
2. Start the necessary Docker containers
3. Run all tests in the Docker environment

### Test Type Options

You can specify which tests to run:

```bash
# Run only unit tests
./scripts/run-backend-tests.sh --unit

# Run only integration tests
./scripts/run-backend-tests.sh --integration

# Run only end-to-end tests
./scripts/run-backend-tests.sh --e2e
```

### Additional Options

```bash
# Run tests with coverage report
./scripts/run-backend-tests.sh --coverage

# Run tests with verbose output
./scripts/run-backend-tests.sh --verbose

# Run tests with database setup
./scripts/run-backend-tests.sh --setup
```

### Running Tests in CI Environment

For CI environments, use the `--ci` flag:

```bash
./scripts/run-backend-tests.sh --ci
```

This will run tests using the local PostgreSQL instance instead of Docker.

## Using the Docker Test Script Directly

You can also use the Docker test script directly:

```bash
./scripts/run-docker-tests.sh
```

This script accepts the same options as `run-backend-tests.sh` (except for `--ci` which is not applicable in Docker mode).

## How It Works

The Docker testing workflow:

1. Uses a dedicated `docker-compose.test.yml` file for the test environment
2. Uses environment variables defined in Docker Compose files
3. Starts a PostgreSQL container for testing
4. Runs the tests in a Docker container with the backend code mounted as a volume
5. Reports test results

The CI testing workflow:

1. Uses local PostgreSQL instance
2. Runs tests directly on the host machine
3. Requires environment variables to be set in the CI environment

## Troubleshooting

If you encounter issues:

1. Ensure Docker is running (for Docker mode)
2. Check that the environment variables are correctly defined in Docker Compose files or CI environment
3. Try stopping all containers and starting fresh:
   ```bash
   docker compose down
   ./scripts/run-backend-tests.sh
   ```
4. Check Docker logs for more detailed error messages:
   ```bash
   docker compose -f docker-compose.test.yml logs
   ```
5. For CI issues, check that the local PostgreSQL is properly configured and accessible
