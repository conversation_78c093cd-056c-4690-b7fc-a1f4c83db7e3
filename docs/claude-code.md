# Claude Code Integration for A2A Platform

This document provides comprehensive guidance on using <PERSON> with the A2A Platform codebase, explaining the custom features, directory structure, and best practices implemented for an optimal AI-assisted development experience.

## Table of Contents

1. [Introduction](#introduction)
2. [Directory Structure](#directory-structure)
3. [Configuration Options](#configuration-options)
4. [Code Templates](#code-templates)
5. [Workflows and Custom Commands](#workflows-and-custom-commands)
6. [Observed Development Patterns](#observed-development-patterns)
7. [Best Practices](#best-practices)
8. [Troubleshooting](#troubleshooting)
9. [Extending the Integration](#extending-the-integration)

## Introduction

The A2A Platform has been enhanced with a tailored Claude integration that leverages Anthropic's best practices to improve AI-assisted development. This integration provides:

- Structured guidelines in the CLAUDE.md file
- Custom code templates for consistent pattern generation
- Workflow documentation with slash commands
- Project-specific configurations

By following the structures and patterns outlined in this document, you'll be able to work more efficiently with <PERSON> and maintain high code quality across the A2A Platform.

## Directory Structure

The Claude integration uses a `.claude` directory at the project root with the following structure:

```
.claude/
  ├── settings.json     # Project-specific configuration (renamed from config.json)
  ├── commands/         # Individual command files (following best practices)
  │   ├── explore.md
  │   ├── test.md
  │   ├── check.md
  │   ├── graphql-update.md
  │   ├── research.md
  │   ├── deep-think.md
  │   └── [50+ command files]
  └── workflows/        # Documentation for complex workflows
      ├── graphql-development.md
      ├── feature-development.md
      ├── test-optimization.md
      ├── code-quality.md
      └── deployment-troubleshooting.md
```

### CLAUDE.md

The root CLAUDE.md file is the primary entry point for Claude's context understanding. It contains:

- Core project commands
- Code style guidelines
- Directory structure information
- Database and API standards
- Security practices
- Custom slash commands

Claude automatically reads this file when starting a conversation, ensuring a consistent foundation of knowledge about the project.

## Configuration Options

The Claude integration uses multiple configuration files to customize behavior:

### settings.json

The `.claude/settings.json` file configures Claude's behavior for the A2A Platform, with these key settings:

```json
{
  "version": "1.0",
  "project": "a2a-platform",
  "preferences": {
    "codingStyle": {
      "python": {
        "formatter": "black",
        "lineLength": 88,
        "importSorter": "isort",
        "typeChecker": "mypy",
        "linter": "ruff"
      },
      "typescript": {
        "quotes": "double",
        "linter": "eslint",
        "formatter": "prettier"
      }
    }
    // Additional configuration...
  },
  "allowedTools": [
    "bun",
    "bunx"
  ]
}
```

### commands/ Directory

Following Anthropic's best practices, commands are now stored as individual markdown files in `.claude/commands/`. Each command file contains instructions and can use `$ARGUMENTS` for dynamic input:

**Example command file (`/check.md`):**
```markdown
Run pre-commit checks (linting, formatting, type checking). Execute:
```bash
./scripts/precommit-check.sh $ARGUMENTS
```

Verifies code quality standards before committing. Includes Black, ESLint, mypy, and other checks.

**Current command categories:**
- **Core Unified Commands**: `test`, `db`, `check`, `graphql`, `run` - Smart commands with subcommands
- **Docker Environment**: `up`, `down`, `logs`, `shell`, `dev`, `test-env`, `dc` - Environment-aware operations
- **Think-Tank Integration**: `research`, `deep-think`, `track-decision`, `feature-plan` - AI-enhanced workflows
- **Development Tools**: `build`, `fresh`, `debug`, `diagnose` - Development utilities
- **Analysis & Security**: `security`, `performance`, `optimize` - Code analysis tools

### Customizing Configuration

To modify Claude's behavior for your specific needs:

1. **Settings**: Edit `.claude/settings.json` for coding style preferences and tool permissions
2. **Commands**: Add new `.md` files to `.claude/commands/` or modify existing command files
3. **Workflows**: Create workflow documentation in `.claude/workflows/` for complex processes
4. Claude will automatically use the updated configuration in future sessions

## Code Templates

The `.claude/templates/` directory contains starter templates for common code patterns in the A2A Platform. These ensure consistency and adherence to project standards.

### Available Templates

#### FastAPI Route Template

```python
# Example usage:
# "Claude, create a new FastAPI route for managing user profiles"

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from apps.backend.api.deps import get_db
from apps.backend.schemas import UserProfileSchema

router = APIRouter(
    prefix="/profiles",
    tags=["user-profiles"],
)

@router.get("/{profile_id}", response_model=UserProfileSchema)
async def get_profile(
    profile_id: int,
    db: AsyncSession = Depends(get_db),
):
    """
    Get a specific user profile by ID.
    """
    # Implementation here
    pass
```

#### GraphQL Schema Template

```graphql
# Example usage:
# "Claude, create a GraphQL schema for the notification system"

type Notification {
  id: ID!
  userId: ID!
  message: String!
  isRead: Boolean!
  type: NotificationType!
  createdAt: DateTime!
}

enum NotificationType {
  SYSTEM
  MENTION
  ALERT
}

type Query {
  notifications(isRead: Boolean): [Notification!]!
  notification(id: ID!): Notification
}

type Mutation {
  markNotificationAsRead(id: ID!): Notification!
  clearAllNotifications: Boolean!
}
```

#### React Component Template

```tsx
// Example usage:
// "Claude, create a React component for a notification badge"

import { FC, useState, useEffect } from "react";
import { cn } from "@/lib/utils";

interface NotificationBadgeProps {
  /** Number of unread notifications */
  count?: number;
  /** Handler for when the badge is clicked */
  onClick?: () => void;
  /** Additional CSS class names */
  className?: string;
}

/**
 * Displays a notification counter badge
 */
export const NotificationBadge: FC<NotificationBadgeProps> = ({
  count = 0,
  onClick,
  className,
}) => {
  return (
    <div
      className={cn("rounded-full bg-red-500 px-2 text-white text-sm", className)}
      onClick={onClick}
    >
      {count}
    </div>
  );
};

export default NotificationBadge;
```

### Requesting a Template

To ask Claude to use a template:

1. Refer to the specific template: "Use the FastAPI route template to create..."
2. Provide the necessary context: "I need a route for user profiles with these fields..."
3. Claude will adapt the template to your specific requirements

## Workflows and Custom Commands

The A2A Platform includes documented workflows accessible via custom slash commands:

### Core Unified Commands (Smart Defaults)

| Command | Description | Example Usage |
|---------|-------------|--------------|
| `/test [target]` | Unified testing command | `/test unit`, `/test frontend`, `/test all` |
| `/db [command]` | Database operations | `/db migrate`, `/db reset`, `/db backup` |
| `/check [type]` | Code quality checks | `/check`, `/check fix`, `/check security` |
| `/graphql [action]` | GraphQL operations | `/graphql update`, `/graphql test` |
| `/run [task]` | Common dev tasks | `/run dev`, `/run test unit` |

### Docker Environment Commands

| Command | Description | Example Usage |
|---------|-------------|--------------|
| `/up [env]` | Start containers | `/up dev`, `/up test` |
| `/down [env]` | Stop containers | `/down test --volumes` |
| `/logs [env] [service]` | View logs | `/logs dev frontend` |
| `/shell [env] [service]` | Container shell | `/shell test db` |
| `/dev [cmd]` | Dev environment shortcuts | `/dev up`, `/dev logs` |
| `/test-env [cmd]` | Test environment shortcuts | `/test-env clean` |
| `/dc` | Direct docker compose | `/dc ps`, `/dc -f docker-compose.dev.yml build` |

### Other Commands

| Command | Description | Example Usage |
|---------|-------------|--------------|
| `/build` | Build Docker images | `/build` |
| `/deploy-dev` | Deploy to development | `/deploy-dev` |
| `/debug` | Diagnose errors | `/debug` |
| `/fresh` | Clean restart | `/fresh` |
| `/performance` | Performance analysis | `/performance` |
| `/security` | Security scan | `/security` |

### Command Design Principles

**Smart Defaults:**
- Commands do the most common thing when run without arguments
- `/test` runs backend tests, `/db` opens console, `/graphql` generates types
- Add subcommands for specific operations

**Progressive Disclosure:**
- Simple for common cases: `/test unit`
- Powerful when needed: `/test backend --coverage --slow`
- Built-in help: Run any command with invalid input to see usage

**Unified Commands:**
- Related operations grouped under single commands
- Consistent patterns across all commands
- Shortcuts and aliases for common operations

### Example Workflows with New Commands

#### Quick Development Start
```bash
/run dev              # Start full dev environment
/db migrate           # Apply database migrations
/graphql update       # Update GraphQL schema
/test                 # Run backend tests
```

#### Pre-commit Workflow
```bash
/check                # Run all quality checks
/check fix            # Auto-fix issues
/test unit            # Run unit tests
/test frontend        # Run frontend tests
```

#### Database Development
```bash
/db                   # Open database console
/db migrate           # Run migrations
/db seed users        # Seed test data
/db backup            # Create backup
```

#### Testing Workflow
```bash
/test                 # Quick backend test
/test all             # Full test suite
/test coverage        # With coverage report
/test smart           # Only affected tests
```

## Command Improvements (v2.0)

The Claude commands have been significantly improved with unified commands and smart defaults:

### What's New

1. **Unified Commands**: Related operations are now grouped under single commands
   - `/test` replaces 7 separate test commands
   - `/db` combines all database operations
   - `/check` unifies code quality checks
   - `/graphql` consolidates GraphQL operations

2. **Smart Environment Detection**: Docker commands automatically detect environments
   - Use `dev` or `test` keywords instead of `-f docker-compose.dev.yml`
   - Example: `/up dev` instead of `/up -f docker-compose.dev.yml`

3. **Better Defaults**: Commands do the right thing without arguments
   - `/test` runs backend tests
   - `/db` opens database console
   - `/graphql` generates types

4. **Built-in Help**: Every command shows usage when given invalid input

### Migration from Old Commands

| Old Command | New Command |
|-------------|-------------|
| `/test-backend` | `/test` or `/test backend` |
| `/test-frontend` | `/test frontend` |
| `/test-all` | `/test all` |
| `/migrate` | `/db migrate` |
| `/reset-db` | `/db reset` |
| `/lint` | `/check lint` |
| `/graphql-update` | `/graphql update` |

## Observed Development Patterns

Based on extensive analysis of the A2A Platform codebase, these key patterns have been identified:

### 1. Docker-First Development
- **Every operation runs through Docker Compose** - Local installations are discouraged
- **Container orchestration** - All services (backend, frontend, postgres, redis) run in containers
- **Consistent environments** - Development matches production closely

### 2. Script-Heavy Automation
- **40+ shell scripts** in the `/scripts` directory handle common tasks
- **Wrapper scripts** provide consistent interfaces to complex operations
- **Error handling** built into scripts with proper exit codes

### 3. Multi-Layer Testing Strategy
- **Unit tests** - Fast, isolated tests with mocked dependencies
- **Integration tests** - Test component interactions with real database
- **E2E tests** - Full user journey validation
- **Performance tests** - Benchmark critical operations

### 4. GraphQL-Centric Architecture
- **Schema-first development** - Define GraphQL schema before implementation
- **Code generation** - TypeScript types generated from GraphQL schema
- **Subscription support** - Real-time updates via WebSocket

### 5. Security-Conscious Practices
- **Multiple security checks** - Pre-commit hooks, security reviews
- **HTTPS enforcement** - SSL certificates for local development
- **Token management** - CLI tokens, JWT handling, Clerk integration

### 6. Database-Heavy Operations
- **Alembic migrations** - Timestamp-based, reversible migrations
- **Performance indexes** - Strategic indexing for query optimization
- **Connection pooling** - Efficient database resource usage

### Common Development Workflows

**Feature Development Flow:**
1. Create database models
2. Generate and run migrations
3. Define GraphQL schema
4. Generate TypeScript types
5. Implement resolvers/mutations
6. Create React components
7. Write comprehensive tests
8. Run pre-commit checks

**Debugging Flow:**
1. Check container logs
2. Access container shell
3. Run diagnostic scripts
4. Test in isolation
5. Review error traces

## Best Practices

### Docker Configurations

1. **Tailwind Configuration**
   - Always use `tailwind.config.ts` (not `.js`) in Docker builds
   - Ensure Dockerfiles copy the TypeScript version of the config
   - Example in Dockerfile:
     ```dockerfile
     # Copy tailwind config (note the .ts extension)
     COPY tailwind.config.ts ./
     ```
   - Both `apps/web/Dockerfile` and `apps/web/Dockerfile.dev` must reference the correct file

### When Working with Claude

1. **Be Specific with Requests**
   - Good: "Create a FastAPI route for managing user profiles with GET, POST, and PUT endpoints"
   - Less Effective: "Create some API endpoints"

2. **Reference Files by Name**
   - Good: "Update the `UserProfile` model in `/apps/backend/models/user.py`"
   - Less Effective: "Update the user model"

3. **Use Custom Commands**
   - Good: "Let's run `/precommit` to check our changes"
   - Less Effective: "Check if the code is valid"

4. **Use Templates for Consistency**
   - Good: "Use the React component template to create a notification bell"
   - Less Effective: "Create a React component for notifications"

### Code Quality and Standards

1. **Follow A2A Platform Standards**
   - Python: Black formatter (88 chars), isort, mypy (strict), ruff/flake8
   - TypeScript: Double quotes, ESLint, Prettier
   - Package Manager: Bun exclusively (never npm or yarn)
   - Database: SQLAlchemy ORM/Core (never string interpolation)
   - GraphQL: camelCase mutations, Pydantic for structured data

2. **Error Handling**
   - Always include proper error handling in generated code
   - Use structured error responses
   - Include appropriate logging

3. **Documentation**
   - Document all functions, methods, and components
   - Include usage examples for complex components
   - Add type hints in Python and TypeScript

## Troubleshooting

### Common Issues and Solutions

#### Database Connection Issues
**Symptoms:** Connection refused, timeout errors
**Solution:**
```bash
# Check database status
docker compose ps postgres

# Restart database
docker compose restart postgres && sleep 5

# Verify connection
docker compose exec backend python check_db.py
```

#### GraphQL Syntax Errors
**Symptoms:** Generated files have syntax errors, multiline string issues
**Solution:**
```bash
# Regenerate with test schema
cd apps/web && bun run codegen:test

# Check generated files
ls -la src/generated/
```

#### Permission Errors
**Symptoms:** EACCES errors, can't write to directories
**Solution:**
```bash
# Fix frontend permissions
./scripts/fix-web-permissions.sh

# Fix SSL certificate permissions
./scripts/generate-ssl-certs.sh
```

#### Container Not Starting
**Symptoms:** Container exits immediately, restart loop
**Solution:**
```bash
# Check logs
docker compose logs [service] --tail=50

# Shell into container for debugging
docker compose run --rm [service] bash

# Clean restart
docker compose down && docker compose up -d
```

#### Test Failures
**Symptoms:** Tests pass locally but fail in CI
**Solution:**
```bash
# Run tests in Docker (matches CI)
./scripts/run-docker-tests.sh

# Check for timing issues
./scripts/run-backend-tests.sh --slow

# Verify test database
docker compose exec postgres psql -U a2a_user -d a2a_test_db
```

## MCP Server Integration

### Think-Tank MCP Server

The A2A Platform includes integration with the think-tank MCP server, which provides advanced knowledge management and research capabilities. This integration enables:

- **Knowledge Graph Management**: Store and retrieve project information, decisions, and patterns
- **Task Planning**: Break down complex features into manageable, trackable tasks
- **Research Integration**: Web search and authoritative answers via Exa API
- **Structured Thinking**: Advanced reasoning with multi-step problem analysis
- **Memory Persistence**: Long-term storage of project insights and lessons learned

#### Setting Up Think-Tank

To add the think-tank MCP server to Claude Code:

```bash
claude mcp add think-tank npx mcp-think-tank@2.0.7 -e MEMORY_PATH=.think-tank/memory.jsonl -e EXA_API_KEY=your_exa_api_key_here
```

**Configuration Details:**
- **Server Name**: `think-tank` - The identifier for the MCP server
- **Command**: `npx mcp-think-tank@2.0.7` - Runs the think-tank package via npx
- **Environment Variables**:
  - `MEMORY_PATH=.think-tank/memory.jsonl` - Local storage for the knowledge graph
  - `EXA_API_KEY=your_exa_api_key_here` - API key for web search functionality

#### Verification Commands

After setup, verify the configuration:

```bash
# Check server configuration
claude mcp get think-tank

# List all MCP servers
claude mcp list
```

#### Think-Tank Commands

The following custom commands leverage think-tank capabilities:

| Command | Description | Usage |
|---------|-------------|-------|
| `/research` | Get authoritative answers with citations | `/research GraphQL best practices` |
| `/web-search` | Search for development resources | `/web-search FastAPI testing patterns` |
| `/deep-think` | Advanced reasoning with research integration | `/deep-think performance optimization strategy` |
| `/track-decision` | Record architectural decisions | `/track-decision database migration approach` |
| `/feature-plan` | Create structured implementation plans | `/feature-plan notification system` |
| `/knowledge-query` | Search stored project knowledge | `/knowledge-query authentication patterns` |
| `/map-relationships` | Visualize component dependencies | `/map-relationships GraphQL resolvers` |
| `/knowledge-cleanup` | Maintain the knowledge base | `/knowledge-cleanup outdated decisions` |
| `/component-deep-dive` | Comprehensive component analysis | `/component-deep-dive user authentication` |
| `/sprint-manage` | Manage task progression | `/sprint-manage current sprint tasks` |

#### Knowledge Graph Storage

Think-tank stores its knowledge graph in `.think-tank/memory.jsonl` within your project directory. This file contains:

- **Entities**: Components, decisions, patterns, and concepts
- **Relations**: How components interact and depend on each other
- **Observations**: Facts, insights, and lessons learned over time
- **Tasks**: Planned work items with priorities and dependencies

This persistent storage ensures that project knowledge accumulates over time and remains available across different Claude sessions.

## Extending the Integration

### Adding New Templates

To add a new template:

1. Create a file in `.claude/templates/` with a descriptive name
2. Structure it as a complete example with placeholders
3. Include comments and docstrings
4. Add JSDoc or Python docstrings for clear usage instructions

Example:
```python
# .claude/templates/sqlalchemy_model.py
from sqlalchemy import Column, Integer, String, ForeignKey, DateTime
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from apps.backend.db.base import Base

class ModelName(Base):
    """
    Description of what this model represents

    Replace ModelName with the actual model name
    Add/remove columns as needed
    """
    __tablename__ = "table_name"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    # Example relationship
    # foreign_id = Column(Integer, ForeignKey("foreign_table.id"))
    # relation = relationship("ForeignModel", back_populates="modelname")
```

### Adding New Workflows

To add a new workflow:

1. Create a markdown file in `.claude/workflows/`
2. Use the YAML frontmatter to add a description
3. Include clear, step-by-step instructions
4. Add examples of common use cases

Example:
```markdown
---
description: Add a new GraphQL resolver
---

# Adding GraphQL Resolver Workflow

This workflow helps you implement a new GraphQL resolver.

## Steps

1. Define the GraphQL schema type in `apps/backend/graphql/schema.graphql`
2. Run codegen to generate the TypeScript types
   ```bash
   cd apps/web && bunx codegen
   ```
3. Implement the resolver in `apps/backend/graphql/resolvers/`
4. Add tests in `apps/backend/tests/graphql/`
5. Run tests to verify
   ```bash
   ./scripts/run-backend-tests.sh tests/graphql/
   ```
```

### Contributing Improvements

The Claude integration is designed to evolve with the project. To contribute improvements:

1. Update the relevant files in `.claude/` directory
2. Test your changes with Claude
3. Document your changes in this file
4. Share feedback on what works well or needs improvement

## Conclusion

The Claude Code integration for the A2A Platform provides a structured approach to AI-assisted development, ensuring consistent, high-quality code that follows project standards. By leveraging the templates, workflows, and best practices outlined in this document, you can maximize the benefits of using Claude for your development tasks.

For questions or further assistance, contact the A2A Platform development team.
