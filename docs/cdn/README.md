# CDN Documentation

This directory contains documentation for the CDN (Content Delivery Network) implementation using Cloudflare.

## Files

- `cache-configuration.md` - Comprehensive guide to configurable CDN caching system
- `../terraform/modules/cloudflare-cdn/README.md` - Technical module reference
- `../terraform/modules/cloudflare-cdn/IMPORT_GUIDE.md` - Guide for importing existing resources

## Quick Start

### View Current Cache Configuration
```bash
# Show configuration from terraform.tfvars
./scripts/manage-cache.sh show staging
./scripts/manage-cache.sh show production

# Show live Terraform outputs (requires terraform init)
./scripts/manage-cache.sh outputs staging
```

### Override Cache Settings via Environment Variables
```bash
# Enable API caching in production
export CACHE_API_ENABLED=true
export CACHE_API_TTL=300

# Enable development mode (bypass all caching)
export CACHE_DEVELOPMENT_MODE=true
```

### Cache Management
- **Frontend assets**: Automatically cached with long TTLs
- **APIs**: Configurable caching (disabled by default in production)
- **GraphQL**: Separate caching configuration
- **Environment variables**: Override any cache setting dynamically

## Architecture Overview

The CDN implementation provides:

1. **Global Content Delivery**: Cloudflare's global edge network
2. **Configurable Caching**: Environment-specific cache policies
3. **Security Features**: DDoS protection, SSL/TLS termination
4. **Performance Optimization**: Edge caching, compression, minification
5. **Dynamic Configuration**: Runtime cache setting overrides
6. **Resource Import Support**: Handle existing Cloudflare resources gracefully

## Environment-Specific Behavior

### Staging
- API caching **ENABLED** for testing
- Shorter TTLs for rapid iteration
- More aggressive caching policies

### Production  
- API caching **DISABLED** by default for safety
- Conservative caching policies
- Can be enabled via environment variables

## Cache Types

| Content Type | Default Behavior | Configurable |
|-------------|------------------|--------------|
| Static Assets (`/assets/*`) | Long-term cache (1 year) | ✅ TTL configurable |
| HTML Files (`*.html`) | Short-term cache (5-30 min) | ✅ TTL configurable |
| API Endpoints (`/api/*`) | Bypass cache (configurable) | ✅ Enable/disable + TTL |
| GraphQL (`/api/graphql*`) | Bypass cache (configurable) | ✅ Enable/disable + TTL |

## Resource Management

### New Deployments
For new environments without existing Cloudflare resources:
```bash
# Default behavior - creates all resources
terraform apply
```

### Existing Resources
For environments with existing DNS records or page rules:
```bash
# Quick setup for imports
./terraform/scripts/setup_imports.sh

# Or use automated import script
./terraform/scripts/import_cloudflare_resources.sh
```

See the [Import Guide](../terraform/modules/cloudflare-cdn/IMPORT_GUIDE.md) for detailed instructions.

## Getting Started

1. **Review current configuration**:
   ```bash
   ./scripts/manage-cache.sh show production
   ```

2. **Test in staging first**:
   ```bash
   # Staging already has API caching enabled
   ./scripts/manage-cache.sh show staging
   ```

3. **Handle existing resources** (if any):
   ```bash
   # Quick setup for imports
   ./terraform/scripts/setup_imports.sh
   ```

4. **Enable production API caching** (when ready):
   ```bash
   # Set in GitHub Actions environment variables
   CACHE_API_ENABLED=true
   CACHE_API_TTL=300  # 5 minutes
   ```

5. **Monitor and adjust**:
   - Use Cloudflare analytics to monitor cache hit rates
   - Adjust TTL values based on content update frequency
   - Use development mode for immediate cache bypass during debugging

## Module Reference

For technical details about the Terraform module:
- **Module Documentation**: [terraform/modules/cloudflare-cdn/README.md](../terraform/modules/cloudflare-cdn/README.md)
- **Import Guide**: [terraform/modules/cloudflare-cdn/IMPORT_GUIDE.md](../terraform/modules/cloudflare-cdn/IMPORT_GUIDE.md)
- **Example Configurations**: [terraform/modules/cloudflare-cdn/examples/](../terraform/modules/cloudflare-cdn/examples/)

For detailed configuration options, see `cache-configuration.md`.
