# Cloudflare R2 Public Access Configuration

This document explains how to configure public access for Cloudflare R2 buckets used in the A2A Platform frontend deployment.

## Overview

Cloudflare R2 buckets are **private by default**. To serve static assets through a CDN (like `www-staging.vedavivi.app`), the buckets must have **public access enabled**. This configuration is currently done manually through the Cloudflare dashboard, as the API does not support programmatic public access configuration.

## Current Configuration

### Staging Environment
- **Bucket**: `a2a-platform-web-assets-staging`
- **Custom Domain**: `www-staging.vedavivi.app`
- **Public Access**: ✅ Enabled
- **Purpose**: Serves frontend assets for staging environment

### Production Environment
- **Bucket**: `a2a-platform-web-assets-production`
- **Custom Domain**: `www.vedavivi.app` (when configured)
- **Public Access**: ✅ Enabled
- **Purpose**: Serves frontend assets for production environment

## Manual Setup Process

### Step 1: Access Cloudflare Dashboard
1. Log in to [Cloudflare Dashboard](https://dash.cloudflare.com)
2. Navigate to **R2 Object Storage**
3. Select the target bucket (e.g., `a2a-platform-web-assets-staging`)

### Step 2: Enable Public Access
1. Click on the bucket name to open bucket details
2. Go to the **Settings** tab
3. Find the **Public Access** section
4. Click **Allow Access** to enable public read access
5. Confirm the action when prompted

### Step 3: Configure Custom Domain (if needed)
1. In the bucket settings, find **Custom Domains**
2. Click **Connect Domain**
3. Enter the custom domain (e.g., `www-staging.vedavivi.app`)
4. Follow the DNS configuration prompts

### Step 4: Verify Configuration
Test public access by accessing a file directly:
```bash
# Test staging bucket
curl -I https://www-staging.vedavivi.app/favicon.ico

# Should return 200 OK with proper headers
```

## Important Notes

### API Limitations
- **No API Support**: Cloudflare R2 API does not currently support enabling public access programmatically
- **Manual Only**: Public access must be configured through the dashboard
- **One-Time Setup**: Once enabled, public access persists until manually disabled

### Security Considerations
- **Read-Only**: Public access only allows reading objects, not writing
- **Bucket-Wide**: Public access applies to all objects in the bucket
- **CDN Integration**: Objects are served through Cloudflare's CDN for performance and security

### DNS Configuration
When using custom domains:
- DNS records are managed by Terraform (`terraform/modules/cloudflare-r2/`)
- CNAME record points to the bucket name
- Cloudflare proxy must be enabled for CDN functionality

## Troubleshooting

### Common Issues

**1. 404 Errors on Assets**
- **Symptom**: `https://www-staging.vedavivi.app/assets/file.js` returns 404
- **Cause**: Public access not enabled or assets not uploaded
- **Solution**: Verify public access in dashboard, check deployment logs

**2. MIME Type Errors**
- **Symptom**: "Expected JavaScript but got text/html"
- **Cause**: 404 error page returned instead of asset file
- **Solution**: Ensure assets are uploaded to correct path in bucket

**3. CORS Issues**
- **Symptom**: Browser blocks asset loading due to CORS
- **Cause**: CORS not configured for bucket
- **Solution**: Configure CORS in bucket settings (if needed)

### Verification Commands

```bash
# Check if bucket is publicly accessible
curl -I https://www-staging.vedavivi.app/

# List bucket contents (if listing is enabled)
aws s3 ls s3://a2a-platform-web-assets-staging/ \
  --endpoint-url=https://ACCOUNT_ID.r2.cloudflarestorage.com

# Test specific asset
curl -I https://www-staging.vedavivi.app/assets/index.js
```

## Deployment Integration

### GitHub Actions Workflow
The deployment workflow (`deploy-frontend-cdn.yml`) assumes public access is already configured:

1. **Builds** frontend assets with Vite
2. **Uploads** assets to R2 bucket using AWS CLI
3. **Serves** assets through custom domain with public access

### No Automation Required
- Public access setup is a **one-time manual process**
- Deployment workflow focuses on asset upload only
- No API calls needed for public access management

## Related Documentation

- [CDN Migration Guide](cdn-migration-guide.md) - Overall CDN setup process
- [Terraform R2 Module](../../terraform/modules/cloudflare-r2/) - Infrastructure configuration
- [Frontend Deployment Workflow](../deployment/github-actions-workflow-analysis.md) - CI/CD process

## Maintenance

### Regular Tasks
- **Monitor**: Check bucket usage and costs in Cloudflare dashboard
- **Cleanup**: Remove old assets if needed (handled by deployment `--delete` flag)
- **Security**: Review public access settings periodically

### Emergency Procedures
If public access needs to be disabled:
1. Go to bucket settings in Cloudflare dashboard
2. Click **Disable Access** in Public Access section
3. Update DNS or deployment process as needed
4. Re-enable when ready

---

**Last Updated**: December 2024  
**Maintained By**: Platform Team
