# Frontend CDN Migration - FAQ

## General Questions

### What is the CDN migration?
We're moving the frontend deployment from Docker containers running on Cloud Run to a CDN + Cloud Storage architecture using Cloudflare CDN and Google Cloud Storage (GCS).

### Why are we making this change?
The migration provides several benefits:
- 60-75% faster global load times (reduced TTFB from 200-800ms to 50-150ms)
- 70% reduction in monthly infrastructure costs (from $59-100 to $8-20)
- Improved reliability with global distribution (99.99% availability)
- Simplified deployment process with reduced operational overhead

### When will this migration happen?
The migration is planned to roll out in phases:
1. Staging environment: [Date]
2. Production environment: [Date]
Users will experience no downtime during the migration.

## Technical Questions

### How does the new architecture work?
The architecture consists of:
1. Google Cloud Storage buckets store static frontend assets (HTML, CSS, JS)
2. Cloudflare CDN distributes these assets globally from edge locations
3. Assets use proper caching headers (1 year for hashed assets, 5 min for HTML)
4. WebSocket connections for real-time features are proxied through Cloudflare Spectrum

### What happens to API requests?
API requests are still sent to the backend services running on Cloud Run. The CDN is configured to bypass caching for API endpoints (`/api/*` and `/graphql`).

### How are environment-specific configurations handled?
Environment variables are injected at build time using Vite's environment variable handling. Each environment (development, staging, production) has its own configuration.

### Will this affect how we develop locally?
No, local development remains unchanged. You'll still use `docker compose -f docker-compose.dev.yml up` for local development.

## Deployment Questions

### How is the frontend deployed now?
The frontend is deployed automatically via GitHub Actions:
1. Code is committed to the main branch
2. GitHub Actions builds the frontend with the appropriate environment variables
3. Assets are uploaded to Google Cloud Storage
4. Cloudflare cache is invalidated if necessary

### How do I test the CDN deployment?
You can use the verification script:
```bash
./scripts/verify-cdn-performance.sh -e staging
```
This will check performance metrics, caching headers, and content integrity.

### What if something goes wrong?
We have a rollback procedure ready:
```bash
./scripts/rollback-cdn-migration.sh -e staging
```
This script will update DNS records to point back to the Cloud Run instances, providing an immediate rollback path with zero downtime.

## Performance Questions

### How much faster will the site be?
- Time to First Byte (TTFB): Reduced from 200-800ms to 50-150ms (60-75% improvement)
- Total load time: Reduced by approximately 50%
- Cold start elimination: Previously 100-1000ms, now 0ms

### How is this measured?
We use multiple tools to measure performance:
1. Automated performance tests in CI/CD pipeline
2. Real-user monitoring (RUM) via analytics
3. Synthetic monitoring from multiple global locations

### Will all users see the same performance improvements?
Performance improvements will be more dramatic for users further from our original Cloud Run regions. Users closer to edge locations will see the greatest improvements.

## Cost Questions

### How much money will this save?
The migration reduces monthly costs by approximately 70%:
- Current monthly costs: $59-100
- New monthly costs: $8-20
- Annual savings: $618-960

### What about traffic spikes?
The CDN architecture handles traffic spikes much more efficiently:
- No need to scale containers up and down
- Edge caching reduces origin load
- Cloudflare's free tier includes DDoS protection

### Is there a risk of unexpected costs?
We've implemented several safeguards:
- Budget alerts in Google Cloud
- Traffic monitoring in Cloudflare
- Regular cost reviews

For a detailed cost analysis, see [CDN Migration Cost Analysis](cdn-migration-cost-analysis.md).

## Security Questions

### Is this architecture more or less secure?
The CDN architecture improves security in several ways:
- Reduced attack surface (immutable deployments)
- DDoS protection via Cloudflare
- Enhanced TLS security with Cloudflare's SSL options
- WebSocket traffic protection via Cloudflare Spectrum
- No public access to GCS buckets (restricted to CDN and deployment service accounts only)
- Enhanced IAM controls with least privilege principle

### How is the GCS bucket secured?
The GCS buckets storing frontend assets are secured with multiple layers of protection:
- `public_access_prevention = "enforced"` setting to explicitly prevent public access
- IAM bindings that only allow access from:
  - Cloudflare CDN service account (for content serving)
  - Deployment service account (for CI/CD pipelines)
- No public internet access to the bucket (CDN-only origin)
- Uniform bucket-level access enabled
- Optional KMS encryption for sensitive environments

### How can I verify the bucket security?
You can use our security verification script:
```bash
./scripts/check-bucket-security.sh staging
```
This script checks for:
- Public access prevention settings
- IAM policies to ensure no public access
- Service account configuration
- Anonymous access attempts

### How are secrets handled?
Secrets are managed through:
- GitHub Action secrets for deployment credentials
- Environment variables injected at build time
- No secrets are stored in the static assets

### Has this architecture been security reviewed?
Yes, the architecture has been reviewed for:
- Data privacy compliance
- Security best practices
- Infrastructure access controls
- Network security

## Support and Maintenance

### Who should I contact with questions?
For questions about the CDN migration, contact:
- [Platform Engineering Team Contact]
- [Slack Channel: #platform-engineering]

### Where can I find more information?
- [CDN Migration Guide](cdn-migration-guide.md)
- [CDN Migration Cost Analysis](cdn-migration-cost-analysis.md)
- [Frontend Deployment Architecture ADR](../adrs/ADR-004-Deployment-Overview.md)
