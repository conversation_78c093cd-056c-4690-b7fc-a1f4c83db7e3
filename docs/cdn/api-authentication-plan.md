# API ## 🏗️ **Architecture Update Notice**

**Important**: This document focuses on **API authentication** through Cloudflare CDN to Cloud Run services. The project architecture now separates concerns:

### **Current Architecture (Post-GCS Proxy Migration)**:
- **API Services**: Direct Cloudflare → Cloud Run (this document)
- **Static Assets**: Cloudflare → GCS Proxy Service → Private GCS Buckets
- **Authentication**: Separate patterns for API vs. static content

### **Static Asset Serving (Completed)**:
- ✅ **GCS Proxy Service**: `services/gcs-proxy/` (FastAPI-based Cloud Run service)
- ✅ **Service Account Authentication**: Direct GCS access with proper IAM
- ✅ **Private Buckets**: No public access required, bypassing org policies
- ✅ **Infrastructure**: Deployed via Terraform with IAM binding resolution

### **API Authentication (This Document)**:
- **Scope**: Authentication for GraphQL API and backend services
- **Pattern**: Cloudflare CDN → Cloud Run API services
- **Current State**: Staging uses `allow-unauthenticated`, Production requires auth

For static asset implementation details, refer to:
- [GCS Proxy Implementation Plan](./gcs-proxy-implementation-plan.md)
- [GCS Proxy Integration Steps](./gcs-proxy-integration-steps.md)ion Plan for Cloudflare CDN

## 🎯 **Overview**

This document outlines the authentication strategy for securing API access through Cloudflare CDN to Google Cloud Run services. This plan has been updated to reflect the new GCS proxy architecture that replaces Cloudflare Workers for static asset serving.

## � **Architecture Update Notice**

**Important**: This document was originally written for a Cloudflare Worker-based architecture. The project has since migrated to a **GCS Proxy service** (Cloud Run) for static asset serving. While the API authentication patterns remain relevant, the static asset serving now uses:

- **GCS Proxy Service**: `services/gcs-proxy/` (FastAPI-based Cloud Run service)
- **Service Account Authentication**: Direct GCS access with proper IAM
- **No Cloudflare Workers**: Simplified architecture with Cloud Run proxy

For static asset serving, refer to:
- [GCS Proxy Implementation Plan](./gcs-proxy-implementation-plan.md)
- [GCS Proxy Integration Steps](./gcs-proxy-integration-steps.md)

## �🔍 **Current Issue Analysis**

### **Problem Statement**
- **URL**: `https://api-staging.vedavivi.app/graphql`
- **Error**: 404 Not Found (Google error page)
- **Root Cause**: Cloud Run service requires authentication (`allow-unauthenticated: false`)
- **DNS Status**: ✅ Correctly configured, pointing to `api-staging-z3unjrt3jq-uc.a.run.app`

### **Service Status Verification**
```bash
# Direct Cloud Run access
curl https://api-staging-z3unjrt3jq-uc.a.run.app/
# Returns: 403 Forbidden (authentication required) ✅

curl https://api-staging-z3unjrt3jq-uc.a.run.app/graphql
# Returns: 404 Not Found (endpoint exists but requires auth)

# Via Cloudflare CDN
curl https://api-staging.vedavivi.app/graphql
# Returns: 404 Google error page (no authentication forwarded)
```

## 🏗️ **Architecture Overview**

### **Separated Concerns Architecture**
```
📊 API Requests:
Client Request → Cloudflare CDN → Cloud Run API Service
     ↓              ↓                    ↓
[Auth Headers] → [Forward Auth] → [Validate & Process]

🎨 Static Assets:
Client Request → Cloudflare CDN → GCS Proxy Service → Private GCS Bucket
     ↓              ↓                    ↓                    ↓
[Cache Check] → [Proxy Request] → [Service Account] → [Secure Access]
```

### **Implementation Status**
- ✅ **Static Assets**: GCS Proxy architecture deployed and working
- ✅ **API Staging**: Unauthenticated access enabled for development
- 🔄 **API Production**: Authentication implementation planned (Phase 2)

## 🔧 **Implementation Plan**

### **Phase 1: Immediate Fix (Staging)**
**Status**: ✅ **COMPLETED**

**Approach**: Enable unauthenticated access for staging environment
```yaml
# .github/workflows/deploy-api.yml (staging only)
allow-unauthenticated: true  # Enable for staging to allow Cloudflare access
```

**Rationale**:
- ✅ **Quick resolution** for development/testing
- ✅ **Staging environment** appropriate for open access
- ✅ **Production remains secure** with authentication required
- ✅ **Allows immediate GraphQL endpoint testing**

**Result**: ✅ **API endpoint now accessible** - `https://api-staging.vedavivi.app/graphql` returns proper responses

### **Phase 2: Production Authentication (Future)**
**Status**: 🚧 **PLANNED**

**Approach**: Implement proper Cloudflare-to-Cloud Run authentication

#### **Option A: Service Account Authentication**
```yaml
# Cloud Run IAM binding
gcloud run services add-iam-policy-binding api-production \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/run.invoker"
```

#### **Option B: GCS Proxy Pattern Authentication**
```yaml
# Similar to GCS proxy service - Cloud Run service with service account
# This follows the established pattern from static asset authentication
gcloud run deploy api-proxy \
  --image="gcr.io/project/api-proxy:latest" \
  --service-account="<EMAIL>" \
  --allow-unauthenticated
```

**Benefits**:
- ✅ **Proven Pattern**: Follows successful GCS proxy implementation
- ✅ **Service Account Security**: Authenticated GCP resource access
- ✅ **Separation of Concerns**: API auth independent of static assets
- ✅ **Scalable**: Can handle high-throughput API requests

#### **Option C: IP-based Authentication**
```yaml
# Cloud Run ingress configuration
ingress: internal-and-cloud-load-balancing
# Configure Cloudflare IP ranges in Cloud Armor
```

## 🔗 **Integration with GCS Proxy Architecture**

### **Complementary Authentication Patterns**
The API authentication strategy now operates alongside the GCS proxy architecture:

#### **Static Asset Flow** (✅ Implemented):
```
Client → Cloudflare CDN → GCS Proxy Service → Private GCS Bucket
                ↓              ↓                    ↓
        [Cache Headers] → [Service Account] → [Secure Access]
```
- **Service**: `services/gcs-proxy/` (FastAPI-based Cloud Run)
- **Authentication**: Service account with `storage.objectViewer` role
- **Security**: Private buckets, no public access required
- **Performance**: Cloudflare edge caching + Cloud Run memory caching

#### **API Service Flow** (🔄 In Progress):
```
Client → Cloudflare CDN → Cloud Run API Service
                ↓              ↓
        [Auth Headers] → [JWT Validation / Service Account]
```
- **Staging**: Unauthenticated access for development
- **Production**: Planned authentication using similar service account patterns

### **Shared Infrastructure Benefits**
- ✅ **Consistent IAM patterns**: Both use service account authentication
- ✅ **Resolved deployment conflicts**: IAM binding conversion benefits both services
- ✅ **Standardized provider versions**: Unified Terraform configuration
- ✅ **Automated deployment**: Both services deployed via GitHub Actions

### **Future Convergence Opportunities**
- **Unified monitoring**: Combined dashboards for API and static asset performance
- **Shared caching strategies**: Coordinated cache invalidation across services
- **Common security patterns**: Apply GCS proxy authentication learnings to API auth

## 📊 **Environment Configuration**

### **Staging Environment**
```yaml
# Configuration
allow-unauthenticated: true
security_level: low
purpose: development/testing

# Access Pattern
Client → Cloudflare → Cloud Run (direct access)
```

### **Production Environment**
```yaml
# Configuration
allow-unauthenticated: false
security_level: high
purpose: production

# Access Pattern (Planned)
Client → Cloudflare → Authentication Layer → Cloud Run
```

## 🚀 **Deployment Steps**

### **Phase 1 Status (Staging Fix)**
1. ✅ **Updated deployment workflow** to enable unauthenticated access
2. ✅ **Deployed via GitHub Actions** successfully
3. ✅ **Verified GraphQL endpoint** accessibility

### **Phase 1 Results - COMPLETED**
```bash
# Before Fix
curl https://api-staging.vedavivi.app/graphql
# Returned: 404 Google error page

# After Fix - SUCCESS
curl https://api-staging.vedavivi.app/graphql
# Returns: 405 Method Not Allowed (correct - GraphQL requires POST)

curl -X POST https://api-staging.vedavivi.app/graphql \
  -H "Content-Type: application/json" \
  -d '{"query": "{ __schema { types { name } } }"}'
# Returns: 200 OK with GraphQL schema
```

### **Infrastructure Deployment Issues Resolved**
**Problem**: Terraform IAM conflicts causing deployment failures
```
Error 412: Too many conflicts. Latest error: Error setting IAM policy for storage bucket
"One or more users named in the policy do not belong to a permitted customer"
```

**Solution Implemented**: ✅ **COMPLETED**
1. ✅ **IAM Binding Conversion**: 
   - Converted `google_storage_bucket_iam_member` to `google_storage_bucket_iam_binding`
   - Added proper dependency ordering: `deployer_access` depends on `object_viewer`
   - Added `create_before_destroy` lifecycle rules for safe updates

2. ✅ **Provider Version Standardization**:
   - Updated Google provider from `~> 5.0` to `~> 6.35.0` across all configurations
   - Updated Terraform version requirements to `>= 1.12.0`

3. ✅ **GCS Proxy Integration**:
   - Added GCS proxy service to infrastructure pipeline
   - Integrated deployment automation in GitHub Actions workflow
   - Configured service account authentication for private bucket access

4. ✅ **Deployment Success**: 
   - All Terraform validations pass in root, staging, and production environments
   - Infrastructure deployments complete without IAM errors
   - GCS proxy service deployed and operational

### **IAM Binding Resolution (Completed)**

#### **Root Cause Resolution**: ✅ **IMPLEMENTED**
The persistent IAM conflicts were resolved through systematic infrastructure improvements:

**1. IAM Resource Type Conversion**:
```hcl
# Before: Multiple concurrent iam_member resources causing conflicts
resource "google_storage_bucket_iam_member" "public_read" { ... }
resource "google_storage_bucket_iam_member" "cdn_access" { ... }

# After: Single iam_binding resources with proper ordering
resource "google_storage_bucket_iam_binding" "object_viewer" {
  bucket = local.bucket_name
  role   = "roles/storage.objectViewer"
  members = [
    "allUsers",
    "serviceAccount:${var.cdn_service_account}"
  ]
  depends_on = [google_storage_bucket.web_assets]
}
```

**2. Provider Version Standardization**:
- ✅ Eliminated version conflicts across all Terraform modules
- ✅ Standardized on Google Provider `~> 6.35.0`
- ✅ Updated Terraform requirements to `>= 1.12.0`

**3. Dependency Management**:
```hcl
resource "google_storage_bucket_iam_binding" "deployer_access" {
  # Explicit dependency prevents concurrent IAM operations
  depends_on = [google_storage_bucket_iam_binding.object_viewer]
}
```

**Result**: ✅ **All IAM conflicts resolved** - Infrastructure deployments now complete successfully

## 🔒 **Security Considerations**

### **Staging Environment**
- ✅ **Acceptable Risk**: Development environment with test data
- ✅ **Network Security**: Still protected by VPC and firewall rules
- ✅ **Application Security**: Authentication handled at application level
- ✅ **Monitoring**: Cloud Run logs and metrics available

### **Production Environment**
- 🔒 **Authentication Required**: Service account or token-based auth
- 🔒 **Network Isolation**: VPC connector and private networking
- 🔒 **Audit Logging**: All requests logged and monitored
- 🔒 **Rate Limiting**: Cloudflare and Cloud Run rate limits

## 📋 **Testing & Validation**

### **Integrated Architecture Tests**
```bash
# 1. API Endpoint Accessibility
curl -X POST https://api-staging.vedavivi.app/graphql \
  -H "Content-Type: application/json" \
  -d '{"query": "{ __schema { types { name } } }"}'

# 2. Static Asset Serving (via GCS Proxy)
curl -I https://assets-staging.vedavivi.app/favicon.ico

# 3. Health Checks for Both Services
curl https://api-staging.vedavivi.app/api/health
curl https://gcs-proxy-staging-[hash]-uc.a.run.app/health

# 4. Performance Testing - Combined Load
ab -n 100 -c 10 https://api-staging.vedavivi.app/api/health &
ab -n 100 -c 10 https://assets-staging.vedavivi.app/favicon.ico &
wait
```

### **Authentication Flow Tests**
```bash
# Current: Staging API (unauthenticated)
curl -X POST https://api-staging.vedavivi.app/graphql \
  -H "Content-Type: application/json" \
  -d '{"query": "{ __schema { types { name } } }"}'

# Future: Production API (authenticated)
curl -H "Authorization: Bearer $TOKEN" \
  -X POST https://api-production.vedavivi.app/graphql \
  -H "Content-Type: application/json" \
  -d '{"query": "{ __schema { types { name } } }"}'

# GCS Proxy Service (service account authenticated)
# Note: Authentication handled internally by Cloud Run service account
curl -I https://assets-staging.vedavivi.app/index.html
```

### **Infrastructure Validation**
```bash
# Terraform State Validation
cd terraform/environments/staging
terraform plan  # Should show "No changes"

# IAM Binding Verification
gcloud storage buckets get-iam-policy gs://a2a-platform-web-assets-staging

# Service Account Validation
gcloud iam service-<NAME_EMAIL>
```

## 🔄 **Rollback Plan**

### **If Issues Occur**
1. **Revert deployment**: Set `allow-unauthenticated: false`
2. **Alternative access**: Use direct Cloud Run URL for testing
3. **Debug authentication**: Check Cloud Run logs and IAM policies

### **Rollback Commands**
```bash
# Emergency rollback via gcloud
gcloud run services update api-staging \
  --no-allow-unauthenticated \
  --region=us-central1
```

## 📈 **Success Metrics**

### **Immediate Goals (Phase 1)**
- ✅ **GraphQL endpoint accessible** via CDN domain
- ✅ **Frontend can connect** to API successfully
- ✅ **No 403/404 errors** on API requests
- ✅ **Development workflow** unblocked

### **Long-term Goals (Phase 2)**
- 🎯 **Production authentication** implemented
- 🎯 **Zero security incidents** related to API access
- 🎯 **Performance optimization** through proper caching
- 🎯 **Monitoring and alerting** for API health

## 📚 **Related Documentation**

### **Primary References**
- [GCS Proxy Implementation Plan](./gcs-proxy-implementation-plan.md) - Static asset architecture
- [GCS Proxy Integration Steps](./gcs-proxy-integration-steps.md) - Deployment instructions
- [CDN Domain Migration Guide](./cdn-domain-migration-guide.md) - Domain configuration

### **Infrastructure & Security**
- [Cloudflare Configuration](../deployment/cloudflare-setup.md) - CDN setup
- [Cloud Run Security Best Practices](../security/cloud-run-security.md) - Service security
- [API Deployment Workflows](../deployment/api-deployment.md) - CI/CD processes
- [GCS Bucket Security](../gcs-bucket-security.md) - Bucket hardening

### **Development & Testing**
- [Testing with Docker](../testing-with-docker.md) - Local development
- [GraphQL Guidelines](../graphql-guidelines.md) - API best practices
- [Debugging Guide](../debugging.md) - Troubleshooting

## 🔗 **Useful Links**

- [Cloud Run Authentication](https://cloud.google.com/run/docs/authenticating/overview)
- [GCP Service Accounts](https://cloud.google.com/iam/docs/service-accounts)
- [Cloudflare Transform Rules](https://developers.cloudflare.com/rules/transform/)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Cloud Run Best Practices](https://cloud.google.com/run/docs/best-practices)

## 🚨 **Action Items**

### **Completed ✅**
- [x] **Deploy API changes** via GitHub Actions workflow
- [x] **Test GraphQL endpoint** accessibility after deployment
- [x] **Resolve infrastructure deployment conflicts** with IAM binding conversion
- [x] **Verify end-to-end functionality** from frontend to API
- [x] **Implement GCS proxy service** for static asset authentication
- [x] **Standardize provider versions** across all Terraform configurations
- [x] **Convert IAM members to bindings** to eliminate conflicts
- [x] **Deploy GCS proxy infrastructure** via automated pipeline

### **In Progress 🚧**
- [ ] **Update frontend configuration** to use CDN domain consistently
- [ ] **Monitor staging performance** and error rates
- [ ] **Document authentication patterns** for frontend developers
- [ ] **Optimize GCS proxy caching** for better performance

### **Short-term (Next week)**
- [ ] **Plan production authentication** implementation (Phase 2)
- [ ] **Create monitoring dashboards** for API health
- [ ] **Performance testing** of integrated API + GCS proxy architecture
- [ ] **Security audit** of current authentication patterns

### **Long-term (Next month)**
- [ ] **Implement production authentication** (Phase 2)
- [ ] **Performance optimization** and caching strategies
- [ ] **Security audit** of complete authentication flow
- [ ] **Load testing** and capacity planning for both API and static assets

## 🎯 **Decision Log**

| Date | Decision | Rationale | Impact |
|------|----------|-----------|---------|
| 2025-06-02 | Enable unauthenticated access for staging | Quick fix for development workflow | ✅ Low risk, high value - COMPLETED |
| 2025-06-02 | Convert IAM members to bindings | Root cause resolution of deployment conflicts | ✅ High impact - deployment pipeline restored |
| 2025-06-02 | Implement GCS proxy architecture | Separate static asset serving from API authentication | ✅ High impact - architectural clarity |
| 2025-06-02 | Standardize provider versions | Eliminate Terraform version conflicts | ✅ Medium impact - infrastructure stability |
| 2025-06-03 | Separate API and static asset authentication | Clear separation of concerns in documentation | ✅ Documentation clarity |
| TBD | Production authentication method | Security requirements analysis | High impact on architecture |

## 📊 **Success Metrics - ACHIEVED**

### **Immediate Goals (Phase 1) - ✅ COMPLETED**
- ✅ **GraphQL endpoint accessible** via CDN domain (`https://api-staging.vedavivi.app/graphql`)
- ✅ **Frontend can connect** to API successfully (405 → POST required, 200 OK with proper requests)
- ✅ **No 403/404 errors** on API requests
- ✅ **Development workflow** unblocked
- ✅ **Infrastructure deployment** pipeline restored

### **Infrastructure Goals - ✅ COMPLETED**
- ✅ **Terraform deployments succeed** without IAM conflicts
- ✅ **IAM binding conversion** eliminates concurrent policy conflicts
- ✅ **Provider version standardization** ensures compatibility
- ✅ **GCS proxy service deployment** automated in CI/CD pipeline
- ✅ **Separation of concerns** between API and static asset authentication
- ✅ **Zero deployment failures** related to IAM policies

### **Architecture Goals - ✅ COMPLETED**
- ✅ **GCS proxy service** deployed for static asset serving
- ✅ **Private GCS buckets** with service account authentication
- ✅ **API authentication** separated from static asset patterns
- ✅ **Scalable infrastructure** with proper dependency management

### **Long-term Goals (Phase 2) - 🎯 PLANNED**
- 🎯 **Production authentication** implemented
- 🎯 **Zero security incidents** related to API access
- 🎯 **Performance optimization** through proper caching
- 🎯 **Monitoring and alerting** for API health

---

**Last Updated**: June 3, 2025
**Status**: ✅ **Phase 1 COMPLETED** - API accessible, infrastructure stable, GCS proxy deployed
**Next Review**: Production authentication implementation planning (Phase 2)
**Owner**: Platform Team
**Stakeholders**: Frontend Team, DevOps Team, Security Team

## 📝 **Architecture Summary**

This API authentication plan now operates within a **separated concerns architecture**:

- **✅ Static Assets**: Handled by GCS Proxy service with service account authentication
- **✅ API Services**: Direct Cloudflare → Cloud Run with staging unauthenticated access
- **🔄 Future**: Production API authentication using proven GCS proxy patterns

The successful resolution of IAM conflicts and deployment of the GCS proxy architecture provides a solid foundation for implementing production API authentication using established patterns.
