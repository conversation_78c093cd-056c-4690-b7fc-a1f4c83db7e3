# API Access Quick Reference

## 🚀 **Current Status**

### **Issue**: `api-staging.vedavivi.app/graphql` returns 404
### **Solution**: Enable unauthenticated access for staging Cloud Run service
### **Status**: ✅ Code changes ready, 🚧 Deployment pending

## 🔧 **Quick Fix Commands**

### **Deploy the Fix**
```bash
# Via GitHub Actions (Recommended)
1. Go to GitHub Actions → "Deploy API to GCP Cloud Run"
2. Select "Run workflow" 
3. Choose Environment: "staging"
4. Click "Run workflow"

# Via gcloud CLI (Emergency)
gcloud run services update api-staging \
  --allow-unauthenticated \
  --region=us-central1
```

### **Test the Fix**
```bash
# Test GraphQL endpoint
curl -X POST https://api-staging.vedavivi.app/graphql \
  -H "Content-Type: application/json" \
  -d '{"query": "{ __schema { types { name } } }"}'

# Test health endpoint  
curl https://api-staging.vedavivi.app/api/health

# Test direct Cloud Run (should work after deployment)
curl https://api-staging-z3unjrt3jq-uc.a.run.app/api/health
```

## 🎯 **Expected Results**

### **Before Fix**
```bash
curl https://api-staging.vedavivi.app/graphql
# Returns: 404 Google error page
```

### **After Fix**
```bash
curl https://api-staging.vedavivi.app/graphql
# Returns: 200 OK (GraphQL schema) or 405 Method Not Allowed (need POST)
```

## 🔍 **Troubleshooting**

### **If Still Getting 404**
1. **Check deployment status**: Verify GitHub Actions completed successfully
2. **Check DNS**: `nslookup api-staging.vedavivi.app` should resolve
3. **Check Cloud Run**: Service should show "allow unauthenticated" enabled
4. **Check Cloudflare**: DNS record should point to correct Cloud Run URL

### **If Getting 403**
1. **Deployment not applied**: Re-run GitHub Actions workflow
2. **Wrong service**: Verify targeting `api-staging` not `api-production`
3. **IAM issues**: Check Cloud Run IAM policies

### **If Getting 500**
1. **Application error**: Check Cloud Run logs
2. **Database connection**: Verify database connectivity
3. **Environment variables**: Check required env vars are set

## 📋 **Verification Checklist**

- [ ] **GitHub Actions deployment** completed successfully
- [ ] **Cloud Run service** shows "allow unauthenticated: true"
- [ ] **DNS resolution** points to correct Cloud Run URL
- [ ] **GraphQL endpoint** returns valid response (not 404)
- [ ] **Frontend application** can connect to API
- [ ] **No authentication errors** in browser console

## 🔗 **Useful Links**

- **GitHub Actions**: [Deploy API Workflow](../../.github/workflows/deploy-api.yml)
- **Cloud Run Console**: [GCP Cloud Run Services](https://console.cloud.google.com/run)
- **Cloudflare DNS**: [Cloudflare Dashboard](https://dash.cloudflare.com/)
- **Full Documentation**: [API Authentication Plan](./api-authentication-plan.md)

## 🚨 **Emergency Contacts**

- **Platform Team**: For deployment and infrastructure issues
- **Frontend Team**: For API integration and testing
- **DevOps Team**: For CI/CD pipeline issues

---

**Quick Reference Updated**: June 2, 2025  
**For detailed information**: See [API Authentication Plan](./api-authentication-plan.md)
