# Frontend CDN Migration Guide

## Overview

This document provides an overview of the migration from Docker containers on Cloud Run to a CDN + Cloud Storage architecture for the A2A Platform frontend deployment. This migration improves global performance by 60-75%, reduces costs by 70%, and simplifies operational overhead.

## Architecture

The new architecture consists of:

1. **Google Cloud Storage (GCS)** - Hosts the static assets (HTML, CSS, JS, images)
2. **Cloudflare CDN** - Serves assets from global edge locations
3. **WebSocket Proxy** - Handles real-time communication via Cloudflare Spectrum
4. **GitHub Actions CI/CD** - Automates deployment process

## Prerequisites

- Google Cloud Platform account with appropriate permissions
- Cloudflare account with API access
- Terraform installed locally (version >= 1.0.0)
- GitHub Actions set up for the repository

## Environment Variables

The following environment variables are required for the deployment process:

### GitHub Secrets

- `GCP_PROJECT_ID` - Google Cloud project ID
- `GCP_SA_KEY` - Base64-encoded Google Cloud service account key JSON
- `GCP_SERVICE_ACCOUNT` - **Required**: Service account email for CDN access to GCS (e.g., `<EMAIL>`)
- `CLOUDFLARE_API_TOKEN` - Cloudflare API token with Zone:Edit permissions
- `GCS_BUCKET` - GCS bucket name (configured per environment in GitHub Environments)
- `CLOUDFLARE_ZONE_ID` - Cloudflare Zone ID (configured per environment in GitHub Environments)

### Frontend Environment Variables

- `VITE_API_URL` - Backend API URL
- `VITE_WS_URL` - WebSocket URL for real-time communication
- `VITE_ASSETS_URL` - URL for static assets (used for CDN path construction)

## Deployment Process

### Terraform Infrastructure Deployment

1. Initialize the Terraform backend:
   ```bash
   ./scripts/init-terraform-backend.sh <gcp-project-id>
   ```

2. Navigate to the appropriate environment directory:
   ```bash
   cd terraform/environments/staging  # or production
   ```

3. Initialize Terraform:
   ```bash
   terraform init
   ```

4. Plan the deployment:
   ```bash
   terraform plan -var="cloud_run_websocket_url=<websocket-url>"
   ```

5. Apply the changes:
   ```bash
   terraform apply -var="cloud_run_websocket_url=<websocket-url>"
   ```

### GitHub Actions CI/CD

The CI/CD pipeline is automated through GitHub Actions:

1. Infrastructure changes are deployed through the `terraform-cdn.yml` workflow
2. Frontend code is built and deployed through the `frontend-cdn-deploy.yml` workflow

The workflows can be triggered:
- Automatically on push to main branch
- Manually through workflow dispatch

## Rollback Procedure

In case of issues with the CDN deployment:

1. Revert DNS to point back to Cloud Run instances
2. Rollback frontend code changes if necessary
3. Run the previous Cloud Run deployment workflow

## Performance Monitoring

Monitor the following metrics to validate the migration success:

- Page load time (target: 50-150ms TTFB)
- Cache hit ratio (target: >95%)
- Monthly cost (target: $8-20)
- Build time (target: 1-2 minutes)
- Availability (target: 99.99%)

## Success Criteria Validation

The migration will be considered successful when:

- [x] Infrastructure is provisioned through Terraform
- [ ] Frontend assets are deployed through the new CI/CD pipeline
- [ ] Global load times are reduced by 60-75%
- [ ] Monthly costs are reduced by 70%
- [ ] Build time is reduced by 50%
- [ ] Zero downtime during migration
- [ ] All existing functionality is preserved
- [ ] API routing maintains seamless backend integration
- [ ] Environment-specific configurations are working

## Security Considerations

- HTTPS is enforced for all traffic
- SSL/TLS security is maintained
- Proper access controls for GCS buckets:
  - No public access (`public_access_prevention = "enforced"`)
  - Restricted to Cloudflare CDN service account and deployment pipeline only
  - IAM binding with minimal required permissions
  - Explicit removal of any public access IAM members
- Cloudflare security features are enabled
- Separate service accounts for CDN access and deployment operations
- All requests to GCS must be authenticated via service accounts

### Security Verification

After deployment, verify the bucket security configuration:

```bash
./scripts/check-bucket-security.sh <environment> [bucket_name]
```

For more details on the security improvements, see the [CDN Security Improvements](./cdn-security-improvements.md) document.

## Contact

For questions or issues related to the CDN migration, please contact the platform engineering team.
