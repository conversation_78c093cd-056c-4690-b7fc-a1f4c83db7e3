# 🌐 CDN Domain Migration Implementation Guide

This document provides a comprehensive guide for the CDN migration from Docker containers on Cloud Run to a Cloudflare CDN + Google Cloud Storage architecture, with centralized domain management based on the `vedavivi.app` root domain.

## 📋 Domain Architecture Overview

All platform URLs are calculated from the root domain `vedavivi.app` using environment-based subdomain patterns:

| Environment | Web Domain | API Domain | WebSocket Domain |
|-------------|-----------|------------|-----------------|
| Production | `vedavivi.app` | `api.vedavivi.app` | `ws.vedavivi.app` |
| Staging | `www-staging.vedavivi.app` | `api-staging.vedavivi.app` | `ws-staging.vedavivi.app` |
| Development | `development.vedavivi.app` | `api-development.vedavivi.app` | `ws-development.vedavivi.app` |

## 🔧 Technical Implementation

### 1️⃣ Terraform Configuration

The domain calculation is implemented in Terraform using environment variables and locals:

```hcl
# Domain calculation based on environment
locals {
  web_domain = var.environment == "production" ? var.root_domain : "${var.environment}.${var.root_domain}"
  api_domain = var.environment == "production" ? "api.${var.root_domain}" : "api-${var.environment}.${var.root_domain}"
  ws_domain  = var.environment == "production" ? "ws.${var.root_domain}" : "ws-${var.environment}.${var.root_domain}"

  web_url       = "https://${local.web_domain}"
  api_url       = "https://${local.api_domain}"
  websocket_url = "wss://${local.ws_domain}"
}
```

### 2️⃣ DNS Configuration

DNS records are created in Cloudflare with the following patterns:

```hcl
# DNS Records for Web Application
resource "cloudflare_record" "web" {
  zone_id = cloudflare_zone.main.id
  name    = var.environment == "production" ? "@" : var.environment
  content = var.gcs_bucket_url
  type    = "CNAME"
  proxied = true
}

# DNS Records for API
resource "cloudflare_record" "api" {
  zone_id = cloudflare_zone.main.id
  name    = var.environment == "production" ? "api" : "api-${var.environment}"
  content = var.backend_origin_url
  type    = "CNAME"
  proxied = true
}

# DNS Records for WebSocket
resource "cloudflare_record" "websocket" {
  zone_id = cloudflare_zone.main.id
  name    = var.environment == "production" ? "ws" : "ws-${var.environment}"
  content = var.websocket_origin_url
  type    = "CNAME"
  proxied = true
}
```

### 3️⃣ Environment Variable Configuration

Frontend applications receive calculated URLs during build through GitHub Actions:

```yaml
env:
  VITE_GRAPHQL_API_URL: ${{ steps.terraform.outputs.api_url }}/graphql
  VITE_WS_URL: ${{ steps.terraform.outputs.websocket_url }}/ws
  VITE_CDN_URL: ${{ steps.terraform.outputs.web_url }}
```

## 🛡️ Security Enhancements

The CDN migration implements secure bucket access by:

1. **Eliminating Public Access**: Removed `allUsers` IAM member from the GCS bucket
2. **Service Account Restriction**: Created dedicated service accounts for:
   - Cloudflare CDN access (read-only)
   - Deployment workflows (write access)
3. **Bucket-Level Access Control**: Enabled uniform bucket-level access for consistent IAM policies

```hcl
# CDN Origin Access - Enhanced security with Cloudflare integration
resource "google_storage_bucket_iam_binding" "cloudflare_access" {
  bucket = google_storage_bucket.web_assets.name
  role   = "roles/storage.objectViewer"
  members = concat(
    var.cdn_service_account != null ? ["serviceAccount:${var.cdn_service_account}"] : [],
    length(var.allowed_origins) > 0 ? ["serviceAccount:cloudflare-cdn@${var.project_id}.iam.gserviceaccount.com"] : []
  )
}

# Service Account for CI/CD
resource "google_service_account" "cdn_deployer" {
  account_id   = "${var.environment}-cdn-deployer"
  display_name = "CDN Deployer for ${var.environment}"
  description  = "Service account for deploying static assets to the CDN"
}

resource "google_storage_bucket_iam_member" "deployer_access" {
  bucket = google_storage_bucket.web_assets.name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${google_service_account.cdn_deployer.email}"
}
```

## 🚀 Deployment Process

### Staging Deployment

1. Run Terraform plan for validation:
   ```bash
   cd terraform/environments/staging
   terraform plan
   ```

2. Apply infrastructure changes:
   ```bash
   terraform apply
   ```

3. Validate domain configuration:
   ```bash
   ./scripts/validate-cdn-domains.sh staging
   ```

4. Deploy frontend assets:
   ```bash
   # Triggered by GitHub Actions workflow
   # Or manually with:
   ./scripts/deploy-frontend-assets.sh staging
   ```

### Production Deployment

After successful staging validation:

1. Run Terraform plan for validation:
   ```bash
   cd terraform/environments/production
   terraform plan
   ```

2. Apply infrastructure changes:
   ```bash
   terraform apply
   ```

3. Validate domain configuration:
   ```bash
   ./scripts/validate-cdn-domains.sh production
   ```

4. Deploy frontend assets:
   ```bash
   # Triggered by GitHub Actions workflow
   # Or manually with:
   ./scripts/deploy-frontend-assets.sh production
   ```

## 🧪 Validation Strategy

1. **DNS Verification**: Ensure all domains resolve correctly
2. **SSL Certificate Validation**: Verify SSL certificates for all domains
3. **HTTP Status Checks**: Check HTTP status codes for all endpoints
4. **Frontend Environment Testing**: Validate correct URL integration in frontend
5. **API Integration Testing**: Verify API communication through new domains
6. **WebSocket Communication**: Test WebSocket connections

Use the validation script to automate these checks:
```bash
./scripts/validate-cdn-domains.sh [environment] [root_domain]
```

## 📊 Performance Metrics

Compare performance metrics before and after migration:

1. **Time to First Byte (TTFB)**: Should improve from 200-800ms to 50-150ms
2. **Page Load Time**: Should improve by >50%
3. **Cache Hit Ratio**: Should exceed 95%
4. **Cold Start Time**: Should be eliminated (from 100-1000ms to 0ms)

## 🔄 Rollback Procedure

In case of deployment issues:

1. Revert to the previous Terraform state:
   ```bash
   terraform plan -destroy -target=module.cloudflare_cdn
   terraform destroy -target=module.cloudflare_cdn
   ```

2. Verify Cloud Run service is still operational
3. Switch DNS records back to Cloud Run origin
4. Update frontend environment variables to use Cloud Run URLs

## 📝 Troubleshooting

### DNS Issues
- Check Cloudflare DNS propagation with `dig +trace`
- Verify proper CNAME records in Cloudflare dashboard

### SSL Certificate Issues
- Ensure Cloudflare SSL mode is set to "Flexible"
- Check certificate validity with `openssl s_client`

### Access Control Issues
- Verify service account permissions with `gcloud iam service-accounts get-iam-policy`
- Check bucket access logs for permission denials

## 🏁 Completion Checklist

- [ ] Infrastructure deployed successfully
- [ ] DNS records resolving correctly
- [ ] SSL certificates valid and active
- [ ] Frontend assets accessible via CDN
- [ ] API requests working through new domains
- [ ] WebSocket connections established successfully
- [ ] Performance metrics collected and validated
- [ ] Documentation updated with new domain structure
- [ ] Team training completed for the new deployment process