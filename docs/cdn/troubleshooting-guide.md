# CDN and API Troubleshooting Guide

## Quick Diagnosis Checklist

### API Not Responding (404/502/503)

1. **Test Direct Cloud Run URL**
   ```bash
   curl https://[service-url].run.app/api/health
   ```
   - ✅ Works: DNS/domain mapping issue
   - ❌ Fails: Application/deployment issue

2. **Check Domain Mapping Status**
   ```bash
   gcloud beta run domain-mappings describe --domain=[custom-domain] --region=[region]
   ```
   Look for: `Ready: True`, `CertificateProvisioned: True`

3. **Verify DNS Configuration**
   ```bash
   dig [custom-domain] CNAME
   # Should return: ghs.googlehosted.com
   ```

4. **Check Cloudflare Proxy Status**
   - Cloudflare Dashboard → DNS → Check cloud icon
   - 🟠 Proxied: May cause conflicts
   - 🔘 DNS-only: Direct routing (recommended)

### Startup Probe Failures

**Symptoms**: Service fails to start, health check timeouts

**Quick Fix**:
1. Check if health endpoints are excluded from host validation
2. Verify `TRUSTED_HOSTS` environment variable includes `*.run.app`
3. Ensure health check middleware bypasses security checks

**Code Check**:
```python
# Should exist in middleware
health_check_paths = ["/api/health", "/health", "/healthz"]
```

### CSP Violations in GraphQL

**Symptoms**: GraphQL interface loads but shows CSP errors in browser console

**Quick Fix**:
1. Check if GraphQL routes have relaxed CSP
2. Verify `data:` is allowed in `font-src` directive
3. Ensure external CDNs are whitelisted

**CSP Check**:
```python
# GraphQL CSP should include:
"font-src 'self' data: https://unpkg.com https://cdn.jsdelivr.net https://fonts.gstatic.com;"
```

### Redirect Loops (302)

**Symptoms**: Infinite redirects, malformed location headers

**Common Causes**:
1. Cloudflare proxy conflicts with domain mapping
2. HTTPS redirect middleware interfering
3. Corrupted domain mapping

**Quick Fix**:
1. Set DNS to "DNS-only" in Cloudflare
2. Recreate domain mapping if corrupted
3. Disable HTTPS redirect middleware if CDN handles HTTPS

## Emergency Procedures

### Complete API Outage

1. **Immediate Fallback**
   ```bash
   # Use direct Cloud Run URL temporarily
   # Update DNS to point to: [service-url].run.app
   ```

2. **Domain Mapping Reset**
   ```bash
   # Delete and recreate domain mapping
   gcloud beta run domain-mappings delete --domain=[domain] --region=[region]
   gcloud beta run domain-mappings create --domain=[domain] --service=[service] --region=[region]
   ```

3. **DNS Reset**
   ```bash
   # In Cloudflare: Delete and recreate CNAME record
   # Type: CNAME, Name: [subdomain], Target: ghs.googlehosted.com
   ```

### GraphQL Interface Broken

1. **Disable CSP Temporarily**
   ```python
   # In security_headers.py, comment out CSP for debugging
   # response.headers["Content-Security-Policy"] = "..."
   ```

2. **Check External Resources**
   ```bash
   # Test if unpkg.com is accessible
   curl -I https://unpkg.com/@graphiql/react@0.20.4/graphiql.min.js
   ```

## Monitoring Commands

### Health Check Monitoring
```bash
# Continuous health check
watch -n 5 'curl -s -o /dev/null -w "%{http_code}" https://api-staging.vedavivi.app/api/health'
```

### Domain Mapping Status
```bash
# Check all domain mappings
gcloud beta run domain-mappings list --region=us-central1
```

### DNS Propagation Check
```bash
# Check DNS from multiple servers
dig @******* api-staging.vedavivi.app
dig @******* api-staging.vedavivi.app
```

### Cloud Run Logs
```bash
# Real-time logs
gcloud run services logs tail api-staging --region=us-central1
```

## Configuration Templates

### Cloudflare DNS Record
```
Type: CNAME
Name: api-staging
Target: ghs.googlehosted.com
Proxy Status: DNS-only (🔘)
TTL: Auto
```

### Cloud Run Domain Mapping
```bash
gcloud beta run domain-mappings create \
  --domain=api-staging.vedavivi.app \
  --service=api-staging \
  --region=us-central1
```

### Environment Variables
```yaml
TRUSTED_HOSTS: "api-staging.vedavivi.app,*.run.app"
ENVIRONMENT: "staging"
```

## Common Error Messages

### "Invalid host header"
- **Cause**: TrustedHostMiddleware rejecting request
- **Fix**: Add domain to TRUSTED_HOSTS or exclude health endpoints

### "Refused to load... CSP directive"
- **Cause**: Content Security Policy blocking resource
- **Fix**: Add source to appropriate CSP directive

### "SSL_ERROR_SYSCALL"
- **Cause**: SSL certificate not ready or domain mapping incomplete
- **Fix**: Wait for certificate provisioning (5-10 minutes)

### "404 Not Found" from Google
- **Cause**: Domain mapping doesn't exist or service mismatch
- **Fix**: Recreate domain mapping with correct service name

## Prevention Checklist

### Before Deployment
- [ ] Test health endpoints work without host validation
- [ ] Verify CSP allows all required external resources
- [ ] Check domain mapping exists and is healthy
- [ ] Confirm DNS points to correct target

### After Deployment
- [ ] Test API health endpoint responds 200
- [ ] Verify GraphQL interface loads without CSP errors
- [ ] Check startup probes are passing
- [ ] Monitor logs for any middleware errors

### Regular Maintenance
- [ ] Monitor SSL certificate expiration
- [ ] Check domain mapping health monthly
- [ ] Review CSP violation reports
- [ ] Update trusted hosts as needed
