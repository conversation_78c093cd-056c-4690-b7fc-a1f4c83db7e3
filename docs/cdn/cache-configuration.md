# CDN Cache Configuration

This document describes the configurable CDN caching system for both frontend and API endpoints in the A2A platform.

## Overview

The CDN caching system provides configurable caching for:
- **Frontend static assets** (CSS, JS, images)
- **HTML files** (dynamic content)
- **API endpoints** (REST APIs)
- **GraphQL endpoints** (GraphQL queries)

## Configuration Structure

### Module-Based Configuration

Cache configuration is managed through the `cloudflare-cdn` Terraform module with comprehensive variable definitions and validation.

### Base Configuration

Cache settings are defined in `terraform.tfvars` files for each environment:

```hcl
cache_config = {
  # Frontend static assets - Long cache for performance
  static_assets_edge_ttl    = 31536000  # 1 year
  static_assets_browser_ttl = 31536000  # 1 year

  # HTML files - Moderate cache for content updates
  html_edge_ttl    = 1800   # 30 minutes
  html_browser_ttl = 1800   # 30 minutes

  # API caching - Can be enabled/disabled
  api_cache_enabled     = false
  api_edge_ttl         = 300     # 5 minutes when enabled
  api_browser_ttl      = 0       # No browser cache for APIs

  # GraphQL caching - Can be enabled/disabled
  graphql_cache_enabled     = false
  graphql_edge_ttl         = 60      # 1 minute when enabled
  graphql_browser_ttl      = 0       # No browser cache for GraphQL

  # Global settings
  development_mode = false    # Bypass cache in development
  cache_level     = "aggressive"
  browser_cache_ttl = 14400  # 4 hours default browser cache
}
```

### Environment Variable Overrides

You can override cache settings using GitHub Actions environment variables:

| Environment Variable | Description | Example Values |
|---------------------|-------------|----------------|
| `CACHE_API_ENABLED` | Enable/disable API caching | `true`, `false` |
| `CACHE_GRAPHQL_ENABLED` | Enable/disable GraphQL caching | `true`, `false` |
| `CACHE_DEVELOPMENT_MODE` | Enable development mode (bypass cache) | `true`, `false` |
| `CACHE_STATIC_ASSETS_TTL` | Static assets cache TTL (seconds) | `86400` (1 day) |
| `CACHE_HTML_TTL` | HTML cache TTL (seconds) | `300` (5 minutes) |
| `CACHE_API_TTL` | API cache TTL (seconds) | `60` (1 minute) |
| `CACHE_GRAPHQL_TTL` | GraphQL cache TTL (seconds) | `30` (30 seconds) |

## Environment-Specific Defaults

### Staging Environment
- **API caching**: ENABLED (for testing)
- **GraphQL caching**: ENABLED (for testing)
- **Static assets TTL**: 1 day (shorter than production)
- **HTML TTL**: 5 minutes
- **API TTL**: 1 minute
- **GraphQL TTL**: 30 seconds

### Production Environment
- **API caching**: DISABLED (for safety)
- **GraphQL caching**: DISABLED (for safety)
- **Static assets TTL**: 1 year (maximum performance)
- **HTML TTL**: 30 minutes
- **API TTL**: 5 minutes (when enabled)
- **GraphQL TTL**: 1 minute (when enabled)

## Cache Behavior

### Frontend Assets
- **Static assets** (`/assets/*`): Long-term caching with versioning
- **HTML files** (`*.html`): Short-term caching for content updates

### API Endpoints
- **REST APIs** (`/api/*`): Configurable caching with smart cache keys
- **GraphQL** (`/api/graphql*`): Separate caching configuration
- **Cache keys**: Include host, authorization headers, and optionally device type

### Cache Invalidation
- **Manual**: Use Cloudflare dashboard or API
- **Automatic**: Deploy new versions with different asset hashes
- **Development mode**: Bypasses all caching when enabled

## Usage Examples

### Enable API Caching in Production
Set GitHub Actions environment variable:
```
CACHE_API_ENABLED=true
```

### Reduce Cache TTL for Testing
Set GitHub Actions environment variables:
```
CACHE_API_TTL=30
CACHE_GRAPHQL_TTL=15
```

### Enable Development Mode
Set GitHub Actions environment variable:
```
CACHE_DEVELOPMENT_MODE=true
```

## Resource Management

### New Deployments
For new environments without existing Cloudflare resources:
```bash
# Default behavior - creates all resources
terraform apply
```

### Existing Resources
For environments with existing DNS records or page rules:
```bash
# Quick setup for imports
./terraform/scripts/setup_imports.sh

# Or use automated import script
./terraform/scripts/import_cloudflare_resources.sh
```

The module automatically handles conflicts by providing:
- `create_dns_records` variable to control DNS record creation
- `create_page_rules` variable to control page rule creation
- Comprehensive import guides and scripts

## Best Practices

1. **Start Conservative**: Begin with caching disabled for APIs in production
2. **Test in Staging**: Enable caching in staging environment first
3. **Monitor Performance**: Use Cloudflare analytics to monitor cache hit rates
4. **Cache Invalidation**: Plan for cache invalidation strategies
5. **Security**: Be careful with caching authenticated endpoints
6. **Resource Management**: Use import tools for existing Cloudflare resources

## Debugging and Monitoring

### View Current Cache Configuration
```bash
# Show configuration from terraform.tfvars
./scripts/manage-cache.sh show staging

# Show live Terraform outputs (requires terraform init)
./scripts/manage-cache.sh outputs staging
```

### Terraform Outputs for Debugging
The CDN module provides comprehensive outputs for observability:

#### `cache_config` Output
Complete cache configuration including:
- Global settings (development_mode, cache_level, browser_cache_ttl)
- Static assets configuration (edge_ttl, browser_ttl)
- HTML configuration (edge_ttl, browser_ttl)
- API configuration (cache_enabled, edge_ttl, browser_ttl, cache_by_device, cache_key_fields)
- GraphQL configuration (cache_enabled, edge_ttl, browser_ttl, cache_by_device)

#### `page_rules_status` Output
Status of page rule creation showing:
- Which rules were created vs imported
- Rule targets and priorities
- Creation reasons and conflict resolution

#### `dns_records_status` Output
Status of DNS record creation showing:
- Which records were created vs imported
- Domain names and targets
- Import handling status

#### `cache_performance_info` Output
Performance analysis information:
- Cache configuration summary
- TTL distribution analysis
- Development mode status

### Example Output
```bash
$ ./scripts/manage-cache.sh outputs staging

cache_config = {
  "api" = {
    "browser_ttl" = 0
    "cache_by_device" = false
    "cache_enabled" = true
    "cache_key_fields" = ["host", "authorization"]
    "edge_ttl" = 60
  }
  "development_mode" = false
  "static_assets" = {
    "browser_ttl" = 86400
    "edge_ttl" = 86400
  }
  # ... more configuration
}
```

## Troubleshooting

### Cache Not Working
1. Check if development mode is enabled: `./scripts/manage-cache.sh outputs <env>`
2. Verify cache configuration in Terraform outputs
3. Check Cloudflare page rules in dashboard
4. Review `page_rules_status` output for rule creation status
5. Verify page rules were created (not skipped due to imports)

### Stale Content
1. Manually purge cache in Cloudflare dashboard
2. Reduce TTL values temporarily
3. Enable development mode for immediate bypass
4. Check `cache_performance_info` for TTL analysis

### Performance Issues
1. Monitor cache hit rates in Cloudflare analytics
2. Use `cache_performance_info` output to analyze configuration
3. Adjust TTL values based on content update frequency
4. Consider enabling API caching for read-heavy endpoints
5. Review `page_rules_status` to ensure rules are active

### Resource Conflicts
1. Use import tools for existing Cloudflare resources: `./terraform/scripts/setup_imports.sh`
2. Check `dns_records_status` and `page_rules_status` outputs for conflict resolution
3. Set `create_dns_records = false` or `create_page_rules = false` if importing
4. See [Import Guide](../terraform/modules/cloudflare-cdn/IMPORT_GUIDE.md) for detailed instructions
