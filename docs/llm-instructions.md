# Development Tips
- Need to be in apps/backend directory when working on backend implementation.
- Use pyproject.toml for dependency management.
- Use bun for frontend development.
- Use Docker for standing up the backend service.
- Use 'docker compose' command (not 'docker-compose') since the project has a docker-compose.yml file.
- SQLAlchemy database URLs use 'postgresql+asyncpg://' for async connections and 'postgresql://' for sync connections.
- The project uses pytest for backend tests, bun for frontend tests, and should have scripts for running tests, managing Docker containers, and handling database migrations.
- User wants to configure pre-commit to automatically fix code issues when possible rather than just reporting them.
- User wants to configure MyPy type checking in the pre-commit config file as specified in the GitHub CI workflow.
- User wants parity between MyPy configuration in pre-commit-config.yaml and GitHub CI workflow to ensure consistent behavior between local and CI environments.
- Pytest fails when run from the project root but passes when run from the apps/backend directory.
- Use apps/backend/pyproject.toml for installing Python dependencies in the Dockerfile.
- Backend pytest is failing due to missing test database; user wants an idempotent script to create the test database and tables using Alembic for both local and CI environments.
- Backend tests should be run from the apps/backend directory, not from the project root.
- Local testing needs to use the postgres db on Docker.
- User prefers to use the Docker PostgreSQL instance rather than a local PostgreSQL server for development.
- User wants to know if there's a way to run pre-commit without running pytest via a flag.
- User wants to skip pytest-backend when running pre-commit checks with the SKIP=pytest-backend environment variable.

# Configuration and Environment Variables
- User prefers to consolidate environment variables in a .env file at the project root and have Docker Compose files reference these variables instead of hardcoding them.
- User prefers to consolidate environment variables in a root .env file, create .env.example with dummy values for version control, add .env to .gitignore, use descriptive variable names, and add comprehensive documentation comments.
- User prefers to centralize database connection configuration in Docker Compose files using dynamic construction of DATABASE_URL from POSTGRES_* variables rather than duplicating it across multiple .env files.
- User wants to include POSTGRES_HOST and POSTGRES_PORT variables in database configuration files for more flexibility.
- User is questioning why test_db_url is needed when DATABASE_URL environment variables are already available, suggesting potential redundancy in database configuration.
- User prefers to use Docker Compose environment variables as the single source of truth for configuration management rather than .env files.
- User wants scripts to use the DATABASE_URL environment variable for database connections instead of constructing connection strings internally, centralizing database configuration in environment variables.
- User wants DATABASE_URL to be required without fallback values, throwing an exception with a clear error message when not set in environment variables.

# Frontend Development
- The frontend Docker container should run in development mode with 'bun run dev -- --host 0.0.0.0' for hot-reloading, not in preview mode with 'vite preview'.
- Bun should be configured to run on port 5173.
- The frontend development server runs on port 5173 for e2e testing.

# CI/CD
- CI workflow should use PostgreSQL with DATABASE_URL in format 'postgres://test_user:test_password@localhost:5432/test_database' for testing.

# Scripts
- User wants the run-backend-tests.sh script to have a --setup flag that makes dependency installation and database setup optional.
- User wants to use .env.test with Docker for running tests instead of the current workflow with run-backend-tests.sh script.
- User wants Docker as the default testing environment with CI as an explicit alternative, requiring consolidation of testing configuration files and updating scripts to remove --docker flags and add --ci flags.

# Testing
- Instead of pytest, use `./scripts/run-backend-tests.sh` for running tests in Docker for any python file from apps/backend.
- User prefers contract tests for webhooks (like Clerk) to be consumer-focused, define clear contracts with comprehensive mock payloads, run in isolation without network calls, verify all expected outcomes including default values, and maintain alignment with provider documentation.

# Logging
- User wants to configure DEBUG log level for the backend service when running with Docker to see detailed logs including the CLERK_JWT_PUBLIC_KEY logging.

# Code Quality & Linting
- **YAML Formatting Rules (Critical):**
  - **NO TRAILING SPACES**: YAML files must not have trailing spaces - yamllint will fail with "trailing spaces" error if empty lines contain spaces
  - **Empty lines must be completely empty**: No spaces or tabs on blank lines
  - **Comments need proper spacing**: YAML comments must have at least two spaces before the comment character (#)
  - **When writing YAML files**: Always end lines cleanly without trailing whitespace
  - **GitHub Actions workflows**: Pay special attention to indentation and trailing spaces in `.github/workflows/*.yml` files
  - **Tool usage**: When using str-replace-editor for YAML files, ensure the new_str content has no trailing spaces on any lines

# Shortcuts/Wake Words
- **spec this** - when followed by a user story on a new line, use the user story and follow steps 2 and 3 from `./templates/README.md` to generate user story and QA testing specification files.
- **wrap it up** - The current conversation is too long. I need to move the current problem to another LLM. Save the context and prompt to a file called `CURRENT-PROBLEM.md`. Use visual aids, emojis, information-dense words, and a highly structured format.
- **unwrap it** - load the context and prompt from `CURRENT-PROBLEM.md` and continue the current problem.
- **precommit** - run `./scripts/precommit-check.sh` in the project root; If it fails, fix the error; and repeat until precommit-check passes

