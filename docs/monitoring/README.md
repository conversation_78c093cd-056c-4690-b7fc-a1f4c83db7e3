# A2A Platform Monitoring and Alerting Guide

## Overview

The A2A Platform monitoring system provides comprehensive observability for all infrastructure components, applications, and user interactions. This document outlines the monitoring architecture, available dashboards, alert policies, and operational procedures.

The monitoring system provides:
- **Real-time dashboards** for application and infrastructure metrics
- **Proactive alerting** for critical system events and anomalies
- **Log-based metrics** for application-specific monitoring
- **SSL certificate monitoring** with expiration tracking
- **Multi-environment support** with configurable thresholds
- **Performance optimization** and capacity planning insights

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Monitoring Components](#monitoring-components)
3. [Dashboards](#dashboards)
4. [Alert Policies](#alert-policies)
5. [Log-Based Metrics](#log-based-metrics)
6. [Configuration](#configuration)
7. [Operational Procedures](#operational-procedures)
8. [Troubleshooting](#troubleshooting)
9. [Roadmap and Future Enhancements](#roadmap-and-future-enhancements)

## Architecture Overview

The monitoring system is built on Google Cloud Monitoring and includes:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Cloud Run     │    │   Cloud SQL     │    │   Load Balancer │
│   Services      │────│   Database      │────│   & CDN         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────────────────────────────┐
         │        Google Cloud Monitoring          │
         │  ┌─────────────┐  ┌─────────────────┐   │
         │  │ Dashboards  │  │ Alert Policies  │   │
         │  └─────────────┘  └─────────────────┘   │
         │  ┌─────────────┐  ┌─────────────────┐   │
         │  │Log Metrics  │  │  Notifications  │   │
         │  └─────────────┘  └─────────────────┘   │
         └─────────────────────────────────────────┘
```

## Monitoring Components

### 1. Infrastructure Monitoring

#### Cloud Run Services
- **Request Rate**: Tracks incoming requests per second
- **Response Latency**: 95th percentile response times
- **Error Rate**: Percentage of non-2xx responses
- **CPU Utilization**: Container CPU usage
- **Memory Utilization**: Container memory usage
- **Instance Count**: Number of running instances
- **Service Availability**: Monitors healthy instance count

#### Cloud SQL Database
- **CPU Utilization**: Database server CPU usage
- **Memory Utilization**: Database server memory usage
- **Active Connections**: Number of open database connections
- **Disk Utilization**: Database storage usage

#### Load Balancer & SSL
- **Backend Failures**: Failed requests to backend services
- **SSL Handshake Latency**: SSL negotiation performance
- **Certificate Expiration**: Days until SSL certificate expires
- **Request Rate Anomaly**: Unusual traffic patterns

### 2. Application Monitoring

#### Log-Based Metrics
- **Application Errors**: General application error rate
- **GraphQL Errors**: GraphQL-specific error tracking
- **Authentication Failures**: Failed authentication attempts
- **Slow Queries**: Database queries exceeding 5 seconds
- **Redis Errors**: Cache and session store issues
- **WebSocket Errors**: Real-time communication failures

## Dashboards

### 1. Monitoring Dashboards

#### Application Overview Dashboard
- **Cloud Run Request Rate**: Requests per second across all services
- **Response Latency**: 95th percentile latency by service
- **Error Rate**: Non-2xx responses by service and response code
- **CPU Utilization**: Average CPU usage across Cloud Run services
- **Memory Utilization**: Average memory usage across Cloud Run services
- **Instance Count**: Number of active instances per service

#### Database Overview Dashboard (if Cloud SQL enabled)
- **Database CPU Utilization**: CPU usage of Cloud SQL instance
- **Database Memory Utilization**: Memory usage of Cloud SQL instance
- **Database Connections**: Active connection count
- **Database Disk Utilization**: Disk usage percentage

## Alert Policies

### Critical Alerts (Immediate Response Required)

#### 1. Service Availability
- **Threshold**: No healthy instances running
- **Duration**: 1 minute
- **Impact**: Complete service outage
- **Notification**: Immediate (PagerDuty, SMS)

#### 2. High Error Rate
- **Threshold**: > 5% error rate (10% for staging)
- **Duration**: 5 minutes
- **Impact**: User experience degradation
- **Notification**: High priority

#### 3. Application Errors
- **Threshold**: > 10 errors/minute (20 for staging)
- **Duration**: 1 minute
- **Impact**: Application functionality issues
- **Notification**: High priority

### Performance Alerts

#### 4. High CPU Utilization
- **Threshold**: > 80% CPU usage (85% for staging)
- **Duration**: 5 minutes
- **Impact**: Performance degradation, scaling needed
- **Notification**: Medium priority

#### 5. High Memory Utilization
- **Threshold**: > 85% memory usage (90% for staging)
- **Duration**: 5 minutes
- **Impact**: Risk of out-of-memory errors
- **Notification**: Medium priority

#### 6. High Response Latency
- **Threshold**: > 2 seconds (95th percentile, 3s for staging)
- **Duration**: 5 minutes
- **Impact**: Poor user experience
- **Notification**: Medium priority

### Security Alerts

#### 7. Authentication Failures
- **Threshold**: > 20 failures/minute (50 for staging)
- **Duration**: 5 minutes
- **Impact**: Potential security breach
- **Notification**: High priority

#### 8. GraphQL Errors
- **Threshold**: > 5 errors/minute (10 for staging)
- **Duration**: 1 minute
- **Impact**: API functionality issues
- **Notification**: Medium priority

### Infrastructure Alerts

#### 9. Database High CPU
- **Threshold**: > 80% DB CPU
- **Duration**: 5 minutes
- **Impact**: Database performance issues
- **Notification**: High priority

#### 10. High Connection Count
- **Threshold**: > 80 connections
- **Duration**: 5 minutes
- **Impact**: Connection pool exhaustion
- **Notification**: Medium priority

#### 11. Backend Failures
- **Threshold**: > 10% backend failure rate
- **Duration**: 5 minutes
- **Impact**: Load balancer backend issues
- **Notification**: High priority

#### 12. Request Rate Anomaly
- **Threshold**: > 100 requests/second
- **Duration**: 5 minutes
- **Impact**: Potential DDoS or traffic spike
- **Notification**: Medium priority

### SSL Monitoring

#### 13. Certificate Expiration
- **Threshold**: < 30 days until expiration
- **Duration**: Immediate
- **Impact**: Future service disruption
- **Notification**: High priority

#### 14. SSL Handshake Latency
- **Threshold**: > 200ms (99th percentile)
- **Duration**: 5 minutes
- **Impact**: SSL performance issues
- **Notification**: Low priority

## Log-Based Metrics

### Available Metrics

1. **Application Errors**: `a2a_application_errors_{environment}`
   - Tracks ERROR level logs and structured error logs
   - Filter: `severity>=ERROR OR jsonPayload.level="error"`

2. **GraphQL Errors**: `a2a_graphql_errors_{environment}`
   - Tracks GraphQL-specific errors
   - Filter: `jsonPayload.message:"GraphQL" AND severity>=ERROR`

3. **Authentication Failures**: `a2a_auth_failures_{environment}`
   - Tracks unauthorized, authentication, and forbidden events
   - Filter: `jsonPayload.message:"unauthorized|authentication|forbidden"`

4. **Slow Queries**: `a2a_slow_queries_{environment}`
   - Database queries exceeding 5 seconds
   - Filter: `jsonPayload.duration>5000 OR jsonPayload.execution_time>5000`

5. **Redis Errors**: `a2a_redis_errors_{environment}`
   - Cache and session store issues
   - Filter: `jsonPayload.message:"redis|cache" AND severity>=ERROR`

6. **WebSocket Errors**: `a2a_websocket_errors_{environment}`
   - Real-time communication failures
   - Filter: `jsonPayload.message:"websocket|ws" AND severity>=ERROR`

## Setup Instructions

### 1. Deploy Monitoring Infrastructure

```bash
# Navigate to staging environment
cd terraform/environments/staging

# Initialize and validate
terraform init
terraform validate

# Plan the deployment
terraform plan

# Apply the monitoring configuration
terraform apply
```

### 2. Configure Notification Channels

1. Go to [Google Cloud Console - Monitoring](https://console.cloud.google.com/monitoring)
2. Navigate to **Alerting** > **Notification Channels**
3. Create notification channels for:
   - Email notifications
   - Slack/Discord webhooks
   - PagerDuty integration
   - SMS alerts (for critical issues)

4. Update the Terraform configuration with channel IDs:

```hcl
module "monitoring" {
  # ... other configuration ...
  
  notification_channels = [
    "projects/clean-algebra-459903-b1/notificationChannels/YOUR_EMAIL_CHANNEL_ID",
    "projects/clean-algebra-459903-b1/notificationChannels/YOUR_SLACK_CHANNEL_ID"
  ]
}
```

### 3. Access Dashboards

After deployment, dashboard URLs will be available in Terraform outputs:

```bash
# Get dashboard URLs
terraform output monitoring_dashboard_urls

# Get monitoring workspace URL
terraform output monitoring_workspace_url
```

## Configuration

### Alert Thresholds

Customize alert thresholds in the monitoring module:

```hcl
module "monitoring" {
  # ... other configuration ...
  
  alert_thresholds = {
    error_rate_threshold         = 0.05   # 5% error rate
    cpu_utilization_threshold    = 0.8    # 80% CPU
    memory_utilization_threshold = 0.85   # 85% memory
    response_latency_threshold   = 2000   # 2 seconds
    application_errors_threshold = 10     # 10 errors/min
    graphql_errors_threshold    = 5       # 5 GraphQL errors/min
    auth_failures_threshold     = 20      # 20 auth failures/min
  }
}
```

### Environment-Specific Settings

Different thresholds are configured for each environment:

- **Production**: Strict thresholds for immediate detection
- **Staging**: More lenient thresholds to reduce noise during development
- **Development**: Monitoring enabled but alerts may be disabled

### SSL Monitoring (Optional)

Enable SSL certificate monitoring:

```hcl
module "monitoring" {
  # ... other configuration ...
  
  enable_ssl_monitoring = true
  certificate_ids = [
    "your-certificate-id-1",
    "your-certificate-id-2"
  ]
  certificate_expiry_threshold_days = 30
}
```

## Maintenance

### Regular Tasks

1. **Review Alert Policies**: Monthly review of alert thresholds and effectiveness
2. **Update Notification Channels**: Ensure contact information is current
3. **Dashboard Optimization**: Add new metrics as the platform evolves
4. **Cost Monitoring**: Monitor Cloud Monitoring costs and optimize retention

### Troubleshooting

#### Common Issues

1. **Missing Metrics**: Ensure services are properly labeled and deployed
2. **False Positives**: Adjust thresholds based on normal operating patterns
3. **Missing Alerts**: Check notification channel configuration
4. **Dashboard Errors**: Verify metric filters and aggregation settings

#### Debugging Steps

1. Check Cloud Run service logs for metric emission
2. Verify IAM permissions for monitoring service account
3. Test notification channels manually
4. Review log-based metric filters for accuracy

## Operational Procedures

### Daily Monitoring Tasks

1. **Dashboard Review**:
   - Check application overview dashboard for anomalies
   - Review error rates and response times
   - Verify service availability and instance counts

2. **Alert Review**:
   - Investigate any fired alerts from previous 24 hours
   - Document any false positives for threshold adjustment
   - Update alert configurations based on operational learnings

### Weekly Monitoring Tasks

1. **Performance Analysis**:
   - Review weekly performance trends
   - Identify capacity planning needs
   - Analyze slow query patterns

2. **Configuration Review**:
   - Validate alert thresholds against actual usage patterns
   - Update notification channels as team changes
   - Review and update documentation

### Alert Response Procedures

#### High Priority Alerts

1. **Service Availability** - Complete Outage:
   ```bash
   # Check service status
   gcloud run services describe api-staging --region=us-central1
   
   # Check recent deployments
   gcloud run revisions list --service=api-staging --region=us-central1
   
   # Review logs for errors
   gcloud logs read "resource.type=cloud_run_revision" --limit=50
   ```

2. **High Error Rate**:
   ```bash
   # Check error distribution
   gcloud logs read "resource.type=cloud_run_revision AND severity>=ERROR" --limit=100
   
   # Check recent changes
   gcloud run revisions list --service=api-staging --region=us-central1 --limit=5
   ```

#### Medium Priority Alerts

1. **High Resource Utilization**:
   ```bash
   # Check current resource usage
   gcloud monitoring metrics list --filter="metric.type:run.googleapis.com"
   
   # Consider scaling adjustments
   gcloud run services update api-staging --cpu=2 --memory=2Gi --region=us-central1
   ```

2. **Database Performance Issues**:
   ```bash
   # Check database performance insights
   gcloud sql operations list --instance=a2a-staging-db
   
   # Review active connections
   gcloud sql connect a2a-staging-db --user=postgres
   ```

## Troubleshooting

### Common Issues

#### 1. Missing Metrics Data

**Symptoms**: Dashboard widgets showing "No data available"

**Causes**:
- Service not generating metrics
- Incorrect metric filters
- Insufficient permissions

**Resolution**:
```bash
# Check service status
gcloud run services list --filter="metadata.name:api-staging"

# Verify metrics are being generated
gcloud logging read "resource.type=cloud_run_revision" --limit=10

# Check IAM permissions
gcloud projects get-iam-policy PROJECT_ID
```

#### 2. False Positive Alerts

**Symptoms**: Alerts firing when service appears healthy

**Causes**:
- Thresholds set too low
- Temporary spikes in metrics
- Metric aggregation issues

**Resolution**:
- Review and adjust alert thresholds
- Increase alert duration before firing
- Add additional conditions to reduce noise

#### 3. Missing Database Metrics

**Symptoms**: Database dashboard empty

**Causes**:
- Cloud SQL instance not configured
- Monitoring not enabled on database
- Incorrect instance name in configuration

**Resolution**:
```bash
# Check Cloud SQL instance
gcloud sql instances describe a2a-staging-db

# Verify monitoring is enabled
gcloud sql instances patch a2a-staging-db --insights-config-query-insights-enabled

# Update Terraform configuration with correct instance name
```

### Emergency Procedures

#### Complete Monitoring Outage

1. **Verify Google Cloud Status**:
   - Check https://status.cloud.google.com/
   - Review any ongoing incidents

2. **Alternative Monitoring**:
   - Use application health endpoints
   - Check service logs directly
   - Monitor via curl/wget scripts

3. **Communication**:
   - Notify team via alternative channels
   - Document the outage duration and impact
   - Create incident report

## Best Practices

1. **Gradual Rollout**: Test monitoring in staging before production
2. **Threshold Tuning**: Start with conservative thresholds and adjust based on experience
3. **Alert Fatigue**: Avoid too many low-priority alerts
4. **Documentation**: Keep runbooks updated for common alert scenarios
5. **Integration**: Connect monitoring with incident response workflows
6. **Security**: Ensure alert payloads don't contain sensitive information
7. **Cost Optimization**: Monitor monitoring costs and optimize retention policies

## Integration with CI/CD

### Terraform Deployment

The monitoring module is automatically deployed with infrastructure:

```bash
# Deploy monitoring with staging environment
cd terraform/environments/staging
terraform plan -var-file=terraform.tfvars
terraform apply

# Get monitoring URLs
terraform output monitoring_dashboard_urls
terraform output monitoring_workspace_url
```

### Validation Scripts

Include monitoring validation in deployment pipelines:

```bash
#!/bin/bash
# scripts/validate-monitoring.sh

echo "Validating monitoring setup..."

# Check alert policies exist
gcloud alpha monitoring policies list --filter="displayName:A2A staging"

# Verify dashboards are accessible
curl -s "https://monitoring.googleapis.com/v1/projects/$PROJECT_ID/dashboards" \
  -H "Authorization: Bearer $(gcloud auth print-access-token)"

echo "Monitoring validation complete"
```

## Roadmap and Future Enhancements

### Planned Integrations

#### Slack Alerts Integration
- **Priority**: High
- **Description**: Integrate Slack notifications for critical monitoring alerts to improve team response times and visibility
- **Implementation Plan**:
  - Configure Slack webhook notification channels in Google Cloud Monitoring
  - Create dedicated Slack channels for different alert severity levels (#alerts-critical, #alerts-warnings, #alerts-info)
  - Implement alert formatting for better readability in Slack messages
  - Add alert acknowledgment and escalation workflows through Slack
  - Set up on-call rotation integration with Slack notifications
- **Benefits**:
  - Real-time team notifications in preferred communication platform
  - Faster incident response and collaboration
  - Better alert visibility and team coordination
  - Reduced alert fatigue through proper channel organization
- **Timeline**: Q1 2025
- **Dependencies**: Slack workspace setup, team notification preferences

#### Database Infrastructure Enhancements
- **Priority**: High
- **Description**: Implement comprehensive database seeding capabilities for development, testing, and demonstration environments
- **Implementation Plan**:
  - Design seed data structure for users, assistants, conversations, and tasks
  - Implement environment-specific seeding strategies (minimal, development, demo)
  - Create idempotent seeding scripts that can be run multiple times safely
  - Add validation and rollback capabilities for seed operations
  - Integrate seeding with CI/CD pipelines for automated testing environments
- **Benefits**:
  - Consistent development and testing environments
  - Faster onboarding for new developers
  - Reliable demo data for presentations and user testing
  - Improved integration testing with realistic data sets
- **Timeline**: Q1 2025
- **Dependencies**: Database schema stabilization, test data requirements

#### Enhanced Alerting Features
- **Custom Alert Runbooks**: Link specific troubleshooting guides to each alert type
- **Alert Correlation**: Group related alerts to reduce noise during incidents
- **Dynamic Thresholds**: Machine learning-based threshold adjustment
- **Cross-Platform Notifications**: Integration with Microsoft Teams, Discord, and PagerDuty

#### Advanced Monitoring Capabilities
- **Application Performance Monitoring (APM)**: Distributed tracing for request flows
- **User Experience Monitoring**: Real user monitoring and synthetic checks
- **Cost Monitoring**: Resource usage and cost optimization alerts
- **Security Event Monitoring**: Integration with security logs and threat detection

## Support and Resources

### Documentation Links

- [Google Cloud Monitoring Documentation](https://cloud.google.com/monitoring/docs)
- [Alert Policy Reference](https://cloud.google.com/monitoring/alerts)
- [Terraform Google Provider](https://registry.terraform.io/providers/hashicorp/google/latest/docs)
- [Alert Policy Best Practices](https://cloud.google.com/monitoring/alerts/policies-in-json)
- [Dashboard Configuration Guide](https://cloud.google.com/monitoring/dashboards)
- [Log-based Metrics](https://cloud.google.com/logging/docs/logs-based-metrics)
- [Slack Notifications Setup](https://cloud.google.com/monitoring/support/notification-options#slack)

### Useful Commands

```bash
# List all alert policies
gcloud alpha monitoring policies list

# Get alert policy details
gcloud alpha monitoring policies describe POLICY_ID

# List notification channels
gcloud alpha monitoring channels list

# View recent alerts
gcloud logging read "protoPayload.serviceName=monitoring.googleapis.com" --limit=10

# Export monitoring configuration
gcloud alpha monitoring policies list --format="export" > alert-policies.yaml
```

---

**Last Updated**: December 2024
**Version**: 2.0
**Maintained By**: Platform Engineering Team