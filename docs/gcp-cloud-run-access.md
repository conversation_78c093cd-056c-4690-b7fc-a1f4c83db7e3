# Accessing Google Cloud Platform and Viewing Cloud Run Logs

## Prerequisites

**Important**: You must have a `@vedavivi.com` email address to access the project's Google Cloud Platform resources. Contact the platform administrator to request access if you don't have one.

## Getting Access to Google Cloud Platform

1. **Obtain a vedavivi.com email account**
   - Contact the platform administrator to request a `@vedavivi.com` email address
   - This email will be used for all GCP access and permissions

2. **Request GCP Project Access**
   - Once you have your vedavivi.com email, request access to the A2A Platform GCP project
   - You'll need the following IAM roles:
     - `Cloud Run Viewer` (minimum for viewing logs)
     - `Logging Viewer` (for accessing Cloud Logging)
     - Additional roles may be granted based on your responsibilities

3. **Access the Google Cloud Console**
   - Navigate to [https://console.cloud.google.com](https://console.cloud.google.com)
   - Sign in with your `@vedavivi.com` email address
   - Select the A2A Platform project from the project dropdown

## Viewing Cloud Run Logs

### Method 1: Through Cloud Run Console

1. **Navigate to Cloud Run**
   - In the Google Cloud Console, go to "Cloud Run" from the navigation menu
   - Select the service you want to view logs for (e.g., `a2a-platform-backend`, `a2a-platform-web`)

2. **Access Service Logs**
   - Click on the service name to open its details
   - Go to the "Logs" tab
   - Logs will display in real-time and historical format

### Method 2: Through Cloud Logging

1. **Open Cloud Logging**
   - Navigate to "Logging" > "Logs Explorer" in the Google Cloud Console

2. **Filter for Cloud Run Logs**
   - Use the following query to filter for specific Cloud Run services:
   ```
   resource.type="cloud_run_revision"
   resource.labels.service_name="your-service-name"
   ```

3. **Common Log Queries**
   - Backend API logs:
   ```
   resource.type="cloud_run_revision"
   resource.labels.service_name="a2a-platform-backend"
   ```
   
   - Frontend logs:
   ```
   resource.type="cloud_run_revision"
   resource.labels.service_name="a2a-platform-web"
   ```
   
   - Error logs only:
   ```
   resource.type="cloud_run_revision"
   severity>=ERROR
   ```

### Method 3: Using gcloud CLI

1. **Install and Configure gcloud**
   ```bash
   # Install gcloud CLI (if not already installed)
   # Follow instructions at: https://cloud.google.com/sdk/docs/install
   
   # Authenticate with your vedavivi.com account
   gcloud auth login
   
   # Set the project
   gcloud config set project your-project-id
   ```

2. **View Cloud Run Logs**
   ```bash
   # View logs for a specific service
   gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=a2a-platform-backend" --limit=50
   
   # Follow logs in real-time
   gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=a2a-platform-backend" --follow
   
   # View recent error logs
   gcloud logging read "resource.type=cloud_run_revision AND severity>=ERROR" --limit=20
   ```

## Troubleshooting

### Access Issues
- **Error: "You don't have permission"**
  - Verify you're signed in with your `@vedavivi.com` email
  - Contact the administrator to verify your IAM permissions
  - Ensure you've selected the correct GCP project

### Log Viewing Issues
- **No logs appearing**
  - Check if the service is currently running and receiving traffic
  - Verify your log filters and time range
  - Ensure you have the `Logging Viewer` role

- **Incomplete log data**
  - Some logs may be delayed; try refreshing after a few minutes
  - Check if log retention policies are affecting older logs
  - Verify the service is writing logs to stdout/stderr (Cloud Run standard)

## Security Notes

- Never share your GCP credentials or access tokens
- Use your vedavivi.com email exclusively for platform access
- Report any suspicious activity or unauthorized access attempts
- Follow the principle of least privilege - only request the minimum permissions needed

## Additional Resources

- [Google Cloud Run Documentation](https://cloud.google.com/run/docs)
- [Cloud Logging Documentation](https://cloud.google.com/logging/docs)
- [gcloud CLI Reference](https://cloud.google.com/sdk/gcloud/reference)