# Testing Documentation

This directory contains documentation for the A2A Platform testing infrastructure.

## Quick Start

### Three-Tier Testing Strategy

The A2A Platform uses a three-tier testing approach for optimal speed and coverage:

```bash
# Tier 1: Database-Free Tests (fastest - 0.38s for 52 tests)
TEST_DATABASE_MODE=no_db python -m pytest -m "no_db"

# Tier 2: SQLite Tests (fast integration - ~2-5s)
TEST_DATABASE_MODE=sqlite python -m pytest -m "fast_db"

# Tier 3: PostgreSQL Tests (full integration - ~30-60s)
python -m pytest  # default mode
```

### Test Markers

- `@pytest.mark.no_db` - Pure business logic, no database
- `@pytest.mark.fast_db` - SQLite-compatible integration tests
- No marker - Full PostgreSQL integration tests

### Environment Variables

```bash
export TEST_DATABASE_MODE=no_db    # or sqlite, postgresql
export REDIS_URL=redis://localhost:6379
```

## Current Status

| Test Tier | Status | Test Count | Execution Time |
|-----------|--------|------------|----------------|
| Database-Free (`no_db`) | ✅ Working | 52 tests | 0.38s |
| SQLite (`fast_db`) | ⚠️ Partial | 42 tests | ~2-5s |
| PostgreSQL (default) | 🔄 Needs verification | All tests | ~30-60s |

### Recent Achievements

- ✅ **Complete database-free testing** with comprehensive mocking
- ✅ **Cross-database JSON compatibility** with PortableJSON implementation
- ✅ **Lazy database initialization** preventing import-time connections
- ✅ **MyPy type safety** maintained throughout
- ✅ **CI/CD integration** with proper environment configuration

### Known Issues

- ⚠️ SQLite UUID compatibility (uses strings instead of native UUIDs)
- ⚠️ SQLite foreign key constraint enforcement
- ⚠️ Timezone-aware datetime comparisons in SQLite

## Key Files

### Backend Testing
- [`three-tier-testing-strategy.md`](./three-tier-testing-strategy.md) - Complete backend testing implementation guide
- [`implementation-guide.md`](./implementation-guide.md) - Technical implementation details
- [`troubleshooting.md`](./troubleshooting.md) - Common issues and solutions
- [`apps/backend/tests/conftest.py`](../../apps/backend/tests/conftest.py) - Test configuration and fixtures
- [`apps/backend/src/a2a_platform/db/types.py`](../../apps/backend/src/a2a_platform/db/types.py) - Cross-database compatibility types
- [`apps/backend/pyproject.toml`](../../apps/backend/pyproject.toml) - Test markers and configuration

### Frontend Testing
- [`frontend-testing.md`](./frontend-testing.md) - Cypress end-to-end testing guide
- [`apps/web/cypress/`](../../apps/web/cypress/) - Cypress test files and configuration

## Development Workflow

1. **Write business logic tests** with `@pytest.mark.no_db`
2. **Add schema tests** with `@pytest.mark.fast_db`
3. **Create integration tests** without markers for PostgreSQL
4. **Run appropriate tier** during development for fast feedback
5. **Run all tiers** before committing

## Code Quality, Linting, and Formatting

Maintaining consistent code style and quality is crucial. We use a combination of Prettier for code formatting and ESLint for code quality and logic checks, primarily for the frontend application (`apps/web`).

### Separation of Concerns

-   **Prettier**: Handles all aspects of code formatting. It ensures a consistent style across the codebase (e.g., line breaks, spacing, indentation, quote style).
-   **ESLint**: Focuses on code quality rules, identifying potential bugs, enforcing best practices, and preventing problematic patterns. It does not handle purely stylistic formatting rules, as that is Prettier's responsibility.

### Recommended Local Workflow (for `apps/web`)

When working on the frontend code in `apps/web`, follow these steps to ensure your code meets our standards before committing:

1.  **Format your code with Prettier**:
    ```bash
    # Navigate to the web app directory
    cd apps/web

    # Run Prettier to format files
    bun run format
    ```
    This command will automatically reformat your `.ts`, `.tsx`, `.js`, and `.jsx` files in the `src` directory.

2.  **Lint your code with ESLint**:
    ```bash
    # Ensure you are in the apps/web directory
    cd apps/web

    # Run ESLint to check for issues and automatically fix them
    bun run lint:fix
    ```
    This command will report any code quality issues and attempt to fix them automatically.

3.  **Combined Fix (Recommended)**:
    To simplify the process, you can use the combined script that runs both formatting and lint fixing sequentially:
    ```bash
    # Ensure you are in the apps/web directory
    cd apps/web

    # Run Prettier formatting then ESLint fixing
    bun run code:fix
    ```

### Automated Checks

-   **Pre-commit Hook**: Before committing your changes, a pre-commit hook is configured to automatically run Prettier and then ESLint on staged files. This helps catch issues early. The hook follows the same Prettier-then-ESLint order.
-   **CI Pipeline**: The Continuous Integration (CI) pipeline also enforces these checks. It first runs a Prettier format check (`bun run format:check`) and then an ESLint lint check (`bun run lint:check`). If either of these checks fails, the build will be marked as failed.

By following this workflow, we ensure code consistency and quality throughout the project.

## Quick Commands

```bash
# Development - run fast tests only
TEST_DATABASE_MODE=no_db python -m pytest -m "no_db" -x

# CI - run all tiers
make test-all  # or equivalent script

# Debug database mocking
TEST_DATABASE_MODE=no_db python -c "import a2a_platform.db; print('OK')"

# Check SQLite compatibility
TEST_DATABASE_MODE=sqlite python -c "from a2a_platform.db.types import PortableJSON; print('OK')"
```

## Performance Benefits

The three-tier strategy provides significant performance improvements:

- **Development feedback**: 0.38s vs 30-60s (99% faster)
- **CI pipeline efficiency**: Parallel execution of different tiers
- **Resource optimization**: No database setup for business logic tests
- **Scalable testing**: Add tests to appropriate tier based on requirements

## Next Steps

1. **Complete SQLite compatibility** - UUID and constraint handling
2. **Verify PostgreSQL functionality** - ensure no regressions
3. **Expand test coverage** - classify existing tests into appropriate tiers
4. **Performance optimization** - further reduce execution times
5. **Documentation updates** - add examples and troubleshooting guides

For detailed implementation information, see [three-tier-testing-strategy.md](./three-tier-testing-strategy.md).
