# Frontend Testing Guide

This guide provides an overview of the frontend testing setup for the A2A Platform project.

## Overview

The frontend uses [Cypress](https://www.cypress.io/) for end-to-end testing. Cypress is a powerful tool for testing web applications, providing a rich API for interacting with the DOM and making assertions.

## Setup

To run the frontend tests, you need to have the frontend server running. The tests are configured to run against `https://localhost:5173`, which is the default development server port for the frontend.

### Environment Requirements

For end-to-end tests to run properly, the following environment variables must be set:

```bash
CLERK_PUBLISHABLE_KEY=pk_test_******
CLERK_API_KEY=sk_test_******
```

Additionally, the development environment must be running with all services (frontend, backend, database, Redis) available.

## Running Tests

To run the frontend tests, use the following command:

```bash
# Run all tests
./scripts/run-frontend-tests.sh

# Run only unit tests
./scripts/run-frontend-tests.sh --unit

# Run only end-to-end tests
./scripts/run-frontend-tests.sh --e2e

# Run end-to-end tests and open the Cypress UI
./scripts/run-frontend-tests.sh --e2e-open
```

## Current Test Status

As of the latest investigation (May 2025):

- **Total Tests**: 13
- **Passing**: 11 (85% success rate)
- **Failing**: 2 (15% failure rate)

### Passing Test Suites
- ✅ `debug-chat.cy.ts` - 1/1 passing
- ✅ `auth/signIn.cy.ts` - 7/7 passing
- ✅ `auth/signUp.cy.ts` - 3/3 passing

### Failing Test Suites
- ❌ `chat/send-message.cy.ts` - 2/2 failing (authentication issues)

## Writing Tests

To write a new test, create a new file in the `cypress/e2e` directory. The file should end with the `.cy.ts` extension.

## Known Issues and Troubleshooting

### Chat Test Authentication Failures

#### Root Cause Analysis

The `chat/send-message.cy.ts` test failures are caused by **Clerk authentication not being properly mocked** in Cypress. The application uses Clerk's `<SignedIn>` and `<SignedOut>` components to control route access, which require proper session tokens rather than simple localStorage manipulation.

**Symptoms:**
- Tests redirect to `/pa/sign-in` instead of accessing protected chat routes
- URL assertion failures: `expected 'https://localhost:5173/pa/sign-in' to include '/chat/test-conversation-id'`
- Elements with `data-testid="message-input"` are never found because the chat page never loads

#### Technical Findings

1. **TypeScript Import Issue (Resolved)**:
   - **Problem**: `MessageContent` import errors due to `verbatimModuleSyntax` setting
   - **Solution**: Use type-only imports (`import type { MessageContent } from "./types"`)

2. **Authentication Architecture**:
   - App uses Clerk's `<SignedIn>` and `<SignedOut>` components for route protection
   - These components require proper Clerk session tokens, not just localStorage values
   - The debug test works because it doesn't test functionality, only takes screenshots

3. **Clerk Integration Complexity**:
   - Clerk's authentication system is more complex than simple localStorage manipulation
   - Session state is managed internally by Clerk's React components
   - Standard Cypress mocking approaches don't work with Clerk's authentication flow

#### Solutions Attempted

The following authentication approaches were tried and failed:

1. **localStorage Manipulation**:
   ```javascript
   cy.window().then((win) => {
     win.localStorage.setItem("clerk-user", JSON.stringify({...}));
   });
   ```

2. **Clerk API Interception**:
   ```javascript
   cy.intercept("GET", "https://api.clerk.com/**", {...});
   ```

3. **Clerk Testing Tokens**:
   ```javascript
   setupClerkTestingToken();
   ```

4. **Session Storage Mocking**:
   ```javascript
   win.localStorage.setItem("__clerk_session", JSON.stringify({...}));
   ```

All approaches resulted in the same redirect behavior to `/pa/sign-in`.

#### Recommended Solutions

To properly test authenticated chat functionality, consider these approaches:

1. **Use Clerk's Official Testing Utilities**:
   - Implement proper Clerk testing setup with their recommended testing patterns
   - Research Clerk's official Cypress integration documentation

2. **Component-Level Mocking**:
   - Create test-specific versions of authentication components
   - Mock `<SignedIn>` and `<SignedOut>` components to always show authenticated state

3. **Test-Only Routes**:
   - Create development/test-only routes that bypass authentication
   - Use environment variables to enable these routes only in test environments

4. **Integration Test Approach**:
   - Set up a real test user account
   - Authenticate properly through the actual sign-in flow
   - Use persistent sessions across test runs

### Environment Setup Issues

If tests fail to start, ensure:

1. **Environment Variables**: Both `CLERK_PUBLISHABLE_KEY` and `CLERK_API_KEY` are set
2. **Development Services**: All services are running via `docker compose -f docker-compose.dev.yml up -d`
3. **Frontend Server**: Accessible at `https://localhost:5173`
4. **Backend Server**: Accessible at `https://localhost:8000`

## Testing Pages with Authentication

### Current Working Approach

For authentication-related tests that are currently working (sign-in, sign-up), the tests use Clerk's built-in testing utilities:

```javascript
import { setupClerkTestingToken } from "@clerk/testing/cypress";

describe("Sign In Flow", () => {
  beforeEach(() => {
    setupClerkTestingToken();
    cy.visit("/pa/sign-in");
  });
  // ... test implementation
});
```

### Best Practices

1. **Test Authentication Flows**: Focus on testing the authentication process itself (sign-in, sign-up, sign-out)
2. **Mock Protected Content**: For protected routes, consider testing the authentication boundary rather than the protected content
3. **Use Test Data**: Create predictable test data that doesn't interfere with real user data
4. **Environment Isolation**: Ensure tests run in isolated environments with test-specific configurations

### Test Structure

Organize tests by functionality:

```
cypress/e2e/
├── auth/
│   ├── signIn.cy.ts          ✅ Working
│   ├── signUp.cy.ts          ✅ Working
│   └── authCallback.cy.ts
├── chat/
│   └── send-message.cy.ts    ❌ Authentication issues
└── debug-chat.cy.ts          ✅ Working (no auth required)
```

### Future Improvements

1. **Implement Proper Clerk Testing**: Research and implement Clerk's recommended testing patterns for protected routes
2. **Component Testing**: Consider using Cypress Component Testing for isolated component tests
3. **API Testing**: Test GraphQL mutations and queries independently of the UI
4. **Visual Testing**: Add visual regression testing for UI components

## Debugging Tests

### Common Commands

```bash
# Run specific test file
CLERK_PUBLISHABLE_KEY=pk_test_... CLERK_API_KEY=sk_test_... \
  ./scripts/run-frontend-tests.sh --e2e -- --spec "cypress/e2e/auth/signIn.cy.ts"

# Open Cypress UI for debugging
./scripts/run-frontend-tests.sh --e2e-open

# Run with additional logging
DEBUG=cypress:* ./scripts/run-frontend-tests.sh --e2e
```

### Screenshots and Videos

Cypress automatically captures screenshots on test failures and can record videos of test runs. These are stored in:

- Screenshots: `apps/web/cypress/screenshots/`
- Videos: `apps/web/cypress/videos/` (if enabled)

### Logs and Console Output

Check the browser console in Cypress for application errors and debug information. The tests include console.log statements for debugging authentication flows.
