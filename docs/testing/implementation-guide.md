# Three-Tier Testing Implementation Guide

This guide provides technical details for developers working with the three-tier testing system.

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Database-Free │    │   SQLite Tests  │    │ PostgreSQL Tests│
│   Tests (no_db) │    │   (fast_db)     │    │   (default)     │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • Pure mocking  │    │ • In-memory DB  │    │ • Full database │
│ • 0.38s/52 tests│    │ • PortableJSON  │    │ • All features  │
│ • Business logic│    │ • Basic CRUD    │    │ • Production-like│
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Database Mocking Implementation

### Session-Scoped Mocking (conftest.py)

```python
@pytest.fixture(scope="session", autouse=True)
def mock_database_for_no_db_tests():
    """Session-scoped fixture to mock database connections for database-free tests."""
    test_db_mode = os.environ.get("TEST_DATABASE_MODE", "postgresql")
    
    if test_db_mode == "no_db":
        # Mock both db/__init__.py and db/session.py modules
        with patch("a2a_platform.db.create_async_engine") as mock_create_engine, \
             patch("a2a_platform.db.session.create_async_engine") as mock_session_create_async_engine, \
             patch("a2a_platform.db.session.create_engine") as mock_session_create_engine:
            
            # Return mock engines that don't actually connect
            mock_engine = AsyncMock()
            mock_create_engine.return_value = mock_engine
            mock_session_create_async_engine.return_value = mock_engine
            mock_session_create_engine.return_value = mock_engine
            
            # Mock session factories
            with patch("a2a_platform.db.async_sessionmaker") as mock_sessionmaker, \
                 patch("a2a_platform.db.session.sessionmaker") as mock_session_sessionmaker:
                
                mock_session = AsyncMock()
                mock_sessionmaker.return_value = lambda: mock_session
                mock_session_sessionmaker.return_value = lambda: mock_session
                
                # Mock settings with fake database URL
                with patch("a2a_platform.config.settings.get_settings") as mock_get_settings:
                    mock_settings = MagicMock()
                    mock_settings.ASYNC_DATABASE_URL = "mock://localhost/testdb"
                    mock_settings.DATABASE_URL = "mock://localhost/testdb"
                    mock_settings.SQL_ECHO = False
                    mock_get_settings.return_value = mock_settings
                    
                    yield
    else:
        yield
```

### Lazy Database Initialization

```python
# apps/backend/src/a2a_platform/db/__init__.py
# Lazy initialization to avoid database connections at import time
_engine: Optional[AsyncEngine] = None
_session_factory: Optional[async_sessionmaker[AsyncSession]] = None

def get_engine() -> AsyncEngine:
    """Get or create the async database engine."""
    global _engine
    if _engine is None:
        settings = get_settings()
        _engine = create_async_engine(settings.ASYNC_DATABASE_URL, pool_pre_ping=True)
    return _engine

def get_session_factory() -> async_sessionmaker[AsyncSession]:
    """Get or create the async session factory."""
    global _session_factory
    if _session_factory is None:
        _session_factory = async_sessionmaker(
            bind=get_engine(),
            class_=AsyncSession,
            autocommit=False,
            autoflush=False,
            expire_on_commit=False,
        )
    return _session_factory
```

## Cross-Database Compatibility

### PortableJSON Implementation

```python
# apps/backend/src/a2a_platform/db/types.py
from sqlalchemy import JSON, TypeDecorator
from sqlalchemy.dialects.postgresql import JSONB

class PortableJSON(TypeDecorator):
    """A portable JSON type that uses JSONB for PostgreSQL and JSON for other databases."""
    
    impl = JSON
    cache_ok = True
    
    def load_dialect_impl(self, dialect):
        if dialect.name == 'postgresql':
            return dialect.type_descriptor(JSONB())
        else:
            return dialect.type_descriptor(JSON())
```

### Model Updates

```python
# Before (PostgreSQL-specific)
from sqlalchemy.dialects.postgresql import JSONB

class User(Base):
    preferences: Mapped[Dict[str, Any]] = mapped_column(
        JSONB, nullable=True, default=dict
    )

# After (Cross-database compatible)
from a2a_platform.db.types import PortableJSON

class User(Base):
    preferences: Mapped[Dict[str, Any]] = mapped_column(
        PortableJSON, nullable=True, default=dict
    )
```

## Test Configuration

### Environment-Based Database Selection

```python
# apps/backend/tests/conftest.py
@pytest.fixture(autouse=True)
def mock_settings():
    """Mock the settings for testing."""
    # Determine database URL based on test mode
    test_db_mode = os.environ.get("TEST_DATABASE_MODE", "postgresql")
    if test_db_mode == "sqlite":
        database_url = "sqlite+aiosqlite:///./test.db"
    elif test_db_mode == "no_db":
        database_url = "mock://localhost/testdb"
    else:
        # Default to PostgreSQL
        database_url = f"postgresql+asyncpg://postgres:postgres@{db_host}:5432/a2a_platform_test"
    
    mock_settings_dict = {
        "DATABASE_URL": database_url,
        # ... other settings
    }
```

### Function-Scoped Engine Creation

```python
@pytest.fixture(scope="function")
async def app_async_engine(setup_test_database_once) -> AsyncGenerator[AsyncEngine, None]:
    """Function-scoped fixture that creates a new engine for each test."""
    test_db_mode = os.environ.get("TEST_DATABASE_MODE", "postgresql")
    
    if test_db_mode == "no_db":
        # Return a mock engine for database-free tests
        mock_engine = AsyncMock()
        yield mock_engine
        return
    
    # Get settings with the correct database URL
    settings = get_settings()
    
    if test_db_mode == "sqlite":
        # For SQLite, use an in-memory database for each test
        engine = create_async_engine("sqlite+aiosqlite:///:memory:", echo=settings.SQL_ECHO)
    else:
        # For PostgreSQL, use the configured database URL
        engine = create_async_engine(settings.DATABASE_URL, echo=settings.SQL_ECHO)
    
    # Create tables for this test to ensure isolation
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    # Clean up after the test
    await engine.dispose()
```

## Writing Tests for Each Tier

### Database-Free Tests (no_db)

```python
import pytest
from unittest.mock import AsyncMock, MagicMock

@pytest.mark.no_db
class TestBusinessLogic:
    """Tests for pure business logic without database dependencies."""
    
    async def test_data_processing_algorithm(self):
        """Test data processing without database."""
        # Test pure functions, algorithms, data transformations
        from a2a_platform.services.data_processor import process_data
        
        input_data = {"key": "value"}
        result = process_data(input_data)
        
        assert result["processed"] is True
    
    async def test_api_request_validation(self):
        """Test API request validation logic."""
        from a2a_platform.api.validators import validate_request
        
        request_data = {"user_id": "123", "action": "create"}
        is_valid, errors = validate_request(request_data)
        
        assert is_valid is True
        assert len(errors) == 0
```

### SQLite Tests (fast_db)

```python
import pytest
from sqlalchemy.ext.asyncio import AsyncSession

@pytest.mark.fast_db
class TestDatabaseSchema:
    """Tests for database schema and basic CRUD operations."""
    
    async def test_user_creation(self, db_session_real: AsyncSession):
        """Test basic user creation in SQLite."""
        from a2a_platform.db.models.user import User
        
        user = User(
            clerk_user_id="test_123",
            email="<EMAIL>",
            timezone="UTC"
        )
        
        db_session_real.add(user)
        await db_session_real.commit()
        await db_session_real.refresh(user)
        
        assert user.id is not None
        assert user.email == "<EMAIL>"
    
    async def test_json_column_storage(self, db_session_real: AsyncSession):
        """Test PortableJSON column works in SQLite."""
        from a2a_platform.db.models.user import User
        
        preferences = {"theme": "dark", "language": "en"}
        user = User(
            clerk_user_id="test_456",
            email="<EMAIL>",
            preferences=preferences
        )
        
        db_session_real.add(user)
        await db_session_real.commit()
        await db_session_real.refresh(user)
        
        assert user.preferences == preferences
        assert user.preferences["theme"] == "dark"
```

### PostgreSQL Tests (default)

```python
import pytest
from sqlalchemy.ext.asyncio import AsyncSession

class TestAdvancedDatabaseFeatures:
    """Tests for PostgreSQL-specific features and complex operations."""
    
    async def test_jsonb_indexing_and_queries(self, db_session_real: AsyncSession):
        """Test JSONB indexing and advanced queries."""
        from sqlalchemy import text
        from a2a_platform.db.models.user import User
        
        # Test JSONB operators (PostgreSQL-specific)
        user = User(
            clerk_user_id="test_789",
            email="<EMAIL>",
            preferences={"settings": {"notifications": True, "theme": "dark"}}
        )
        
        db_session_real.add(user)
        await db_session_real.commit()
        
        # Test JSONB containment operator
        result = await db_session_real.execute(
            text("SELECT * FROM users WHERE preferences @> :prefs"),
            {"prefs": '{"settings": {"theme": "dark"}}'}
        )
        
        found_user = result.fetchone()
        assert found_user is not None
    
    async def test_complex_relationships_and_constraints(self, db_session_real: AsyncSession):
        """Test complex database relationships and constraints."""
        # Test foreign key constraints, cascading deletes, etc.
        pass
```

## Debugging and Troubleshooting

### Debug Database Mocking

```python
# Test if database mocking is working
def test_database_mocking_debug():
    import os
    os.environ["TEST_DATABASE_MODE"] = "no_db"
    
    try:
        import a2a_platform.db
        print("✅ Database import successful with mocking")
    except Exception as e:
        print(f"❌ Database import failed: {e}")
```

### Verify Cross-Database Compatibility

```python
# Test PortableJSON type selection
def test_portable_json_debug():
    from a2a_platform.db.types import PortableJSON
    from sqlalchemy.dialects import postgresql, sqlite
    
    # Test PostgreSQL dialect
    pg_impl = PortableJSON().load_dialect_impl(postgresql.dialect())
    print(f"PostgreSQL: {type(pg_impl).__name__}")  # Should be JSONB
    
    # Test SQLite dialect  
    sqlite_impl = PortableJSON().load_dialect_impl(sqlite.dialect())
    print(f"SQLite: {type(sqlite_impl).__name__}")  # Should be JSON
```

### Performance Profiling

```bash
# Profile test execution time
time TEST_DATABASE_MODE=no_db python -m pytest -m "no_db" -v
time TEST_DATABASE_MODE=sqlite python -m pytest -m "fast_db" -v
time python -m pytest -v
```

## Common Patterns

### Service Layer Testing

```python
@pytest.mark.no_db
class TestUserService:
    """Test user service business logic."""
    
    async def test_user_validation(self):
        """Test user data validation without database."""
        from a2a_platform.services.user_service import validate_user_data
        
        user_data = {"email": "invalid-email", "timezone": "Invalid/Timezone"}
        is_valid, errors = validate_user_data(user_data)
        
        assert is_valid is False
        assert "email" in errors
        assert "timezone" in errors

@pytest.mark.fast_db  
class TestUserServiceIntegration:
    """Test user service with database integration."""
    
    async def test_user_creation_flow(self, db_session_real: AsyncSession):
        """Test complete user creation flow."""
        from a2a_platform.services.user_service import create_user
        from a2a_platform.schemas.user import UserCreate
        
        user_data = UserCreate(
            clerk_user_id="test_user_123",
            email="<EMAIL>",
            timezone="UTC"
        )
        
        user = await create_user(db_session_real, user_data)
        
        assert user.id is not None
        assert user.email == "<EMAIL>"
```

### API Endpoint Testing

```python
@pytest.mark.no_db
class TestAPIEndpoints:
    """Test API endpoint logic without database."""
    
    async def test_request_parsing(self):
        """Test request parsing and validation."""
        # Test request/response logic, validation, serialization
        pass

class TestAPIIntegration:
    """Test API endpoints with full database integration."""
    
    async def test_complete_api_flow(self, test_client):
        """Test complete API request/response cycle."""
        response = await test_client.post("/api/users", json={
            "clerk_user_id": "test_123",
            "email": "<EMAIL>"
        })
        
        assert response.status_code == 201
        assert response.json()["email"] == "<EMAIL>"
```

## Migration Strategy

### Converting Existing Tests

1. **Identify test type**:
   - Pure business logic → `@pytest.mark.no_db`
   - Basic database operations → `@pytest.mark.fast_db`
   - Complex database features → No marker (PostgreSQL)

2. **Update imports and mocking**:
   ```python
   # Remove manual database mocking
   # Add appropriate test markers
   # Use provided fixtures (db_session_real, etc.)
   ```

3. **Test database compatibility**:
   ```bash
   # Test with SQLite
   TEST_DATABASE_MODE=sqlite python -m pytest path/to/test.py -v
   
   # Test with PostgreSQL
   python -m pytest path/to/test.py -v
   ```

### Gradual Adoption

1. Start with new tests using appropriate markers
2. Convert high-value existing tests (frequently run, slow tests)
3. Gradually migrate remaining tests
4. Monitor performance improvements and adjust strategy

This implementation provides a robust foundation for fast, reliable testing across different database environments while maintaining full PostgreSQL compatibility for production-like testing scenarios.
