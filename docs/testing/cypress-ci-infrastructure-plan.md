# Cypress CI Infrastructure Plan

## 🎯 **Overview**

This document outlines the approach for integrating Cypress end-to-end testing into our GitHub Actions CI pipeline with proper frontend/backend communication setup.

## 🏗️ **Current Architecture Analysis**

### **Frontend Configuration**
- **Framework**: React + Vite + TypeScript
- **Port**: 5173 (development)
- **Build Tool**: Bun
- **GraphQL Client**: Apollo Client
- **Authentication**: Clerk

### **Backend Configuration**
- **Framework**: FastAPI + GraphQL (Strawberry)
- **Port**: 8000
- **Database**: PostgreSQL (5432)
- **Cache**: Redis (6379)
- **Message Queue**: RQ (Redis Queue)

### **Current CI Setup**
- **Parallel Jobs**: Backend tests + Frontend unit tests
- **Services**: PostgreSQL + Redis containers
- **Test Database**: `a2a_platform_test`

## 🔧 **Proposed Cypress Integration Strategy**

### **Option 1: Service Containers Approach (Recommended)**

```yaml
cypress_e2e_tests:
  name: Cypress E2E Tests
  needs: determine_changes
  if: ${{ needs.determine_changes.outputs.frontend_changed == 'true' || needs.determine_changes.outputs.backend_changed == 'true' }}
  runs-on: ubuntu-latest
  timeout-minutes: 30

  services:
    postgres:
      image: postgres:14
      ports:
        - 5432:5432
      env:
        POSTGRES_USER: postgres
        POSTGRES_PASSWORD: postgres
        POSTGRES_DB: a2a_platform_test
      options: >-
        --health-cmd pg_isready
        --health-interval 10s
        --health-timeout 5s
        --health-retries 5

    redis:
      image: redis:alpine
      ports:
        - 6379:6379
      options: >-
        --health-cmd "redis-cli ping"
        --health-interval 10s
        --health-timeout 5s
        --health-retries 5

  env:
    # Backend Configuration
    DATABASE_URL: postgresql+asyncpg://postgres:postgres@localhost:5432/a2a_platform_test
    REDIS_URL: redis://localhost:6379/1
    CORS_ORIGINS: http://localhost:5173,http://127.0.0.1:5173
    
    # Frontend Configuration  
    VITE_GRAPHQL_API_URL: http://localhost:8000/graphql
    VITE_API_URL: http://localhost:8000
    VITE_WS_URL: ws://localhost:8000/ws
    
    # Test Configuration
    ENABLE_TEST_AUTH_BYPASS: true
    AI_RESPONSE_ENABLED: false  # Disable AI for faster, deterministic tests
    
  steps:
    - name: Checkout code
      uses: actions/checkout@v4

    # Backend Setup
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.12.10'

    - name: Install backend dependencies
      working-directory: ./apps/backend
      run: |
        python -m pip install --upgrade pip
        pip install .[dev]

    - name: Setup test database
      working-directory: ./
      run: ./scripts/setup-test-db.sh --ci

    - name: Start backend server
      working-directory: ./apps/backend
      run: |
        uvicorn a2a_platform.main:app --host 0.0.0.0 --port 8000 &
        # Wait for backend to be ready
        timeout 60s bash -c 'until curl -f http://localhost:8000/health; do sleep 2; done'
      env:
        DATABASE_URL: postgresql+asyncpg://postgres:postgres@localhost:5432/a2a_platform_test

    # Frontend Setup
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'

    - name: Install Bun
      uses: oven-sh/setup-bun@v2
      with:
        bun-version: '1.2.14'

    - name: Install frontend dependencies
      working-directory: ./apps/web
      run: bun install --frozen-lockfile

    - name: Build frontend
      working-directory: ./apps/web
      run: bun run build
      env:
        VITE_GRAPHQL_API_URL: http://localhost:8000/graphql

    - name: Start frontend server
      working-directory: ./apps/web
      run: |
        bun run preview --port 5173 --host 0.0.0.0 &
        # Wait for frontend to be ready
        timeout 60s bash -c 'until curl -f http://localhost:5173; do sleep 2; done'

    # Cypress Testing
    - name: Run Cypress tests
      uses: cypress-io/github-action@v6
      with:
        working-directory: ./apps/web
        start: echo "Servers already running"
        wait-on: 'http://localhost:5173, http://localhost:8000/health'
        wait-on-timeout: 120
        browser: chrome
        record: false
        config: baseUrl=http://localhost:5173
      env:
        CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Upload Cypress screenshots
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: cypress-screenshots
        path: apps/web/cypress/screenshots
        retention-days: 7

    - name: Upload Cypress videos
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: cypress-videos
        path: apps/web/cypress/videos
        retention-days: 7
```

### **Network Configuration Benefits**

1. **Simplified Communication**: All services run on localhost
2. **No Docker Networking**: Avoids container-to-container networking complexity
3. **Standard Ports**: Uses default development ports
4. **Fast Startup**: No container build time
5. **Easy Debugging**: Standard localhost URLs for troubleshooting

### **Environment Variable Strategy**

```yaml
env:
  # Dynamic CORS for different CI environments
  CORS_ORIGINS: >-
    ${{ 
      github.event_name == 'pull_request' && 
      'http://localhost:5173,http://127.0.0.1:5173,https://localhost:5173' || 
      'http://localhost:5173' 
    }}
  
  # Backend URLs for frontend
  VITE_GRAPHQL_API_URL: http://localhost:8000/graphql
  VITE_API_URL: http://localhost:8000
  
  # Test-specific overrides
  ENABLE_TEST_AUTH_BYPASS: true
  AI_RESPONSE_ENABLED: false
```

## 🧪 **Test Strategy Recommendations**

### **Test Categories**

1. **Authentication Flow**
   - Sign up/sign in with Clerk test users
   - JWT token validation
   - Protected route access

2. **Core User Flows**
   - PA setup wizard completion
   - Message sending/receiving
   - Conversation management

3. **GraphQL Integration**
   - Query execution
   - Mutation success/failure
   - Subscription handling (WebSocket)

4. **Error Handling**
   - Network failures
   - Authentication errors
   - Validation errors

### **Test Data Management**

```javascript
// cypress/support/test-data.js
export const testUsers = {
  validUser: {
    email: '<EMAIL>',
    password: 'TestPassword123!'
  }
}

// Reset database before each test
beforeEach(() => {
  cy.task('db:reset')
  cy.task('auth:createTestUser', testUsers.validUser)
})
```

### **Mock Strategy**

1. **AI Responses**: Mock all AI service calls for deterministic testing
2. **External APIs**: Mock Clerk auth in test environment
3. **WebSockets**: Use real WebSocket connections for integration testing

## 🚀 **Implementation Phases**

### **Phase 1: Basic Integration (Week 1)**
- [ ] Add Cypress job to CI pipeline
- [ ] Configure service containers
- [ ] Set up basic health checks
- [ ] Test simple page loads

### **Phase 2: Authentication Tests (Week 2)**
- [ ] Configure Clerk test environment
- [ ] Add authentication flow tests
- [ ] Test protected routes

### **Phase 3: E2E Feature Tests (Week 3-4)**
- [ ] PA setup wizard tests
- [ ] Message flow tests
- [ ] Error handling tests
- [ ] Performance monitoring

## 🔍 **Monitoring & Reporting**

### **Test Results**
- **Screenshots**: Capture on failure for debugging
- **Videos**: Record all test runs
- **Performance**: Monitor test execution time
- **Coverage**: Track E2E test coverage

### **CI Integration**
- **Parallel Execution**: Run with existing test jobs
- **Fast Failure**: Fail fast on critical path issues
- **Artifact Storage**: 7-day retention for debugging

## 📋 **Review Checklist for @adslaton**

### **Technical Review Points**
- [ ] Service container configuration approach
- [ ] CORS configuration for CI environment
- [ ] Environment variable management
- [ ] Test data reset strategy
- [ ] Mock vs real service balance

### **Infrastructure Review Points**
- [ ] CI pipeline performance impact
- [ ] Resource usage (CPU/memory)
- [ ] Test execution time targets
- [ ] Failure handling and debugging
- [ ] Artifact management strategy

### **Security Review Points**
- [ ] Test authentication bypass safety
- [ ] Test data exposure concerns
- [ ] Secret management for Cypress
- [ ] Network security in CI environment

## 💡 **Alternative Approaches Considered**

### **Option 2: Docker Compose in CI**
- **Pros**: Matches local development exactly
- **Cons**: Slower startup, more complex networking
- **Use Case**: If service containers prove insufficient

### **Option 3: Separate E2E Pipeline**
- **Pros**: Isolated from main CI, can run less frequently
- **Cons**: Delayed feedback, more complex branching logic
- **Use Case**: If E2E tests are too slow for main pipeline

## 🎯 **Success Metrics**

1. **Test Reliability**: >95% pass rate on main branch
2. **Execution Time**: E2E tests complete in <10 minutes
3. **Coverage**: Core user flows covered with E2E tests
4. **Developer Experience**: Easy to run locally and debug failures
5. **CI Performance**: <5 minute impact on total CI time

This plan provides a solid foundation for Cypress integration while maintaining the existing CI pipeline's performance and reliability.