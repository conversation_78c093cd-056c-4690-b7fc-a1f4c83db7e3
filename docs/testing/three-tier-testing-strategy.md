# Three-Tier Testing Strategy

This document describes the implementation of a three-tier testing strategy for the A2A Platform backend, designed to optimize test execution speed while maintaining comprehensive coverage.

## Overview

The three-tier testing strategy provides different levels of database integration:

1. **Database-Free Tests (`no_db`)** - Pure business logic tests with complete database mocking
2. **SQLite Tests (`fast_db`)** - Fast integration tests using in-memory SQLite database
3. **PostgreSQL Tests (default)** - Full integration tests using PostgreSQL database

## Test Tiers

### Tier 1: Database-Free Tests (`no_db`)

**Purpose**: Test pure business logic without any database dependencies.

**Configuration**:
```bash
TEST_DATABASE_MODE=no_db python -m pytest -m "no_db"
```

**Characteristics**:
- ✅ **Extremely fast execution** (52 tests in 0.38s)
- ✅ **Complete database mocking** - no real database connections
- ✅ **Pure business logic testing** - validates algorithms, data processing, API logic
- ✅ **CI/CD friendly** - no external dependencies

**Test Markers**:
```python
@pytest.mark.no_db
class TestBusinessLogic:
    def test_algorithm_logic(self):
        # Test pure business logic
        pass
```

**Implementation Details**:
- All SQLAlchemy engines and sessions are mocked
- Database operations return mock objects
- Settings use mock database URLs
- No actual database connections are established

### Tier 2: SQLite Tests (`fast_db`)

**Purpose**: Fast integration tests using SQLite for basic CRUD operations and schema validation.

**Configuration**:
```bash
TEST_DATABASE_MODE=sqlite python -m pytest -m "fast_db"
```

**Characteristics**:
- ⚡ **Fast execution** - uses in-memory SQLite database
- 🔄 **Basic integration testing** - validates database schema and simple operations
- ✅ **Cross-database compatibility** - tests database-agnostic code
- ⚠️ **Limited feature set** - SQLite doesn't support all PostgreSQL features

**Test Markers**:
```python
@pytest.mark.fast_db
class TestDatabaseSchema:
    def test_table_creation(self):
        # Test basic database operations
        pass
```

**Current Status**:
- ✅ JSON column compatibility (PortableJSON implementation)
- ✅ Basic table creation and schema validation
- ⚠️ UUID type compatibility issues (SQLite uses strings)
- ⚠️ Foreign key constraint enforcement
- ⚠️ Timezone-aware datetime handling

### Tier 3: PostgreSQL Tests (default)

**Purpose**: Full integration tests using PostgreSQL with all advanced features.

**Configuration**:
```bash
# Default mode - no environment variable needed
python -m pytest
# Or explicitly:
TEST_DATABASE_MODE=postgresql python -m pytest
```

**Characteristics**:
- 🔧 **Full feature testing** - all PostgreSQL features (JSONB, UUIDs, constraints)
- 🐘 **Production-like environment** - matches production database
- 🐌 **Slower execution** - requires database setup and teardown
- 💯 **Complete integration testing** - end-to-end database operations

**Features**:
- Full JSONB support with indexing and advanced queries
- Native UUID type support
- Advanced constraint validation
- Complex relationship testing
- Performance testing with realistic data volumes

## Cross-Database Compatibility

### PortableJSON Implementation

To support both PostgreSQL and SQLite, we implemented a `PortableJSON` type that automatically selects the appropriate JSON column type:

```python
# apps/backend/src/a2a_platform/db/types.py
class PortableJSON(TypeDecorator):
    """A portable JSON type that uses JSONB for PostgreSQL and JSON for other databases."""
    
    impl = JSON
    cache_ok = True
    
    def load_dialect_impl(self, dialect):
        if dialect.name == 'postgresql':
            return dialect.type_descriptor(JSONB())
        else:
            return dialect.type_descriptor(JSON())
```

**Usage in Models**:
```python
from a2a_platform.db.types import PortableJSON

class User(Base):
    preferences: Mapped[Dict[str, Any]] = mapped_column(
        PortableJSON, nullable=True, default=dict
    )
```

**Database-Specific Behavior**:
- **PostgreSQL**: Uses `JSONB` for better performance and indexing
- **SQLite**: Uses `JSON` for compatibility
- **Other databases**: Falls back to standard `JSON` type

## Test Configuration

### Environment Variables

| Variable | Values | Description |
|----------|--------|-------------|
| `TEST_DATABASE_MODE` | `no_db`, `sqlite`, `postgresql` | Controls which testing tier to use |
| `REDIS_URL` | `redis://localhost:6379` | Required for all test modes |

### Pytest Markers

```python
# pyproject.toml
[tool.pytest.ini_options]
markers = [
    "no_db: marks tests as database-free (pure business logic)",
    "fast_db: marks tests as SQLite-compatible (basic CRUD operations)",
    "slow: marks tests as requiring full PostgreSQL (complex operations)",
]
```

### Test Execution Examples

```bash
# Run only database-free tests (fastest)
TEST_DATABASE_MODE=no_db python -m pytest -m "no_db"

# Run only SQLite tests (fast integration)
TEST_DATABASE_MODE=sqlite python -m pytest -m "fast_db"

# Run only PostgreSQL tests (full integration)
python -m pytest -m "not no_db and not fast_db"

# Run all tests in order (comprehensive)
TEST_DATABASE_MODE=no_db python -m pytest -m "no_db" && \
TEST_DATABASE_MODE=sqlite python -m pytest -m "fast_db" && \
python -m pytest -m "not no_db and not fast_db"
```

## Implementation Details

### Database Mocking (no_db mode)

The database-free testing implementation includes:

1. **Session-scoped mocking** in `conftest.py`
2. **Engine creation mocking** for both `db/__init__.py` and `db/session.py`
3. **Settings mocking** with mock database URLs
4. **Comprehensive fixture isolation**

### SQLite Configuration (fast_db mode)

SQLite tests use:

1. **In-memory database** (`sqlite+aiosqlite:///:memory:`)
2. **Function-scoped engines** for test isolation
3. **Skipped database setup scripts**
4. **PortableJSON for cross-database compatibility**

### PostgreSQL Configuration (default mode)

PostgreSQL tests maintain:

1. **Full database setup** with migration scripts
2. **Transaction isolation** between tests
3. **Complete feature support** (JSONB, UUIDs, constraints)
4. **Production-like testing environment**

## Performance Comparison

| Test Tier | Execution Time | Test Count | Database | Use Case |
|-----------|---------------|------------|----------|----------|
| Database-Free | 0.38s | 52 tests | None (mocked) | Business logic validation |
| SQLite | ~2-5s | 42 tests | In-memory SQLite | Schema and basic CRUD |
| PostgreSQL | ~30-60s | All tests | Full PostgreSQL | Complete integration |

## CI/CD Integration

### GitHub Actions Workflow

```yaml
jobs:
  database_free_tests:
    runs-on: ubuntu-latest
    env:
      TEST_DATABASE_MODE: no_db
      REDIS_URL: redis://localhost:6379
    steps:
      - name: Run Database-Free Tests
        run: python -m pytest -m "no_db"

  sqlite_tests:
    runs-on: ubuntu-latest
    env:
      TEST_DATABASE_MODE: sqlite
      REDIS_URL: redis://localhost:6379
    steps:
      - name: Run SQLite Tests
        run: python -m pytest -m "fast_db"

  postgresql_tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
    steps:
      - name: Run PostgreSQL Tests
        run: python -m pytest
```

## Best Practices

### Test Classification Guidelines

**Use `@pytest.mark.no_db` for**:
- Pure business logic functions
- Data validation and transformation
- Algorithm implementations
- API request/response processing
- Unit tests that don't require database state

**Use `@pytest.mark.fast_db` for**:
- Database schema validation
- Basic CRUD operations
- Simple relationship testing
- Cross-database compatibility validation
- Integration tests with minimal database complexity

**Use default (PostgreSQL) for**:
- Complex database operations
- Advanced PostgreSQL features (JSONB queries, advanced constraints)
- Performance testing with realistic data
- End-to-end integration tests
- Tests requiring full production-like environment

### Development Workflow

1. **Start with `no_db` tests** for new business logic
2. **Add `fast_db` tests** for database schema changes
3. **Use PostgreSQL tests** for complex integration scenarios
4. **Run appropriate tier** during development for fast feedback
5. **Run all tiers** before committing changes

## Known Limitations

### SQLite Compatibility Issues

1. **UUID Support**: SQLite stores UUIDs as strings, not native UUID type
2. **Foreign Key Constraints**: May not be enforced by default
3. **Timezone Handling**: Limited timezone-aware datetime support
4. **Advanced Features**: No support for PostgreSQL-specific features

### Future Improvements

1. **UUID Compatibility Layer**: Implement automatic UUID ↔ string conversion
2. **Enhanced SQLite Configuration**: Enable foreign key constraints
3. **Timezone Normalization**: Standardize datetime handling across databases
4. **Performance Optimization**: Further optimize test execution times
5. **Additional Database Support**: Extend compatibility to other databases

## Troubleshooting

### Common Issues

**Database-free tests connecting to database**:
- Ensure `TEST_DATABASE_MODE=no_db` is set
- Check that all database imports are properly mocked
- Verify fixture isolation in test setup

**SQLite tests failing with JSONB errors**:
- Ensure models use `PortableJSON` instead of `JSONB`
- Check that `aiosqlite` dependency is installed
- Verify SQLite-specific configuration is applied

**PostgreSQL tests hanging**:
- Check database connection configuration
- Verify PostgreSQL service is running
- Ensure proper database setup scripts execution

### Debug Commands

```bash
# Test database connection mocking
TEST_DATABASE_MODE=no_db python -c "import a2a_platform.db; print('Mocking working')"

# Verify SQLite compatibility
TEST_DATABASE_MODE=sqlite python -c "from a2a_platform.db.types import PortableJSON; print('PortableJSON available')"

# Check PostgreSQL connection
python -c "from a2a_platform.db import get_engine; print('PostgreSQL connection OK')"
```

## Related Documentation

- [Database Architecture](../database/architecture.md)
- [Testing Guidelines](../development/testing-guidelines.md)
- [CI/CD Pipeline](../deployment/ci-cd-pipeline.md)
- [Development Setup](../development/setup.md)
