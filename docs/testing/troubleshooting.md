# Testing Troubleshooting Guide

This guide helps resolve common issues with the three-tier testing system.

## Quick Diagnostics

### Test Current Configuration

```bash
# Check which test mode is active
echo "TEST_DATABASE_MODE: ${TEST_DATABASE_MODE:-postgresql}"

# Test database-free mode
TEST_DATABASE_MODE=no_db python -c "
import os
print(f'Mode: {os.environ.get(\"TEST_DATABASE_MODE\", \"postgresql\")}')
try:
    import a2a_platform.db
    print('✅ Database import successful')
except Exception as e:
    print(f'❌ Database import failed: {e}')
"

# Test SQLite mode
TEST_DATABASE_MODE=sqlite python -c "
from a2a_platform.db.types import PortableJSON
from sqlalchemy.dialects import sqlite
impl = PortableJSON().load_dialect_impl(sqlite.dialect())
print(f'✅ SQLite JSON type: {type(impl).__name__}')
"

# Test PostgreSQL mode
python -c "
from a2a_platform.db.types import PortableJSON
from sqlalchemy.dialects import postgresql
impl = PortableJSON().load_dialect_impl(postgresql.dialect())
print(f'✅ PostgreSQL JSON type: {type(impl).__name__}')
"
```

## Common Issues and Solutions

### Database-Free Tests (no_db)

#### Issue: Tests still connecting to database

**Symptoms**:
```
sqlalchemy.exc.OperationalError: could not connect to server
```

**Solutions**:

1. **Check environment variable**:
   ```bash
   export TEST_DATABASE_MODE=no_db
   python -m pytest -m "no_db" -v
   ```

2. **Verify mocking is active**:
   ```python
   # Add debug print to conftest.py
   print(f"Test mode: {os.environ.get('TEST_DATABASE_MODE', 'postgresql')}")
   ```

3. **Check import order**:
   ```python
   # Ensure database modules are imported after mocking is set up
   # Move database imports inside test functions if needed
   ```

#### Issue: Mock objects not behaving correctly

**Symptoms**:
```
AttributeError: 'AsyncMock' object has no attribute 'some_method'
```

**Solutions**:

1. **Configure mock properly**:
   ```python
   mock_session = AsyncMock()
   mock_session.execute.return_value = AsyncMock()
   mock_session.execute.return_value.scalar_one_or_none.return_value = None
   ```

2. **Use spec parameter**:
   ```python
   mock_session = AsyncMock(spec=AsyncSession)
   ```

### SQLite Tests (fast_db)

#### Issue: JSONB not supported error

**Symptoms**:
```
sqlalchemy.exc.UnsupportedCompilationError: Compiler can't render element of type JSONB
```

**Solutions**:

1. **Update model to use PortableJSON**:
   ```python
   # Before
   from sqlalchemy.dialects.postgresql import JSONB
   preferences: Mapped[Dict[str, Any]] = mapped_column(JSONB, ...)
   
   # After
   from a2a_platform.db.types import PortableJSON
   preferences: Mapped[Dict[str, Any]] = mapped_column(PortableJSON, ...)
   ```

2. **Check all models for JSONB usage**:
   ```bash
   grep -r "JSONB" src/a2a_platform/db/models/
   ```

#### Issue: UUID type not supported

**Symptoms**:
```
sqlite3.ProgrammingError: Error binding parameter 1: type 'UUID' is not supported
```

**Current Status**: Known limitation - SQLite doesn't support native UUID types.

**Workarounds**:
1. Use string representations in SQLite tests
2. Skip UUID-specific tests in SQLite mode
3. Implement UUID ↔ string conversion layer (future enhancement)

#### Issue: Foreign key constraints not enforced

**Symptoms**: Tests pass in SQLite but should fail due to constraint violations.

**Solutions**:

1. **Enable foreign key support**:
   ```python
   # In conftest.py for SQLite engine creation
   engine = create_async_engine(
       "sqlite+aiosqlite:///:memory:",
       connect_args={"check_same_thread": False},
       echo=settings.SQL_ECHO
   )
   
   # Enable foreign keys
   @event.listens_for(engine.sync_engine, "connect")
   def set_sqlite_pragma(dbapi_connection, connection_record):
       cursor = dbapi_connection.cursor()
       cursor.execute("PRAGMA foreign_keys=ON")
       cursor.close()
   ```

#### Issue: Timezone-aware datetime comparisons

**Symptoms**:
```
TypeError: can't compare offset-naive and offset-aware datetimes
```

**Solutions**:

1. **Normalize datetime objects**:
   ```python
   from datetime import timezone
   
   # Ensure all datetimes are timezone-aware
   before_creation = datetime.now(timezone.utc)
   ```

2. **Use UTC consistently**:
   ```python
   from datetime import UTC
   
   timestamp = datetime.now(UTC)
   ```

### PostgreSQL Tests (default)

#### Issue: Tests hanging during database setup

**Symptoms**: Tests never start, hang during setup phase.

**Solutions**:

1. **Check database connection**:
   ```bash
   # Test direct connection
   psql -h localhost -U postgres -d a2a_platform_test -c "SELECT 1;"
   ```

2. **Verify database exists**:
   ```bash
   # Run setup script manually
   ./scripts/setup-test-db.sh
   ```

3. **Check Docker services**:
   ```bash
   docker ps | grep postgres
   docker logs <postgres_container_id>
   ```

#### Issue: Database migration errors

**Symptoms**:
```
alembic.util.exc.CommandError: Can't locate revision identified by 'xyz'
```

**Solutions**:

1. **Reset database**:
   ```bash
   ./scripts/setup-test-db.sh --reset
   ```

2. **Check migration files**:
   ```bash
   ls -la alembic/versions/
   ```

## Performance Issues

### Slow Test Execution

**Diagnosis**:
```bash
# Profile test execution
time TEST_DATABASE_MODE=no_db python -m pytest -m "no_db" --tb=no -q
time TEST_DATABASE_MODE=sqlite python -m pytest -m "fast_db" --tb=no -q
time python -m pytest --tb=no -q
```

**Solutions**:

1. **Use appropriate test tier**:
   - Business logic → `no_db`
   - Basic CRUD → `fast_db`
   - Complex operations → PostgreSQL

2. **Optimize database setup**:
   ```python
   # Use session-scoped fixtures for expensive setup
   @pytest.fixture(scope="session")
   def expensive_setup():
       # Setup code here
       pass
   ```

3. **Parallel execution**:
   ```bash
   # Run different tiers in parallel
   python -m pytest -m "no_db" & \
   TEST_DATABASE_MODE=sqlite python -m pytest -m "fast_db" & \
   wait
   ```

### Memory Usage

**Issue**: High memory usage during test execution.

**Solutions**:

1. **Use function-scoped engines**:
   ```python
   # Ensure engines are disposed after each test
   await engine.dispose()
   ```

2. **Clear caches**:
   ```python
   # Clear SQLAlchemy metadata cache
   Base.metadata.clear()
   ```

## Environment-Specific Issues

### CI/CD Pipeline

#### Issue: Tests pass locally but fail in CI

**Solutions**:

1. **Check environment variables**:
   ```yaml
   # GitHub Actions
   env:
     TEST_DATABASE_MODE: no_db
     REDIS_URL: redis://localhost:6379
   ```

2. **Verify service dependencies**:
   ```yaml
   services:
     postgres:
       image: postgres:15
       env:
         POSTGRES_PASSWORD: postgres
   ```

3. **Check file permissions**:
   ```bash
   # Ensure test files are executable
   chmod +x scripts/setup-test-db.sh
   ```

### Docker Environment

#### Issue: Database connection refused in Docker

**Solutions**:

1. **Use correct hostnames**:
   ```python
   # Use 'db' hostname in Docker, 'localhost' locally
   db_host = "localhost" if (is_ci or not is_docker) else "db"
   ```

2. **Check network connectivity**:
   ```bash
   # Test from within container
   docker exec -it backend_container ping db
   ```

### Local Development

#### Issue: Conflicting database connections

**Solutions**:

1. **Use separate test database**:
   ```bash
   # Ensure test database is separate from development
   createdb a2a_platform_test
   ```

2. **Clean up connections**:
   ```bash
   # Kill existing connections
   sudo pkill -f postgres
   ```

## Debug Commands

### Test Isolation

```bash
# Test single test in isolation
TEST_DATABASE_MODE=no_db python -m pytest tests/unit/test_specific.py::test_function -v -s

# Test with maximum verbosity
TEST_DATABASE_MODE=sqlite python -m pytest -vvv -s --tb=long

# Test with pdb debugging
python -m pytest --pdb tests/unit/test_specific.py::test_function
```

### Database State Inspection

```bash
# Check SQLite database state
TEST_DATABASE_MODE=sqlite python -c "
import asyncio
from a2a_platform.db import get_engine
from sqlalchemy import text

async def check_tables():
    engine = get_engine()
    async with engine.begin() as conn:
        result = await conn.execute(text('SELECT name FROM sqlite_master WHERE type=\"table\"'))
        tables = result.fetchall()
        print('Tables:', [t[0] for t in tables])

asyncio.run(check_tables())
"

# Check PostgreSQL database state
python -c "
import asyncio
from a2a_platform.db import get_engine
from sqlalchemy import text

async def check_tables():
    engine = get_engine()
    async with engine.begin() as conn:
        result = await conn.execute(text('SELECT tablename FROM pg_tables WHERE schemaname=\"public\"'))
        tables = result.fetchall()
        print('Tables:', [t[0] for t in tables])

asyncio.run(check_tables())
"
```

### Configuration Validation

```bash
# Validate test configuration
python -c "
import os
from a2a_platform.config.settings import get_settings

print('Environment Variables:')
for key in ['TEST_DATABASE_MODE', 'REDIS_URL', 'DATABASE_URL']:
    print(f'  {key}: {os.environ.get(key, \"<not set>\")}')

print('\nSettings:')
settings = get_settings()
print(f'  DATABASE_URL: {settings.DATABASE_URL}')
print(f'  SQL_ECHO: {settings.SQL_ECHO}')
"
```

## Getting Help

### Log Analysis

1. **Enable SQL logging**:
   ```python
   # In test settings
   SQL_ECHO = True
   ```

2. **Add debug prints**:
   ```python
   # In conftest.py
   print(f"Test mode: {test_db_mode}")
   print(f"Database URL: {database_url}")
   ```

3. **Use pytest verbose output**:
   ```bash
   python -m pytest -vvv -s --tb=long
   ```

### Community Resources

- **GitHub Issues**: Report bugs and feature requests
- **Documentation**: Check latest updates in `docs/testing/`
- **Code Examples**: See `tests/` directory for implementation patterns

### Emergency Fixes

If tests are completely broken:

1. **Reset to PostgreSQL only**:
   ```bash
   unset TEST_DATABASE_MODE
   python -m pytest
   ```

2. **Skip problematic tests**:
   ```bash
   python -m pytest -k "not test_problematic_function"
   ```

3. **Use minimal test set**:
   ```bash
   python -m pytest tests/unit/test_simple.py -v
   ```

Remember: The three-tier testing system is designed to be robust, but if you encounter issues, you can always fall back to the traditional PostgreSQL-only testing approach while troubleshooting.
