# 📚 Storybook Guide - A2A Platform

Storybook is the interactive component library and design system for the A2A Platform. It enables developers and designers to build, test, and document UI components in isolation, serving as a living style guide and collaboration tool.

## 🎯 What You Can Do

- **👀 Browse Components**: View all UI components and their variations
- **🎨 Design Review**: Test components in different states (loading, error, success)
- **🔧 Interactive Testing**: Modify component properties in real-time
- **📖 Documentation**: Access auto-generated documentation from TypeScript props
- **📱 Responsive Design**: Test components across different screen sizes
- **🧪 Visual Testing**: Run automated visual regression tests

---

## 🚀 Quick Start

### Simple Start (Recommended)
```bash
# Start Storybook with auto-open browser
./scripts/storybook.sh dev --open
# Accessible at: http://localhost:6006
```

### Alternative Methods
```bash
# Basic development server
./scripts/storybook.sh

# Custom port
./scripts/storybook.sh dev --port 6007

# Docker mode
./scripts/storybook.sh docker

# Development environment with Storybook
./scripts/dev.sh --storybook
```

---

## 🛠️ The Storybook Script

The `./scripts/storybook.sh` script provides comprehensive Storybook management:

### Commands

| Command | Description | Example |
|---------|-------------|---------|
| `dev` (default) | Start development server | `./scripts/storybook.sh dev --open` |
| `build` | Build for production | `./scripts/storybook.sh build` |
| `serve` | Serve built version | `./scripts/storybook.sh serve` |
| `docker` | Run in Docker | `./scripts/storybook.sh docker` |
| `test` | Run component tests | `./scripts/storybook.sh test` |
| `chromatic` | Visual regression tests | `./scripts/storybook.sh chromatic` |

### Options

| Option | Description | Example |
|--------|-------------|---------|
| `--port PORT` | Custom port (default: 6006) | `--port 6007` |
| `--host HOST` | Custom host (default: localhost) | `--host 0.0.0.0` |
| `--open` | Auto-open browser | `dev --open` |
| `--ci` | CI mode (no interactive features) | `build --ci` |
| `--help` | Show help message | `--help` |

### Expected Output
When starting development server:
```
Starting Storybook in development mode...
Port: 6006
Host: localhost

Storybook 9.0.0 for react-vite started
Local: http://localhost:6006/
```

---

## 🧩 Available Components

### 🎨 UI Components
- **Button** - Primary, secondary, destructive variants
- **Card** - Content containers with headers/footers
- **Input** - Form inputs with validation states
- **Badge** - Status indicators and labels
- **Alert** - Success, warning, error messages

### 💬 Chat Components
- **ChatInterface** - Complete chat experience
- **ChatMessage** - Individual message display
- **MessageInput** - Text input with send functionality
- **PAMessage** - Personal assistant message format

### 🔐 Authentication
- **SignInForm/SignUpForm** - User authentication forms
- **SignInPage/SignUpPage** - Complete authentication pages
- **AuthCallbackPage** - OAuth callback handling

### ⚙️ Management & Pages
- **CliTokenManager** - CLI token management interface
- **UserSettingsPage** - User preferences
- **DashboardPage** - Main application dashboard
- **ConversationList** - Chat conversation management

---

## 📝 Development Workflow

### 1. Component-First Development
```bash
# 1. Start Storybook
./scripts/storybook.sh dev --open

# 2. Create component in src/components/
# 3. Write story alongside component
# 4. Develop and test in Storybook
# 5. Integrate into application
```

### 2. Writing Stories

**Basic Story Structure:**
```typescript
import type { Meta, StoryObj } from '@storybook/react';
import { YourComponent } from './YourComponent';

const meta: Meta<typeof YourComponent> = {
  title: 'Components/YourComponent',
  component: YourComponent,
  parameters: { layout: 'centered' },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['primary', 'secondary']
    }
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: { variant: 'primary' }
};

export const Loading: Story = {
  args: { variant: 'primary', loading: true }
};
```

**Story Organization:**
```
src/components/
├── ui/
│   ├── Button.tsx
│   └── Button.stories.tsx
├── chat/
│   ├── ChatMessage.tsx
│   └── ChatMessage.stories.tsx
└── auth/
    ├── SignInForm.tsx
    └── SignInForm.stories.tsx
```

### 3. Best Practices

**Story Naming:**
- **Title**: Use hierarchical naming: `'Components/UI/Button'`
- **Exports**: Descriptive names: `Primary`, `Secondary`, `Loading`, `Error`
- **Files**: Place stories next to components: `Button.stories.tsx`

**Documentation:**
- Use `tags: ['autodocs']` for auto-generated docs
- Add JSDoc comments to component props
- Create stories for all component states
- Include usage examples and edge cases

---

## 🧪 Testing

### Story Testing
```bash
# Run component tests
./scripts/storybook.sh test

# Run specific story tests
bun run test -- --testPathPattern="Button.stories"
```

### Visual Testing with Chromatic
```bash
# Set up Chromatic (requires account)
export CHROMATIC_PROJECT_TOKEN="your_token"

# Run visual regression tests
./scripts/storybook.sh chromatic
```

### Interaction Testing
```typescript
import { userEvent, within, expect } from '@storybook/test';

export const Interactive: Story = {
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const button = canvas.getByRole('button');
    await userEvent.click(button);
    await expect(button).toHaveClass('active');
  },
};
```

---

## 🐳 Docker Integration

Storybook is integrated with the Docker development environment:

```bash
# Start only Storybook
./scripts/dev.sh --storybook

# Start full environment with Storybook
./scripts/dev.sh --with-storybook

# Check service status
./scripts/dev.sh status
```

**Docker Services:**
- **storybook**: Port 6006
- **frontend**: Port 5173 (main app)
- **backend**: Port 8000 (API)

---

## 🔧 Configuration

### Current Setup
- **Framework**: React + Vite
- **Stories**: Auto-discovers `*.stories.*` files
- **Addons**:
  - Essentials (controls, actions, docs)
  - Interactions (user interaction testing)
  - Chromatic (visual testing)
  - Test addon (experimental)

### Configuration Files
- **Main config**: `.storybook/main.ts`
- **Preview config**: `.storybook/preview.ts`
- **Styling**: Automatically includes Tailwind CSS
- **Test setup**: `.storybook/vitest.setup.ts`

### Preview Settings
- **Global styles**: Tailwind CSS imported
- **Backgrounds**: Light/dark theme options
- **Viewports**: Mobile, tablet, desktop presets
- **Controls**: Auto-detection for colors and dates

---

## 🚨 Troubleshooting

### Common Issues

**Port Already in Use:**
```bash
# Use different port
./scripts/storybook.sh dev --port 6007
```

**Storybook Won't Start:**
```bash
# Check bun installation
which bun

# Install if missing
curl -fsSL https://bun.sh/install | bash

# Clear cache and restart
rm -rf apps/web/node_modules/.cache
./scripts/storybook.sh dev
```

**Components Not Loading:**
- Verify CSS imports in `.storybook/preview.ts`
- Check Tailwind config includes Storybook paths
- Ensure story files follow `*.stories.tsx` naming

**Docker Issues:**
```bash
# Rebuild containers
./scripts/dev.sh --storybook --build

# Check logs
docker compose -f docker-compose.dev.yml logs storybook

# Restart services
docker compose -f docker-compose.dev.yml restart storybook
```

**Stories Not Appearing:**
- Check file naming: Must end with `.stories.tsx`
- Verify story export structure
- Check browser console for TypeScript errors

### Performance Optimization

**Slow Startup:**
- Normal startup time: 1-3 seconds
- Large projects take longer
- Close other applications if low on memory

**Slow Hot Reload:**
- Organize stories into smaller groups
- Break down complex components

---

## 🔗 Access & URLs

### Development
- **Local**: http://localhost:6006
- **Docker**: http://localhost:6006
- **Frontend App**: http://localhost:5173
- **Backend API**: http://localhost:8000

### Production Build
```bash
# Build static files
./scripts/storybook.sh build
# Output: apps/web/storybook-static/

# Serve locally
./scripts/storybook.sh serve
# Access: http://localhost:6006
```

### CI/CD Artifacts

**Automated Builds:** Storybook is automatically built and uploaded as artifacts on every commit to main branch.

**Available Artifacts:**
- **Dedicated Workflow**: `storybook-static-{commit-sha}` (30 days retention)
- **Latest Build**: `storybook-latest` (7 days retention) 
- **CI Pipeline**: `storybook-ci-{commit-sha}` (30 days retention)

**Download & Serve Artifacts:**
```bash
# Easy way - download and serve latest build
./scripts/download-storybook.sh latest --open

# Download specific commit
./scripts/download-storybook.sh commit abc1234

# List available artifacts
./scripts/download-storybook.sh list

# Serve existing download
./scripts/download-storybook.sh serve --port 3000
```

**Manual Download (GitHub CLI):**
```bash
# Download latest
gh run download --name storybook-latest

# Extract and serve
tar -xzf storybook-bundle.tar.gz
bunx serve .
```

**Artifact Contents:**
- Complete static Storybook build
- `build-info.json` with metadata (commit, build time, stats)
- Ready-to-deploy bundle

---

## 📁 Project Structure

```
apps/web/
├── .storybook/                 # Configuration
│   ├── main.ts                # Main config
│   ├── preview.ts             # Global settings
│   └── vitest.setup.ts        # Test setup
├── src/
│   ├── components/            # Components with stories
│   │   ├── ui/                # Basic UI components
│   │   ├── chat/              # Chat components
│   │   ├── auth/              # Authentication
│   │   └── dashboard/         # Dashboard components
│   ├── pages/                 # Page-level components
│   └── stories/               # Templates & examples
└── storybook-static/          # Built output
```

---

## 👥 Team Collaboration

### For Designers
1. **Review Components**: Browse component library and variants
2. **Test Interactions**: Use Controls panel to modify properties
3. **Check Responsiveness**: Test across different viewports
4. **Provide Feedback**: Share specific component URLs
5. **Document Requirements**: Request missing states or variants

### For Engineers
1. **Develop in Isolation**: Build components before integration
2. **Document Components**: Write comprehensive stories
3. **Test All States**: Cover loading, error, empty states
4. **Maintain Consistency**: Follow established patterns
5. **Collaborate**: Share Storybook URLs for reviews

### Sharing & Feedback
- **Component URLs**: `http://localhost:6006/?path=/story/components-ui-button--primary`
- **Screenshots**: Use browser tools or Storybook features
- **Version Control**: Commit story files with component changes
- **Regular Reviews**: Use Storybook as design system source of truth

---

## 📦 Alternative Package Scripts

If you prefer direct package.json scripts:

```bash
# Development
bun run storybook

# Build
bun run build-storybook

# Test
bun run test-storybook
```

**💡 Recommendation**: Use `./scripts/storybook.sh` for the best experience with additional features and error handling.

---

## 📚 Additional Resources

- **Component Templates**: `src/stories/ComponentTemplate.stories.tsx`
- **Official Documentation**: [Storybook.js.org](https://storybook.js.org/docs)
- **Existing Examples**: Browse `src/components/` for story patterns
- **Script Help**: `./scripts/storybook.sh --help`

## 🎉 Quick Reference

**Most Common Commands:**
```bash
# Start development with browser
./scripts/storybook.sh dev --open

# Build for production
./scripts/storybook.sh build

# Start with Docker
./scripts/dev.sh --storybook
```

**Key URLs:**
- Development: http://localhost:6006
- Production build: http://localhost:6006 (after serve)

**CI/CD Artifacts:**
```bash
# Easy download and serve
./scripts/download-storybook.sh latest --open

# Download specific commit  
./scripts/download-storybook.sh commit {commit-sha}
```

Storybook serves as your living design system and component development environment. Happy building! 🎨✨