# Infrastructure Deployment Control

This document explains how to control which infrastructure resources are created vs. using existing resources during Terraform deployments.

## Overview

To prevent "resource already exists" errors and provide fine-grained control over infrastructure deployment, we've implemented environment variables that control resource creation for each module.

## Environment Variables

### Core Infrastructure Resources

| Variable | Description | Default | Controls |
|----------|-------------|---------|----------|
| `TF_VAR_create_network_resources` | Create VPC and subnets | `false` | Network module (VPC, subnets, firewall rules) |
| `TF_VAR_create_database_resources` | Create Cloud SQL instances | `false` | Database module (Cloud SQL, users, databases) |
| `TF_VAR_create_redis_resources` | Create Redis/Memorystore instances | `false` | Redis module (Memorystore instances) |
| `TF_VAR_create_storage_resources` | Create KMS keys and storage buckets | `false` | Storage module (KMS keyrings, keys, buckets) |

### CDN and DNS Resources

| Variable | Description | Default | Controls |
|----------|-------------|---------|----------|
| `TF_VAR_create_dns_records` | Create DNS records | `false` | Cloudflare DNS records |
| `TF_VAR_create_page_rules` | Create Cloudflare page rules | `false` | Cloudflare page rules and caching |

## Usage Examples

### 1. Deploy Only New Resources (First Time Setup)

```bash
# Set all creation flags to true for initial deployment
export TF_VAR_create_network_resources=true
export TF_VAR_create_database_resources=true
export TF_VAR_create_redis_resources=true
export TF_VAR_create_storage_resources=true
export TF_VAR_create_dns_records=true
export TF_VAR_create_page_rules=true

# Run deployment
gh workflow run deploy-infra.yml --ref main -f environment=staging
```

### 2. Use Existing Infrastructure (Default Behavior)

```bash
# All variables default to false, so no exports needed
# This will use existing resources via data sources

gh workflow run deploy-infra.yml --ref main -f environment=staging
```

### 3. Selective Resource Creation

```bash
# Only create DNS records and page rules, use existing infrastructure
export TF_VAR_create_dns_records=true
export TF_VAR_create_page_rules=true

gh workflow run deploy-infra.yml --ref main -f environment=staging
```

### 4. Add New Infrastructure Components

```bash
# Add Redis to existing setup
export TF_VAR_create_redis_resources=true

gh workflow run deploy-infra.yml --ref main -f environment=staging
```

## GitHub Actions Integration

The deployment workflow automatically:

1. **Sets default values** for all variables to `false` if not provided
2. **Displays configuration** showing which resources will be created
3. **Passes variables** to both Terraform plan and apply steps

### Workflow Output Example

```
🔧 Resource Creation Configuration:
  Network Resources: false
  Database Resources: false  
  Redis Resources: true
  Storage Resources: false
  DNS Records: true
  Page Rules: false
```

## Module Behavior

### When `create_resources = true`
- Terraform creates new resources using `resource` blocks
- Will fail if resources already exist

### When `create_resources = false` (default)
- Terraform references existing resources using `data` sources
- Will fail if resources don't exist

## Best Practices

### 1. Environment-Specific Strategies

**Staging Environment:**
- Use existing shared infrastructure when possible
- Create environment-specific resources (DNS, page rules)

**Production Environment:**
- Create dedicated infrastructure for isolation
- Use all creation flags for initial setup

### 2. Incremental Deployment

1. **Start with existing infrastructure** (all flags `false`)
2. **Add components incrementally** by setting specific flags to `true`
3. **Validate each step** before proceeding to next component

### 3. Troubleshooting

**"Resource already exists" errors:**
- Set the corresponding creation flag to `false`
- Ensure the resource name matches existing infrastructure

**"Resource not found" errors:**
- Set the corresponding creation flag to `true`
- Or create the resource manually first

## Security Considerations

- **Default to `false`** prevents accidental resource creation
- **Explicit opt-in** required for creating new infrastructure
- **Environment isolation** maintained through separate variable sets

## Migration Strategy

### From Existing Infrastructure

1. **Audit existing resources** in your GCP project and Cloudflare account
2. **Set creation flags to `false`** for existing resources
3. **Test with Terraform plan** to verify no unwanted changes
4. **Apply incrementally** with monitoring

### To New Environment

1. **Set all creation flags to `true`** for initial deployment
2. **Deploy in dependency order**: Network → Storage → Database → Redis → CDN
3. **Verify each component** before proceeding
4. **Switch to maintenance mode** (all flags `false`) after initial setup

## Monitoring and Validation

The deployment workflow provides detailed output showing:
- Which resources will be created vs. referenced
- Terraform plan summary
- Infrastructure component status
- Connection details for verification

This approach ensures safe, controlled infrastructure deployments while preventing resource conflicts.
