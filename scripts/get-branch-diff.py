#!/usr/bin/env python3
"""
Script to get the full diff between current branch and main branch.
This can be used to generate summaries or narratives of changes.
"""

import argparse
import subprocess
import sys
from datetime import datetime
from fnmatch import fnmatch
from pathlib import Path
from typing import List, Optional


# Files to exclude from diff (large auto-generated files)
EXCLUDE_PATTERNS = [
    # Package lock files
    "**/package-lock.json",
    "**/yarn.lock",
    "**/pnpm-lock.yaml",
    "**/bun.lock",
    "**/uv.lock",
    "**/poetry.lock",
    "**/Pipfile.lock",
    "**/requirements.lock",
    "**/pdm.lock",
    "package-lock.json",
    "yarn.lock",
    "pnpm-lock.yaml",
    "bun.lock",
    "uv.lock",
    "poetry.lock",
    "Pipfile.lock",
    "requirements.lock",
    "pdm.lock",

    # Build outputs and distributions
    "dist/*",
    "build/*",
    "out/*",
    ".next/*",
    ".nuxt/*",
    ".output/*",
    "coverage/*",
    ".coverage/*",
    "*.tsbuildinfo",

    # Storybook build outputs
    "storybook-static/*",
    "chromatic-build/*",

    # Node modules and dependencies
    "node_modules/*",
    ".pnp/*",
    ".yarn/*",
    "__pycache__/*",
    "*.pyc",
    ".venv/*",
    "venv/*",
    ".env/*",

    # Minified and compressed files
    "*.min.js",
    "*.min.css",
    "*.min.html",
    "*.bundle.js",
    "*.bundle.css",
    "*.chunk.js",
    "*.chunk.css",

    # Generated assets
    "*.map",
    "*.d.ts.map",
    "public/build/*",
    "static/build/*",
    "assets/build/*",

    # Logs and temporary files
    "*.log",
    "logs/*",
    ".tmp/*",
    "tmp/*",
    ".cache/*",
    ".parcel-cache/*",
    ".turbo/*",

    # IDE and system files
    ".DS_Store",
    "Thumbs.db",
    "desktop.ini",
    "*.swp",
    "*.swo",
    "*~",

    # Database files
    "*.db",
    "*.sqlite",
    "*.sqlite3",

    # Binary and media files (often large)
    "*.png",
    "*.jpg",
    "*.jpeg",
    "*.gif",
    "*.ico",
    "*.svg",
    "*.webp",
    "*.mp4",
    "*.mov",
    "*.avi",
    "*.pdf",
    "*.zip",
    "*.tar.gz",
    "*.tar.bz2",

    # Generated documentation
    "docs/build/*",
    "_site/*",
    ".docusaurus/*",

    # Test outputs
    "test-results/*",
    "playwright-report/*",
    "cypress/videos/*",
    "cypress/screenshots/*",
    ".nyc_output/*",
    "junit.xml",

    # Generated schema and API files
    "schema.generated.ts",
    "api.generated.ts",
    "*.generated.js",
    "*.generated.json",
    "openapi.json",
    "swagger.json",

    # Large data files
    "*.csv",
    "*.json",  # Only if they're known to be large data files
    "*.xml",   # Only if they're known to be large
    "data/*",
    "fixtures/*",
    "seeds/*",
]


def run_git_command(cmd: List[str], capture_output: bool = True) -> subprocess.CompletedProcess:
    """Run a git command and return the result."""
    try:
        result = subprocess.run(
            cmd,
            capture_output=capture_output,
            text=True,
            check=True
        )
        return result
    except subprocess.CalledProcessError as e:
        print(f"Error running git command: {' '.join(cmd)}", file=sys.stderr)
        print(f"Error: {e.stderr}", file=sys.stderr)
        sys.exit(1)


def get_current_branch() -> str:
    """Get the current git branch name."""
    result = run_git_command(["git", "branch", "--show-current"])
    return result.stdout.strip()


def check_git_repo() -> None:
    """Ensure we're in a git repository."""
    try:
        run_git_command(["git", "rev-parse", "--is-inside-work-tree"])
    except subprocess.CalledProcessError:
        print("Error: Not in a git repository", file=sys.stderr)
        sys.exit(1)


def check_branch_exists(branch: str) -> None:
    """Check if a branch exists locally or remotely."""
    try:
        # Check local branch
        run_git_command(["git", "show-ref", "--verify", "--quiet", f"refs/heads/{branch}"])
        return
    except subprocess.CalledProcessError:
        pass

    try:
        # Check remote branch
        run_git_command(["git", "show-ref", "--verify", "--quiet", f"refs/remotes/origin/{branch}"])
        return
    except subprocess.CalledProcessError:
        print(f"Error: Branch '{branch}' does not exist locally or remotely", file=sys.stderr)
        sys.exit(1)


def should_exclude_file(filepath: str, exclude_patterns: List[str]) -> bool:
    """Check if a file should be excluded based on patterns."""
    for pattern in exclude_patterns:
        if fnmatch(filepath, pattern):
            return True
    return False


def get_changed_files(target_branch: str, current_branch: str) -> List[str]:
    """Get list of changed files between branches."""
    result = run_git_command([
        "git", "diff", "--name-only", f"{target_branch}...{current_branch}"
    ])
    return [line.strip() for line in result.stdout.splitlines() if line.strip()]


def get_file_stats(target_branch: str, current_branch: str) -> str:
    """Get file change statistics."""
    result = run_git_command([
        "git", "diff", "--stat", f"{target_branch}...{current_branch}"
    ])
    return result.stdout


def get_full_diff(target_branch: str, current_branch: str, files: Optional[List[str]] = None) -> str:
    """Get the full diff, optionally for specific files."""
    cmd = ["git", "diff", f"{target_branch}...{current_branch}"]
    if files:
        cmd.extend(["--"] + files)

    result = run_git_command(cmd)
    return result.stdout


def output_markdown(
    current_branch: str,
    target_branch: str,
    include_stats: bool,
    include_files_only: bool,
    comprehensive_files: bool,
    exclude_large_files: bool
) -> None:
    """Output the diff in markdown format."""
    print("# Branch Diff Summary")
    print(f"- Current branch: {current_branch}")
    print(f"- Target branch: {target_branch}")
    print(f"- Generated on: {datetime.now().strftime('%a %b %d %H:%M:%S UTC %Y')}")
    print(f"- Excluded large/generated files from full diff: {'Yes' if exclude_large_files else 'No'}")
    print()

    if include_stats:
        print("## Change Statistics")
        print("```")
        stats = get_file_stats(target_branch, current_branch)
        print(stats.rstrip())
        print("```")
        print()

    if not include_files_only and not comprehensive_files:
        print("## Full Diff")
        print("```diff")

        if exclude_large_files:
            # Get all changed files and filter out excluded ones
            all_files = get_changed_files(target_branch, current_branch)
            included_files = [
                f for f in all_files
                if not should_exclude_file(f, EXCLUDE_PATTERNS)
            ]

            if included_files:
                diff = get_full_diff(target_branch, current_branch, included_files)
                print(diff.rstrip())
            else:
                print("No files to show after exclusions.")
        else:
            diff = get_full_diff(target_branch, current_branch)
            print(diff.rstrip())

        print("```")


def main():
    parser = argparse.ArgumentParser(
        description="Get the full diff between current branch and target branch"
    )
    parser.add_argument(
        "-b", "--branch",
        default="main",
        help="Target branch to compare against (default: main)"
    )
    parser.add_argument(
        "--no-stats",
        action="store_true",
        help="Don't include file statistics"
    )
    parser.add_argument(
        "--files-only",
        action="store_true",
        help="Only list changed files, no diff content"
    )
    parser.add_argument(
        "--comprehensive-files",
        action="store_true",
        help="Show comprehensive file list with stats but no diffs"
    )
    parser.add_argument(
        "--include-large",
        action="store_true",
        help="Include large/generated files in diff"
    )

    args = parser.parse_args()

    # Validation
    check_git_repo()
    check_branch_exists(args.branch)

    current_branch = get_current_branch()

    output_markdown(
        current_branch=current_branch,
        target_branch=args.branch,
        include_stats=not args.no_stats,
        include_files_only=args.files_only,
        comprehensive_files=args.comprehensive_files,
        exclude_large_files=not args.include_large
    )


if __name__ == "__main__":
    main()
