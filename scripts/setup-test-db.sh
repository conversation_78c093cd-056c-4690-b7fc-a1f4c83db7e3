#!/bin/bash
#
# A2A Platform - Test Database Setup Script
#
# This script creates the test database and runs migrations.
# It is idempotent and can be run multiple times without errors.
#
# Usage:
#   ./scripts/setup-test-db.sh [OPTIONS]
#
# Options:
#   --ci            Use local PostgreSQL instance for CI environments
#   --help          Display this help message
#
# Environment Variables:
#   DATABASE_URL    Required. The database connection string to use.
#                   Example: **************************************/a2a_platform_test
#
# Examples:
#   DATABASE_URL="**************************************/a2a_platform_test" ./scripts/setup-test-db.sh
#   DATABASE_URL="postgresql://postgres:postgres@localhost:5432/a2a_platform_test" ./scripts/setup-test-db.sh --ci

set -e

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
BACKEND_DIR="${PROJECT_ROOT}/apps/backend"

# Default values
USE_DOCKER=true  # Default to Docker for local development
DB_NAME="a2a_platform_test"
DB_CONTAINER="a2a-postgres"

# Check if running in CI environment
if [[ -n "${CI}" ]] || [[ "${CI}" == "true" ]]; then
  # In CI environment, use local PostgreSQL
  USE_DOCKER=false
  echo "CI environment detected, using local PostgreSQL"

  # Force database URLs to use localhost in CI mode
  if [[ -n "${DATABASE_URL}" ]] && [[ "${DATABASE_URL}" == *"@db:"* ]]; then
    export DATABASE_URL="${DATABASE_URL/@db:/@localhost:}"
    echo "CI environment: Adjusted DATABASE_URL to use localhost"
  fi
  if [[ -n "${DATABASE_ASYNC_URL}" ]] && [[ "${DATABASE_ASYNC_URL}" == *"@db:"* ]]; then
    export DATABASE_ASYNC_URL="${DATABASE_ASYNC_URL/@db:/@localhost:}"
    echo "CI environment: Adjusted DATABASE_ASYNC_URL to use localhost"
  fi
fi

# Initial check - will be re-done after argument parsing for proper defaults

# Ensure the correct hostname is used based on the environment
if [[ "${USE_DOCKER}" == false ]]; then
  # In CI/local environment, replace 'db' with 'localhost'
  if [[ "${DATABASE_URL}" == *"@db:"* ]]; then
    export DATABASE_URL="${DATABASE_URL/@db:/@localhost:}"
    echo "Adjusted DATABASE_URL for CI/local environment"
  fi
  if [[ "${DATABASE_ASYNC_URL}" == *"@db:"* ]]; then
    export DATABASE_ASYNC_URL="${DATABASE_ASYNC_URL/@db:/@localhost:}"
    echo "Adjusted DATABASE_ASYNC_URL for CI/local environment"
  fi
elif [[ "${USE_DOCKER}" == true ]]; then
  # In Docker environment, replace 'localhost' with 'db'
  if [[ "${DATABASE_URL}" == *"@localhost:"* ]]; then
    export DATABASE_URL="${DATABASE_URL/@localhost:/@db:}"
    echo "Adjusted DATABASE_URL for Docker environment"
  fi
  if [[ "${DATABASE_ASYNC_URL}" == *"@localhost:"* ]]; then
    export DATABASE_ASYNC_URL="${DATABASE_ASYNC_URL/@localhost:/@db:}"
    echo "Adjusted DATABASE_ASYNC_URL for Docker environment"
  fi
fi

# Extract database name from DATABASE_URL
# This regex extracts the database name from the end of the URL
if [[ "${DATABASE_URL}" =~ /([^/]+)$ ]]; then
  DB_NAME="${BASH_REMATCH[1]}"
  echo "Using database name '${DB_NAME}'"
else
  echo "Warning: Could not extract database name from DATABASE_URL, using default: ${DB_NAME}"
fi

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    --ci)
      USE_DOCKER=false
      echo "CI flag detected, using local PostgreSQL"
      shift
      ;;
    --help)
      grep '^#' "$0" | grep -v '#!/bin/bash' | sed 's/^# \?//'
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

# Re-check and set default database URLs after argument parsing
# This ensures the correct defaults are set based on final USE_DOCKER value
if [[ -z "${DATABASE_URL}" ]]; then
  if [[ "${USE_DOCKER}" == true ]]; then
    export DATABASE_URL="**************************************/a2a_platform_test"
    echo "DATABASE_URL not set, using default for Docker: ${DATABASE_URL}"
  else
    export DATABASE_URL="postgresql://postgres:postgres@localhost:5432/a2a_platform_test"
    echo "DATABASE_URL not set, using default for CI/local: ${DATABASE_URL}"
  fi
fi

if [[ -z "${DATABASE_ASYNC_URL}" ]]; then
  if [[ "${USE_DOCKER}" == true ]]; then
    export DATABASE_ASYNC_URL="postgresql+asyncpg://postgres:postgres@db:5432/a2a_platform_test"
    echo "DATABASE_ASYNC_URL not set, using default for Docker: ${DATABASE_ASYNC_URL}"
  else
    export DATABASE_ASYNC_URL="postgresql+asyncpg://postgres:postgres@localhost:5432/a2a_platform_test"
    echo "DATABASE_ASYNC_URL not set, using default for CI/local: ${DATABASE_ASYNC_URL}"
  fi
fi

# Log database connection information
echo "Database connection configured"
if [[ "${USE_DOCKER}" == true ]]; then
  echo "Using Docker PostgreSQL instance"
else
  echo "Using local PostgreSQL instance"
fi

# Function to check if Docker container is running
check_docker_container() {
  # Just check if the container exists and is up
  if docker compose ps | grep -q "db.*Up"; then
    return 0
  else
    return 1
  fi
}

# Function to check if database exists
check_db_exists() {
  if [[ "${USE_DOCKER}" == true ]]; then
    # First check if container is running
    if ! check_docker_container; then
      echo "Error: Docker container '${DB_CONTAINER}' is not running."
      echo "Please start it with: docker compose up -d db"
      exit 1
    fi
    # Check in Docker
    docker compose exec -T db psql -U postgres -lqt | cut -d \| -f 1 | grep -qw ${DB_NAME}
  else
    # Extract host and port from DATABASE_URL for local connection
    # This regex extracts host and port from the URL
    if [[ "${DATABASE_URL}" =~ ://[^:]+:([^@]+)@([^:]+):([0-9]+)/ ]]; then
      DB_PASSWORD="${BASH_REMATCH[1]}"
      DB_HOST="${BASH_REMATCH[2]}"
      DB_PORT="${BASH_REMATCH[3]}"
      # Check locally using extracted credentials
      PGPASSWORD="${DB_PASSWORD}" psql -U postgres -h ${DB_HOST} -p ${DB_PORT} -lqt | cut -d \| -f 1 | grep -qw ${DB_NAME}
    else
      echo "Error: Could not parse host and port from DATABASE_URL"
      exit 1
    fi
  fi
  return $?
}

# Check if database exists, create if it doesn't
if check_db_exists; then
  echo "Test database '${DB_NAME}' already exists."
else
  echo "Test database '${DB_NAME}' does not exist, trying to create it..."
  if [[ "${USE_DOCKER}" == true ]]; then
    # Create in Docker
    docker compose exec -T db psql -U postgres -c "CREATE DATABASE ${DB_NAME} WITH OWNER postgres;" || echo "Could not create database, may already exist or be in progress"
  else
    # Extract credentials from DATABASE_URL for local connection
    if [[ "${DATABASE_URL}" =~ ://[^:]+:([^@]+)@([^:]+):([0-9]+)/ ]]; then
      DB_PASSWORD="${BASH_REMATCH[1]}"
      DB_HOST="${BASH_REMATCH[2]}"
      DB_PORT="${BASH_REMATCH[3]}"
      # Create locally using extracted credentials
      PGPASSWORD="${DB_PASSWORD}" psql -U postgres -h ${DB_HOST} -p ${DB_PORT} -c "CREATE DATABASE ${DB_NAME} WITH OWNER postgres;" || echo "Could not create database, may already exist or be in progress"
    else
      echo "Error: Could not parse credentials from DATABASE_URL"
      # But continue anyway, as the database might exist
    fi
  fi
  # Verify database was created
  if check_db_exists; then
    echo "Test database '${DB_NAME}' created or found successfully."
  else
    echo "Warning: Could not verify if database '${DB_NAME}' exists, but continuing..."
  fi
fi

# Change to backend directory
cd "${BACKEND_DIR}"
echo "Changed to directory: $(pwd)"

# Check if alembic.ini exists
if [[ ! -f "alembic.ini" ]]; then
  echo "Error: alembic.ini not found in ${BACKEND_DIR}"
  exit 1
fi

# Check if alembic is installed
if ! command -v alembic &> /dev/null; then
  echo "Error: alembic is not installed."
  echo "Please install it with: pip install alembic"
  exit 1
fi

# Check if psycopg2 is installed (needed for Alembic with PostgreSQL)
if ! python -c "import psycopg2" &> /dev/null; then
  echo "Installing psycopg2 for PostgreSQL support..."
  pip install psycopg2-binary
fi

# Check if database exists, create if it doesn't
if check_db_exists; then
  echo "Test database '${DB_NAME}' already exists."
else
  echo "Test database '${DB_NAME}' does not exist, creating it..."
  if [[ "${USE_DOCKER}" == true ]]; then
    # Create in Docker
    docker compose exec -T db psql -U postgres -c "CREATE DATABASE ${DB_NAME} WITH OWNER postgres;" || echo "Could not create database, may already exist or be in progress"
  else
    # Extract credentials from DATABASE_URL for local connection
    if [[ "${DATABASE_URL}" =~ ://[^:]+:([^@]+)@([^:]+):([0-9]+)/ ]]; then
      DB_PASSWORD="${BASH_REMATCH[1]}"
      DB_HOST="${BASH_REMATCH[2]}"
      DB_PORT="${BASH_REMATCH[3]}"
      # Create locally using extracted credentials
      PGPASSWORD="${DB_PASSWORD}" psql -U postgres -h ${DB_HOST} -p ${DB_PORT} -c "CREATE DATABASE ${DB_NAME} WITH OWNER postgres;" || echo "Could not create database, may already exist or be in progress"
    else
      echo "Error: Could not parse credentials from DATABASE_URL"
      exit 1
    fi
  fi
  echo "Test database '${DB_NAME}' created successfully."
fi

# Determine the correct Docker Compose file to use
POTENTIAL_COMPOSE_FILES=("${PROJECT_ROOT}/docker-compose.test.yml" "${PROJECT_ROOT}/docker-compose.dev.yml" "${PROJECT_ROOT}/docker-compose.yml")
EFFECTIVE_COMPOSE_FILE=""
BACKEND_SERVICE_FOR_MIGRATION=""
# Run migrations
echo "Running database migrations on '${DB_NAME}'..."

if [[ "${USE_DOCKER}" == "true" ]]; then
  for cf_path in "${POTENTIAL_COMPOSE_FILES[@]}"; do
    if [[ -f "${cf_path}" ]]; then
      if docker compose --file "${cf_path}" ps backend > /dev/null 2>&1; then
        EFFECTIVE_COMPOSE_FILE="${cf_path}"
        BACKEND_SERVICE_FOR_MIGRATION="backend"
        echo "Found backend service 'backend' in ${EFFECTIVE_COMPOSE_FILE} for migrations."
        break
      fi
      discovered_service=$(docker compose --file "${cf_path}" ps --services | grep -E '(backend|api|app|web)' | head -n 1)
      if [[ -n "${discovered_service}" ]]; then
        if docker compose --file "${cf_path}" ps "${discovered_service}" > /dev/null 2>&1; then
          EFFECTIVE_COMPOSE_FILE="${cf_path}"
          BACKEND_SERVICE_FOR_MIGRATION="${discovered_service}"
          echo "Discovered running backend-like service '${BACKEND_SERVICE_FOR_MIGRATION}' in ${EFFECTIVE_COMPOSE_FILE} for migrations."
          break
        fi
      fi
    fi
  done
fi

# Function to load .env.test if it exists
load_dot_env_test() {
  ENV_TEST_FILE="${PROJECT_ROOT}/.env.test"
  if [[ -f "${ENV_TEST_FILE}" ]]; then
    echo "Loading environment variables from ${ENV_TEST_FILE} for local alembic execution..."
    export $(grep -v '^#' "${ENV_TEST_FILE}" | xargs)
  else
    echo "Warning: .env.test file not found at ${ENV_TEST_FILE}. Local alembic might fail if it relies on these variables."
  fi
}

# Run migrations
echo "Running database migrations on '${DB_NAME}'..."

if [[ "${USE_DOCKER}" == "true" ]]; then
  echo "Using Docker PostgreSQL instance"
  if [[ -n "${EFFECTIVE_COMPOSE_FILE}" && -n "${BACKEND_SERVICE_FOR_MIGRATION}" ]]; then
    echo "Running migrations via Docker Compose exec on service '${BACKEND_SERVICE_FOR_MIGRATION}' using ${EFFECTIVE_COMPOSE_FILE}..."
    if docker compose --file "${EFFECTIVE_COMPOSE_FILE}" exec -T "${BACKEND_SERVICE_FOR_MIGRATION}" alembic upgrade head; then
      echo "Migrations applied successfully via Docker exec."
    else
      echo "Error: Failed to apply migrations via Docker exec on ${BACKEND_SERVICE_FOR_MIGRATION}."
      echo "Attempting to run migrations locally as a fallback (might fail if DB_HOST is 'db')..."
      load_dot_env_test # Load .env.test for local fallback
      DB_USER_PASS_DBNAME=$(echo "${DATABASE_URL}" | sed -n 's|postgresql://\(.*\)@db:5432/\(.*\)|\1/\2|p')
      if [[ -n "$DB_USER_PASS_DBNAME" ]]; then
        LOCAL_ALEMBIC_DB_URL="postgresql://postgres:postgres@localhost:5432/a2a_platform_test"
        echo "Using DATABASE_URL='${LOCAL_ALEMBIC_DB_URL}' for local alembic."
        DATABASE_URL="${LOCAL_ALEMBIC_DB_URL}" alembic upgrade head || echo "Local alembic fallback also failed."
      else
        alembic upgrade head || echo "Local alembic fallback also failed (could not parse original DATABASE_URL for localhost override)."
      fi
    fi
  else
    echo "Backend service for migration not found in specified Docker Compose files, or Docker mode not configured to use exec."
    echo "Attempting to run migrations locally (this might fail if DATABASE_URL host is 'db')..."
    load_dot_env_test # Load .env.test for local fallback
    DB_USER_PASS_DBNAME=$(echo "${DATABASE_URL}" | sed -n 's|postgresql://\(.*\)@db:5432/\(.*\)|\1/\2|p')
    if [[ -n "$DB_USER_PASS_DBNAME" ]]; then
      LOCAL_ALEMBIC_DB_URL="postgresql://${DB_USER_PASS_DBNAME%/*}@localhost:5432/${DB_USER_PASS_DBNAME#*/}"
      echo "Using DATABASE_URL='${LOCAL_ALEMBIC_DB_URL}' for local alembic."
      DATABASE_URL="${LOCAL_ALEMBIC_DB_URL}" alembic upgrade head || echo "Local alembic fallback also failed."
    else
      alembic upgrade head || echo "Local alembic fallback also failed (could not parse original DATABASE_URL for localhost override)."
    fi
  fi
else
  echo "Running migrations locally (non-Docker mode or CI)..."
  load_dot_env_test # Load .env.test for local/CI alembic too
  alembic upgrade head
fi

echo "Test database setup completed successfully!"
exit 0
