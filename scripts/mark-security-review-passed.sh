#!/bin/bash
# filepath: /Users/<USER>/dev/blkops-collective/a2a-platform/scripts/mark-security-review-passed.sh
#
# Script to mark the "Security review passed" item as complete in the implementation checklist
# This script provides a simple interface for marking the security review as passed
# and updating the implementation checklist in the US6.1 specification file.

set -e

# Define colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Define paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(dirname "$SCRIPT_DIR")"
SPEC_FILE="${ROOT_DIR}/specs/US6.1-Ensure-HTTPS-Communication.md"
TIMESTAMP=$(date +"%Y%m%d-%H%M%S")
BACKUP_FILE="${SPEC_FILE}.${TIMESTAMP}.bak"

# Check if we have the required file
if [ ! -f "${SPEC_FILE}" ]; then
  echo -e "${RED}Error: Specification file not found: ${SPEC_FILE}${NC}"
  exit 1
fi

# Display header
echo -e "${BLUE}=========================================================${NC}"
echo -e "${BLUE}${BOLD}       Mark Security Review as Passed       ${NC}"
echo -e "${BLUE}=========================================================${NC}"
echo ""

# Check if security review is already marked as passed
if grep -q "- \[x\] ✅ Security review passed" "${SPEC_FILE}"; then
  echo -e "${YELLOW}Security review is already marked as passed.${NC}"
  echo -e "No changes needed."
  exit 0
fi

# Ask for confirmation
echo -e "${YELLOW}This script will mark the security review as passed in the implementation checklist.${NC}"
echo -e "${YELLOW}Before proceeding, make sure that:${NC}"
echo -e "  1. The security review workflow has been run and all tests have passed"
echo -e "  2. External security assessments (SSL Labs, Security Headers) have been completed"
echo -e "  3. All manual verification steps have been performed"
echo -e ""
read -p "Do you want to proceed? (y/n): " CONFIRM

if [ "${CONFIRM}" != "y" ]; then
  echo -e "${YELLOW}Operation cancelled.${NC}"
  exit 0
fi

# Prompt for reviewer name
read -p "Enter your name (for documentation purposes): " REVIEWER_NAME
if [ -z "${REVIEWER_NAME}" ]; then
  REVIEWER_NAME=$(whoami)
fi

# Backup original file
echo -e "${BLUE}Creating backup of specification file...${NC}"
cp "${SPEC_FILE}" "${BACKUP_FILE}"
echo -e "${GREEN}Backup created: ${BACKUP_FILE}${NC}"

# Update the checklist item
echo -e "${BLUE}Updating implementation checklist...${NC}"
sed -i.tmp 's/- \[ \] ✅ Security review passed (TODO)/- [x] ✅ Security review passed/' "${SPEC_FILE}"
rm -f "${SPEC_FILE}.tmp"

# Verify the update
if grep -q "- \[x\] ✅ Security review passed" "${SPEC_FILE}"; then
  echo -e "${GREEN}${BOLD}Security review has been marked as passed!${NC}"
  echo -e "${GREEN}Updated file: ${SPEC_FILE}${NC}"
  echo -e ""
  
  # Create a review record
  REVIEW_DIR="${ROOT_DIR}/security-review-results"
  mkdir -p "${REVIEW_DIR}"
  REVIEW_RECORD="${REVIEW_DIR}/security-review-passed-${TIMESTAMP}.txt"
  
  cat > "${REVIEW_RECORD}" << EOF
HTTPS Security Review Completion Record
======================================

Date: $(date)
Reviewer: ${REVIEWER_NAME}
Status: PASSED

This record indicates that the HTTPS security review has been completed
and the implementation has passed all required checks.

Next steps:
1. Proceed to production deployment verification
2. Set up continuous monitoring for SSL certificates
3. Schedule periodic re-verification as per docs/security/https-review-process.md

Checklist item updated in: ${SPEC_FILE}
EOF
  
  echo -e "${GREEN}Review record created: ${REVIEW_RECORD}${NC}"
  echo -e "${BLUE}Next steps:${NC}"
  echo -e "1. Proceed to production deployment verification"
  echo -e "2. Commit the updated specification file"
  echo -e "3. Set up continuous monitoring for SSL certificates"
  echo -e "The implementation checklist now shows that security review has been completed."
  echo -e "You can verify the change by opening the specification file."
  echo -e ""
  echo -e "${YELLOW}Note: This script does not perform any actual security checks.${NC}"
  echo -e "Before running this script, you should have already run the full security"
  echo -e "review process using the 'run-security-review.sh' script and confirmed"
  echo -e "that all checks have passed."
  exit 0
else
  echo -e "${RED}Error: Failed to update the checklist.${NC}"
  echo -e "Restoring backup..."
  cp "${SPEC_FILE}.bak" "${SPEC_FILE}"
  echo -e "Original file has been restored."
  exit 1
fi
