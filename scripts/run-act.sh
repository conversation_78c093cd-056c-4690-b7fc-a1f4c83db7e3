#!/bin/bash
#
# A script to simplify running GitHub Actions workflows locally using act
# https://github.com/nektos/act
#
# Updated for R2 migration - supports new deploy-infra.yml workflow
# and includes Cloudflare provider configuration for R2 storage testing
#

set -e

# Default values
WORKFLOW=""
JOB=""
EVENT_TYPE="workflow_dispatch"
DRY_RUN=false
DEBUG=false
CONTAINER_ARCH="linux/amd64"  # For M1/M2 Mac compatibility
INPUT_VALUES=""
EVENT_PATH=".github/workflows/event.json"
SECRET_FILE=".secrets"
FORCE_LOCAL_BACKEND=true

# Function to show usage
show_usage() {
  cat << EOF
Usage: run-act.sh [options]

Run GitHub Actions workflows locally using act.

Options:
  -w, --workflow FILE     Specify the workflow file to run (required)
  -j, --job JOB           Specify a particular job to run
  -e, --event EVENT       Specify the event type (default: workflow_dispatch)
  -i, --input KEY=VALUE   Specify input values for workflow_dispatch (can be used multiple times)
  -d, --dry-run           Perform a dry run without executing actions
  -b, --use-gcp-backend   Use the GCP backend instead of a local backend (for terraform workflows)
  -p, --event-path FILE   Use a custom event payload file (default: .github/workflows/event.json)
  -s, --secret-file FILE  Use a custom secrets file (default: .secrets)
  --debug                 Enable debug output
  -h, --help              Show this help message

Examples:
  # Run the combined infrastructure deployment workflow
  ./scripts/run-act.sh -w deploy-infra.yml -j terraform-plan -i environment=staging

  # Run infrastructure deployment with dry-run
  ./scripts/run-act.sh -w deploy-infra.yml -j terraform-plan -d -i environment=production

  # Run with custom input values and skip tests check
  ./scripts/run-act.sh -w deploy-infra.yml -i environment=staging -i skip_tests_check=true

  # Run Terraform module tests
  ./scripts/run-act.sh -w terraform-module-tests.yml

EOF
  exit 1
}

# Function to set input values in the event.json file
setup_event_json() {
  if [ ! -f "$EVENT_PATH" ]; then
    mkdir -p $(dirname "$EVENT_PATH")

    cat > "$EVENT_PATH" << EOF
{
  "repository": {
    "name": "a2a-platform",
    "owner": {
      "name": "blkops-collective"
    }
  },
  "event_name": "${EVENT_TYPE}",
  "inputs": {
  }
}
EOF
  fi

  # Parse input values and update event.json
  if [ -n "$INPUT_VALUES" ]; then
    # Make a temporary copy of the event file
    TMP_EVENT_FILE=$(mktemp)
    cp "$EVENT_PATH" "$TMP_EVENT_FILE"

    for input in $INPUT_VALUES; do
      KEY=$(echo $input | cut -d= -f1)
      VALUE=$(echo $input | cut -d= -f2-)

      # Determine if value is boolean, number or string
      if [[ "$VALUE" == "true" || "$VALUE" == "false" ]]; then
        # For boolean values, don't add quotes
        sed -i.bak "s|\(\"inputs\": {\)|\1\n    \"$KEY\": $VALUE,|" "$TMP_EVENT_FILE"
      elif [[ "$VALUE" =~ ^[0-9]+$ ]]; then
        # For numeric values, don't add quotes
        sed -i.bak "s|\(\"inputs\": {\)|\1\n    \"$KEY\": $VALUE,|" "$TMP_EVENT_FILE"
      else
        # For string values, add quotes
        sed -i.bak "s|\(\"inputs\": {\)|\1\n    \"$KEY\": \"$VALUE\",|" "$TMP_EVENT_FILE"
      fi
    done

    # Clean up trailing commas in JSON
    sed -i.bak 's/,\(\s*\}\)/\1/g' "$TMP_EVENT_FILE"

    # Replace the original event file
    mv "$TMP_EVENT_FILE" "$EVENT_PATH"
    rm -f "${TMP_EVENT_FILE}.bak" 2>/dev/null || true
    rm -f "${EVENT_PATH}.bak" 2>/dev/null || true
  fi

  if [ "$DEBUG" = true ]; then
    echo "Event payload:"
    cat "$EVENT_PATH"
  fi
}

# Function to handle Terraform specific setup
setup_terraform_workflow() {
  if [[ "$WORKFLOW" == *"terraform"* || "$WORKFLOW" == *"infra"* ]] && [[ "$FORCE_LOCAL_BACKEND" == true ]]; then
    echo "Setting up local backend for Terraform workflow..."

    # Determine if this is an environment-specific workflow
    local terraform_dir="terraform"
    if [[ "$INPUT_VALUES" == *"environment="* ]]; then
      local env=$(echo "$INPUT_VALUES" | grep -o 'environment=[^[:space:]]*' | cut -d= -f2)
      if [[ -n "$env" && -d "terraform/environments/$env" ]]; then
        terraform_dir="terraform/environments/$env"
        echo "Using environment-specific Terraform directory: $terraform_dir"
      fi
    fi

    # Check if backend.tf.local exists
    if [ -f "$terraform_dir/backend.tf.local" ]; then
      echo "Found existing backend.tf.local file in $terraform_dir"
    else
      echo "Creating minimal backend.tf.local file in $terraform_dir"
      cat > "$terraform_dir/backend.tf.local" << EOF
# Local backend for testing with act
terraform {
  backend "local" {}
}
EOF
    fi

    # Backup original backend.tf if it exists
    if [ -f "$terraform_dir/backend.tf" ]; then
      echo "Backing up original backend.tf in $terraform_dir"
      cp "$terraform_dir/backend.tf" "$terraform_dir/backend.tf.bak"
    fi

    # Use the local backend
    echo "Applying local backend configuration in $terraform_dir"
    cp "$terraform_dir/backend.tf.local" "$terraform_dir/backend.tf"

    # Create a provider_override.tf file to add credentials support
    echo "Creating provider_override.tf with mock credentials"
    cat > terraform/provider_override.tf << EOF
# This file is auto-generated by run-act.sh for local testing
provider "google" {
  credentials = file("\${path.module}/mock-credentials.json")
  project     = "local-test-project"
  region      = "us-central1"
}

provider "google-beta" {
  credentials = file("\${path.module}/mock-credentials.json")
  project     = "local-test-project"
  region      = "us-central1"
}

# Cloudflare provider for R2 storage
provider "cloudflare" {
  api_token = "mock-cloudflare-token"
}

# Add a local variables file for testing
locals {
  skip_data_source_errors = true
}
EOF

    # Create mock credentials if they don't exist
    if [ ! -f "terraform/mock-credentials.json" ]; then
      echo "Creating mock GCP credentials"
      cat > terraform/mock-credentials.json << EOF
**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
EOF
    fi

    echo "Terraform set up for local testing with act"
  fi
}

# Parse command line arguments
while [[ "$#" -gt 0 ]]; do
  case $1 in
    -w|--workflow) WORKFLOW="$2"; shift ;;
    -j|--job) JOB="$2"; shift ;;
    -e|--event) EVENT_TYPE="$2"; shift ;;
    -i|--input)
      if [ -z "$INPUT_VALUES" ]; then
        INPUT_VALUES="$2"
      else
        INPUT_VALUES="$INPUT_VALUES $2"
      fi
      shift ;;
    -d|--dry-run) DRY_RUN=true ;;
    -b|--use-gcp-backend) FORCE_LOCAL_BACKEND=false ;;
    -p|--event-path) EVENT_PATH="$2"; shift ;;
    -s|--secret-file) SECRET_FILE="$2"; shift ;;
    --debug) DEBUG=true ;;
    -h|--help) show_usage ;;
    *) echo "Unknown parameter: $1"; show_usage ;;
  esac
  shift
done

# Validate required parameters
if [ -z "$WORKFLOW" ]; then
  echo "Error: Workflow file (-w, --workflow) is required"
  show_usage
fi

# Check if the workflow file exists
WORKFLOW_PATH=".github/workflows/$WORKFLOW"
if [ ! -f "$WORKFLOW_PATH" ]; then
  echo "Error: Workflow file not found at $WORKFLOW_PATH"
  echo "Available workflows:"
  ls -1 .github/workflows/*.yml
  exit 1
fi

# Build the act command
ACT_CMD="act"
ACT_CMD+=" -W $WORKFLOW_PATH"
ACT_CMD+=" --container-architecture $CONTAINER_ARCH"

# Add event path
ACT_CMD+=" -e $EVENT_PATH"

# Add secrets file if it exists
if [ -f "$SECRET_FILE" ]; then
  ACT_CMD+=" --secret-file $SECRET_FILE"
fi

# Add job if specified
if [ -n "$JOB" ]; then
  ACT_CMD+=" -j $JOB"
fi

# Add dry run if enabled
if [ "$DRY_RUN" = true ]; then
  ACT_CMD+=" --dryrun"
fi

# Setup the event JSON file with inputs
setup_event_json

# Setup terraform workflow if needed
setup_terraform_workflow

# Print the command if in debug mode
if [ "$DEBUG" = true ]; then
  echo "Running command: $ACT_CMD"
fi

# Execute the act command
echo "Running workflow: $WORKFLOW"
echo "Event type: $EVENT_TYPE"
if [ -n "$JOB" ]; then
  echo "Job: $JOB"
fi

# Run the command
bash -c "$ACT_CMD"

# Restore Terraform backend if needed
if [[ ("$WORKFLOW" == *"terraform"* || "$WORKFLOW" == *"infra"*) && "$FORCE_LOCAL_BACKEND" == true ]]; then
  # Determine the terraform directory used
  local terraform_dir="terraform"
  if [[ "$INPUT_VALUES" == *"environment="* ]]; then
    local env=$(echo "$INPUT_VALUES" | grep -o 'environment=[^[:space:]]*' | cut -d= -f2)
    if [[ -n "$env" && -d "terraform/environments/$env" ]]; then
      terraform_dir="terraform/environments/$env"
    fi
  fi

  if [ -f "$terraform_dir/backend.tf.bak" ]; then
    mv "$terraform_dir/backend.tf.bak" "$terraform_dir/backend.tf"
    echo "Restored original Terraform backend configuration in $terraform_dir"
  fi

  # Clean up provider override file
  if [ -f "terraform/provider_override.tf" ]; then
    rm -f terraform/provider_override.tf
    echo "Cleaned up provider override file"
  fi
fi