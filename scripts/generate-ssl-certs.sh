#!/bin/bash
# filepath: /Users/<USER>/dev/blkops-collective/a2a-platform/scripts/generate-ssl-certs.sh
#
# Script to generate self-signed SSL certificates for local HTTPS development
# This creates certificates in the ssl-certs directory for use with local HTTPS development

set -e

# Define colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Define paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(dirname "$SCRIPT_DIR")"
CERTS_DIR="${ROOT_DIR}/ssl-certs"

echo -e "${YELLOW}Generating SSL certificates for local development...${NC}"

# Check if mkcert is installed
if ! command -v mkcert &> /dev/null; then
    echo -e "${RED}Error: mkcert is not installed${NC}"
    echo -e "Please install mkcert first:"
    echo -e "${GREEN}macOS:${NC} brew install mkcert"
    echo -e "${GREEN}Linux:${NC} apt install mkcert or equivalent for your distribution"
    exit 1
fi

# Create the ssl-certs directory if it doesn't exist
mkdir -p "${CERTS_DIR}"

# Change to the certs directory
cd "${CERTS_DIR}"

# Install the local CA if it doesn't exist
echo -e "${YELLOW}Installing local CA (may require sudo password)...${NC}"
mkcert -install

# Generate certificates for localhost and other local domains
echo -e "${YELLOW}Generating certificates for localhost...${NC}"
mkcert -cert-file localhost.pem -key-file localhost-key.pem localhost 127.0.0.1 ::1

# Set proper permissions
chmod 644 localhost.pem localhost-key.pem

echo -e "${GREEN}SSL certificates generated successfully!${NC}"
echo -e "Certificate: ${CERTS_DIR}/localhost.pem"
echo -e "Private key: ${CERTS_DIR}/localhost-key.pem"
echo ""
echo -e "${YELLOW}To use HTTPS in development:${NC}"
echo -e "1. Set VITE_USE_HTTPS=true in your .env file"
echo -e "2. Restart your development server"
echo ""
echo -e "${GREEN}Done!${NC}"
