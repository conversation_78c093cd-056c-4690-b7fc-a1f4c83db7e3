#!/bin/bash
set -euo pipefail

# GraphQL Schema Update Script
# Updates the frontend GraphQL schema from the backend

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

echo "🔄 Updating GraphQL schema from backend..."

# Check if backend is running
if ! docker compose ps backend | grep -q "Up"; then
    echo "⚠️  Backend is not running. Starting backend..."
    docker compose up -d backend
    echo "⏳ Waiting for backend to be ready..."
    sleep 10
fi

# Check if web app directory exists
if [[ ! -d "$PROJECT_ROOT/apps/web" ]]; then
    echo "❌ Web app directory not found: $PROJECT_ROOT/apps/web"
    exit 1
fi

cd "$PROJECT_ROOT/apps/web"

# Download schema from backend
echo "📥 Downloading schema from backend..."
if ! curl -f -s -o temp-schema.graphql "http://localhost:8000/graphql?sdl"; then
    echo "❌ Failed to download schema from backend"
    echo "   Ensure backend is running and GraphQL endpoint is accessible"
    exit 1
fi

# Move to final location
if [[ -f "temp-schema.graphql" ]]; then
    mv temp-schema.graphql src/graphql/schema.graphql
    echo "✅ Schema updated: src/graphql/schema.graphql"
else
    echo "❌ Failed to download schema"
    exit 1
fi

# Generate TypeScript types
echo "🏗️  Generating TypeScript types..."
if bun run codegen; then
    echo "✅ TypeScript types generated successfully"
else
    echo "❌ Failed to generate TypeScript types"
    exit 1
fi

echo "🎉 GraphQL schema update complete!"
