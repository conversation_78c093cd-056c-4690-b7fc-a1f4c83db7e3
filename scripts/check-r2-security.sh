#!/bin/bash
# Cloudflare R2 Storage Security Validation Script
# Validates R2 bucket security configuration and access controls
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT=${1:-staging}
VALIDATE_ONLY=${2:-false}

echo -e "${BLUE}🪣 Validating R2 storage security for environment: $ENVIRONMENT${NC}"
echo "=================================================================="

# Set environment-specific configuration
if [[ "$ENVIRONMENT" == "production" ]]; then
    R2_BUCKET_NAME="a2a-platform-web-assets-production"
    R2_CUSTOM_DOMAIN="assets.vedavivi.app"
    EXPECTED_CORS_ORIGINS="https://vedavivi.app"
else
    R2_BUCKET_NAME="a2a-platform-web-assets-staging"
    R2_CUSTOM_DOMAIN="assets-staging.vedavivi.app"
    EXPECTED_CORS_ORIGINS="https://staging.vedavivi.app"
fi

# Validation counters
PASSED=0
FAILED=0

# Function to run validation checks
validate_check() {
    local test_name="$1"
    local command="$2"
    local expected="$3"

    echo -e "\n${BLUE}Testing: $test_name${NC}"

    if bash -c "$command"; then
        echo -e "${GREEN}✅ PASSED: $test_name${NC}"
        PASSED=$((PASSED + 1))
    else
        echo -e "${RED}❌ FAILED: $test_name${NC}"
        echo -e "${RED}   Expected: $expected${NC}"
        FAILED=$((FAILED + 1))
    fi
}

# Function to check HTTP status
check_http_status() {
    local url="$1"
    local expected_status="$2"

    local status=$(curl -s -o /dev/null -w "%{http_code}" "$url" || echo "000")
    [[ "$status" == "$expected_status" ]]
}

# Function to check SSL certificate
check_ssl_cert() {
    local domain="$1"

    # Extract domain from URL if needed
    domain=$(echo "$domain" | sed 's|https\?://||' | sed 's|/.*||')

    # Check SSL certificate validity
    echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | \
    openssl x509 -noout -dates 2>/dev/null | grep -q "notAfter"
}

# Function to check CORS headers
check_cors_headers() {
    local url="$1"
    local expected_origin="$2"

    # Make an OPTIONS request to check CORS
    local cors_headers=$(curl -s -I -X OPTIONS \
        -H "Origin: $expected_origin" \
        -H "Access-Control-Request-Method: GET" \
        "$url" | grep -i "access-control" || echo "")
    
    [[ -n "$cors_headers" ]]
}

# Function to check cache headers
check_cache_headers() {
    local url="$1"
    local expected_pattern="$2"

    local headers=$(curl -s -I "$url" | grep -i "cache-control" || echo "")
    [[ "$headers" =~ $expected_pattern ]]
}

# Function to check Cloudflare headers
check_cloudflare_headers() {
    local url="$1"

    local cf_ray=$(curl -s -I "$url" | grep -i "cf-ray" || echo "")
    [[ -n "$cf_ray" ]]
}

echo -e "\n${YELLOW}🔍 Starting R2 Storage Security Validation Tests${NC}"

if [[ "$VALIDATE_ONLY" == "--validate-only" ]]; then
    echo -e "${YELLOW}⚠️ Running in validate-only mode - skipping live environment tests${NC}"
    echo -e "${BLUE}🔍 Performing configuration validation only${NC}"

    # 1. Configuration Validation Tests
    echo -e "\n${BLUE}📋 R2 Configuration Validation Tests${NC}"
    
    validate_check "R2 bucket name follows naming convention" "
        [[ '$R2_BUCKET_NAME' =~ ^a2a-platform-web-assets-(staging|production)$ ]]
    " "R2 bucket name should follow convention"

    validate_check "R2 custom domain follows naming convention" "
        if [[ '$ENVIRONMENT' == 'production' ]]; then
            [[ '$R2_CUSTOM_DOMAIN' == 'assets.vedavivi.app' ]]
        else
            [[ '$R2_CUSTOM_DOMAIN' == 'assets-staging.vedavivi.app' ]]
        fi
    " "R2 custom domain should match environment"

    validate_check "CORS origins configuration is set" "
        [[ -n '$EXPECTED_CORS_ORIGINS' ]]
    " "CORS origins should be configured"

else
    # Live environment tests
    echo -e "${BLUE}🌐 Running live R2 storage security tests${NC}"

    # 1. R2 Custom Domain Tests
    echo -e "\n${BLUE}🌐 R2 Custom Domain Tests${NC}"
    
    validate_check "R2 custom domain accessibility" "
        check_http_status 'https://$R2_CUSTOM_DOMAIN' '200'
    " "R2 custom domain should be accessible"

    validate_check "R2 custom domain SSL certificate" "
        check_ssl_cert '$R2_CUSTOM_DOMAIN'
    " "R2 custom domain should have valid SSL certificate"

    validate_check "R2 custom domain Cloudflare integration" "
        check_cloudflare_headers 'https://$R2_CUSTOM_DOMAIN'
    " "R2 custom domain should have Cloudflare headers"

    # 2. R2 Security Configuration Tests
    echo -e "\n${BLUE}🔒 R2 Security Configuration Tests${NC}"

    # Test that direct R2 bucket access is restricted (should not be publicly accessible)
    validate_check "Direct R2 bucket access is restricted" "
        # Try to access bucket directly (should fail or redirect)
        status=\$(curl -s -o /dev/null -w '%{http_code}' 'https://$R2_BUCKET_NAME.r2.cloudflarestorage.com/' || echo '000')
        [[ \"\$status\" != '200' ]]
    " "Direct bucket access should be restricted"

    # 3. CORS Configuration Tests
    echo -e "\n${BLUE}🌐 CORS Configuration Tests${NC}"

    # Test CORS headers for allowed origins
    validate_check "CORS headers for allowed origins" "
        check_cors_headers 'https://$R2_CUSTOM_DOMAIN' '$EXPECTED_CORS_ORIGINS'
    " "CORS should be configured for allowed origins"

    # 4. Cache Configuration Tests
    echo -e "\n${BLUE}⚡ Cache Configuration Tests${NC}"

    # Test cache headers for static assets
    validate_check "Static asset cache headers" "
        # Test with a common static asset path
        check_cache_headers 'https://$R2_CUSTOM_DOMAIN/assets/index.js' 'max-age'
    " "Static assets should have cache headers"

    # 5. Content Security Tests
    echo -e "\n${BLUE}🛡️ Content Security Tests${NC}"

    # Test that sensitive files are not accessible
    validate_check "Sensitive files are not accessible" "
        # Test common sensitive file patterns
        status_env=\$(curl -s -o /dev/null -w '%{http_code}' 'https://$R2_CUSTOM_DOMAIN/.env' || echo '000')
        status_git=\$(curl -s -o /dev/null -w '%{http_code}' 'https://$R2_CUSTOM_DOMAIN/.git/config' || echo '000')
        [[ \"\$status_env\" == '404' ]] && [[ \"\$status_git\" == '404' ]]
    " "Sensitive files should return 404"

    # Test directory listing is disabled
    validate_check "Directory listing is disabled" "
        # Test if directory listing is disabled
        response=\$(curl -s 'https://$R2_CUSTOM_DOMAIN/' || echo '')
        ! echo \"\$response\" | grep -qi 'index of'
    " "Directory listing should be disabled"
fi

# 6. Environment-Specific Security Tests
echo -e "\n${BLUE}🏷️ Environment-Specific Security Tests${NC}"

if [[ "$ENVIRONMENT" == "production" ]]; then
    validate_check "Production environment security baseline" "
        # Production should have stricter security
        [[ '$R2_CUSTOM_DOMAIN' == 'assets.vedavivi.app' ]] && [[ '$EXPECTED_CORS_ORIGINS' == 'https://vedavivi.app' ]]
    " "Production should use production domains"
else
    validate_check "Staging environment security baseline" "
        # Staging should use staging domains
        [[ '$R2_CUSTOM_DOMAIN' == 'assets-staging.vedavivi.app' ]] && [[ '$EXPECTED_CORS_ORIGINS' == 'https://staging.vedavivi.app' ]]
    " "Staging should use staging domains"
fi

# Summary
echo -e "\n${BLUE}=================================================================="
echo -e "📊 R2 Storage Security Validation Summary${NC}"
echo -e "=================================================================="
echo -e "${GREEN}✅ Passed: $PASSED tests${NC}"
echo -e "${RED}❌ Failed: $FAILED tests${NC}"
echo -e "Total: $((PASSED + FAILED)) tests"

if [[ $FAILED -eq 0 ]]; then
    echo -e "\n${GREEN}🎉 All R2 storage security validation tests passed!${NC}"
    echo -e "${GREEN}✅ R2 storage is properly secured for $ENVIRONMENT environment${NC}"
    
    # Additional security recommendations
    echo -e "\n${BLUE}💡 Security Recommendations:${NC}"
    echo -e "  • Regularly rotate R2 access keys"
    echo -e "  • Monitor R2 access logs for unusual activity"
    echo -e "  • Keep CORS configuration minimal and specific"
    echo -e "  • Use custom domain instead of direct R2 URLs"
    echo -e "  • Implement proper cache headers for performance"
    
    exit 0
else
    echo -e "\n${RED}⚠️ Some R2 storage security validation tests failed!${NC}"
    echo -e "${RED}❌ Please review and fix the security issues before proceeding${NC}"
    
    echo -e "\n${YELLOW}💡 Common fixes:${NC}"
    echo -e "  • Check R2 bucket CORS configuration in Cloudflare dashboard"
    echo -e "  • Verify custom domain is properly configured"
    echo -e "  • Ensure SSL certificate is valid and not expired"
    echo -e "  • Review R2 bucket access policies"
    echo -e "  • Check Cloudflare security settings"
    
    exit 1
fi
