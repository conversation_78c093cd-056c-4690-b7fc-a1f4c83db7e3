#!/bin/bash
set -euo pipefail

# Database seeding script
# Seeds the database with test/development data

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Check if we're in a valid environment
if [[ "${POSTGRES_DB:-}" == *"production"* ]] || [[ "${DATABASE_URL:-}" == *"production"* ]]; then
    echo "❌ ERROR: Cannot seed production database"
    exit 1
fi

echo "🌱 Seeding database with test data..."

# Determine what to seed based on arguments
SEED_TYPE="${1:-all}"

case "$SEED_TYPE" in
    "users"|"user")
        echo "👥 Seeding user data..."
        docker compose exec backend python apps/backend/scripts/create_test_user.py
        ;;
    "agents"|"agent")
        echo "🤖 Seeding agent data..."
        docker compose exec backend python apps/backend/scripts/register_agent.py
        ;;
    "all"|"")
        echo "📦 Seeding all data..."
        docker compose exec backend python apps/backend/scripts/create_test_user.py
        docker compose exec backend python apps/backend/scripts/register_agent.py
        ;;
    "help"|"--help"|"-h")
        echo "Usage: $0 [type]"
        echo ""
        echo "Types:"
        echo "  users, user   - Seed user data only"
        echo "  agents, agent - Seed agent data only"
        echo "  all           - Seed all data (default)"
        echo "  help          - Show this help"
        ;;
    *)
        echo "❌ Unknown seed type: $SEED_TYPE"
        echo "Run '$0 help' for usage information"
        exit 1
        ;;
esac

echo "✅ Database seeding complete"
