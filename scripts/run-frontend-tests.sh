#!/bin/bash
#
# A2A Platform - Frontend Tests Runner
#
# This script runs frontend tests using Je<PERSON> and <PERSON><PERSON>.
# It supports running unit tests or e2e tests with various options.
# The script automatically loads test environment variables from .env.test if present.
#
# Usage:
#   ./scripts/run-frontend-tests.sh [OPTIONS]
#
# Options:
#   --unit          Run unit tests (default if no option specified)
#   --e2e           Run end-to-end tests with Cypress
#   --e2e-open      Open Cypress test runner UI
#   --watch         Run tests in watch mode (unit tests only)
#   --coverage      Run tests with coverage report (unit tests only)
#   --help          Display this help message
#
# Examples:
#   ./scripts/run-frontend-tests.sh             # Run unit tests
#   ./scripts/run-frontend-tests.sh --e2e       # Run e2e tests
#   ./scripts/run-frontend-tests.sh --watch     # Run unit tests in watch mode
#
# Environment Variables:
#   The script automatically loads test environment variables from .env.test
#   in the project root if the file exists. This includes variables like
#   REDIS_URL, CLERK_API_KEY, VITE_GRAPHQL_API_URL, etc.

set -e

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
FRONTEND_DIR="${PROJECT_ROOT}/apps/web"

# Load test environment variables if .env.test exists
ENV_TEST_FILE="${PROJECT_ROOT}/.env.test"
if [[ -f "${ENV_TEST_FILE}" ]]; then
  echo "Loading test environment variables from .env.test..."
  # Export variables from .env.test, handling multi-line values and comments
  set -a  # Automatically export all variables
  source "${ENV_TEST_FILE}"
  set +a  # Stop automatically exporting
  echo "Test environment variables loaded successfully."
else
  echo "No .env.test file found at ${ENV_TEST_FILE}. Proceeding without loading test environment variables."
fi

# Default values
RUN_UNIT=true
RUN_E2E=false
RUN_E2E_OPEN=false
RUN_WATCH=false
RUN_COVERAGE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    --unit)
      RUN_UNIT=true
      RUN_E2E=false
      RUN_E2E_OPEN=false
      shift
      ;;
    --e2e)
      RUN_UNIT=false
      RUN_E2E=true
      RUN_E2E_OPEN=false
      shift
      ;;
    --e2e-open)
      RUN_UNIT=false
      RUN_E2E=false
      RUN_E2E_OPEN=true
      shift
      ;;
    --watch)
      RUN_WATCH=true
      shift
      ;;
    --coverage)
      RUN_COVERAGE=true
      shift
      ;;
    --help)
      grep '^#' "$0" | grep -v '#!/bin/bash' | sed 's/^# \?//'
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

# Check if we're in the frontend directory
if [[ ! -d "${FRONTEND_DIR}" ]]; then
  echo "Error: Frontend directory not found at ${FRONTEND_DIR}"
  exit 1
fi

# Change to frontend directory
cd "${FRONTEND_DIR}"
echo "Changed to directory: $(pwd)"

# Check if bun is installed
if command -v bun &> /dev/null; then
  PACKAGE_MANAGER="bun"
# Check standard Bun installation path if not in PATH
elif [[ -x "$HOME/.bun/bin/bun" ]]; then
  export PATH="$HOME/.bun/bin:$PATH"
  PACKAGE_MANAGER="bun"
# Check for Bun via asdf
elif command -v asdf &> /dev/null && asdf which bun &> /dev/null; then
  PACKAGE_MANAGER="bun" # asdf shims will handle the path
else
  echo "Warning: Bun is not installed or not in PATH (checked command, $HOME/.bun/bin, and asdf)."
  echo "Using npm as fallback. For better performance, install Bun: https://bun.sh/"
  PACKAGE_MANAGER="npm"
fi

# Build test command based on options
if [[ "${RUN_UNIT}" == true ]]; then
  TEST_CMD="${PACKAGE_MANAGER} run test"

  if [[ "${RUN_WATCH}" == true ]]; then
    TEST_CMD="${PACKAGE_MANAGER} run test:watch"
  fi

  if [[ "${RUN_COVERAGE}" == true ]]; then
    # Add coverage flag to Jest
    TEST_CMD="${PACKAGE_MANAGER} run test -- --coverage"
  fi

  echo "Running unit tests..."
elif [[ "${RUN_E2E}" == true ]]; then
  TEST_CMD="${PACKAGE_MANAGER} run test:e2e"
  echo "Running end-to-end tests..."
elif [[ "${RUN_E2E_OPEN}" == true ]]; then
  TEST_CMD="${PACKAGE_MANAGER} run test:e2e:open"
  echo "Opening Cypress test runner..."
fi

# Run the tests
echo "Running command: ${TEST_CMD}"
if ! bash -c "${TEST_CMD}"; then
  echo "Tests failed!"
  exit 1
fi

echo "Tests completed successfully!"
exit 0
