#!/bin/bash
#
# A2A Platform - Redis Queue (RQ) Tests Runner
#
# This script runs the Redis Queue (RQ) specific tests with proper environment setup.
# It supports running in both Docker and CI environments.
#
# Usage:
#   ./scripts/run-rq-tests.sh [OPTIONS]
#
# Options:
#   --ci            Run tests in CI environment (non-Docker)
#   --reduced-load  Run performance tests with reduced load for CI
#   --verbose       Run tests with verbose output
#   --help          Display this help message
#
# Examples:
#   ./scripts/run-rq-tests.sh                # Run all RQ tests in Docker (default)
#   ./scripts/run-rq-tests.sh --ci           # Run all RQ tests in CI environment
#   ./scripts/run-rq-tests.sh --reduced-load # Run with reduced load for performance tests
#   ./scripts/run-rq-tests.sh --verbose      # Run with verbose output

set -e

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
BACKEND_DIR="${PROJECT_ROOT}/apps/backend"
ENV_TEST_FILE="${PROJECT_ROOT}/.env.test"

# Default values
RUN_CI=false
REDUCED_LOAD=false
VERBOSE=false
RUN_DOCKER=true  # Docker is the default

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    --ci)
      RUN_CI=true
      RUN_DOCKER=false  # CI mode disables Docker
      shift
      ;;
    --reduced-load)
      REDUCED_LOAD=true
      shift
      ;;
    --verbose)
      VERBOSE=true
      shift
      ;;
    --help)
      grep '^#' "$0" | grep -v '#!/bin/bash' | sed 's/^# \?//'
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

# If Docker mode is enabled (default), run tests in Docker
if [[ "${RUN_DOCKER}" == true ]]; then
  echo "Running RQ tests in Docker mode..."

  # Check for required environment variables
  if [[ -z "${DATABASE_URL}" ]]; then
    echo "Error: DATABASE_URL environment variable is not set"
    echo "Please set DATABASE_URL before running this script"
    exit 1
  fi

  if [[ -z "${REDIS_URL}" ]]; then
    echo "Error: REDIS_URL environment variable is not set"
    echo "Please set REDIS_URL before running this script"
    exit 1
  fi

  # Ensure Redis and DB are running
  echo "Ensuring Redis and database containers are running..."
  if ! docker compose -f docker-compose.test.yml ps | grep -q "redis.*Up"; then
    echo "Starting Redis container using test configuration..."
    docker compose -f docker-compose.test.yml up -d redis
  fi

  if ! docker compose -f docker-compose.test.yml ps | grep -q "db.*Up"; then
    echo "Starting database container using test configuration..."
    docker compose -f docker-compose.test.yml up -d db

    # Wait for the database to be ready
    echo "Waiting for database to be ready..."
    for i in {1..30}; do
      if docker compose -f docker-compose.test.yml exec db pg_isready -U postgres > /dev/null 2>&1; then
        echo "Database is ready."
        break
      fi
      echo "Database not ready yet. Retrying in 1 second..."
      sleep 1
    done
    if [[ $i -eq 30 ]]; then
      echo "Error: Database did not become ready in time."
      exit 1
    fi
  fi

  # Build the Docker command
  DOCKER_CMD="docker compose -f docker-compose.test.yml run --rm -T backend sh -c"

  # Install psutil for performance tests
  echo "Installing psutil for performance tests..."
  $DOCKER_CMD "pip install psutil"

  # Set up environment variables
  ENV_VARS=""
  if [[ "${REDUCED_LOAD}" == true ]]; then
    ENV_VARS="RQ_TEST_REDUCED_LOAD=true"
  fi

  # Make sure environment variables are exported if .env.test exists
  if [[ -f "${ENV_TEST_FILE}" ]]; then
    export $(grep -v '^#' "${ENV_TEST_FILE}" | xargs)
  fi

  # Run the RQ tests
  echo "Running RQ tests in Docker..."


  # Run the worker test first to ensure the monkey patch works
  echo "Running RQ worker test..."
  $DOCKER_CMD "${ENV_VARS} python test_rq_worker.py"

  # Run the message processing test
  echo "Running RQ message processing test..."
  $DOCKER_CMD "${ENV_VARS} python test_rq_message_processing.py"

  # Run the client direct test
  echo "Running RQ client direct test..."
  $DOCKER_CMD "${ENV_VARS} python test_rq_client_direct.py"

  # Run the edge cases test
  echo "Running RQ edge cases test..."
  $DOCKER_CMD "${ENV_VARS} python test_rq_edge_cases.py"

  # Run the security test
  echo "Running RQ security test..."
  $DOCKER_CMD "${ENV_VARS} python test_rq_security.py"

  # Run the performance test last as it's the most resource-intensive
  echo "Running RQ performance test..."
  $DOCKER_CMD "${ENV_VARS} python test_rq_performance.py"

  echo "All RQ tests completed successfully in Docker!"
  exit 0
fi

# If we get here, we're running in CI mode (non-Docker)
echo "Running RQ tests in CI mode..."

# Change to backend directory
cd "${BACKEND_DIR}"
echo "Changed to directory: $(pwd)"

# Set up environment variables for tests
export PYTHONPATH="${BACKEND_DIR}/src:${PYTHONPATH}"

# Load environment variables from .env.test if it exists
if [[ -f "${ENV_TEST_FILE}" ]]; then
  echo "Loading environment variables from ${ENV_TEST_FILE}"
  export $(grep -v '^#' "${ENV_TEST_FILE}" | xargs)
fi

# Check for required environment variables
if [[ -z "${DATABASE_URL}" ]]; then
  echo "Error: DATABASE_URL environment variable is not set"
  echo "Please set DATABASE_URL before running this script"
  exit 1
fi

if [[ -z "${REDIS_URL}" ]]; then
  echo "Error: REDIS_URL environment variable is not set"
  echo "Please set REDIS_URL before running this script"
  exit 1
fi

# Set CI environment variable
export CI=true

# Set reduced load flag if needed
if [[ "${REDUCED_LOAD}" == true ]]; then
  export RQ_TEST_REDUCED_LOAD=true
fi

# Install psutil for performance tests
echo "Installing psutil for performance tests..."
pip install psutil

# Run the RQ tests
echo "Running RQ tests in CI mode..."

# Run the worker test first to ensure the monkey patch works
echo "Running RQ worker test..."
python test_rq_worker.py

# Run the message processing test
echo "Running RQ message processing test..."
python test_rq_message_processing.py

# Run the client direct test
echo "Running RQ client direct test..."
python test_rq_client_direct.py

# Run the edge cases test
echo "Running RQ edge cases test..."
python test_rq_edge_cases.py

# Run the security test
echo "Running RQ security test..."
python test_rq_security.py

# Run the performance test last as it's the most resource-intensive
echo "Running RQ performance test..."
python test_rq_performance.py

echo "All RQ tests completed successfully in CI mode!"
exit 0
