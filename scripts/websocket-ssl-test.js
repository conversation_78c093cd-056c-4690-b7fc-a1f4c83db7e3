/**
 * WebSocket SSL Test Script
 * 
 * This script tests WebSocket connections to ensure they use WSS protocol
 * and validates certificate security.
 */
const WebSocket = require('ws');
const https = require('https');
const fs = require('fs');
const path = require('path');
const util = require('util');

// Configuration
const config = {
  // Domain to test (should match your environment)
  domain: process.env.WS_TEST_DOMAIN || 'localhost:8000',
  
  // Endpoint path
  path: process.env.WS_TEST_PATH || '/graphql',
  
  // Test both secure and insecure connections
  testWs: process.env.TEST_WS !== 'false',
  testWss: process.env.TEST_WSS !== 'false',
  
  // SSL options for WSS connections
  ssl: {
    // For local testing with self-signed certificates
    rejectUnauthorized: process.env.REJECT_UNAUTHORIZED !== 'false',
    
    // If using local certificates
    ca: process.env.SSL_CA_PATH ? fs.readFileSync(process.env.SSL_CA_PATH) : undefined,
  },
  
  // Timeout for connection attempts (ms)
  connectionTimeout: process.env.CONNECTION_TIMEOUT || 5000,
};

// GraphQL subscription message
const subscriptionMessage = {
  type: 'connection_init',
  payload: {},
};

// Test results
const results = {
  ws: { success: false, error: null, response: null },
  wss: { success: false, error: null, response: null },
};

/**
 * Test a WebSocket connection
 * @param {string} protocol - 'ws' or 'wss'
 * @returns {Promise<Object>} - Connection result
 */
async function testConnection(protocol) {
  return new Promise((resolve) => {
    console.log(`Testing ${protocol}://${config.domain}${config.path}...`);
    
    // Timeout handler
    const timeout = setTimeout(() => {
      if (ws.readyState !== WebSocket.CLOSED) {
        ws.terminate();
        resolve({
          success: false,
          error: 'Connection timeout',
          response: null,
        });
      }
    }, config.connectionTimeout);
    
    // Options for WSS
    const options = protocol === 'wss' ? {
      rejectUnauthorized: config.ssl.rejectUnauthorized,
      ca: config.ssl.ca,
    } : {};
    
    // Create WebSocket connection
    const ws = new WebSocket(`${protocol}://${config.domain}${config.path}`, options);
    
    // Connection opened
    ws.on('open', () => {
      console.log(`${protocol.toUpperCase()} connection established`);
      
      // Send subscription message
      ws.send(JSON.stringify(subscriptionMessage));
    });
    
    // Handle messages
    ws.on('message', (data) => {
      clearTimeout(timeout);
      
      try {
        const response = JSON.parse(data.toString());
        console.log(`${protocol.toUpperCase()} received response:`, response);
        
        ws.close();
        resolve({
          success: true,
          error: null,
          response,
        });
      } catch (err) {
        ws.close();
        resolve({
          success: true,
          error: null,
          response: data.toString(),
        });
      }
    });
    
    // Handle errors
    ws.on('error', (error) => {
      clearTimeout(timeout);
      console.error(`${protocol.toUpperCase()} connection error:`, error.message);
      
      resolve({
        success: false,
        error: error.message,
        response: null,
      });
    });
    
    // Handle connection closed
    ws.on('close', (code, reason) => {
      clearTimeout(timeout);
      
      if (!results[protocol].success && !results[protocol].error) {
        console.log(`${protocol.toUpperCase()} connection closed: ${code} - ${reason || 'No reason'}`);
        resolve({
          success: false,
          error: `Connection closed: ${code} - ${reason || 'No reason'}`,
          response: null,
        });
      }
    });
  });
}

/**
 * Run all tests and print results
 */
async function runTests() {
  console.log('Starting WebSocket Security Tests');
  console.log('=================================');
  console.log('Configuration:', util.inspect(config, { depth: null, colors: true }));
  console.log('---------------------------------');
  
  // Test WS (insecure)
  if (config.testWs) {
    results.ws = await testConnection('ws');
    console.log('---------------------------------');
  } else {
    console.log('Skipping WS test');
  }
  
  // Test WSS (secure)
  if (config.testWss) {
    results.wss = await testConnection('wss');
    console.log('---------------------------------');
  } else {
    console.log('Skipping WSS test');
  }
  
  // Print summary
  console.log('Test Results Summary:');
  console.log('=================================');
  
  if (config.testWs) {
    console.log('WS (insecure):', results.ws.success ? 'SUCCESS' : 'FAILED');
    if (results.ws.error) console.log('  Error:', results.ws.error);
  }
  
  if (config.testWss) {
    console.log('WSS (secure):', results.wss.success ? 'SUCCESS' : 'FAILED');
    if (results.wss.error) console.log('  Error:', results.wss.error);
  }
  
  // Validate security
  console.log('---------------------------------');
  console.log('Security Validation:');
  
  // For proper security, WS should fail and WSS should succeed
  if (config.testWs && config.testWss) {
    if (!results.ws.success && results.wss.success) {
      console.log('✅ SECURE: WS connections are properly rejected while WSS connections work');
    } else if (results.ws.success) {
      console.log('❌ INSECURE: WS connections are accepted, which is a security risk');
    } else if (!results.wss.success) {
      console.log('❌ ERROR: WSS connections are not working properly');
    }
  } else if (config.testWss && results.wss.success) {
    console.log('✅ SECURE: WSS connections are working properly');
  } else if (config.testWs && !results.ws.success) {
    console.log('✅ SECURE: WS connections are properly rejected');
  }
  
  // Exit with appropriate status code
  if ((!config.testWs || !results.ws.success) && 
      (config.testWss && results.wss.success)) {
    console.log('✅ All security tests passed!');
    process.exit(0);
  } else {
    console.log('❌ Security tests failed!');
    process.exit(1);
  }
}

// Run the tests
runTests().catch(error => {
  console.error('Test execution error:', error);
  process.exit(1);
});
