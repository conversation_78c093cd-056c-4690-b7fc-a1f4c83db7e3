#!/bin/bash
# Script to initialize Terraform backend in GCS

set -e

# Check if project ID is provided
if [ -z "$1" ]; then
    echo "Usage: $0 <gcp-project-id>"
    exit 1
fi

PROJECT_ID=$1
BUCKET_NAME="a2a-platform-terraform-state"
REGION="us-central1"

# Check if gcloud is authenticated
echo "Checking gcloud authentication..."
gcloud auth list --filter=status:ACTIVE --format="value(account)" || {
    echo "Error: No active gcloud account found. Please run 'gcloud auth login' first."
    exit 1
}

# Set the current project
echo "Setting project to $PROJECT_ID..."
gcloud config set project $PROJECT_ID

# Check if bucket already exists
if gsutil ls -b "gs://$BUCKET_NAME" > /dev/null 2>&1; then
    echo "Bucket $BUCKET_NAME already exists."
else
    # Create the bucket with versioning enabled
    echo "Creating bucket $BUCKET_NAME in $REGION..."
    gsutil mb -l $REGION "gs://$BUCKET_NAME"
    
    # Enable versioning
    echo "Enabling versioning for $BUCKET_NAME..."
    gsutil versioning set on "gs://$BUCKET_NAME"
    
    # Set lifecycle policy (optional)
    echo "Setting lifecycle policy for older versions..."
    cat > /tmp/lifecycle.json << EOF
{
  "rule": [
    {
      "action": {"type": "Delete"},
      "condition": {"numNewerVersions": 10}
    }
  ]
}
EOF
    gsutil lifecycle set /tmp/lifecycle.json "gs://$BUCKET_NAME"
    rm /tmp/lifecycle.json
fi

echo "Terraform state bucket setup complete!"
echo "Use the following backend configuration in your Terraform files:"
echo ""
echo "terraform {"
echo "  backend \"gcs\" {"
echo "    bucket = \"$BUCKET_NAME\""
echo "    prefix = \"environment-name\""
echo "  }"
echo "}"
