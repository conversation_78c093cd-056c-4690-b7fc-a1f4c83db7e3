#!/bin/bash
# filepath: /Users/<USER>/dev/blkops-collective/a2a-platform/scripts/run-https-tests.sh
#
# Script to run end-to-end tests with HTTPS enabled
# This script will start the development environment with HTTPS, run the tests,
# and then shut down the environment.

set -e

# Define colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Define paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(dirname "$SCRIPT_DIR")"
SSL_CERTS_DIR="${ROOT_DIR}/ssl-certs"

# Check if certificates exist, generate them if they don't
if [ ! -f "${SSL_CERTS_DIR}/localhost.pem" ] || [ ! -f "${SSL_CERTS_DIR}/localhost-key.pem" ]; then
    echo -e "${YELLOW}SSL certificates not found. Generating certificates...${NC}"
    ${SCRIPT_DIR}/generate-ssl-certs.sh
fi

# Create a temporary .env file for HTTPS testing
ENV_FILE="${ROOT_DIR}/.env.https-test"
cp "${ROOT_DIR}/.env" "${ENV_FILE}" 2>/dev/null || touch "${ENV_FILE}"

# Enable HTTPS in the environment
echo -e "${YELLOW}Configuring environment for HTTPS testing...${NC}"
echo "ENFORCE_HTTPS=true" >> "${ENV_FILE}"
echo "VITE_USE_HTTPS=true" >> "${ENV_FILE}"

# Start the development environment with HTTPS enabled
echo -e "${YELLOW}Starting development environment with HTTPS enabled...${NC}"
docker compose -f "${ROOT_DIR}/docker-compose.dev.yml" --env-file "${ENV_FILE}" down
docker compose -f "${ROOT_DIR}/docker-compose.dev.yml" --env-file "${ENV_FILE}" up -d

# Wait for services to be ready
echo -e "${YELLOW}Waiting for services to be ready...${NC}"
attempts=0
max_attempts=30
while ! curl -k -s https://localhost:5173 > /dev/null; do
    attempts=$((attempts+1))
    if [ $attempts -ge $max_attempts ]; then
        echo -e "${RED}Services failed to start within the expected time.${NC}"
        docker compose -f "${ROOT_DIR}/docker-compose.dev.yml" --env-file "${ENV_FILE}" down
        rm "${ENV_FILE}"
        exit 1
    fi
    echo "Waiting for frontend to be ready... ($attempts/$max_attempts)"
    sleep 2
done

echo -e "${GREEN}Services are ready!${NC}"

# Run the HTTPS enforcement tests
echo -e "${YELLOW}Running HTTPS enforcement tests...${NC}"
cd "${ROOT_DIR}/apps/web"
CYPRESS_baseUrl=https://localhost:5173 npx cypress run --spec "cypress/e2e/https-enforcement.cy.ts" --config video=false

# Get the test result
TEST_RESULT=$?

# Clean up
echo -e "${YELLOW}Cleaning up...${NC}"
docker compose -f "${ROOT_DIR}/docker-compose.dev.yml" --env-file "${ENV_FILE}" down
rm "${ENV_FILE}"

# Return the test result
if [ $TEST_RESULT -eq 0 ]; then
    echo -e "${GREEN}HTTPS enforcement tests passed!${NC}"
    exit 0
else
    echo -e "${RED}HTTPS enforcement tests failed.${NC}"
    exit $TEST_RESULT
fi
