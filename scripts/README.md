# Scripts Directory

This directory contains utility scripts for the A2A Platform development and deployment workflows.

## Development & Environment Scripts

- **[app-runner.sh](./app-runner.sh)** - Manages Docker containers for the A2A Platform using docker compose
- **[dev.sh](./dev.sh)** - Starts the A2A Platform in development mode with hot reloading
- **[setup-dev-env.sh](./setup-dev-env.sh)** - Installs development tools and sets up pre-commit hooks
- **[setup-deployment-testing.sh](./setup-deployment-testing.sh)** - Sets up environment for deployment testing

## Testing Scripts

- **[run-backend-tests.sh](./run-backend-tests.sh)** - Runs backend tests with proper environment setup (Docker default)
- **[run-frontend-tests.sh](./run-frontend-tests.sh)** - Runs frontend tests with environment configuration
- **[run-docker-tests.sh](./run-docker-tests.sh)** - Executes tests in Docker environment
- **[run-rq-tests.sh](./run-rq-tests.sh)** - Runs Redis Queue worker tests
- **[run-webhook-tests.sh](./run-webhook-tests.sh)** - Tests webhook functionality
- **[run-https-tests.sh](./run-https-tests.sh)** - Tests HTTPS implementation and SSL configuration
- **[run-tests-with-env.sh](./run-tests-with-env.sh)** - Runs tests with specific environment variables
- **[run-full-check.sh](./run-full-check.sh)** - Comprehensive test suite runner
- **[run-act.sh](./run-act.sh)** - Runs GitHub Actions locally using act

## Database Scripts

- **[db-migrate.sh](./db-migrate.sh)** - Runs database migrations using Alembic through Docker Compose
- **[db-reset.sh](./db-reset.sh)** - Resets the database to a clean state
- **[setup-test-db.sh](./setup-test-db.sh)** - Sets up test database with proper schema
- **[reset-test-db.sh](./reset-test-db.sh)** - Resets test database to clean state

## Code Quality & Pre-commit Scripts

- **[precommit-check.sh](./precommit-check.sh)** - Runs pre-commit checks, backend tests, and frontend tests sequentially

## Security Scripts

- **[run-security-review.sh](./run-security-review.sh)** - Comprehensive security review process for HTTPS implementation
- **[security-review-checklist.sh](./security-review-checklist.sh)** - Security review checklist and validation
- **[check-security-headers.sh](./check-security-headers.sh)** - Validates HTTP security headers
- **[terraform-security-check.sh](./terraform-security-check.sh)** - Security validation for Terraform configurations
- **[mark-security-review-passed.sh](./mark-security-review-passed.sh)** - Marks security review as passed in CI

## SSL/TLS & Certificate Scripts

- **[generate-ssl-certs.sh](./generate-ssl-certs.sh)** - Generates self-signed SSL certificates for local HTTPS development
- **[monitor-ssl-certificates.sh](./monitor-ssl-certificates.sh)** - Monitors SSL certificate status and expiration
- **[ssl-performance-test.sh](./ssl-performance-test.sh)** - Tests SSL/TLS performance and configuration
- **[validate-https-implementation.sh](./validate-https-implementation.sh)** - Validates HTTPS implementation completeness
- **[websocket-ssl-test.js](./websocket-ssl-test.js)** - Tests WebSocket connections over SSL

## Deployment & Infrastructure Scripts

- **[create-vpc-connector.sh](./create-vpc-connector.sh)** - Creates VPC connector for Google Cloud deployment
- **[grant-run-invoker.sh](./grant-run-invoker.sh)** - Grants Cloud Run invoker permissions
- **[diagnose-deploy-errors.sh](./diagnose-deploy-errors.sh)** - Diagnoses deployment errors and provides solutions

## Utility Scripts

- **[fix-web-permissions.sh](./fix-web-permissions.sh)** - Fixes file permissions for web container
- **[manage-rq-workers.sh](./manage-rq-workers.sh)** - Manages Redis Queue worker processes
- **[create-yamllint-config.sh](./create-yamllint-config.sh)** - Creates yamllint configuration
- **[get-branch-diff.py](./get-branch-diff.py)** - Python script to get branch differences
- **[get-branch-diff.sh](./get-branch-diff.sh)** - Shell script to get branch differences
- **[demo_pa_messages.py](./demo_pa_messages.py)** - Demo script for personal agent messages
- **[storybook.sh](./storybook.sh)** - Manages Storybook development server (see [Storybook Guide](../docs/storybook.md))
- **[download-storybook.sh](./download-storybook.sh)** - Downloads and serves Storybook artifacts from CI/CD builds

## Usage Notes

- Most scripts should be run from the project root directory
- Scripts use Docker Compose for consistent environments
- Backend tests require the scripts to be run from `apps/backend` context
- Environment variables are loaded from `.env` files when needed
- Use `--help` flag with most scripts for detailed usage information

## Common Workflows

```bash
# Set up development environment
./scripts/setup-dev-env.sh

# Start development servers
./scripts/dev.sh up

# Run all checks before committing
./scripts/precommit-check.sh

# Run specific test suites
./scripts/run-backend-tests.sh
./scripts/run-frontend-tests.sh

# Database operations
./scripts/db-migrate.sh upgrade
./scripts/db-reset.sh
```
