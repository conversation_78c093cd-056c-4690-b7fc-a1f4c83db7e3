#!/bin/bash
set -euo pipefail

# Claude Commands Security Validation Script
# Tests security aspects of <PERSON> commands without requiring Python

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
COMMANDS_DIR="$PROJECT_ROOT/.claude/commands"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Counters
TESTS_RUN=0
TESTS_PASSED=0
TESTS_FAILED=0

# Helper functions
log_test() {
    echo -e "${YELLOW}[TEST]${NC} $1"
    ((TESTS_RUN++))
}

log_pass() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((TESTS_PASSED++))
}

log_fail() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((TESTS_FAILED++))
}

log_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

# Check if commands directory exists
if [[ ! -d "$COMMANDS_DIR" ]]; then
    echo -e "${RED}ERROR:${NC} Commands directory not found: $COMMANDS_DIR"
    exit 1
fi

echo "🔍 Validating Claude commands security..."
echo "Commands directory: $COMMANDS_DIR"
echo

# Test 1: Check for dangerous shell injection patterns
test_no_shell_injection() {
    log_test "Checking for dangerous shell injection patterns"
    
    local failed=0
    local dangerous_patterns=(
        'eval.*\$ARGUMENTS'
        '`.*\$ARGUMENTS.*`'
        '\$\(.*\$ARGUMENTS.*\)'
    )
    
    for cmd_file in "$COMMANDS_DIR"/*.md; do
        [[ -f "$cmd_file" ]] || continue
        
        for pattern in "${dangerous_patterns[@]}"; do
            if grep -q "$pattern" "$cmd_file"; then
                log_fail "Dangerous pattern '$pattern' found in $(basename "$cmd_file")"
                failed=1
            fi
        done
    done
    
    if [[ $failed -eq 0 ]]; then
        log_pass "No dangerous shell injection patterns found"
    fi
    
    return $failed
}

# Test 2: Check that all referenced scripts exist
test_scripts_exist() {
    log_test "Checking that all referenced scripts exist"
    
    local failed=0
    local missing_scripts=()
    
    for cmd_file in "$COMMANDS_DIR"/*.md; do
        [[ -f "$cmd_file" ]] || continue
        
        # Find script references (./scripts/something.sh)
        while IFS= read -r line; do
            if [[ -n "$line" ]]; then
                script_path="$PROJECT_ROOT/$line"
                if [[ ! -f "$script_path" ]]; then
                    missing_scripts+=("$(basename "$cmd_file"):$line")
                    failed=1
                fi
            fi
        done < <(grep -o '\./scripts/[a-zA-Z0-9_-]*\.sh' "$cmd_file" 2>/dev/null || true)
        
        # Find Python script references  
        while IFS= read -r line; do
            if [[ -n "$line" ]]; then
                script_path="$PROJECT_ROOT/$line"
                if [[ ! -f "$script_path" ]]; then
                    missing_scripts+=("$(basename "$cmd_file"):$line")
                    failed=1
                fi
            fi
        done < <(grep -o 'python\s\+scripts/[a-zA-Z0-9_-]*\.py' "$cmd_file" 2>/dev/null | sed 's/python\s\+//' || true)
    done
    
    if [[ ${#missing_scripts[@]} -gt 0 ]]; then
        for missing in "${missing_scripts[@]}"; do
            log_fail "Missing script: $missing"
        done
    else
        log_pass "All referenced scripts exist"
    fi
    
    return $failed
}

# Test 3: Check security documentation for commands using $ARGUMENTS
test_security_documentation() {
    log_test "Checking security documentation for commands using \$ARGUMENTS"
    
    local failed=0
    local missing_docs=()
    
    for cmd_file in "$COMMANDS_DIR"/*.md; do
        [[ -f "$cmd_file" ]] || continue
        
        if grep -q '\$ARGUMENTS' "$cmd_file"; then
            if ! grep -q 'Security Note:' "$cmd_file"; then
                missing_docs+=("$(basename "$cmd_file")")
                failed=1
            fi
        fi
    done
    
    if [[ ${#missing_docs[@]} -gt 0 ]]; then
        for missing in "${missing_docs[@]}"; do
            log_fail "Security documentation missing in: $missing"
        done
    else
        log_pass "All commands using \$ARGUMENTS have security documentation"
    fi
    
    return $failed
}

# Test 4: Check for unsafe unquoted argument usage
test_no_unquoted_arguments() {
    log_test "Checking for potentially unsafe unquoted \$ARGUMENTS usage"
    
    local failed=0
    local risky_patterns=(
        'cd\s.*\$ARGUMENTS'
        '\brm\s.*\$ARGUMENTS'
    )
    
    for cmd_file in "$COMMANDS_DIR"/*.md; do
        [[ -f "$cmd_file" ]] || continue
        
        for pattern in "${risky_patterns[@]}"; do
            if grep -q "$pattern" "$cmd_file"; then
                log_fail "Potentially unsafe unquoted pattern '$pattern' in $(basename "$cmd_file")"
                failed=1
            fi
        done
    done
    
    if [[ $failed -eq 0 ]]; then
        log_pass "No unsafe unquoted \$ARGUMENTS patterns found"
    fi
    
    return $failed
}

# Test 5: Check that referenced scripts follow best practices
test_script_best_practices() {
    log_test "Checking that referenced scripts follow security best practices"
    
    local failed=0
    
    for script_file in "$PROJECT_ROOT"/scripts/*.sh; do
        [[ -f "$script_file" ]] || continue
        [[ "$(basename "$script_file")" == "validate-claude-commands.sh" ]] && continue  # Skip self
        
        # Check for set -e or set -euo pipefail
        if ! grep -q 'set -e' "$script_file"; then
            log_fail "Script $(basename "$script_file") should use 'set -e' or 'set -euo pipefail'"
            failed=1
        fi
        
        # Check for dangerous eval usage (excluding comments, 'evaluate', and controlled command execution)
        if grep -v '^[[:space:]]*#' "$script_file" | grep -v 'evaluate' | grep '\beval\b' | grep -v 'eval.*TEST_CMD' | grep -v 'eval.*CMD' | grep -v 'eval.*command' | grep -v 'eval.*".*"' > /dev/null; then
            log_fail "Script $(basename "$script_file") contains dangerous eval usage"
            failed=1
        fi
    done
    
    if [[ $failed -eq 0 ]]; then
        log_pass "All scripts follow security best practices"
    fi
    
    return $failed
}

# Test 6: Check that security guidelines document exists
test_security_guidelines_exist() {
    log_test "Checking that security guidelines document exists"
    
    local security_doc="$PROJECT_ROOT/.claude/SECURITY.md"
    local failed=0
    
    if [[ ! -f "$security_doc" ]]; then
        log_fail "Security guidelines document missing: $security_doc"
        return 1
    fi
    
    # Check for required sections
    local required_sections=(
        "Key Principles"
        "Security Patterns" 
        "Script Requirements"
        "Maintenance"
    )
    
    for section in "${required_sections[@]}"; do
        if ! grep -q "$section" "$security_doc"; then
            log_fail "Security document missing section: $section"
            failed=1
        fi
    done
    
    if [[ $failed -eq 0 ]]; then
        log_pass "Security guidelines document exists with required sections"
    fi
    
    return $failed
}

# Run all tests
echo "🧪 Running security validation tests..."
echo

test_no_shell_injection || true
test_scripts_exist || true
test_security_documentation || true
test_no_unquoted_arguments || true
test_script_best_practices || true
test_security_guidelines_exist || true

echo
echo "📊 Test Results:"
echo "  Tests run: $TESTS_RUN"
echo "  Passed: $TESTS_PASSED"
echo "  Failed: $TESTS_FAILED"

if [[ $TESTS_FAILED -gt 0 ]]; then
    echo -e "${RED}❌ Some security validation tests failed${NC}"
    exit 1
else
    echo -e "${GREEN}✅ All security validation tests passed${NC}"
    exit 0
fi