#!/bin/bash
#
# A2A Platform - Database Reset Script
#
# This script resets the PostgreSQL database to a clean state.
# It drops and recreates the database, preserving the schema but clearing all data.
#
# Usage:
#   ./scripts/db-reset.sh [OPTIONS]
#
# Options:
#   --docker        Use Docker PostgreSQL instance
#   --local         Use local PostgreSQL instance (default)
#   --test          Reset the test database instead of the development database
#   --force         Skip confirmation prompt
#   --help          Display this help message
#
# Examples:
#   ./scripts/db-reset.sh              # Reset local development database with confirmation
#   ./scripts/db-reset.sh --docker     # Reset Docker development database with confirmation
#   ./scripts/db-reset.sh --test       # Reset local test database with confirmation

set -e

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
BACKEND_DIR="${PROJECT_ROOT}/apps/backend"

# Default values
USE_DOCKER=false
USE_TEST_DB=false
SKIP_CONFIRMATION=false

# Database settings
DB_USER="postgres"
DB_PASSWORD="postgres"
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="a2a_platform"
DB_CONTAINER="a2a-postgres"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    --docker)
      USE_DOCKER=true
      shift
      ;;
    --local)
      USE_DOCKER=false
      shift
      ;;
    --test)
      USE_TEST_DB=true
      DB_NAME="a2a_platform_test"
      shift
      ;;
    --force)
      SKIP_CONFIRMATION=true
      shift
      ;;
    --help)
      grep '^#' "$0" | grep -v '#!/bin/bash' | sed 's/^# \?//'
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

# Set up the psql command based on whether we're using Docker or local
if [[ "${USE_DOCKER}" == true ]]; then
  PSQL_CMD="docker exec -it ${DB_CONTAINER} psql -U ${DB_USER}"
  echo "Using Docker PostgreSQL instance (${DB_CONTAINER})"
else
  PSQL_CMD="PGPASSWORD=${DB_PASSWORD} psql -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER}"
  echo "Using local PostgreSQL instance (${DB_HOST}:${DB_PORT})"
fi

# Check if we can connect to the database
echo "Testing database connection..."
if ! bash -c "${PSQL_CMD} -c 'SELECT 1' postgres" >/dev/null 2>&1; then
  echo "Error: Could not connect to PostgreSQL."
  if [[ "${USE_DOCKER}" == true ]]; then
    echo "Make sure the Docker container ${DB_CONTAINER} is running."
  else
    echo "Make sure PostgreSQL is running on ${DB_HOST}:${DB_PORT}."
  fi
  exit 1
fi

# Confirm database reset
if [[ "${SKIP_CONFIRMATION}" != true ]]; then
  echo ""
  echo "WARNING: This will reset the database '${DB_NAME}' and delete ALL data!"
  echo "The schema will be preserved through migrations, but all data will be lost."
  echo ""
  read -p "Are you sure you want to continue? (y/N) " -n 1 -r
  echo ""
  if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Operation cancelled."
    exit 0
  fi
fi

# Drop and recreate the database
echo "Dropping database ${DB_NAME}..."
bash -c "${PSQL_CMD} -c 'DROP DATABASE IF EXISTS ${DB_NAME}' postgres"

echo "Creating database ${DB_NAME}..."
bash -c "${PSQL_CMD} -c 'CREATE DATABASE ${DB_NAME}' postgres"

# Run migrations to restore schema
echo "Running migrations to restore schema..."
cd "${BACKEND_DIR}"

if [[ "${USE_DOCKER}" == true ]]; then
  # Run migrations using Docker
  if [[ "${USE_TEST_DB}" == true ]]; then
    docker run --rm --network a2a-network \
      -e DATABASE_URL="postgresql://postgres:postgres@${DB_CONTAINER}:5432/${DB_NAME}" \
      a2a-backend alembic upgrade head
  else
    docker run --rm --network a2a-network \
      -e DATABASE_URL="postgresql://postgres:postgres@${DB_CONTAINER}:5432/${DB_NAME}" \
      a2a-backend alembic upgrade head
  fi
else
  # Run migrations locally
  if [[ "${USE_TEST_DB}" == true ]]; then
    # Use test environment
    DATABASE_URL="postgresql://postgres:postgres@localhost:5432/${DB_NAME}" alembic upgrade head
  else
    # Use development environment
    alembic upgrade head
  fi
fi

echo ""
echo "Database '${DB_NAME}' has been reset successfully!"
echo "Schema has been restored using migrations."
echo ""

exit 0
