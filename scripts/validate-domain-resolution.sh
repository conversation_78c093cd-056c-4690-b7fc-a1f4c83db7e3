#!/bin/bash
# Domain Resolution and SSL Validation Script
# Validates all calculated URLs and SSL certificates for CDN migration
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT=${1:-staging}
REPORT_FILE=${2:-"domain-validation-report.json"}

echo -e "${BLUE}🌐 Domain Resolution Validation for environment: $ENVIRONMENT${NC}"
echo "=================================================================="

# Set environment-specific URLs based on CDN migration specification
if [[ "$ENVIRONMENT" == "production" ]]; then
    WEB_URL="https://vedavivi.app"
    API_URL="https://api.vedavivi.app"
    WS_URL="wss://ws.vedavivi.app"
    DOMAINS=("vedavivi.app" "api.vedavivi.app" "ws.vedavivi.app")
else
    WEB_URL="https://www-staging.vedavivi.app"
    API_URL="https://api-staging.vedavivi.app"
    WS_URL="wss://ws-staging.vedavivi.app"
    DOMAINS=("www-staging.vedavivi.app" "api-staging.vedavivi.app" "ws-staging.vedavivi.app")
fi

# Validation counters
PASSED=0
FAILED=0

# Function to run validation checks
validate_check() {
    local test_name="$1"
    local command="$2"
    local expected="$3"
    
    echo -e "\n${BLUE}Testing: $test_name${NC}"
    
    # Use bash -c instead of eval for better security isolation
    if bash -c "$command"; then
        echo -e "${GREEN}✅ PASSED: $test_name${NC}"
        ((PASSED++))
        return 0
    else
        echo -e "${RED}❌ FAILED: $test_name${NC}"
        echo -e "${RED}   Expected: $expected${NC}"
        ((FAILED++))
        return 1
    fi
}

# Function to check DNS resolution
check_dns_resolution() {
    local domain="$1"
    local record_type="${2:-A}"
    
    echo "  Checking DNS resolution for $domain ($record_type record)..."
    
    # Use dig to check DNS resolution
    local result=$(dig +short "$domain" "$record_type" 2>/dev/null | head -1)
    
    if [[ -n "$result" ]]; then
        echo "  Resolved to: $result"
        return 0
    else
        echo "  DNS resolution failed"
        return 1
    fi
}

# Function to check SSL certificate
check_ssl_certificate() {
    local domain="$1"
    local port="${2:-443}"
    
    echo "  Checking SSL certificate for $domain:$port..."
    
    # Get certificate information
    local cert_info=$(echo | openssl s_client -servername "$domain" -connect "$domain:$port" 2>/dev/null | openssl x509 -noout -dates -subject -issuer 2>/dev/null)
    
    if [[ -n "$cert_info" ]]; then
        echo "  Certificate found:"
        echo "$cert_info" | sed 's/^/    /'
        
        # Check if certificate is not expired
        local not_after=$(echo "$cert_info" | grep "notAfter" | cut -d= -f2)
        local expiry_epoch=$(date -d "$not_after" +%s 2>/dev/null || echo "0")
        local current_epoch=$(date +%s)
        
        if [[ "$expiry_epoch" -gt "$current_epoch" ]]; then
            echo "  Certificate is valid (expires: $not_after)"
            return 0
        else
            echo "  Certificate is expired or invalid"
            return 1
        fi
    else
        echo "  SSL certificate check failed"
        return 1
    fi
}

# Function to check HTTP/HTTPS response
check_http_response() {
    local url="$1"
    local expected_status="${2:-200}"
    
    echo "  Checking HTTP response for $url..."
    
    local response=$(curl -s -o /dev/null -w "%{http_code},%{time_total},%{time_starttransfer}" "$url" 2>/dev/null)
    local status_code=$(echo "$response" | cut -d, -f1)
    local total_time=$(echo "$response" | cut -d, -f2)
    local ttfb=$(echo "$response" | cut -d, -f3)
    
    echo "  Status: $status_code, Total time: ${total_time}s, TTFB: ${ttfb}s"
    
    if [[ "$status_code" == "$expected_status" ]]; then
        return 0
    else
        return 1
    fi
}

# Function to check HTTPS redirect
check_https_redirect() {
    local domain="$1"
    
    echo "  Checking HTTPS redirect for $domain..."
    
    local redirect_response=$(curl -s -o /dev/null -w "%{http_code},%{redirect_url}" "http://$domain" 2>/dev/null)
    local status_code=$(echo "$redirect_response" | cut -d, -f1)
    local redirect_url=$(echo "$redirect_response" | cut -d, -f2)
    
    echo "  HTTP status: $status_code, Redirect: $redirect_url"
    
    # Check if it's a redirect status and redirects to HTTPS
    if [[ "$status_code" =~ ^30[1-8]$ ]] && [[ "$redirect_url" =~ ^https:// ]]; then
        return 0
    else
        return 1
    fi
}

# Function to check Cloudflare integration
check_cloudflare_integration() {
    local url="$1"
    
    echo "  Checking Cloudflare integration for $url..."
    
    local headers=$(curl -s -I "$url" 2>/dev/null)
    local cf_ray=$(echo "$headers" | grep -i "cf-ray" | head -1)
    local cf_cache=$(echo "$headers" | grep -i "cf-cache-status" | head -1)
    
    if [[ -n "$cf_ray" ]]; then
        echo "  Cloudflare detected: $cf_ray"
        if [[ -n "$cf_cache" ]]; then
            echo "  Cache status: $cf_cache"
        fi
        return 0
    else
        echo "  Cloudflare headers not found"
        return 1
    fi
}

# Function to validate domain patterns
validate_domain_patterns() {
    local environment="$1"
    
    echo -e "\n${BLUE}🔍 Validating Domain Patterns${NC}"
    
    if [[ "$environment" == "production" ]]; then
        # Production should use root domain and simple subdomains
        validate_check "Production web domain pattern" \
            "[[ '$WEB_URL' == 'https://vedavivi.app' ]]" \
            "Production web should use root domain"
        
        validate_check "Production API domain pattern" \
            "[[ '$API_URL' == 'https://api.vedavivi.app' ]]" \
            "Production API should use api subdomain"
        
        validate_check "Production WebSocket domain pattern" \
            "[[ '$WS_URL' == 'wss://ws.vedavivi.app' ]]" \
            "Production WebSocket should use ws subdomain"
    else
        # Staging should use www-staging prefix
        validate_check "Staging web domain pattern" \
            "[[ '$WEB_URL' == 'https://www-staging.vedavivi.app' ]]" \
            "Staging web should use www-staging subdomain"
        
        validate_check "Staging API domain pattern" \
            "[[ '$API_URL' == 'https://api-staging.vedavivi.app' ]]" \
            "Staging API should use api-staging subdomain"
        
        validate_check "Staging WebSocket domain pattern" \
            "[[ '$WS_URL' == 'wss://ws-staging.vedavivi.app' ]]" \
            "Staging WebSocket should use ws-staging subdomain"
    fi
}

# Main validation tests
echo -e "\n${YELLOW}🚀 Starting Domain Resolution Validation Tests${NC}"

# 1. Validate domain patterns
validate_domain_patterns "$ENVIRONMENT"

# 2. DNS Resolution Tests
echo -e "\n${BLUE}📡 DNS Resolution Tests${NC}"

for domain in "${DOMAINS[@]}"; do
    validate_check "DNS resolution for $domain" \
        "check_dns_resolution '$domain'" \
        "Domain should resolve to IP address"
done

# 3. SSL Certificate Tests
echo -e "\n${BLUE}🔒 SSL Certificate Tests${NC}"

for domain in "${DOMAINS[@]}"; do
    # Skip WebSocket domain for SSL check as it might use different port
    if [[ "$domain" != *"ws"* ]]; then
        validate_check "SSL certificate for $domain" \
            "check_ssl_certificate '$domain'" \
            "Valid SSL certificate"
    fi
done

# 4. HTTP Response Tests
echo -e "\n${BLUE}🌐 HTTP Response Tests${NC}"

validate_check "Web application accessibility" \
    "check_http_response '$WEB_URL' '200'" \
    "HTTP 200 status"

validate_check "API health endpoint" \
    "check_http_response '$API_URL/health' '200'" \
    "HTTP 200 status"

# 5. HTTPS Redirect Tests
echo -e "\n${BLUE}🔄 HTTPS Redirect Tests${NC}"

for domain in "${DOMAINS[@]}"; do
    # Skip WebSocket domains for HTTP redirect test
    if [[ "$domain" != *"ws"* ]]; then
        validate_check "HTTPS redirect for $domain" \
            "check_https_redirect '$domain'" \
            "HTTP should redirect to HTTPS"
    fi
done

# 6. Cloudflare Integration Tests
echo -e "\n${BLUE}☁️ Cloudflare Integration Tests${NC}"

validate_check "Cloudflare CDN integration for web" \
    "check_cloudflare_integration '$WEB_URL'" \
    "Cloudflare headers should be present"

validate_check "Cloudflare CDN integration for API" \
    "check_cloudflare_integration '$API_URL'" \
    "Cloudflare headers should be present"

# 7. Cross-Region DNS Tests
echo -e "\n${BLUE}🌍 Cross-Region DNS Tests${NC}"

# Test DNS resolution from different public DNS servers
declare -a dns_servers=("*******" "*******" "**************" "*******")
declare -a dns_names=("Google" "Cloudflare" "OpenDNS" "Quad9")

for i in "${!dns_servers[@]}"; do
    dns_server="${dns_servers[$i]}"
    dns_name="${dns_names[$i]}"
    
    echo -e "\n${BLUE}Testing DNS resolution via $dns_name ($dns_server)${NC}"
    
    for domain in "${DOMAINS[@]}"; do
        # Skip WebSocket domains for this test
        if [[ "$domain" != *"ws"* ]]; then
            echo "  Resolving $domain via $dns_name..."
            resolved_ip=$(dig @"$dns_server" +short "$domain" 2>/dev/null | head -1)
            
            if [[ -n "$resolved_ip" ]]; then
                echo "    ✅ Resolved to: $resolved_ip"
                ((PASSED++))
            else
                echo "    ❌ Resolution failed"
                ((FAILED++))
            fi
        fi
    done
done

# 8. Generate Domain Validation Report
echo -e "\n${BLUE}📊 Generating Domain Validation Report${NC}"

cat > "$REPORT_FILE" << EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "environment": "$ENVIRONMENT",
  "domains_tested": $(printf '%s\n' "${DOMAINS[@]}" | jq -R . | jq -s .),
  "urls": {
    "web_url": "$WEB_URL",
    "api_url": "$API_URL",
    "websocket_url": "$WS_URL"
  },
  "test_results": {
    "tests_passed": $PASSED,
    "tests_failed": $FAILED,
    "total_tests": $((PASSED + FAILED))
  },
  "validation_status": $(if [[ $FAILED -eq 0 ]]; then echo '"PASSED"'; else echo '"FAILED"'; fi)
}
EOF

echo "Domain validation report saved to: $REPORT_FILE"

# Summary
echo -e "\n${BLUE}=================================================================="
echo -e "📊 Domain Resolution Validation Summary${NC}"
echo -e "=================================================================="
echo -e "${GREEN}✅ Passed: $PASSED tests${NC}"
echo -e "${RED}❌ Failed: $FAILED tests${NC}"
echo -e "Total: $((PASSED + FAILED)) tests"

if [[ $FAILED -eq 0 ]]; then
    echo -e "\n${GREEN}🎉 All domain resolution validation tests passed!${NC}"
    echo -e "${GREEN}✅ All calculated URLs resolve correctly and SSL certificates are valid${NC}"
    exit 0
else
    echo -e "\n${RED}⚠️ Some domain resolution validation tests failed!${NC}"
    echo -e "${RED}❌ Please review domain configuration issues before proceeding${NC}"
    exit 1
fi
