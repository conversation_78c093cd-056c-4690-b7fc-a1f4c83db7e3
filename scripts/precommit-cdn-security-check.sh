#!/bin/bash
# Pre-commit hook to validate CDN migration security checklist
# This script runs security validation on CDN-related changes before commits
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Running CDN Migration Security Checklist...${NC}"

# Get the root directory of the git repository
ROOT_DIR=$(git rev-parse --show-toplevel)
cd "$ROOT_DIR"

# Check if this is a CDN-related change
CDN_FILES_CHANGED=false

# Check for changes in terraform files
if git diff --cached --name-only | grep -qE "^terraform/"; then
    echo -e "${YELLOW}📋 Terraform files detected in commit${NC}"
    CDN_FILES_CHANGED=true
fi

# Check for changes in CDN-related GitHub workflows
if git diff --cached --name-only | grep -qE "\.github/workflows/(deploy-infra|deploy-frontend-cdn|deploy-api)\.yml"; then
    echo -e "${YELLOW}📋 CDN workflow files detected in commit${NC}"
    CDN_FILES_CHANGED=true
fi

# Check for changes in CDN-related scripts
if git diff --cached --name-only | grep -qE "scripts/(validate-cdn-migration|precommit-cdn-security-check)\.sh"; then
    echo -e "${YELLOW}📋 CDN security scripts detected in commit${NC}"
    CDN_FILES_CHANGED=true
fi

if [[ "$CDN_FILES_CHANGED" == "true" ]]; then
    echo -e "${BLUE}📋 CDN-related files detected, running security checklist...${NC}"

    # Validation counters
    CHECKS_PASSED=0
    CHECKS_FAILED=0

    # Function to run a validation check
    run_security_check() {
        local check_name="$1"
        local check_command="$2"

        echo -e "\n${BLUE}🔍 Checking: $check_name${NC}"

        if bash -c "$check_command"; then
            echo -e "${GREEN}✅ PASSED: $check_name${NC}"
            CHECKS_PASSED=$((CHECKS_PASSED + 1))
        else
            echo -e "${RED}❌ FAILED: $check_name${NC}"
            CHECKS_FAILED=$((CHECKS_FAILED + 1))
        fi
    }

    echo -e "\n${BLUE}🛡️ Running CDN Security Validation Checks${NC}"
    echo "=================================================="

    # 1. Check if required security scripts exist
    run_security_check "CDN migration validation script exists" \
        "test -f scripts/validate-cdn-migration.sh"

    run_security_check "CDN migration validation script is executable" \
        "test -x scripts/validate-cdn-migration.sh"

    # 3. Validate Terraform syntax if terraform files changed
    if git diff --cached --name-only | grep -qE "^terraform/.*\.tf$"; then
        echo -e "\n${BLUE}🏗️ Validating Terraform Configuration${NC}"

        # Check if terraform is available
        if command -v terraform >/dev/null 2>&1; then
            # Find all terraform directories that have changes
            TERRAFORM_DIRS=$(git diff --cached --name-only | grep -E "^terraform/.*\.tf$" | xargs dirname | sort -u)

            for tf_dir in $TERRAFORM_DIRS; do
                if [[ -f "$tf_dir/main.tf" || -f "$tf_dir/versions.tf" ]]; then
                    # Use absolute paths to ensure we're in the right directory
                    abs_tf_dir="$ROOT_DIR/$tf_dir"
                    run_security_check "Terraform format check in $tf_dir" \
                        "cd '$abs_tf_dir' && terraform fmt -check=true -diff=true"

                    run_security_check "Terraform validation in $tf_dir" \
                        "cd '$abs_tf_dir' && terraform init -backend=false >/dev/null 2>&1 && terraform validate"
                fi
            done
        else
            echo -e "${YELLOW}⚠️ Terraform not found, skipping Terraform validation${NC}"
        fi
    fi

    # 4. Check for security best practices in Terraform files
    if git diff --cached --name-only | grep -qE "^terraform/.*\.tf$"; then
        echo -e "\n${BLUE}🔒 Checking Terraform Security Best Practices${NC}"

        # Check for R2 storage security configurations
        run_security_check "R2 storage origin URL uses HTTPS" \
            "! git diff --cached | grep -E 'storage_origin_url.*=' | grep -qv 'https://' || echo 'No storage origin URL changes detected'"

        # Check for proper Cloudflare security settings
        run_security_check "Cloudflare security settings validation" \
            "! git diff --cached | grep -E '(ssl.*=.*\"off\"|always_use_https.*=.*\"off\")' || echo 'No insecure SSL changes detected'"

        # Check for sensitive data exposure
        run_security_check "No hardcoded secrets or API keys" \
            "! git diff --cached | grep -iE '(api_key|secret|password|token).*=.*\"[^{]' || echo 'No hardcoded secrets detected'"
    fi

    # 5. Validate GitHub Actions workflow syntax (only for existing files)
    if git diff --cached --name-only | grep -qE "\.github/workflows/.*\.yml$"; then
        echo -e "\n${BLUE}🔄 Validating GitHub Actions Workflows${NC}"

        # Check if yq or python is available for YAML validation
        if command -v python3 >/dev/null 2>&1; then
            # Only validate files that exist (not deleted)
            WORKFLOW_FILES=$(git diff --cached --name-status | grep -E "^[^D].*\.github/workflows/.*\.yml$" | cut -f2)
            for workflow_file in $WORKFLOW_FILES; do
                if [[ -f "$workflow_file" ]]; then
                    run_security_check "YAML syntax validation for $workflow_file" \
                        "python3 -c 'import yaml; yaml.safe_load(open(\"$workflow_file\"))'"
                fi
            done
        else
            echo -e "${YELLOW}⚠️ Python3 not found, skipping YAML validation${NC}"
        fi
    fi

    # Note: GCS bucket security validation removed after R2 migration
    # R2 storage uses access keys instead of GCS IAM policies
    echo -e "\n${BLUE}🪣 Storage Security Status${NC}"
    echo -e "${GREEN}✅ Using Cloudflare R2 storage (no GCS bucket validation needed)${NC}"

    # Summary
    echo -e "\n${BLUE}=================================================="
    echo -e "📊 CDN Security Checklist Summary${NC}"
    echo -e "=================================================="
    echo -e "${GREEN}✅ Passed: $CHECKS_PASSED checks${NC}"
    echo -e "${RED}❌ Failed: $CHECKS_FAILED checks${NC}"
    echo -e "Total: $((CHECKS_PASSED + CHECKS_FAILED)) checks"

    if [[ $CHECKS_FAILED -eq 0 ]]; then
        echo -e "\n${GREEN}🎉 All CDN security checklist validations passed!${NC}"
        echo -e "${GREEN}✅ Commit is approved for CDN security compliance${NC}"
        exit 0
    else
        echo -e "\n${RED}⚠️ Some CDN security checklist validations failed!${NC}"
        echo -e "${RED}❌ Please fix the issues before committing${NC}"
        echo -e "\n${YELLOW}💡 Tips:${NC}"
        echo -e "  • Run 'terraform fmt' to fix formatting issues"
        echo -e "  • Run 'terraform validate' to check configuration"
        echo -e "  • Ensure scripts are executable: chmod +x scripts/*.sh"
        echo -e "  • Review security configurations in Terraform files"
        exit 1
    fi

else
    echo -e "${GREEN}ℹ️ No CDN-related changes detected, skipping security checklist${NC}"
    exit 0
fi
