#!/bin/bash
#
# A2A Platform - Deployment Error Diagnosis Script
#
# This script diagnoses common issues with GitHub Actions deployment workflows
# when running with act (local testing) and provides a plan to fix them.
#
# Usage:
#   ./scripts/diagnose-deploy-errors.sh [--workflow WORKFLOW_NAME]
#

set -e

# Default values
WORKFLOW=""
DEBUG=false

# Function to show usage
show_usage() {
  cat << EOF
Usage: diagnose-deploy-errors.sh [options]

Diagnose GitHub Actions deployment workflow issues for act testing.

Options:
  -w, --workflow WORKFLOW   Specific workflow to diagnose (e.g., deploy-api.yml)
  --debug                   Enable debug output
  -h, --help                Show this help message

Examples:
  # Diagnose all deployment workflows
  ./scripts/diagnose-deploy-errors.sh

  # Diagnose specific workflow
  ./scripts/diagnose-deploy-errors.sh -w deploy-api.yml

EOF
  exit 1
}

# Parse command line arguments
while [[ "$#" -gt 0 ]]; do
  case $1 in
    -w|--workflow) WORKFLOW="$2"; shift ;;
    --debug) DEBUG=true ;;
    -h|--help) show_usage ;;
    *) echo "Unknown parameter: $1"; show_usage ;;
  esac
  shift
done

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
  local color=$1
  local message=$2
  echo -e "${color}${message}${NC}"
}

# Function to print section header
print_section() {
  local message=$1
  echo ""
  print_message "${BLUE}" "====== ${message} ======"
  echo ""
}

# Function to check if required tools are installed
check_prerequisites() {
  print_section "Checking Prerequisites"
  
  local missing_tools=()
  
  # Check for act
  if ! command -v act &> /dev/null; then
    missing_tools+=("act")
    print_message "${RED}" "✗ act is not installed"
  else
    print_message "${GREEN}" "✓ act is installed: $(act --version)"
  fi
  
  # Check for docker
  if ! command -v docker &> /dev/null; then
    missing_tools+=("docker")
    print_message "${RED}" "✗ docker is not installed"
  else
    print_message "${GREEN}" "✓ docker is installed: $(docker --version)"
  fi
  
  # Check if docker daemon is running
  if ! docker info &> /dev/null; then
    print_message "${RED}" "✗ Docker daemon is not running"
    missing_tools+=("docker-daemon")
  else
    print_message "${GREEN}" "✓ Docker daemon is running"
  fi
  
  return ${#missing_tools[@]}
}

# Function to check workflow files
check_workflow_files() {
  print_section "Checking Workflow Files"
  
  local workflows_dir="${PROJECT_ROOT}/.github/workflows"
  local deployment_workflows=("deploy-api.yml" "deploy-workers.yml" "deploy-web.yml")
  
  if [ -n "$WORKFLOW" ]; then
    deployment_workflows=("$WORKFLOW")
  fi
  
  local issues=()
  
  for workflow in "${deployment_workflows[@]}"; do
    local workflow_path="${workflows_dir}/${workflow}"
    
    if [ ! -f "$workflow_path" ]; then
      print_message "${RED}" "✗ Workflow file not found: $workflow"
      issues+=("missing-$workflow")
      continue
    fi
    
    print_message "${GREEN}" "✓ Found workflow: $workflow"
    
    # Check for common issues in workflow files
    if grep -q "service-name:" "$workflow_path" && grep -q "service_name:" "$workflow_path"; then
      print_message "${YELLOW}" "⚠ Mixed naming conventions (service-name vs service_name) in $workflow"
      issues+=("naming-convention-$workflow")
    fi
    
    # Check for missing reusable workflow reference
    if ! grep -q "uses: ./.github/workflows/reusable-deploy-service.yml" "$workflow_path"; then
      print_message "${YELLOW}" "⚠ Missing reusable workflow reference in $workflow"
      issues+=("missing-reusable-$workflow")
    fi
    
    # Check for environment-specific secrets (should be removed)
    if grep -q "_STAGING\|_PRODUCTION" "$workflow_path"; then
      print_message "${YELLOW}" "⚠ Found environment-specific secret names in $workflow (should be removed)"
      issues+=("env-secrets-$workflow")
    fi
  done
  
  return ${#issues[@]}
}

# Function to check reusable workflow
check_reusable_workflow() {
  print_section "Checking Reusable Workflow"
  
  local reusable_workflow="${PROJECT_ROOT}/.github/workflows/reusable-deploy-service.yml"
  
  if [ ! -f "$reusable_workflow" ]; then
    print_message "${RED}" "✗ Reusable workflow not found: reusable-deploy-service.yml"
    return 1
  fi
  
  print_message "${GREEN}" "✓ Found reusable workflow"
  
  # Check for workflow_call trigger
  if ! grep -q "workflow_call:" "$reusable_workflow"; then
    print_message "${RED}" "✗ Missing workflow_call trigger in reusable workflow"
    return 1
  fi
  
  print_message "${GREEN}" "✓ Reusable workflow has workflow_call trigger"
  return 0
}

# Function to check composite actions
check_composite_actions() {
  print_section "Checking Composite Actions"
  
  local actions_dir="${PROJECT_ROOT}/.github/actions"
  local required_actions=("gcp-auth-setup" "docker-build-push" "gcloud-run-deploy")
  
  local missing_actions=()
  
  for action in "${required_actions[@]}"; do
    local action_path="${actions_dir}/${action}/action.yml"
    
    if [ ! -f "$action_path" ]; then
      print_message "${RED}" "✗ Composite action not found: $action"
      missing_actions+=("$action")
    else
      print_message "${GREEN}" "✓ Found composite action: $action"
    fi
  done
  
  return ${#missing_actions[@]}
}

# Function to check secrets file for act
check_secrets_file() {
  print_section "Checking Secrets Configuration"
  
  local secrets_file="${PROJECT_ROOT}/.secrets"
  
  if [ ! -f "$secrets_file" ]; then
    print_message "${YELLOW}" "⚠ No .secrets file found for act testing"
    print_message "${YELLOW}" "  This is needed for act to work with secrets"
    return 1
  fi
  
  print_message "${GREEN}" "✓ Found .secrets file"
  
  # Check for required secrets (basic ones)
  local required_secrets=("GCP_PROJECT_ID" "GCP_SERVICE_ACCOUNT" "WORKLOAD_IDENTITY_PROVIDER")
  local missing_secrets=()
  
  for secret in "${required_secrets[@]}"; do
    if ! grep -q "^${secret}=" "$secrets_file"; then
      missing_secrets+=("$secret")
    fi
  done
  
  if [ ${#missing_secrets[@]} -gt 0 ]; then
    print_message "${YELLOW}" "⚠ Missing secrets in .secrets file:"
    for secret in "${missing_secrets[@]}"; do
      print_message "${YELLOW}" "  - $secret"
    done
    return 1
  fi
  
  print_message "${GREEN}" "✓ Required secrets are present"
  return 0
}

# Function to run a test with act and capture errors
test_workflow_with_act() {
  local workflow=$1
  print_section "Testing Workflow: $workflow"
  
  local output_file=$(mktemp)
  local exit_code=0
  
  # Run act with dry-run to test the workflow
  if ! "${SCRIPT_DIR}/run-act.sh" -w "$workflow" -j "call_reusable_deploy_staging" --dry-run > "$output_file" 2>&1; then
    exit_code=1
  fi
  
  if [ $exit_code -eq 0 ]; then
    print_message "${GREEN}" "✓ Workflow $workflow passed dry-run test"
  else
    print_message "${RED}" "✗ Workflow $workflow failed dry-run test"
    print_message "${YELLOW}" "Error output:"
    cat "$output_file" | head -20  # Show first 20 lines of error
    if [ $(wc -l < "$output_file") -gt 20 ]; then
      print_message "${YELLOW}" "... (truncated, see full output in $output_file)"
    fi
  fi
  
  rm -f "$output_file"
  return $exit_code
}

# Function to generate action plan
generate_action_plan() {
  print_section "ACTION PLAN TO FIX DEPLOYMENT ERRORS"
  
  print_message "${BLUE}" "Based on the diagnosis, here's your action plan:"
  echo ""
  
  # Plan for missing prerequisites
  if ! command -v act &> /dev/null; then
    print_message "${YELLOW}" "1. Install act:"
    echo "   # On macOS:"
    echo "   brew install act"
    echo ""
    echo "   # On Linux:"
    echo "   curl -s https://raw.githubusercontent.com/nektos/act/master/install.sh | sudo bash"
    echo ""
  fi
  
  if ! docker info &> /dev/null; then
    print_message "${YELLOW}" "2. Start Docker daemon:"
    echo "   # Make sure Docker Desktop is running"
    echo "   # Or start docker service on Linux: sudo systemctl start docker"
    echo ""
  fi
  
  # Plan for missing reusable workflow
  if [ ! -f "${PROJECT_ROOT}/.github/workflows/reusable-deploy-service.yml" ]; then
    print_message "${YELLOW}" "3. Create reusable deployment workflow:"
    echo "   # Create .github/workflows/reusable-deploy-service.yml"
    echo "   # This should define the common deployment pattern used by all services"
    echo ""
  fi
  
  # Plan for missing composite actions
  local actions_dir="${PROJECT_ROOT}/.github/actions"
  local required_actions=("gcp-auth-setup" "docker-build-push" "gcloud-run-deploy")
  
  for action in "${required_actions[@]}"; do
    if [ ! -f "${actions_dir}/${action}/action.yml" ]; then
      print_message "${YELLOW}" "4. Create composite action: $action"
      echo "   # Create .github/actions/${action}/action.yml"
      echo "   # This should encapsulate the ${action} logic"
      echo ""
    fi
  done
  
  # Plan for secrets
  if [ ! -f "${PROJECT_ROOT}/.secrets" ]; then
    print_message "${YELLOW}" "5. Create .secrets file for act:"
    echo "   # Create .secrets file in project root with dummy values for testing"
    echo "   # Example:"
    echo "   GCP_PROJECT_ID=test-project"
    echo "   GCP_SERVICE_ACCOUNT=<EMAIL>"
    echo "   WORKLOAD_IDENTITY_PROVIDER=projects/123/locations/global/workloadIdentityPools/test"
    echo ""
  fi
  
  # Plan for workflow naming consistency
  print_message "${YELLOW}" "6. Fix workflow naming consistency:"
  echo "   # Ensure all workflows use consistent parameter names"
  echo "   # Either use kebab-case (service-name) or snake_case (service_name) consistently"
  echo ""
  
  # Plan for testing approach
  print_message "${YELLOW}" "7. Testing approach:"
  echo "   # Use --dry-run flag for initial validation"
  echo "   # Create mock Docker images for testing"
  echo "   # Use local backend for terraform workflows"
  echo ""
  
  print_message "${BLUE}" "Next steps:"
  echo "1. Run: ./scripts/diagnose-deploy-errors.sh --workflow deploy-api.yml"
  echo "2. Fix the identified issues"
  echo "3. Test with: ./scripts/run-act.sh -w deploy-api.yml --dry-run"
  echo "4. Repeat for other workflows"
  echo ""
}

# Main function
main() {
  print_message "${BLUE}" "A2A Platform - Deployment Error Diagnosis"
  echo ""
  
  local total_issues=0
  
  # Check prerequisites
  if ! check_prerequisites; then
    ((total_issues++))
  fi
  
  # Check workflow files
  if ! check_workflow_files; then
    ((total_issues++))
  fi
  
  # Check reusable workflow
  if ! check_reusable_workflow; then
    ((total_issues++))
  fi
  
  # Check composite actions
  if ! check_composite_actions; then
    ((total_issues++))
  fi
  
  # Check secrets
  if ! check_secrets_file; then
    ((total_issues++))
  fi
  
  # Test workflows if basic checks pass
  if [ $total_issues -eq 0 ] || [ "$DEBUG" = true ]; then
    local workflows_to_test=("deploy-api.yml" "deploy-workers.yml" "deploy-web.yml")
    
    if [ -n "$WORKFLOW" ]; then
      workflows_to_test=("$WORKFLOW")
    fi
    
    for workflow in "${workflows_to_test[@]}"; do
      if ! test_workflow_with_act "$workflow"; then
        ((total_issues++))
      fi
    done
  fi
  
  # Generate action plan
  generate_action_plan
  
  if [ $total_issues -eq 0 ]; then
    print_message "${GREEN}" "All checks passed! No deployment errors detected."
    exit 0
  else
    print_message "${RED}" "Found $total_issues issue(s) that need to be addressed."
    exit 1
  fi
}

# Run the main function
main