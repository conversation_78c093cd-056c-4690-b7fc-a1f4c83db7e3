#!/bin/bash
#
# A2A Platform - Test Performance Benchmark Script
#
# This script benchmarks different test execution modes to measure
# performance improvements from database optimization strategies.
#
# Usage:
#   ./scripts/test-performance-benchmark.sh [OPTIONS]
#
# Options:
#   --detailed      Show detailed timing for each test category
#   --csv          Output results in CSV format
#   --baseline     Run baseline PostgreSQL tests only
#   --optimized    Run optimized tests only (no-db + fast-db)
#   --help         Show this help message

set -e

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
RESULTS_FILE="${PROJECT_ROOT}/test-performance-results.txt"
CSV_FILE="${PROJECT_ROOT}/test-performance-results.csv"

# Default options
DETAILED=false
CSV_OUTPUT=false
BASELINE_ONLY=false
OPTIMIZED_ONLY=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --detailed)
      DETAILED=true
      shift
      ;;
    --csv)
      CSV_OUTPUT=true
      shift
      ;;
    --baseline)
      BASELINE_ONLY=true
      shift
      ;;
    --optimized)
      OPTIMIZED_ONLY=true
      shift
      ;;
    --help)
      echo "Usage: $0 [OPTIONS]"
      echo "Options:"
      echo "  --detailed      Show detailed timing for each test category"
      echo "  --csv          Output results in CSV format"
      echo "  --baseline     Run baseline PostgreSQL tests only"
      echo "  --optimized    Run optimized tests only (no-db + fast-db)"
      echo "  --help         Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for available options"
      exit 1
      ;;
  esac
done

echo "╔════════════════════════════════════════════════════════════╗"
echo "║ A2A Platform - Test Performance Benchmark                  ║"
echo "╚════════════════════════════════════════════════════════════╝"
echo ""

# Initialize results
if [[ "$CSV_OUTPUT" == true ]]; then
  echo "test_mode,execution_time_seconds,test_count,tests_per_second" > "$CSV_FILE"
fi

# Function to run tests and measure performance
run_test_benchmark() {
  local test_mode="$1"
  local test_args="$2"
  local description="$3"

  echo "🔄 Running $description..."

  # Capture start time
  local start_time=$(date +%s.%N)

  # Run the test and capture output
  local test_output
  if test_output=$(cd "$PROJECT_ROOT" && ./scripts/run-backend-tests.sh --ci $test_args 2>&1); then
    local end_time=$(date +%s.%N)
    local duration=$(python3 -c "print(f'{$end_time - $start_time:.2f}')")

    # Extract test count from output
    local test_count=$(echo "$test_output" | grep -E "=[0-9]+ passed" | tail -1 | sed -E 's/.*=([0-9]+) passed.*/\1/')

    if [[ -z "$test_count" ]]; then
      test_count="0"
    fi

    # Calculate tests per second
    local tests_per_second="0"
    if [[ "$test_count" != "0" ]]; then
      tests_per_second=$(python3 -c "print(f'{$test_count / $duration:.1f}' if $duration > 0 else '0')")
    fi

    printf "✅ %-20s: %6.2fs (%3s tests, %5.1f tests/sec)\n" "$test_mode" "$duration" "$test_count" "$tests_per_second"

    # Save to CSV if requested
    if [[ "$CSV_OUTPUT" == true ]]; then
      echo "$test_mode,$duration,$test_count,$tests_per_second" >> "$CSV_FILE"
    fi

    # Save detailed output if requested
    if [[ "$DETAILED" == true ]]; then
      echo "--- $description Output ---" >> "$RESULTS_FILE"
      echo "$test_output" >> "$RESULTS_FILE"
      echo "" >> "$RESULTS_FILE"
    fi

    # Return the duration for calculations
    echo "$duration"
  else
    echo "❌ $description failed!"
    echo "Error output:" >&2
    echo "$test_output" >&2
    return 1
  fi
}

# Initialize results file
echo "A2A Platform Test Performance Benchmark - $(date)" > "$RESULTS_FILE"
echo "================================================================" >> "$RESULTS_FILE"
echo "" >> "$RESULTS_FILE"

# Baseline tests (if not optimized-only)
if [[ "$OPTIMIZED_ONLY" != true ]]; then
  echo "📊 Baseline Performance (PostgreSQL)"
  echo "────────────────────────────────────"

  unit_time=$(run_test_benchmark "unit-postgresql" "--unit" "Unit tests with PostgreSQL")
  integration_time=$(run_test_benchmark "integration-postgresql" "--integration" "Integration tests with PostgreSQL")

  # Calculate total baseline time
  baseline_total=$(python3 -c "print(f'{$unit_time + $integration_time:.2f}')")
  printf "📈 %-20s: %6.2fs (total baseline)\n" "baseline-total" "$baseline_total"

  if [[ "$CSV_OUTPUT" == true ]]; then
    echo "baseline-total,$baseline_total,0,0" >> "$CSV_FILE"
  fi

  echo ""
fi

# Optimized tests (if not baseline-only)
if [[ "$BASELINE_ONLY" != true ]]; then
  echo "🚀 Optimized Performance"
  echo "────────────────────────"

  # Database-free tests
  nodb_time=$(run_test_benchmark "no-db" "--no-db" "Database-free tests")

  # SQLite tests (if any exist)
  fastdb_time=$(run_test_benchmark "fast-db" "--fast-db" "SQLite-compatible tests" || echo "0")

  # Calculate optimized total
  optimized_total=$(python3 -c "print(f'{$nodb_time + $fastdb_time:.2f}')")
  printf "🏃‍♂️ %-20s: %6.2fs (optimized total)\n" "optimized-total" "$optimized_total"

  if [[ "$CSV_OUTPUT" == true ]]; then
    echo "optimized-total,$optimized_total,0,0" >> "$CSV_FILE"
  fi

  echo ""
fi

# Performance comparison (if both baseline and optimized were run)
if [[ "$BASELINE_ONLY" != true && "$OPTIMIZED_ONLY" != true ]]; then
  echo "📈 Performance Analysis"
  echo "──────────────────────"

  # Calculate improvement
  improvement=$(python3 -c "
baseline = $baseline_total
optimized = $optimized_total
if baseline > 0:
    improvement = (baseline - optimized) / baseline * 100
    speedup = baseline / optimized if optimized > 0 else 0
    print(f'{improvement:.1f}')
    print(f'{speedup:.2f}')
    print('yes' if improvement > 15 else 'no')
else:
    print('0')
    print('0')
    print('no')
")

  read improvement_pct speedup_factor target_met <<< "$improvement"

  printf "⚡ Performance improvement: %s%% faster\n" "$improvement_pct"
  printf "🔥 Speedup factor: %sx\n" "$speedup_factor"

  if [[ "$target_met" == "yes" ]]; then
    echo "🎯 Target achieved: >15% performance improvement!"
  else
    echo "⚠️  Target not met: Need >15% improvement for success"
  fi

  echo ""
fi

echo "📋 Results Summary"
echo "─────────────────"
echo "• Results saved to: $RESULTS_FILE"
if [[ "$CSV_OUTPUT" == true ]]; then
  echo "• CSV data saved to: $CSV_FILE"
fi
echo "• Run with --detailed for full test output"
echo ""

echo "🔧 Optimization Recommendations"
echo "──────────────────────────────"
echo "1. Mark pure business logic tests with @pytest.mark.no_db"
echo "2. Mark basic CRUD tests with @pytest.mark.fast_db"
echo "3. Reserve PostgreSQL for complex integration tests only"
echo "4. Use ./scripts/run-backend-tests.sh --no-db for rapid feedback"
echo ""

echo "✅ Benchmark completed successfully!"
