#!/bin/bash
# Terraform State Migration Script
# Migrates local Terraform state to remote GCS backend
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT=${1:-staging}
DRY_RUN=${2:-true}
TERRAFORM_DIR="terraform/environments/$ENVIRONMENT"

echo -e "${BLUE}🔄 Terraform State Migration for environment: $ENVIRONMENT${NC}"
echo "=================================================================="

if [[ "$DRY_RUN" == "true" || "$DRY_RUN" == "--dry-run" ]]; then
    echo -e "${YELLOW}⚠️ Running in DRY RUN mode - no actual changes will be made${NC}"
    DRY_RUN=true
else
    echo -e "${RED}⚠️ LIVE MODE - actual state migration will be performed!${NC}"
    read -p "Are you sure you want to proceed? (yes/no): " confirm
    if [[ "$confirm" != "yes" ]]; then
        echo "State migration cancelled."
        exit 0
    fi
    DRY_RUN=false
fi

# Check if required environment variables are set
check_environment_variables() {
    echo -e "\n${BLUE}🔍 Checking environment variables${NC}"
    
    local missing_vars=()
    
    if [[ -z "${GCP_TF_STATE_BUCKET:-}" ]]; then
        missing_vars+=("GCP_TF_STATE_BUCKET")
    fi
    
    if [[ -z "${GCP_PROJECT_ID:-}" ]]; then
        missing_vars+=("GCP_PROJECT_ID")
    fi
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        echo -e "${RED}❌ Missing required environment variables:${NC}"
        for var in "${missing_vars[@]}"; do
            echo -e "${RED}   - $var${NC}"
        done
        echo -e "\n${YELLOW}Please set these variables and try again:${NC}"
        echo "export GCP_TF_STATE_BUCKET=your-terraform-state-bucket"
        echo "export GCP_PROJECT_ID=your-gcp-project-id"
        exit 1
    fi
    
    echo -e "${GREEN}✅ All required environment variables are set${NC}"
    echo "   GCP_TF_STATE_BUCKET: $GCP_TF_STATE_BUCKET"
    echo "   GCP_PROJECT_ID: $GCP_PROJECT_ID"
}

# Check if Terraform directory exists
check_terraform_directory() {
    echo -e "\n${BLUE}📁 Checking Terraform directory${NC}"
    
    if [[ ! -d "$TERRAFORM_DIR" ]]; then
        echo -e "${RED}❌ Terraform directory not found: $TERRAFORM_DIR${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Terraform directory found: $TERRAFORM_DIR${NC}"
}

# Check if local state exists
check_local_state() {
    echo -e "\n${BLUE}🗃️ Checking for local Terraform state${NC}"
    
    if [[ -f "$TERRAFORM_DIR/terraform.tfstate" ]]; then
        echo -e "${GREEN}✅ Local state file found${NC}"
        
        # Check if state has resources
        local resource_count=$(jq '.resources | length' "$TERRAFORM_DIR/terraform.tfstate" 2>/dev/null || echo "0")
        echo "   Resources in state: $resource_count"
        
        if [[ "$resource_count" == "0" ]]; then
            echo -e "${YELLOW}⚠️ State file exists but contains no resources${NC}"
        fi
        
        return 0
    else
        echo -e "${YELLOW}⚠️ No local state file found${NC}"
        echo "   This might be a new environment or state is already remote"
        return 1
    fi
}

# Backup local state
backup_local_state() {
    echo -e "\n${BLUE}💾 Backing up local state${NC}"
    
    local backup_dir="state-backup-$(date +%Y%m%d-%H%M%S)"
    
    if [[ "$DRY_RUN" == "false" ]]; then
        mkdir -p "$backup_dir"
        
        if [[ -f "$TERRAFORM_DIR/terraform.tfstate" ]]; then
            cp "$TERRAFORM_DIR/terraform.tfstate" "$backup_dir/"
            echo -e "${GREEN}✅ Local state backed up to: $backup_dir/terraform.tfstate${NC}"
        fi
        
        if [[ -f "$TERRAFORM_DIR/terraform.tfstate.backup" ]]; then
            cp "$TERRAFORM_DIR/terraform.tfstate.backup" "$backup_dir/"
            echo -e "${GREEN}✅ State backup file copied to: $backup_dir/terraform.tfstate.backup${NC}"
        fi
        
        echo "BACKUP_DIR=$backup_dir" > .state-migration-info
        echo -e "${BLUE}💡 Backup location saved to .state-migration-info${NC}"
    else
        echo -e "${YELLOW}[DRY RUN] Would create backup directory: $backup_dir${NC}"
    fi
}

# Configure remote backend
configure_remote_backend() {
    echo -e "\n${BLUE}🔧 Configuring remote backend${NC}"
    
    local backend_config="terraform/environments/$ENVIRONMENT/backend.tf"
    
    if [[ "$DRY_RUN" == "false" ]]; then
        cat > "$backend_config" << EOF
terraform {
  backend "gcs" {
    bucket = "$GCP_TF_STATE_BUCKET"
    prefix = "terraform/cdn/state/$ENVIRONMENT"
  }
}
EOF
        echo -e "${GREEN}✅ Remote backend configuration created: $backend_config${NC}"
    else
        echo -e "${YELLOW}[DRY RUN] Would create backend configuration:${NC}"
        echo "terraform {"
        echo "  backend \"gcs\" {"
        echo "    bucket = \"$GCP_TF_STATE_BUCKET\""
        echo "    prefix = \"terraform/cdn/state/$ENVIRONMENT\""
        echo "  }"
        echo "}"
    fi
}

# Initialize with remote backend
initialize_remote_backend() {
    echo -e "\n${BLUE}🚀 Initializing Terraform with remote backend${NC}"
    
    if [[ "$DRY_RUN" == "false" ]]; then
        cd "$TERRAFORM_DIR"
        
        echo "Running terraform init with backend migration..."
        terraform init -migrate-state \
            -backend-config="bucket=$GCP_TF_STATE_BUCKET" \
            -backend-config="prefix=terraform/cdn/state/$ENVIRONMENT"
        
        echo -e "${GREEN}✅ Terraform initialized with remote backend${NC}"
        
        cd - > /dev/null
    else
        echo -e "${YELLOW}[DRY RUN] Would run: terraform init -migrate-state${NC}"
        echo -e "${YELLOW}   with backend config:${NC}"
        echo -e "${YELLOW}   - bucket=$GCP_TF_STATE_BUCKET${NC}"
        echo -e "${YELLOW}   - prefix=terraform/cdn/state/$ENVIRONMENT${NC}"
    fi
}

# Verify remote state
verify_remote_state() {
    echo -e "\n${BLUE}✅ Verifying remote state${NC}"
    
    if [[ "$DRY_RUN" == "false" ]]; then
        cd "$TERRAFORM_DIR"
        
        echo "Checking remote state..."
        terraform state list
        
        echo -e "${GREEN}✅ Remote state verification complete${NC}"
        
        cd - > /dev/null
    else
        echo -e "${YELLOW}[DRY RUN] Would verify remote state with: terraform state list${NC}"
    fi
}

# Cleanup local state files
cleanup_local_state() {
    echo -e "\n${BLUE}🧹 Cleaning up local state files${NC}"
    
    if [[ "$DRY_RUN" == "false" ]]; then
        if [[ -f "$TERRAFORM_DIR/terraform.tfstate" ]]; then
            rm "$TERRAFORM_DIR/terraform.tfstate"
            echo -e "${GREEN}✅ Removed local state file${NC}"
        fi
        
        if [[ -f "$TERRAFORM_DIR/terraform.tfstate.backup" ]]; then
            rm "$TERRAFORM_DIR/terraform.tfstate.backup"
            echo -e "${GREEN}✅ Removed local state backup file${NC}"
        fi
    else
        echo -e "${YELLOW}[DRY RUN] Would remove local state files${NC}"
    fi
}

# Main migration process
main() {
    echo -e "\n${YELLOW}🚀 Starting Terraform State Migration${NC}"
    
    # Pre-migration checks
    check_environment_variables
    check_terraform_directory
    
    # Check if migration is needed
    if ! check_local_state; then
        echo -e "\n${BLUE}ℹ️ No local state found - migration may not be necessary${NC}"
        echo -e "${BLUE}   The environment might already be using remote state${NC}"
        
        if [[ "$DRY_RUN" == "false" ]]; then
            read -p "Continue with remote backend configuration? (yes/no): " continue_confirm
            if [[ "$continue_confirm" != "yes" ]]; then
                echo "Migration cancelled."
                exit 0
            fi
        fi
    fi
    
    # Migration steps
    backup_local_state
    configure_remote_backend
    initialize_remote_backend
    verify_remote_state
    
    if [[ "$DRY_RUN" == "false" ]]; then
        cleanup_local_state
    fi
    
    # Summary
    echo -e "\n${BLUE}=================================================================="
    echo -e "📊 Terraform State Migration Summary${NC}"
    echo -e "=================================================================="
    echo -e "${GREEN}✅ Environment: $ENVIRONMENT${NC}"
    echo -e "${GREEN}✅ Backend: GCS bucket $GCP_TF_STATE_BUCKET${NC}"
    echo -e "${GREEN}✅ State prefix: terraform/cdn/state/$ENVIRONMENT${NC}"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        echo -e "\n${YELLOW}💡 This was a dry run. To perform actual migration:${NC}"
        echo -e "   $0 $ENVIRONMENT --live"
    else
        echo -e "\n${GREEN}🎉 State migration completed successfully!${NC}"
        echo -e "${BLUE}💡 Next steps:${NC}"
        echo -e "   1. Update GitHub Actions workflows to use remote backend"
        echo -e "   2. Test Terraform operations with remote state"
        echo -e "   3. Remove local backend configurations"
    fi
}

# Run main function
main "$@"
