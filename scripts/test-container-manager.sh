#!/bin/bash
#
# A2A Platform - Test Container Manager
#
# Intelligent container lifecycle management for test performance optimization.
# Provides container reuse, health monitoring, and migration checksum validation.
#
# Usage:
#   ./scripts/test-container-manager.sh [COMMAND] [OPTIONS]
#
# Commands:
#   start       Start test containers with reuse optimization
#   stop        Stop test containers
#   reset       Reset containers and data
#   status      Show container status and health
#   migrate     Run migrations with checksum validation
#   cleanup     Clean up orphaned containers and volumes

set -e

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
CONTAINER_PREFIX="a2a-test"
DB_CONTAINER_NAME="${CONTAINER_PREFIX}-db"
REDIS_CONTAINER_NAME="${CONTAINER_PREFIX}-redis"
DB_VOLUME_NAME="${CONTAINER_PREFIX}-db-data"
REDIS_VOLUME_NAME="${CONTAINER_PREFIX}-redis-data"
MIGRATION_CHECKSUM_FILE="/tmp/a2a-migration-checksum"
CONTAINER_STATE_FILE="/tmp/a2a-container-state"

# Database configuration
DB_NAME="a2a_platform_test"
DB_USER="postgres"
DB_PASSWORD="postgres"
DB_PORT="5432"
REDIS_PORT="6379"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Container management functions
container_exists() {
    local container_name="$1"
    docker ps -a --format "table {{.Names}}" | grep -q "^${container_name}$"
}

container_running() {
    local container_name="$1"
    docker ps --format "table {{.Names}}" | grep -q "^${container_name}$"
}

container_healthy() {
    local container_name="$1"
    local health_status=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "none")
    [[ "$health_status" == "healthy" ]] || [[ "$health_status" == "none" ]]
}

# Database health check
check_db_health() {
    local max_attempts=30
    local attempt=1
    
    log_info "Checking database health..."
    
    while [[ $attempt -le $max_attempts ]]; do
        if docker exec "$DB_CONTAINER_NAME" pg_isready -U "$DB_USER" -d "$DB_NAME" >/dev/null 2>&1; then
            log_success "Database is healthy"
            return 0
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            log_error "Database health check failed after $max_attempts attempts"
            return 1
        fi
        
        echo -n "."
        sleep 1
        ((attempt++))
    done
}

# Redis health check
check_redis_health() {
    log_info "Checking Redis health..."
    
    if docker exec "$REDIS_CONTAINER_NAME" redis-cli ping >/dev/null 2>&1; then
        log_success "Redis is healthy"
        return 0
    else
        log_error "Redis health check failed"
        return 1
    fi
}

# Migration checksum management
calculate_migration_checksum() {
    find "$PROJECT_ROOT/apps/backend/alembic/versions" -name "*.py" -type f | sort | xargs md5sum | md5sum | cut -d' ' -f1
}

migrations_changed() {
    local current_checksum=$(calculate_migration_checksum)
    local stored_checksum=""
    
    if [[ -f "$MIGRATION_CHECKSUM_FILE" ]]; then
        stored_checksum=$(cat "$MIGRATION_CHECKSUM_FILE")
    fi
    
    [[ "$current_checksum" != "$stored_checksum" ]]
}

update_migration_checksum() {
    calculate_migration_checksum > "$MIGRATION_CHECKSUM_FILE"
}

# Container state management
save_container_state() {
    local state="$1"
    echo "$state" > "$CONTAINER_STATE_FILE"
    echo "$(date)" >> "$CONTAINER_STATE_FILE"
}

get_container_state() {
    if [[ -f "$CONTAINER_STATE_FILE" ]]; then
        head -n 1 "$CONTAINER_STATE_FILE"
    else
        echo "unknown"
    fi
}

# Start containers with intelligent reuse
start_containers() {
    local force_recreate=false
    local skip_migrations=false
    
    # Parse options
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force-recreate)
                force_recreate=true
                shift
                ;;
            --skip-migrations)
                skip_migrations=true
                shift
                ;;
            *)
                shift
                ;;
        esac
    done
    
    log_info "Starting test containers with intelligent reuse..."
    
    # Create volumes if they don't exist
    docker volume create "$DB_VOLUME_NAME" >/dev/null 2>&1 || true
    docker volume create "$REDIS_VOLUME_NAME" >/dev/null 2>&1 || true
    
    # Start PostgreSQL container
    if container_running "$DB_CONTAINER_NAME" && [[ "$force_recreate" == false ]]; then
        log_success "PostgreSQL container already running"
    else
        if container_exists "$DB_CONTAINER_NAME" && [[ "$force_recreate" == false ]]; then
            log_info "Starting existing PostgreSQL container..."
            docker start "$DB_CONTAINER_NAME" >/dev/null
        else
            if container_exists "$DB_CONTAINER_NAME"; then
                log_info "Removing existing PostgreSQL container..."
                docker rm -f "$DB_CONTAINER_NAME" >/dev/null
            fi
            
            log_info "Creating new PostgreSQL container..."
            docker run -d \
                --name "$DB_CONTAINER_NAME" \
                -e POSTGRES_USER="$DB_USER" \
                -e POSTGRES_PASSWORD="$DB_PASSWORD" \
                -e POSTGRES_DB="$DB_NAME" \
                -v "$DB_VOLUME_NAME:/var/lib/postgresql/data" \
                -p "$DB_PORT:5432" \
                --health-cmd="pg_isready -U $DB_USER -d $DB_NAME" \
                --health-interval=5s \
                --health-timeout=5s \
                --health-retries=5 \
                postgres:14 >/dev/null
        fi
    fi
    
    # Start Redis container
    if container_running "$REDIS_CONTAINER_NAME" && [[ "$force_recreate" == false ]]; then
        log_success "Redis container already running"
    else
        if container_exists "$REDIS_CONTAINER_NAME" && [[ "$force_recreate" == false ]]; then
            log_info "Starting existing Redis container..."
            docker start "$REDIS_CONTAINER_NAME" >/dev/null
        else
            if container_exists "$REDIS_CONTAINER_NAME"; then
                log_info "Removing existing Redis container..."
                docker rm -f "$REDIS_CONTAINER_NAME" >/dev/null
            fi
            
            log_info "Creating new Redis container..."
            docker run -d \
                --name "$REDIS_CONTAINER_NAME" \
                -v "$REDIS_VOLUME_NAME:/data" \
                -p "$REDIS_PORT:6379" \
                --health-cmd="redis-cli ping" \
                --health-interval=5s \
                --health-timeout=3s \
                --health-retries=3 \
                redis:alpine >/dev/null
        fi
    fi
    
    # Wait for containers to be healthy
    check_db_health || return 1
    check_redis_health || return 1
    
    # Run migrations if needed
    if [[ "$skip_migrations" == false ]]; then
        run_migrations
    fi
    
    save_container_state "running"
    log_success "Test containers are ready"
}

# Run migrations with checksum validation
run_migrations() {
    log_info "Checking migration status..."
    
    if migrations_changed; then
        log_info "Migrations have changed, applying updates..."
        
        # Ensure database exists
        docker exec "$DB_CONTAINER_NAME" psql -U "$DB_USER" -d postgres -c "CREATE DATABASE $DB_NAME;" 2>/dev/null || true
        
        # Run migrations from the backend directory
        cd "$PROJECT_ROOT/apps/backend"
        
        # Set environment variables for migration
        export DATABASE_URL="postgresql+asyncpg://$DB_USER:$DB_PASSWORD@localhost:$DB_PORT/$DB_NAME"
        
        if python -m alembic upgrade head; then
            update_migration_checksum
            log_success "Migrations applied successfully"
        else
            log_error "Migration failed"
            return 1
        fi
    else
        log_success "Migrations are up to date"
    fi
}

# Stop containers
stop_containers() {
    log_info "Stopping test containers..."
    
    if container_running "$DB_CONTAINER_NAME"; then
        docker stop "$DB_CONTAINER_NAME" >/dev/null
        log_success "PostgreSQL container stopped"
    fi
    
    if container_running "$REDIS_CONTAINER_NAME"; then
        docker stop "$REDIS_CONTAINER_NAME" >/dev/null
        log_success "Redis container stopped"
    fi
    
    save_container_state "stopped"
}

# Reset containers and data
reset_containers() {
    log_info "Resetting test containers and data..."
    
    # Stop and remove containers
    docker rm -f "$DB_CONTAINER_NAME" "$REDIS_CONTAINER_NAME" >/dev/null 2>&1 || true
    
    # Remove volumes
    docker volume rm "$DB_VOLUME_NAME" "$REDIS_VOLUME_NAME" >/dev/null 2>&1 || true
    
    # Clear state files
    rm -f "$MIGRATION_CHECKSUM_FILE" "$CONTAINER_STATE_FILE"
    
    log_success "Test containers and data reset"
}

# Show container status
show_status() {
    echo "╔════════════════════════════════════════════════════════════╗"
    echo "║ A2A Platform - Test Container Status                       ║"
    echo "╚════════════════════════════════════════════════════════════╝"
    echo ""
    
    # Container state
    local state=$(get_container_state)
    echo "Container State: $state"
    echo ""
    
    # PostgreSQL status
    echo "PostgreSQL Container:"
    if container_running "$DB_CONTAINER_NAME"; then
        echo "  Status: Running ✅"
        if container_healthy "$DB_CONTAINER_NAME"; then
            echo "  Health: Healthy ✅"
        else
            echo "  Health: Unhealthy ❌"
        fi
    elif container_exists "$DB_CONTAINER_NAME"; then
        echo "  Status: Stopped 🛑"
    else
        echo "  Status: Not Created ❌"
    fi
    
    # Redis status
    echo "Redis Container:"
    if container_running "$REDIS_CONTAINER_NAME"; then
        echo "  Status: Running ✅"
        if container_healthy "$REDIS_CONTAINER_NAME"; then
            echo "  Health: Healthy ✅"
        else
            echo "  Health: Unhealthy ❌"
        fi
    elif container_exists "$REDIS_CONTAINER_NAME"; then
        echo "  Status: Stopped 🛑"
    else
        echo "  Status: Not Created ❌"
    fi
    
    # Migration status
    echo "Migration Status:"
    if [[ -f "$MIGRATION_CHECKSUM_FILE" ]]; then
        if migrations_changed; then
            echo "  Status: Changes Detected 🔄"
        else
            echo "  Status: Up to Date ✅"
        fi
    else
        echo "  Status: Unknown ❓"
    fi
    
    echo ""
}

# Cleanup orphaned containers and volumes
cleanup() {
    log_info "Cleaning up orphaned test containers and volumes..."
    
    # Remove any containers with our prefix that might be orphaned
    docker ps -a --format "table {{.Names}}" | grep "^${CONTAINER_PREFIX}-" | xargs -r docker rm -f >/dev/null 2>&1 || true
    
    # Remove any volumes with our prefix that might be orphaned
    docker volume ls --format "table {{.Name}}" | grep "^${CONTAINER_PREFIX}-" | xargs -r docker volume rm >/dev/null 2>&1 || true
    
    log_success "Cleanup completed"
}

# Main command dispatcher
main() {
    local command="${1:-status}"
    shift || true
    
    case "$command" in
        start)
            start_containers "$@"
            ;;
        stop)
            stop_containers
            ;;
        reset)
            reset_containers
            ;;
        status)
            show_status
            ;;
        migrate)
            run_migrations
            ;;
        cleanup)
            cleanup
            ;;
        *)
            echo "Usage: $0 {start|stop|reset|status|migrate|cleanup} [options]"
            echo ""
            echo "Commands:"
            echo "  start       Start test containers with reuse optimization"
            echo "  stop        Stop test containers"
            echo "  reset       Reset containers and data"
            echo "  status      Show container status and health"
            echo "  migrate     Run migrations with checksum validation"
            echo "  cleanup     Clean up orphaned containers and volumes"
            echo ""
            echo "Start options:"
            echo "  --force-recreate    Force recreation of containers"
            echo "  --skip-migrations   Skip migration execution"
            exit 1
            ;;
    esac
}

main "$@"
