#!/bin/bash
#
# A2A Platform - Run Tests with Environment Variables
#
# This script runs backend tests with all required environment variables.
# It loads variables from .env.test and runs the tests.
#
# Usage:
#   ./scripts/run-tests-with-env.sh [TEST_PATHS...]
#
# Examples:
#   ./scripts/run-tests-with-env.sh                # Run all tests
#   ./scripts/run-tests-with-env.sh tests/unit/    # Run unit tests
#

set -e

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
BACKEND_DIR="${PROJECT_ROOT}/apps/backend"
ENV_TEST_FILE="${PROJECT_ROOT}/.env.test"

# Check if .env.test exists
if [[ ! -f "${ENV_TEST_FILE}" ]]; then
  echo "Error: .env.test file not found at ${ENV_TEST_FILE}"
  exit 1
fi

# Change to backend directory
cd "${BACKEND_DIR}"
echo "Changed to directory: $(pwd)"

# Load environment variables from .env.test
echo "Loading environment variables from ${ENV_TEST_FILE}"
# Use more explicit pattern to avoid matching partial comments
export $(grep -v '^[[:space:]]*#' "${ENV_TEST_FILE}" | grep -v '^[[:space:]]*$' | xargs)


# Print environment variables for debugging
echo "Environment variables:"
echo "DATABASE_URL: ${DATABASE_URL}"
echo "REDIS_URL: ${REDIS_URL}"
echo "CLERK_API_KEY: ${CLERK_API_KEY}"
echo "CLERK_JWT_PUBLIC_KEY: ${CLERK_JWT_PUBLIC_KEY}"
echo "CLERK_WEBHOOK_SECRET: ${CLERK_WEBHOOK_SECRET}"
echo "STORAGE_BUCKET: ${STORAGE_BUCKET}"
echo "CDN_URL: ${CDN_URL}"
echo "PUBSUB_PROJECT_ID: ${PUBSUB_PROJECT_ID}"

# Set PYTHONPATH
export PYTHONPATH="${BACKEND_DIR}/src:${PYTHONPATH}"

# Build test command
TEST_CMD="python -m pytest"

# Add any test paths provided as arguments
if [[ $# -gt 0 ]]; then
  TEST_CMD="${TEST_CMD} $@"
fi

# Run the tests
echo "Running command: ${TEST_CMD}"
bash -c "${TEST_CMD}"

echo "Tests completed!"
exit 0
