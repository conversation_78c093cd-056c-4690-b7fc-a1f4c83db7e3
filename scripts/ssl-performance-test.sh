#!/bin/bash
# filepath: /Users/<USER>/dev/blkops-collective/a2a-platform/scripts/ssl-performance-test.sh
#
# Script to measure SSL handshake performance and throughput
# This script tests SSL connection performance metrics for the A2A Platform

set -e

# Define colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Check if required tools are installed
missing_tools=()
for tool in openssl ab wrk; do
  if ! command -v $tool &> /dev/null; then
    missing_tools+=($tool)
  fi
done

if [ ${#missing_tools[@]} -gt 0 ]; then
  echo -e "${RED}Error: Required tools not found: ${missing_tools[*]}${NC}"
  echo -e "Please install missing tools:"
  echo -e "  ${YELLOW}brew install openssl apache2-utils wrk${NC}"
  exit 1
fi

# Get domain from command line or use default
DOMAIN=${1:-"localhost:8000"}
HEALTH_ENDPOINT=${2:-"/api/health"}
GRAPHQL_ENDPOINT=${3:-"/graphql"}

echo -e "${YELLOW}Running SSL Performance Tests for ${DOMAIN}${NC}"
echo "==============================================="

# 1. Test SSL handshake time
echo -e "${YELLOW}1. Testing SSL handshake performance...${NC}"
echo "Target: $DOMAIN"

# Run the test 5 times and calculate average
total_time=0
iterations=5

for i in $(seq 1 $iterations); do
  handshake_time=$(openssl s_time -connect ${DOMAIN} -new -time 2 2>&1 | grep "connections in" | awk '{print $6}')
  if [ -z "$handshake_time" ]; then
    echo -e "${RED}Error measuring handshake time in iteration $i${NC}"
    continue
  fi
  
  # Calculate average time per connection in ms
  connections=$(openssl s_time -connect ${DOMAIN} -new -time 2 2>&1 | grep "connections in" | awk '{print $1}')
  time_ms=$(echo "($handshake_time / $connections) * 1000" | bc -l)
  
  total_time=$(echo "$total_time + $time_ms" | bc -l)
  echo "Iteration $i: $time_ms ms per connection ($connections connections in $handshake_time seconds)"
done

average_time=$(echo "$total_time / $iterations" | bc -l)
average_time_rounded=$(printf "%.2f" $average_time)

echo -e "Average SSL handshake time: ${YELLOW}$average_time_rounded ms${NC}"
if (( $(echo "$average_time < 200" | bc -l) )); then
  echo -e "${GREEN}✅ PASS: Handshake time is below 200ms target${NC}"
else
  echo -e "${RED}❌ FAIL: Handshake time exceeds 200ms target${NC}"
fi

echo "==============================================="

# 2. Test throughput with ApacheBench
echo -e "${YELLOW}2. Testing throughput with ApacheBench...${NC}"
echo "Target: https://$DOMAIN$HEALTH_ENDPOINT"

echo "Running ab with 100 concurrent connections, 1000 requests..."
ab_output=$(ab -n 1000 -c 100 -f TLS1.2 -k https://$DOMAIN$HEALTH_ENDPOINT 2>&1)
req_per_sec=$(echo "$ab_output" | grep "Requests per second" | awk '{print $4}')

echo -e "Throughput: ${YELLOW}$req_per_sec requests/second${NC}"
if (( $(echo "$req_per_sec >= 1000" | bc -l) )); then
  echo -e "${GREEN}✅ PASS: Throughput meets 1000 req/s target${NC}"
else
  echo -e "${RED}❌ FAIL: Throughput below 1000 req/s target${NC}"
fi

echo "==============================================="

# 3. Test concurrent connections with wrk
echo -e "${YELLOW}3. Testing concurrent connections with wrk...${NC}"
echo "Target: https://$DOMAIN$GRAPHQL_ENDPOINT"

echo "Running wrk with 100 threads, 1000 connections for 30 seconds..."
wrk_output=$(wrk -t100 -c1000 -d30s https://$DOMAIN$GRAPHQL_ENDPOINT 2>&1)
req_per_sec_wrk=$(echo "$wrk_output" | grep "Requests/sec" | awk '{print $2}')
latency_avg=$(echo "$wrk_output" | grep "Latency" | awk '{print $2}')

echo -e "Throughput: ${YELLOW}$req_per_sec_wrk requests/second${NC}"
echo -e "Average latency: ${YELLOW}$latency_avg${NC}"

echo "==============================================="

# 4. Test session resumption rate
echo -e "${YELLOW}4. Testing TLS session resumption rate...${NC}"
echo "Target: $DOMAIN"

# This creates a file with 1000 connection attempts with session reuse
echo "Creating 1000 test connections with session reuse..."
echo "Q
Q
Q
Q
Q" | openssl s_client -connect ${DOMAIN} -tls1_2 -reconnect -no_ticket -sess_out /tmp/session.pem > /dev/null 2>&1

# Count successful resumes
resumes=$(openssl s_client -connect ${DOMAIN} -tls1_2 -sess_in /tmp/session.pem < /dev/null 2>&1 | grep "Reused" | grep "Yes" | wc -l | tr -d ' ')

echo -e "Session resumption: ${YELLOW}$resumes${NC}"
if [ "$resumes" -eq 1 ]; then
  echo -e "${GREEN}✅ PASS: Session resumption is working${NC}"
else
  echo -e "${RED}❌ FAIL: Session resumption is not working${NC}"
fi

# Clean up
rm -f /tmp/session.pem

echo "==============================================="
echo -e "${GREEN}SSL Performance Testing Complete${NC}"

# Summarize results
echo "Summary:"
echo "- SSL Handshake: $average_time_rounded ms (Target: <200ms)"
echo "- Throughput: $req_per_sec req/s (Target: >1000 req/s)"
echo "- Concurrent Connections: Tested with 1000 connections"
echo "- Session Resumption: $([ "$resumes" -eq 1 ] && echo "Working" || echo "Not working") (Target: 95% success)"

# Overall assessment
if (( $(echo "$average_time < 200" | bc -l) )) && (( $(echo "$req_per_sec >= 1000" | bc -l) )) && [ "$resumes" -eq 1 ]; then
  echo -e "${GREEN}✅ OVERALL: SSL performance meets requirements${NC}"
else
  echo -e "${RED}❌ OVERALL: SSL performance does not meet all requirements${NC}"
fi
