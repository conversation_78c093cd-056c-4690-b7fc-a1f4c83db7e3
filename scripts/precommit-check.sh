#!/bin/bash
#
# A2A Platform - Pre-commit Check Script
#
# This script performs sequential checks:
# 1. Run pre-commit checks (Web Code Quality, then others)
# 2. Run backend tests (optional)
# 3. Run frontend tests (optional)
#
# Web Code Quality combines Prettier formatting + ESLint linting for efficiency.
# Each step only runs if the previous step passes.
# The script exits with an error code if any check fails.
#
# Usage:
#   ./scripts/precommit-check.sh [--skip-tests] [--skip-web-quality]
#

set -e

# Parse arguments
SKIP_TESTS=false
SKIP_HOOKS=()
while [[ $# -gt 0 ]]; do
  case $1 in
    --skip-tests)
      SKIP_TESTS=true
      shift
      ;;
    --skip-web-quality)
      SKIP_HOOKS+=("web-code-quality") # Skip the combined web code quality hook
      shift
      ;;
    *)
      echo "Unknown option: $1"
      echo "Usage: $0 [--skip-tests] [--skip-web-quality]"
      exit 1
      ;;
  esac
done

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
  local color=$1
  local message=$2
  echo -e "${color}${message}${NC}"
}

# Function to print section header
print_section() {
  local message=$1
  echo ""
  print_message "${YELLOW}" "====== ${message} ======"
  echo ""
}

# Function to check if files were modified by pre-commit
check_modified_files() {
  (cd "${PROJECT_ROOT}" && git diff --name-only HEAD | wc -l)
}

# Helper for individual hook runs
run_specific_hook() {
  local hook_id=$1
  local hook_name=$2 # e.g., "Prettier"
  local skip_flag_name=$3 # e.g., "prettier" or "eslint-web"

  # Check if this specific hook should be skipped based on command-line arguments
  local skip_this_hook=false
  for skipped_hook in "${SKIP_HOOKS[@]}"; do
    if [[ "$skipped_hook" == "$skip_flag_name" ]]; then
      skip_this_hook=true
      break
    fi
  done

  if $skip_this_hook; then
    print_message "${YELLOW}" "Skipping ${hook_name} checks (--skip-${skip_flag_name} flag provided)."
    return 0
  fi

  print_message "${YELLOW}" "Running ${hook_name} checks..."
  local initial_modified=$(check_modified_files)

  # Run the specific pre-commit hook
  # Ensure running from project root
  if ! (cd "${PROJECT_ROOT}" && pre-commit run ${hook_id} --all-files); then
    local current_modified=$(check_modified_files)
    # Check if files were modified by this hook run, indicating auto-fixes might have occurred
    if [ "$current_modified" -ne "$initial_modified" ]; then
      print_message "${YELLOW}" "Some files were modified by ${hook_name}. Running ${hook_name} again to verify fixes..."
      # Run the hook again to see if fixes resolved the issues
      if ! (cd "${PROJECT_ROOT}" && pre-commit run ${hook_id} --all-files); then
        print_message "${RED}" "${hook_name} checks failed after automatic fixes. Please review the errors."
        return 1
      else
        print_message "${GREEN}" "${hook_name} checks passed after automatic fixes."
        return 0
      fi
    else
      # No files were modified, but the hook still failed
      print_message "${RED}" "${hook_name} checks failed. Please review the errors."
      return 1
    fi
  fi
  print_message "${GREEN}" "${hook_name} checks passed."
  return 0
}


# Step 1: Run pre-commit checks (Prettier, then ESLint, then others)
run_precommit_checks() {
  print_section "STEP 1: Running pre-commit checks"

  # Run web code quality using package.json scripts
  if ! run_specific_hook "web-code-quality" "Web Code Quality (code:fix:all)" "web-code-quality"; then return 1; fi

  # Run other hooks
  print_message "${YELLOW}" "Running other pre-commit checks..."

  # Define hooks that have already been run or should always be skipped for the 'others' run.
  local base_skip_array=("pytest-backend" "web-code-quality")

  # Add user-provided skips (--skip-eslint, --skip-prettier will be redundant here but harmless)
  # This ensures any *other* user-defined skips passed via SKIP_HOOKS are respected.
  for user_skipped_hook in "${SKIP_HOOKS[@]}"; do
    # Add to base_skip_array if not already present
    if [[ ! " ${base_skip_array[@]} " =~ " ${user_skipped_hook} " ]]; then
      base_skip_array+=("$user_skipped_hook")
    fi
  done

  local other_hooks_skip_env=""
  if [ ${#base_skip_array[@]} -gt 0 ]; then
    IFS=','
    other_hooks_skip_env="${base_skip_array[*]}"
    unset IFS
  fi

  local initial_modified_other=$(check_modified_files)
  # Run all hooks, skipping those already handled or specified.
  if ! (cd "${PROJECT_ROOT}" && SKIP="$other_hooks_skip_env" pre-commit run --all-files); then
    local current_modified_other=$(check_modified_files)
    if [ "$current_modified_other" -ne "$initial_modified_other" ]; then
        print_message "${YELLOW}" "Some files were modified by other hooks. Running them again to verify fixes..."
        if ! (cd "${PROJECT_ROOT}" && SKIP="$other_hooks_skip_env" pre-commit run --all-files); then
            print_message "${RED}" "Other pre-commit checks failed after automatic fixes. Please review."
            return 1
        else
            print_message "${GREEN}" "Other pre-commit checks passed after automatic fixes."
        fi
    else
        print_message "${RED}" "Other pre-commit checks failed. Please review."
        return 1
    fi
  else
    print_message "${GREEN}" "Other pre-commit checks passed."
  fi

  # If we reach here, all parts of pre-commit that ran were successful.
  # The original script printed "Pre-commit checks passed." only if the single pre-commit run passed.
  # This new structure has multiple steps. A final "all pre-commit checks passed" is implicit if no return 1 occurred.
  return 0
}

# Step 2: Run backend tests
run_backend_tests() {
  print_section "STEP 2: Running backend tests"

  if ! "${SCRIPT_DIR}/run-backend-tests.sh"; then
    print_message "${RED}" "Backend tests failed. Please review the errors above."
    return 1
  fi

  print_message "${GREEN}" "Backend tests passed."
  return 0
}

# Step 3: Run frontend tests
run_frontend_tests() {
  print_section "STEP 3: Running frontend tests"

  if ! "${SCRIPT_DIR}/run-frontend-tests.sh"; then
    print_message "${RED}" "Frontend tests failed. Please review the errors above."
    return 1
  fi

  print_message "${GREEN}" "Frontend tests passed."
  return 0
}

# Main function to run all checks sequentially
main() {
  print_message "${YELLOW}" "Starting pre-commit check sequence..."

  # Step 1: Run pre-commit checks
  if ! run_precommit_checks; then
    # run_precommit_checks already prints specific error messages.
    # A general failure message for the pre-commit step could be added here if desired.
    print_message "${RED}" "Pre-commit stage failed."
    exit 1
  fi
  print_message "${GREEN}" "All pre-commit checks passed successfully."


  # Skip tests if requested
  if [ "$SKIP_TESTS" = true ]; then
    print_message "${YELLOW}" "Skipping tests (--skip-tests flag provided)"
    print_section "SUCCESS"
    print_message "${GREEN}" "Pre-commit checks passed! Tests were skipped."
    exit 0
  fi

  # Step 2: Run backend tests
  if ! run_backend_tests; then
    exit 1
  fi

  # Step 3: Run frontend tests
  if ! run_frontend_tests; then
    exit 1
  fi

  # All checks passed
  print_section "SUCCESS"
  print_message "${GREEN}" "All checks passed! Your code is ready to be committed."
  exit 0
}

# Run the main function
main
