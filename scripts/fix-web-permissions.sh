#!/bin/bash
#
# <PERSON>ript to fix permissions in the web app containers
# This creates proper volume directories with the right permissions
# without requiring root inside the container.
#

set -e

# Arguments
SERVICE=${1:-frontend}

echo "🔧 Fixing permissions for the $SERVICE container..."

# Get current user and group IDs
HOST_UID=$(id -u)
HOST_GID=$(id -g)

# Define the paths
NODE_MODULES_PATH="./apps/web/node_modules"
NODE_CACHE_PATH="./apps/web/node_modules/.cache"

# Create directories with proper permissions if they don't exist
mkdir -p "$NODE_MODULES_PATH"
mkdir -p "$NODE_CACHE_PATH"

# Ensure host user has ownership of the directories
echo "📂 Setting correct permissions on host directories..."
chmod -R 777 "$NODE_MODULES_PATH"

echo "✅ Permissions have been fixed!"
echo "🚀 You can now run bun commands without permission errors!"
echo "💡 Example usage:"
echo "   docker-compose -f docker-compose.dev.yml exec $SERVICE bun install"

exit 0
