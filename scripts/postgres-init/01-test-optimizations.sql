-- A2A Platform Test Database Optimizations
-- This script optimizes PostgreSQL for test performance

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Create optimized test database if it doesn't exist
SELECT 'CREATE DATABASE a2a_platform_test'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'a2a_platform_test')\gexec

-- Connect to test database for further optimizations
\c a2a_platform_test;

-- Performance optimizations for test environment
ALTER SYSTEM SET synchronous_commit = off;
ALTER SYSTEM SET fsync = off;
ALTER SYSTEM SET full_page_writes = off;
ALTER SYSTEM SET checkpoint_segments = 32;
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';

-- Reload configuration
SELECT pg_reload_conf();

-- Create test-specific schemas for isolation
CREATE SCHEMA IF NOT EXISTS test_isolation;
CREATE SCHEMA IF NOT EXISTS test_performance;

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE a2a_platform_test TO postgres;
GRANT ALL PRIVILEGES ON SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON SCHEMA test_isolation TO postgres;
GRANT ALL PRIVILEGES ON SCHEMA test_performance TO postgres;

-- Create function for test database reset (for faster test isolation)
CREATE OR REPLACE FUNCTION reset_test_data()
RETURNS void AS $$
BEGIN
    -- Truncate all tables in public schema (faster than DROP/CREATE)
    EXECUTE (
        SELECT 'TRUNCATE TABLE ' || string_agg(quote_ident(tablename), ', ') || ' RESTART IDENTITY CASCADE'
        FROM pg_tables 
        WHERE schemaname = 'public'
        AND tablename NOT LIKE 'alembic_%'
    );
END;
$$ LANGUAGE plpgsql;

-- Create function for performance monitoring
CREATE OR REPLACE FUNCTION get_test_performance_stats()
RETURNS TABLE(
    query_type text,
    total_time numeric,
    calls bigint,
    mean_time numeric
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        CASE 
            WHEN query LIKE 'SELECT%' THEN 'SELECT'
            WHEN query LIKE 'INSERT%' THEN 'INSERT'
            WHEN query LIKE 'UPDATE%' THEN 'UPDATE'
            WHEN query LIKE 'DELETE%' THEN 'DELETE'
            ELSE 'OTHER'
        END as query_type,
        sum(total_exec_time) as total_time,
        sum(calls) as calls,
        avg(mean_exec_time) as mean_time
    FROM pg_stat_statements
    GROUP BY 1
    ORDER BY 2 DESC;
END;
$$ LANGUAGE plpgsql;
