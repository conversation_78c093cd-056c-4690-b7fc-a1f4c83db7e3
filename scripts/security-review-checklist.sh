#!/bin/bash
# filepath: /Users/<USER>/dev/blkops-collective/a2a-platform/scripts/security-review-checklist.sh
#
# Script to run a security review checklist for HTTPS implementation
# This helps with the "Security review passed" checklist item

set -e

# Define colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
GRAY='\033[0;90m'
NC='\033[0m' # No Color

# Define paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(dirname "$SCRIPT_DIR")"

# Define domain to check
DOMAIN=${1:-"localhost:8000"}
FRONTEND=${2:-"localhost:5173"}

# Print header
echo -e "${BLUE}=========================================================${NC}"
echo -e "${BLUE}      A2A Platform HTTPS Security Review Checklist       ${NC}"
echo -e "${BLUE}=========================================================${NC}"
echo ""

# Initialize variables to track progress
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
SKIPPED_CHECKS=0

# Function to run a check
run_check() {
  local check_name=$1
  local check_cmd=$2
  local is_manual=${3:-false}
  
  ((TOTAL_CHECKS++))
  
  echo -e "${YELLOW}Check ${TOTAL_CHECKS}: ${check_name}${NC}"
  
  if [ "$is_manual" = true ]; then
    echo -e "${GRAY}This is a manual check. Please verify and enter the result.${NC}"
    echo -e "1: ${GREEN}PASS${NC}  2: ${RED}FAIL${NC}  3: ${GRAY}SKIP${NC}"
    read -p "Enter result (1/2/3): " result
    
    case $result in
      1)
        echo -e "${GREEN}✅ PASS: ${check_name}${NC}"
        ((PASSED_CHECKS++))
        ;;
      2)
        echo -e "${RED}❌ FAIL: ${check_name}${NC}"
        ((FAILED_CHECKS++))
        ;;
      3)
        echo -e "${GRAY}⏩ SKIP: ${check_name}${NC}"
        ((SKIPPED_CHECKS++))
        ;;
      *)
        echo -e "${RED}Invalid input. Marking as SKIP.${NC}"
        echo -e "${GRAY}⏩ SKIP: ${check_name}${NC}"
        ((SKIPPED_CHECKS++))
        ;;
    esac
  else
    if bash -c "$check_cmd"; then
      echo -e "${GREEN}✅ PASS: ${check_name}${NC}"
      ((PASSED_CHECKS++))
    else
      echo -e "${RED}❌ FAIL: ${check_name}${NC}"
      ((FAILED_CHECKS++))
    fi
  fi
  
  echo ""
}

# 1. Backend security middleware checks
echo -e "${BLUE}Part 1: Backend Security Implementation${NC}"

run_check "HTTPS Redirect Middleware" \
  "grep -q 'get_https_redirect_middleware' ${ROOT_DIR}/apps/backend/src/a2a_platform/main.py"

run_check "Security Headers Middleware" \
  "grep -q 'get_security_headers_middleware' ${ROOT_DIR}/apps/backend/src/a2a_platform/main.py"

run_check "WebSocket Security Middleware" \
  "grep -q 'get_websocket_security_middleware' ${ROOT_DIR}/apps/backend/src/a2a_platform/main.py"

run_check "HSTS Headers Configuration" \
  "grep -q 'max-age=31536000; includeSubDomains; preload' ${ROOT_DIR}/apps/backend/src/a2a_platform/middleware/security_headers.py"

run_check "Content Security Policy" \
  "grep -q 'connect-src.*https: wss:' ${ROOT_DIR}/apps/backend/src/a2a_platform/middleware/security_headers.py"

run_check "HTTPS Enforcement Tests" \
  "test -f ${ROOT_DIR}/apps/backend/tests/integration/test_https_enforcement.py"

# 2. Frontend security checks
echo -e "${BLUE}Part 2: Frontend Security Implementation${NC}"

run_check "HTTPS API Configuration" \
  "grep -q 'upgradeToHttps\\|ensureHttps' ${ROOT_DIR}/apps/web/src/config/api.ts"

run_check "WSS WebSocket Configuration" \
  "grep -q 'wss:\\|ensureWss' ${ROOT_DIR}/apps/web/src/lib/apollo.ts"

run_check "Mixed Content Detection" \
  "test -f ${ROOT_DIR}/apps/web/src/lib/mixed-content-detection.ts"

run_check "useSecureConnections Hook" \
  "test -f ${ROOT_DIR}/apps/web/src/hooks/useSecureConnections.ts"

run_check "HTTPS E2E Tests" \
  "test -f ${ROOT_DIR}/apps/web/cypress/e2e/https-enforcement.cy.ts"

# 3. Infrastructure checks
echo -e "${BLUE}Part 3: Infrastructure Configuration${NC}"

run_check "Load Balancer SSL Config" \
  "test -f ${ROOT_DIR}/terraform/modules/load-balancer/ssl-config.tf"

run_check "Cloud Run HTTPS-Only Config" \
  "test -f ${ROOT_DIR}/terraform/modules/cloud-run/https-only.tf"

run_check "SSL Monitoring Config" \
  "test -f ${ROOT_DIR}/terraform/modules/monitoring/ssl-monitoring.tf"

# 4. Script and testing checks
echo -e "${BLUE}Part 4: Scripts and Testing${NC}"

run_check "SSL Certificate Generation Script" \
  "test -f ${ROOT_DIR}/scripts/generate-ssl-certs.sh && test -x ${ROOT_DIR}/scripts/generate-ssl-certs.sh"

run_check "HTTPS Tests Script" \
  "test -f ${ROOT_DIR}/scripts/run-https-tests.sh && test -x ${ROOT_DIR}/scripts/run-https-tests.sh"

# 5. Live checks (if domain is not localhost)
echo -e "${BLUE}Part 5: Live Environment Checks${NC}"

if [[ "$DOMAIN" != localhost* ]]; then
  run_check "Live HTTPS Redirect" \
    "curl -sI -X GET http://${DOMAIN}/api/health | grep -q 'HTTP/[0-9.]* 301'"

  run_check "Live HSTS Header" \
    "curl -sI -X GET https://${DOMAIN}/api/health | grep -i 'strict-transport-security' | grep -q 'max-age=31536000'"

  run_check "Live WebSocket Security" \
    "WS_TEST_DOMAIN=${DOMAIN} node ${SCRIPT_DIR}/websocket-ssl-test.js"
else
  run_check "Live HTTPS Redirect" true
  run_check "Live HSTS Header" true
  run_check "Live WebSocket Security" true
fi

# 6. SSL Labs test (manual)
echo -e "${BLUE}Part 6: External Security Testing${NC}"

run_check "SSL Labs A+ Rating" true

run_check "No Mixed Content Issues" true

run_check "Proper Certificate Chain" true

run_check "Certificate Expiration > 30 days" true

# Summary
echo -e "${BLUE}=========================================================${NC}"
echo -e "${BLUE}                 Security Review Summary                 ${NC}"
echo -e "${BLUE}=========================================================${NC}"
echo -e "Total checks: ${TOTAL_CHECKS}"
echo -e "${GREEN}Passed: ${PASSED_CHECKS}${NC}"
echo -e "${RED}Failed: ${FAILED_CHECKS}${NC}"
echo -e "${GRAY}Skipped: ${SKIPPED_CHECKS}${NC}"
echo ""

PASS_PERCENTAGE=$((PASSED_CHECKS * 100 / (TOTAL_CHECKS - SKIPPED_CHECKS)))
echo -e "Pass rate: ${PASS_PERCENTAGE}%"

if [ $FAILED_CHECKS -eq 0 ]; then
  echo -e "${GREEN}✅ Security review PASSED${NC}"
  echo "All security checks have passed. The HTTPS implementation meets security requirements."
  echo "Please update the implementation checklist in specs/US6.1-Ensure-HTTPS-Communication.md"
else
  echo -e "${RED}❌ Security review FAILED${NC}"
  echo "Some security checks have failed. Please address the issues before proceeding to production."
fi

echo -e "${BLUE}=========================================================${NC}"
