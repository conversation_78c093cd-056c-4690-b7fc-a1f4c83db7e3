#!/bin/bash
# Workflow Consolidation Validation Script
# Validates Phase 4 workflow improvements and standardization
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔄 Validating GitHub Actions Workflow Consolidation${NC}"
echo "=================================================================="

# Validation counters
PASSED=0
FAILED=0

# Function to run validation checks
validate_check() {
    local test_name="$1"
    local command="$2"
    local expected="$3"

    echo -e "\n${BLUE}Testing: $test_name${NC}"

    if bash -c "$command"; then
        echo -e "${GREEN}✅ PASSED: $test_name${NC}"
        ((PASSED++))
    else
        echo -e "${RED}❌ FAILED: $test_name${NC}"
        echo -e "${RED}   Expected: $expected${NC}"
        ((FAILED++))
    fi
}

# Function to check YAML syntax
check_yaml_syntax() {
    local file="$1"

    if command -v python3 >/dev/null 2>&1; then
        python3 -c "import yaml; yaml.safe_load(open('$file'))" 2>/dev/null
    else
        echo "Python3 not available for YAML validation"
        return 0
    fi
}

# Function to check for Workload Identity usage
check_workload_identity() {
    local file="$1"

    grep -q "workload_identity_provider" "$file" && \
    grep -q "service_account.*GCP_SA_EMAIL" "$file" && \
    ! grep -q "credentials_json.*GCP_SA_KEY" "$file"
}

# Function to check Terraform version consistency
check_terraform_version() {
    local file="$1"
    local expected_version="$2"

    grep -q "TF_VERSION.*$expected_version" "$file" || \
    grep -q "terraform_version.*$expected_version" "$file"
}

# Function to check for remote backend configuration
check_remote_backend() {
    local file="$1"

    grep -q "GCP_TF_STATE_BUCKET" "$file" && \
    grep -q "backend-config.*bucket" "$file"
}

# Function to check for plan/apply separation
check_plan_apply_separation() {
    local file="$1"

    grep -q "terraform-plan:" "$file" && \
    grep -q "terraform-apply:" "$file" && \
    grep -q "needs.*terraform-plan" "$file"
}

echo -e "\n${YELLOW}🔍 Starting Workflow Consolidation Validation${NC}"

# 1. YAML Syntax Validation
echo -e "\n${BLUE}📋 YAML Syntax Validation${NC}"

validate_check "terraform-cdn.yml has been removed (deprecated)" \
    "! test -f '.github/workflows/terraform-cdn.yml'" \
    "terraform-cdn.yml should be removed as it's deprecated"

validate_check "deploy-web.yml has been removed (no longer needed)" \
    "! test -f '.github/workflows/deploy-web.yml'" \
    "deploy-web.yml should be removed as frontend-cdn-deploy.yml handles web deployment"

validate_check "frontend-cdn-deploy.yml YAML syntax" \
    "check_yaml_syntax '.github/workflows/frontend-cdn-deploy.yml'" \
    "Valid YAML syntax"

validate_check "terraform-gcp.yml YAML syntax" \
    "check_yaml_syntax '.github/workflows/terraform-gcp.yml'" \
    "Valid YAML syntax"

# 2. Workload Identity Federation Validation
echo -e "\n${BLUE}🔐 Workload Identity Federation Validation${NC}"

validate_check "terraform-gcp.yml includes CDN environment variables" \
    "grep -q 'TF_VAR_cloudflare_api_token' '.github/workflows/terraform-gcp.yml'" \
    "Should include CDN-specific environment variables"

validate_check "frontend-cdn-deploy.yml uses Workload Identity" \
    "check_workload_identity '.github/workflows/frontend-cdn-deploy.yml'" \
    "Should use Workload Identity Federation"

validate_check "frontend-cdn-deploy.yml depends on CI Pipeline" \
    "grep -q 'CI Pipeline (Static Analysis & Tests)' '.github/workflows/frontend-cdn-deploy.yml'" \
    "Should depend on CI Pipeline completion"

validate_check "terraform-gcp.yml uses Workload Identity" \
    "check_workload_identity '.github/workflows/terraform-gcp.yml'" \
    "Should use Workload Identity Federation"

validate_check "terraform-gcp.yml depends on Terraform Module Tests" \
    "grep -q 'Terraform Module Tests' '.github/workflows/terraform-gcp.yml'" \
    "Should depend on Terraform Module Tests completion"

# 3. Terraform Version Consistency
echo -e "\n${BLUE}🔧 Terraform Version Consistency${NC}"

EXPECTED_TF_VERSION="1.12.0"

validate_check "terraform-gcp.yml includes CDN outputs" \
    "grep -q 'Output CDN URLs' '.github/workflows/terraform-gcp.yml'" \
    "Should include CDN URL outputs"

validate_check "terraform-gcp.yml Terraform version" \
    "check_terraform_version '.github/workflows/terraform-gcp.yml' '$EXPECTED_TF_VERSION'" \
    "Should use Terraform v$EXPECTED_TF_VERSION"

# 4. Remote Backend Configuration
echo -e "\n${BLUE}🗄️ Remote Backend Configuration${NC}"

validate_check "terraform-gcp.yml workflow name updated" \
    "grep -q 'Terraform for Google Cloud and CDN Infrastructure' '.github/workflows/terraform-gcp.yml'" \
    "Should have updated workflow name reflecting CDN consolidation"

validate_check "terraform-gcp.yml remote backend" \
    "check_remote_backend '.github/workflows/terraform-gcp.yml'" \
    "Should use GCS remote backend"

# 5. Plan/Apply Separation
echo -e "\n${BLUE}🔄 Plan/Apply Separation${NC}"

validate_check "CDN infrastructure consolidated into terraform-gcp.yml" \
    "grep -q 'CDN-specific environment variables' '.github/workflows/terraform-gcp.yml'" \
    "Should have CDN infrastructure consolidated"

validate_check "terraform-gcp.yml plan/apply separation" \
    "check_plan_apply_separation '.github/workflows/terraform-gcp.yml'" \
    "Should have separate plan and apply jobs"

# 6. Security Best Practices
echo -e "\n${BLUE}🛡️ Security Best Practices${NC}"

validate_check "terraform-gcp.yml has proper permissions" \
    "grep -q 'id-token: write' '.github/workflows/terraform-gcp.yml'" \
    "Should have id-token: write permission"

validate_check "frontend-cdn-deploy.yml has proper permissions" \
    "grep -q 'id-token: write' '.github/workflows/frontend-cdn-deploy.yml'" \
    "Should have id-token: write permission"

validate_check "No Service Account Keys in terraform-gcp.yml" \
    "! grep -q 'GCP_SA_KEY' '.github/workflows/terraform-gcp.yml'" \
    "Should not use Service Account Keys"

validate_check "No Service Account Keys in frontend-cdn-deploy.yml" \
    "! grep -q 'GCP_SA_KEY' '.github/workflows/frontend-cdn-deploy.yml'" \
    "Should not use Service Account Keys"

# 7. Environment Configuration
echo -e "\n${BLUE}🌍 Environment Configuration${NC}"

validate_check "terraform-gcp.yml environment gates" \
    "grep -q 'environment:' '.github/workflows/terraform-gcp.yml'" \
    "Should have environment protection"

validate_check "Consistent environment variable naming" \
    "grep -q 'EFFECTIVE_ENVIRONMENT' '.github/workflows/terraform-gcp.yml' && grep -q 'DEPLOY_ENV' '.github/workflows/frontend-cdn-deploy.yml'" \
    "Should use consistent environment variable names"

# 8. Artifact Management
echo -e "\n${BLUE}📦 Artifact Management${NC}"

validate_check "terraform-gcp.yml uploads plan artifacts" \
    "grep -q 'upload-artifact@v4' '.github/workflows/terraform-gcp.yml'" \
    "Should upload Terraform plan artifacts"

validate_check "terraform-gcp.yml downloads plan artifacts" \
    "grep -q 'download-artifact@v4' '.github/workflows/terraform-gcp.yml'" \
    "Should download Terraform plan artifacts"

# 9. Action Version Consistency
echo -e "\n${BLUE}📌 Action Version Consistency${NC}"

validate_check "terraform-gcp.yml uses checkout@v4" \
    "grep -q 'checkout@v4' '.github/workflows/terraform-gcp.yml'" \
    "Should use checkout@v4"

validate_check "frontend-cdn-deploy.yml uses auth@v2" \
    "grep -q 'auth@v2' '.github/workflows/frontend-cdn-deploy.yml'" \
    "Should use google-github-actions/auth@v2"

validate_check "terraform-gcp.yml uses setup-terraform@v3" \
    "grep -q 'setup-terraform@v3' '.github/workflows/terraform-gcp.yml'" \
    "Should use hashicorp/setup-terraform@v3"

# 10. Migration Script Validation
echo -e "\n${BLUE}🔄 Migration Script Validation${NC}"

validate_check "State migration script exists" \
    "test -f 'scripts/migrate-terraform-state.sh'" \
    "Migration script should exist"

validate_check "State migration script is executable" \
    "test -x 'scripts/migrate-terraform-state.sh'" \
    "Migration script should be executable"

validate_check "Workflow consolidation validation script exists" \
    "test -f 'scripts/validate-workflow-consolidation.sh'" \
    "Validation script should exist"

# Summary
echo -e "\n${BLUE}=================================================================="
echo -e "📊 Workflow Consolidation Validation Summary${NC}"
echo -e "=================================================================="
echo -e "${GREEN}✅ Passed: $PASSED tests${NC}"
echo -e "${RED}❌ Failed: $FAILED tests${NC}"
echo -e "Total: $((PASSED + FAILED)) tests"

# Detailed recommendations
if [[ $FAILED -gt 0 ]]; then
    echo -e "\n${YELLOW}💡 Recommendations for Failed Tests:${NC}"
    echo -e "   • Review workflow files for missing Workload Identity configuration"
    echo -e "   • Ensure all workflows use Terraform v1.12.0"
    echo -e "   • Configure GCS remote backend for state management"
    echo -e "   • Implement plan/apply separation for safety"
    echo -e "   • Remove Service Account Key dependencies"
    echo -e "   • Add proper environment protection gates"
fi

echo -e "\n${BLUE}🚀 Next Steps:${NC}"
echo -e "   1. Fix any failed validation tests"
echo -e "   2. Test workflows in staging environment"
echo -e "   3. Migrate Terraform state to remote backend"
echo -e "   4. Update team documentation and procedures"
echo -e "   5. Train team on new workflow structure"

if [[ $FAILED -eq 0 ]]; then
    echo -e "\n${GREEN}🎉 All workflow consolidation validations passed!${NC}"
    echo -e "${GREEN}✅ Phase 4 implementation is ready for testing${NC}"
    exit 0
else
    echo -e "\n${RED}⚠️ Some workflow consolidation validations failed!${NC}"
    echo -e "${RED}❌ Please fix the issues before proceeding${NC}"
    exit 1
fi
