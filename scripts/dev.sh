#!/bin/bash
#
# A2A Platform - Development Environment
#
# This script starts the A2A Platform in development mode using docker-compose.dev.yml.
# It provides a complete development environment with hot reloading for the frontend.
#
# Usage:
#   ./scripts/dev.sh [COMMAND] [OPTIONS]
#
# Commands:
#   start (default)  Start the services
#   stop             Stop the services
#   status           Show status of running services
#
# Options:
#   --clean         Remove existing containers before starting
#   --build         Force rebuild of containers
#   --frontend-only Start only the frontend service
#   --backend-only  Start only the backend services (PostgreSQL, Redis, backend)
#   --storybook     Start only Storybook service
#   --with-storybook Include Storybook with other services
#   --wait          Wait for services to be ready (in addition to <PERSON><PERSON>'s healthchecks)
#   --help          Display this help message
#
# Examples:
#   ./scripts/dev.sh              # Start all services in dev mode
#   ./scripts/dev.sh --build      # Rebuild and start all services in dev mode
#   ./scripts/dev.sh --clean      # Clean and start all services in dev mode
#   ./scripts/dev.sh --frontend-only # Start only the frontend in dev mode
#   ./scripts/dev.sh --storybook  # Start only Storybook in dev mode
#   ./scripts/dev.sh --with-storybook # Start all services including Storybook
#   ./scripts/dev.sh --wait       # Start all services and wait for them to be ready
#   ./scripts/dev.sh stop         # Stop all services
#   ./scripts/dev.sh status       # Show status of all services

set -e

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"

# Default values
FORCE_BUILD=false
CLEAN=false
FRONTEND_ONLY=false
BACKEND_ONLY=false
STORYBOOK_ONLY=false
WITH_STORYBOOK=false
WAIT=false
COMMAND="start"

# Docker compose file
COMPOSE_FILE="${PROJECT_ROOT}/docker-compose.dev.yml"

# Function to display help message
show_help() {
  grep "^#" "$0" | grep -v "!/bin/bash" | sed 's/^# \?//'
  exit 0
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    start|stop|status)
      COMMAND="$1"
      shift
      ;;
    --build)
      FORCE_BUILD=true
      shift
      ;;
    --clean)
      CLEAN=true
      shift
      ;;
    --frontend-only)
      FRONTEND_ONLY=true
      shift
      ;;
    --backend-only)
      BACKEND_ONLY=true
      shift
      ;;
    --storybook)
      STORYBOOK_ONLY=true
      shift
      ;;
    --with-storybook)
      WITH_STORYBOOK=true
      shift
      ;;
    --wait)
      WAIT=true
      shift
      ;;
    --help)
      show_help
      ;;
    *)
      echo "Unknown option: $1"
      show_help
      ;;
  esac
done

# Check if docker compose is available
if ! command -v docker &> /dev/null; then
  echo "Error: Docker is not installed or not in PATH"
  exit 1
fi

# Determine which services to include
SERVICES=""
if [ "$FRONTEND_ONLY" = true ]; then
  SERVICES="frontend"
elif [ "$BACKEND_ONLY" = true ]; then
  SERVICES="db redis backend rq-worker"
elif [ "$STORYBOOK_ONLY" = true ]; then
  SERVICES="storybook"
elif [ "$WITH_STORYBOOK" = true ]; then
  SERVICES="db redis backend rq-worker frontend storybook"
fi

# Build the docker compose command
DOCKER_COMPOSE_CMD="docker compose -f ${COMPOSE_FILE}"

# Execute the appropriate command
case "$COMMAND" in
  start)
    echo "Starting development environment..."

    # Clean if requested
    if [ "$CLEAN" = true ]; then
      echo "Cleaning existing containers..."
      $DOCKER_COMPOSE_CMD down -v
    fi

    # Build command options
    START_OPTS=""
    if [ "$FORCE_BUILD" = true ]; then
      START_OPTS="--build"
    fi

    # Start the services
    if [ -n "$SERVICES" ]; then
      echo "Starting selected services: $SERVICES"
      $DOCKER_COMPOSE_CMD up $START_OPTS -d $SERVICES
    else
      echo "Starting all services..."
      $DOCKER_COMPOSE_CMD up $START_OPTS -d
    fi

    # Wait for services if requested
    if [ "$WAIT" = true ]; then
      echo "Waiting for services to be ready..."
      # This relies on the healthchecks defined in docker-compose.dev.yml
      sleep 5
      $DOCKER_COMPOSE_CMD ps
    fi

    echo "Development environment is running"
    if [ "$STORYBOOK_ONLY" = true ]; then
      echo "Storybook: https://localhost:6006"
    elif [ "$FRONTEND_ONLY" = true ]; then
      echo "Frontend: https://localhost:5173"
    elif [ "$BACKEND_ONLY" = true ]; then
      echo "Backend API: https://localhost:8000"
    else
      echo "Frontend: https://localhost:5173"
      echo "Backend API: https://localhost:8000"
      if [ "$WITH_STORYBOOK" = true ]; then
        echo "Storybook: https://localhost:6006"
      fi
    fi
    ;;

  stop)
    echo "Stopping development environment..."
    $DOCKER_COMPOSE_CMD down
    echo "Development environment stopped"
    ;;

  status)
    echo "Development environment status:"
    $DOCKER_COMPOSE_CMD ps
    ;;

  *)
    echo "Unknown command: $COMMAND"
    show_help
    ;;
esac
