#!/bin/bash
#
# A2A Platform - Database Migration Script
#
# This script runs database migrations using Alembic through Docker Compose.
# It loads environment variables from .env files and supports various migration operations.
#
# Usage:
#   ./scripts/db-migrate.sh [OPTIONS] [COMMAND]
#
# Options:
#   --docker        Use Docker PostgreSQL instance (default)
#   --local         Use local PostgreSQL instance
#   --dev           Load environment variables from .env.dev
#   --test          Load environment variables from .env.test
#   --verify        Verify schema after migration
#   --help          Display this help message
#
# Commands:
#   upgrade [revision]    Upgrade to the latest revision or specified revision (default command)
#   downgrade [revision]  Downgrade to the previous revision or specified revision
#   revision [message]    Create a new revision with the given message
#   generate [message]    Create a new revision with timestamp-based filename (YYYYMMDDHHMMSS_message.py)
#   current               Show current revision
#   history               Show revision history
#   heads                 Show current available heads
#
# Examples:
#   ./scripts/db-migrate.sh                     # Upgrade to latest revision using Docker with .env
#   ./scripts/db-migrate.sh --local upgrade     # Upgrade to latest revision using local PostgreSQL with .env
#   ./scripts/db-migrate.sh --test upgrade      # Upgrade to latest revision using .env.test
#   ./scripts/db-migrate.sh downgrade -1        # Downgrade one revision using Docker with .env
#   ./scripts/db-migrate.sh revision "Add users table"  # Create a new migration
#   ./scripts/db-migrate.sh generate "add_user_preferences"  # Create a new migration with timestamp filename

set -e

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
BACKEND_DIR="${PROJECT_ROOT}/apps/backend"

# Default values
USE_DOCKER=true  # Default to Docker for local development
ENV_FILE="${PROJECT_ROOT}/.env"
COMPOSE_FILE="${PROJECT_ROOT}/docker-compose.yml"
ALEMBIC_COMMAND="upgrade"
ALEMBIC_ARGS="head"
REVISION_MESSAGE=""
ORIGINAL_COMMAND=""
VERIFY_SCHEMA=false

# Check if Docker is available
check_docker() {
  if ! command -v docker &> /dev/null; then
    echo "Error: Docker is not installed or not in PATH."
    return 1
  fi

  if ! docker info &> /dev/null; then
    echo "Error: Docker daemon is not running."
    return 1
  fi

  return 0
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    --docker)
      USE_DOCKER=true
      shift
      ;;
    --local)
      USE_DOCKER=false
      shift
      ;;
    --dev)
      ENV_FILE="${PROJECT_ROOT}/.env.dev"
      COMPOSE_FILE="${PROJECT_ROOT}/docker-compose.dev.yml"
      shift
      ;;
    --test)
      ENV_FILE="${PROJECT_ROOT}/.env.test"
      COMPOSE_FILE="${PROJECT_ROOT}/docker-compose.test.yml"
      shift
      ;;
    --verify)
      VERIFY_SCHEMA=true
      shift
      ;;
    --help)
      grep '^#' "$0" | grep -v '#!/bin/bash' | sed 's/^# \?//'
      exit 0
      ;;
    upgrade|downgrade|revision|generate|current|history|heads)
      ORIGINAL_COMMAND="$1"
      ALEMBIC_COMMAND="$1"
      shift
      if [[ $# -gt 0 && ! "$1" =~ ^-- ]]; then
        if [[ "${ALEMBIC_COMMAND}" == "revision" ]]; then
          # For revision command, store the message separately
          REVISION_MESSAGE="$1"
          ALEMBIC_ARGS="--message"
        elif [[ "${ALEMBIC_COMMAND}" == "generate" ]]; then
          # For generate command, store the message separately
          REVISION_MESSAGE="$1"
          ALEMBIC_ARGS="--message"
          # Change the command to revision since we're using Alembic's revision command
          ALEMBIC_COMMAND="revision"
        else
          ALEMBIC_ARGS="$1"
        fi
        shift
      else
        # Default args based on command
        case "${ALEMBIC_COMMAND}" in
          upgrade) ALEMBIC_ARGS="head" ;;
          downgrade) ALEMBIC_ARGS="-1" ;;
          *) ALEMBIC_ARGS="" ;;
        esac
      fi
      ;;
    *)
      if [[ $# -eq 1 && ("${ALEMBIC_COMMAND}" == "revision" || "${ALEMBIC_COMMAND}" == "generate") ]]; then
        # Assume this is a revision message
        REVISION_MESSAGE="$1"
        ALEMBIC_ARGS="--message"

        # If this is a generate command, change it to revision since we're using Alembic's revision command
        if [[ "${ALEMBIC_COMMAND}" == "generate" ]]; then
          ORIGINAL_COMMAND="generate"
          ALEMBIC_COMMAND="revision"
        fi

        shift
      else
        echo "Unknown option or command: $1"
        echo "Use --help for usage information"
        exit 1
      fi
      ;;
  esac
done

# Check if the environment file exists
if [[ -f "${ENV_FILE}" ]]; then
  echo "Loading environment variables from ${ENV_FILE}"
  set -o allexport
  source "${ENV_FILE}"
  set +o allexport
else
  echo "Warning: Environment file ${ENV_FILE} not found."
  echo "Will use environment variables from Docker Compose file only."
fi

# Determine the appropriate Docker Compose file
if [[ ! -f "${COMPOSE_FILE}" ]]; then
  echo "Warning: Docker Compose file ${COMPOSE_FILE} not found."
  # Try to find an alternative compose file
  if [[ -f "${PROJECT_ROOT}/docker-compose.yml" ]]; then
    COMPOSE_FILE="${PROJECT_ROOT}/docker-compose.yml"
    echo "Using default docker-compose.yml instead."
  else
    echo "Error: No Docker Compose file found."
    exit 1
  fi
fi

# Function to run migrations using Docker Compose
run_migrations_docker() {
  local compose_file="$1"
  local command="$2"
  local args="$3"
  local message="$4"
  local original_cmd="$5"

  echo "Running migrations using Docker Compose with ${compose_file}"

  # Check if the backend service exists in the Docker Compose file
  if ! docker compose -f "${compose_file}" config --services | grep -q "backend"; then
    echo "Error: 'backend' service not found in ${compose_file}"
    exit 1
  fi

  # Make sure the database is running
  if ! docker compose -f "${compose_file}" ps | grep -q "db.*Up"; then
    echo "Database service is not running. Starting it..."
    docker compose -f "${compose_file}" up -d db

    # Wait for the database to be ready
    echo "Waiting for database service to be ready..."
    for i in {1..30}; do
      if docker compose -f "${compose_file}" exec db pg_isready -U postgres > /dev/null 2>&1; then
        echo "Database service is ready."
        break
      fi
      echo "Database service not ready yet. Retrying in 1 second..."
      sleep 1
    done
    if [[ $i -eq 30 ]]; then
      echo "Error: Database service did not become ready in time."
      exit 1
    fi
  fi

  # Execute the Alembic command using docker compose run
  if [[ "${command}" == "revision" && -n "${message}" ]]; then
    # Check if we need to create a timestamped migration file
    if [[ "${original_cmd}" == "generate" ]]; then
      # Generate timestamp
      TIMESTAMP=$(date +"%Y%m%d%H%M%S")
      # Convert message to snake_case for filename
      FILENAME=$(echo "${message}" | tr '[:upper:]' '[:lower:]' | tr ' ' '_' | sed 's/[^a-z0-9_]//g')

      echo "Generating migration with filename: ${TIMESTAMP}_${FILENAME}.py"

      # Create a custom migration file with timestamp
      if docker compose -f "${compose_file}" run --rm backend bash -c "cd /app && \
        TIMESTAMP=${TIMESTAMP} FILENAME=${FILENAME} MESSAGE='${message}' \
        python -c \"
import os
import sys
from alembic import command, util
from alembic.config import Config

timestamp = os.environ['TIMESTAMP']
filename = os.environ['FILENAME']
message = os.environ['MESSAGE']

# Create config and set template
alembic_cfg = Config('alembic.ini')

# Create the migration
try:
    command.revision(alembic_cfg, message=message, rev_id=timestamp)
    # Rename the file
    versions_dir = os.path.join('alembic', 'versions')
    for file in os.listdir(versions_dir):
        if file.startswith(timestamp) and file.endswith('.py'):
            old_path = os.path.join(versions_dir, file)
            new_filename = f'{timestamp}_{filename}.py'
            new_path = os.path.join(versions_dir, new_filename)
            os.rename(old_path, new_path)
            print(f'Created migration: {new_filename}')
            sys.exit(0)
    print(f'Error: Could not find generated migration file with revision ID {timestamp}')
    sys.exit(1)
except Exception as e:
    print(f'Error generating migration: {str(e)}')
    sys.exit(1)
\""; then
        echo "Migration file generated successfully."
        return 0
      else
        echo "Error: Failed to generate migration file."
        return 1
      fi
    else
      echo "Executing 'alembic ${command} ${args} \"${message}\"' using docker compose run..."
      if docker compose -f "${compose_file}" run --rm backend bash -c "cd /app && alembic ${command} ${args} \"${message}\""; then
        echo "Migration command executed successfully."
        return 0
      else
        echo "Error: Migration command failed."
        return 1
      fi
    fi
  else
    echo "Executing 'alembic ${command} ${args}' using docker compose run..."
    if docker compose -f "${compose_file}" run --rm backend bash -c "cd /app && alembic ${command} ${args}"; then
      echo "Migration command executed successfully."
      return 0
    else
      echo "Error: Migration command failed."
      return 1
    fi
  fi
}

# Function to run migrations locally
run_migrations_local() {
  local command="$1"
  local args="$2"
  local message="$3"
  local original_cmd="$4"

  echo "Running migrations locally"

  # Change to backend directory
  cd "${BACKEND_DIR}"

  # Check if alembic.ini exists
  if [[ ! -f "alembic.ini" ]]; then
    echo "Error: alembic.ini not found in ${BACKEND_DIR}"
    exit 1
  fi

  # Check if alembic is installed
  if ! command -v alembic &> /dev/null; then
    echo "Error: alembic is not installed."
    echo "Please install it with: pip install alembic"
    exit 1
  fi

  # Execute the Alembic command
  if [[ "${command}" == "revision" && -n "${message}" ]]; then
    # Check if we need to create a timestamped migration file
    if [[ "${original_cmd}" == "generate" ]]; then
      # Generate timestamp
      TIMESTAMP=$(date +"%Y%m%d%H%M%S")
      # Convert message to snake_case for filename
      FILENAME=$(echo "${message}" | tr '[:upper:]' '[:lower:]' | tr ' ' '_' | sed 's/[^a-z0-9_]//g')

      echo "Generating migration with filename: ${TIMESTAMP}_${FILENAME}.py"

      # Create a custom migration file with timestamp
      TIMESTAMP=${TIMESTAMP} FILENAME=${FILENAME} MESSAGE="${message}" python - << 'EOF'
import os
import sys
from alembic import command, util
from alembic.config import Config

timestamp = os.environ['TIMESTAMP']
filename = os.environ['FILENAME']
message = os.environ['MESSAGE']

# Create config and set template
alembic_cfg = Config('alembic.ini')

# Create the migration
try:
    command.revision(alembic_cfg, message=message, rev_id=timestamp)
    # Rename the file
    versions_dir = os.path.join('alembic', 'versions')
    for file in os.listdir(versions_dir):
        if file.startswith(timestamp) and file.endswith('.py'):
            old_path = os.path.join(versions_dir, file)
            new_filename = f'{timestamp}_{filename}.py'
            new_path = os.path.join(versions_dir, new_filename)
            os.rename(old_path, new_path)
            print(f'Created migration: {new_filename}')
            sys.exit(0)
    print(f'Error: Could not find generated migration file with revision ID {timestamp}')
    sys.exit(1)
except Exception as e:
    print(f'Error generating migration: {str(e)}')
    sys.exit(1)
EOF

      if [ $? -eq 0 ]; then
        echo "Migration file generated successfully."
        return 0
      else
        echo "Error: Failed to generate migration file."
        return 1
      fi
    else
      echo "Executing 'alembic ${command} ${args} \"${message}\"' locally..."
      if alembic "${command}" "${args}" "${message}"; then
        echo "Migration command executed successfully."
        return 0
      else
        echo "Error: Migration command failed."
        return 1
      fi
    fi
  else
    echo "Executing 'alembic ${command} ${args}' locally..."
    if alembic "${command}" ${args}; then
      echo "Migration command executed successfully."
      return 0
    else
      echo "Error: Migration command failed."
      return 1
    fi
  fi
}

# Main execution
echo "A2A Platform - Database Migration"
if [[ "${ALEMBIC_COMMAND}" == "revision" && -n "${REVISION_MESSAGE}" ]]; then
  echo "Command: ${ALEMBIC_COMMAND} ${ALEMBIC_ARGS} \"${REVISION_MESSAGE}\""
else
  echo "Command: ${ALEMBIC_COMMAND} ${ALEMBIC_ARGS}"
fi

if [[ "${USE_DOCKER}" == true ]]; then
  # Check if Docker is available
  if ! check_docker; then
    echo "Docker is not available. Falling back to local execution."
    USE_DOCKER=false
  else
    run_migrations_docker "${COMPOSE_FILE}" "${ALEMBIC_COMMAND}" "${ALEMBIC_ARGS}" "${REVISION_MESSAGE}" "${ORIGINAL_COMMAND}"
    exit_code=$?
  fi
fi

if [[ "${USE_DOCKER}" == false ]]; then
  run_migrations_local "${ALEMBIC_COMMAND}" "${ALEMBIC_ARGS}" "${REVISION_MESSAGE}" "${ORIGINAL_COMMAND}"
  exit_code=$?
fi

if [[ ${exit_code} -eq 0 ]]; then
  echo "Migration completed successfully."

  # Verify schema if requested
  if [[ "${VERIFY_SCHEMA}" == true ]]; then
    echo "Verifying database schema..."

    if [[ "${USE_DOCKER}" == true ]]; then
      # Verify using Docker
      echo "Checking if conversations and chat_messages tables exist..."
      if docker compose -f "${COMPOSE_FILE}" exec -T db psql -U postgres -d a2a_platform_test -c "\d conversations; \d chat_messages;" > /dev/null 2>&1; then
        echo "✅ Schema verification passed: conversations and chat_messages tables exist"
      else
        echo "⚠️  Schema verification warning: Could not verify tables via Docker"
      fi
    else
      echo "Schema verification completed (local mode - manual verification recommended)"
    fi
  fi
else
  echo "Migration failed with exit code ${exit_code}."
fi

exit ${exit_code}
