#!/bin/bash
#
# A2A Platform - Test Performance Monitor
#
# This script monitors and tracks test execution performance across different test categories.
# It provides detailed timing information and performance regression detection.
#
# Usage:
#   ./scripts/monitor-test-performance.sh [OPTIONS]
#
# Options:
#   --baseline      Establish a new performance baseline
#   --compare       Compare current performance against baseline
#   --report        Generate a detailed performance report
#   --ci            Run in CI mode with JSON output
#   --help          Display this help message
#
# Examples:
#   ./scripts/monitor-test-performance.sh --baseline    # Establish baseline
#   ./scripts/monitor-test-performance.sh --compare     # Compare against baseline
#   ./scripts/monitor-test-performance.sh --report      # Generate report
#   ./scripts/monitor-test-performance.sh --ci          # CI mode with JSON output

set -e

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
BACKEND_DIR="${PROJECT_ROOT}/apps/backend"
PERFORMANCE_DIR="${PROJECT_ROOT}/tmp/performance"

# Default values
ESTABLISH_BASELINE=false
COMPARE_PERFORMANCE=false
GENERATE_REPORT=false
CI_MODE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    --baseline)
      ESTABLISH_BASELINE=true
      shift
      ;;
    --compare)
      COMPARE_PERFORMANCE=true
      shift
      ;;
    --report)
      GENERATE_REPORT=true
      shift
      ;;
    --ci)
      CI_MODE=true
      shift
      ;;
    --help)
      grep '^#' "$0" | grep -v '#!/bin/bash' | sed 's/^# \?//'
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

# Create performance directory if it doesn't exist
mkdir -p "${PERFORMANCE_DIR}"

# Function to run tests and measure performance
measure_test_performance() {
  local test_type="$1"
  local test_flags="$2"
  local description="$3"

  echo "Measuring performance for: ${description}"

  # Run tests and capture timing
  start_time=$(date +%s)
  if "${SCRIPT_DIR}/run-backend-tests.sh" ${test_flags} --ci > /dev/null 2>&1; then
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    status="PASSED"
  else
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    status="FAILED"
  fi

  # Format duration (integer seconds)
  duration_formatted="${duration}"

  echo "${test_type},${duration_formatted},${status},$(date -Iseconds)" >> "${PERFORMANCE_DIR}/current_run.csv"

  if [[ "${CI_MODE}" == false ]]; then
    echo "  ${description}: ${duration_formatted}s (${status})"
  fi
}

# Function to establish baseline
establish_baseline() {
  echo "🎯 Establishing Performance Baseline"
  echo "======================================"

  # Create baseline file header
  echo "test_type,duration_seconds,status,timestamp" > "${PERFORMANCE_DIR}/baseline.csv"
  echo "test_type,duration_seconds,status,timestamp" > "${PERFORMANCE_DIR}/current_run.csv"

  # Measure each test category
  measure_test_performance "database_free" "--no-db" "Database-Free Tests (102 tests)"
  measure_test_performance "sqlite" "--fast-db" "SQLite Tests (Fast Database)"
  measure_test_performance "postgresql" "--exclude-no-db --exclude-fast-db" "PostgreSQL Tests (Full Integration)"

  # Copy current run to baseline
  cp "${PERFORMANCE_DIR}/current_run.csv" "${PERFORMANCE_DIR}/baseline.csv"

  echo ""
  echo "✅ Baseline established successfully!"
  echo "📊 Baseline data saved to: ${PERFORMANCE_DIR}/baseline.csv"
}

# Function to compare performance
compare_performance() {
  echo "📊 Comparing Performance Against Baseline"
  echo "========================================="

  if [[ ! -f "${PERFORMANCE_DIR}/baseline.csv" ]]; then
    echo "❌ No baseline found. Please run with --baseline first."
    exit 1
  fi

  # Create current run file
  echo "test_type,duration_seconds,status,timestamp" > "${PERFORMANCE_DIR}/current_run.csv"

  # Measure current performance
  measure_test_performance "database_free" "--no-db" "Database-Free Tests (102 tests)"
  measure_test_performance "sqlite" "--fast-db" "SQLite Tests (Fast Database)"
  measure_test_performance "postgresql" "--exclude-no-db --exclude-fast-db" "PostgreSQL Tests (Full Integration)"

  echo ""
  echo "📈 Performance Comparison Results:"
  echo "=================================="

  # Compare results
  while IFS=',' read -r test_type baseline_duration baseline_status baseline_timestamp; do
    if [[ "${test_type}" == "test_type" ]]; then continue; fi  # Skip header

    # Get current duration for this test type
    current_line=$(grep "^${test_type}," "${PERFORMANCE_DIR}/current_run.csv")
    if [[ -n "${current_line}" ]]; then
      current_duration=$(echo "${current_line}" | cut -d',' -f2)
      current_status=$(echo "${current_line}" | cut -d',' -f3)

      # Calculate percentage change
      if [[ "${baseline_duration}" != "0" ]]; then
        percentage_change=$(( (current_duration - baseline_duration) * 100 / baseline_duration ))
      else
        percentage_change="N/A"
      fi

      # Determine status icon
      if [[ "${percentage_change}" != "N/A" ]] && [[ "${percentage_change}" -gt 10 ]]; then
        status_icon="🔴"  # Regression
      elif [[ "${percentage_change}" != "N/A" ]] && [[ "${percentage_change}" -lt -5 ]]; then
        status_icon="🟢"  # Improvement
      else
        status_icon="🟡"  # Stable
      fi

      echo "${status_icon} ${test_type}: ${current_duration}s (baseline: ${baseline_duration}s, change: ${percentage_change}%)"
    fi
  done < "${PERFORMANCE_DIR}/baseline.csv"
}

# Function to generate detailed report
generate_report() {
  echo "📋 Generating Detailed Performance Report"
  echo "========================================"

  local report_file="${PERFORMANCE_DIR}/performance_report_$(date +%Y%m%d_%H%M%S).md"

  cat > "${report_file}" << EOF
# A2A Platform Test Performance Report

**Generated:** $(date)
**Report Type:** Three-Tier Testing Performance Analysis

## Executive Summary

This report provides detailed performance metrics for the A2A Platform's three-tier testing strategy implementation.

## Test Categories Performance

EOF

  if [[ -f "${PERFORMANCE_DIR}/current_run.csv" ]]; then
    echo "### Current Run Results" >> "${report_file}"
    echo "" >> "${report_file}"

    while IFS=',' read -r test_type duration status timestamp; do
      if [[ "${test_type}" == "test_type" ]]; then continue; fi  # Skip header

      case "${test_type}" in
        "database_free")
          echo "- **Database-Free Tests**: ${duration}s (${status})" >> "${report_file}"
          echo "  - 102 tests covering business logic, schemas, authentication" >> "${report_file}"
          echo "  - No external dependencies required" >> "${report_file}"
          ;;
        "sqlite")
          echo "- **SQLite Tests**: ${duration}s (${status})" >> "${report_file}"
          echo "  - Fast database operations with SQLite" >> "${report_file}"
          echo "  - Service layer and basic CRUD testing" >> "${report_file}"
          ;;
        "postgresql")
          echo "- **PostgreSQL Tests**: ${duration}s (${status})" >> "${report_file}"
          echo "  - Full integration tests with PostgreSQL" >> "${report_file}"
          echo "  - Complex queries and PostgreSQL-specific features" >> "${report_file}"
          ;;
      esac
      echo "" >> "${report_file}"
    done < "${PERFORMANCE_DIR}/current_run.csv"
  fi

  cat >> "${report_file}" << EOF

## Performance Benefits

- **Parallel Execution**: All test categories can run simultaneously in CI
- **Fast Feedback**: Database-free tests provide instant validation (~3s)
- **Resource Optimization**: Only PostgreSQL tests require database services
- **Selective Testing**: Run only relevant test categories based on changes

## Recommendations

1. **Database-Free Tests**: Should complete in under 5 seconds
2. **SQLite Tests**: Should complete in under 15 seconds
3. **PostgreSQL Tests**: Should complete in under 60 seconds
4. **Total CI Time**: With parallel execution, total time should be dominated by the slowest category

---

*Report generated by A2A Platform Test Performance Monitor*
EOF

  echo "✅ Report generated: ${report_file}"

  if [[ "${CI_MODE}" == false ]]; then
    echo ""
    echo "📄 Report Preview:"
    echo "=================="
    head -30 "${report_file}"
    echo "..."
    echo "(Full report saved to file)"
  fi
}

# Function to output CI-friendly JSON
output_ci_json() {
  if [[ -f "${PERFORMANCE_DIR}/current_run.csv" ]]; then
    echo "{"
    echo '  "performance_results": ['

    first_line=true
    while IFS=',' read -r test_type duration status timestamp; do
      if [[ "${test_type}" == "test_type" ]]; then continue; fi  # Skip header

      if [[ "${first_line}" == false ]]; then
        echo ","
      fi
      first_line=false

      echo -n "    {"
      echo -n '"test_type": "'"${test_type}"'", '
      echo -n '"duration_seconds": '"${duration}"', '
      echo -n '"status": "'"${status}"'", '
      echo -n '"timestamp": "'"${timestamp}"'"'
      echo -n "}"
    done < "${PERFORMANCE_DIR}/current_run.csv"

    echo ""
    echo "  ],"
    echo '  "generated_at": "'"$(date -Iseconds)"'"'
    echo "}"
  else
    echo '{"error": "No performance data available"}'
  fi
}

# Main execution logic
if [[ "${ESTABLISH_BASELINE}" == true ]]; then
  establish_baseline
elif [[ "${COMPARE_PERFORMANCE}" == true ]]; then
  compare_performance
elif [[ "${GENERATE_REPORT}" == true ]]; then
  generate_report
else
  # Default: run comparison if baseline exists, otherwise establish baseline
  if [[ -f "${PERFORMANCE_DIR}/baseline.csv" ]]; then
    compare_performance
  else
    establish_baseline
  fi
fi

# Output CI JSON if in CI mode
if [[ "${CI_MODE}" == true ]]; then
  output_ci_json
fi

echo ""
echo "🎉 Performance monitoring completed!"
