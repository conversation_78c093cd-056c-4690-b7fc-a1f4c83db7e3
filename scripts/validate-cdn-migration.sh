#!/bin/bash
# CDN Migration Validation Script
# Validates CDN migration configuration and domain resolution
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT=${1:-staging}
VALIDATE_ONLY=${2:-false}

echo -e "${BLUE}🌐 Validating CDN migration for environment: $ENVIRONMENT${NC}"
echo "=================================================================="

# Set environment-specific URLs
if [[ "$ENVIRONMENT" == "production" ]]; then
    WEB_URL="https://vedavivi.app"
    API_URL="https://api.vedavivi.app"
    WS_URL="wss://ws.vedavivi.app"
    R2_BUCKET_NAME="a2a-platform-web-assets-production"
    R2_CUSTOM_DOMAIN="assets.vedavivi.app"
else
    WEB_URL="https://www-staging.vedavivi.app"
    API_URL="https://api-staging.vedavivi.app"
    WS_URL="wss://ws-staging.vedavivi.app"
    R2_BUCKET_NAME="a2a-platform-web-assets-staging"
    R2_CUSTOM_DOMAIN="assets-staging.vedavivi.app"
fi

# Validation counters
PASSED=0
FAILED=0

# Function to run validation checks
validate_check() {
    local test_name="$1"
    local command="$2"
    local expected="$3"

    echo -e "\n${BLUE}Testing: $test_name${NC}"

    if bash -c "$command"; then
        echo -e "${GREEN}✅ PASSED: $test_name${NC}"
        PASSED=$((PASSED + 1))
    else
        echo -e "${RED}❌ FAILED: $test_name${NC}"
        echo -e "${RED}   Expected: $expected${NC}"
        FAILED=$((FAILED + 1))
    fi
}

# Function to check HTTP status
check_http_status() {
    local url="$1"
    local expected_status="$2"

    local status=$(curl -s -o /dev/null -w "%{http_code}" "$url" || echo "000")
    [[ "$status" == "$expected_status" ]]
}

# Function to check SSL certificate
check_ssl_cert() {
    local domain="$1"

    # Extract domain from URL if needed
    domain=$(echo "$domain" | sed 's|https\?://||' | sed 's|/.*||')

    # Check SSL certificate validity
    echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | \
    openssl x509 -noout -dates 2>/dev/null | grep -q "notAfter"
}

# Function to check DNS resolution
check_dns_resolution() {
    local domain="$1"

    # Extract domain from URL if needed
    domain=$(echo "$domain" | sed 's|https\?://||' | sed 's|wss\?://||' | sed 's|/.*||')

    # Check if domain resolves
    nslookup "$domain" >/dev/null 2>&1
}

# Function to check cache headers
check_cache_headers() {
    local url="$1"
    local expected_pattern="$2"

    local headers=$(curl -s -I "$url" | grep -i "cache-control" || echo "")
    [[ "$headers" =~ $expected_pattern ]]
}

# Function to check Cloudflare headers
check_cloudflare_headers() {
    local url="$1"

    local cf_ray=$(curl -s -I "$url" | grep -i "cf-ray" || echo "")
    [[ -n "$cf_ray" ]]
}

echo -e "\n${YELLOW}🔍 Starting CDN Migration Validation Tests${NC}"

if [[ "$VALIDATE_ONLY" == "--validate-only" ]]; then
    echo -e "${YELLOW}⚠️ Running in validate-only mode - skipping live environment tests${NC}"
    echo -e "${BLUE}🔍 Performing syntax and configuration validation only${NC}"

    # 1. Configuration Syntax Tests
    echo -e "\n${BLUE}📋 Configuration Validation Tests${NC}"
    validate_check "Environment URLs are properly formatted" "
        [[ '$WEB_URL' =~ ^https:// ]] && [[ '$API_URL' =~ ^https:// ]] && [[ '$WS_URL' =~ ^wss:// ]]
    " "URLs should use proper protocols"

    validate_check "Domain names follow expected patterns" "
        if [[ '$ENVIRONMENT' == 'production' ]]; then
            [[ '$WEB_URL' == 'https://vedavivi.app' ]] && [[ '$API_URL' == 'https://api.vedavivi.app' ]]
        else
            [[ '$WEB_URL' == 'https://www-staging.vedavivi.app' ]] && [[ '$API_URL' == 'https://api-staging.vedavivi.app' ]]
        fi
    " "Domain patterns should match environment"

    validate_check "R2 bucket name follows naming convention" "
        [[ '$R2_BUCKET_NAME' =~ ^a2a-platform-web-assets-(staging|production)$ ]]
    " "R2 bucket name should follow convention"

    validate_check "R2 custom domain follows naming convention" "
        if [[ '$ENVIRONMENT' == 'production' ]]; then
            [[ '$R2_CUSTOM_DOMAIN' == 'assets.vedavivi.app' ]]
        else
            [[ '$R2_CUSTOM_DOMAIN' == 'assets-staging.vedavivi.app' ]]
        fi
    " "R2 custom domain should match environment"

else
    # Live environment tests
    # 1. Domain Resolution Tests
    echo -e "\n${BLUE}📡 Domain Resolution Tests${NC}"
    validate_check "Web domain DNS resolution" "check_dns_resolution '$WEB_URL'" "Domain should resolve to IP"
    validate_check "API domain DNS resolution" "check_dns_resolution '$API_URL'" "Domain should resolve to IP"
    validate_check "WebSocket domain DNS resolution" "check_dns_resolution '$WS_URL'" "Domain should resolve to IP"

    # 2. SSL Certificate Tests
    echo -e "\n${BLUE}🔒 SSL Certificate Tests${NC}"
    validate_check "Web domain SSL certificate" "check_ssl_cert '$WEB_URL'" "Valid SSL certificate"
    validate_check "API domain SSL certificate" "check_ssl_cert '$API_URL'" "Valid SSL certificate"
    validate_check "WebSocket domain SSL certificate" "check_ssl_cert '$WS_URL'" "Valid SSL certificate"

    # 3. HTTP Response Tests
    echo -e "\n${BLUE}🌐 HTTP Response Tests${NC}"
    validate_check "Web application accessibility" "check_http_status '$WEB_URL' '200'" "HTTP 200 status"
    validate_check "API health endpoint" "check_http_status '$API_URL/health' '200'" "HTTP 200 status"

    # 4. CDN Configuration Tests
    echo -e "\n${BLUE}☁️ CDN Configuration Tests${NC}"
    validate_check "Cloudflare CDN headers present" "check_cloudflare_headers '$WEB_URL'" "CF-RAY header present"
    validate_check "HTML cache headers (5min)" "check_cache_headers '$WEB_URL' 'max-age=300'" "Cache-Control: max-age=300"

    # 5. Security Tests
    echo -e "\n${BLUE}🛡️ Security Tests${NC}"

    # Check HTTPS redirect
    validate_check "HTTP to HTTPS redirect" "
        redirect_status=\$(curl -s -o /dev/null -w '%{http_code}' 'http://\$(echo '$WEB_URL' | sed 's|https://||')')
        [[ \"\$redirect_status\" =~ ^30[1-8]$ ]]
    " "HTTP 301/302/308 redirect status"

    # Check HSTS headers
    validate_check "HSTS security headers" "
        hsts_header=\$(curl -s -I '$WEB_URL' | grep -i 'strict-transport-security' || echo '')
        [[ -n \"\$hsts_header\" ]]
    " "Strict-Transport-Security header present"

    # 6. Performance Tests
    echo -e "\n${BLUE}⚡ Performance Tests${NC}"

    # Check TTFB (Time to First Byte)
    validate_check "TTFB performance (<150ms)" "
        ttfb=\$(curl -s -w '%{time_starttransfer}' -o /dev/null '$WEB_URL')
        (( \$(echo \"\$ttfb < 0.150\" | bc -l) ))
    " "TTFB should be under 150ms"
fi

# 7. R2 Storage Security Tests
echo -e "\n${BLUE}🔒 R2 Storage Security Tests${NC}"

if [[ "$VALIDATE_ONLY" != "--validate-only" ]]; then
    # Test R2 custom domain accessibility
    validate_check "R2 custom domain accessibility" "
        check_http_status 'https://$R2_CUSTOM_DOMAIN' '200'
    " "R2 custom domain should be accessible"

    # Test R2 custom domain SSL
    validate_check "R2 custom domain SSL certificate" "
        check_ssl_cert 'https://$R2_CUSTOM_DOMAIN'
    " "R2 custom domain should have valid SSL"

    # Test R2 custom domain Cloudflare integration
    validate_check "R2 custom domain Cloudflare headers" "
        check_cloudflare_headers 'https://$R2_CUSTOM_DOMAIN'
    " "R2 custom domain should have Cloudflare headers"
else
    echo -e "${YELLOW}⏭️ SKIPPED: R2 storage live tests (validate-only mode)${NC}"

    # In validate-only mode, validate configuration
    validate_check "R2 configuration variables are set" "
        [[ -n '$R2_BUCKET_NAME' ]] && [[ -n '$R2_CUSTOM_DOMAIN' ]]
    " "R2 bucket name and custom domain should be configured"
fi

# Summary
echo -e "\n${BLUE}=================================================================="
echo -e "📊 CDN Migration Validation Summary${NC}"
echo -e "=================================================================="
echo -e "${GREEN}✅ Passed: $PASSED tests${NC}"
echo -e "${RED}❌ Failed: $FAILED tests${NC}"
echo -e "Total: $((PASSED + FAILED)) tests"

if [[ $FAILED -eq 0 ]]; then
    echo -e "\n${GREEN}🎉 All CDN migration validation tests passed!${NC}"
    echo -e "${GREEN}✅ CDN migration is properly configured for $ENVIRONMENT environment${NC}"
    exit 0
else
    echo -e "\n${RED}⚠️ Some CDN migration validation tests failed!${NC}"
    echo -e "${RED}❌ Please review and fix the issues before proceeding${NC}"
    exit 1
fi
