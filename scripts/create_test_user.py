#!/usr/bin/env python3
"""
Create a test user for development and testing purposes.
This script creates a test user in the database for testing workflows.
"""
import sys
import os
import uuid
import argparse
import asyncio
from typing import Optional

# Add the source directory to the path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'apps', 'backend', 'src'))

try:
    from a2a_platform.db.session import get_async_session
    from a2a_platform.db.models.user import User
    from sqlalchemy import select
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Make sure you're running this from the project root and dependencies are installed.")
    sys.exit(1)

async def create_test_user(email: Optional[str] = None, name: Optional[str] = None) -> dict:
    """Create a test user in the database."""
    
    # Generate defaults if not provided
    if not email:
        user_id = str(uuid.uuid4())[:8]
        email = f"test-user-{user_id}@example.com"
    
    if not name:
        name = f"Test User {email.split('@')[0].split('-')[-1]}"
    
    # Generate test data
    user_data = {
        'id': str(uuid.uuid4()),
        'email': email,
        'name': name,
        'clerk_user_id': f"test_clerk_{str(uuid.uuid4())[:8]}",
        'timezone': 'UTC',
        'is_pa_setup_complete': False
    }
    
    async with get_async_session() as session:
        # Check if user already exists
        existing_user = await session.execute(
            select(User).where(User.email == email)
        )
        if existing_user.scalar_one_or_none():
            raise ValueError(f"User with email {email} already exists")
        
        # Create new user
        new_user = User(
            id=user_data['id'],
            email=user_data['email'],
            name=user_data['name'],
            clerk_user_id=user_data['clerk_user_id'],
            timezone=user_data['timezone'],
            is_pa_setup_complete=user_data['is_pa_setup_complete']
        )
        
        session.add(new_user)
        await session.commit()
        await session.refresh(new_user)
        
        return {
            'id': new_user.id,
            'email': new_user.email,
            'name': new_user.name,
            'clerk_user_id': new_user.clerk_user_id,
            'timezone': new_user.timezone,
            'created_at': new_user.created_at.isoformat() if new_user.created_at else None
        }

async def main():
    """Main function to handle command line arguments and create test user."""
    parser = argparse.ArgumentParser(description='Create a test user for development and testing')
    parser.add_argument('--email', help='Email for the test user')
    parser.add_argument('--name', help='Name for the test user')
    parser.add_argument('--output', choices=['json', 'simple'], default='simple', 
                       help='Output format (default: simple)')
    
    args = parser.parse_args()
    
    try:
        user_data = await create_test_user(args.email, args.name)
        
        if args.output == 'json':
            import json
            print(json.dumps(user_data, indent=2))
        else:
            print("✅ Test User Created Successfully:")
            print(f"  ID: {user_data['id']}")
            print(f"  Email: {user_data['email']}")
            print(f"  Name: {user_data['name']}")
            print(f"  Clerk ID: {user_data['clerk_user_id']}")
            print(f"  Timezone: {user_data['timezone']}")
            if user_data['created_at']:
                print(f"  Created: {user_data['created_at']}")
            print(f"\n📧 Use email '{user_data['email']}' for testing authentication flows.")
        
        return 0
        
    except ValueError as e:
        print(f"❌ User creation failed: {e}", file=sys.stderr)
        return 1
    except Exception as e:
        print(f"❌ Error creating test user: {e}", file=sys.stderr)
        return 1

if __name__ == '__main__':
    sys.exit(asyncio.run(main()))