#!/bin/bash
#
# A2A Platform - Test Database Reset Script
#
# This script drops and recreates the test database, then runs migrations to set up the schema.
# It is useful for quickly resetting the test database to a clean state.
#
# Usage:
#   ./scripts/reset-test-db.sh [OPTIONS]
#
# Options:
#   --verify        Verify the database tables after migration
#   --help          Display this help message
#
# Examples:
#   ./scripts/reset-test-db.sh             # Reset the test database
#   ./scripts/reset-test-db.sh --verify    # Reset the test database and verify tables

set -e

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
COMPOSE_FILE="${PROJECT_ROOT}/docker-compose.test.yml"
DB_NAME="a2a_platform_test"
VERIFY_DB=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    --verify)
      VERIFY_DB=true
      shift
      ;;
    --help)
      grep '^#' "$0" | grep -v '#!/bin/bash' | sed 's/^# \?//'
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

# Check if Docker is available
if ! command -v docker &> /dev/null; then
  echo "Error: Docker is not installed or not in PATH."
  exit 1
fi

if ! docker info &> /dev/null; then
  echo "Error: Docker daemon is not running."
  exit 1
fi

# Check if the database container is running
if ! docker compose -f "${COMPOSE_FILE}" ps | grep -q "db.*Up"; then
  echo "Starting database container..."
  docker compose -f "${COMPOSE_FILE}" up -d db
  
  # Wait for the database to be ready
  echo "Waiting for database to be ready..."
  for i in {1..30}; do
    if docker compose -f "${COMPOSE_FILE}" exec db pg_isready -U postgres > /dev/null 2>&1; then
      echo "Database is ready."
      break
    fi
    echo "Database not ready yet. Retrying in 1 second..."
    sleep 1
  done
  if [[ $i -eq 30 ]]; then
    echo "Error: Database did not become ready in time."
    exit 1
  fi
fi

# Drop the database if it exists
echo "Dropping database '${DB_NAME}' if it exists..."
docker compose -f "${COMPOSE_FILE}" exec db psql -U postgres -c "DROP DATABASE IF EXISTS ${DB_NAME};"

# Create the database
echo "Creating database '${DB_NAME}'..."
docker compose -f "${COMPOSE_FILE}" exec db psql -U postgres -c "CREATE DATABASE ${DB_NAME} WITH OWNER postgres;"

# Run migrations
echo "Running migrations..."
"${SCRIPT_DIR}/db-migrate.sh" --test upgrade head

# Verify the database if requested
if [[ "${VERIFY_DB}" == true ]]; then
  echo "Verifying database tables..."
  
  # Check if the cli_tokens table exists
  echo "Checking if cli_tokens table exists..."
  if docker compose -f "${COMPOSE_FILE}" exec db psql -U postgres -d "${DB_NAME}" -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'cli_tokens');" | grep -q "t"; then
    echo "✅ cli_tokens table exists."
  else
    echo "❌ cli_tokens table does not exist."
    exit 1
  fi
  
  # List all tables
  echo "Listing all tables in the database..."
  docker compose -f "${COMPOSE_FILE}" exec db psql -U postgres -d "${DB_NAME}" -c "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;"
  
  # Check the current Alembic version
  echo "Checking current Alembic version..."
  docker compose -f "${COMPOSE_FILE}" exec db psql -U postgres -d "${DB_NAME}" -c "SELECT * FROM alembic_version;"
fi

echo "Test database reset completed successfully!"
exit 0
