#!/bin/bash
# <PERSON><PERSON>t to run the webhook signature verification tests using Docker Compose

set -e

# Navigate to the project root
cd "$(dirname "$0")/.."

# Ensure we're using the test environment variables
ENV_FILE=".env.test"

# Run the tests using Docker Compose
echo "Running webhook signature verification tests with Docker Compose..."
docker compose --env-file $ENV_FILE run --rm backend python -m pytest tests/unit/auth/test_clerk_webhook_verification.py -v

echo "Tests completed."
