#!/bin/bash
#
# A2A Platform - Selective Test Runner
#
# This script analyzes code changes and runs only the relevant test categories,
# optimizing CI execution time while maintaining comprehensive coverage.
#
# Usage:
#   ./scripts/selective-test-runner.sh [OPTIONS]
#
# Options:
#   --base-ref REF    Base reference for comparison (default: main)
#   --head-ref REF    Head reference for comparison (default: HEAD)
#   --dry-run         Show what tests would be run without executing them
#   --force-all       Force running all test categories regardless of changes
#   --ci              Run in CI mode with optimized output
#   --help            Display this help message
#
# Examples:
#   ./scripts/selective-test-runner.sh                           # Compare HEAD with main
#   ./scripts/selective-test-runner.sh --base-ref origin/main   # Compare with origin/main
#   ./scripts/selective-test-runner.sh --dry-run                # Show what would run
#   ./scripts/selective-test-runner.sh --force-all              # Run all tests

set -e

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"

# Default values
BASE_REF="main"
HEAD_REF="HEAD"
DRY_RUN=false
FORCE_ALL=false
CI_MODE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    --base-ref)
      BASE_REF="$2"
      shift 2
      ;;
    --head-ref)
      HEAD_REF="$2"
      shift 2
      ;;
    --dry-run)
      DRY_RUN=true
      shift
      ;;
    --force-all)
      FORCE_ALL=true
      shift
      ;;
    --ci)
      CI_MODE=true
      shift
      ;;
    --help)
      grep '^#' "$0" | grep -v '#!/bin/bash' | sed 's/^# \?//'
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

# Change to project root
cd "${PROJECT_ROOT}"

# Function to get changed files
get_changed_files() {
  # Try to get changed files using git diff
  if git rev-parse --verify "${BASE_REF}" >/dev/null 2>&1; then
    git diff --name-only "${BASE_REF}...${HEAD_REF}" 2>/dev/null || {
      echo "Warning: Could not determine changed files. Running all tests." >&2
      echo "all"
      return
    }
  else
    echo "Warning: Base reference '${BASE_REF}' not found. Running all tests." >&2
    echo "all"
    return
  fi
}

# Function to analyze changes and determine test categories
analyze_changes() {
  local changed_files="$1"
  
  # Initialize test category flags
  local run_database_free=false
  local run_sqlite=false
  local run_postgresql=false
  local run_all_reason=""
  
  # If we couldn't determine changes or force-all is set, run everything
  if [[ "${changed_files}" == "all" ]] || [[ "${FORCE_ALL}" == true ]]; then
    run_database_free=true
    run_sqlite=true
    run_postgresql=true
    run_all_reason="Unable to determine changes or --force-all specified"
  else
    # Analyze each changed file
    while IFS= read -r file; do
      [[ -z "$file" ]] && continue
      
      case "$file" in
        # Backend source code changes
        apps/backend/src/*)
          # Determine which test categories are affected
          if [[ "$file" =~ (schemas|models|validation) ]]; then
            run_database_free=true  # Schema changes affect database-free tests
          fi
          
          if [[ "$file" =~ (services|api|routes) ]]; then
            run_database_free=true  # Service changes affect database-free tests
            run_postgresql=true     # Service changes may affect integration tests
          fi
          
          if [[ "$file" =~ (db|database|migration) ]]; then
            run_sqlite=true         # Database changes affect SQLite tests
            run_postgresql=true     # Database changes affect PostgreSQL tests
          fi
          
          if [[ "$file" =~ (messaging|queue|worker) ]]; then
            run_database_free=true  # Messaging changes affect database-free tests
          fi
          ;;
          
        # Test file changes
        apps/backend/tests/*)
          if [[ "$file" =~ @pytest\.mark\.no_db ]] || grep -q "@pytest.mark.no_db" "$file" 2>/dev/null; then
            run_database_free=true
          elif [[ "$file" =~ @pytest\.mark\.fast_db ]] || grep -q "@pytest.mark.fast_db" "$file" 2>/dev/null; then
            run_sqlite=true
          else
            run_postgresql=true
          fi
          ;;
          
        # CI/CD changes
        .github/workflows/*)
          run_database_free=true
          run_sqlite=true
          run_postgresql=true
          run_all_reason="CI/CD configuration changed"
          ;;
          
        # Test infrastructure changes
        scripts/run-backend-tests.sh|scripts/*test*.sh)
          run_database_free=true
          run_sqlite=true
          run_postgresql=true
          run_all_reason="Test infrastructure changed"
          ;;
          
        # Configuration changes
        apps/backend/pyproject.toml|apps/backend/pytest.ini)
          run_database_free=true
          run_sqlite=true
          run_postgresql=true
          run_all_reason="Test configuration changed"
          ;;
          
        # Docker or environment changes
        docker-compose*.yml|Dockerfile*|.env*)
          run_database_free=true
          run_sqlite=true
          run_postgresql=true
          run_all_reason="Environment configuration changed"
          ;;
      esac
    done <<< "$changed_files"
  fi
  
  # If no specific categories were triggered, default to database-free tests
  if [[ "$run_database_free" == false ]] && [[ "$run_sqlite" == false ]] && [[ "$run_postgresql" == false ]]; then
    run_database_free=true
  fi
  
  # Output results
  echo "database_free:$run_database_free"
  echo "sqlite:$run_sqlite"
  echo "postgresql:$run_postgresql"
  echo "reason:$run_all_reason"
}

# Function to execute tests
execute_tests() {
  local run_database_free="$1"
  local run_sqlite="$2"
  local run_postgresql="$3"
  local reason="$4"
  
  local test_commands=()
  local parallel_jobs=()
  
  echo "🚀 Selective Test Execution Plan"
  echo "================================"
  
  if [[ -n "$reason" ]]; then
    echo "Reason: $reason"
  fi
  
  echo ""
  echo "Test Categories to Execute:"
  
  # Build test commands
  if [[ "$run_database_free" == true ]]; then
    echo "✅ Database-Free Tests (102 tests, ~3s)"
    test_commands+=("${SCRIPT_DIR}/run-backend-tests.sh --no-db --ci")
    if [[ "${CI_MODE}" == true ]]; then
      parallel_jobs+=("database_free")
    fi
  else
    echo "⏭️  Database-Free Tests (skipped)"
  fi
  
  if [[ "$run_sqlite" == true ]]; then
    echo "✅ SQLite Tests (~10s)"
    test_commands+=("${SCRIPT_DIR}/run-backend-tests.sh --fast-db --ci")
    if [[ "${CI_MODE}" == true ]]; then
      parallel_jobs+=("sqlite")
    fi
  else
    echo "⏭️  SQLite Tests (skipped)"
  fi
  
  if [[ "$run_postgresql" == true ]]; then
    echo "✅ PostgreSQL Tests (full integration)"
    test_commands+=("${SCRIPT_DIR}/run-backend-tests.sh --exclude-no-db --exclude-fast-db --ci")
    if [[ "${CI_MODE}" == true ]]; then
      parallel_jobs+=("postgresql")
    fi
  else
    echo "⏭️  PostgreSQL Tests (skipped)"
  fi
  
  echo ""
  
  # Execute tests
  if [[ "${DRY_RUN}" == true ]]; then
    echo "🔍 Dry Run - Commands that would be executed:"
    for cmd in "${test_commands[@]}"; do
      echo "  $cmd"
    done
    return 0
  fi
  
  if [[ ${#test_commands[@]} -eq 0 ]]; then
    echo "ℹ️  No tests to run based on changes detected."
    return 0
  fi
  
  echo "⚡ Executing Tests..."
  echo "==================="
  
  local overall_success=true
  local start_time=$(date +%s)
  
  # Execute tests sequentially (CI will handle parallelization)
  for cmd in "${test_commands[@]}"; do
    echo ""
    echo "Running: $cmd"
    if ! bash -c "$cmd"; then
      overall_success=false
      echo "❌ Test command failed: $cmd"
    else
      echo "✅ Test command succeeded: $cmd"
    fi
  done
  
  local end_time=$(date +%s)
  local duration=$((end_time - start_time))
  
  echo ""
  echo "📊 Execution Summary"
  echo "==================="
  echo "Total execution time: ${duration}s"
  echo "Commands executed: ${#test_commands[@]}"
  
  if [[ "$overall_success" == true ]]; then
    echo "🎉 All tests passed!"
    return 0
  else
    echo "❌ Some tests failed!"
    return 1
  fi
}

# Main execution
echo "🔍 Analyzing code changes..."
echo "Base: $BASE_REF, Head: $HEAD_REF"
echo ""

# Get changed files
changed_files=$(get_changed_files)

if [[ "${CI_MODE}" == false ]]; then
  echo "Changed files:"
  if [[ "$changed_files" == "all" ]]; then
    echo "  (Unable to determine or running all tests)"
  else
    echo "$changed_files" | sed 's/^/  /'
  fi
  echo ""
fi

# Analyze changes
analysis=$(analyze_changes "$changed_files")

# Parse analysis results
run_database_free=$(echo "$analysis" | grep "^database_free:" | cut -d: -f2)
run_sqlite=$(echo "$analysis" | grep "^sqlite:" | cut -d: -f2)
run_postgresql=$(echo "$analysis" | grep "^postgresql:" | cut -d: -f2)
reason=$(echo "$analysis" | grep "^reason:" | cut -d: -f2-)

# Execute tests
execute_tests "$run_database_free" "$run_sqlite" "$run_postgresql" "$reason"
