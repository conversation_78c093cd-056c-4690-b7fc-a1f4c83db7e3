#!/bin/bash
#
# A2A Platform - Storybook Development Script
#
# This script provides convenient commands for working with Storybook in the A2A Platform.
# It supports both local development and Docker-based development workflows.
#
# Usage:
#   ./scripts/storybook.sh [COMMAND] [OPTIONS]
#
# Commands:
#   dev (default)    Start Storybook in development mode
#   build            Build Storybook for production
#   serve            Serve built Storybook
#   docker           Start Storybook in Docker
#   test             Run Storybook tests
#   chromatic        Run Chromatic visual tests (requires CHROMATIC_PROJECT_TOKEN)
#
# Options:
#   --port PORT     Specify port for Storybook (default: 6006)
#   --host HOST     Specify host for Storybook (default: localhost)
#   --open          Open browser automatically
#   --ci            Run in CI mode (no interactive features)
#   --help          Display this help message
#
# Examples:
#   ./scripts/storybook.sh                    # Start Storybook locally
#   ./scripts/storybook.sh dev --open        # Start Storybook and open browser
#   ./scripts/storybook.sh docker            # Start Storybook in Docker
#   ./scripts/storybook.sh build             # Build Storybook for production
#   ./scripts/storybook.sh test              # Run Storybook tests
#   ./scripts/storybook.sh chromatic         # Run visual regression tests

set -e

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
WEB_DIR="${PROJECT_ROOT}/apps/web"

# Default values
COMMAND="dev"
PORT="6006"
HOST="localhost"
OPEN_BROWSER=false
CI_MODE=false

# Function to display help message
show_help() {
  grep "^#" "$0" | grep -v "!/bin/bash" | sed 's/^# \?//'
  exit 0
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    dev|build|serve|docker|test|chromatic)
      COMMAND="$1"
      shift
      ;;
    --port)
      PORT="$2"
      shift 2
      ;;
    --host)
      HOST="$2"
      shift 2
      ;;
    --open)
      OPEN_BROWSER=true
      shift
      ;;
    --ci)
      CI_MODE=true
      shift
      ;;
    --help)
      show_help
      ;;
    *)
      echo "Unknown option: $1"
      show_help
      ;;
  esac
done

# Check if we're in the correct directory
if [ ! -f "${WEB_DIR}/package.json" ]; then
  echo "Error: Could not find package.json in ${WEB_DIR}"
  echo "Please run this script from the project root directory"
  exit 1
fi

# Check if bun is available
if ! command -v bun &> /dev/null; then
  echo "Error: bun is not installed or not in PATH"
  echo "Please install bun: https://bun.sh/"
  exit 1
fi

# Build command options
STORYBOOK_OPTS=""
if [ "$OPEN_BROWSER" = true ] && [ "$COMMAND" = "dev" ]; then
  STORYBOOK_OPTS="--open"
fi

if [ "$CI_MODE" = true ]; then
  STORYBOOK_OPTS="$STORYBOOK_OPTS --ci"
fi

# Execute the appropriate command
case "$COMMAND" in
  dev)
    echo "Starting Storybook in development mode..."
    echo "Port: $PORT"
    echo "Host: $HOST"
    cd "$WEB_DIR"
    bun run storybook --port "$PORT" --host "$HOST" $STORYBOOK_OPTS
    ;;

  build)
    echo "Building Storybook for production..."
    cd "$WEB_DIR"
    bun run build-storybook
    echo "Storybook built successfully in ${WEB_DIR}/storybook-static"
    ;;

  serve)
    echo "Serving built Storybook..."
    if [ ! -d "${WEB_DIR}/storybook-static" ]; then
      echo "Error: Storybook build not found. Run 'build' command first."
      exit 1
    fi
    cd "$WEB_DIR"
    npx serve storybook-static -l "$PORT"
    ;;

  docker)
    echo "Starting Storybook in Docker..."
    cd "$PROJECT_ROOT"
    docker compose -f docker-compose.dev.yml up --build storybook
    ;;

  test)
    echo "Running Storybook tests..."
    cd "$WEB_DIR"
    # Run test runner if available
    if bun run --silent test-storybook --help &> /dev/null; then
      bun run test-storybook $STORYBOOK_OPTS
    else
      echo "Storybook test runner not configured. Install @storybook/test-runner to enable testing."
      echo "For now, running component tests that include stories..."
      bun run test -- --testPathPattern="stories|story"
    fi
    ;;

  chromatic)
    echo "Running Chromatic visual tests..."
    if [ -z "$CHROMATIC_PROJECT_TOKEN" ]; then
      echo "Error: CHROMATIC_PROJECT_TOKEN environment variable is required"
      echo "Get your token from https://www.chromatic.com/"
      exit 1
    fi
    cd "$WEB_DIR"
    if ! command -v chromatic &> /dev/null; then
      echo "Installing Chromatic..."
      bun add --dev chromatic
    fi
    npx chromatic --project-token="$CHROMATIC_PROJECT_TOKEN" $STORYBOOK_OPTS
    ;;

  *)
    echo "Unknown command: $COMMAND"
    show_help
    ;;
esac
