#!/bin/zsh
set -e

# Script to grant the run.invoker role to a service account for a Cloud Run service.

# --- Configuration ---
# GCP Project ID where the Cloud Run service is located
PROJECT_ID="$GCP_PROJECT_ID"

# Region of the Cloud Run service
REGION="us-central1"

# The service account to grant permissions to (the "member")
# Example: serviceAccount:<EMAIL>
MEMBER="serviceAccount:$GCP_SERVICE_ACCOUNT"

# The role to grant
ROLE="roles/run.invoker"

# The name of the Cloud Run service
SERVICE_NAME="api-staging"
# --- End Configuration ---

echo "Granting '$ROLE' to '$MEMBER' for Cloud Run service '$SERVICE_NAME' in region '$REGION'..."

gcloud beta run services add-iam-policy-binding \
  "$SERVICE_NAME" \
  --project="$PROJECT_ID" \
  --region="$REGION" \
  --member="$MEMBER" \
  --role="$ROLE"

if [ $? -eq 0 ]; then
  echo -e "\nSuccessfully updated IAM policy for service '$SERVICE_NAME'."
else
  echo -e "\nFailed to update IAM policy for service '$SERVICE_NAME'."
fi
