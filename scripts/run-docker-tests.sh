#!/bin/bash
#
# A2A Platform - Backend Tests Runner (Docker)
#
# This script runs backend tests in Docker using environment variables.
# Docker is the default testing environment for the project.
#
# Usage:
#   ./scripts/run-docker-tests.sh [OPTIONS] [TEST_PATHS...]
#
# Options:
#   --unit          Run only unit tests
#   --integration   Run only integration tests
#   --e2e           Run only end-to-end tests
#   --no-db         Run only database-free tests (marked with @pytest.mark.no_db)
#   --fast-db       Run only SQLite-based tests (marked with @pytest.mark.fast_db)
#   --exclude-no-db Exclude database-free tests (for PostgreSQL-only runs)
#   --exclude-fast-db Exclude SQLite tests (for PostgreSQL-only runs)
#   --performance   Include performance tests (excluded by default)
#   --coverage      Run tests with coverage report
#   --verbose       Run tests with verbose output
#   --setup         Perform dependency installation and database setup before running tests
#   --help          Display this help message
#
# Test Paths:
#   Optional paths to specific test files or test functions to run.
#   Paths should be relative to the project root.
#   Both file paths and method-specific paths are supported.
#
# Examples:
#   ./scripts/run-docker-tests.sh                # Run all tests in Docker
#   ./scripts/run-docker-tests.sh --unit         # Run only unit tests in Docker
#   ./scripts/run-docker-tests.sh --coverage     # Run all tests with coverage in Docker
#   ./scripts/run-docker-tests.sh tests/integration/test_file.py  # Run specific test file
#   ./scripts/run-docker-tests.sh tests/unit/test_file.py::test_function  # Run specific test function
#   ./scripts/run-docker-tests.sh tests/unit/test_file1.py tests/integration/test_file2.py  # Run multiple test files

set -e

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
BACKEND_DIR="${PROJECT_ROOT}/apps/backend"
ENV_TEST_FILE="${PROJECT_ROOT}/.env.test"

# Define the database service name from docker-compose.test.yml
DB_SERVICE_NAME="db" # Assuming 'db' is the service name for PostgreSQL

# Default values
RUN_UNIT=false
RUN_INTEGRATION=false
RUN_E2E=false
RUN_NO_DB=false
RUN_FAST_DB=false
EXCLUDE_NO_DB=false
EXCLUDE_FAST_DB=false
RUN_PERFORMANCE=false
RUN_COVERAGE=false
VERBOSE=false
RUN_SETUP=false
RUN_ALL=true
TEST_PATHS=()

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    --unit)
      RUN_UNIT=true
      RUN_ALL=false
      shift
      ;;
    --integration)
      RUN_INTEGRATION=true
      RUN_ALL=false
      shift
      ;;
    --e2e)
      RUN_E2E=true
      RUN_ALL=false
      shift
      ;;
    --no-db)
      RUN_NO_DB=true
      RUN_ALL=false
      shift
      ;;
    --fast-db)
      RUN_FAST_DB=true
      RUN_ALL=false
      shift
      ;;
    --exclude-no-db)
      EXCLUDE_NO_DB=true
      shift
      ;;
    --exclude-fast-db)
      EXCLUDE_FAST_DB=true
      shift
      ;;
    --performance)
      RUN_PERFORMANCE=true
      shift
      ;;
    --coverage)
      RUN_COVERAGE=true
      shift
      ;;
    --verbose)
      VERBOSE=true
      shift
      ;;
    --setup)
      RUN_SETUP=true
      shift
      ;;
    --help)
      grep '^#' "$0" | grep -v '#!/bin/bash' | sed 's/^# \?//'
      exit 0
      ;;
    *)
      # If argument doesn't start with --, assume it's a test path
      if [[ "$1" != --* ]]; then
        # Store the test path
        TEST_PATHS+=("$1")
        shift
      else
        echo "Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
      fi
      ;;
  esac
done

# Check if .env.test exists (used for loading environment variables)
if [[ ! -f "${ENV_TEST_FILE}" ]]; then
  echo "Warning: .env.test file not found at ${ENV_TEST_FILE}"
  echo "Will use environment variables from docker-compose.test.yml only"
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
  echo "Error: Docker is not running. Please start Docker and try again."
  exit 1
fi

# Enhanced database initialization using Python script
echo "🏗️  Initializing test database with enhanced setup..."

# Ensure database container is running
echo "Ensuring database container is running..."
docker compose -f "${PROJECT_ROOT}/docker-compose.test.yml" up -d "${DB_SERVICE_NAME}" --remove-orphans

# Wait for database to be ready
echo "Waiting for database to be ready..."
for i in {1..30}; do
  if docker compose -f "${PROJECT_ROOT}/docker-compose.test.yml" exec -T "${DB_SERVICE_NAME}" pg_isready -U postgres > /dev/null 2>&1; then
    echo "✅ Database is ready"
    break
  fi
  echo -n "."
  sleep 1
done

if [[ $i -eq 30 ]]; then
  echo "❌ Database did not become ready in time"
  exit 1
fi

# Determine initialization flags
INIT_FLAGS=""
if [[ "${RUN_SETUP}" == true ]]; then
  INIT_FLAGS="${INIT_FLAGS} --force"
fi

# Load environment variables for database connection
if [[ -f "${ENV_TEST_FILE}" ]]; then
  set -o allexport
  source "${ENV_TEST_FILE}"
  set +o allexport
fi

# Run enhanced database initialization
echo "🚀 Running enhanced database initialization..."
docker compose -f "${PROJECT_ROOT}/docker-compose.test.yml" run --rm \
  -e DATABASE_URL="postgresql+asyncpg://postgres:postgres@${DB_SERVICE_NAME}:5432/a2a_platform_test" \
  backend python /app/scripts/init_test_db.py ${INIT_FLAGS}

# Check if .env.test exists for reference
if [[ ! -f "${ENV_TEST_FILE}" ]]; then
  echo "Warning: .env.test file not found at ${ENV_TEST_FILE}"
  echo "Will use environment variables from docker-compose.test.yml only"
fi

# Build the test command for Docker
# We don't need to modify the conftest.py file anymore
# The Docker container will use the environment variables from docker-compose.test.yml
TEST_CMD="cd /app && python -m pytest"

# Add verbosity if requested
if [[ "${VERBOSE}" == true ]]; then
  TEST_CMD="${TEST_CMD} -v"
fi

# Add coverage if requested
if [[ "${RUN_COVERAGE}" == true ]]; then
  TEST_CMD="${TEST_CMD} --cov=src/a2a_platform --cov-report=term --cov-report=html"
fi

# Check if user has provided custom markers (either in args or in any test paths)
USER_PROVIDED_MARKERS=false
for arg in "$@"; do
  if [[ "$arg" == "-m" ]]; then
    USER_PROVIDED_MARKERS=true
    break
  fi
done

# Also check if any of the test paths contain marker syntax
if [[ "${USER_PROVIDED_MARKERS}" == false ]]; then
  for test_path in "${TEST_PATHS[@]}"; do
    if [[ "$test_path" == "-m" ]]; then
      USER_PROVIDED_MARKERS=true
      break
    fi
  done
fi

# Add pytest marker filtering based on database optimization flags
if [[ "${RUN_NO_DB}" == true ]]; then
  # Handle performance exclusion for no_db tests
  if [[ "${RUN_PERFORMANCE}" == true ]]; then
    TEST_CMD="${TEST_CMD} -m 'no_db'"
    echo "Running only database-free tests (including performance)..."
  else
    TEST_CMD="${TEST_CMD} -m 'no_db and not performance'"
    echo "Running only database-free tests (excluding performance)..."
  fi
elif [[ "${RUN_FAST_DB}" == true ]]; then
  # Handle performance exclusion for fast_db tests
  if [[ "${RUN_PERFORMANCE}" == true ]]; then
    TEST_CMD="${TEST_CMD} -m 'fast_db'"
    echo "Running only SQLite tests (including performance)..."
  else
    TEST_CMD="${TEST_CMD} -m 'fast_db and not performance'"
    echo "Running only SQLite tests (excluding performance)..."
  fi
else
  # Handle exclusion flags for normal runs - build marker expression
  EXCLUSIONS=()

  if [[ "${EXCLUDE_NO_DB}" == true ]]; then
    EXCLUSIONS+=("not no_db")
    echo "Excluding database-free tests..."
  fi

  if [[ "${EXCLUDE_FAST_DB}" == true ]]; then
    EXCLUSIONS+=("not fast_db")
    echo "Excluding SQLite tests..."
  fi

  # By default, exclude performance tests unless --performance flag is used or user provided markers
  if [[ "${RUN_PERFORMANCE}" != true ]] && [[ "${USER_PROVIDED_MARKERS}" != true ]]; then
    EXCLUSIONS+=("not performance")
    echo "Excluding performance tests (use --performance to include)..."
  elif [[ "${RUN_PERFORMANCE}" == true ]] && [[ "${USER_PROVIDED_MARKERS}" != true ]]; then
    echo "Including performance tests..."
  elif [[ "${USER_PROVIDED_MARKERS}" == true ]]; then
    echo "Using custom test markers provided by user..."
  fi

  # Combine exclusions with 'and' only if user hasn't provided markers
  if [[ ${#EXCLUSIONS[@]} -gt 0 ]] && [[ "${USER_PROVIDED_MARKERS}" != true ]]; then
    MARKER_EXPR=$(IFS=" and "; echo "${EXCLUSIONS[*]}")
    TEST_CMD="${TEST_CMD} -m '${MARKER_EXPR}'"
  fi
fi

# Determine which tests to run
if [[ ${#TEST_PATHS[@]} -gt 0 ]]; then
  echo "Running specific tests..."
  for test_path in "${TEST_PATHS[@]}"; do
    # Convert path from project-root-relative to backend-relative
    # Check if the path starts with apps/backend/
    if [[ "${test_path}" == apps/backend/* ]]; then
      # Remove apps/backend/ prefix
      backend_relative_path="${test_path#apps/backend/}"
    else
      # Assume it's already relative to backend directory
      backend_relative_path="${test_path}"
    fi
    TEST_CMD="${TEST_CMD} ${backend_relative_path}"
  done
  # Apply performance exclusion for specific test paths only if user hasn't provided marker
  if [[ "${RUN_PERFORMANCE}" != true ]] && [[ "${USER_PROVIDED_MARKERS}" != true ]]; then
    TEST_CMD="${TEST_CMD} -m 'not performance'"
    echo "Excluding performance tests from specified paths..."
  elif [[ "${RUN_PERFORMANCE}" == true ]] && [[ "${USER_PROVIDED_MARKERS}" != true ]]; then
    echo "Including performance tests in specified paths..."
  elif [[ "${USER_PROVIDED_MARKERS}" == true ]]; then
    echo "Using custom markers for specified test paths..."
  fi
elif [[ "${RUN_ALL}" == true ]]; then
  echo "Running all tests..."
  TEST_CMD="${TEST_CMD} tests/"
  # Apply performance exclusion for all tests only if user hasn't provided marker
  if [[ "${RUN_PERFORMANCE}" != true ]] && [[ "${USER_PROVIDED_MARKERS}" != true ]]; then
    TEST_CMD="${TEST_CMD} -m 'not performance'"
    echo "Excluding performance tests (use --performance to include)..."
  elif [[ "${RUN_PERFORMANCE}" == true ]] && [[ "${USER_PROVIDED_MARKERS}" != true ]]; then
    echo "Including performance tests..."
  elif [[ "${USER_PROVIDED_MARKERS}" == true ]]; then
    echo "Using custom markers for all tests..."
  fi
elif [[ "${RUN_UNIT}" == true ]]; then
  TEST_CMD="${TEST_CMD} tests/unit/"
  echo "Running unit tests..."
  # Apply performance exclusion for unit tests only if user hasn't provided marker
  if [[ "${RUN_PERFORMANCE}" != true ]] && [[ "${USER_PROVIDED_MARKERS}" != true ]]; then
    TEST_CMD="${TEST_CMD} -m 'not performance'"
    echo "Excluding performance tests from unit tests..."
  elif [[ "${RUN_PERFORMANCE}" == true ]] && [[ "${USER_PROVIDED_MARKERS}" != true ]]; then
    echo "Including performance tests in unit tests..."
  elif [[ "${USER_PROVIDED_MARKERS}" == true ]]; then
    echo "Using custom markers for unit tests..."
  fi
elif [[ "${RUN_INTEGRATION}" == true ]]; then
  TEST_CMD="${TEST_CMD} tests/integration/"
  echo "Running integration tests..."
  # Apply performance exclusion for integration tests only if user hasn't provided marker
  if [[ "${RUN_PERFORMANCE}" != true ]] && [[ "${USER_PROVIDED_MARKERS}" != true ]]; then
    TEST_CMD="${TEST_CMD} -m 'not performance'"
    echo "Excluding performance tests from integration tests..."
  elif [[ "${RUN_PERFORMANCE}" == true ]] && [[ "${USER_PROVIDED_MARKERS}" != true ]]; then
    echo "Including performance tests in integration tests..."
  elif [[ "${USER_PROVIDED_MARKERS}" == true ]]; then
    echo "Using custom markers for integration tests..."
  fi
elif [[ "${RUN_E2E}" == true ]]; then
  TEST_CMD="${TEST_CMD} tests/e2e/"
  echo "Running end-to-end tests..."
  # Apply performance exclusion for e2e tests only if user hasn't provided marker
  if [[ "${RUN_PERFORMANCE}" != true ]] && [[ "${USER_PROVIDED_MARKERS}" != true ]]; then
    TEST_CMD="${TEST_CMD} -m 'not performance'"
    echo "Excluding performance tests from e2e tests..."
  elif [[ "${RUN_PERFORMANCE}" == true ]] && [[ "${USER_PROVIDED_MARKERS}" != true ]]; then
    echo "Including performance tests in e2e tests..."
  elif [[ "${USER_PROVIDED_MARKERS}" == true ]]; then
    echo "Using custom markers for e2e tests..."
  fi
fi

# Run the tests in Docker with env variables
echo "🧪 Running tests in Docker..."
# Make sure environment variables are exported if .env.test exists
if [[ -f "${ENV_TEST_FILE}" ]]; then
  # Load variables silently
  set -o allexport
  source "${ENV_TEST_FILE}"
  set +o allexport
  echo "Loaded environment variables from .env.test"
fi

# Database and migrations are already handled by init_test_db.py
# Now run the tests

docker compose -f docker-compose.test.yml run --rm \
  -e DOCKER_ENV=true -T \
  backend sh -c "${TEST_CMD}"

echo "Docker tests completed!"
exit 0
