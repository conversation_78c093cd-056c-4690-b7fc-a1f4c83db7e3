#!/bin/bash
# setup-dev-env.sh - Setup development environment for the A2A Platform
#
# This script installs all necessary tools for development and pre-commit hooks:
# - Python and pip
# - pre-commit
# - Terraform
# - terraform-docs
# - trivy
#
# Usage: ./scripts/setup-dev-env.sh [--ci] [--skip-db]
#   --ci: Install for CI environment (non-interactive)
#   --skip-db: Skip setting up the test database

set -e

# Colors for prettier output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

CI_MODE=false
SKIP_DB=false

# Parse command line arguments
while [[ "$#" -gt 0 ]]; do
  case $1 in
    --ci) CI_MODE=true ;;
    --skip-db) SKIP_DB=true ;;
    *) echo "Unknown parameter: $1"; exit 1 ;;
  esac
  shift
done

echo -e "${GREEN}Setting up development environment for A2A Platform...${NC}"

# Detect OS
if [[ "$OSTYPE" == "darwin"* ]]; then
  OS="macOS"
  PACKAGE_MANAGER="brew"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
  OS="Linux"
  if command -v apt-get >/dev/null 2>&1; then
    PACKAGE_MANAGER="apt"
  elif command -v yum >/dev/null 2>&1; then
    PACKAGE_MANAGER="yum"
  else
    echo -e "${RED}Unsupported Linux distribution. Please install dependencies manually.${NC}"
    exit 1
  fi
elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
  OS="Windows"
  echo -e "${YELLOW}Windows detected. Some tools may need to be installed manually.${NC}"
else
  echo -e "${RED}Unsupported operating system: $OSTYPE${NC}"
  exit 1
fi

echo -e "Detected ${GREEN}$OS${NC} with package manager ${GREEN}$PACKAGE_MANAGER${NC}\n"

# Function to check if a command is available
command_exists() {
  command -v "$1" >/dev/null 2>&1
}

# Function to install packages using the appropriate package manager
install_package() {
  package=$1
  if [[ "$PACKAGE_MANAGER" == "brew" ]]; then
    echo "Installing $package with Homebrew..."
    brew install "$package"
  elif [[ "$PACKAGE_MANAGER" == "apt" ]]; then
    echo "Installing $package with apt..."
    sudo apt-get install -y "$package"
  elif [[ "$PACKAGE_MANAGER" == "yum" ]]; then
    echo "Installing $package with yum..."
    sudo yum install -y "$package"
  fi
}

# Install Homebrew on macOS if needed
if [[ "$OS" == "macOS" && ! $(command_exists brew) ]]; then
  echo "Installing Homebrew..."
  /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
fi

# Check and install Python
if command_exists python3; then
  echo -e "${GREEN}Python is already installed!${NC}"
  python3 --version
else
  echo "Installing Python..."
  install_package "python3"
fi

# Check and install pip
if command_exists pip3; then
  echo -e "${GREEN}pip is already installed!${NC}"
  pip3 --version
else
  echo "Installing pip..."
  if [[ "$PACKAGE_MANAGER" == "brew" ]]; then
    # Pip comes with Python on macOS
    echo "pip should be installed with Python. If not, run: brew install python"
  elif [[ "$PACKAGE_MANAGER" == "apt" ]]; then
    sudo apt-get install -y python3-pip
  elif [[ "$PACKAGE_MANAGER" == "yum" ]]; then
    sudo yum install -y python3-pip
  fi
fi

# Check and install Node.js and npm
if command_exists npm; then
  echo -e "${GREEN}Node.js and npm are already installed!${NC}"
  node --version
  npm --version
else
  echo "Installing Node.js and npm..."
  if [[ "$PACKAGE_MANAGER" == "brew" ]]; then
    brew install node
  elif [[ "$PACKAGE_MANAGER" == "apt" ]]; then
    echo "Setting up NodeSource repository for Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_current.x | sudo -E bash -
    sudo apt-get install -y nodejs
  elif [[ "$PACKAGE_MANAGER" == "yum" ]]; then
    echo "Setting up NodeSource repository for Node.js..."
    curl -fsSL https://rpm.nodesource.com/setup_current.x | sudo -E bash -
    sudo yum install -y nodejs
  fi
  echo -e "${GREEN}Node.js and npm installed successfully.${NC}"
  node --version
  npm --version
fi

# Check and install pre-commit
if command_exists pre-commit; then
  echo -e "${GREEN}pre-commit is already installed!${NC}"
  pre-commit --version
else
  echo "Installing pre-commit..."
  pip3 install pre-commit
fi

# Check and install Terraform
if command_exists terraform; then
  echo -e "${GREEN}Terraform is already installed!${NC}"
  terraform version
else
  echo "Installing Terraform..."
  if [[ "$PACKAGE_MANAGER" == "brew" ]]; then
    brew install terraform
  elif [[ "$PACKAGE_MANAGER" == "apt" ]]; then
    wget -O- https://apt.releases.hashicorp.com/gpg | sudo gpg --dearmor -o /usr/share/keyrings/hashicorp-archive-keyring.gpg
    echo "deb [signed-by=/usr/share/keyrings/hashicorp-archive-keyring.gpg] https://apt.releases.hashicorp.com $(lsb_release -cs) main" | sudo tee /etc/apt/sources.list.d/hashicorp.list
    sudo apt-get update
    sudo apt-get install -y terraform
  elif [[ "$PACKAGE_MANAGER" == "yum" ]]; then
    sudo yum install -y yum-utils
    sudo yum-config-manager --add-repo https://rpm.releases.hashicorp.com/RHEL/hashicorp.repo
    sudo yum install -y terraform
  fi
fi

# Check and install terraform-docs
if command_exists terraform-docs; then
  echo -e "${GREEN}terraform-docs is already installed!${NC}"
  terraform-docs --version
else
  echo "Installing terraform-docs..."
  if [[ "$PACKAGE_MANAGER" == "brew" ]]; then
    brew install terraform-docs
  else
    echo -e "${YELLOW}Installing terraform-docs...${NC}"
    curl -sSLo ./terraform-docs.tar.gz https://terraform-docs.io/dl/v0.16.0/terraform-docs-v0.16.0-$(uname)-amd64.tar.gz
    tar -xzf terraform-docs.tar.gz
    chmod +x terraform-docs
    sudo mv terraform-docs /usr/local/bin/
    rm terraform-docs.tar.gz
  fi
fi

# Check and install trivy
if command_exists trivy; then
  echo -e "${GREEN}trivy is already installed!${NC}"
  trivy --version
else
  echo "Installing trivy..."
  if [[ "$PACKAGE_MANAGER" == "brew" ]]; then
    brew install trivy
  elif [[ "$PACKAGE_MANAGER" == "apt" ]]; then
    wget -qO - https://aquasecurity.github.io/trivy-repo/deb/public.key | sudo apt-key add -
    echo deb https://aquasecurity.github.io/trivy-repo/deb $(lsb_release -sc) main | sudo tee -a /etc/apt/sources.list.d/trivy.list
    sudo apt-get update
    sudo apt-get install -y trivy
  elif [[ "$PACKAGE_MANAGER" == "yum" ]]; then
    cat << EOF | sudo tee -a /etc/yum.repos.d/trivy.repo
[trivy]
name=Trivy repository
baseurl=https://aquasecurity.github.io/trivy-repo/rpm/\$basearch/
gpgcheck=0
enabled=1
EOF
    sudo yum install -y trivy
  fi
fi

# Check and install Bun
if command_exists bun; then
  echo -e "${GREEN}Bun is already installed!${NC}"
  bun --version
else
  echo "Installing Bun..."
  curl -fsSL https://bun.sh/install | bash
  # Add bun to PATH for the current session if it's not already there
  # This depends on where bun installs itself, typically ~/.bun/bin
  export PATH="$HOME/.bun/bin:$PATH"
  echo -e "${GREEN}Bun installed successfully.${NC}"
  # Source .bashrc or .zshrc or recommend restarting terminal if PATH issues persist
  if [[ -f "$HOME/.zshrc" ]] && [[ "$SHELL" == *"zsh"* ]]; then
    echo "Please run 'source $HOME/.zshrc' or restart your terminal for Bun to be available in PATH."
  elif [[ -f "$HOME/.bashrc" ]] && [[ "$SHELL" == *"bash"* ]]; then
    echo "Please run 'source $HOME/.bashrc' or restart your terminal for Bun to be available in PATH."
  else
    echo "Please ensure $HOME/.bun/bin is in your PATH or restart your terminal."
  fi
  bun --version
fi

# Install web application dependencies
echo -e "\n${GREEN}Setting up web application dependencies...${NC}"
if [[ -f "./apps/web/package.json" ]]; then
  echo "Found package.json in apps/web, running bun install..."
  if command_exists bun; then
    bun install --cwd ./apps/web
    echo -e "${GREEN}Web application dependencies installed using Bun.${NC}"
  else
    echo -e "${RED}bun command not found. Skipping web dependency installation.${NC}"
    echo -e "${YELLOW}Please ensure Bun is installed correctly.${NC}"
  fi
else
  echo -e "${YELLOW}./apps/web/package.json not found. Skipping web dependency installation.${NC}"
fi

# Set up Git hooks
echo -e "\n${GREEN}Setting up pre-commit hooks...${NC}"
pre-commit install

# Setup test database
if [[ "$SKIP_DB" == false ]]; then
  echo -e "\n${GREEN}Setting up test database using Docker...${NC}"
  if command_exists docker; then
    echo -e "Docker is installed, proceeding with database setup..."
    # Check if Docker is running
    if docker info > /dev/null 2>&1; then
      # Run the database setup script
      if [[ -f "./scripts/setup-test-db.sh" ]]; then
        echo -e "Running database setup script..."
        ./scripts/setup-test-db.sh --docker
      else
        echo -e "${RED}Database setup script not found at ./scripts/setup-test-db.sh${NC}"
        echo -e "${YELLOW}You may need to run it manually later.${NC}"
      fi
    else
      echo -e "${YELLOW}Docker is not running. Please start Docker and then run:${NC}"
      echo -e "${YELLOW}  ./scripts/setup-test-db.sh --docker${NC}"
    fi
  else
    echo -e "${YELLOW}Docker is not installed. The test database requires Docker.${NC}"
    echo -e "${YELLOW}Please install Docker and then run:${NC}"
    echo -e "${YELLOW}  ./scripts/setup-test-db.sh --docker${NC}"
  fi
fi

echo -e "\n${GREEN}Development environment setup complete!${NC}"
echo -e "The following tools are now installed and configured:${NC}"
echo -e "  ${YELLOW}• Python and pip${NC}"
echo -e "  ${YELLOW}• Node.js and npm${NC}"
echo -e "  ${YELLOW}• Bun${NC}"
echo -e "  ${YELLOW}• pre-commit${NC}"
echo -e "  ${YELLOW}• Terraform${NC}"
echo -e "  ${YELLOW}• terraform-docs${NC}"
echo -e "  ${YELLOW}• trivy${NC}"

if [[ "$SKIP_DB" == false && -f "./scripts/setup-test-db.sh" ]]; then
  echo -e "  ${YELLOW}• Test database (Docker)${NC}"
fi

echo -e "\n${GREEN}You can now run pre-commit checks with:${NC}"
echo -e "  ${YELLOW}pre-commit run --all-files${NC}"