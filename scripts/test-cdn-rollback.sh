#!/bin/bash
# CDN Rollback Procedure Testing Script
# Tests rollback procedures for CDN migration via GitHub Actions
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT=${1:-staging}
DRY_RUN=${2:-true}
REPORT_FILE=${3:-"rollback-test-report.json"}

echo -e "${BLUE}🔄 CDN Rollback Procedure Testing for environment: $ENVIRONMENT${NC}"
echo "=================================================================="

if [[ "$DRY_RUN" == "true" || "$DRY_RUN" == "--dry-run" ]]; then
    echo -e "${YELLOW}⚠️ Running in DRY RUN mode - no actual changes will be made${NC}"
    DRY_RUN=true
else
    echo -e "${RED}⚠️ LIVE MODE - actual rollback changes will be made!${NC}"
    read -p "Are you sure you want to proceed? (yes/no): " confirm
    if [[ "$confirm" != "yes" ]]; then
        echo "Rollback test cancelled."
        exit 0
    fi
    DRY_RUN=false
fi

# Set environment-specific configurations
if [[ "$ENVIRONMENT" == "production" ]]; then
    WEB_URL="https://vedavivi.app"
    API_URL="https://api.vedavivi.app"
    BACKUP_URL="https://vedavivi-production-backup.run.app"
    TERRAFORM_DIR="terraform/environments/production"
else
    WEB_URL="https://staging.vedavivi.app"
    API_URL="https://api-staging.vedavivi.app"
    BACKUP_URL="https://vedavivi-staging-backup.run.app"
    TERRAFORM_DIR="terraform/environments/staging"
fi

# Test counters
PASSED=0
FAILED=0

# Function to run rollback tests
test_rollback_step() {
    local step_name="$1"
    local test_command="$2"
    local expected="$3"
    
    echo -e "\n${BLUE}🧪 Testing: $step_name${NC}"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        echo -e "${YELLOW}[DRY RUN] Would execute: $test_command${NC}"
        echo -e "${GREEN}✅ PASSED (DRY RUN): $step_name${NC}"
        ((PASSED++))
        return 0
    else
        if bash -c "$test_command"; then
            echo -e "${GREEN}✅ PASSED: $step_name${NC}"
            ((PASSED++))
            return 0
        else
            echo -e "${RED}❌ FAILED: $step_name${NC}"
            echo -e "${RED}   Expected: $expected${NC}"
            ((FAILED++))
            return 1
        fi
    fi
}

# Function to check service availability
check_service_availability() {
    local url="$1"
    local service_name="$2"
    
    echo "  Checking availability of $service_name at $url..."
    
    local response=$(curl -s -o /dev/null -w "%{http_code},%{time_total}" "$url" 2>/dev/null)
    local status_code=$(echo "$response" | cut -d, -f1)
    local response_time=$(echo "$response" | cut -d, -f2)
    
    echo "  Status: $status_code, Response time: ${response_time}s"
    
    if [[ "$status_code" == "200" ]]; then
        return 0
    else
        return 1
    fi
}

# Function to backup current state
backup_current_state() {
    echo -e "\n${BLUE}💾 Backing up current state${NC}"
    
    # Create backup directory
    local backup_dir="rollback-backup-$(date +%Y%m%d-%H%M%S)"
    
    if [[ "$DRY_RUN" == "false" ]]; then
        mkdir -p "$backup_dir"
        
        # Backup Terraform state
        if [[ -d "$TERRAFORM_DIR" ]]; then
            cp -r "$TERRAFORM_DIR" "$backup_dir/"
            echo "  Terraform state backed up to $backup_dir/"
        fi
        
        # Backup current DNS records (if dig is available)
        if command -v dig >/dev/null 2>&1; then
            dig +short "$(echo "$WEB_URL" | sed 's|https\?://||')" > "$backup_dir/current-dns.txt"
            echo "  DNS records backed up to $backup_dir/current-dns.txt"
        fi
        
        echo "  Backup completed: $backup_dir"
    else
        echo -e "${YELLOW}[DRY RUN] Would create backup directory: $backup_dir${NC}"
    fi
}

# Function to test Terraform rollback
test_terraform_rollback() {
    echo -e "\n${BLUE}🏗️ Testing Terraform Rollback${NC}"
    
    if [[ -d "$TERRAFORM_DIR" ]]; then
        # Test Terraform plan for rollback
        test_rollback_step "Terraform init for rollback" \
            "cd '$TERRAFORM_DIR' && terraform init" \
            "Terraform should initialize successfully"
        
        test_rollback_step "Terraform plan for rollback" \
            "cd '$TERRAFORM_DIR' && terraform plan -out=rollback.tfplan" \
            "Terraform plan should complete without errors"
        
        if [[ "$DRY_RUN" == "false" ]]; then
            test_rollback_step "Terraform apply rollback" \
                "cd '$TERRAFORM_DIR' && terraform apply -auto-approve rollback.tfplan" \
                "Terraform rollback should apply successfully"
        else
            echo -e "${YELLOW}[DRY RUN] Would apply Terraform rollback plan${NC}"
            ((PASSED++))
        fi
    else
        echo -e "${RED}❌ Terraform directory not found: $TERRAFORM_DIR${NC}"
        ((FAILED++))
    fi
}

# Function to test DNS rollback
test_dns_rollback() {
    echo -e "\n${BLUE}🌐 Testing DNS Rollback${NC}"
    
    # In a real rollback, this would involve updating DNS records
    # For testing, we simulate the process
    
    test_rollback_step "DNS record backup verification" \
        "echo 'DNS records would be backed up'" \
        "DNS records should be backed up before changes"
    
    test_rollback_step "DNS rollback simulation" \
        "echo 'DNS records would be updated to point to backup infrastructure'" \
        "DNS should point to backup/previous infrastructure"
    
    # Test DNS propagation check
    test_rollback_step "DNS propagation check" \
        "echo 'DNS propagation would be monitored'" \
        "DNS changes should propagate within 5 minutes"
}

# Function to test service continuity
test_service_continuity() {
    echo -e "\n${BLUE}🔄 Testing Service Continuity${NC}"
    
    # Test that services remain available during rollback
    test_rollback_step "Web service availability during rollback" \
        "check_service_availability '$WEB_URL' 'Web Application'" \
        "Web service should remain available"
    
    test_rollback_step "API service availability during rollback" \
        "check_service_availability '$API_URL/health' 'API Service'" \
        "API service should remain available"
    
    # Test backup service availability
    if [[ -n "$BACKUP_URL" ]]; then
        test_rollback_step "Backup service availability" \
            "check_service_availability '$BACKUP_URL' 'Backup Service'" \
            "Backup service should be available for rollback"
    fi
}

# Function to test rollback validation
test_rollback_validation() {
    echo -e "\n${BLUE}✅ Testing Rollback Validation${NC}"
    
    # Test that rollback can be validated
    test_rollback_step "Rollback validation script execution" \
        "./scripts/validate-cdn-migration.sh '$ENVIRONMENT' --validate-only" \
        "Rollback validation should pass"
    
    test_rollback_step "Performance baseline restoration" \
        "echo 'Performance metrics would be validated against baseline'" \
        "Performance should match pre-CDN baseline"
    
    test_rollback_step "Functional testing post-rollback" \
        "echo 'Functional tests would be executed'" \
        "All functionality should work after rollback"
}

# Function to test forward migration capability
test_forward_migration() {
    echo -e "\n${BLUE}⏭️ Testing Forward Migration Capability${NC}"
    
    # Test that we can migrate forward again after rollback
    test_rollback_step "Forward migration preparation" \
        "echo 'CDN infrastructure would be prepared for re-deployment'" \
        "CDN infrastructure should be ready for forward migration"
    
    test_rollback_step "Forward migration validation" \
        "echo 'Forward migration plan would be validated'" \
        "Forward migration should be possible after rollback"
}

# Function to test GitHub Actions integration
test_github_actions_integration() {
    echo -e "\n${BLUE}🔄 Testing GitHub Actions Integration${NC}"
    
    # Test GitHub Actions workflow for rollback
    test_rollback_step "GitHub Actions rollback workflow validation" \
        "echo 'Rollback workflow would be triggered via GitHub Actions'" \
        "GitHub Actions should support automated rollback"
    
    test_rollback_step "Rollback workflow permissions" \
        "echo 'Workflow permissions would be validated'" \
        "GitHub Actions should have necessary permissions for rollback"
    
    test_rollback_step "Rollback notification system" \
        "echo 'Rollback notifications would be sent'" \
        "Team should be notified of rollback completion"
}

# Main rollback testing procedure
echo -e "\n${YELLOW}🚀 Starting CDN Rollback Procedure Testing${NC}"

# 1. Backup current state
backup_current_state

# 2. Test Terraform rollback
test_terraform_rollback

# 3. Test DNS rollback
test_dns_rollback

# 4. Test service continuity
test_service_continuity

# 5. Test rollback validation
test_rollback_validation

# 6. Test forward migration capability
test_forward_migration

# 7. Test GitHub Actions integration
test_github_actions_integration

# 8. Generate rollback test report
echo -e "\n${BLUE}📊 Generating Rollback Test Report${NC}"

cat > "$REPORT_FILE" << EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "environment": "$ENVIRONMENT",
  "dry_run": $DRY_RUN,
  "test_results": {
    "tests_passed": $PASSED,
    "tests_failed": $FAILED,
    "total_tests": $((PASSED + FAILED))
  },
  "rollback_readiness": $(if [[ $FAILED -eq 0 ]]; then echo '"READY"'; else echo '"NOT_READY"'; fi),
  "urls_tested": {
    "web_url": "$WEB_URL",
    "api_url": "$API_URL",
    "backup_url": "$BACKUP_URL"
  },
  "terraform_directory": "$TERRAFORM_DIR"
}
EOF

echo "Rollback test report saved to: $REPORT_FILE"

# Summary
echo -e "\n${BLUE}=================================================================="
echo -e "📊 CDN Rollback Procedure Testing Summary${NC}"
echo -e "=================================================================="
echo -e "${GREEN}✅ Passed: $PASSED tests${NC}"
echo -e "${RED}❌ Failed: $FAILED tests${NC}"
echo -e "Total: $((PASSED + FAILED)) tests"

if [[ $FAILED -eq 0 ]]; then
    echo -e "\n${GREEN}🎉 All CDN rollback procedure tests passed!${NC}"
    echo -e "${GREEN}✅ Rollback procedures are ready and validated${NC}"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        echo -e "\n${BLUE}💡 To test actual rollback (USE WITH CAUTION):${NC}"
        echo -e "   $0 $ENVIRONMENT --live"
    fi
    
    exit 0
else
    echo -e "\n${RED}⚠️ Some CDN rollback procedure tests failed!${NC}"
    echo -e "${RED}❌ Please fix rollback issues before proceeding with CDN migration${NC}"
    exit 1
fi
