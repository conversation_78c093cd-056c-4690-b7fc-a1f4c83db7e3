#!/bin/bash
#
# A2A Platform - Parallel Test Runner with TUI
#
# Executes different test tiers in parallel for maximum performance.
# Features interactive Terminal User Interface (TUI) powered by Gum.
# Automatically determines optimal parallelization strategy based on test categories.
#
# Usage:
#   ./scripts/parallel-test-runner.sh [OPTIONS]
#
# Options:
#   --max-workers N     Maximum number of parallel workers (default: auto-detect)
#   --timeout N         Timeout for each tier in seconds (default: 300)
#   --fail-fast         Stop on first failure
#   --detailed          Show detailed output from each tier (default behavior)
#   --brief             Disable detailed output for minimal reporting
#   --dry-run           Show what would be executed without running tests
#   --interactive       Enable interactive TUI mode (requires explicit flag)
#   --no-interactive    Disable interactive TUI mode (default behavior)

set -e

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
MAX_WORKERS=""
TIMEOUT=300
FAIL_FAST=false
DETAILED=true  # Default to detailed output for better debugging experience
DRY_RUN=false
INTERACTIVE=false  # Default to non-interactive mode for CI/CD compatibility

# Colors for output (fallback when gum is not available)
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Check if gum is available
GUM_AVAILABLE=false
if command -v gum >/dev/null 2>&1; then
  GUM_AVAILABLE=true
fi

# TUI utility functions
tui_log() {
  local level="$1"
  local message="$2"
  shift 2

  if [[ "$GUM_AVAILABLE" == true ]]; then
    gum log --structured --level "$level" "$message" "$@"
  else
    case "$level" in
      "debug") echo -e "${BLUE}[DEBUG]${NC} $message" ;;
      "info") echo -e "${GREEN}[INFO]${NC} $message" ;;
      "warn") echo -e "${YELLOW}[WARN]${NC} $message" ;;
      "error") echo -e "${RED}[ERROR]${NC} $message" ;;
      *) echo "$message" ;;
    esac
  fi
}

tui_style() {
  local text="$1"
  shift

  if [[ "$GUM_AVAILABLE" == true ]]; then
    echo "$text" | gum style "$@"
  else
    echo "$text"
  fi
}

tui_confirm() {
  local prompt="$1"

  if [[ "$GUM_AVAILABLE" == true ]]; then
    gum confirm "$prompt"
  else
    echo -n "$prompt (y/N): "
    read -r response
    [[ "$response" =~ ^[Yy]$ ]]
  fi
}

tui_input() {
  local placeholder="$1"
  local default="$2"

  if [[ "$GUM_AVAILABLE" == true ]]; then
    if [[ -n "$default" ]]; then
      gum input --placeholder "$placeholder" --value "$default"
    else
      gum input --placeholder "$placeholder"
    fi
  else
    echo -n "$placeholder: "
    if [[ -n "$default" ]]; then
      echo -n "[$default] "
    fi
    read -r response
    echo "${response:-$default}"
  fi
}

tui_choose() {
  local prompt="$1"
  shift

  if [[ "$GUM_AVAILABLE" == true ]]; then
    gum choose --header "$prompt" "$@"
  else
    echo "$prompt"
    select option in "$@"; do
      if [[ -n "$option" ]]; then
        echo "$option"
        break
      fi
    done
  fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --max-workers)
      MAX_WORKERS="$2"
      shift 2
      ;;
    --timeout)
      TIMEOUT="$2"
      shift 2
      ;;
    --fail-fast)
      FAIL_FAST=true
      shift
      ;;
    --detailed)
      DETAILED=true
      shift
      ;;
    --brief)
      DETAILED=false
      shift
      ;;
    --dry-run)
      DRY_RUN=true
      shift
      ;;
    --interactive)
      INTERACTIVE=true
      shift
      ;;
    --no-interactive)
      INTERACTIVE=false
      shift
      ;;
    --help)
      echo "Usage: $0 [OPTIONS]"
      echo "Options:"
      echo "  --max-workers N     Maximum number of parallel workers (default: auto-detect)"
      echo "  --timeout N         Timeout for each test tier in seconds (default: 300)"
      echo "  --fail-fast         Stop on first failure"
      echo "  --detailed          Show detailed output from each tier (default behavior)"
      echo "  --brief             Disable detailed output for minimal reporting"
      echo "  --dry-run           Show what would be executed without running tests"
      echo "  --interactive       Enable interactive TUI mode (requires explicit flag)"
      echo "  --no-interactive    Disable interactive TUI mode (default behavior)"
      echo "  --help              Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for available options"
      exit 1
      ;;
  esac
done

# Interactive mode is now explicitly controlled by command-line flags
# Default is non-interactive (false) for CI/CD compatibility
# Users must explicitly use --interactive to enable TUI features

# Interactive configuration function
interactive_config() {
  if [[ "$INTERACTIVE" != true ]]; then
    return 0
  fi

  tui_log "info" "🎯 Interactive Configuration Mode"
  echo ""

  # Show current configuration
  local config_display
  if [[ "$GUM_AVAILABLE" == true ]]; then
    config_display=$(gum style \
      --foreground 212 --border-foreground 212 --border rounded \
      --align left --width 60 --margin "0 2" --padding "1 2" \
      "Current Configuration:" \
      "• Max Workers: ${MAX_WORKERS:-auto-detect}" \
      "• Timeout: ${TIMEOUT}s" \
      "• Fail Fast: $FAIL_FAST" \
      "• Detailed Output: $DETAILED")
    echo "$config_display"
  else
    echo "Current Configuration:"
    echo "  Max Workers: ${MAX_WORKERS:-auto-detect}"
    echo "  Timeout: ${TIMEOUT}s"
    echo "  Fail Fast: $FAIL_FAST"
    echo "  Detailed Output: $DETAILED"
  fi
  echo ""

  # Ask if user wants to modify configuration
  if tui_confirm "Modify configuration?"; then
    echo ""

    # Configure max workers
    local new_workers
    new_workers=$(tui_input "Max workers (1-8, or 'auto')" "${MAX_WORKERS:-auto}")
    if [[ "$new_workers" != "auto" ]] && [[ "$new_workers" =~ ^[1-8]$ ]]; then
      MAX_WORKERS="$new_workers"
    elif [[ "$new_workers" == "auto" ]]; then
      MAX_WORKERS=""
    fi

    # Configure timeout
    local new_timeout
    new_timeout=$(tui_input "Timeout in seconds" "$TIMEOUT")
    if [[ "$new_timeout" =~ ^[0-9]+$ ]] && [[ "$new_timeout" -gt 0 ]]; then
      TIMEOUT="$new_timeout"
    fi

    # Configure options
    local options=()
    if [[ "$FAIL_FAST" == true ]]; then
      options+=("fail-fast (currently ON)")
    else
      options+=("fail-fast (currently OFF)")
    fi

    if [[ "$DETAILED" == true ]]; then
      options+=("detailed (currently ON)")
    else
      options+=("detailed (currently OFF)")
    fi

    options+=("dry-run (currently OFF)")
    options+=("Continue with current settings")

    local choice
    choice=$(tui_choose "Toggle options:" "${options[@]}")

    case "$choice" in
      "fail-fast"*) FAIL_FAST=$([[ "$FAIL_FAST" == true ]] && echo false || echo true) ;;
      "detailed"*) DETAILED=$([[ "$DETAILED" == true ]] && echo false || echo true) ;;
      "dry-run"*) DRY_RUN=true ;;
    esac
  fi

  echo ""
}

# Auto-detect optimal worker count if not specified
if [[ -z "$MAX_WORKERS" ]]; then
  # Use number of CPU cores, but cap at 4 for test stability
  MAX_WORKERS=$(nproc 2>/dev/null || echo "2")
  if [[ $MAX_WORKERS -gt 4 ]]; then
    MAX_WORKERS=4
  fi
fi

# Display header
display_header() {
  if [[ "$GUM_AVAILABLE" == true ]]; then
    local header
    header=$(gum style \
      --foreground 212 --border-foreground 212 --border double \
      --align center --width 64 --margin "1 0" --padding "1 2" \
      "🧪 A2A Platform - Parallel Test Runner with TUI 🎀")
    echo "$header"

    local config
    config=$(gum join --vertical \
      "$(gum style --foreground 99 --bold "Configuration:")" \
      "$(gum style --foreground 250 "  Max Workers: $MAX_WORKERS")" \
      "$(gum style --foreground 250 "  Timeout: ${TIMEOUT}s")" \
      "$(gum style --foreground 250 "  Fail Fast: $FAIL_FAST")" \
      "$(gum style --foreground 250 "  Detailed Output: $DETAILED")" \
      "$(gum style --foreground 250 "  Interactive Mode: $INTERACTIVE")")
    echo "$config"
  else
    echo "╔════════════════════════════════════════════════════════════╗"
    echo "║ A2A Platform - Parallel Test Runner with TUI               ║"
    echo "╚════════════════════════════════════════════════════════════╝"
    echo ""
    echo -e "${BLUE}Configuration:${NC}"
    echo "  Max Workers: $MAX_WORKERS"
    echo "  Timeout: ${TIMEOUT}s"
    echo "  Fail Fast: $FAIL_FAST"
    echo "  Detailed Output: $DETAILED"
    echo "  Interactive Mode: $INTERACTIVE"
  fi
  echo ""
}

# Run interactive configuration
interactive_config

# Display header with final configuration
display_header

# Test tier definitions
# Defines the different categories of tests to be run.
# Each tier is defined by a name and the arguments to pass to ./scripts/run-backend-tests.sh
# The goal is to have three mutually exclusive test categories:
# 1. No-db tests: Only tests marked with @pytest.mark.no_db
# 2. Fast-db tests: Only tests marked with @pytest.mark.fast_db
# 3. Normal tests: All remaining tests that don\'t have either no_db or fast_db marker
declare -A TEST_TIERS=(
  ["no-db-tests"]="--no-db"
  ["fast-db-tests"]="--fast-db" # Assumes run-backend-tests.sh supports --fast-db to run only tests marked \'fast_db\'
  ["normal-tests"]="--exclude-no-db --exclude-fast-db" # Runs all tests not covered by \'no-db-tests\' or \'fast-db-tests\'
)

declare -A TIER_COLORS=(
  ["no-db-tests"]="$GREEN"
  ["fast-db-tests"]="$YELLOW"
  ["normal-tests"]="$PURPLE"
)

declare -A TIER_DESCRIPTIONS=(
  ["no-db-tests"]="No-DB Tests (marked @pytest.mark.no_db)"
  ["fast-db-tests"]="Fast-DB Tests (marked @pytest.mark.fast_db)"
  ["normal-tests"]="Normal Tests (without no_db or fast_db markers)"
)

declare -A TIER_EMOJIS=(
  ["no-db-tests"]="⚡" # Previously \'database-free\'
  ["fast-db-tests"]="🔧" # Reusing emoji from \'unit-postgresql\', now for fast_db tests
  ["normal-tests"]="🔗" # Reusing emoji from \'integration\', now for other standard tests
)

# Interactive test tier selection
interactive_tier_selection() {
  if [[ "$INTERACTIVE" != true ]]; then
    return 0
  fi

  tui_log "info" "🎯 Test Tier Selection"
  echo ""

  # Show available test tiers
  if [[ "$GUM_AVAILABLE" == true ]]; then
    local tier_info
    tier_info=$(gum style \
      --foreground 99 --border-foreground 99 --border rounded \
      --align left --width 70 --margin "0 2" --padding "1 2" \
      "Available Test Tiers:" \
      "⚡ no-db-tests      - No-DB Tests (marked @pytest.mark.no_db)" \
      "🔧 fast-db-tests    - Fast-DB Tests (marked @pytest.mark.fast_db)" \
      "🔗 normal-tests     - Normal Tests (without no_db or fast_db markers)")
    echo "$tier_info"
  else
    echo "Available Test Tiers:"
    echo "  no-db-tests      - No-DB Tests (marked @pytest.mark.no_db)"
    echo "  fast-db-tests    - Fast-DB Tests (marked @pytest.mark.fast_db)"
    echo "  normal-tests     - Normal Tests (without no_db or fast_db markers)"
  fi
  echo ""

  # Ask if user wants to select specific tiers
  if tui_confirm "Select specific test tiers? (default: run all)"; then
    echo ""

    local tier_options=()
    for tier in "${!TEST_TIERS[@]}"; do
      local emoji="${TIER_EMOJIS[$tier]}"
      local desc="${TIER_DESCRIPTIONS[$tier]}"
      tier_options+=("$emoji $tier - $desc")
    done
    tier_options+=("All tiers (default)")

    local selected_tiers=()
    if [[ "$GUM_AVAILABLE" == true ]]; then
      # Use gum choose with multi-select
      local choices
      choices=$(printf '%s\n' "${tier_options[@]}" | gum choose --no-limit --header "Select test tiers to run:")

      # Parse selected tiers
      while IFS= read -r choice; do
        if [[ "$choice" == "All tiers"* ]]; then
          selected_tiers=()
          break
        else
          # Extract tier name from choice
          local tier_name
          tier_name=$(echo "$choice" | sed -E 's/^[^ ]+ ([^ ]+) -.*/\1/')
          selected_tiers+=("$tier_name")
        fi
      done <<< "$choices"
    else
      # Fallback selection
      echo "Select tiers (enter numbers separated by spaces, or 'all'):"
      for i in "${!tier_options[@]}"; do
        echo "$((i+1)). ${tier_options[$i]}"
      done
      read -r selection

      if [[ "$selection" == "all" ]] || [[ -z "$selection" ]]; then
        selected_tiers=()
      else
        for num in $selection; do
          if [[ "$num" =~ ^[0-9]+$ ]] && [[ "$num" -le "${#tier_options[@]}" ]]; then
            local idx=$((num-1))
            local choice="${tier_options[$idx]}"
            if [[ "$choice" != "All tiers"* ]]; then
              local tier_name
              tier_name=$(echo "$choice" | sed -E 's/^[^ ]+ ([^ ]+) -.*/\1/')
              selected_tiers+=("$tier_name")
            fi
          fi
        done
      fi
    fi

    # Update TEST_TIERS to only include selected tiers
    if [[ ${#selected_tiers[@]} -gt 0 ]]; then
      local new_test_tiers=()
      for tier in "${selected_tiers[@]}"; do
        if [[ -n "${TEST_TIERS[$tier]}" ]]; then
          new_test_tiers["$tier"]="${TEST_TIERS[$tier]}"
        fi
      done

      # Replace TEST_TIERS with selected tiers
      unset TEST_TIERS
      declare -gA TEST_TIERS
      for tier in "${!new_test_tiers[@]}"; do
        TEST_TIERS["$tier"]="${new_test_tiers[$tier]}"
      done

      tui_log "info" "Selected tiers: ${selected_tiers[*]}"
    else
      tui_log "info" "Running all test tiers"
    fi
  fi

  echo ""
}

# Function to extract pytest short test summary info section
extract_test_summary() {
  local log_file="$1"
  local output_file="$2"

  # Check if the log file exists
  if [[ ! -f "$log_file" ]]; then
    return 1
  fi

  # Extract the section between the markers using sed
  # Start from "short test summary info" line and continue until final summary line
  # Handle various patterns for the end marker (with different counts and durations)
  sed -n '/=*.*short test summary info.*=*/,/=*.*[0-9]\+ \(failed\|passed\|error\|skipped\).*in.*[0-9]\+\.[0-9]\+s.*=*/p' "$log_file" > "$output_file" 2>/dev/null

  # Check if we actually captured something meaningful
  if [[ -s "$output_file" ]]; then
    # Verify the content looks like a pytest summary
    if grep -q "FAILED\|ERROR\|short test summary info" "$output_file"; then
      return 0
    fi
  fi

  # If the standard extraction didn't work, try a broader approach
  # Look for any section that starts with short test summary and contains FAILED/ERROR lines
  if grep -q "short test summary info" "$log_file" && grep -q "FAILED\|ERROR" "$log_file"; then
    # Extract from short test summary to end of file, then trim to just the relevant section
    sed -n '/=*.*short test summary info.*=*/,$p' "$log_file" | \
    sed '/^$/,$d' > "$output_file" 2>/dev/null

    if [[ -s "$output_file" ]]; then
      return 0
    fi
  fi

  return 1
}

# Function to run a single test tier with TUI enhancements
run_test_tier() {
  local tier_name="$1"
  local tier_args="$2"
  local tier_color="$3"
  local output_file="/tmp/a2a-test-${tier_name}.log"
  local start_time=$(date +%s)
  local emoji="${TIER_EMOJIS[$tier_name]}"

  # Enhanced starting message with TUI
  if [[ "$GUM_AVAILABLE" == true ]]; then
    local start_msg
    start_msg=$(gum style --foreground 250 --bold "$emoji [$tier_name] Starting tests...")
    echo "$start_msg"
  else
    echo -e "${tier_color}[${tier_name}]${NC} Starting..."
  fi

  if [[ "$DRY_RUN" == true ]]; then
    local dry_run_msg="DRY RUN: ./scripts/run-backend-tests.sh --ci $tier_args"
    if [[ "$GUM_AVAILABLE" == true ]]; then
      gum style --foreground 99 --italic "$emoji [$tier_name] $dry_run_msg"
    else
      echo -e "${tier_color}[${tier_name}]${NC} $dry_run_msg"
    fi
    echo "0" > "/tmp/a2a-test-${tier_name}.result"
    echo "5" > "/tmp/a2a-test-${tier_name}.duration"
    echo "10" > "/tmp/a2a-test-${tier_name}.count"  # Mock test count for dry run
    return 0
  fi

  # Run the test and capture exit code
  timeout "$TIMEOUT" ./scripts/run-backend-tests.sh --ci $tier_args > "$output_file" 2>&1
  local exit_code=$?

  local end_time=$(date +%s)
  local duration=$((end_time - start_time))

  # Extract test counts from pytest output
  local summary_line=$(grep -E "([0-9]+ (passed|failed|skipped|error))" "$output_file" | tail -1)
  local test_count=$(echo "$summary_line" | grep -oE "[0-9]+ passed" | sed -E 's/([0-9]+) passed/\1/')
  local failed_count=$(echo "$summary_line" | grep -oE "[0-9]+ failed" | sed -E 's/([0-9]+) failed/\1/')
  local error_count=$(echo "$summary_line" | grep -oE "[0-9]+ error" | sed -E 's/([0-9]+) error/\1/')
  local skipped_count=$(echo "$summary_line" | grep -oE "[0-9]+ skipped" | sed -E 's/([0-9]+) skipped/\1/')

  # Ensure all counts have default values
  test_count=${test_count:-0}
  failed_count=${failed_count:-0}
  error_count=${error_count:-0}
  skipped_count=${skipped_count:-0}

  local total_tests=$((test_count + failed_count + error_count + skipped_count))
  local has_failures=$((failed_count + error_count))

  # Enhanced result reporting with TUI
  if [[ $total_tests -gt 0 ]]; then
    # Tests ran, report results
    if [[ $has_failures -gt 0 ]]; then
      # Failure - store result without printing completion message
      # (completion will be shown in final summary)
      echo "1" > "/tmp/a2a-test-${tier_name}.result"  # Changed to 1 to indicate failure

      # Extract test summary for later use in final summary
      local summary_file="/tmp/a2a-test-${tier_name}.summary"
      extract_test_summary "$output_file" "$summary_file" || true
    else
      # Success - store result without printing completion message
      # (completion will be shown in final summary)
      echo "0" > "/tmp/a2a-test-${tier_name}.result"
    fi
    echo "$duration" > "/tmp/a2a-test-${tier_name}.duration"
    echo "$total_tests" > "/tmp/a2a-test-${tier_name}.count"
  else
    # No tests ran, likely a setup failure or collection error
    # Store result without printing completion message
    # (completion will be shown in final summary)
    echo "1" > "/tmp/a2a-test-${tier_name}.result"
    echo "$duration" > "/tmp/a2a-test-${tier_name}.duration"
    echo "0" > "/tmp/a2a-test-${tier_name}.count"

    # Extract test summary for later use in final summary
    local summary_file="/tmp/a2a-test-${tier_name}.summary"
    extract_test_summary "$output_file" "$summary_file" || true
  fi
}

# Function to monitor parallel execution with TUI enhancements
monitor_execution() {
  local pids=("$@")
  local total=${#pids[@]}

  # Enhanced monitoring message with TUI
  if [[ "$GUM_AVAILABLE" == true ]]; then
    local monitor_msg
    monitor_msg=$(gum style --foreground 99 --bold "🔍 Monitoring $total parallel test tiers...")
    echo "$monitor_msg"
  else
    echo -e "${BLUE}Monitoring ${total} parallel test tiers...${NC}"
  fi
  echo ""

  # Progress tracking
  local completed=0
  local failed_tiers=()

  # Gum spinner integration
  local SPIN_PID=""
  if [[ "$GUM_AVAILABLE" == true ]]; then
    # Start gum spin in background with a long sleep command
    gum spin --spinner dot --title "Executing parallel test suite..." sleep 3600 &
    SPIN_PID=$!
  fi

  # Wait for all processes to complete
  for pid in "${pids[@]}"; do
    if [[ -n "$pid" ]]; then
      wait "$pid" 2>/dev/null || true
      completed=$((completed + 1))
      # No per-process progress output; spinner is running
      # Check for fail-fast after each completion
      if [[ "$FAIL_FAST" == true ]]; then
        for tier in "${!TEST_TIERS[@]}"; do
          if [[ -f "/tmp/a2a-test-${tier}.result" ]]; then
            local result=$(cat "/tmp/a2a-test-${tier}.result")
            if [[ "$result" != "0" ]]; then
              if [[ "$GUM_AVAILABLE" == true ]]; then
                local fail_fast_msg
                fail_fast_msg=$(gum style --foreground 196 --bold "⚡ Fail-fast triggered by $tier failure")
                echo "$fail_fast_msg"
              else
                echo -e "${RED}Fail-fast triggered by ${tier} failure${NC}"
              fi

              # Kill remaining processes
              for remaining_pid in "${pids[@]}"; do
                if [[ -n "$remaining_pid" ]] && [[ "$remaining_pid" != "$pid" ]]; then
                  kill "$remaining_pid" 2>/dev/null || true
                fi
              done
              return 1
            fi
          fi
        done
      fi
    fi
  done

  # Stop gum spinner if running
  if [[ "$GUM_AVAILABLE" == true && -n "$SPIN_PID" ]]; then
    kill "$SPIN_PID" 2>/dev/null || true
    wait "$SPIN_PID" 2>/dev/null || true
    echo "" # Ensure clean line after spinner
  fi

  return 0
}

# Main execution with TUI enhancements
main() {
  local start_time=$(date +%s)
  local pids=()
  local active_workers=0

  # Enhanced execution start message
  if [[ "$GUM_AVAILABLE" == true ]]; then
    local start_msg
    start_msg=$(gum style --foreground 46 --bold "🚀 Starting parallel test execution...")
    echo "$start_msg"

    # Show execution plan
    if [[ "$INTERACTIVE" == true ]]; then
      local plan_items=()
      for tier in "${!TEST_TIERS[@]}"; do
        local emoji="${TIER_EMOJIS[$tier]}"
        local desc="${TIER_DESCRIPTIONS[$tier]}"
        plan_items+=("$emoji $tier - $desc")
      done

      local execution_plan
      execution_plan=$(gum style \
        --foreground 250 --border-foreground 99 --border rounded \
        --align left --width 70 --margin "0 2" --padding "1 2" \
        "Execution Plan:" \
        "${plan_items[@]}")
      echo "$execution_plan"
    fi
  else
    echo -e "${BLUE}Starting parallel test execution...${NC}"
  fi
  echo ""

  # Confirmation for destructive operations
  if [[ "$FAIL_FAST" == true ]] && [[ "$INTERACTIVE" == true ]]; then
    if ! tui_confirm "Fail-fast mode enabled. Continue?"; then
      tui_log "info" "Execution cancelled by user"
      return 0
    fi
    echo ""
  fi

  # Start all test tiers in parallel
  for tier in "${!TEST_TIERS[@]}"; do
    local tier_args="${TEST_TIERS[$tier]}"
    local tier_color="${TIER_COLORS[$tier]}"

    # Always call run_test_tier, which handles dry run logic internally
    run_test_tier "$tier" "$tier_args" "$tier_color" &
    local pid=$!
    pids+=("$pid")
  done

  if [[ "$DRY_RUN" == true ]]; then
    echo ""
    if [[ "$GUM_AVAILABLE" == true ]]; then
      local dry_run_complete
      dry_run_complete=$(gum style --foreground 46 --bold "✅ Dry run completed. No tests were actually executed.")
      echo "$dry_run_complete"
    else
      echo -e "${GREEN}Dry run completed. No tests were actually executed.${NC}"
    fi
    return 0
  fi

  # Monitor execution
  if ! monitor_execution "${pids[@]}"; then
    if [[ "$GUM_AVAILABLE" == true ]]; then
      local failure_msg
      failure_msg=$(gum style --foreground 196 --bold "❌ Parallel execution failed")
      echo "$failure_msg"
    else
      echo -e "${RED}Parallel execution failed${NC}"
    fi
    return 1
  fi

  # Collect and report results with TUI enhancements
  local end_time=$(date +%s)
  local total_duration=$((end_time - start_time))
  local total_tests=0
  local failed_tiers=()

  echo ""

  # Enhanced results header
  if [[ "$GUM_AVAILABLE" == true ]]; then
    echo "$results_header"
  else
    # Removed ASCII art box for results header
    echo "Parallel Test Execution Results"
  fi
  echo ""

  # Collect results and build display
  local result_lines=()
  for tier in "${!TEST_TIERS[@]}"; do
    local tier_color="${TIER_COLORS[$tier]}"
    local tier_desc="${TIER_DESCRIPTIONS[$tier]}"
    local emoji="${TIER_EMOJIS[$tier]}"

    if [[ -f "/tmp/a2a-test-${tier}.result" ]]; then
      local result=$(cat "/tmp/a2a-test-${tier}.result")
      local duration=$(cat "/tmp/a2a-test-${tier}.duration" 2>/dev/null || echo "0")
      local count=$(cat "/tmp/a2a-test-${tier}.count" 2>/dev/null || echo "0")

      total_tests=$((total_tests + count))

      if [[ "$result" == "0" ]]; then
        if [[ "$GUM_AVAILABLE" == true ]]; then
          local success_line
          success_line=$(gum style --foreground 46 "$emoji $tier_desc ✅ $count tests in ${duration}s")
          result_lines+=("$success_line")
        else
          printf "${tier_color}%-35s${NC} ✅ %3s tests in %3ss\n" "$tier_desc" "$count" "$duration"
        fi
      else
        # Check if tests actually ran by looking at the count
        if [[ "$count" -gt 0 ]]; then
          if [[ "$GUM_AVAILABLE" == true ]]; then
            local warning_line
            warning_line=$(gum style --foreground 214 "$emoji $tier_desc ⚠️  $count tests in ${duration}s (some failed)")
            result_lines+=("$warning_line")
          else
            printf "${tier_color}%-35s${NC} ⚠️  %3s tests in %3ss (some failed)\n" "$tier_desc" "$count" "$duration"
          fi
        else
          if [[ "$GUM_AVAILABLE" == true ]]; then
            local error_line
            error_line=$(gum style --foreground 196 "$emoji $tier_desc ❌ Failed after ${duration}s (no tests executed)")
            result_lines+=("$error_line")
          else
            printf "${tier_color}%-35s${NC} ❌ Failed after %3ss (no tests executed)\n" "$tier_desc" "$duration"
          fi
          failed_tiers+=("$tier")
        fi
      fi
    else
      # Check if log file exists to provide more specific error information
      local log_file="/tmp/a2a-test-${tier}.log"
      local error_msg="❓ No result file"

      if [[ -f "$log_file" ]]; then
        # Log file exists but no result file - likely a test execution failure
        error_msg="❌ Test execution failed"
      else
        # No log file at all - likely a setup or script failure
        error_msg="❌ Failed to start tests"
      fi

      if [[ "$GUM_AVAILABLE" == true ]]; then
        local missing_line
        missing_line=$(gum style --foreground 196 "$emoji $tier_desc $error_msg")
        result_lines+=("$missing_line")
      else
        printf "${tier_color}%-35s${NC} %s\n" "$tier_desc" "$error_msg"
      fi
      failed_tiers+=("$tier")
    fi
  done

  # Display results
  if [[ "$GUM_AVAILABLE" == true ]]; then
    for line in "${result_lines[@]}"; do
      echo "$line"
    done
  fi

  echo ""

  # Enhanced summary
  if [[ "$GUM_AVAILABLE" == true ]]; then
    local summary
    summary=$(gum join --vertical \
      "$(gum style --foreground 99 --bold "📈 Summary:")" \
      "$(gum style --foreground 250 "  Total Tests: $total_tests")" \
      "$(gum style --foreground 250 "  Total Time: ${total_duration}s")" \
      "$(gum style --foreground 250 "  Failed Tiers: ${#failed_tiers[@]}")")
    echo "$summary"
  else
    echo "Summary:"
    echo "  Total Tests: $total_tests"
    echo "  Total Time: ${total_duration}s"
    echo "  Failed Tiers: ${#failed_tiers[@]}"
  fi

  # Enhanced success/failure reporting
  if [[ ${#failed_tiers[@]} -eq 0 ]]; then
    if [[ "$GUM_AVAILABLE" == true ]]; then
      local success_msg
      success_msg=$(gum style --foreground 46 --bold "🎉 All test tiers completed successfully!")
      echo "$success_msg"
    else
      echo -e "${GREEN}🎉 All test tiers completed successfully!${NC}"
    fi

    # Calculate performance improvement
    local sequential_estimate=$((total_tests / 20))  # Rough estimate
    if [[ $sequential_estimate -gt $total_duration ]]; then
      local improvement=$(( (sequential_estimate - total_duration) * 100 / sequential_estimate ))
      if [[ "$GUM_AVAILABLE" == true ]]; then
        local perf_msg
        perf_msg=$(gum style --foreground 99 "⚡ Estimated ${improvement}% faster than sequential execution")
        echo "$perf_msg"
      else
        echo -e "${GREEN}⚡ Estimated ${improvement}% faster than sequential execution${NC}"
      fi
    fi
  else
    if [[ "$GUM_AVAILABLE" == true ]]; then
      local failure_msg
      failure_msg=$(gum style --foreground 196 --bold "❌ Failed tiers: ${failed_tiers[*]}")
      echo "$failure_msg"
    else
      echo -e "${RED}❌ Failed tiers: ${failed_tiers[*]}${NC}"
    fi

    if [[ "$DETAILED" == true ]]; then
      echo ""
      if [[ "$GUM_AVAILABLE" == true ]]; then
        local detail_header
        detail_header=$(gum style --foreground 196 --bold "🔍 Detailed failure information:")
        echo "$detail_header"
      else
        echo "Detailed failure information:"
      fi

      for tier in "${failed_tiers[@]}"; do
        local output_file="/tmp/a2a-test-${tier}.log"
        local summary_file="/tmp/a2a-test-${tier}.summary"
        if [[ -f "$output_file" ]]; then
          if [[ "$GUM_AVAILABLE" == true ]]; then
            local emoji="${TIER_EMOJIS[$tier]}"
            echo ""
            # Try to show test summary first, fallback to last lines
            if [[ -f "$summary_file" ]]; then
              gum style --foreground 196 --bold "$emoji [$tier] Test Summary:"
              cat "$summary_file" | gum style --foreground 250 --margin "0 2"
            else
              gum style --foreground 196 --bold "$emoji [$tier] Last 10 lines:"
              tail -10 "$output_file" | gum style --foreground 250 --margin "0 2"
            fi
          else
            echo -e "${RED}[${tier}] $([ -f "$summary_file" ] && echo "Test Summary" || echo "Last 10 lines"):${NC}"
            if [[ -f "$summary_file" ]]; then
              cat "$summary_file" | sed "s/^/  /"
            else
              tail -10 "$output_file" | sed "s/^/  /"
            fi
          fi
          echo ""
        fi
      done
    fi

    return 1
  fi

  # Cleanup temporary files (only if no failures occurred)
  if [[ ${#failed_tiers[@]} -eq 0 ]]; then
    # Success - clean up all temporary files
    for tier in "${!TEST_TIERS[@]}"; do
      rm -f "/tmp/a2a-test-${tier}.result" "/tmp/a2a-test-${tier}.duration" "/tmp/a2a-test-${tier}.count" "/tmp/a2a-test-${tier}.log" "/tmp/a2a-test-${tier}.summary"
    done
  else
    # Failures occurred - preserve log and summary files for debugging, clean up other files
    echo ""
    if [[ "$GUM_AVAILABLE" == true ]]; then
      local preserve_msg
      preserve_msg=$(gum style --foreground 99 "📁 Test files preserved for debugging:")
      echo "$preserve_msg"
    else
      echo -e "${BLUE}📁 Test files preserved for debugging:${NC}"
    fi

    for tier in "${failed_tiers[@]}"; do
      local log_file="/tmp/a2a-test-${tier}.log"
      local summary_file="/tmp/a2a-test-${tier}.summary"
      if [[ -f "$log_file" ]]; then
        if [[ "$GUM_AVAILABLE" == true ]]; then
          local emoji="${TIER_EMOJIS[$tier]}"
          gum style --foreground 250 "  $emoji $tier: $log_file"
          if [[ -f "$summary_file" ]]; then
            gum style --foreground 250 "  $emoji $tier summary: $summary_file"
          fi
        else
          echo "  $tier: $log_file"
          if [[ -f "$summary_file" ]]; then
            echo "  $tier summary: $summary_file"
          fi
        fi
      fi
    done

    # Clean up non-log files for successful tiers
    for tier in "${!TEST_TIERS[@]}"; do
      rm -f "/tmp/a2a-test-${tier}.result" "/tmp/a2a-test-${tier}.duration" "/tmp/a2a-test-${tier}.count"
      # Only remove log and summary files for successful tiers
      if [[ ! " ${failed_tiers[*]} " =~ " ${tier} " ]]; then
        rm -f "/tmp/a2a-test-${tier}.log" "/tmp/a2a-test-${tier}.summary"
      fi
    done

    echo ""
    if [[ "$DETAILED" == false ]]; then
      if [[ "$GUM_AVAILABLE" == true ]]; then
        local tip_msg
        tip_msg=$(gum style --foreground 99 "💡 TIP: Use --detailed flag to see error output in the summary")
        echo "$tip_msg"
      else
        echo -e "${BLUE}💡 TIP: Use --detailed flag to see error output in the summary${NC}"
      fi
    else
      if [[ "$GUM_AVAILABLE" == true ]]; then
        local tip_msg
        tip_msg=$(gum style --foreground 99 "💡 TIP: Use --brief flag for minimal output")
        echo "$tip_msg"
      else
        echo -e "${BLUE}💡 TIP: Use --brief flag for minimal output${NC}"
      fi
    fi
  fi
}

# Check for gum installation and provide helpful message when interactive mode is requested
if [[ "$GUM_AVAILABLE" == false ]] && [[ "$INTERACTIVE" == true ]]; then
  echo ""
  echo "💡 TIP: Install 'gum' for enhanced Terminal User Interface experience!"
  echo "   brew install gum  # macOS"
  echo "   apt install gum   # Ubuntu/Debian"
  echo "   See: https://github.com/charmbracelet/gum"
  echo ""
  echo "ℹ️  Note: Interactive mode was requested but gum is not available."
  echo "   Falling back to basic interactive prompts."
  echo ""
fi

# Helper function to examine test logs
examine_logs() {
  echo ""
  if [[ "$GUM_AVAILABLE" == true ]]; then
    local header
    header=$(gum style --foreground 212 --bold "🔍 Available Test Log Files:")
    echo "$header"
  else
    echo -e "${BLUE}🔍 Available Test Log Files:${NC}"
  fi
  echo ""

  local found_logs=false
  for log_file in /tmp/a2a-test-*.log; do
    if [[ -f "$log_file" ]]; then
      found_logs=true
      local tier_name
      tier_name=$(basename "$log_file" .log | sed 's/a2a-test-//')
      local emoji="${TIER_EMOJIS[$tier_name]:-🔧}"

      if [[ "$GUM_AVAILABLE" == true ]]; then
        gum style --foreground 250 "$emoji $tier_name: $log_file"
      else
        echo "  $tier_name: $log_file"
      fi

      # Show test summary if available, otherwise show last few lines as preview
      local summary_file="/tmp/a2a-test-${tier_name}.summary"
      echo ""
      if [[ -f "$summary_file" ]]; then
        if [[ "$GUM_AVAILABLE" == true ]]; then
          gum style --foreground 196 "  Test Summary:"
          cat "$summary_file" | gum style --foreground 250 --margin "0 4"
        else
          echo -e "${RED}  Test Summary:${NC}"
          cat "$summary_file" | sed 's/^/    /'
        fi
      else
        if [[ "$GUM_AVAILABLE" == true ]]; then
          gum style --foreground 99 "  Last 5 lines:"
          tail -5 "$log_file" | gum style --foreground 250 --margin "0 4"
        else
          echo -e "${YELLOW}  Last 5 lines:${NC}"
          tail -5 "$log_file" | sed 's/^/    /'
        fi
      fi
      echo ""
    fi
  done

  if [[ "$found_logs" == false ]]; then
    if [[ "$GUM_AVAILABLE" == true ]]; then
      gum style --foreground 99 "No test log files found in /tmp/"
    else
      echo "No test log files found in /tmp/"
    fi
  fi
}

# Check if user wants to examine logs (only if there are log files)
if [[ -f /tmp/a2a-test-*.log ]] 2>/dev/null; then
  echo ""
  if [[ "$GUM_AVAILABLE" == true ]]; then
    local examine_tip
    examine_tip=$(gum style --foreground 99 "💡 TIP: Run 'bash -c \"source scripts/parallel-test-runner.sh && examine_logs\"' to examine test logs and summaries")
    echo "$examine_tip"
  else
    echo -e "${BLUE}💡 TIP: Check /tmp/a2a-test-*.log and /tmp/a2a-test-*.summary files for detailed error information${NC}"
  fi
fi

main "$@"
