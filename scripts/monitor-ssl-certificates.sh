#!/bin/bash
# filepath: /Users/<USER>/dev/blkops-collective/a2a-platform/scripts/monitor-ssl-certificates.sh
#
# Script to monitor SSL certificates for expiration and alert if they're nearing expiration
# This should be run regularly via cron or a CI/CD pipeline

set -e

# Define colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Define the domains to check
DOMAINS=(
  "api.domain.com"
  # Add additional domains as needed
)

# Warning thresholds (in days)
CRITICAL_THRESHOLD=15
WARNING_THRESHOLD=30

# Log file
LOG_FILE="/tmp/ssl-certificate-check.log"
touch "$LOG_FILE"

# Email settings (if using email alerts)
EMAIL_TO="<EMAIL>"
EMAIL_FROM="<EMAIL>"
EMAIL_SUBJECT="SSL Certificate Expiration Alert"

# Slack webhook URL (if using Slack alerts)
SLACK_WEBHOOK_URL="https://hooks.slack.com/services/your/webhook/url"

# Function to send Slack alert
send_slack_alert() {
  local domain=$1
  local days_remaining=$2
  local expiry_date=$3
  local severity=$4
  
  if [ -n "$SLACK_WEBHOOK_URL" ]; then
    local color="warning"
    if [ "$severity" == "CRITICAL" ]; then
      color="danger"
    fi
    
    curl -s -X POST --data-urlencode "payload={
      \"attachments\": [
        {
          \"fallback\": \"SSL Certificate Alert: $domain will expire in $days_remaining days\",
          \"color\": \"$color\",
          \"pretext\": \"SSL Certificate Expiration Alert\",
          \"title\": \"$severity: Certificate for $domain is expiring soon\",
          \"text\": \"The SSL certificate for $domain will expire in $days_remaining days (on $expiry_date).\",
          \"fields\": [
            {
              \"title\": \"Domain\",
              \"value\": \"$domain\",
              \"short\": true
            },
            {
              \"title\": \"Days Remaining\",
              \"value\": \"$days_remaining\",
              \"short\": true
            },
            {
              \"title\": \"Expiry Date\",
              \"value\": \"$expiry_date\",
              \"short\": true
            },
            {
              \"title\": \"Severity\",
              \"value\": \"$severity\",
              \"short\": true
            }
          ]
        }
      ]
    }" "$SLACK_WEBHOOK_URL"
  fi
}

# Function to send email alert
send_email_alert() {
  local domain=$1
  local days_remaining=$2
  local expiry_date=$3
  local severity=$4
  
  if [ -n "$EMAIL_TO" ] && [ -n "$EMAIL_FROM" ]; then
    echo "Subject: $EMAIL_SUBJECT - $severity: $domain ($days_remaining days)
From: $EMAIL_FROM
To: $EMAIL_TO
MIME-Version: 1.0
Content-Type: text/html; charset=UTF-8

<html>
<body>
<h2>SSL Certificate Expiration Alert</h2>
<p><strong>$severity</strong>: The SSL certificate for <strong>$domain</strong> will expire in <strong>$days_remaining days</strong> (on $expiry_date).</p>
<p>Please take immediate action to renew this certificate to prevent service disruption.</p>
</body>
</html>" | sendmail -t
  fi
}

echo -e "${YELLOW}Checking SSL certificates for expiration...${NC}" | tee -a "$LOG_FILE"
echo "$(date)" | tee -a "$LOG_FILE"
echo "----------------------------------------" | tee -a "$LOG_FILE"

for domain in "${DOMAINS[@]}"; do
  echo -e "Checking ${GREEN}$domain${NC}..." | tee -a "$LOG_FILE"
  
  # Get certificate expiration date
  expiry_date=$(echo | openssl s_client -servername "$domain" -connect "$domain":443 2>/dev/null | openssl x509 -noout -enddate 2>/dev/null | cut -d= -f2)
  
  if [ -z "$expiry_date" ]; then
    echo -e "${RED}Error: Could not retrieve certificate for $domain${NC}" | tee -a "$LOG_FILE"
    continue
  fi
  
  # Convert expiry date to seconds since epoch
  expiry_epoch=$(date -d "$expiry_date" +%s 2>/dev/null || date -j -f "%b %d %H:%M:%S %Y %Z" "$expiry_date" +%s)
  current_epoch=$(date +%s)
  
  # Calculate days remaining
  seconds_remaining=$((expiry_epoch - current_epoch))
  days_remaining=$((seconds_remaining / 86400))
  
  echo -e "  Expires: ${YELLOW}$expiry_date${NC} ($days_remaining days remaining)" | tee -a "$LOG_FILE"
  
  # Check if certificate is nearing expiration
  if [ $days_remaining -le $CRITICAL_THRESHOLD ]; then
    echo -e "  ${RED}CRITICAL: Certificate will expire in less than $CRITICAL_THRESHOLD days!${NC}" | tee -a "$LOG_FILE"
    send_slack_alert "$domain" "$days_remaining" "$expiry_date" "CRITICAL"
    send_email_alert "$domain" "$days_remaining" "$expiry_date" "CRITICAL"
  elif [ $days_remaining -le $WARNING_THRESHOLD ]; then
    echo -e "  ${YELLOW}WARNING: Certificate will expire in less than $WARNING_THRESHOLD days.${NC}" | tee -a "$LOG_FILE"
    send_slack_alert "$domain" "$days_remaining" "$expiry_date" "WARNING"
    send_email_alert "$domain" "$days_remaining" "$expiry_date" "WARNING"
  else
    echo -e "  ${GREEN}OK: Certificate is valid for $days_remaining more days.${NC}" | tee -a "$LOG_FILE"
  fi
  
  echo "----------------------------------------" | tee -a "$LOG_FILE"
done

echo -e "${GREEN}SSL certificate check completed.${NC}" | tee -a "$LOG_FILE"
