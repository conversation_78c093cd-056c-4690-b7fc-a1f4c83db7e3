#!/bin/bash

# Import existing Cloudflare DNS records into Terraform state
# This script handles the "already exists" error by importing existing resources

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Default to staging if no environment specified
ENVIRONMENT="${1:-staging}"
TERRAFORM_DIR="$PROJECT_ROOT/terraform/environments/$ENVIRONMENT"

# Load environment variables
if [[ -f "$PROJECT_ROOT/.env" ]]; then
    export $(grep -v '^#' "$PROJECT_ROOT/.env" | xargs)
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔧 Cloudflare DNS Import Script${NC}"
echo "=================================================="

# Check if terraform directory exists
if [[ ! -d "$TERRAFORM_DIR" ]]; then
    echo -e "${RED}❌ Terraform directory not found: $TERRAFORM_DIR${NC}"
    echo "Available environments:"
    ls -1 "$PROJECT_ROOT/terraform/environments/" 2>/dev/null || echo "No environments found"
    exit 1
fi

# Check if we're in the right directory
cd "$TERRAFORM_DIR"
echo -e "${BLUE}Using Terraform directory: ${YELLOW}$TERRAFORM_DIR${NC}"

# Get zone ID from environment
ZONE_ID="${CLOUDFLARE_ZONE_ID}"
ROOT_DOMAIN="${CDN_URL:-vedavivi.app}"

if [[ -z "$ZONE_ID" ]]; then
    echo -e "${RED}❌ CLOUDFLARE_ZONE_ID not set in environment${NC}"
    exit 1
fi

echo -e "${BLUE}Zone ID: ${YELLOW}$ZONE_ID${NC}"
echo -e "${BLUE}Environment: ${YELLOW}$ENVIRONMENT${NC}"
echo -e "${BLUE}Root Domain: ${YELLOW}$ROOT_DOMAIN${NC}"

# Function to get DNS record ID from Cloudflare API
get_record_id() {
    local record_name="$1"
    local record_type="$2"

    echo -e "${BLUE}📡 Fetching record ID for ${YELLOW}$record_name${NC} (${record_type})"

    # Use Cloudflare API to get record ID
    local response=$(curl -s -H "Authorization: Bearer ${CLOUDFLARE_API_TOKEN}" \
        -H "Content-Type: application/json" \
        "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/dns_records?name=$record_name&type=$record_type")

    local record_id=$(echo "$response" | jq -r '.result[0].id // empty')

    if [[ -n "$record_id" && "$record_id" != "null" ]]; then
        echo -e "${GREEN}✅ Found record ID: $record_id${NC}"
        echo "$record_id"
    else
        echo -e "${YELLOW}⚠️  Record not found: $record_name${NC}"
        echo ""
    fi
}

# Function to import a DNS record
import_dns_record() {
    local resource_name="$1"
    local record_name="$2"
    local record_type="$3"

    echo -e "\n${BLUE}🔄 Importing $resource_name${NC}"

    local record_id=$(get_record_id "$record_name" "$record_type")

    if [[ -n "$record_id" ]]; then
        echo -e "${BLUE}Importing ${YELLOW}$resource_name${NC} with ID ${YELLOW}$record_id${NC}"

        # Try to import the resource
        if terraform import "$resource_name" "$ZONE_ID/$record_id" 2>/dev/null; then
            echo -e "${GREEN}✅ Successfully imported $resource_name${NC}"
        else
            echo -e "${YELLOW}⚠️  Resource may already be imported or doesn't exist in state${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  Skipping import for $resource_name - record not found${NC}"
    fi
}

# Calculate record names based on environment
if [[ "$ENVIRONMENT" == "production" ]]; then
    WEB_RECORD="$ROOT_DOMAIN"
    API_RECORD="api.$ROOT_DOMAIN"
    WS_RECORD="ws.$ROOT_DOMAIN"
else
    WEB_RECORD="$ENVIRONMENT.$ROOT_DOMAIN"
    API_RECORD="api-$ENVIRONMENT.$ROOT_DOMAIN"
    WS_RECORD="ws-$ENVIRONMENT.$ROOT_DOMAIN"
fi

echo -e "\n${BLUE}📋 DNS Records to import:${NC}"
echo -e "  Web: ${YELLOW}$WEB_RECORD${NC}"
echo -e "  API: ${YELLOW}$API_RECORD${NC}"
echo -e "  WebSocket: ${YELLOW}$WS_RECORD${NC}"

# Import DNS records if they exist
echo -e "\n${BLUE}🚀 Starting import process...${NC}"

# Import web record
import_dns_record "module.cloudflare_cdn.cloudflare_record.web[0]" "$WEB_RECORD" "CNAME"

# Import API record
import_dns_record "module.cloudflare_cdn.cloudflare_record.api[0]" "$API_RECORD" "CNAME"

# Import WebSocket record (if websockets are enabled)
import_dns_record "module.cloudflare_cdn.cloudflare_record.websocket[0]" "$WS_RECORD" "CNAME"

echo -e "\n${GREEN}🎉 Import process completed!${NC}"
echo ""
echo -e "${BLUE}💡 Next Steps:${NC}"
echo "1. Set TF_VAR_create_dns_records=false in your environment"
echo "2. Run terraform plan to verify the import was successful"
echo "3. Run terraform apply to ensure state is consistent"
echo ""
echo -e "${BLUE}Usage Examples:${NC}"
echo "  ./scripts/import-cloudflare-dns.sh staging"
echo "  ./scripts/import-cloudflare-dns.sh production"
echo ""
echo -e "${YELLOW}⚠️  Note: If you want to manage these records with Terraform, set TF_VAR_create_dns_records=true${NC}"
