#!/bin/bash
# filepath: /Users/<USER>/dev/blkops-collective/a2a-platform/scripts/check-security-headers.sh
#
# Script to check security headers compliance for HTTPS endpoints
# This script validates that all required security headers are present and correctly configured

set -e

# Define colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Get domain from command line or use default
DOMAIN=${1:-"localhost:8000"}
ENDPOINT=${2:-"/api/health"}

echo -e "${YELLOW}Checking Security Headers for ${DOMAIN}${NC}"
echo "==============================================="

# Required security headers and their expected values
declare -A REQUIRED_HEADERS
REQUIRED_HEADERS["strict-transport-security"]="max-age=31536000; includeSubDomains; preload"
REQUIRED_HEADERS["content-security-policy"]="connect-src 'self' https: wss:"
REQUIRED_HEADERS["x-content-type-options"]="nosniff"
REQUIRED_HEADERS["x-frame-options"]="DENY"
REQUIRED_HEADERS["referrer-policy"]="strict-origin-when-cross-origin"
REQUIRED_HEADERS["x-xss-protection"]="1; mode=block"

# Get headers using curl
echo -e "${YELLOW}Fetching headers from https://$DOMAIN$ENDPOINT...${NC}"
HEADER_OUTPUT=$(curl -sI -X GET https://$DOMAIN$ENDPOINT)

if [ $? -ne 0 ]; then
  echo -e "${RED}Error: Failed to fetch headers${NC}"
  exit 1
fi

echo "$HEADER_OUTPUT"
echo "==============================================="

# Check each required header
ALL_PASSED=true
HEADER_RESULTS=()

for header in "${!REQUIRED_HEADERS[@]}"; do
  expected_value=${REQUIRED_HEADERS[$header]}
  
  # Extract header value from curl output (case-insensitive match)
  header_value=$(echo "$HEADER_OUTPUT" | grep -i "^$header:" | sed "s/^[^:]*: //i" | tr -d '\r')
  
  if [ -z "$header_value" ]; then
    HEADER_RESULTS+=("${RED}❌ $header: Missing${NC}")
    ALL_PASSED=false
  elif [[ "$header" == "content-security-policy" ]]; then
    # For CSP, just check if the expected parts are included
    if [[ "$header_value" == *"connect-src 'self' https: wss:"* ]] || [[ "$header_value" == *"connect-src 'self' wss: https:"* ]]; then
      HEADER_RESULTS+=("${GREEN}✅ $header: Contains required directives${NC}")
    else
      HEADER_RESULTS+=("${RED}❌ $header: Does not contain required directives${NC}")
      HEADER_RESULTS+=("   Expected to contain: $expected_value")
      HEADER_RESULTS+=("   Actual: $header_value")
      ALL_PASSED=false
    fi
  else
    # For other headers, check exact match
    if [[ "$header_value" == "$expected_value" ]]; then
      HEADER_RESULTS+=("${GREEN}✅ $header: Correct${NC}")
    else
      HEADER_RESULTS+=("${RED}❌ $header: Incorrect${NC}")
      HEADER_RESULTS+=("   Expected: $expected_value")
      HEADER_RESULTS+=("   Actual: $header_value")
      ALL_PASSED=false
    fi
  fi
done

# Display results
echo -e "${YELLOW}Security Header Validation Results:${NC}"
for result in "${HEADER_RESULTS[@]}"; do
  echo -e "$result"
done

echo "==============================================="

# Check for HTTP to HTTPS redirect
echo -e "${YELLOW}Checking HTTP to HTTPS redirect...${NC}"
REDIRECT_OUTPUT=$(curl -sI -X GET http://$DOMAIN$ENDPOINT)
REDIRECT_STATUS=$(echo "$REDIRECT_OUTPUT" | grep "^HTTP" | awk '{print $2}')
REDIRECT_LOCATION=$(echo "$REDIRECT_OUTPUT" | grep -i "^location:" | sed "s/^[^:]*: //i" | tr -d '\r')

if [[ "$REDIRECT_STATUS" == "301" ]] && [[ "$REDIRECT_LOCATION" == https://* ]]; then
  echo -e "${GREEN}✅ HTTP to HTTPS redirect: Correct (301 to $REDIRECT_LOCATION)${NC}"
else
  echo -e "${RED}❌ HTTP to HTTPS redirect: Incorrect${NC}"
  echo "   Expected: 301 redirect to HTTPS URL"
  echo "   Actual status: $REDIRECT_STATUS"
  echo "   Actual location: $REDIRECT_LOCATION"
  ALL_PASSED=false
fi

echo "==============================================="

# Overall result
if [ "$ALL_PASSED" = true ]; then
  echo -e "${GREEN}✅ OVERALL: All security headers are correctly configured${NC}"
else
  echo -e "${RED}❌ OVERALL: Some security headers are missing or incorrectly configured${NC}"
fi
