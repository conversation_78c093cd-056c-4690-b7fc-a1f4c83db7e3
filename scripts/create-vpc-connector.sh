#!/bin/zsh

# Script to create a Serverless VPC Access connector

# Configuration
PROJECT_ID="clean-algebra-459903-b1"
REGION="us-central1"
CONNECTOR_NAME="a2a-staging-vpc-connector"
NETWORK_NAME="a2a-staging-network"

# Specify an unreserved /28 IP range. This is REQUIRED.
# Ensure this range is not used by any subnet in your VPC.
IP_RANGE="********/28" # Updated IP Range
MAX_INSTANCES="3" # Added Max Instances

echo "Attempting to create Serverless VPC Access connector..."
echo "Project: $PROJECT_ID"
echo "Region: $REGION"
echo "Connector Name: $CONNECTOR_NAME"
echo "Network: $NETWORK_NAME"
echo "IP Range: $IP_RANGE"
echo "Max Instances: $MAX_INSTANCES"

if [ -z "$IP_RANGE" ]; then
  echo -e "\nERROR: IP_RANGE is not set in the script. Please configure it."
  exit 1
fi

COMMAND="gcloud compute networks vpc-access connectors create \"$CONNECTOR_NAME\" \\
    --project=\"$PROJECT_ID\" \\
    --region=\"$REGION\" \\
    --network=\"$NETWORK_NAME\" \\
    --range=\"$IP_RANGE\" \\
    --max-instances=\"$MAX_INSTANCES\"" # Added max-instances flag

echo "\nExecuting command:"
echo "$COMMAND\n"

bash -c "$COMMAND"

if [ $? -eq 0 ]; then
  echo "\nSuccessfully initiated creation of connector '$CONNECTOR_NAME'."
  echo "It may take a few minutes for the connector to be ready."
  echo "You can check its status with:"
  echo "gcloud compute networks vpc-access connectors describe \"$CONNECTOR_NAME\" --project=\"$PROJECT_ID\" --region=\"$REGION\""
else
  echo "\nFailed to create connector '$CONNECTOR_NAME'."
fi
