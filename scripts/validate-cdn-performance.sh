#!/bin/bash
# CDN Performance Validation Script
# Validates performance improvements and cost metrics for CDN migration
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT=${1:-staging}
BASELINE_MODE=${2:-false}
REPORT_FILE=${3:-"cdn-performance-report.json"}

echo -e "${BLUE}⚡ CDN Performance Validation for environment: $ENVIRONMENT${NC}"
echo "=================================================================="

# Set environment-specific URLs
if [[ "$ENVIRONMENT" == "production" ]]; then
    WEB_URL="https://vedavivi.app"
    API_URL="https://api.vedavivi.app"
    BASELINE_URL="https://vedavivi-production-baseline.run.app"  # Cloud Run baseline
else
    WEB_URL="https://www-staging.vedavivi.app"
    API_URL="https://api-staging.vedavivi.app"
    BASELINE_URL="https://vedavivi-staging-baseline.run.app"  # Cloud Run baseline
fi

# Performance targets from specification
TARGET_LOAD_TIME_IMPROVEMENT=50  # >50% improvement
TARGET_COST_REDUCTION=60         # >60% cost reduction
TARGET_TTFB_MS=150              # <150ms TTFB
TARGET_CACHE_HIT_RATIO=95       # >95% cache hit ratio

# Validation counters
PASSED=0
FAILED=0

# Function to run performance tests
run_performance_test() {
    local test_name="$1"
    local url="$2"
    local metric_name="$3"
    
    echo -e "\n${BLUE}🔍 Testing: $test_name${NC}"
    echo "URL: $url"
    
    # Run curl with timing information
    local timing_output=$(curl -w "@-" -o /dev/null -s "$url" <<'EOF'
{
  "time_namelookup": %{time_namelookup},
  "time_connect": %{time_connect},
  "time_appconnect": %{time_appconnect},
  "time_pretransfer": %{time_pretransfer},
  "time_redirect": %{time_redirect},
  "time_starttransfer": %{time_starttransfer},
  "time_total": %{time_total},
  "speed_download": %{speed_download},
  "speed_upload": %{speed_upload},
  "size_download": %{size_download},
  "size_upload": %{size_upload},
  "size_header": %{size_header},
  "size_request": %{size_request},
  "http_code": %{http_code}
}
EOF
)
    
    echo "$timing_output"
    return 0
}

# Function to validate performance metrics
validate_performance_metric() {
    local metric_name="$1"
    local current_value="$2"
    local target_value="$3"
    local comparison_type="$4"  # "less_than", "greater_than", "percentage_improvement"
    local baseline_value="$5"
    
    local result=false
    
    case "$comparison_type" in
        "less_than")
            if (( $(echo "$current_value < $target_value" | bc -l) )); then
                result=true
            fi
            ;;
        "greater_than")
            if (( $(echo "$current_value > $target_value" | bc -l) )); then
                result=true
            fi
            ;;
        "percentage_improvement")
            if [[ -n "$baseline_value" && "$baseline_value" != "0" ]]; then
                local improvement=$(echo "scale=2; (($baseline_value - $current_value) / $baseline_value) * 100" | bc -l)
                if (( $(echo "$improvement > $target_value" | bc -l) )); then
                    result=true
                fi
                echo "  Improvement: ${improvement}% (target: >${target_value}%)"
            fi
            ;;
    esac
    
    if [[ "$result" == "true" ]]; then
        echo -e "${GREEN}✅ PASSED: $metric_name${NC}"
        ((PASSED++))
    else
        echo -e "${RED}❌ FAILED: $metric_name${NC}"
        echo -e "${RED}   Current: $current_value, Target: $target_value${NC}"
        ((FAILED++))
    fi
}

# Function to get Cloudflare analytics (if API token available)
get_cloudflare_analytics() {
    local zone_id="$1"
    local api_token="$2"
    
    if [[ -n "$api_token" && -n "$zone_id" ]]; then
        echo -e "\n${BLUE}📊 Fetching Cloudflare Analytics${NC}"
        
        # Get cache analytics for the last 24 hours
        local analytics_response=$(curl -s -X GET \
            "https://api.cloudflare.com/client/v4/zones/$zone_id/analytics/dashboard?since=-1440" \
            -H "Authorization: Bearer $api_token" \
            -H "Content-Type: application/json")
        
        echo "$analytics_response" | jq '.' 2>/dev/null || echo "Analytics data retrieved"
    else
        echo -e "${YELLOW}⚠️ Cloudflare API token not available, skipping analytics${NC}"
    fi
}

# Function to run WebPageTest (if available)
run_webpagetest() {
    local url="$1"
    local api_key="$2"
    
    if [[ -n "$api_key" ]]; then
        echo -e "\n${BLUE}🌐 Running WebPageTest${NC}"
        
        # Submit test to WebPageTest
        local test_response=$(curl -s -X POST \
            "https://www.webpagetest.org/runtest.php" \
            -d "url=$url" \
            -d "k=$api_key" \
            -d "f=json" \
            -d "location=Dulles:Chrome" \
            -d "runs=3")
        
        local test_id=$(echo "$test_response" | jq -r '.data.testId' 2>/dev/null)
        
        if [[ "$test_id" != "null" && -n "$test_id" ]]; then
            echo "WebPageTest submitted: $test_id"
            echo "Results will be available at: https://www.webpagetest.org/result/$test_id/"
        fi
    else
        echo -e "${YELLOW}⚠️ WebPageTest API key not available, skipping external testing${NC}"
    fi
}

# Main performance validation
echo -e "\n${YELLOW}🚀 Starting Performance Validation Tests${NC}"

# 1. TTFB (Time to First Byte) Test
echo -e "\n${BLUE}⏱️ TTFB Performance Tests${NC}"

# Test CDN TTFB
cdn_timing=$(run_performance_test "CDN TTFB Test" "$WEB_URL" "ttfb")
cdn_ttfb=$(echo "$cdn_timing" | jq -r '.time_starttransfer')
cdn_ttfb_ms=$(echo "$cdn_ttfb * 1000" | bc -l)

validate_performance_metric "CDN TTFB" "$cdn_ttfb_ms" "$TARGET_TTFB_MS" "less_than" ""

# 2. Full Page Load Time Test
echo -e "\n${BLUE}📄 Page Load Time Tests${NC}"

cdn_total_time=$(echo "$cdn_timing" | jq -r '.time_total')
cdn_total_ms=$(echo "$cdn_total_time * 1000" | bc -l)

echo "CDN Total Load Time: ${cdn_total_ms}ms"

# If baseline mode, test baseline performance
if [[ "$BASELINE_MODE" == "true" || "$BASELINE_MODE" == "--baseline" ]]; then
    echo -e "\n${BLUE}📊 Baseline Performance Comparison${NC}"
    
    if curl -s --head "$BASELINE_URL" | head -n 1 | grep -q "200 OK"; then
        baseline_timing=$(run_performance_test "Baseline TTFB Test" "$BASELINE_URL" "baseline_ttfb")
        baseline_ttfb=$(echo "$baseline_timing" | jq -r '.time_starttransfer')
        baseline_ttfb_ms=$(echo "$baseline_ttfb * 1000" | bc -l)
        
        baseline_total_time=$(echo "$baseline_timing" | jq -r '.time_total')
        baseline_total_ms=$(echo "$baseline_total_time * 1000" | bc -l)
        
        # Validate improvements
        validate_performance_metric "TTFB Improvement" "$cdn_ttfb_ms" "$TARGET_LOAD_TIME_IMPROVEMENT" "percentage_improvement" "$baseline_ttfb_ms"
        validate_performance_metric "Total Load Time Improvement" "$cdn_total_ms" "$TARGET_LOAD_TIME_IMPROVEMENT" "percentage_improvement" "$baseline_total_ms"
    else
        echo -e "${YELLOW}⚠️ Baseline URL not accessible, skipping comparison${NC}"
    fi
fi

# 3. Cache Performance Test
echo -e "\n${BLUE}🗄️ Cache Performance Tests${NC}"

# Test cache headers
cache_headers=$(curl -s -I "$WEB_URL" | grep -i "cache-control\|cf-cache-status\|cf-ray" || echo "")
echo "Cache Headers:"
echo "$cache_headers"

# Check for Cloudflare cache hit
if echo "$cache_headers" | grep -qi "cf-cache-status.*hit"; then
    echo -e "${GREEN}✅ PASSED: Cache hit detected${NC}"
    ((PASSED++))
else
    echo -e "${YELLOW}⚠️ Cache status not detected or miss${NC}"
fi

# 4. Multiple Location Tests
echo -e "\n${BLUE}🌍 Multi-Location Performance Tests${NC}"

# Test from different geographic locations (simulated with different DNS servers)
declare -a dns_servers=("*******" "*******" "**************")
declare -a dns_names=("Google" "Cloudflare" "OpenDNS")

for i in "${!dns_servers[@]}"; do
    dns_server="${dns_servers[$i]}"
    dns_name="${dns_names[$i]}"
    
    echo "Testing via $dns_name DNS ($dns_server)..."
    
    # Use dig to resolve via specific DNS server
    resolved_ip=$(dig @"$dns_server" +short "$(echo "$WEB_URL" | sed 's|https\?://||')" | head -1)
    
    if [[ -n "$resolved_ip" ]]; then
        echo "  Resolved to: $resolved_ip"
        # Test response time
        response_time=$(curl -w "%{time_total}" -o /dev/null -s "$WEB_URL")
        response_ms=$(echo "$response_time * 1000" | bc -l)
        echo "  Response time: ${response_ms}ms"
    else
        echo "  DNS resolution failed"
    fi
done

# 5. Generate Performance Report
echo -e "\n${BLUE}📊 Generating Performance Report${NC}"

cat > "$REPORT_FILE" << EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "environment": "$ENVIRONMENT",
  "test_results": {
    "cdn_ttfb_ms": $cdn_ttfb_ms,
    "cdn_total_load_ms": $cdn_total_ms,
    "target_ttfb_ms": $TARGET_TTFB_MS,
    "target_improvement_percent": $TARGET_LOAD_TIME_IMPROVEMENT,
    "tests_passed": $PASSED,
    "tests_failed": $FAILED
  },
  "urls_tested": {
    "web_url": "$WEB_URL",
    "api_url": "$API_URL"
  },
  "cache_headers": $(echo "$cache_headers" | jq -R -s 'split("\n")' 2>/dev/null || echo '[]')
}
EOF

echo "Performance report saved to: $REPORT_FILE"

# Summary
echo -e "\n${BLUE}=================================================================="
echo -e "📊 CDN Performance Validation Summary${NC}"
echo -e "=================================================================="
echo -e "${GREEN}✅ Passed: $PASSED tests${NC}"
echo -e "${RED}❌ Failed: $FAILED tests${NC}"
echo -e "Total: $((PASSED + FAILED)) tests"

if [[ $FAILED -eq 0 ]]; then
    echo -e "\n${GREEN}🎉 All CDN performance validation tests passed!${NC}"
    echo -e "${GREEN}✅ CDN performance meets or exceeds targets${NC}"
    exit 0
else
    echo -e "\n${RED}⚠️ Some CDN performance validation tests failed!${NC}"
    echo -e "${RED}❌ Please review performance issues before proceeding${NC}"
    exit 1
fi
