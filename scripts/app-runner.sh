#!/bin/bash
#
# A2A Platform - Application Runner
#
# This script manages Docker containers for the A2A Platform.
# It uses docker compose to set up, manage, stop, and check the services defined in docker-compose.yml.
#
# Usage:
#   ./scripts/app-runner.sh [OPTIONS] [COMMAND]
#
# Commands:
#   start (default)  Start the services
#   stop             Stop the services
#   status           Show status of running services
#
# Options:
#   --build         Force rebuild of all containers (start only)
#   --backend-only  Target only the backend services (PostgreSQL, Redis, backend)
#   --frontend-only Target only the frontend service
#   --clean         Remove existing containers before starting (start only)
#   --dev-frontend  Use development mode for frontend with hot reloading (start only)
#   --help          Display this help message
#
# Examples:
#   ./scripts/app-runner.sh              # Start all services
#   ./scripts/app-runner.sh start        # Same as above
#   ./scripts/app-runner.sh --build      # Rebuild and start all services
#   ./scripts/app-runner.sh --clean      # Clean and start all services
#   ./scripts/app-runner.sh stop         # Stop all services
#   ./scripts/app-runner.sh status       # Show status of all services

set -e

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"

# Default values
FORCE_BUILD=false
BACKEND_ONLY=false
FRONTEND_ONLY=false
CLEAN=false
COMMAND="start"
DEV_FRONTEND=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    --build)
      FORCE_BUILD=true
      shift
      ;;
    --backend-only)
      BACKEND_ONLY=true
      shift
      ;;
    --frontend-only)
      FRONTEND_ONLY=true
      shift
      ;;
    --clean)
      CLEAN=true
      shift
      ;;
    --dev-frontend)
      DEV_FRONTEND=true
      shift
      ;;
    --help)
      grep '^#' "$0" | grep -v '#!/bin/bash' | sed 's/^# \?//'
      exit 0
      ;;
    start|stop|status)
      COMMAND="$1"
      shift
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

# Validate options
if [[ "${BACKEND_ONLY}" == true && "${FRONTEND_ONLY}" == true ]]; then
  echo "Error: Cannot specify both --backend-only and --frontend-only"
  exit 1
fi

# Function to wait for a service to be ready
wait_for_service() {
  local host=$1
  local port=$2
  local service_name=$3
  local max_attempts=$4
  local attempt=1

  echo "Waiting for $service_name to be ready..."
  while ! nc -z $host $port >/dev/null 2>&1; do
    if [[ $attempt -gt $max_attempts ]]; then
      echo "Error: $service_name did not become ready in time"
      return 1
    fi
    echo "Attempt $attempt/$max_attempts: $service_name is not ready yet..."
    sleep 2
    ((attempt++))
  done
  echo "$service_name is ready!"
  return 0
}

# Status command - show running containers
if [[ "${COMMAND}" == "status" ]]; then
  echo "Checking Docker service status..."
  echo "=================================="

  if [[ "${FRONTEND_ONLY}" == true ]]; then
    docker compose ps frontend
  elif [[ "${BACKEND_ONLY}" == true ]]; then
    docker compose ps backend db redis
  else
    docker compose ps
  fi

  exit 0
fi

# Stop command - stop running containers
if [[ "${COMMAND}" == "stop" ]]; then
  echo "Stopping Docker services..."

  if [[ "${FRONTEND_ONLY}" == true ]]; then
    docker compose stop frontend
    docker compose rm -f frontend
  elif [[ "${BACKEND_ONLY}" == true ]]; then
    docker compose stop backend db redis
    docker compose rm -f backend db redis
  else
    docker compose down
  fi

  echo "Services stopped successfully."
  exit 0
fi

# Start command - remaining part of the script handles starting services

# Clean up existing containers if requested
if [[ "${CLEAN}" == true ]]; then
  echo "Cleaning up existing containers..."

  if [[ "${FRONTEND_ONLY}" == true ]]; then
    docker compose rm -sf frontend
  elif [[ "${BACKEND_ONLY}" == true ]]; then
    docker compose rm -sf backend db redis
  else
    docker compose down -v
  fi
fi

# Build services if requested
if [[ "${FORCE_BUILD}" == true ]]; then
  echo "Building Docker images..."

  if [[ "${FRONTEND_ONLY}" == true ]]; then
    if [[ "${DEV_FRONTEND}" == true ]]; then
      docker compose -f docker-compose.yml -f docker-compose.dev.yml build frontend
    else
      docker compose build frontend
    fi
  elif [[ "${BACKEND_ONLY}" == true ]]; then
    docker compose build backend
  else
    if [[ "${DEV_FRONTEND}" == true ]]; then
      docker compose -f docker-compose.yml -f docker-compose.dev.yml build
    else
      docker compose build
    fi
  fi
fi

# Start services
echo "Starting Docker services..."

if [[ "${FRONTEND_ONLY}" == true ]]; then
  if [[ "${DEV_FRONTEND}" == true ]]; then
    docker compose -f docker-compose.yml -f docker-compose.dev.yml up -d frontend
    wait_for_service localhost 5173 "Frontend (Dev)" 20
  else
    docker compose up -d frontend
    wait_for_service localhost 5173 "Frontend" 20
  fi
elif [[ "${BACKEND_ONLY}" == true ]]; then
  docker compose up -d backend db redis
  wait_for_service localhost 5432 "PostgreSQL" 30
  wait_for_service localhost 6379 "Redis" 15
  wait_for_service localhost 8000 "Backend API" 20
else
  if [[ "${DEV_FRONTEND}" == true ]]; then
    docker compose -f docker-compose.yml -f docker-compose.dev.yml up -d
    wait_for_service localhost 5432 "PostgreSQL" 30
    wait_for_service localhost 6379 "Redis" 15
    wait_for_service localhost 8000 "Backend API" 20
    wait_for_service localhost 5173 "Frontend (Dev)" 20
  else
    docker compose up -d
    wait_for_service localhost 5432 "PostgreSQL" 30
    wait_for_service localhost 6379 "Redis" 15
    wait_for_service localhost 8000 "Backend API" 20
    wait_for_service localhost 5173 "Frontend" 20
  fi
fi

# Display access URLs
echo ""
echo "A2A Platform services are ready!"
echo "=================================="
if ! [[ "${FRONTEND_ONLY}" == true ]]; then
  echo "Backend API: https://localhost:8000/docs"
  echo "GraphQL Playground: https://localhost:8000/graphql"
fi
if ! [[ "${BACKEND_ONLY}" == true ]]; then
  if [[ "${DEV_FRONTEND}" == true ]]; then
    echo "Frontend (Dev): https://localhost:5173"
  else
    echo "Frontend: https://localhost:5173"
  fi
fi
echo ""

exit 0
