#!/bin/bash

# Setup GitHub Repository Variables for A2A Platform
# This script helps configure the required GitHub repository variables for CORS and trusted hosts

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

# Check if GitHub CLI is installed
if ! command -v gh &> /dev/null; then
    print_error "GitHub CLI (gh) is not installed. Please install it first:"
    echo "  - macOS: brew install gh"
    echo "  - Ubuntu: sudo apt install gh"
    echo "  - Windows: winget install GitHub.cli"
    echo ""
    echo "Then authenticate with: gh auth login"
    exit 1
fi

# Check if user is authenticated
if ! gh auth status &> /dev/null; then
    print_error "Not authenticated with GitHub CLI. Please run: gh auth login"
    exit 1
fi

print_header "GitHub Repository Variables Setup for A2A Platform"

print_info "This script will set up the required GitHub repository variables for CORS and trusted hosts configuration."
print_info "These variables control which domains can access your API in different environments."

echo ""
read -p "Do you want to continue? (y/N): " -n 1 -r
echo ""
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_info "Setup cancelled."
    exit 0
fi

# Default values
DEFAULT_STAGING_CORS="https://www-staging.vedavivi.app,https://staging.vedavivi.app"
DEFAULT_STAGING_HOSTS="api-staging.vedavivi.app,www-staging.vedavivi.app,staging.vedavivi.app"
DEFAULT_PRODUCTION_CORS="https://vedavivi.app,https://www.vedavivi.app"
DEFAULT_PRODUCTION_HOSTS="api.vedavivi.app,vedavivi.app,www.vedavivi.app"

print_header "Staging Environment Configuration"

print_info "Setting up CORS origins for staging..."
echo "Default: $DEFAULT_STAGING_CORS"
read -p "Enter staging CORS origins (or press Enter for default): " STAGING_CORS
STAGING_CORS=${STAGING_CORS:-$DEFAULT_STAGING_CORS}

print_info "Setting up trusted hosts for staging..."
echo "Default: $DEFAULT_STAGING_HOSTS"
read -p "Enter staging trusted hosts (or press Enter for default): " STAGING_HOSTS
STAGING_HOSTS=${STAGING_HOSTS:-$DEFAULT_STAGING_HOSTS}

print_header "Production Environment Configuration"

print_info "Setting up CORS origins for production..."
echo "Default: $DEFAULT_PRODUCTION_CORS"
read -p "Enter production CORS origins (or press Enter for default): " PRODUCTION_CORS
PRODUCTION_CORS=${PRODUCTION_CORS:-$DEFAULT_PRODUCTION_CORS}

print_info "Setting up trusted hosts for production..."
echo "Default: $DEFAULT_PRODUCTION_HOSTS"
read -p "Enter production trusted hosts (or press Enter for default): " PRODUCTION_HOSTS
PRODUCTION_HOSTS=${PRODUCTION_HOSTS:-$DEFAULT_PRODUCTION_HOSTS}

print_header "Configuration Summary"
echo "Staging CORS Origins: $STAGING_CORS"
echo "Staging Trusted Hosts: $STAGING_HOSTS"
echo "Production CORS Origins: $PRODUCTION_CORS"
echo "Production Trusted Hosts: $PRODUCTION_HOSTS"

echo ""
read -p "Do you want to apply these settings? (y/N): " -n 1 -r
echo ""
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_info "Setup cancelled."
    exit 0
fi

print_header "Applying Configuration"

# Set the variables for staging environment
print_info "Setting staging environment variables..."
gh variable set CORS_ORIGINS --body "$STAGING_CORS" --env staging
print_success "CORS_ORIGINS set for staging environment"

gh variable set TRUSTED_HOSTS --body "$STAGING_HOSTS" --env staging
print_success "TRUSTED_HOSTS set for staging environment"

# Set the variables for production environment
print_info "Setting production environment variables..."
gh variable set CORS_ORIGINS --body "$PRODUCTION_CORS" --env production
print_success "CORS_ORIGINS set for production environment"

gh variable set TRUSTED_HOSTS --body "$PRODUCTION_HOSTS" --env production
print_success "TRUSTED_HOSTS set for production environment"

print_header "Setup Complete!"
print_success "All GitHub environment variables have been configured successfully."
print_info "You can now deploy your API and the CORS configuration will be applied automatically."
print_info "To view your variables, run: gh variable list --env staging or gh variable list --env production"

echo ""
print_warning "Next steps:"
echo "1. Deploy your API using the GitHub Actions workflow"
echo "2. Deploy your frontend to test the CORS configuration"
echo "3. Monitor the deployment logs for any CORS-related issues"

echo ""
print_info "For more information, see: docs/deployment/github-environment-variables.md"
