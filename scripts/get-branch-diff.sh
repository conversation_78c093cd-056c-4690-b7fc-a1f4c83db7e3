#!/bin/bash

# Script to get the full diff between current branch and main branch
# This can be used to generate summaries or narratives of changes

set -e

# Define colors for better output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Print script banner
echo -e "${BLUE}${BOLD}==============================================${NC}"
echo -e "${GREEN}${BOLD}    Branch Diff Summary Generator    ${NC}"
echo -e "${BLUE}${BOLD}==============================================${NC}\n"

# Default values
target_branch="main"
current_branch=$(git branch --show-current)
include_stats=true
colorized=true
include_files_only=false

# Function to print usage instructions
usage() {
  echo -e "${BOLD}Usage:${NC} $0 [options]"
  echo
  echo "Options:"
  echo "  -b, --branch <branch>     Target branch to compare against (default: main)"
  echo "  --no-stats                Don't include file statistics"
  echo "  --no-color                Don't colorize output"
  echo "  --files-only              Only list changed files, no diff content"
  echo "  -h, --help                Show this help message"
  echo
  exit 1
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    -b|--branch)
      target_branch="$2"
      shift 2
      ;;
    --no-stats)
      include_stats=false
      shift
      ;;
    --no-color)
      colorized=false
      shift
      ;;
    --files-only)
      include_files_only=true
      shift
      ;;
    -h|--help)
      usage
      ;;
    *)
      echo -e "${RED}Error: Unknown option $1${NC}"
      usage
      ;;
  esac
done

# Ensure we're in a git repository
if ! git rev-parse --is-inside-work-tree > /dev/null 2>&1; then
  echo -e "${RED}Error: Not in a git repository${NC}"
  exit 1
fi

# Check if target branch exists
if ! git show-ref --verify --quiet refs/heads/"$target_branch" && \
   ! git show-ref --verify --quiet refs/remotes/origin/"$target_branch"; then
  echo -e "${RED}Error: Branch '$target_branch' does not exist locally or remotely${NC}"
  exit 1
fi

# Function to output markdown-formatted content
output_markdown() {
  echo "# Branch Diff Summary"
  echo "- Current branch: $current_branch"
  echo "- Target branch: $target_branch"
  echo "- Generated on: $(date)"
  echo

  if [ "$include_stats" = true ]; then
    echo "## Change Statistics"
    echo '```'
    git diff --stat "$target_branch"..."$current_branch"
    echo '```'
    echo
  fi

  echo "## Files Changed"
  echo '```'
  git diff --name-status "$target_branch"..."$current_branch"
  echo '```'
  echo

  if [ "$include_files_only" = false ]; then
    echo "## Full Diff"
    echo '```diff'
    git diff "$target_branch"..."$current_branch"
    echo '```'
  fi
}

# Function to output colorized content
output_colorized() {
  echo -e "${YELLOW}${BOLD}Branch Diff Summary${NC}"
  echo -e "${BLUE}Current branch:${NC} $current_branch"
  echo -e "${BLUE}Target branch:${NC} $target_branch"
  echo -e "${BLUE}Generated on:${NC} $(date)"
  echo

  if [ "$include_stats" = true ]; then
    echo -e "${YELLOW}${BOLD}Change Statistics:${NC}"
    git diff --stat --color "$target_branch"..."$current_branch"
    echo
  fi

  echo -e "${YELLOW}${BOLD}Files Changed:${NC}"
  git diff --name-status --color "$target_branch"..."$current_branch"
  echo

  if [ "$include_files_only" = false ]; then
    echo -e "${YELLOW}${BOLD}Full Diff:${NC}"
    git diff --color "$target_branch"..."$current_branch"
  fi
}

# Output based on colorization preference
if [ "$colorized" = true ] && [ -t 1 ]; then
  output_colorized
else
  output_markdown
fi

echo -e "\n${GREEN}${BOLD}Diff generation complete!${NC}"
echo -e "${BLUE}You can now use this output to generate a summary narrative.${NC}"
