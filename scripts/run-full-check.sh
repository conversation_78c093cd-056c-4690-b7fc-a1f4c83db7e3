#!/bin/bash
#
# Quick script to run precommit checks and diagnose deployment errors
#
# This combines the precommit-check.sh and diagnose-deploy-errors.sh scripts
# to provide a comprehensive check of the codebase and deployment workflows.
#

set -e

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
  local color=$1
  local message=$2
  echo -e "${color}${message}${NC}"
}

print_message "${BLUE}" "A2A Platform - Full Check and Diagnosis"
echo ""

# Make scripts executable
chmod +x "${SCRIPT_DIR}/precommit-check.sh"
chmod +x "${SCRIPT_DIR}/diagnose-deploy-errors.sh"
chmod +x "${SCRIPT_DIR}/run-act.sh"

# Step 1: Run precommit checks (skip tests for now to focus on deployment issues)
print_message "${YELLOW}" "Step 1: Running pre-commit checks..."
if "${SCRIPT_DIR}/precommit-check.sh" --skip-tests; then
  print_message "${GREEN}" "✓ Pre-commit checks passed"
else
  print_message "${RED}" "✗ Pre-commit checks failed"
  print_message "${YELLOW}" "Continuing with deployment diagnosis..."
fi

echo ""

# Step 2: Diagnose deployment errors
print_message "${YELLOW}" "Step 2: Diagnosing deployment workflow errors..."
if "${SCRIPT_DIR}/diagnose-deploy-errors.sh"; then
  print_message "${GREEN}" "✓ No deployment errors detected"
else
  print_message "${RED}" "✗ Deployment errors detected - see action plan above"
fi

echo ""
print_message "${BLUE}" "Check complete. Review the output above for any issues that need to be addressed."