#!/bin/bash
# <PERSON>ript to manage RQ workers for the A2A platform

set -e

# Default values
WORKER_COUNT=${RQ_WORKER_COUNT:-2}
QUEUES="default a2a-messages"
ACTION="start"

# Help message
function show_help {
    echo "Usage: $0 [OPTIONS]"
    echo "Manage RQ workers for the A2A platform"
    echo ""
    echo "Options:"
    echo "  -a, --action ACTION    Action to perform (start, stop, restart, status)"
    echo "  -w, --workers COUNT    Number of workers to start (default: $WORKER_COUNT)"
    echo "  -q, --queues QUEUES    Space-separated list of queues to listen to (default: $QUEUES)"
    echo "  -h, --help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --action start --workers 4 --queues 'default a2a-messages high-priority'"
    echo "  $0 --action stop"
    echo "  $0 --action status"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        -a|--action)
            ACTION="$2"
            shift
            shift
            ;;
        -w|--workers)
            WORKER_COUNT="$2"
            shift
            shift
            ;;
        -q|--queues)
            QUEUES="$2"
            shift
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate action
if [[ ! "$ACTION" =~ ^(start|stop|restart|status)$ ]]; then
    echo "Error: Invalid action '$ACTION'. Must be one of: start, stop, restart, status"
    exit 1
fi

# Function to start workers
function start_workers {
    echo "Starting $WORKER_COUNT RQ workers for queues: $QUEUES"
    
    # Check if we're in a Docker environment
    if [ -f /.dockerenv ] || [ -f /run/.containerenv ]; then
        # Inside Docker container
        python -m a2a_platform.workers.rq_worker --queues $QUEUES --workers $WORKER_COUNT
    else
        # Outside Docker container, use docker-compose
        docker compose exec -d backend python -m a2a_platform.workers.rq_worker --queues $QUEUES --workers $WORKER_COUNT
    fi
    
    echo "Workers started successfully"
}

# Function to stop workers
function stop_workers {
    echo "Stopping RQ workers"
    
    # Check if we're in a Docker environment
    if [ -f /.dockerenv ] || [ -f /run/.containerenv ]; then
        # Inside Docker container
        pkill -f "python -m a2a_platform.workers.rq_worker" || echo "No workers found to stop"
    else
        # Outside Docker container, use docker-compose
        docker compose exec backend pkill -f "python -m a2a_platform.workers.rq_worker" || echo "No workers found to stop"
    fi
    
    echo "Workers stopped successfully"
}

# Function to check worker status
function check_status {
    echo "Checking RQ worker status"
    
    # Check if we're in a Docker environment
    if [ -f /.dockerenv ] || [ -f /run/.containerenv ]; then
        # Inside Docker container
        ps aux | grep "[p]ython -m a2a_platform.workers.rq_worker" || echo "No workers running"
    else
        # Outside Docker container, use docker-compose
        docker compose exec backend ps aux | grep "[p]ython -m a2a_platform.workers.rq_worker" || echo "No workers running"
    fi
}

# Execute the requested action
case $ACTION in
    start)
        start_workers
        ;;
    stop)
        stop_workers
        ;;
    restart)
        stop_workers
        sleep 2
        start_workers
        ;;
    status)
        check_status
        ;;
esac

exit 0
