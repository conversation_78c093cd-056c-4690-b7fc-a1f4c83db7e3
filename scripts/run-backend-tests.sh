#!/bin/bash
#
# A2A Platform - Backend Tests Runner
#
# This script runs backend tests with proper environment setup.
# It supports running specific test categories (unit, integration, e2e).
# Docker is the default testing environment.
#
# Usage:
#   ./scripts/run-backend-tests.sh [OPTIONS] [TEST_PATHS...]
#
# Options:
#   --unit          Run only unit tests
#   --integration   Run only integration tests
#   --e2e           Run only end-to-end tests
#   --no-db         Run only database-free tests (marked with @pytest.mark.no_db)
#   --fast-db       Run only SQLite-based tests (marked with @pytest.mark.fast_db)
#   --exclude-no-db Exclude database-free tests (for PostgreSQL-only runs)
#   --exclude-fast-db Exclude SQLite tests (for PostgreSQL-only runs)
#   --performance   Include performance tests (excluded by default)
#   --coverage      Run tests with coverage report
#   --verbose       Run tests with verbose output
#   --setup         Perform dependency installation and database setup before running tests
#   --ci            Run tests in CI environment (non-Docker)
#   --help          Display this help message
#
# Test Paths:
#   Optional paths to specific test files or test functions to run.
#   Paths should be relative to the project root.
#   The script will automatically convert paths to be relative to the backend directory.
#
# Examples:
#   ./scripts/run-backend-tests.sh                # Run all tests in Docker (default)
#   ./scripts/run-backend-tests.sh --unit         # Run only unit tests in Docker
#   ./scripts/run-backend-tests.sh --coverage     # Run all tests with coverage in Docker
#   ./scripts/run-backend-tests.sh --setup        # Run all tests with full setup in Docker
#   ./scripts/run-backend-tests.sh --ci           # Run all tests in CI environment (non-Docker)
#   ./scripts/run-backend-tests.sh --ci --unit    # Run only unit tests in CI environment
#   ./scripts/run-backend-tests.sh tests/integration/test_graphql_auth_for_new_user.py  # Run specific test file
#   ./scripts/run-backend-tests.sh tests/unit/test_something.py::test_specific_method   # Run specific test method
#   ./scripts/run-backend-tests.sh tests/unit/test_file1.py tests/unit/test_file2.py    # Run multiple test files

set -e

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
BACKEND_DIR="${PROJECT_ROOT}/apps/backend"

# Default values
RUN_UNIT=false
RUN_INTEGRATION=false
RUN_E2E=false
RUN_NO_DB=false
RUN_FAST_DB=false
EXCLUDE_NO_DB=false
EXCLUDE_FAST_DB=false
RUN_PERFORMANCE=false
RUN_COVERAGE=false
VERBOSE=false
RUN_SETUP=false
RUN_CI=false
RUN_ALL=true
RUN_DOCKER=true  # Docker is now the default
TEST_PATHS=()    # Array to store test paths

# Check if user has provided custom markers
USER_PROVIDED_MARKERS=false
for arg in "$@"; do
  if [[ "$arg" == "-m" ]]; then
    USER_PROVIDED_MARKERS=true
    break
  fi
done

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    --unit)
      RUN_UNIT=true
      RUN_ALL=false
      shift
      ;;
    --integration)
      RUN_INTEGRATION=true
      RUN_ALL=false
      shift
      ;;
    --e2e)
      RUN_E2E=true
      RUN_ALL=false
      shift
      ;;
    --no-db)
      RUN_NO_DB=true
      RUN_ALL=false
      shift
      ;;
    --fast-db)
      RUN_FAST_DB=true
      RUN_ALL=false
      shift
      ;;
    --exclude-no-db)
      EXCLUDE_NO_DB=true
      shift
      ;;
    --exclude-fast-db)
      EXCLUDE_FAST_DB=true
      shift
      ;;
    --performance)
      RUN_PERFORMANCE=true
      shift
      ;;
    --coverage)
      RUN_COVERAGE=true
      shift
      ;;
    --verbose)
      VERBOSE=true
      shift
      ;;
    --setup)
      RUN_SETUP=true
      shift
      ;;
    --ci)
      RUN_CI=true
      RUN_DOCKER=false  # CI mode disables Docker
      shift
      ;;
    --help)
      grep '^#' "$0" | grep -v '#!/bin/bash' | sed 's/^# \?//'
      exit 0
      ;;
    *)
      # If argument doesn't start with --, assume it's a test path
      if [[ "$1" != --* ]]; then
        # Store the test path
        TEST_PATHS+=("$1")
        shift
      else
        echo "Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
      fi
      ;;
  esac
done

# Check if we're in the backend directory
if [[ ! -d "${BACKEND_DIR}" ]]; then
  echo "Error: Backend directory not found at ${BACKEND_DIR}"
  exit 1
fi

# Force CI mode for database optimization flags since Docker script doesn't support them
if [[ "${RUN_NO_DB}" == true ]] || [[ "${RUN_FAST_DB}" == true ]]; then
  echo "Running tests in CI mode (required for database optimization flags)..."
  RUN_DOCKER=false
  RUN_CI=true
fi

# If Docker mode is enabled (default), delegate to the Docker test script
if [[ "${RUN_DOCKER}" == true ]]; then
  echo "Running tests in Docker mode (default)..."

  # Build the Docker test command with the same options
  DOCKER_CMD="${SCRIPT_DIR}/run-docker-tests.sh"

  # Pass through the test type flags
  if [[ "${RUN_UNIT}" == true ]]; then
    DOCKER_CMD="${DOCKER_CMD} --unit"
  fi
  if [[ "${RUN_INTEGRATION}" == true ]]; then
    DOCKER_CMD="${DOCKER_CMD} --integration"
  fi
  if [[ "${RUN_E2E}" == true ]]; then
    DOCKER_CMD="${DOCKER_CMD} --e2e"
  fi
  if [[ "${RUN_NO_DB}" == true ]]; then
    DOCKER_CMD="${DOCKER_CMD} --no-db"
  fi
  if [[ "${RUN_FAST_DB}" == true ]]; then
    DOCKER_CMD="${DOCKER_CMD} --fast-db"
  fi
  if [[ "${EXCLUDE_NO_DB}" == true ]]; then
    DOCKER_CMD="${DOCKER_CMD} --exclude-no-db"
  fi
  if [[ "${EXCLUDE_FAST_DB}" == true ]]; then
    DOCKER_CMD="${DOCKER_CMD} --exclude-fast-db"
  fi
  if [[ "${RUN_PERFORMANCE}" == true ]]; then
    DOCKER_CMD="${DOCKER_CMD} --performance"
  fi

  # Pass through other flags
  if [[ "${RUN_COVERAGE}" == true ]]; then
    DOCKER_CMD="${DOCKER_CMD} --coverage"
  fi
  if [[ "${VERBOSE}" == true ]]; then
    DOCKER_CMD="${DOCKER_CMD} --verbose"
  fi
  if [[ "${RUN_SETUP}" == true ]]; then
    DOCKER_CMD="${DOCKER_CMD} --setup"
  fi

  # Pass test paths if provided
  if [[ ${#TEST_PATHS[@]} -gt 0 ]]; then
    for test_path in "${TEST_PATHS[@]}"; do
      DOCKER_CMD="${DOCKER_CMD} ${test_path}"
    done
  fi

  # Execute the Docker test script
  echo "Executing: ${DOCKER_CMD}"
  exec ${DOCKER_CMD}
  # The exec command replaces the current process, so no code after this will run
fi

# If we get here, we're running in CI mode (non-Docker)

# Setup the test database if --setup flag is provided
if [[ "${RUN_SETUP}" == true ]]; then
  echo "Setting up test database..."
  # Check if Docker container is running
  if docker compose ps | grep -q "db.*Up"; then
    echo "Docker database container is running."
  else
    echo "Starting Docker database container..."
    docker compose up -d db
    # Wait for the database to be ready
    echo "Waiting for database to be ready..."
    for i in {1..30}; do
      if docker exec db-container-name pg_isready -U postgres > /dev/null 2>&1; then
        echo "Database is ready."
        break
      fi
      echo "Database not ready yet. Retrying in 1 second..."
      sleep 1
    done
    if [[ $i -eq 30 ]]; then
      echo "Error: Database did not become ready in time."
      exit 1
    fi
  fi

  # Run the setup script with CI mode if needed
  if [[ "${RUN_CI}" == true ]]; then
    "${SCRIPT_DIR}/setup-test-db.sh" --ci || {
      echo "Warning: Database setup encountered errors, but we'll continue with tests."
    }
  else
    # Default is Docker mode
    "${SCRIPT_DIR}/setup-test-db.sh" || {
      echo "Warning: Database setup encountered errors, but we'll continue with tests."
    }
  fi
else
  echo "Skipping database setup (use --setup flag to enable)"
fi

# Change to backend directory
cd "${BACKEND_DIR}"
echo "Changed to directory: $(pwd)"

# Check for virtual environment
VENV_ACTIVATED=false
if [[ -d "venv" ]]; then
  echo "Activating virtual environment..."
  source venv/bin/activate
  VENV_ACTIVATED=true
elif [[ -n "${VIRTUAL_ENV}" ]]; then
  echo "Using active virtual environment: ${VIRTUAL_ENV}"
  VENV_ACTIVATED=true
else
  echo "Warning: No virtual environment found or activated."
  echo "Tests will run using system Python."
fi

# Install required dependencies if --setup flag is provided
if [[ "${RUN_SETUP}" == true ]]; then
  echo "Installing required dependencies..."
  pip install -e ".[dev]" || {
    echo "Warning: Failed to install dependencies with pip install -e .[dev]"
    echo "Trying to install dependencies from pyproject.toml..."
    pip install pytest pytest-asyncio pytest-cov svix
  }
else
  echo "Skipping dependency installation (use --setup flag to enable)"
fi

# Check if pytest is installed
if ! command -v pytest &> /dev/null; then
  if [[ "${RUN_SETUP}" == true ]]; then
    echo "Error: pytest is not installed despite running setup."
    echo "Please check your Python environment and try again."
    exit 1
  else
    echo "Error: pytest is not installed."
    echo "Please run with --setup flag or install manually with: pip install pytest pytest-asyncio pytest-cov"
    exit 1
  fi
fi

# Set up environment variables for tests
export PYTHONPATH="${BACKEND_DIR}/src:${PYTHONPATH}"

# Load environment variables from .env.test
ENV_TEST_FILE="${PROJECT_ROOT}/.env.test"
if [[ -f "${ENV_TEST_FILE}" ]]; then
  echo "Loading environment variables from ${ENV_TEST_FILE}"
  export $(grep -v '^#' "${ENV_TEST_FILE}" | xargs)

else
  echo "Warning: .env.test file not found at ${ENV_TEST_FILE}"
fi

# Set environment variables when running in CI mode
if [[ "${RUN_CI}" == true ]]; then
  # Always use 'localhost' as the hostname in CI mode
  export DATABASE_URL="postgresql://postgres:postgres@localhost:5432/a2a_platform_test"
  export DATABASE_ASYNC_URL="postgresql+asyncpg://postgres:postgres@localhost:5432/a2a_platform_test"
  echo "CI mode detected. Setting database URLs to use localhost"

  # Set CI environment variable to true
  export CI=true
  echo "Setting CI=true for environment detection"
fi

# Build test command
TEST_CMD="pytest"

# Add verbosity if requested
if [[ "${VERBOSE}" == true ]]; then
  TEST_CMD="${TEST_CMD} -v"
fi

# Add coverage if requested
if [[ "${RUN_COVERAGE}" == true ]]; then
  TEST_CMD="${TEST_CMD} --cov=src/a2a_platform --cov-report=term --cov-report=html --cov-report=xml"
fi

# Determine which tests to run
if [[ ${#TEST_PATHS[@]} -gt 0 ]]; then
  echo "Running specific tests..."
  for test_path in "${TEST_PATHS[@]}"; do
    # Convert path from project-root-relative to backend-relative
    # Check if the path starts with apps/backend/
    if [[ "${test_path}" == apps/backend/* ]]; then
      # Remove apps/backend/ prefix
      backend_relative_path="${test_path#apps/backend/}"
    else
      # Assume it's already relative to backend directory
      backend_relative_path="${test_path}"
    fi
    TEST_CMD="${TEST_CMD} ${backend_relative_path}"
  done
  # Performance exclusion handled in database mode selection below
elif [[ "${RUN_ALL}" == true ]]; then
  echo "Running all tests..."
  # Performance exclusion handled in database mode selection below
elif [[ "${RUN_UNIT}" == true ]]; then
  TEST_CMD="${TEST_CMD} tests/unit/"
  echo "Running unit tests..."
  # Performance exclusion handled in database mode selection below
elif [[ "${RUN_INTEGRATION}" == true ]]; then
  TEST_CMD="${TEST_CMD} tests/integration/"
  echo "Running integration tests..."
  # Performance exclusion handled in database mode selection below
elif [[ "${RUN_E2E}" == true ]]; then
  TEST_CMD="${TEST_CMD} tests/e2e/"
  echo "Running end-to-end tests..."
  # Performance exclusion handled in database mode selection below
fi

# Smart database setup and mode selection
setup_test_environment() {
  local db_mode="$1"

  case "$db_mode" in
    "no_db")
      echo "ℹ️  Database-free mode: No database setup required"
      export TEST_DATABASE_URL="mock://localhost/testdb"
      ;;
    "fast_db")
      echo "ℹ️  SQLite mode: Setting up in-memory database"
      export TEST_DATABASE_URL="sqlite+aiosqlite:///:memory:"
      # Check if PostgreSQL is available for SQLite tests
      check_postgresql_availability_for_sqlite
      ;;
    "postgresql")
      echo "ℹ️  PostgreSQL mode: Using configured test database"
      setup_postgresql_environment
      ;;
    *)
      echo "❌ Unknown database mode: $db_mode"
      exit 1
      ;;
  esac
}

check_postgresql_availability_for_sqlite() {
  # Check if PostgreSQL is available for SQLite tests
  # SQLite tests should work independently, but some fixtures might still depend on PostgreSQL
  echo "🔍 Checking PostgreSQL availability for SQLite tests..."

  # Try to connect to PostgreSQL
  if command -v pg_isready &> /dev/null; then
    if pg_isready -h localhost -p 5432 -U postgres &> /dev/null; then
      echo "✅ PostgreSQL is available - SQLite tests can use full fixtures"
      export SQLITE_POSTGRESQL_AVAILABLE=true
    else
      echo "⚠️  PostgreSQL is not available - using standalone SQLite mode"
      export SQLITE_POSTGRESQL_AVAILABLE=false
      setup_standalone_sqlite_environment
    fi
  else
    echo "⚠️  pg_isready not found - using standalone SQLite mode"
    export SQLITE_POSTGRESQL_AVAILABLE=false
    setup_standalone_sqlite_environment
  fi
}

setup_standalone_sqlite_environment() {
  echo "🔧 Setting up standalone SQLite environment..."

  # Set minimal required environment variables to prevent validation errors
  export DEBUG=false
  export APP_TITLE="A2A Platform SQLite Test"
  export CLERK_JWT_PUBLIC_KEY="dummy-key-for-sqlite-tests"
  export CLERK_WEBHOOK_SECRET="dummy-secret-for-sqlite-tests"
  export STORAGE_BUCKET="dummy-bucket-for-sqlite-tests"
  export CDN_URL="https://dummy-cdn.example.com"
  export PUBSUB_PROJECT_ID="dummy-project-for-sqlite-tests"

  # Override database URLs for SQLite
  export DATABASE_URL="sqlite:///:memory:"
  export DATABASE_ASYNC_URL="sqlite+aiosqlite:///:memory:"

  echo "✅ Standalone SQLite environment configured"
}

setup_postgresql_environment() {
  # Use the enhanced container manager or intelligent container manager if available and setup is requested
  if [[ "${RUN_SETUP}" == true ]]; then
    if [[ -f "./scripts/test-container-manager.sh" ]]; then
      echo "ℹ️  Using intelligent container manager for PostgreSQL setup..."
      ./scripts/test-container-manager.sh start
    else
      echo "ℹ️  Intelligent container manager not found, using standard setup..."
    fi
  fi

  # Set the database URLs for PostgreSQL
  if [[ "${RUN_CI}" == true ]]; then
    export DATABASE_URL="postgresql://postgres:postgres@localhost:5432/a2a_platform_test"
    export DATABASE_ASYNC_URL="postgresql+asyncpg://postgres:postgres@localhost:5432/a2a_platform_test"
    export TEST_DATABASE_URL="postgresql+asyncpg://postgres:postgres@localhost:5432/a2a_platform_test"
  else
    export DATABASE_URL="postgresql://postgres:postgres@localhost:5432/a2a_platform_test"
    export DATABASE_ASYNC_URL="postgresql+asyncpg://postgres:postgres@localhost:5432/a2a_platform_test"
    export TEST_DATABASE_URL="postgresql+asyncpg://postgres:postgres@localhost:5432/a2a_platform_test"
  fi
}

# Handle database mode selection
if [[ "${RUN_NO_DB}" == true ]]; then
  # Handle performance exclusion for no_db tests
  if [[ "${RUN_PERFORMANCE}" == true ]]; then
    NO_DB_MARKER="no_db"
    echo "Running only database-free tests (including performance)..."
  else
    NO_DB_MARKER="no_db and not performance"
    echo "Running only database-free tests (excluding performance)..."
  fi

  TEST_CMD="${TEST_CMD} -m '${NO_DB_MARKER}'"
  export TEST_DATABASE_MODE="no_db"
  setup_test_environment "no_db"
elif [[ "${RUN_FAST_DB}" == true ]]; then
  # Handle performance exclusion for fast_db tests
  if [[ "${RUN_PERFORMANCE}" == true ]]; then
    FAST_DB_MARKER="fast_db"
    echo "Running only SQLite tests (including performance)..."
  else
    FAST_DB_MARKER="fast_db and not performance"
    echo "Running only SQLite tests (excluding performance)..."
  fi

  TEST_CMD="${TEST_CMD} -m '${FAST_DB_MARKER}'"
  export TEST_DATABASE_MODE="fast_db"
  setup_test_environment "fast_db"

  # Check if we're in standalone mode (PostgreSQL not available)
  # if [[ "${SQLITE_POSTGRESQL_AVAILABLE}" == "false" ]]; then
  #   echo "🔧 Using standalone SQLite mode - main test directory"
  #   # Use main test directory with SQLite conftest override
  #   TEST_CMD="${TEST_CMD} --confcutdir=tests -p no:conftest tests/conftest_sqlite_standalone.py -m '${FAST_DB_MARKER}' tests/integration/"
  # else
  #   echo "🔧 Using hybrid SQLite mode - main test directory"
  #   # Use main test directory with SQLite conftest override
  #   TEST_CMD="${TEST_CMD} --confcutdir=tests -p no:conftest tests/conftest_sqlite.py -m '${FAST_DB_MARKER}' tests/integration/"
  # fi
  TEST_CMD="${TEST_CMD} --confcutdir=tests -p no:conftest tests/conftest_sqlite_standalone.py -m '${FAST_DB_MARKER}' tests/integration/"
else
  # Handle exclusion flags for normal runs - build marker expression
  MARKER_EXPR=""

  # Build exclusion expressions
  EXCLUSIONS=()

  if [[ "${EXCLUDE_NO_DB}" == true ]]; then
    EXCLUSIONS+=("not no_db")
    echo "Excluding database-free tests..."
  fi

  if [[ "${EXCLUDE_FAST_DB}" == true ]]; then
    EXCLUSIONS+=("not fast_db")
    echo "Excluding SQLite tests..."
  fi

  # By default, exclude performance tests unless --performance flag is used or user provided markers
  if [[ "${RUN_PERFORMANCE}" != true ]] && [[ "${USER_PROVIDED_MARKERS}" != true ]]; then
    EXCLUSIONS+=("not performance")
    echo "Excluding performance tests (use --performance to include)..."
  elif [[ "${RUN_PERFORMANCE}" == true ]] && [[ "${USER_PROVIDED_MARKERS}" != true ]]; then
    echo "Including performance tests..."
  elif [[ "${USER_PROVIDED_MARKERS}" == true ]]; then
    echo "Using custom test markers provided by user..."
  fi

  # Combine exclusions with 'and' only if user hasn't provided markers
  if [[ ${#EXCLUSIONS[@]} -gt 0 ]] && [[ "${USER_PROVIDED_MARKERS}" != true ]]; then
    # Build marker expression by joining array elements with " and "
    printf -v MARKER_EXPR "%s and " "${EXCLUSIONS[@]}"
    MARKER_EXPR=${MARKER_EXPR% and }  # Remove trailing " and "
  fi

  if [[ -n "${MARKER_EXPR}" ]]; then
    TEST_CMD="${TEST_CMD} -m '${MARKER_EXPR}'"
  fi
  export TEST_DATABASE_MODE="postgresql"
  setup_test_environment "postgresql"
fi

# Performance monitoring
TEST_START_TIME=$(date +%s)
echo "🚀 Starting test execution at $(date)"

# Run the tests
echo "Running command: ${TEST_CMD}"
if ! bash -c "${TEST_CMD}"; then
  echo "❌ Tests failed!"
  exit 1
fi

# Calculate and report performance
TEST_END_TIME=$(date +%s)
TEST_DURATION=$((TEST_END_TIME - TEST_START_TIME))

echo ""
echo "✅ Tests completed successfully!"
echo "⏱️  Total execution time: ${TEST_DURATION}s"

# Performance categorization
if [[ "${TEST_DATABASE_MODE}" == "no_db" ]]; then
  echo "🏃‍♂️ Database-free mode: Optimal performance achieved"
elif [[ "${TEST_DATABASE_MODE}" == "sqlite" ]]; then
  echo "🐇 SQLite mode: Fast database testing"
else
  echo "🐢 PostgreSQL mode: Full integration testing"
fi

# Performance recommendations
if [[ "${TEST_DURATION}" -gt 30 ]]; then
  echo ""
  echo "💡 Performance tip: Consider using --no-db or --fast-db for faster feedback"
  echo "   --no-db: Run only database-free tests (~1-3s)"
  echo "   --fast-db: Run SQLite-compatible tests (~3-8s)"
fi

# Deactivate virtual environment if we activated it
if [[ "${VENV_ACTIVATED}" == true && -n "${VIRTUAL_ENV}" ]]; then
  deactivate
  echo "Virtual environment deactivated."
fi

exit 0
