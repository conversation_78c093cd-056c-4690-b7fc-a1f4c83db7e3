#!/bin/bash

# <PERSON>ript to run all PA message tests for US7.2 implementation
# This script runs the comprehensive test suite for PA message functionality

set -e

echo "🧪 Running PA Message Test Suite for US7.2"
echo "============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the correct directory
if [ ! -f "docker-compose.yml" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Parse command line arguments
SETUP_FLAG=""
VERBOSE_FLAG=""
SKIP_BACKEND=""
SKIP_FRONTEND=""
SKIP_E2E=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --setup)
            SETUP_FLAG="--setup"
            shift
            ;;
        --verbose|-v)
            VERBOSE_FLAG="-v"
            shift
            ;;
        --skip-backend)
            SKIP_BACKEND="true"
            shift
            ;;
        --skip-frontend)
            SKIP_FRONTEND="true"
            shift
            ;;
        --skip-e2e)
            SKIP_E2E="true"
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --setup         Run setup (install dependencies, migrate database)"
            echo "  --verbose, -v   Run tests in verbose mode"
            echo "  --skip-backend  Skip backend tests"
            echo "  --skip-frontend Skip frontend tests"
            echo "  --skip-e2e      Skip E2E tests"
            echo "  --help, -h      Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Function to run a test and track results
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    print_status "Running $test_name..."
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if bash -c "$test_command"; then
        print_success "$test_name passed"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        print_error "$test_name failed"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# Setup phase
if [ "$SETUP_FLAG" = "--setup" ]; then
    print_status "Setting up test environment..."
    
    # Start Docker services
    print_status "Starting Docker services..."
    docker compose up -d db redis
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 10
    
    # Run database migrations
    print_status "Running database migrations..."
    ./scripts/db-migrate.sh
    
    print_success "Setup completed"
fi

# Backend Tests
if [ "$SKIP_BACKEND" != "true" ]; then
    echo ""
    print_status "🖥️  Running Backend Tests"
    echo "=========================="
    
    # Unit Tests
    run_test "PA Message Unit Tests" \
        "./scripts/run-backend-tests.sh tests/unit/services/test_chat_service_pa.py $VERBOSE_FLAG"
    
    # Integration Tests
    run_test "PA Message Integration Tests" \
        "./scripts/run-backend-tests.sh tests/integration/test_pa_messages.py $VERBOSE_FLAG"
    
    # Performance Tests
    if [ -f "apps/backend/tests/performance/test_pa_message_performance.py" ]; then
        run_test "PA Message Performance Tests" \
            "./scripts/run-backend-tests.sh tests/performance/test_pa_message_performance.py $VERBOSE_FLAG"
    else
        print_warning "Performance tests not found, skipping..."
    fi
    
    # Security Tests
    if [ -f "apps/backend/tests/security/test_pa_message_security.py" ]; then
        run_test "PA Message Security Tests" \
            "./scripts/run-backend-tests.sh tests/security/test_pa_message_security.py $VERBOSE_FLAG"
    else
        print_warning "Security tests not found, skipping..."
    fi
    
    # Environment Tests
    if [ -f "apps/backend/tests/environment/test_pa_message_environment.py" ]; then
        run_test "PA Message Environment Tests" \
            "./scripts/run-backend-tests.sh tests/environment/test_pa_message_environment.py $VERBOSE_FLAG"
    else
        print_warning "Environment tests not found, skipping..."
    fi
fi

# Frontend Tests
if [ "$SKIP_FRONTEND" != "true" ]; then
    echo ""
    print_status "🌐 Running Frontend Tests"
    echo "========================="
    
    # Unit Tests
    run_test "PAMessage Component Tests" \
        "cd apps/web && bun test PAMessage.test.tsx"
    
    # Hook Tests
    if [ -f "apps/web/src/hooks/__tests__/useMessageSubscription.test.ts" ]; then
        run_test "Message Subscription Hook Tests" \
            "cd apps/web && bun test useMessageSubscription.test.ts"
    else
        print_warning "Message subscription hook tests not found, skipping..."
    fi
fi

# E2E Tests
if [ "$SKIP_E2E" != "true" ]; then
    echo ""
    print_status "🎭 Running E2E Tests"
    echo "==================="
    
    # Start test services
    print_status "Starting test services..."
    docker compose -f docker-compose.test.yml up -d
    
    # Wait for services
    sleep 15
    
    # PA Message E2E Tests
    if [ -f "apps/web/cypress/e2e/chat/pa-messages.cy.ts" ]; then
        run_test "PA Message E2E Tests" \
            "./scripts/run-frontend-tests.sh --e2e --spec 'cypress/e2e/chat/pa-messages.cy.ts'"
    else
        print_warning "PA message E2E tests not found, skipping..."
    fi
    
    # Cleanup test services
    print_status "Stopping test services..."
    docker compose -f docker-compose.test.yml down
fi

# Test Summary
echo ""
echo "📊 Test Summary"
echo "==============="
echo "Total Tests: $TOTAL_TESTS"
echo "Passed: $PASSED_TESTS"
echo "Failed: $FAILED_TESTS"

if [ $FAILED_TESTS -eq 0 ]; then
    print_success "All tests passed! 🎉"
    echo ""
    echo "✅ US7.2 PA Message Implementation Status:"
    echo "   - PA message creation and persistence: ✅"
    echo "   - GraphQL mutations and subscriptions: ✅"
    echo "   - Frontend PA message display: ✅"
    echo "   - Real-time message delivery: ✅"
    echo "   - Performance requirements: ✅"
    echo "   - Security validation: ✅"
    echo "   - Environment compatibility: ✅"
    echo ""
    echo "🎯 Ready for production deployment!"
    exit 0
else
    print_error "$FAILED_TESTS test(s) failed"
    echo ""
    echo "❌ US7.2 PA Message Implementation needs attention:"
    echo "   Please review failed tests and fix issues before deployment"
    exit 1
fi
