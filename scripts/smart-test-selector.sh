#!/bin/bash
#
# A2A Platform - Smart Test Selector
#
# Intelligently selects which tests to run based on code changes,
# test history, and performance characteristics.
#
# Usage:
#   ./scripts/smart-test-selector.sh [OPTIONS]
#
# Options:
#   --changed-only      Run only tests affected by changed files
#   --since COMMIT      Compare changes since specific commit
#   --cache-results     Cache test results for unchanged code
#   --force-all         Force run all tests regardless of changes
#   --analyze           Analyze test dependencies and suggest optimizations

set -e

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
CACHE_DIR="${PROJECT_ROOT}/.test-cache"
CHANGED_ONLY=false
SINCE_COMMIT=""
CACHE_RESULTS=false
FORCE_ALL=false
ANALYZE=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --changed-only)
      CHANGED_ONLY=true
      shift
      ;;
    --since)
      SINCE_COMMIT="$2"
      shift 2
      ;;
    --cache-results)
      CACHE_RESULTS=true
      shift
      ;;
    --force-all)
      FORCE_ALL=true
      shift
      ;;
    --analyze)
      ANALYZE=true
      shift
      ;;
    --help)
      echo "Usage: $0 [OPTIONS]"
      echo "Options:"
      echo "  --changed-only      Run only tests affected by changed files"
      echo "  --since COMMIT      Compare changes since specific commit"
      echo "  --cache-results     Cache test results for unchanged code"
      echo "  --force-all         Force run all tests regardless of changes"
      echo "  --analyze           Analyze test dependencies and suggest optimizations"
      echo "  --help              Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for available options"
      exit 1
      ;;
  esac
done

echo "╔════════════════════════════════════════════════════════════╗"
echo "║ A2A Platform - Smart Test Selector                         ║"
echo "╚════════════════════════════════════════════════════════════╝"
echo ""

# Initialize cache directory
mkdir -p "$CACHE_DIR"

# Function to get file hash
get_file_hash() {
  local file="$1"
  if [[ -f "$file" ]]; then
    md5sum "$file" | cut -d' ' -f1
  else
    echo "missing"
  fi
}

# Function to get changed files
get_changed_files() {
  local since="${1:-HEAD~1}"

  if git rev-parse --is-inside-work-tree >/dev/null 2>&1; then
    git diff --name-only "$since" HEAD 2>/dev/null || git diff --name-only --cached 2>/dev/null || echo ""
  else
    echo ""
  fi
}

# Function to map source files to test files
map_source_to_tests() {
  local source_file="$1"
  local test_files=()

  # Direct mapping: src/module.py -> tests/test_module.py
  if [[ "$source_file" =~ ^apps/backend/src/(.+)\.py$ ]]; then
    local module_path="${BASH_REMATCH[1]}"
    local module_name=$(basename "$module_path")
    local module_dir=$(dirname "$module_path")

    # Look for corresponding test files
    find "apps/backend/tests" -name "*test*${module_name}*" -o -name "*${module_name}*test*" 2>/dev/null || true
  fi

  # Configuration changes affect all tests
  if [[ "$source_file" =~ (pyproject\.toml|\.env|docker-compose|alembic) ]]; then
    find "apps/backend/tests" -name "*.py" -type f 2>/dev/null || true
  fi

  # Database schema changes affect integration tests
  if [[ "$source_file" =~ (alembic/versions|models|database) ]]; then
    find "apps/backend/tests/integration" -name "*.py" -type f 2>/dev/null || true
  fi
}

# Function to analyze test dependencies
analyze_test_dependencies() {
  echo -e "${BLUE}Analyzing test dependencies...${NC}"

  local total_tests=0
  local no_db_tests=0
  local fast_db_tests=0
  local slow_tests=0

  # Scan all test files
  local test_files=($(find "apps/backend/tests" -name "*.py" -type f))
  total_tests=${#test_files[@]}

  for test_file in "${test_files[@]}"; do
    if grep -q "@pytest.mark.no_db" "$test_file" 2>/dev/null; then
      ((no_db_tests++))
    elif grep -q "@pytest.mark.fast_db" "$test_file" 2>/dev/null; then
      ((fast_db_tests++))
    else
      ((slow_tests++))
    fi
  done

  echo ""
  echo "Test Distribution Analysis:"
  echo "  Total test files: $total_tests"

  if [[ $total_tests -gt 0 ]]; then
    echo "  Database-free (@no_db): $no_db_tests ($(( no_db_tests * 100 / total_tests ))%)"
    echo "  SQLite-compatible (@fast_db): $fast_db_tests ($(( fast_db_tests * 100 / total_tests ))%)"
    echo "  PostgreSQL-required: $slow_tests ($(( slow_tests * 100 / total_tests ))%)"
  else
    echo "  No test files found"
    return 0
  fi
  echo ""

  # Optimization recommendations
  echo -e "${YELLOW}Optimization Recommendations:${NC}"

  if [[ $no_db_tests -lt $((total_tests / 4)) ]]; then
    echo "  • Consider migrating more tests to @pytest.mark.no_db for faster execution"
  fi

  if [[ $fast_db_tests -lt $((total_tests / 4)) ]]; then
    echo "  • Consider migrating basic CRUD tests to @pytest.mark.fast_db"
  fi

  if [[ $slow_tests -gt $((total_tests / 2)) ]]; then
    echo "  • High percentage of PostgreSQL tests may slow down CI"
  fi

  # Find unmarked test files
  local unmarked_files=()
  local test_files_to_check=($(find "apps/backend/tests" -name "test_*.py" -type f))

  for test_file in "${test_files_to_check[@]}"; do
    if ! grep -q "@pytest.mark\." "$test_file" 2>/dev/null; then
      unmarked_files+=("$test_file")
    fi
  done

  if [[ ${#unmarked_files[@]} -gt 0 ]]; then
    echo ""
    echo -e "${YELLOW}Unmarked test files (candidates for optimization):${NC}"
    for file in "${unmarked_files[@]:0:10}"; do  # Show first 10
      echo "  • $file"
    done
    if [[ ${#unmarked_files[@]} -gt 10 ]]; then
      echo "  • ... and $((${#unmarked_files[@]} - 10)) more"
    fi
  fi
}

# Function to select tests based on changes
select_tests() {
  local changed_files=()
  local test_files=()

  if [[ "$FORCE_ALL" == true ]]; then
    echo -e "${BLUE}Force mode: Running all tests${NC}"
    echo "all"
    return
  fi

  if [[ "$CHANGED_ONLY" == true ]]; then
    echo -e "${BLUE}Analyzing changed files...${NC}"

    # Get changed files
    local since_ref="${SINCE_COMMIT:-HEAD~1}"
    mapfile -t changed_files < <(get_changed_files "$since_ref")

    if [[ ${#changed_files[@]} -eq 0 ]]; then
      echo -e "${GREEN}No changes detected. Consider using cached results.${NC}"
      echo "none"
      return
    fi

    echo "Changed files:"
    for file in "${changed_files[@]}"; do
      echo "  • $file"
    done
    echo ""

    # Map changed files to test files
    local all_test_files=()
    for file in "${changed_files[@]}"; do
      mapfile -t mapped_tests < <(map_source_to_tests "$file")
      all_test_files+=("${mapped_tests[@]}")
    done

    # Remove duplicates and filter existing files
    local unique_tests=()
    for test in "${all_test_files[@]}"; do
      if [[ -f "$test" ]] && [[ ! " ${unique_tests[*]} " =~ " $test " ]]; then
        unique_tests+=("$test")
      fi
    done

    if [[ ${#unique_tests[@]} -eq 0 ]]; then
      echo -e "${YELLOW}No test files mapped to changes. Running fast tests as fallback.${NC}"
      echo "fast"
    else
      echo "Affected test files:"
      for test in "${unique_tests[@]}"; do
        echo "  • $test"
      done
      echo ""

      # Return space-separated list of test files
      printf "%s " "${unique_tests[@]}"
      echo ""
    fi
  else
    echo "all"
  fi
}

# Function to check cache validity
check_cache_validity() {
  local test_file="$1"
  local cache_file="$CACHE_DIR/$(echo "$test_file" | tr '/' '_').cache"

  if [[ ! -f "$cache_file" ]]; then
    return 1  # No cache
  fi

  # Check if source file has changed
  local cached_hash=$(head -n 1 "$cache_file" 2>/dev/null || echo "")
  local current_hash=$(get_file_hash "$test_file")

  if [[ "$cached_hash" != "$current_hash" ]]; then
    return 1  # Cache invalid
  fi

  return 0  # Cache valid
}

# Function to cache test results
cache_test_result() {
  local test_file="$1"
  local result="$2"
  local cache_file="$CACHE_DIR/$(echo "$test_file" | tr '/' '_').cache"

  local file_hash=$(get_file_hash "$test_file")
  echo "$file_hash" > "$cache_file"
  echo "$result" >> "$cache_file"
  echo "$(date)" >> "$cache_file"
}

# Main execution
main() {
  if [[ "$ANALYZE" == true ]]; then
    analyze_test_dependencies
    return 0
  fi

  local test_selection=$(select_tests)

  case "$test_selection" in
    "all")
      echo -e "${BLUE}Running all tests with optimal strategy...${NC}"
      if [[ -f "./scripts/parallel-test-runner.sh" ]]; then
        ./scripts/parallel-test-runner.sh
      else
        ./scripts/run-backend-tests.sh --ci
      fi
      ;;
    "none")
      echo -e "${GREEN}No tests need to be run.${NC}"
      ;;
    "fast")
      echo -e "${BLUE}Running fast test subset...${NC}"
      ./scripts/run-backend-tests.sh --ci --no-db
      ;;
    *)
      echo -e "${BLUE}Running selected tests...${NC}"
      # Convert test selection to relative paths and run
      local test_args=""
      for test_file in $test_selection; do
        if [[ -f "$test_file" ]]; then
          # Convert to backend-relative path
          local backend_relative="${test_file#apps/backend/}"
          test_args="$test_args $backend_relative"
        fi
      done

      if [[ -n "$test_args" ]]; then
        ./scripts/run-backend-tests.sh --ci $test_args
      else
        echo -e "${YELLOW}No valid test files found.${NC}"
      fi
      ;;
  esac
}

main "$@"
