#!/bin/bash

# CDN Cache Management Script
# This script helps manage CDN cache settings via GitHub Actions environment variables

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show current cache configuration
show_current_config() {
    print_status "Current cache configuration from terraform.tfvars:"
    echo

    if [[ "$1" == "staging" ]]; then
        CONFIG_FILE="$PROJECT_ROOT/terraform/environments/staging/terraform.tfvars"
    elif [[ "$1" == "production" ]]; then
        CONFIG_FILE="$PROJECT_ROOT/terraform/environments/production/terraform.tfvars"
    else
        print_error "Invalid environment. Use 'staging' or 'production'"
        exit 1
    fi

    if [[ -f "$CONFIG_FILE" ]]; then
        grep -A 30 "cache_config = {" "$CONFIG_FILE" | head -31
    else
        print_error "Configuration file not found: $CONFIG_FILE"
        exit 1
    fi
}

# Function to show environment variable examples
show_env_vars() {
    print_status "Available environment variables for cache override:"
    echo
    cat << 'EOF'
# API Caching
CACHE_API_ENABLED=true|false
CACHE_API_TTL=300  # seconds

# GraphQL Caching
CACHE_GRAPHQL_ENABLED=true|false
CACHE_GRAPHQL_TTL=60  # seconds

# Frontend Caching
CACHE_STATIC_ASSETS_TTL=86400  # seconds
CACHE_HTML_TTL=1800  # seconds

# Global Settings
CACHE_DEVELOPMENT_MODE=true|false

# Example: Enable API caching with 5-minute TTL
export CACHE_API_ENABLED=true
export CACHE_API_TTL=300
EOF
}

# Function to validate cache TTL values
validate_ttl() {
    local ttl=$1
    local name=$2

    if ! [[ "$ttl" =~ ^[0-9]+$ ]]; then
        print_error "Invalid TTL value for $name: $ttl (must be a positive integer)"
        return 1
    fi

    if [[ $ttl -lt 0 ]]; then
        print_error "TTL value for $name must be non-negative: $ttl"
        return 1
    fi

    if [[ $ttl -gt 31536000 ]]; then
        print_warning "TTL value for $name is very high (>1 year): $ttl seconds"
    fi

    return 0
}

# Function to show cache recommendations
show_recommendations() {
    print_status "Cache configuration recommendations:"
    echo
    cat << 'EOF'
STAGING ENVIRONMENT:
- Enable API/GraphQL caching for testing
- Use shorter TTLs (1-5 minutes) for rapid iteration
- Monitor cache hit rates and performance

PRODUCTION ENVIRONMENT:
- Start with API/GraphQL caching DISABLED
- Use longer TTLs for static assets (1 year)
- Enable API caching gradually after testing
- Use device-specific caching for responsive content

GENERAL GUIDELINES:
- Static assets: 1 year (with versioning)
- HTML files: 5-30 minutes
- API endpoints: 1-5 minutes (when enabled)
- GraphQL: 30 seconds - 1 minute (when enabled)
EOF
}

# Function to show Terraform outputs for cache debugging
show_terraform_outputs() {
    local env=$1
    print_status "Terraform cache outputs for $env environment:"
    echo

    local tf_dir="$PROJECT_ROOT/terraform/environments/$env"

    if [[ ! -d "$tf_dir" ]]; then
        print_error "Terraform directory not found: $tf_dir"
        return 1
    fi

    if command -v terraform >/dev/null 2>&1; then
        cd "$tf_dir"

        # Check if terraform is initialized
        if [[ ! -d ".terraform" ]]; then
            print_warning "Terraform not initialized in $env environment"
            print_status "Run 'terraform init' in $tf_dir first"
            return 1
        fi

        print_status "Cache configuration output:"
        terraform output cache_config 2>/dev/null || print_warning "cache_config output not available"

        echo
        print_status "Page rules status:"
        terraform output page_rules_status 2>/dev/null || print_warning "page_rules_status output not available"

        echo
        print_status "DNS records status:"
        terraform output dns_records_status 2>/dev/null || print_warning "dns_records_status output not available"

        echo
        print_status "Cache performance info:"
        terraform output cache_performance_info 2>/dev/null || print_warning "cache_performance_info output not available"

    else
        print_error "Terraform not found. Please install Terraform to view outputs."
        return 1
    fi
}

# Function to show usage
show_usage() {
    cat << EOF
CDN Cache Management Script

USAGE:
    $0 <command> [options]

COMMANDS:
    show <env>          Show current cache configuration for environment
    outputs <env>       Show Terraform cache outputs for debugging
    env-vars           Show available environment variables
    recommendations    Show cache configuration recommendations
    help              Show this help message

ENVIRONMENTS:
    staging            Staging environment
    production         Production environment

EXAMPLES:
    $0 show staging
    $0 show production
    $0 outputs staging
    $0 env-vars
    $0 recommendations

For more information, see: docs/cdn/cache-configuration.md
EOF
}

# Main script logic
case "${1:-}" in
    "show")
        if [[ -z "${2:-}" ]]; then
            print_error "Environment required. Use 'staging' or 'production'"
            echo
            show_usage
            exit 1
        fi
        show_current_config "$2"
        ;;
    "outputs")
        if [[ -z "${2:-}" ]]; then
            print_error "Environment required. Use 'staging' or 'production'"
            echo
            show_usage
            exit 1
        fi
        show_terraform_outputs "$2"
        ;;
    "env-vars")
        show_env_vars
        ;;
    "recommendations")
        show_recommendations
        ;;
    "help"|"-h"|"--help")
        show_usage
        ;;
    *)
        print_error "Invalid command: ${1:-}"
        echo
        show_usage
        exit 1
        ;;
esac
