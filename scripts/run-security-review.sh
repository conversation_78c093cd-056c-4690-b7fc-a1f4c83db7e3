#!/bin/bash
# filepath: /Users/<USER>/dev/blkops-collective/a2a-platform/scripts/run-security-review.sh
#
# Workflow script to run the entire security review process for HTTPS implementation
# This script guides the reviewer through the entire security review process

set -e

# Define colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Define paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(dirname "$SCRIPT_DIR")"

# Define environment and domains
ENV=${1:-"dev"}
API_DOMAIN=${2:-"localhost:8000"}
FRONTEND_DOMAIN=${3:-"localhost:5173"}

# Create a results directory to store all review artifacts
RESULTS_DIR="${ROOT_DIR}/security-review-results"
mkdir -p "${RESULTS_DIR}"
TIMESTAMP=$(date +"%Y%m%d-%H%M%S")
REVIEW_DIR="${RESULTS_DIR}/${TIMESTAMP}-https-security-review"
mkdir -p "${REVIEW_DIR}"

# Log file for capturing output
LOG_FILE="${REVIEW_DIR}/security-review.log"
touch "${LOG_FILE}"

# Function to log messages to file and console
log() {
  echo -e "$1" | tee -a "${LOG_FILE}"
}

# Function to run a command and log output
run_and_log() {
  local cmd_name=$1
  local cmd=$2
  local output_file="${REVIEW_DIR}/${cmd_name}.log"
  
  log "\n${YELLOW}Running: ${cmd_name}${NC}"
  log "${BLUE}Command: ${cmd}${NC}"
  log "${BLUE}Output will be saved to: ${output_file}${NC}"
  
  # Run the command and capture exit status
  if bash -c "${cmd}" > "${output_file}" 2>&1; then
    log "${GREEN}✅ Command completed successfully${NC}"
    return 0
  else
    local exit_code=$?
    log "${RED}❌ Command failed with exit code: ${exit_code}${NC}"
    return $exit_code
  fi
}

# Display header
log "${BLUE}=========================================================${NC}"
log "${BLUE}${BOLD}       A2A Platform HTTPS Security Review Workflow       ${NC}"
log "${BLUE}=========================================================${NC}"
log ""
log "${YELLOW}Environment: ${ENV}${NC}"
log "${YELLOW}API Domain: ${API_DOMAIN}${NC}"
log "${YELLOW}Frontend Domain: ${FRONTEND_DOMAIN}${NC}"
log "${YELLOW}Review artifacts will be saved to: ${REVIEW_DIR}${NC}"
log ""

# Step 1: Create review metadata file
log "${BLUE}Step 1: Creating review metadata${NC}"
cat > "${REVIEW_DIR}/review-metadata.json" << EOF
{
  "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "reviewer": "$(whoami)",
  "environment": "${ENV}",
  "api_domain": "${API_DOMAIN}",
  "frontend_domain": "${FRONTEND_DOMAIN}"
}
EOF
log "${GREEN}✅ Created review metadata file${NC}"

# Step 2: Run automated validation tests
log "${BLUE}Step 2: Running automated validation tests${NC}"

# Step 2.1: Run HTTPS implementation validation
run_and_log "validate-https-implementation" "${SCRIPT_DIR}/validate-https-implementation.sh ${ENV} ${API_DOMAIN} ${FRONTEND_DOMAIN}"
VALIDATE_RESULT=$?

# Step 2.2: Run security headers check
run_and_log "check-security-headers" "${SCRIPT_DIR}/check-security-headers.sh ${API_DOMAIN}"
HEADERS_RESULT=$?

# Step 2.3: Run WebSocket security test (if Node.js is available)
if command -v node &> /dev/null; then
  run_and_log "websocket-ssl-test" "WS_TEST_DOMAIN=${API_DOMAIN} node ${SCRIPT_DIR}/websocket-ssl-test.js"
  WEBSOCKET_RESULT=$?
else
  log "${YELLOW}⚠️ Skipping WebSocket security test - Node.js not found${NC}"
  WEBSOCKET_RESULT=0
fi

# Step 2.4: Run SSL performance test (if in non-dev environment)
if [ "${ENV}" != "dev" ]; then
  run_and_log "ssl-performance-test" "${SCRIPT_DIR}/ssl-performance-test.sh ${API_DOMAIN}"
  PERFORMANCE_RESULT=$?
else
  log "${YELLOW}⚠️ Skipping SSL performance test in dev environment${NC}"
  PERFORMANCE_RESULT=0
fi

# Step 3: Run security review checklist
log "${BLUE}Step 3: Running security review checklist${NC}"
log "${YELLOW}This step requires manual input - please follow the prompts${NC}"
log ""

# Run the checklist and capture output
"${SCRIPT_DIR}/security-review-checklist.sh" "${API_DOMAIN}" "${FRONTEND_DOMAIN}" | tee "${REVIEW_DIR}/security-checklist.log"
CHECKLIST_RESULT=$?

# Step 4: Collect external security assessment evidence
log "${BLUE}Step 4: External security assessment${NC}"
log "${YELLOW}Please complete the following external security assessments:${NC}"
log ""
log "1. SSL Labs assessment (https://www.ssllabs.com/ssltest/)"
log "   - Test URL: https://${API_DOMAIN}"
log "   - Save results as PDF or screenshot to: ${REVIEW_DIR}/ssllabs-results.pdf"
log ""
log "2. Security Headers assessment (https://securityheaders.com/)"
log "   - Test URL: https://${API_DOMAIN}"
log "   - Save results as PDF or screenshot to: ${REVIEW_DIR}/securityheaders-results.pdf"
log ""
log "3. Mixed Content Check (using browser developer tools)"
log "   - Test URL: https://${FRONTEND_DOMAIN}"
log "   - Save screenshot of Network tab to: ${REVIEW_DIR}/mixed-content-check.png"
log ""

read -p "Have you completed the external security assessments? (y/n): " EXTERNAL_COMPLETE
if [ "${EXTERNAL_COMPLETE}" != "y" ]; then
  log "${RED}❌ External security assessment not completed${NC}"
  EXTERNAL_RESULT=1
else
  log "${GREEN}✅ External security assessment completed${NC}"
  EXTERNAL_RESULT=0
fi

# Step 5: Generate security review report
log "${BLUE}Step 5: Generating security review report${NC}"

# Calculate overall result
if [ $VALIDATE_RESULT -eq 0 ] && [ $HEADERS_RESULT -eq 0 ] && [ $WEBSOCKET_RESULT -eq 0 ] && [ $PERFORMANCE_RESULT -eq 0 ] && [ $CHECKLIST_RESULT -eq 0 ] && [ $EXTERNAL_RESULT -eq 0 ]; then
  OVERALL_RESULT="PASSED"
else
  OVERALL_RESULT="FAILED"
fi

# Create report markdown file
REPORT_FILE="${REVIEW_DIR}/security-review-report.md"
cat > "${REPORT_FILE}" << EOF
# HTTPS Security Review Report

**Date:** $(date)
**Reviewer:** $(whoami)
**Environment:** ${ENV}
**API Domain:** ${API_DOMAIN}
**Frontend Domain:** ${FRONTEND_DOMAIN}

## Overall Result: ${OVERALL_RESULT}

## Component Results

| Component | Result |
|:----------|:-------|
| HTTPS Implementation Validation | $([ $VALIDATE_RESULT -eq 0 ] && echo "✅ PASSED" || echo "❌ FAILED") |
| Security Headers Check | $([ $HEADERS_RESULT -eq 0 ] && echo "✅ PASSED" || echo "❌ FAILED") |
| WebSocket Security Test | $([ $WEBSOCKET_RESULT -eq 0 ] && echo "✅ PASSED" || echo "❌ FAILED") |
| SSL Performance Test | $([ $PERFORMANCE_RESULT -eq 0 ] && echo "✅ PASSED" || echo "❌ FAILED") |
| Security Review Checklist | $([ $CHECKLIST_RESULT -eq 0 ] && echo "✅ PASSED" || echo "❌ FAILED") |
| External Security Assessment | $([ $EXTERNAL_RESULT -eq 0 ] && echo "✅ PASSED" || echo "❌ FAILED") |

## Detailed Results

For detailed test results, see the log files in the review directory:
\`${REVIEW_DIR}\`

## Next Steps

$(if [ "${OVERALL_RESULT}" == "PASSED" ]; then
  echo "1. Update the implementation checklist in \`specs/US6.1-Ensure-HTTPS-Communication.md\` to mark \"Security review passed\" as complete."
  echo "2. Proceed to production deployment verification."
else
  echo "1. Address the issues identified in the failed components."
  echo "2. Re-run the security review process."
fi)

## Reviewer Comments

<!-- Add any comments or observations here -->

EOF

log "${GREEN}✅ Generated security review report: ${REPORT_FILE}${NC}"

# Step 6: Update implementation checklist if passed
if [ "${OVERALL_RESULT}" == "PASSED" ]; then
  log "${BLUE}Step 6: Updating implementation checklist${NC}"
  log "${YELLOW}Do you want to update the implementation checklist to mark 'Security review passed' as complete? (y/n): ${NC}"
  read -p "" UPDATE_CHECKLIST
  
  if [ "${UPDATE_CHECKLIST}" == "y" ]; then
    # Backup original file
    cp "${ROOT_DIR}/specs/US6.1-Ensure-HTTPS-Communication.md" "${ROOT_DIR}/specs/US6.1-Ensure-HTTPS-Communication.md.bak"
    
    # Update checklist item
    sed -i.bak 's/- \[ \] ✅ Security review passed (TODO)/- [x] ✅ Security review passed/' "${ROOT_DIR}/specs/US6.1-Ensure-HTTPS-Communication.md"
    
    # Check if update was successful
    if grep -q "- \[x\] ✅ Security review passed" "${ROOT_DIR}/specs/US6.1-Ensure-HTTPS-Communication.md"; then
      log "${GREEN}✅ Updated implementation checklist${NC}"
    else
      log "${RED}❌ Failed to update implementation checklist${NC}"
    fi
    
    # Remove backup file
    rm "${ROOT_DIR}/specs/US6.1-Ensure-HTTPS-Communication.md.bak"
  else
    log "${YELLOW}⚠️ Skipping implementation checklist update${NC}"
  fi
fi

# Final summary
log "\n${BLUE}=========================================================${NC}"
log "${BLUE}${BOLD}               Security Review Summary                 ${NC}"
log "${BLUE}=========================================================${NC}"
log ""
log "Review completed with overall result: ${OVERALL_RESULT}"
log "Review artifacts saved to: ${REVIEW_DIR}"
log "Review report: ${REPORT_FILE}"
log ""

if [ "${OVERALL_RESULT}" == "PASSED" ]; then
  log "${GREEN}${BOLD}Security review passed successfully!${NC}"
  exit 0
else
  log "${RED}${BOLD}Security review failed. Please address the issues and try again.${NC}"
  exit 1
fi
