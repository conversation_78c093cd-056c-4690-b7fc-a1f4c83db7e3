#!/bin/bash
# filepath: /Users/<USER>/dev/blkops-collective/a2a-platform/scripts/validate-https-implementation.sh
#
# Comprehensive script to validate the entire HTTPS implementation
# This script runs all HTTPS-related tests to ensure complete security compliance

set -e

# Define colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Define paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(dirname "$SCRIPT_DIR")"

# Print header
echo -e "${BLUE}=========================================================${NC}"
echo -e "${BLUE}   A2A Platform HTTPS Implementation Validation Suite    ${NC}"
echo -e "${BLUE}=========================================================${NC}"
echo ""

# Get the test environment from command line argument or default to dev
ENV=${1:-"dev"}
DOMAIN=${2:-"localhost:8000"}
FRONTEND_URL=${3:-"localhost:5173"}

echo -e "${YELLOW}Environment: ${ENV}${NC}"
echo -e "${YELLOW}API Domain: ${DOMAIN}${NC}"
echo -e "${YELLOW}Frontend URL: ${FRONTEND_URL}${NC}"
echo ""

# Track overall test status
OVERALL_STATUS=0

# Function to run a test and track status
run_test() {
  local test_name=$1
  local test_cmd=$2
  
  echo -e "${BLUE}=========================================================${NC}"
  echo -e "${YELLOW}Running: $test_name${NC}"
  echo -e "${BLUE}=========================================================${NC}"
  
  # Run the test command
  if bash -c "$test_cmd"; then
    echo -e "${GREEN}✅ $test_name completed successfully${NC}"
  else
    echo -e "${RED}❌ $test_name failed${NC}"
    OVERALL_STATUS=1
  fi
  
  echo ""
}

# 1. Check if SSL certificates exist
if [ ! -f "${ROOT_DIR}/ssl-certs/localhost.pem" ] || [ ! -f "${ROOT_DIR}/ssl-certs/localhost-key.pem" ]; then
  echo -e "${YELLOW}SSL certificates not found. Generating certificates...${NC}"
  run_test "Generate SSL Certificates" "${SCRIPT_DIR}/generate-ssl-certs.sh"
fi

# 2. Backend HTTPS enforcement tests
run_test "Backend HTTPS Enforcement Tests" "cd ${ROOT_DIR}/apps/backend && ${SCRIPT_DIR}/run-backend-tests.sh --test-path=tests/integration/test_https_enforcement.py"

# 3. Backend Security Middleware Tests
run_test "Backend Security Middleware Tests" "cd ${ROOT_DIR}/apps/backend && ${SCRIPT_DIR}/run-backend-tests.sh --test-path=tests/integration/test_security_middleware.py"

# 4. Frontend HTTPS E2E Tests
if [ "$ENV" == "dev" ]; then
  run_test "Frontend HTTPS E2E Tests" "${SCRIPT_DIR}/run-https-tests.sh"
else
  # For non-dev environments, just run the Cypress tests against the provided URL
  run_test "Frontend HTTPS E2E Tests" "cd ${ROOT_DIR}/apps/web && CYPRESS_baseUrl=https://${FRONTEND_URL} npx cypress run --spec \"cypress/e2e/https-enforcement.cy.ts\""
fi

# 5. Check Security Headers
run_test "Security Headers Validation" "${SCRIPT_DIR}/check-security-headers.sh ${DOMAIN}"

# 6. WebSocket Security Tests (if Node.js is installed)
if command -v node &> /dev/null; then
  run_test "WebSocket Security Tests" "WS_TEST_DOMAIN=${DOMAIN} node ${SCRIPT_DIR}/websocket-ssl-test.js"
fi

# 7. SSL Performance Tests (if environment is not dev)
if [ "$ENV" != "dev" ]; then
  run_test "SSL Performance Tests" "${SCRIPT_DIR}/ssl-performance-test.sh ${DOMAIN}"
fi

# Final summary
echo -e "${BLUE}=========================================================${NC}"
echo -e "${YELLOW}HTTPS Implementation Validation Summary${NC}"
echo -e "${BLUE}=========================================================${NC}"

if [ $OVERALL_STATUS -eq 0 ]; then
  echo -e "${GREEN}✅ All HTTPS implementation tests passed!${NC}"
  echo ""
  echo -e "The A2A Platform HTTPS implementation has been validated against:"
  echo -e "- HTTP to HTTPS redirects"
  echo -e "- Security headers compliance (HSTS, CSP, etc.)"
  echo -e "- WebSocket WSS enforcement"
  echo -e "- Frontend mixed content prevention"
  echo -e "- Certificate validation"
  if [ "$ENV" != "dev" ]; then
    echo -e "- SSL performance metrics"
  fi
else
  echo -e "${RED}❌ Some HTTPS implementation tests failed.${NC}"
  echo -e "Please review the test output above to identify and fix the issues."
fi

echo ""
echo -e "${BLUE}=========================================================${NC}"

# Return the overall status
exit $OVERALL_STATUS
