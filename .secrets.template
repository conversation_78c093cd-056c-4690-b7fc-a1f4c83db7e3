# .secrets file for act (GitHub Actions local testing)
# 
# This file contains dummy/mock values for testing workflows locally with act.
# DO NOT put real secrets here - this is for local testing only.
#
# To use this file:
# 1. Copy this to .secrets in your project root
# 2. Update values as needed for your local testing
# 3. Make sure .secrets is in your .gitignore

# GCP Configuration (mock values for testing)
GCP_PROJECT_ID=test-project-id
GCP_SERVICE_ACCOUNT=<EMAIL>
WORKLOAD_IDENTITY_PROVIDER=projects/*********/locations/global/workloadIdentityPools/test-pool/providers/test-provider

# Database Configuration (mock values)
DB_APP_PROXY_URL=postgresql://test_user:test_password@localhost:5432/test_database
DB_PROXY_URL=postgresql+asyncpg://test_user:test_password@localhost:5432/test_database
REDIS_URL=redis://localhost:6379

# Clerk Configuration (mock values)
CLERK_API_KEY=sk_test_mock_api_key_*********0abcdef
CLERK_JWT_PUBLIC_KEY=-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA*********0\n-----END PUBLIC KEY-----
CLERK_WEBHOOK_SECRET=whsec_mock_webhook_secret_*********0

# Storage Configuration (mock values)
STORAGE_BUCKET=test-storage-bucket
CDN_URL=https://cdn.test-domain.com

# Web Frontend Configuration (mock values)
VITE_GRAPHQL_API_URL=https://api.test-domain.com/graphql

# Additional GCP Configuration for workers workflow
ARTIFACT_REGISTRY_HOST=us-central1-docker.pkg.dev
GCP_WIF_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GCP_RUN_SERVICE_ACCOUNT_EMAIL=<EMAIL>