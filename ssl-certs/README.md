# SSL Certificates for Local Development

This directory contains SSL certificates for local HTTPS development. These files are generated by the `scripts/generate-ssl-certs.sh` script and should not be committed to version control.

## 📄 Certificate Files

When generated, this directory will contain:

- `localhost.pem` - SSL certificate for localhost
- `localhost-key.pem` - Private key for the localhost certificate

## 🔧 How to Generate Certificates

To generate SSL certificates for local development, run:

```bash
./scripts/generate-ssl-certs.sh
```

This script uses [mkcert](https://github.com/FiloSottile/mkcert) to create locally-trusted SSL certificates for development.

## 🔒 Enabling HTTPS in Development

To enable HTTPS in your local development environment:

1. Generate the certificates as described above
2. Add `VITE_USE_HTTPS=true` to your `.env` file
3. Restart your development environment:
   ```bash
   docker compose -f docker-compose.dev.yml down
   docker compose -f docker-compose.dev.yml up
   ```

## ⚠️ Security Note

These certificates are meant for local development only and should never be used in production.

In production, we use properly-issued SSL certificates managed by Google Certificate Manager with automatic renewal.

## 🧪 Testing HTTPS

To test the HTTPS implementation, you can run:

```bash
./scripts/run-https-tests.sh
```

This script will start the development environment with HTTPS enabled and run the end-to-end tests to verify HTTPS enforcement.
