<!-- filepath: /home/<USER>/workbook/a2a-platform/PROMPTS.md -->
# Prompts

## Comprehensive Analysis of User Story and QA Testing Specifications Generated by Large Language Models

```
Perform a comprehensive comparative analysis of user story and QA testing specifications using the evaluation framework defined in `research/user_stories/OVERVIEW.md`. This analysis will compare implementations from different LLMs to evaluate their adherence to the project's specification standards.

**Scope and File Groups:**
1. **User Story Batch (non-QA)**: All user story specification files that follow the `user-story-specification.md` template
2. **QA Testing Batch**: All QA testing specification files that follow the `qa-testing-specification.md` template

**Detailed Analysis Requirements:**

**Phase 1: Criteria Extraction**
- Extract and display the complete evaluation criteria/rubric from `research/user_stories/OVERVIEW.md`
- Identify any scoring methodology, weighting, or assessment guidelines
- Note any specific requirements for template adherence, emoji usage, compression techniques, or AI agent optimization

**Phase 2: Individual Specification Evaluation**
For each specification file in both batches:
- Evaluate adherence to each criterion using the established rubric
- Document specific evidence (direct quotes, structural elements, formatting choices)
- Assign quantitative scores where criteria provide scoring guidelines
- Note qualitative assessments for subjective criteria

**Phase 3: Comparative Analysis**
Create a comprehensive comparison table with:
- **Criterion Name**: Each evaluation criterion from OVERVIEW.md
- **{LLM NAME} Implementation**: Score/assessment with specific evidence
- **Evidence Examples**: Direct quotes and structural examples from specifications
- **Gap Analysis**: Specific differences and their impact on specification quality

**Phase 4: Strategic Assessment**
- Identify distinct strengths and weaknesses of each LLM's approach
- Analyze patterns across multiple specifications (consistency, template adherence, etc.)
- Evaluate which approach better serves AI coding agents (the target audience)
- Assess alignment with project preferences (compression, emoji usage, information density)

**Output Requirements:**
- **Format**: Structured markdown with clear hierarchical headings
- **Evidence**: Include direct quotes from specifications as supporting evidence
- **Scoring**: Provide quantitative scores where evaluation criteria allow
- **Documentation**: Update the results section in OVERVIEW.md with findings
- **Actionability**: Conclude with specific, implementable insights for improving specification quality

**Success Criteria:**
- Complete coverage of all evaluation criteria from OVERVIEW.md
- Evidence-based assessments with specific examples
- Clear identification of which approach better serves the project's needs
- Actionable recommendations that can improve future specification generation
```

## Chain of Thought Prompting

```
Let's work through this step by step.

First, I need to understand [specific aspect].
Then, I'll analyze [second aspect].
Finally, I'll synthesize these findings to [desired outcome].

Walk me through your reasoning process for each step.
```

## Role-Based Prompting

```
You are a [specific role, e.g., "senior software architect with 15 years of experience in distributed systems"].

Your task is to [specific task].

Consider the following constraints and requirements:
- [constraint 1]
- [constraint 2]
- [requirement 1]

Please approach this with the mindset and expertise that your role would bring to this problem.
```

## Few-Shot Learning Examples

```
Here are examples of the format I want:

Example 1:
Input: [example input]
Output: [example output]

Example 2:
Input: [example input]
Output: [example output]

Now, please follow the same pattern for:
Input: [your actual input]
Output:
```

## Constitutional AI Approach

```
Please [perform task] while adhering to these principles:

1. **Accuracy**: Ensure all information is factually correct
2. **Completeness**: Address all aspects of the request
3. **Clarity**: Use clear, unambiguous language
4. **Practicality**: Focus on actionable insights
5. **Context-awareness**: Consider the specific domain and constraints

If you encounter any conflicts between these principles, explain your reasoning and prioritization.
```

## Tree of Thoughts Problem Solving

```
Let's explore multiple solution paths for this problem:

**Problem**: [state the problem clearly]

**Approach 1**: [describe first approach]
- Pros: [list advantages]
- Cons: [list disadvantages]
- Implementation steps: [key steps]

**Approach 2**: [describe second approach]
- Pros: [list advantages]
- Cons: [list disadvantages]
- Implementation steps: [key steps]

**Approach 3**: [describe third approach]
- Pros: [list advantages]
- Cons: [list disadvantages]
- Implementation steps: [key steps]

Now, evaluate these approaches against our criteria: [list evaluation criteria]

Recommend the best approach with justification.
```

## Iterative Refinement Pattern

```
I want to iteratively improve [subject]. Let's start with a basic version and refine it.

**Version 1 (Basic)**: Create a simple [subject] that includes [basic requirements].

After I review this, I'll ask for:
**Version 2 (Enhanced)**: Add [specific improvements]
**Version 3 (Optimized)**: Focus on [optimization criteria]

For each version, explain:
1. What you added/changed
2. Why these changes improve the solution
3. What trade-offs were made
```

## Constraint-Based Design

```
Design [solution] with these hard constraints:
- **Must have**: [non-negotiable requirements]
- **Cannot have**: [forbidden elements]
- **Resource limits**: [budget, time, technical constraints]

And these soft preferences:
- **Should have**: [preferred features]
- **Nice to have**: [optional enhancements]

Explain how your design satisfies all hard constraints and which soft preferences you prioritized and why.
```

## Socratic Method Prompting

```
Don't give me the answer directly. Instead, guide me to the solution by asking probing questions.

The problem I'm trying to solve: [describe problem]

Ask me one question at a time that will help me think through the problem systematically. Each question should build on my previous answers.
```

## Error Analysis and Prevention

```
Analyze potential failure modes for [system/process/solution]:

**High-Risk Failures**:
1. [failure mode] - Probability: [high/medium/low], Impact: [high/medium/low]
   - Root causes: [list causes]
   - Prevention strategies: [list strategies]
   - Detection methods: [how to identify]
   - Recovery procedures: [how to recover]

**Medium-Risk Failures**:
[repeat format]

**Low-Risk Failures**:
[repeat format]

Recommend a prioritized action plan for addressing these risks.
```

## Comparative Analysis Framework

```
Compare [option A] vs [option B] vs [option C] across these dimensions:

| Criteria | Weight | Option A | Option B | Option C | Winner |
|----------|--------|----------|----------|----------|---------|
| [criterion 1] | [weight] | [score/notes] | [score/notes] | [score/notes] | [best option] |
| [criterion 2] | [weight] | [score/notes] | [score/notes] | [score/notes] | [best option] |

**Scoring**: Use 1-10 scale where 10 is best
**Weighting**: Percentage importance (total = 100%)

Provide a weighted final score and recommendation with justification.
```

## Meta-Cognitive Prompting

```
Before solving this problem, let's think about how to think about it:

1. **Problem Classification**: What type of problem is this? (analytical, creative, technical, strategic, etc.)
2. **Approach Selection**: What problem-solving methods would be most effective?
3. **Success Criteria**: How will we know we've solved it well?
4. **Potential Pitfalls**: What common mistakes should we avoid?
5. **Resource Requirements**: What information/tools do we need?

Now, using this meta-analysis, solve: [actual problem]
```

## Idea Honing

```
Ask me one question at a time so we can develop a thorough, step-by-step spec for this idea. Each question should build on my previous answers, and our end goal is to have a detailed specification I can hand off to a developer. Let's do this iteratively and dig into every relevant detail. Remember, only one question at a time.

Here's the idea:

<IDEA>
```

After brainstorming...
```
Now that we've wrapped up the brainstorming process, can you compile our findings into a comprehensive, developer-ready specification? Include all relevant requirements, architecture choices, data handling details, error handling strategies, and a testing plan so a developer can immediately begin implementation.
```

## Context Saving For Long Running Problems

```
I need to move the problem to another LLM. Save the context and prompt to a file called CURRENT-PROBLEM.md for me to use.
```

## Context Saving For Long Running Problems

```
I need to move the problem to another LLM. Save the context and prompt to a file called PROBLEM-SUMMARY.md for me to use.
```

## Code Review Template

```
Please review this code with focus on:

**Functionality**: Does it work as intended?
**Performance**: Are there efficiency concerns?
**Security**: Any security vulnerabilities?
**Maintainability**: Is it readable and well-structured?
**Best Practices**: Does it follow language/framework conventions?

For each issue found, provide:
1. **Issue**: Brief description
2. **Severity**: Critical/High/Medium/Low
3. **Location**: Specific line/function
4. **Recommendation**: How to fix
5. **Example**: Show corrected code if applicable

[CODE TO REVIEW]
```

## Architecture Decision Template

```
We need to make an architectural decision about [topic].

**Context**: [current situation and constraints]

**Options Considered**:
1. **Option 1**: [description]
   - Pros: [benefits]
   - Cons: [drawbacks]
   - Implementation effort: [estimate]

2. **Option 2**: [description]
   - Pros: [benefits]
   - Cons: [drawbacks]
   - Implementation effort: [estimate]

**Decision Criteria**:
- [criterion 1]: [weight/importance]
- [criterion 2]: [weight/importance]

**Recommendation**: [chosen option] because [justification]

**Consequences**: [positive and negative outcomes]
**Mitigation Strategies**: [how to address negative consequences]
```

## Testing Strategy Template

```
Design a comprehensive testing strategy for [system/feature]:

**Unit Tests**:
- Components to test: [list]
- Key scenarios: [critical paths]
- Edge cases: [boundary conditions]

**Integration Tests**:
- Integration points: [systems/services]
- Data flow validation: [key flows]
- Error propagation: [failure scenarios]

**End-to-End Tests**:
- User journeys: [critical paths]
- Cross-system workflows: [complex scenarios]

**Performance Tests**:
- Load scenarios: [expected usage]
- Stress scenarios: [peak usage]
- Scalability targets: [growth expectations]

**Security Tests**:
- Authentication: [auth scenarios]
- Authorization: [permission scenarios]
- Data validation: [input scenarios]

Provide specific test cases for each category with expected outcomes.
````

## Branch Summary Template

```
## Pull Request Technical Review Framework

**Context**: Generate a comprehensive technical review for this A2A Platform pull request that serves both engineering leadership and product stakeholders. The review should demonstrate deep understanding of the technical implementation while clearly articulating business value and operational impact.

### Review Structure

**1. Executive Impact Summary**
- **Title**: Craft a compelling headline that captures the core technical achievement
- **Business Value**: 2-3 sentences explaining how this change directly benefits the platform's reliability, security posture, and developer productivity

**2. End-User & Stakeholder Benefits**
- **Developer Experience**: Concrete improvements to local development workflow
- **Security Enhancements**: Specific security benefits and compliance implications
- **Operational Excellence**: How this reduces friction, improves reliability, or prevents future issues
- Use bullet points with strategic emoji usage for visual hierarchy

**3. Technical Architecture Assessment**
```
- **Implementation Quality**: Evaluate code patterns, configuration management, and architectural decisions
- **Security Engineering**: Analyze the security header middleware changes and CSP policy adjustments
- **Infrastructure Design**: Assess the HTTPS entrypoint script and environment variable strategy
- **Developer Tooling**: Review the pre-commit and Storybook configuration improvements
```
Include relevant code snippets that demonstrate key architectural decisions or potential concerns.

**4. Engineering Excellence Highlights**
Identify 2-3 standout technical decisions that demonstrate:
- Clean separation of concerns
- Robust error handling and validation
- Thoughtful configuration management
- Security-first design principles

**5. Risk Analysis & Mitigation Strategy**
```
- **Low Risk**: Changes with minimal impact and clear rollback paths
- **Medium Risk**: Areas requiring careful deployment coordination or monitoring
- **High Risk**: Critical changes needing extensive validation or phased rollout
```
For each risk level, provide specific mitigation strategies and monitoring recommendations.

**6. Technical Debt & Maintenance Impact**
- Honest assessment of any complexity introduced
- Long-term maintenance considerations
- Documentation or training requirements
- Integration testing implications

**7. Strategic Recommendation**
Provide a clear **APPROVE** or **NEEDS WORK** recommendation with:
- Specific technical justification
- Priority level for deployment
- Any prerequisite changes or follow-up work required

### Analysis Requirements

- **Code-Centric Focus**: Base analysis strictly on the actual diff content, not hypothetical scenarios
- **Platform Context**: Leverage knowledge of GraphQL APIs, Clerk authentication, Django/FastAPI backend, and React frontend architecture
- **Stakeholder Communication**: Write for both senior engineers and product managers who need to understand technical decisions
- **Security Emphasis**: Given the HTTPS nature of these changes, thoroughly evaluate security implications
- **Operational Impact**: Consider deployment, monitoring, and debugging implications

### Quality Standards

- Reference specific file paths, configuration values, and implementation details from the diff
- Balance technical precision with accessible explanations
- Highlight both strengths and potential improvement areas
- Ensure recommendations are actionable and time-bound
- Connect technical implementation decisions to broader platform strategy

**Tone**: Professional, thorough, and constructive. Demonstrate both technical depth and business acumen. The review should read like it comes from a senior technical architect who understands both code quality and operational excellence.
```
