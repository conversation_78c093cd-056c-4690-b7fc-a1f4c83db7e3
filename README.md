# Vedavivi Platform

## Overview

The A2A (Agent-to-Agent) Platform aims to address user hesitation in sharing data with centralized AI systems due to trust concerns. It also tackles the difficulty users face in finding reliable AI agents and the expertise required for personalization, such as building knowledge graphs.

The platform provides a secure environment for discovering, vetting, and executing AI agents, fostering trust and enabling users to leverage AI capabilities without compromising data privacy.

## Problem Addressed

*   **Trust Deficit:** Users are wary of sharing data with traditional, centralized AI systems.
*   **Agent Discovery:** Finding trustworthy and capable AI agents is challenging.
*   **Personalization Barrier:** Creating personalized agent intelligence (e.g., via knowledge graphs) requires significant technical expertise.

## Goal (MVP)

The primary goal for the Minimum Viable Product (MVP) is to launch a platform focusing on:

1.  **Trusted Agent Discovery:** Providing a clear interface to find pre-vetted agents.
2.  **Secure Agent Execution:** Enabling users to run agents within a secure, sandboxed environment.
3.  **Foundation Building:** Establishing core security concepts and foundational agent-to-agent (A2A) secure communication capabilities.

**MVP Success Metrics:**

*   MVP platform live within the target timeframe (4 weeks).
*   A minimum number of users successfully running a pre-built agent shortly after launch.
*   High user satisfaction regarding the perceived security of interactions (≥4/5 score).

## Key Features (MVP)

*   **Agent Discovery UI:** An intuitive interface (ReactJS with ShadCN) to browse pre-vetted agents, showing names, descriptions, and vetting status.
*   **Agent Details:** Dedicated pages providing comprehensive information about each agent's capabilities, requirements, and security context.
*   **Secure Sandbox Execution:** Leveraging Google Agent Development Kit (ADK) for sandboxed code execution, ensuring isolation and resource control.
*   **Input Handling:** Secure interface for users to provide necessary input data to agents.
*   **Execution Feedback:** Clear UI indicators confirming execution within the secure sandbox.
*   **Basic A2A Communication:** Foundational implementation for secure communication between agents, based on research into protocols like Google A2A, IBM ACP, or ADK internal methods.
*   **Agent Vetting Process:** A defined internal process for selecting, vetting (security, functionality, reliability), and publishing the initial set of MVP agents.
*   **CI/CD Pipeline:** Automation using Dagger for building, testing, packaging, and deploying the platform components.

## Architecture Overview

*(Based on ADR: [Issue #4](https://github.com/blkops-collective/a2a-platform/issues/4))*

The proposed architecture follows a modular, layered approach:

1.  **Frontend:** User-facing interface built with ReactJS/TypeScript.
2.  **Backend API Gateway:** Manages user requests, authentication, and orchestration.
3.  **Agent Runtime:** Secure environment (leveraging Google ADK sandboxing, likely on Cloud Run/GKE) where agents execute. Includes:
    *   **User Agents:** Handle user interactions and task coordination.
    *   **Specialized Agents:** Perform specific tasks (e.g., scheduling, data analysis).
4.  **Communication Layer:** Asynchronous messaging (e.g., Google Pub/Sub, Kafka) for decoupling and inter-agent communication. REST/gRPC for synchronous calls where needed.
5.  **Data Stores:**
    *   **Document Store (Firestore/MongoDB):** For user profiles, agent metadata, configurations.
    *   **Vector Database (Vertex AI Vector Search/Pinecone):** For semantic search and agent memory.
    *   **Graph Database (Neo4j):** For knowledge graphs and complex relationships (post-MVP focus).
6.  **CI/CD:** Dagger for building, testing, and deploying containerized components.

### Diagrams

**Agent Roles Flowchart**

```mermaid
graph TD
    subgraph User Interaction
        UI[User Interface]
    end

    subgraph Agent Core
        UA["User Agent (Orchestrator)"]
        SA{{"Specialized Agents (Delegates)"}}
    end

    subgraph Communication
        CB[Communication Bus]
    end

    UI -- High-Level Objective --> CB
    CB -- Objective --> UA

    UA -- Interprets Objective --> UA
    UA -- Maintains Context/Memory --> UA
    UA -- Delegates Tasks --> CB
    CB -- Specific Task Request --> SA

    SA -- Executes Task --> SA
    SA -- Returns Result/Status --> CB
    CB -- Result/Status --> UA

    UA -- Processes Results --> UA
    UA -- Updates UI/User --> CB
    CB -- Status/Result Update --> UI

    style UA fill:#9cf,stroke:#333,stroke-width:2px,color:#333
    style SA fill:#6fc,stroke:#333,stroke-width:2px,stroke-dasharray: 5 5,color:#333
    style CB fill:#fcc,stroke:#333,stroke-width:1px,color:#333
    style UI fill:#ccf,stroke:#333,stroke-width:1px,color:#333
```

**Agent Delegation Sequence Diagram**

```mermaid
sequenceDiagram
    participant UA as User Agent (Orchestrator)
    participant CB as Communication Bus
    participant SA1 as Specialized Agent 1 (e.g., Search)
    participant SA2 as Specialized Agent 2 (e.g., Filter)

    UA->>UA: Receive & Interpret Objective
    UA->>UA: Plan Execution Steps (Requires Search & Filter)

    UA->>+CB: Publish Task 1 (Search Request)
    CB->>+SA1: Deliver Task 1
    SA1->>SA1: Execute Search Task
    SA1->>+CB: Publish Result 1 (Search Results)
    CB->>+UA: Deliver Result 1
    UA->>UA: Process Search Results

    opt Task 2 Depends on Result 1
        UA->>+CB: Publish Task 2 (Filter Request w/ Results)
        CB->>+SA2: Deliver Task 2
        SA2->>SA2: Execute Filter Task
        SA2->>+CB: Publish Result 2 (Filtered Results)
        CB->>+UA: Deliver Result 2
        UA->>UA: Process Filtered Results
    end

    UA->>UA: Consolidate Results, Update Objective Status
    UA->>-CB: Ack Result 1 (Optional)
    UA->>-CB: Ack Result 2 (Optional)
```

## Technology Stack

*   **Frontend:** ReactJS, TypeScript, ShadCN, Bun (package manager), Storybook
*   **Backend/Agents:** Python, Google Agent Development Kit (ADK), `asyncpg`, `psycopg2-binary`
*   **Communication:** Message Queue (e.g., Google Pub/Sub, Redis Queue), REST/gRPC
*   **Databases:** Document Store (e.g., Firestore), Vector DB (e.g., Vertex AI Vector Search), Graph DB (e.g., Neo4j)
*   **CI/CD & Build:** Dagger, Docker, GitHub Actions
*   **Cloud Platform:** Google Cloud (Cloud Run/GKE, IAM, Logging, Monitoring, Secret Manager, Cloud SQL, Cloud SQL Auth Proxy)

## Storybook

Interactive component library and design system for developing and documenting UI components.

📚 **[Complete Storybook Guide](docs/storybook.md)** - Comprehensive setup, usage, and development guide

### Quick Start
```bash
# Start Storybook development server
./scripts/storybook.sh dev --open

# Access at http://localhost:6006
```

For detailed instructions, component catalog, troubleshooting, and advanced features, see the [complete guide](docs/storybook.md).

## CI/CD and Deployment

The A2A Platform utilizes GitHub Actions for continuous integration and deployment to Google Cloud Platform (GCP).

### Workflow Overview

The primary deployment workflow is defined in `.github/workflows/deploy.yml`. This workflow handles:
*   **Linting and Testing:** (Refer to `ci-checks.yml` and pre-commit hooks described below)
*   **Building Docker Images:** For the backend and frontend applications.
*   **Pushing Images to Google Artifact Registry.**
*   **Database Migrations:** Using Alembic, connecting to Cloud SQL via the Cloud SQL Auth Proxy.
*   **Deploying Services:** Deploying the backend and frontend to Cloud Run.

### Connecting to Cloud SQL in CI/CD

To securely connect to Cloud SQL instances from GitHub Actions for tasks like database migrations, the workflow uses the **Cloud SQL Auth Proxy**.
*   The proxy is downloaded and started as a background process within the GitHub Actions runner.
*   Database connection URLs for Alembic (`DB_PROXY_URL`) and the application (`DB_APP_PROXY_URL`) are configured to point to the proxy (e.g., `postgresql+asyncpg://<user>:<password>@127.0.0.1:5432/<db-name>`).

### Environment Configuration and Secrets Management

The platform leverages **GitHub Environments** (e.g., `staging`, `production`) to manage environment-specific configurations and secrets. This approach avoids prefixing secrets (like `STAGING_DB_NAME` or `PRODUCTION_DB_NAME`) directly in the workflow file.

**Required GitHub Secrets:**

*   **Repository/Organization Level Secrets:**
    *   `WORKLOAD_IDENTITY_PROVIDER`: The Workload Identity Federation provider resource name (e.g., `projects/<project-number>/locations/global/workloadIdentityPools/<pool-id>/providers/<provider-id>`).
*   **Environment Level Secrets (for each environment like `staging`, `production`):**
    *   `GCP_PROJECT_ID`: Google Cloud Project ID.
    *   `GCP_SERVICE_ACCOUNT`: Email of the Google Service Account for deployment (e.g., `<EMAIL>`). **Required for CDN infrastructure** - used for Cloudflare access to GCS buckets.
    *   `DB_INSTANCE_NAME`: The Cloud SQL instance ID (e.g., `a2a-platform-staging-xxxx` or `a2a-platform-production-yyyy`). This is used by the Cloud SQL Auth Proxy to identify the correct instance.
    *   `DB_NAME`: The logical name of the Cloud SQL database within the instance (e.g., `a2a-staging-db`, `a2a-production-db`, or a common name like `postgres` if that's what your application connects to).
    *   `DB_USER`: Database user for the connection (e.g., `postgres`).
    *   `DB_PASSWORD`: Password for the database user.
    *   `DB_PROXY_URL`: Database connection string specifically for Alembic migrations, configured to use the Cloud SQL Auth Proxy. It should use a synchronous driver like `psycopg2`. Example: `postgresql+psycopg2://${DB_USER}:${DB_PASSWORD}@127.0.0.1:5432/${DB_NAME}`.
    *   `DB_APP_PROXY_URL`: Database connection string for the application, configured to use the Cloud SQL Auth Proxy. It should use an asynchronous driver like `asyncpg`. Example: `postgresql+asyncpg://${DB_USER}:${DB_PASSWORD}@127.0.0.1:5432/${DB_NAME}`.
    *   `REDIS_URL`: Connection string for Redis (e.g., `redis://<redis-host>:<redis-port>`).
    *   `CLERK_API_KEY`: Clerk Backend API Key.
    *   `CLERK_JWT_PUBLIC_KEY`: Clerk JWT public key (ensure newlines are handled correctly if copied from Clerk dashboard, may need to be base64 encoded or stored in a single line).
    *   `CLERK_WEBHOOK_SECRET`: Clerk webhook signing secret.
    *   `STORAGE_BUCKET`: Name of the Google Cloud Storage bucket.
    *   `CDN_URL`: URL for the Content Delivery Network.
    *   `ARTIFACT_REGISTRY_HOST`: Host for Artifact Registry (e.g., `us-central1-docker.pkg.dev`).
    *   `REPOSITORY`: Name of the Artifact Registry repository (e.g., `a2a-platform-images`).

**Important Considerations:**
*   **Database Existence:** Ensure that the target databases (e.g., `a2a-staging-db`, `a2a-production-db`) are created in their respective Cloud SQL instances before running deployment workflows that execute migrations. The workflow includes a validation step to check for database existence but does not create databases.
*   **Cloud SQL Proxy Authentication:** The proxy authenticates to Cloud SQL using the permissions of the `GCP_SERVICE_ACCOUNT` via Workload Identity Federation. Ensure this service account has the "Cloud SQL Client" role and necessary permissions for your database.
*   **Secret Formatting:** When adding multi-line secrets like `CLERK_JWT_PUBLIC_KEY` to GitHub, ensure they are formatted correctly. It's often best to store them as a single line or use base64 encoding if issues arise.

### Running Database Migrations in CI/CD

Database migrations are automatically applied by the `deploy.yml` workflow during the deployment process for the relevant environment. The workflow:
1.  Authenticates to GCP and sets up the Cloud SQL Auth Proxy.
2.  Uses the `DB_PROXY_URL` (composed from `DB_USER`, `DB_PASSWORD`, `DB_NAME`) secret to configure `alembic.ini`.
3.  Runs `alembic upgrade head`.

## Development and Code Quality

This section provides guidance on setting up your local development environment for the backend and running code quality checks.

### Backend Development Environment

For detailed instructions on setting up the Python backend environment, installing dependencies, and running the backend services locally, please refer to the backend-specific README:

*   [`apps/backend/README.md`](apps/backend/README.md)

Make sure you have Python 3.12 installed and can create a virtual environment. The setup script in `apps/backend/scripts/` will help you install all necessary dependencies, including those for development and linting, from the `apps/backend/pyproject.toml` file.

### Using Pre-commit Hooks

We use pre-commit hooks to automatically check code quality before commits. These hooks run linters, formatters, and security scanners to catch issues early.

To set up pre-commit hooks:

1. Run our setup script to install all required tools:
   ```bash
   ./scripts/setup-dev-env.sh
   ```

2. The hooks will now run automatically when you commit changes.

3. To manually run all checks:
   ```bash
   pre-commit run --all-files
   ```

Our pre-commit configuration includes:
- Python linting and formatting (Ruff)
- Type checking (MyPy)
- Terraform formatting (terraform fmt)
- Terraform validation (terraform validate)
- Security scanning for Terraform (trivy)
- Terraform documentation generation (terraform-docs)

### Running Linters and Type Checking (Backend)

We use several tools to maintain code quality for the Python backend. These tools are configured in `apps/backend/pyproject.toml` and are run as part of our CI pipeline (`.github/workflows/ci-checks.yml`).

To run them locally, first ensure your backend environment is set up and dependencies (including `[dev]` extras) are installed by following the instructions in `apps/backend/README.md`.

Then, navigate to the backend directory and run the following commands:

```bash
cd apps/backend

# Run Ruff (linter and formatter check)
ruff check src/a2a_platform/
ruff format --check src/a2a_platform/

# Run MyPy (static type checker)
mypy -p a2a_platform

# To automatically format with Ruff (optional):
# ruff format src/a2a_platform/
```

These checks are now also part of our pre-commit hooks.

### Frontend Development with Bun

The frontend uses Bun as the package manager and runtime for improved performance. Key commands:

```bash
cd apps/web

# Install dependencies
bun install

# Start development server
bun run dev

# Run tests
bun run test

# Run Storybook
bun run storybook

# Lint and fix code
bun run lint --fix
```

### HTTPS Development Setup

To enable HTTPS in local development:

1. **Generate SSL certificates**:
   ```bash
   ./scripts/generate-ssl-certs.sh
   ```

2. **Configure environment variables** in your `.env` file:
   ```bash
   # Enable HTTPS for frontend
   VITE_USE_HTTPS=true

   # Enable HTTPS for backend (optional)
   ENFORCE_HTTPS=true
   ```

3. **Start development services**:
   ```bash
   # Using Docker Compose
   docker-compose -f docker-compose.dev.yml up -d

   # Or using the development script
   ./scripts/dev.sh
   ```

The development setup now supports both HTTP and HTTPS modes, with automatic SSL certificate validation and enhanced security headers.

## Scripts

This section documents the utility scripts available in the `scripts/` directory to help with development, testing, and deployment tasks.

### `scripts/setup-dev-env.sh`

A script for setting up the development environment with all required tools.

**Purpose:** Installs all necessary tools for development and pre-commit hooks.

**Tools Installed:**
- Python and pip
- pre-commit
- Terraform
- terraform-docs
- trivy (for security scanning)

**Usage:**
```bash
# Install all tools and set up pre-commit hooks
./scripts/setup-dev-env.sh

# Install in CI mode (non-interactive)
./scripts/setup-dev-env.sh --ci
```

**Features:**
- Cross-platform support (macOS, Linux)
- Automatic detection of package managers (brew, apt, yum)
- Skips installation if tools are already present
- Sets up pre-commit hooks automatically

**Dependencies:**
- bash
- Internet connection
- Admin privileges (for some installations)

### `scripts/db-migrate.sh`

A script for running database migrations using Alembic.

**Purpose:** Manages database schema migrations with environment variable support from different .env files.

**Options:**
- `--docker`: Use Docker PostgreSQL instance (default)
- `--local`: Use local PostgreSQL instance
- `--dev`: Load environment variables from .env.dev
- `--test`: Load environment variables from .env.test
- `--help`: Display help message

**Commands:**
- `upgrade [revision]`: Upgrade to the latest revision or specified revision (default command)
- `downgrade [revision]`: Downgrade to the previous revision or specified revision
- `revision [message]`: Create a new revision with the given message
- `current`: Show current revision
- `history`: Show revision history
- `heads`: Show current available heads

**Examples:**
```bash
# Upgrade to latest revision using Docker with .env
./scripts/db-migrate.sh

# Upgrade to latest revision using local PostgreSQL with .env
./scripts/db-migrate.sh --local upgrade

# Upgrade to latest revision using .env.test
./scripts/db-migrate.sh --test upgrade

# Downgrade one revision using Docker with .env
./scripts/db-migrate.sh downgrade -1

# Create a new migration
./scripts/db-migrate.sh revision "Add users table"
```

**Dependencies:**
- Alembic
- PostgreSQL (either local or via Docker)

**Environment Variables:**
The script uses environment variables from the selected .env file to construct the database connection string. If `DATABASE_URL` is defined, it will be used directly. Otherwise, the script will construct the URL from individual variables (`POSTGRES_USER`, `POSTGRES_PASSWORD`, `POSTGRES_DB`, `POSTGRES_HOST`, `POSTGRES_PORT`).

### `scripts/run-backend-tests.sh`

A script for running backend tests with proper environment setup.

**Purpose:** Executes pytest for the backend with configurable test categories and setup options.

**Options:**
- `--unit`: Run only unit tests
- `--integration`: Run only integration tests
- `--e2e`: Run only end-to-end tests
- `--coverage`: Run tests with coverage report
- `--verbose`: Run tests with verbose output
- `--setup`: Perform dependency installation and database setup before running tests
- `--help`: Display help message

**Examples:**
```bash
# Run all tests without setup
./scripts/run-backend-tests.sh

# Run only unit tests without setup
./scripts/run-backend-tests.sh --unit

# Run all tests with coverage without setup
./scripts/run-backend-tests.sh --coverage

# Run all tests with full setup
./scripts/run-backend-tests.sh --setup

# Run only unit tests with full setup
./scripts/run-backend-tests.sh --unit --setup
```

**Dependencies:**
- Python 3.12
- Docker (when using `--setup` flag)
- PostgreSQL (either local or via Docker)

**Integration:**
This script works with `setup-test-db.sh` to prepare the test database. The `--setup` flag optimizes test execution by making dependency installation and database setup optional, which is particularly useful during development when you're running tests frequently and don't need to set up the environment each time.

### `scripts/setup-test-db.sh`

A script for setting up the test database for backend tests.

**Purpose:** Creates and configures the test database with the necessary schema.

**Options:**
- `--docker`: Use Docker PostgreSQL instance (default)
- `--local`: Use local PostgreSQL instance
- `--help`: Display help message

**Examples:**
```bash
# Setup test database using local PostgreSQL
./scripts/setup-test-db.sh --local

# Setup test database using Docker PostgreSQL (default)
./scripts/setup-test-db.sh
```

**Dependencies:**
- PostgreSQL (either local or via Docker)
- Alembic

**Integration:**
This script is called by `run-backend-tests.sh` when the `--setup` flag is used. It ensures the test database exists and has the correct schema for running tests.

### `scripts/run-frontend-tests.sh`

A script for running frontend tests using Jest and Cypress.

**Purpose:** Executes frontend unit and end-to-end tests with various options.

**Options:**
- `--unit`: Run unit tests (default)
- `--e2e`: Run end-to-end tests with Cypress
- `--e2e-open`: Open Cypress test runner UI
- `--watch`: Run tests in watch mode (unit tests only)
- `--coverage`: Run tests with coverage report (unit tests only)
- `--help`: Display help message

**Examples:**
```bash
# Run unit tests
./scripts/run-frontend-tests.sh

# Run end-to-end tests
./scripts/run-frontend-tests.sh --e2e

# Run unit tests in watch mode
./scripts/run-frontend-tests.sh --watch
```

**Dependencies:**
- Bun (preferred) or npm
- Node.js

**Integration:**
This script works with the frontend testing infrastructure to run tests. It can be used in CI/CD pipelines or for local development.

### `scripts/app-runner.sh`

A script for managing Docker containers for the A2A Platform.

**Purpose:** Sets up, manages, stops, and checks the services defined in docker-compose.yml.

**Commands:**
- `start` (default): Start the services
- `stop`: Stop the services
- `status`: Show status of running services

**Options:**
- `--build`: Force rebuild of all containers (start only)
- `--backend-only`: Target only the backend services (PostgreSQL, Redis, backend)
- `--frontend-only`: Target only the frontend service
- `--clean`: Remove existing containers before starting (start only)
- `--dev-frontend`: Use development mode for frontend with hot reloading (start only)
- `--help`: Display help message

**Examples:**
```bash
# Start all services
./scripts/app-runner.sh

# Rebuild and start all services
./scripts/app-runner.sh --build

# Clean and start all services
./scripts/app-runner.sh --clean

# Stop all services
./scripts/app-runner.sh stop

# Show status of all services
./scripts/app-runner.sh status
```

**Dependencies:**
- Docker
- Docker Compose

**Integration:**
This script is the main entry point for managing the application's Docker containers. It's used by other scripts like `dev.sh` to start specific services.

### `scripts/dev.sh`

A wrapper script for starting the frontend in development mode.

**Purpose:** Simplifies starting the frontend with hot reloading for development.

**Options:**
- `--clean`: Remove existing containers before starting
- `--build`: Force rebuild of the frontend container
- `--help`: Display help message

**Examples:**
```bash
# Start frontend in dev mode
./scripts/dev.sh

# Rebuild and start frontend in dev mode
./scripts/dev.sh --build

# Clean and start frontend in dev mode
./scripts/dev.sh --clean
```

**Dependencies:**
- Docker
- Docker Compose

**Integration:**
This script calls `app-runner.sh` with the appropriate flags to start the frontend in development mode.


### `scripts/fix-web-permissions.sh`

A script for fixing Docker container permission issues in the web development environment.

**Purpose:** Resolves permission issues that can occur when running the frontend development environment in Docker containers.

**Examples:**
```bash
# Fix permissions for web development
./scripts/fix-web-permissions.sh
```

**Dependencies:**
- Docker
- Docker Compose

**Integration:**
This script is automatically called by enhanced entrypoint scripts but can also be run manually when permission issues occur during development.

### `scripts/db-migrate.sh`

A script for running database migrations using Alembic.

**Purpose:** Manages database schema migrations for development and test databases.

**Options:**
- `--docker`: Use Docker PostgreSQL instance (recommended)
- `--local`: Use local PostgreSQL instance
- `--test`: Use test database instead of development database
- `--help`: Display help message

**Commands:**
- `upgrade [revision]`: Upgrade to the latest revision or specified revision (default)
- `downgrade [revision]`: Downgrade to the previous revision or specified revision
- `revision [message]`: Create a new revision with the given message
- `current`: Show current revision
- `history`: Show revision history
- `heads`: Show current available heads

**Examples:**
```bash
# Recommended: Use Docker for consistent environment
# Start the database container first
docker compose up -d db

# Upgrade to latest revision using Docker database
./scripts/db-migrate.sh --docker upgrade head

# Check current migration state
./scripts/db-migrate.sh --docker heads
./scripts/db-migrate.sh --docker history

# Create a new migration (use descriptive timestamps)
./scripts/db-migrate.sh --docker revision "YYYYMMDDHHMMSS_descriptive_name"

# Downgrade one revision
./scripts/db-migrate.sh --docker downgrade -1

# Work with test database
./scripts/db-migrate.sh --docker --test upgrade head
```

**Best Practices:**
1. Always use Docker for consistent environments (`--docker` flag)
2. Use timestamp-based naming for migrations (YYYYMMDDHHMMSS_description)
3. Test migrations by running both upgrade and downgrade
4. Use a clean test database
5. Verify data integrity
   ```bash
   # Test with a clean database
   docker compose -f docker-compose.test.yml up -d db
   ./scripts/db-migrate.sh --docker --test upgrade head
   ```

6. **Handling Multiple Heads**
   ```bash
   # Check current state
   ./scripts/db-migrate.sh --docker heads

   # View history
   ./scripts/db-migrate.sh --docker history

   # Merge if needed
   ./scripts/db-migrate.sh --docker merge -m "merge_branches" rev1 rev2
   ```

**Integration:**
This script is essential for managing database schema changes. Use it whenever you need to:
- Apply pending migrations to your database
- Create new migrations for schema changes
- Check migration status
- Troubleshoot migration issues

**Note on CI/CD Migrations:**
While this script is for local development and testing, database migrations in the CI/CD pipeline (GitHub Actions) are handled automatically. The pipeline sets up a Cloud SQL Auth Proxy and uses environment-specific database URLs (via GitHub secrets) to run `alembic upgrade head`. Refer to the "CI/CD and Deployment" section for more details.

**Troubleshooting:**
If you encounter multiple heads:
1. Check current state: `./scripts/db-migrate.sh --docker heads`
2. View history: `./scripts/db-migrate.sh --docker history`
3. Either merge heads or ensure linear history in migration files

## Development

### Database Migrations

The A2A Platform uses Alembic for database migrations. Here's what you need to know:

#### Quick Start

1. Start the database container for the appropriate environment:
   ```bash
   # For default environment
   docker compose up -d db

   # For development environment
   docker compose -f docker-compose.dev.yml up -d db

   # For testing environment
   docker compose -f docker-compose.test.yml up -d db
   ```

2. Run migrations:
   ```bash
   # Apply all pending migrations (uses Docker by default)
   ./scripts/db-migrate.sh upgrade head

   # For test environment
   ./scripts/db-migrate.sh --test upgrade head

   # Create a new migration
   ./scripts/db-migrate.sh revision "YYYYMMDDHHMMSS_description"
   ```

#### Environment Configuration

When working with the A2A Platform, it's important to use the appropriate Docker Compose configuration file for your current task:

- **Default environment** (`docker-compose.yml`): Use for general development work
- **Development environment** (`docker-compose.dev.yml`): Use for development with additional services or custom configurations
- **Testing environment** (`docker-compose.test.yml`): Use for running tests or when you need an isolated test database

The `db-migrate.sh` script supports loading environment variables from different `.env` files to match these environments:

```bash
# Default: Uses .env file in project root
./scripts/db-migrate.sh upgrade head

# Use .env.dev file
./scripts/db-migrate.sh --dev upgrade head

# Use .env.test file
./scripts/db-migrate.sh --test upgrade head

# Use local PostgreSQL instead of Docker
./scripts/db-migrate.sh --local upgrade head
```

The script will use environment variables from these files to construct the database connection string. If `DATABASE_URL` is defined in the environment file, it will be used directly. Otherwise, the script will construct the URL from individual variables (`POSTGRES_USER`, `POSTGRES_PASSWORD`, `POSTGRES_DB`, `POSTGRES_HOST`, `POSTGRES_PORT`).

#### Best Practices

1. **Use Environment Files**
   Store database configuration in `.env` files instead of hardcoding values.

2. **Migration Naming**
   Use timestamp-based names (YYYYMMDDHHMMSS_description):
   ```
   # Good
   20230901123456_add_user_profile.py

   # Bad
   add_column.py
   ```

3. **Testing Migrations**
   - Test both upgrade and downgrade
   - Use a clean test database
   - Verify data integrity
   ```bash
   # Start a clean test database
   docker compose -f docker-compose.test.yml up -d db

   # Apply migrations to the test database
   ./scripts/db-migrate.sh --test upgrade head

   # Test downgrade (optional)
   ./scripts/db-migrate.sh --test downgrade -1
   ```

4. **Handling Multiple Heads**
   ```bash
   # Check current state (use --test for test environment)
   ./scripts/db-migrate.sh heads

   # View history
   ./scripts/db-migrate.sh history

   # Merge if needed
   ./scripts/db-migrate.sh merge -m "merge_branches" rev1 rev2

   # Apply merged migration
   ./scripts/db-migrate.sh upgrade head
   ```

   Remember to use the appropriate environment flag (`--test` or `--dev`) when working with different environments.

### Redis Queue (RQ) Workers

The A2A Platform uses Redis Queue (RQ) for background task processing. Here's how to use it:

#### Quick Start

1. Start/stop workers:
   ```bash
   # Start workers
   ./scripts/manage-rq-workers.sh --action start

   # Stop workers
   ./scripts/manage-rq-workers.sh --action stop

   # Check status
   ./scripts/manage-rq-workers.sh --action status
   ```

2. Worker Configuration:
   ```bash
   # Start with custom settings
   ./scripts/manage-rq-workers.sh --action start --workers 4 --queues "default high-priority"
   ```

#### Implementation

1. **Environment Variables**
   - `RQ_WORKER_COUNT`: Number of workers (default: 2)
   - `RQ_DEFAULT_TIMEOUT`: Job timeout in seconds (default: 180)
   - `RQ_DEFAULT_TTL`: Result TTL in seconds (default: 86400)

2. **Using RQ in Code**
   ```python
   from a2a_platform.messaging.queue_factory import create_queue_client

   # Create RQ client
   queue_client = await create_queue_client(queue_type="rq")

   # Send a message
   message_id = await queue_client.send_message(
       queue_name="default",
       message_body="Hello, world!",
       message_attributes={"priority": "high"}
   )
   ```

3. **Managing Workers with Docker**
   In development, workers start automatically with:
   ```bash
   # Default environment
   docker compose up -d rq-worker

   # Development environment
   docker compose -f docker-compose.dev.yml up -d rq-worker

   # Testing environment
   docker compose -f docker-compose.test.yml up -d rq-worker
   ```
   Use the appropriate Docker Compose configuration file for your current environment.

#### Database Changes and Workers

When making database changes that affect RQ workers:

1. Stop the workers:
   ```bash
   ./scripts/manage-rq-workers.sh --action stop
   ```

2. Apply database migrations:
   ```bash
   ./scripts/db-migrate.sh --docker upgrade head
   ```

3. Start the workers:
   ```bash
   ./scripts/manage-rq-workers.sh --action start
   ```

For more examples and details, see `apps/backend/examples/rq_messaging_example.py`.

## Frontend CDN Deployment

The A2A Platform frontend is deployed using a CDN + Cloud Storage architecture for improved performance, cost efficiency, and reliability.

### Architecture

The frontend deployment architecture consists of:

1. **Google Cloud Storage (GCS)** - Stores static assets (HTML, CSS, JS, images)
2. **Cloudflare CDN** - Distributes assets globally with edge caching
3. **WebSocket Proxy** - Handles real-time features via Cloudflare Spectrum

### Benefits

- **Performance:** 60-75% faster global load times (TTFB reduced to 50-150ms)
- **Cost:** 70% reduction in monthly costs compared to Docker/Cloud Run
- **Reliability:** 99.99% availability with global edge distribution
- **Security:** Reduced attack surface with immutable static deployments

### Deployment Process

The frontend deployment is fully automated via GitHub Actions:

1. **Infrastructure:** Managed via Terraform in `terraform/` directory
2. **CI/CD:** `.github/workflows/frontend-cdn-deploy.yml` handles builds and deployments
3. **Verification:** Performance metrics are validated against targets

For detailed information, see the [CDN Migration Guide](docs/cdn-migration-guide.md).

### Rollback Procedure

If needed, rollback to the previous Cloud Run deployment:

```bash
# Rollback staging environment
export CLOUDFLARE_API_TOKEN=your_token
export STAGING_CLOUDFLARE_ZONE_ID=your_zone_id
./scripts/rollback-cdn-migration.sh -e staging

# Rollback production environment
export CLOUDFLARE_API_TOKEN=your_token
export CLOUDFLARE_ZONE_ID=your_zone_id
./scripts/rollback-cdn-migration.sh -e production
```
