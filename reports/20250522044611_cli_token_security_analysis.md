## VEDAVIVI CLI Token Management Security Analysis

This analysis reviews the VEDAVIVI platform's CLI token management based on the provided `ADR-002-Backend-Overview.md` document, focusing on potential security vulnerabilities and areas for further investigation.

### 1. Token Generation

*   **How tokens are generated, length, complexity ([cite: 407]):**
    *   The ADR states tokens are "web-generated." The exact generation mechanism, length, and complexity (entropy) of the raw token (before hashing) are not specified.
    *   It is recommended to use a cryptographically secure pseudo-random number generator (CSPRNG) for token generation to ensure sufficient entropy.
    *   Tokens should have a minimum length of 32 characters and include a mix of uppercase letters, lowercase letters, numbers, and special characters to maximize complexity and reduce predictability.
*   **Concern:** If tokens are not generated using a cryptographically secure pseudo-random number generator (CSPRNG), or if they are too short or predictable, they could be guessed or brute-forced.
*   **Questions for Audit:**
    *   Q1: What is the exact process for generating CLI tokens? Which library or method is used?
    *   Q2: What is the length and character set of the raw CLI tokens before hashing?
    *   Q3: Is a CSPRNG used for token generation to ensure sufficient entropy?

### 2. Token Storage

*   **Hashing and Algorithm ([cite: 407, 484]):**
    *   The `cli_tokens.token_hash` column (TEXT, NOT NULL, UNIQUE) stores a hash of the token.
    *   The notes explicitly state: "**HASHED** (e.g., bcrypt) value of the generated CLI token." and "Store only a strong hash of the token, never the plaintext."
    *   This indicates an awareness of the need for strong hashing. The mention of "e.g., bcrypt" is positive.
*   **Concern:**
    *   While "e.g., bcrypt" is good, it's not a definitive statement that bcrypt *is* used. If a weaker algorithm (MD5, SHA1) or an unsalted/improperly configured modern algorithm is used, the hashes could be vulnerable to offline cracking (rainbow tables, dictionary attacks).
*   **Questions for Audit:**
    *   Q4: What specific hashing algorithm is implemented for `token_hash`? Is it bcrypt, scrypt, Argon2, or something else?
    *   Q5: If bcrypt is used, what is the configured cost factor? If another algorithm is used, are its parameters (salt, iterations, memory/parallelism) configured to industry best practices?
    *   Q6: Is a unique salt used for each token hash, or is there a system-wide salt (which is less ideal)? (Standard bcrypt usage implies per-hash salt).

### 3. Token Validation

*   **How tokens are validated:**
    *   The ADR mentions "backend middleware validates tokens (hashed)." This implies the client sends the raw token, and the backend hashes it for comparison against the stored `token_hash`.
*   **Timing Attack Protection:**
    *   Not specified in the documentation.
*   **Concern:**
    *   If the hash comparison is not done in a constant-time manner, it could be vulnerable to timing attacks, allowing an attacker to potentially infer the hash character by character based on response times.
*   **Questions for Audit:**
    *   Q7: How is the comparison between the hash of the received token and the stored `token_hash` performed? Is a constant-time comparison function used?
    *   Q8: What is the exact process flow in the backend middleware for token validation?

### 4. Token Revocation

*   **Revocation Mechanism ([cite: 407]):**
    *   The `cli_tokens` table has a `revoked_at` (TIMESTAMPTZ, NULL) column.
    *   The GraphQL schema includes a mutation `revokeCliToken(id: ID!): Boolean!`.
    *   This suggests a clear mechanism for marking tokens as revoked.
*   **Immediate Revocation:**
    *   Setting `revoked_at` should effectively lead to immediate revocation if validation logic checks this field.
*   **Concern:**
    *   The validation logic must explicitly check that `revoked_at IS NULL` for a token to be considered valid.
*   **Questions for Audit:**
    *   Q9: Does the token validation middleware correctly and consistently check that the `revoked_at` field is NULL for the token being validated?
    *   Q10: What is the user experience for revoking a token? Is it easily accessible?

### 5. Token Lifecycle & Management

*   **UI/API for Management:**
    *   GraphQL schema shows `myCliTokens: [CliToken!]` query and `createCliToken(description: String): CliToken!`, `revokeCliToken(id: ID!): Boolean!` mutations. This implies users can list, create, and revoke their tokens.
    *   The `token_prefix` column (TEXT, NOT NULL, "Short, non-sensitive prefix (e.g., first 8 chars) for display") is a good usability feature for users to identify their tokens.
    *   The `description` field also aids in management.
*   **Token Expiry ([cite: 407]):**
    *   The `cli_tokens` table has an `expires_at` (TIMESTAMPTZ, NULL) column with the note "Optional expiry date (non-expiring for MVP)."
    *   **Concern:** For MVP, tokens are non-expiring by default. Long-lived tokens increase the risk if compromised. While understandable for an MVP, a plan for enforcing expiry should be considered.
*   **Rate Limiting/Monitoring:**
    *   Not specified in the documentation.
    *   **Concern:** Without rate limiting on token validation attempts or monitoring for rapid validation failures, the system might be more susceptible to brute-force attacks against tokens (even though they are hashed, the raw token is sent by the client).
*   **Questions for Audit:**
    *   Q11: Is there a plan to implement mandatory token expiry post-MVP?
    *   Q12: Is there any rate-limiting on token validation attempts (per token, per user, per IP)?
    *   Q13: Is there any monitoring or alerting for suspicious activity related to CLI token usage (e.g., multiple failed validation attempts, use from unusual locations)?
    *   Q14: What is the process for handling a compromised CLI token if detected?

### 6. Permissions/Scope

*   **Token Permissions:**
    *   The documentation does not mention any specific permission or scoping mechanism for CLI tokens. They appear to grant the same level of access as the user who owns them.
*   **Concern:**
    *   If CLI tokens grant full user privileges, the compromise of a CLI token is equivalent to a full account compromise for the CLI context. Implementing least privilege (e.g., read-only tokens, tokens for specific actions) would be a significant security enhancement.
*   **Questions for Audit:**
    *   Q15: Do CLI tokens grant the full permissions of the associated user account?
    *   Q16: Is there a plan to introduce scoped/permissioned CLI tokens in the future to limit their capabilities?

### Summary & Recommendations for Deeper Investigation:

The VEDAVIVI CLI token management system, as described in `ADR-002`, incorporates several good security practices: storing hashed tokens, providing revocation capability, and offering user management features via GraphQL. However, several areas require deeper investigation in a real audit:

1.  **Token Generation:** Verify the cryptographic strength of the raw token generation process.
2.  **Hashing Implementation:** Confirm the exact hashing algorithm (hopefully bcrypt or similar) and its configuration (salt, cost factor).
3.  **Validation Logic:** Ensure constant-time hash comparison to prevent timing attacks and that `revoked_at` and `expires_at` fields are correctly checked.
4.  **Token Expiry:** Strongly recommend implementing mandatory expiry for tokens post-MVP.
5.  **Rate Limiting & Monitoring:** Investigate the feasibility of implementing rate limiting and suspicious activity monitoring for token validation.
6.  **Token Scoping:** Explore the introduction of permissioned/scoped CLI tokens to adhere to the principle of least privilege. This is a crucial enhancement for improving security posture.

The current design relies on the user keeping their raw token secret and the backend securely hashing and validating it. The "non-expiring for MVP" and lack of explicit scoping are the most significant areas of concern from a security best-practice standpoint for long-term use.
