## VEDAVIVI Platform: WebSocket Security Analysis for GraphQL Subscriptions

This analysis assesses the security of WebSocket communication used for GraphQL subscriptions in the VEDAVIVI platform, based on `ADR-002-Backend-Overview.md` and common security best practices.

### 1. Connection Establishment & Authentication ([cite: 426])

*   **Use of WSS (WebSocket Secure):**
    *   `ADR-002 (Sec 5, Security Principles)` states "HTTPS mandatory." While this applies to HTTP, it strongly implies that its secure counterpart, **WSS (WebSocket Secure), should also be mandatory** for all WebSocket connections to ensure encryption in transit.
    *   *Concern:* The ADR does not explicitly state "WSS mandatory." If unencrypted WS is allowed, all transmitted data, including authentication tokens and subscription data, would be vulnerable to sniffing.
    *   *Recommendation:* Explicitly mandate and enforce WSS for all WebSocket connections.

*   **User Authentication over WebSockets:**
    *   `ADR-002 (Sec 2.5)`: "Back<PERSON> manages WebSocket connections for GraphQL, **authenticated via Clerk or VEDAVIVI CLI Token**."
    *   **Mechanism of Token Transmission:** The ADR does **not specify** *how* the Clerk JWT or VEDAVIVI CLI Token is transmitted during the WebSocket upgrade request (e.g., in a `Sec-WebSocket-Protocol` header, as a query parameter during the handshake, or via an initial HTTP authentication that is then upgraded to a WebSocket session).
        *   *Concern:* Transmitting tokens insecurely (e.g., as a query parameter that might get logged by intermediaries) would expose them. The method chosen should be secure. Using the `Authorization` header during the HTTP upgrade request is a common and secure method if the WebSocket library and server support it. Alternatively, a short-lived, single-use token obtained via a separate authenticated HTTP request could be passed in the subprotocol or as a query parameter for the upgrade.
    *   **Token Validation:** The ADR implies that the same backend validation logic used for regular HTTP requests (Clerk SDK for JWTs, hashed token comparison for CLI tokens, as per `ADR-002, Sec 2.2`) would be applied during or immediately after the WebSocket connection setup.
        *   *Critical Concern (from previous analyses):* The `X-Clerk-User-Id` header bypass vulnerability in the `ClerkAuthMiddleware` would also impact WebSocket authentication if the authentication logic for WebSockets reuses or is affected by this flawed middleware path for initial token validation before or during the upgrade. If an attacker can establish a WebSocket connection by spoofing `X-Clerk-User-Id`, they could potentially subscribe to any user's data.
    *   *Recommendation:*
        *   Specify and implement a secure method for transmitting authentication tokens during the WebSocket handshake (e.g., `Authorization` header during upgrade, or a secure, single-use token mechanism).
        *   Ensure that the token validation logic for WebSockets is robust and **not** susceptible to the `X-Clerk-User-Id` bypass.

### 2. Authorization for Subscriptions

*   **Enforcement for Specific Subscriptions (e.g., `newMessages` [cite: 465]):**
    *   `ADR-002 (GraphQL Schema)` defines `newMessages(conversationId: UUID!): ChatMessage!`.
    *   **Mechanism:** The ADR does **not explicitly detail** the authorization logic within the `newMessages` subscription resolver or connection handler.
    *   **Expected Practice:** Upon a client requesting to subscribe to `newMessages` for a particular `conversationId`, the backend must:
        1.  Identify the `clerk_user_id` associated with the authenticated WebSocket session.
        2.  Verify that this `clerk_user_id` is authorized to access messages for the given `conversationId` (e.g., is a participant in that conversation). This check must occur *before* the subscription is allowed and messages start flowing.
*   **Application of OBAC (`clerk_user_id` based):**
    *   **Intent:** The platform's OBAC, based on `clerk_user_id` (`ADR-002, Sec 2.2`), is intended to apply to all data access, including subscription data.
    *   **Effectiveness:** This depends entirely on the correct implementation of authorization checks within each subscription resolver/handler.
    *   *Concern:* If these checks are missing or flawed for any subscription, a user could potentially subscribe to and receive real-time updates (e.g., messages, notifications) intended for another user, leading to a significant data breach. The `X-Clerk-User-Id` bypass would directly facilitate this.
*   **Recommendation:**
    *   Implement and audit strict authorization checks within each GraphQL subscription resolver/handler to ensure that the authenticated user is only allowed to subscribe to data they are permitted to access based on their `clerk_user_id` and the specific resource ID (e.g., `conversationId`).
    *   Ensure that changes in user permissions or session status (e.g., revocation of access to a conversation) result in the termination of relevant active subscriptions.

### 3. Data Transmission Security

*   **Encryption in Transit:** Assuming WSS is enforced, data transmitted over WebSockets will be encrypted by TLS.
*   **Sensitive Information in Messages:**
    *   The primary data for `newMessages` is chat content (`ChatMessage.content` which is JSONB). This content can be highly sensitive.
    *   *Concern:* While WSS encrypts the WebSocket channel, if WSS is not enforced or if there are client-side vulnerabilities (e.g., user installing a malicious root CA, client software not verifying certs properly – though these are client-side issues), the data could be intercepted. Application-level encryption for the message content itself is generally not performed over WSS due to complexity and performance overhead, relying on TLS for protection. The primary risk here is a failure to enforce WSS.
*   **Recommendation:**
    *   Strictly enforce WSS for all WebSocket communications.
    *   Ensure client applications are configured to validate TLS certificates properly.

### 4. Tracking WebSocket Connections in Redis ([cite: 427, 489])

*   **Data Stored in Redis:**
    *   `ADR-002 (Sec 4, Redis Usage)`: "WebSocket Connections: Track active GraphQL Subscription connections state (User ID -> Server/Connection ID)."
    *   The task description provides a more detailed (though not directly from ADR) structure for `websocket_connections` in Redis: `connection_id` (PK), `user_id` (FK to users table), `server_id` (identifier of the backend server handling the connection), `created_at`.
*   **Protection of Data in Redis:**
    *   **Mechanism:** `ADR-002` does **not specify** security measures for the Redis instance itself (e.g., authentication, TLS for connections to Redis, encryption at rest within Redis).
    *   *Concern:* If Redis is not secured (e.g., no password, unencrypted connections within the internal network), an attacker gaining internal network access could potentially:
        *   Read the `websocket_connections` data, mapping active `connection_id`s to `user_id`s and `server_id`s, giving them an overview of active user sessions and distribution.
        *   Modify or delete this data, potentially disrupting WebSocket message delivery or causing DoS for real-time features.
*   **Usage of this Information:**
    *   This data is likely used by the backend to manage the fan-out of messages for GraphQL subscriptions. When a new event occurs (e.g., a new chat message), the backend can identify which users are subscribed to that event (e.g., participants of a conversation) and then use the Redis store to find their active `connection_id`s and the `server_id`s handling those connections, so the message can be routed efficiently.
    *   *Concern:* If `connection_id` were predictable or easily guessable (UUIDs are generally not), and if messages were targeted directly by `connection_id` without further checks, it could theoretically be abused if an attacker could inject messages into the routing system. However, the primary risk is unauthorized access/modification of the Redis data itself.
*   **Recommendation:**
    *   **Q1:** Secure the Redis instance:
        *   Enable strong authentication (`REQUIREPASS`).
        *   Configure TLS for connections to Redis if supported and network environment warrants it.
        *   Consider encryption at rest for Redis data if available and deemed necessary based on risk.
    *   **Q2:** Ensure `connection_id` is a cryptographically secure random UUID to prevent guessing.

### 5. Denial of Service (DoS) / Resource Exhaustion

*   **Mechanism:** `ADR-002` does not explicitly mention limits on the number of active WebSocket connections (per user or globally), the rate of messages sent over WebSockets, or the rate of subscription requests. The general principle "Rate limiting recommended" (`ADR-002, Sec 5`) may not specifically cover WebSocket traffic or connection limits.
*   *Concern:* Without such limits, the platform could be vulnerable to:
    *   Attackers opening a vast number of WebSocket connections, exhausting server memory or connection slots.
    *   Attackers sending a high volume of messages over established connections, consuming CPU/bandwidth.
    *   Attackers rapidly creating and tearing down subscriptions.
*   **Recommendation:**
    *   **Q3:** Implement limits on the number of concurrent active WebSocket connections per user and globally.
    *   **Q4:** Implement rate limiting on incoming messages over WebSockets and on the frequency of new subscription requests per user/connection.
    *   Monitor WebSocket connection counts and message rates.

### 6. Cross-Site WebSocket Hijacking (CSWSH)

*   **Mechanism:** `ADR-002` does not mention if origin checks are performed on WebSocket HTTP upgrade requests.
*   **CSWSH Attack:** A malicious website could attempt to trick a user's browser (already authenticated to VEDAVIVI via cookies, if WebSockets use cookie-based auth for upgrade, or if the token is available to JavaScript that can be compromised by XSS) into establishing a WebSocket connection to the VEDAVIVI backend. The malicious site could then send/receive messages through this hijacked connection.
*   *Concern:* If the `Origin` header of the WebSocket upgrade request is not validated against a whitelist of allowed origins, CSWSH attacks are possible.
*   **Recommendation:**
    *   **Q5:** Implement `Origin` header validation for all WebSocket upgrade requests. Only allow connections from trusted, whitelisted domains.

### Summary & Recommendations

The use of WebSockets for GraphQL subscriptions in VEDAVIVI introduces specific security considerations. While the ADRs mention authentication, the details are sparse, and several standard WebSocket security practices are not explicitly confirmed.

**Key Findings & Concerns:**

1.  **WSS Enforcement & Token Transmission:** Lack of explicit confirmation for WSS and no details on the secure method for transmitting auth tokens during WebSocket handshake.
2.  **Critical Authentication Bypass:** The `X-Clerk-User-Id` header vulnerability poses a severe threat to WebSocket authentication if the same flawed logic path is involved.
3.  **Subscription Authorization:** Details on how OBAC is enforced for specific subscriptions (e.g., `newMessages`) are missing.
4.  **Redis Security:** The Redis instance storing connection metadata needs to be secured (auth, encryption).
5.  **DoS/Resource Exhaustion:** Lack of defined limits for WebSocket connections or message rates.
6.  **CSWSH Protection:** Origin checks for WebSocket upgrade requests are not mentioned.

**Recommendations for Deeper Investigation & Remediation:**

1.  **Enforce WSS and Secure Token Transmission:**
    *   Mandate WSS for all WebSocket connections.
    *   Define and implement a secure method for transmitting authentication tokens during the handshake. Ensure this is not vulnerable to the `X-Clerk-User-Id` bypass.
2.  **Implement Robust Subscription Authorization:**
    *   Audit/implement strict authorization checks in all GraphQL subscription resolvers to ensure users can only subscribe to data they are permitted to access (OBAC).
3.  **Secure Redis Instance (Q1):** Implement authentication, and consider encryption in transit/at rest for Redis. Ensure `connection_id` is a CSPRNG UUID (Q2).
4.  **Implement WebSocket DoS/Resource Limits (Q3, Q4):** Define and enforce limits on concurrent connections and message/subscription rates.
5.  **Implement CSWSH Protection (Q5):** Validate the `Origin` header for all WebSocket upgrade requests against a whitelist.
6.  **Logging and Monitoring:** Implement logging for WebSocket connection attempts (successful and failed), subscription requests, and errors. Monitor for anomalous activity.

Addressing these recommendations will significantly enhance the security of VEDAVIVI's real-time communication features.
