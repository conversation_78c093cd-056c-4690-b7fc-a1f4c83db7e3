## VEDAVIVI Platform: File Upload Security Analysis

This analysis evaluates the security of the file upload mechanism in the VEDAVIVI platform, based on `ADR-002-Backend-Overview.md`. It covers pre-signed URLs for uploads, CDN signed URLs for access, file validation, avatar processing, and the security of stored content.

### 1. Direct-to-CDN Pre-signed URL Mechanism for Uploads ([cite: 69, 419, 420])

The platform uses pre-signed URLs for direct uploads to object storage (AWS S3, Google Cloud Storage, or Cloudflare R2), with metadata managed in a PostgreSQL `file_metadata` table.

*   **Generation of Pre-signed URLs:**
    *   **Process:** Generated via a GraphQL mutation `requestFileUploadUrl` (`ADR-002, Sec 2.4`).
    *   **Details Not Specified:**
        *   The specific library/method for pre-signed URL generation.
        *   Permissions granted by the URL (should be strictly limited to `PUT` for the specific object key).
        *   The expiry time of these upload URLs.
    *   *Concern:* If URLs are long-lived, they increase the risk window if leaked. If permissions are too broad (e.g., allowing read or delete), they could be abused. Standard practice is for these URLs to be short-lived (e.g., minutes).

*   **Authentication/Authorization for URL Generation:**
    *   **Process:** The `requestFileUploadUrl` GraphQL mutation is subject to standard platform authentication (Clerk JWT or VEDAVIVI CLI Token) (`ADR-002, Sec 2.2`).
    *   **Implication:** Only authenticated users can request to upload a file.
    *   *Concern:* No concerns here if the primary authentication is secure (though the `X-Clerk-User-Id` bypass previously noted is a general threat to all authenticated actions).

*   **`file_metadata` Creation & Linking ([cite: 476]):**
    *   **Process:**
        1.  Client calls `requestFileUploadUrl`. A `file_metadata` record is likely created with `user_id` (of the authenticated user), a generated `object_key`, and `upload_status: 'pending'` (`ADR-002, file_metadata` table).
        2.  Client uploads directly to object storage using the pre-signed URL.
        3.  Client calls `completeFileUpload` GraphQL mutation to confirm the upload, and the backend updates the `file_metadata.upload_status` to `'confirmed'` (`ADR-002, Sec 2.4`).
    *   **Linkage:** The `file_metadata` record links the `user_id` to the `object_key` and other details like `original_filename`, `mime_type`, `file_size_bytes`, `purpose`.
    *   *Concern:* The process seems robust for preventing orphaned files if `completeFileUpload` is not called (they remain 'pending' and can be cleaned up). Ensuring the `user_id` is correctly captured from the authenticated session and immutably stored in `file_metadata` is crucial for OBAC.

### 2. Accessing Files via CDN Signed URLs ([cite: 421])

*   **Generation of CDN Signed URLs:**
    *   **Process:** "Access via CDN Day 1 using signed CDN URLs (retrieved via GraphQL)" (`ADR-002, Sec 2.4`).
    *   **Details Not Specified:**
        *   The mechanism for generating these CDN-specific signed URLs (e.g., using AWS CloudFront signed URLs, Google Cloud CDN signed URLs).
        *   The expiry times for these access URLs.
    *   *Concern:* If these URLs are long-lived, they can be shared or leaked, bypassing normal access controls for the duration of their validity. They should be short-lived.

*   **IDOR Vulnerabilities & Authorization for Access:**
    *   **Process:** A user requests access to a file via a GraphQL query, which then returns a CDN signed URL. This GraphQL query must perform authorization to ensure the authenticated user is permitted to access the file identified by the `file_id` or `object_key`.
    *   **Nature of CDN Signed URL:** Typically, a CDN signed URL acts as a temporary bearer token for a specific resource. Anyone possessing a valid (non-expired) signed URL can access the resource directly from the CDN.
    *   *Concern:*
        *   The critical point of authorization is the GraphQL resolver that issues the CDN signed URL. This resolver **must** verify that the requesting user (e.g., via `clerk_user_id`) has ownership or legitimate access rights to the `file_metadata` record (and thus the file) they are requesting a CDN URL for.
        *   If this check is flawed, or if the `X-Clerk-User-Id` bypass is exploited to impersonate another user when calling this GraphQL query, an attacker could obtain valid CDN signed URLs for files they shouldn't access, leading to IDOR.
        *   The CDN itself does not re-verify the user's session with VEDAVIVI when the signed URL is presented; it only checks the validity of the signature and expiry of the URL itself.

### 3. File Type and Size Validation ([cite: 420])

*   **Process:** "Backend validates type/size" (`ADR-002, Sec 2.4`).
*   **Details Not Specified:**
    *   **Timing:** Does validation occur before pre-signed URL generation (based on client-declared type/size, which can be spoofed but can be part of the URL signing conditions) or after upload via `completeFileUpload` (by inspecting the object in storage)? Both are recommended. The object storage service itself might enforce size limits set in the pre-signed URL.
    *   **Specific Limits:** Maximum file size, list of allowed MIME types.
    *   **MIME Type Verification:** How is the MIME type verified? Relying solely on the client-provided `Content-Type` header is insecure. True type detection (e.g., using magic numbers or server-side libraries) is better.
*   *Concern:*
    *   If validation is only client-side, it's easily bypassed.
    *   If type validation is weak, malicious files (e.g., HTML with JS, executables) could be uploaded. The risk depends on how these files are later served (see Section 4).
    *   Excessively large files could lead to Denial of Service (DoS) on storage or during processing (like avatar resizing).

*   **Avatar Resizing ([cite: 420]):**
    *   **Process:** "Sync backend avatar resizing. Image processing logic." (`ADR-002, Sec 2.4`).
    *   **Details Not Specified:** The image processing library used.
    *   *Concern:* Image processing libraries can have vulnerabilities (e.g., "ImageTragick"-like issues, decompression bombs, malicious EXIF data leading to information disclosure or RCE). The process should be sandboxed and use a well-maintained library. Resource limits should be applied to prevent DoS (e.g., if a tiny image is crafted to expand massively in memory).

### 4. Security of Uploaded Content

*   **Malware Scanning:**
    *   **Not Mentioned:** The ADR does not mention any malware or virus scanning of uploaded files.
    *   *Concern:* The platform could be used to store and distribute malicious files. This is a significant risk, especially if files can be shared or made public.

*   **Restrictions on Serving Active Content:**
    *   **Not Mentioned:** The ADR does not specify how files are served from the CDN to prevent XSS if active content (e.g., HTML, SVG with embedded scripts, JavaScript files) is uploaded and then accessed by users in a browser context.
    *   *Concern:* If files are served from the same domain as the main application, or without appropriate `Content-Security-Policy` headers and `Content-Disposition: attachment` (for non-trusted types), this could lead to XSS vulnerabilities. Ideally, user-uploaded content should be served from a separate, sandboxed domain.

### Summary & Recommendations

The file upload mechanism in VEDAVIVI uses good architectural patterns like pre-signed URLs for direct uploads and CDN signed URLs for downloads. However, the security of this system heavily relies on correct implementation details not fully specified in `ADR-002` and robust authorization logic.

**Key Findings & Concerns:**

1.  **Pre-signed Upload URL Expiry & Permissions:** Lack of specified expiry times and permissions for upload URLs.
2.  **CDN Signed URL Expiry & Authorization:** Lack of specified expiry for access URLs. The GraphQL resolver issuing these URLs is a critical authorization checkpoint to prevent IDORs. The `X-Clerk-User-Id` bypass is a major threat here.
3.  **File Validation Details:** Timing and methods for file type (actual type vs. client-declared) and size validation are unclear.
4.  **Avatar Resizing Security:** Potential vulnerabilities in the image processing library or DoS via crafted images.
5.  **No Malware Scanning:** Uploaded files are not scanned for malware.
6.  **Serving Active Content:** Risks of XSS if active content can be uploaded and rendered inline from the CDN, especially if served from the primary application domain.

**Recommendations for Deeper Investigation & Remediation:**

1.  **Pre-signed Upload URLs:**
    *   **Q1:** Confirm that pre-signed URLs for upload are short-lived (e.g., 5-15 minutes).
    *   **Q2:** Ensure permissions are strictly limited to `PUT` for the specific object key.
    *   **Q3:** Can `Content-Type` and `Content-Length` constraints be embedded in the pre-signed URL conditions?
2.  **CDN Signed URLs for Access:**
    *   **Q4:** Confirm that CDN signed URLs for access are short-lived.
    *   **Q5:** Rigorously audit the GraphQL resolver that generates these URLs to ensure it performs strict authorization checks, verifying the authenticated user's right to access the specific file.
3.  **File Validation:**
    *   **Q6:** Implement robust server-side validation of file types (using magic numbers or reliable libraries, not just `Content-Type` header) and sizes *after* upload (e.g., in `completeFileUpload`) and *before* making the file available. Define and enforce strict allowlists for MIME types and a reasonable maximum file size.
4.  **Avatar Resizing:**
    *   **Q7:** Use a well-maintained, secure image processing library.
    *   **Q8:** Sandbox the resizing process and apply resource limits (CPU, memory) to mitigate DoS risks from "image bombs."
5.  **Content Security:**
    *   **Q9:** Implement malware scanning for all uploaded files before they are made accessible. This can be done asynchronously after upload but before confirming the file.
    *   **Q10:** To prevent XSS from uploaded content:
        *   Serve user-uploaded files from a separate, sandboxed domain.
        *   Set `Content-Disposition: attachment` header for all potentially active file types to force download.
        *   Implement a strict `Content-Security-Policy` (CSP) that disallows execution of scripts from the CDN domain where files are hosted, if they must be rendered inline.
6.  **`file_metadata` Integrity:** Ensure that `user_id` in `file_metadata` is always derived from the authenticated session and cannot be tampered with.
7.  **Deferred Post-MVP Items:** Note that "File uploads from CLI" and "advanced file management, virus scanning" are deferred post-MVP, but virus scanning should be considered a core security requirement.

Addressing these concerns, particularly around strict validation, authorization for access, and content security (malware, active content), is crucial for a secure file upload and delivery system.
