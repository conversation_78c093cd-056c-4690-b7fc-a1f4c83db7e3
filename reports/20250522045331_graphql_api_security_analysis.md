## VEDAVIVI Platform: GraphQL API Security Analysis

This analysis assesses the security of the VEDAVIVI platform's GraphQL API, based on `ADR-002-Backend-Overview.md`, `apps/backend/src/a2a_platform/auth/clerk.py`, and common GraphQL security best practices. It focuses on common vulnerabilities and the security of specified operations.

### 1. Common GraphQL Vulnerabilities ([cite: 152])

*   **Introspection Queries:**
    *   **Status:** `ADR-002` does not explicitly state whether GraphQL introspection is disabled in production.
    *   **Concern:** If enabled in production, introspection allows attackers to obtain the full API schema, including all types, fields, queries, mutations, and subscriptions. This information is invaluable for constructing targeted attacks and understanding the data model.
    *   **Recommendation:** Introspection should be disabled in production environments. It can be enabled in development/staging for debugging and client generation.

*   **Query Depth & Complexity Limits:**
    *   **Status:** `ADR-002 (Sec 1 & 4)` mentions "Investigation into tooling like Apollo Platform or GraphQL Hive is required." These tools *can* provide mechanisms for query cost analysis, depth limiting, and complexity limiting. However, the ADR does not confirm that such limits are currently implemented or configured.
    *   **Concern:** Without limits, attackers can send deeply nested or computationally expensive queries that consume excessive server resources, potentially leading to Denial of Service (DoS).
    *   **Recommendation:** Implement and configure limits for query depth, query complexity/cost, and potentially the number of nodes that can be returned. Tools like Apollo Server have built-in or plugin options for this.

*   **Batching Attacks:**
    *   **Status:** The ADRs do not explicitly discuss whether GraphQL query batching (multiple operations in a single HTTP request) is supported or if there are protections against its abuse.
    *   **Concern:** If batching is allowed without limits, an attacker could send a large number of queries or mutations in a single HTTP request, potentially overwhelming the server or specific resolvers, leading to DoS or amplifying the impact of other attacks.
    *   **Recommendation:** If batching is enabled, limit the number of operations allowed in a single batched request.

*   **Insecure Object Exposure/Access Control (Resolver-Level Authorization):**
    *   **Status:** `ADR-002 (Sec 2.2 & 5)` states that authorization is OBAC based on `clerk_user_id`. The `ClerkAuthMiddleware` in `apps/backend/src/a2a_platform/auth/clerk.py` provides this `clerk_user_id` as `HTTPAuthorizationCredentials.credentials`, which is presumably available to resolvers.
    *   **Critical Concern:** The `X-Clerk-User-Id` header bypass vulnerability (previously identified) completely undermines any OBAC checks in resolvers, as the `clerk_user_id` can be spoofed.
    *   **Concern (General):** Even if the bypass is fixed, the ADRs do not detail *how* individual resolvers consistently enforce these OBAC checks. Each resolver accessing user-specific data must correctly use the `clerk_user_id` from the context to filter data. Any resolver that fails to do so can lead to data leakage or unauthorized actions.
    *   **Recommendation:**
        1.  **IMMEDIATE REMEDIATION:** Fix the `X-Clerk-User-Id` header bypass.
        2.  Establish a clear pattern or middleware for authorization within resolvers to ensure consistency.
        3.  Audit all resolvers to confirm correct and consistent application of OBAC based on the authenticated user's context.

*   **Error Handling:**
    *   **Status:** `ADR-002` does not specify how GraphQL errors (resolver errors, validation errors) are formatted and returned to the client.
    *   **Concern:** Default error configurations in some GraphQL libraries can leak sensitive information, such as internal server error details, stack traces, or database error messages, which can aid attackers.
    *   **Recommendation:** Implement custom error formatting to ensure that error messages returned to clients are generic and do not expose internal details. Log detailed errors server-side for debugging.

*   **Suggestions/Autocomplete:**
    *   **Status:** Not mentioned in the ADRs.
    *   **Concern:** If introspection is disabled in production (as recommended), query suggestions and autocomplete features in GraphQL IDEs or client tools will not function by default. If a mechanism is implemented to support this without full introspection, it must be carefully designed not to leak information about private or internal schema elements.
    *   **Recommendation:** Rely on schema downloads or static schema files for client-side development if introspection is disabled in production.

### 2. Security of Specific GraphQL Operations

*   **`sendMessage` Mutation ([cite: 464, 466]):**
    *   **Definition:** `sendMessage(conversationId: ID!, content: JSON!): ChatMessage!` (`ADR-002, GraphQL Schema`).
    *   **Authorization Concerns:**
        *   The resolver must verify that the authenticated user (`clerk_user_id`) is a participant in the specified `conversationId`.
        *   It must prevent a user from sending messages as another user (i.e., the `sender_role` should be correctly set based on the authenticated user or system logic for agent messages).
    *   **Input Validation Concerns:**
        *   `content: JSON!`: While GraphQL validates that it's JSON, the structure and content within this JSON need validation. For example:
            *   Are there limits on the size of the JSON payload or specific fields within it?
            *   If the content can include rich text or interactive elements (e.g., Markdown, HTML snippets, file references), is it sanitized before being stored and before being rendered in any UI to prevent XSS? (The `chat_messages.content` is JSONB, suggesting structured data).
    *   **Recommendation:**
        *   Implement strict authorization checks in the `sendMessage` resolver.
        *   Implement robust validation for the `content` field, including sanitization if it can contain user-renderable rich content.

*   **`newMessages` Subscription ([cite: 464, 465]):**
    *   **Definition:** `newMessages(conversationId: UUID!): ChatMessage!` (`ADR-002, GraphQL Schema`).
    *   **Authentication:** WebSocket connections are authenticated via Clerk JWT or VEDAVIVI CLI Token (`ADR-002, Sec 2.5`).
    *   **Authorization Concerns:**
        *   The subscription resolver/handler must ensure that the authenticated user is authorized to subscribe to messages for the given `conversationId`. This check must be performed upon subscription initiation and ideally re-verified periodically if the subscription is long-lived.
        *   If permissions change while a subscription is active, the subscription should be terminated.
    *   **WebSocket Security:**
        *   Ensure WebSocket connections are encrypted (WSS).
        *   The initial authentication must be secure. The use of Clerk JWTs or VEDAVIVI CLI Tokens is appropriate.
        *   *Further WebSocket concerns (e.g., resource exhaustion, handling of many connections) are typically addressed at the infrastructure/server level but are relevant for overall service availability.*
    *   **Recommendation:** Implement strict authorization in the `newMessages` subscription handler. Ensure WSS is used.

*   **`myDataOverview` Query ([cite: 449]):**
    *   **Definition:** `myDataOverview: JSON` (`ADR-002, GraphQL Schema`).
    *   **Function:** Aggregates user profile, assistant config, objectives, and connected services (`ADR-002, Sec 2.9`).
    *   **Authorization Concerns:**
        *   This query implicitly operates in the context of the authenticated user (`me`). The resolver must use the `clerk_user_id` from the authentication context to fetch and return data *only* for that user.
        *   The `X-Clerk-User-Id` bypass is a direct threat, allowing an attacker to execute this query for any user.
        *   Care must betaken to ensure that no sensitive data that the user should *not* see (e.g., internal system IDs not meant for exposure, excessive details from related objects if not carefully curated) is included in the aggregated JSON.
    *   **Recommendation:** Rigorously audit the `myDataOverview` resolver to ensure it correctly scopes data by `clerk_user_id` and only returns data intended for user visibility.

### 3. Authentication & Authorization within Resolvers

*   **User Context:** `apps/backend/src/a2a_platform/auth/clerk.py` shows that `ClerkAuthMiddleware` provides the `clerk_user_id` as `HTTPAuthorizationCredentials.credentials`. This context needs to be reliably passed to GraphQL resolvers, typically via a `context` object in the resolver function arguments.
*   **Consistency Concerns:** The ADRs imply OBAC is the standard, but there's no described mechanism (like a directive-based authorization system or a base resolver class) to ensure consistent application of authorization checks across all resolvers. This relies on developer discipline for each resolver.
*   **Recommendation:**
    *   Ensure the authenticated user context (especially `clerk_user_id`) is available in all GraphQL resolvers.
    *   Consider implementing a reusable authorization layer or directives (if the GraphQL framework supports them) to apply authorization rules more consistently and declaratively, reducing the risk of omissions in individual resolvers. For example, `@isAuthenticated` or `@hasOwnership(resourceField: "userId")`.

### 4. Input Validation at the GraphQL Layer

*   **Basic Type Checking:** GraphQL's schema definition language (SDL) enforces basic type checking for arguments (e.g., ensuring an `ID` is an ID, an `Int` is an integer).
*   **Business Logic Validation Concerns:** Beyond SDL types, specific business logic validation (e.g., string length limits, specific formats for strings, range checks for numbers, validation of JSON structures within a `JSON` scalar type) is not detailed in `ADR-002`. This validation must occur within the resolver logic or a dedicated validation layer called by resolvers.
*   **Recommendation:** Implement comprehensive input validation within resolvers or a service layer for all arguments, especially those that are complex (like JSON inputs) or that control sensitive operations. Do not rely solely on client-side validation or GraphQL's basic type system for security.

### Summary & Recommendations

The VEDAVIVI GraphQL API security relies heavily on the correct implementation of authentication and authorization within each resolver. The `X-Clerk-User-Id` bypass is a critical threat that must be addressed immediately. Beyond that, standard GraphQL security best practices need to be explicitly implemented and verified.

**Key Findings & Concerns:**

1.  **Critical `X-Clerk-User-Id` Bypass:** Undermines all GraphQL API authentication and authorization.
2.  **Lack of Explicit GraphQL DoS Protections:** Introspection, query depth/complexity, and batching limits are not confirmed to be implemented.
3.  **Resolver-Level Authorization Consistency:** Relies on developer discipline for each resolver to correctly implement OBAC.
4.  **Detailed Input Validation:** Specifics of input validation beyond basic GraphQL types are not detailed.
5.  **Error Handling:** Potential for information leakage through default error messages.

**Recommendations for Deeper Investigation & Remediation:**

1.  **IMMEDIATE REMEDIATION:** Fix the `X-Clerk-User-Id` header bypass in `ClerkAuthMiddleware`.
2.  **Implement GraphQL Security Controls:**
    *   **Q1:** Disable introspection in production environments.
    *   **Q2:** Implement and configure limits for query depth, complexity, and batching.
    *   **Q3:** Implement custom error formatting to prevent leakage of sensitive information.
3.  **Resolver Security Audit:**
    *   **Q4:** Review all GraphQL resolvers to ensure consistent and correct implementation of OBAC based on the authenticated user's `clerk_user_id`.
    *   **Q5:** Specifically audit the resolvers for `sendMessage`, `newMessages`, and `myDataOverview` for correct authorization logic and secure handling of their specific functionalities.
4.  **Input Validation:**
    *   **Q6:** Implement comprehensive input validation for all GraphQL arguments within resolvers or a dedicated service layer, including checks for length, format, range, and structure for complex types like JSON.
5.  **WebSocket Security for Subscriptions:** Ensure WSS is used and that authorization for subscription initiation is robust.
6.  **Tooling:** Expedite the investigation and implementation of tools like Apollo Platform or GraphQL Hive for features like persisted queries, query cost analysis, and security insights.

Addressing these points will significantly improve the security posture of the VEDAVIVI GraphQL API.
