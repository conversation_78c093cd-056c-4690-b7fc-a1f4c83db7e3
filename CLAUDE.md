# A2A Platform Development Guide

This document provides essential guidance for working with the A2A Platform codebase.
**IMPORTANT: Always follow these guidelines for consistent, high-quality code.**

## Claude Code Integration (v2.0)

This project includes 60+ Claude commands for streamlined development. Use **Claude Code** for the best experience.

### Core Unified Commands (Zero Friction)

#### **Testing - Intuitive Cross-Application Semantics**
```bash
/test                   # Quick feedback (backend unit tests)
/test unit              # ALL unit tests (frontend + backend)
/test integration       # ALL integration tests (frontend + backend) 
/test backend           # All backend tests
/test frontend          # All frontend tests
/test all               # Complete test suite
/test coverage          # With coverage reporting
/test tests/unit/test_user.py    # Specific Python file
```

#### **Database Operations**
```bash
/db                     # Open console (default)
/db migrate             # Run migrations
/db reset               # Reset database (with confirmation)
/db seed                # Seed test data
/db backup              # Create timestamped backup
```

#### **Code Quality & Checks**
```bash
/check                  # Run all checks (default)
/check lint             # Linting only
/check format           # Format check
/check security         # Security scan
/check fix              # Auto-fix issues
/precommit              # Run precommit with auto-fix loop
```

#### **GraphQL Operations**
```bash
/graphql                # Generate TypeScript types (default)
/graphql update         # Update schema from backend
/graphql test           # Run GraphQL tests
/graphql validate       # Validate queries
```

### Docker Environment Management

#### **Smart Environment Detection**
```bash
/up dev                 # Start development environment
/up test                # Start test environment
/down dev --volumes     # Stop dev with volume cleanup
/logs dev frontend      # View specific service logs
/shell test db          # Access database shell in test env
```

#### **Quick Shortcuts**
```bash
/dev up                 # Start dev environment
/dev down               # Stop dev environment
/test-env clean         # Clean test environment
/dc ps                  # Direct docker compose access
```

### Development Workflow Commands

#### **Essential Development**
```bash
/build                  # Build Docker images
/fresh                  # Clean restart of environment
/debug                  # Diagnose deployment errors
/run dev                # Start full development environment
```

#### **AI-Enhanced Development**
```bash
/deep-think [topic]     # Advanced reasoning with research
/research [query]       # Get authoritative answers
/track-decision [ADR]   # Record architectural decisions
/feature-plan [feature] # Create implementation plans
/remember [insight]     # Store knowledge for later
/recall [topic]         # Retrieve stored knowledge
```

#### **Workflow Shortcuts**
```bash
/spec-this [story]      # Generate user story and QA specs
/wrap-it-up             # Save context for LLM handoff
/unwrap-it              # Load saved context and continue
```

### Legacy Script Commands (Still Available)
- **Backend Tests**: `./scripts/run-backend-tests.sh` (use `/test backend` instead)
- **Frontend Tests**: `./scripts/run-frontend-tests.sh` (use `/test frontend` instead)
- **Precommit**: `./scripts/precommit-check.sh` (use `/precommit` instead)

## Directory Structure
- **apps/web**: Frontend TypeScript/React code
- **apps/backend**: Python/FastAPI backend code
- **scripts**: Utility scripts for development and CI/CD

## Code Style Guidelines

### Python
- **REQUIRED**: Black formatter (88 chars), isort for imports
- **STRICT TYPE CHECKING**: mypy with strict mode enabled
- **LINTING**: ruff/flake8 (120 chars max line)
- **MUST BE RUN FROM**: apps/backend directory for python files

### TypeScript
- **REQUIRED**: Double quotes for strings, not single quotes
- **FORMATTING**: ESLint and Prettier for consistent style
- **IMPORTS**: Use aliases via baseUrl/paths in tsconfig

### Database
- **CRITICAL**: Use SQLAlchemy ORM/Core, NEVER string interpolation
- **URLs**: async connections use `postgresql+asyncpg://`, sync connections use `postgresql://`
- **DOCKER**: Use the Docker PostgreSQL instance for development, not local PostgreSQL

### GraphQL
- **NAMING**: camelCase for mutations (required)
- **DATA STRUCTURES**: Use Pydantic for structured data instead of dictionaries
- **SYNTAX ERRORS**: When GraphQL generated files have syntax errors (like broken multiline strings), use the project's existing code generation scripts (bun run codegen:test or similar)

### Security
- **AUTH**: Clerk for authentication, implement proper CORS
- **SECRETS**: Avoid hardcoded secrets - use environment variables
- **ENVIRONMENT**: Consolidate environment variables in a root .env file

### Testing
- **UNIT TESTS**: Use mocks for DB in unit tests
- **INTEGRATION TESTS**: Use transactional sessions with rollback
- **PATHS**: Backend tests MUST be run from the apps/backend directory

## Configuration and Environment Variables
- **CENTRALIZATION**: Consolidate environment variables in a root .env file
- **DOCUMENTATION**: Create .env.example with dummy values for version control
- **SECURITY**: Add .env to .gitignore
- **NAMING**: Use descriptive variable names with documentation comments
- **DATABASE CONFIG**: Use dynamic construction of DATABASE_URL from POSTGRES_* variables
- **SOURCE OF TRUTH**: Prefer Docker Compose environment variables as the single source of truth

## Frontend Development
- **TOOLS**: Use bun and bunx for frontend development
- **DEV SERVER**: The frontend Docker container should run in development mode with 'bun run dev -- --host 0.0.0.0'
- **PORT**: Bun should be configured to run on port 5173 (important for e2e testing)

## Key Benefits of Claude Code Integration

### 🎯 **Zero Friction Development**
- **Smart Defaults**: Commands do the right thing without arguments
- **Intuitive Semantics**: `/test unit` means ALL unit tests (not just backend)
- **Progressive Disclosure**: Simple for common tasks, powerful when needed
- **Cross-Application Scope**: Commands work across frontend and backend

### 🚀 **Unified Command Interface**
- **60+ Commands**: All development tasks accessible via Claude
- **Consistent Patterns**: Similar commands follow similar patterns
- **Built-in Help**: Every command shows usage when given invalid input
- **Auto-Detection**: Commands automatically detect file types and environments

### 🧠 **AI-Enhanced Workflows**
- **Think-Tank Integration**: Advanced reasoning and research capabilities
- **Knowledge Management**: Store and retrieve project insights
- **Decision Tracking**: Record and reference architectural decisions
- **Context Handoff**: Seamless collaboration between LLM sessions

### 📁 **Claude Code Configuration**
See `.claude/` directory for:
- **60+ Commands**: Individual command files in `.claude/commands/`
- **Security Guidelines**: Security best practices in `.claude/SECURITY.md`
- **Templates**: Code generation templates
- **Configuration**: Claude behavior settings

For complete command reference, see `.claude/README.md`.

## Working with Claude - Repository Etiquette

### 🎯 How to Communicate Effectively

#### **Be Specific and Clear**
```bash
# ❌ Vague
"Fix the bug"

# ✅ Specific
"Fix the authentication error in SignInForm.tsx when OAuth providers fail"
```

#### **Course-Correct Early**
- If Claude is going in the wrong direction, stop immediately
- Provide clear feedback: "That's not what I meant, let me clarify..."
- Share examples or screenshots when explaining UI issues

#### **Provide Context**
- Share error messages completely
- Mention what you've already tried
- Include relevant file paths or function names
- Specify your environment (dev/test/prod)

### 🔄 Common Workflow Patterns

#### **Bug Fix Workflow**
```bash
# 1. Explore the problem area
/explore apps/web/src/components/auth

# 2. Create a plan
/plan-first bug-fix

# 3. Write a failing test (coming soon: /tdd start)
/test backend unit  # Verify test fails

# 4. Implement the fix
# Claude implements with your guidance

# 5. Verify the fix
/test                # Quick verification
/test all           # Full confidence

# 6. Quality check
/check all          # Lint, format, security
```

#### **Feature Development Workflow**
```bash
# 1. Research and explore
/research "best practices for React data fetching"
/explore apps/web/src/hooks

# 2. Plan the feature
/plan-first feature

# 3. Test-driven development
# Write tests first, then implement

# 4. Incremental implementation
# Build in small, testable chunks

# 5. Visual verification (for UI)
# Provide screenshots for comparison

# 6. Full validation
/test all
/check all
/precommit
```

#### **Refactoring Workflow**
```bash
# 1. Understand current state
/explore [target-area]
/test               # Ensure tests pass first

# 2. Plan the refactoring
/plan-first refactor

# 3. Make incremental changes
# Refactor in small steps, testing between each

# 4. Verify no regressions
/test all           # Everything still works
/performance        # No performance degradation
```

#### **Emergency Hotfix Workflow**
```bash
# 1. Quick assessment
/debug              # Diagnose the issue
/logs production    # Check production logs

# 2. Targeted fix
# Minimal change to fix the issue

# 3. Quick validation
/test [affected-area]
/safe-mode production  # (coming soon)

# 4. Deploy with confidence
/deploy-dev         # Test in dev first
```

### ❌ Anti-Patterns to Avoid

#### **Don't Skip Exploration**
```bash
# ❌ BAD: Jumping straight to implementation
"Add a new API endpoint for user preferences"

# ✅ GOOD: Explore first
/explore apps/backend/src/api
"Now let's add a user preferences endpoint following the existing patterns"
```

#### **Don't Implement Without Tests**
```bash
# ❌ BAD: Code first, test later (or never)
"Implement the feature"

# ✅ GOOD: Test-driven approach
"Let's write a test for this feature first"
/test               # Verify test fails
"Now implement to make the test pass"
```

#### **Don't Make Assumptions**
```bash
# ❌ BAD: Assuming implementation details
"Just use Redis for caching"

# ✅ GOOD: Check existing patterns
/explore apps/backend/src/cache
"What caching solution is this project using?"
```

#### **Don't Ignore Context**
```bash
# ❌ BAD: Working in isolation
"Create a new user service"

# ✅ GOOD: Understand relationships
/explore apps/backend/src/services
/grep "UserService"  # Check if it exists
/impact-check       # (coming soon)
```

### 💡 Pro Tips

#### **Use Screenshots for UI Work**
- Before: Show the current state
- Design: Share mockups or requirements
- After: Verify the implementation

#### **Leverage Claude's Memory**
```bash
/remember "Project uses Clerk for auth, not Auth0"
/recall auth  # Retrieve later
```

#### **Batch Related Changes**
Instead of many small requests, group related changes:
- "Update all user-related API endpoints to include audit fields"
- "Add error handling to all database operations in services/"

#### **Be Patient with Iterations**
- Complex features take multiple iterations
- Each iteration should improve on the last
- Celebrate progress, not perfection

### 🚀 Getting the Most from Claude

1. **Start with exploration** - Understand before changing
2. **Plan before coding** - Think through the approach
3. **Test continuously** - Catch issues early
4. **Iterate visually** - For UI work, use screenshots
5. **Document decisions** - Future you will thank you
6. **Learn from patterns** - Claude adapts to your style

### 📚 Visual Examples

#### **Good vs Bad: Error Handling**
```python
# ❌ BAD: Generic error handling
try:
    user = get_user(user_id)
except Exception as e:
    return {"error": "Something went wrong"}

# ✅ GOOD: Specific error handling
try:
    user = get_user(user_id)
except UserNotFoundError:
    return {"error": "User not found", "code": "USER_404"}, 404
except DatabaseError as e:
    logger.error(f"Database error fetching user {user_id}: {e}")
    return {"error": "Service temporarily unavailable", "code": "DB_503"}, 503
```

#### **Good vs Bad: Component Structure**
```typescript
// ❌ BAD: Everything in one component
const UserProfile = () => {
  // 500 lines of mixed concerns
}

// ✅ GOOD: Separated concerns
const UserProfile = () => {
  return (
    <>
      <UserHeader {...headerProps} />
      <UserDetails {...detailsProps} />
      <UserActions {...actionsProps} />
    </>
  )
}
```
