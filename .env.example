# Environment variables for A2A Platform
# This file serves as a template for setting up your environment
# Copy this file to .env and fill in your specific values

# Backend application settings
# Controls debug mode for the backend application
DEBUG=false
# Sets the logging level (debug, info, warning, error, critical)
LOG_LEVEL=info
# Application title displayed in API documentation
APP_TITLE="A2A Platform API"
# Application version
APP_VERSION="0.1.0"

# Database settings
# PostgreSQL user for the database container
POSTGRES_USER=postgres
# PostgreSQL password for the database container
POSTGRES_PASSWORD=postgres
# PostgreSQL database name for the database container
POSTGRES_DB=a2a_platform_test
# PostgreSQL host for the database connection
POSTGRES_HOST=localhost
# PostgreSQL port for the database connection
POSTGRES_PORT=5432

# Redis settings
# Connection string for Redis
# Format: redis://host:port/db_number
REDIS_URL=redis://localhost:6379/1

# Redis Queue (RQ) settings
# Default job timeout in seconds
RQ_DEFAULT_TIMEOUT=180
# Default job TTL in seconds (1 day)
RQ_DEFAULT_TTL=86400
# How long to keep job results (1 hour)
RQ_RESULT_TTL=3600
# Number of RQ workers to start
RQ_WORKER_COUNT=2

# Auth settings (Clerk)
# Backend Clerk settings
# Clerk API key for backend authentication
CLERK_API_KEY="sk_test_******"
# Clerk JWT public key for verifying tokens
CLERK_JWT_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----
******
-----END PUBLIC KEY-----
"
# Secret for verifying Clerk webhook signatures
CLERK_WEBHOOK_SECRET="whsec_********"

# Security and HTTPS settings
# Set to true to enforce HTTPS redirects in backend development
ENFORCE_HTTPS=false
# Domain for which SSL certificates are issued
SSL_DOMAIN=domain.com
# HSTS max age in seconds (31536000 = 1 year)
HSTS_MAX_AGE=31536000
# Include subdomains in HSTS policy
HSTS_INCLUDE_SUBDOMAINS=true
# Add site to HSTS preload list
HSTS_PRELOAD=true

# Frontend Clerk settings
# Publishable key for Clerk frontend integration
# This variable is prefixed with VITE_ to make it accessible in the frontend code
VITE_CLERK_PUBLISHABLE_KEY="pk_test_******"

# Clerk testing keys for Cypress and local frontend tests
CLERK_PUBLISHABLE_KEY=pk_test_******
CLERK_SECRET_KEY=sk_test_******

# CORS settings
# List of origins allowed to make cross-origin requests to the API
# Format: http://example1.com,https://example2.com
CORS_ORIGINS=https://localhost:5173
# File storage settings
# Name of the storage bucket for file uploads
STORAGE_BUCKET=a2a-dev-storage
# Base URL for the content delivery network
CDN_URL=your_cdn_url

# Pub/Sub settings
# Google Cloud project ID for Pub/Sub messaging
PUBSUB_PROJECT_ID="******"

# AI Service Configuration
# OpenAI API key for GPT models
OPENAI_API_KEY="sk-******"
# Anthropic API key for Claude models
ANTHROPIC_API_KEY="sk-ant-******"
# Google Gemini API key
GEMINI_API_KEY="AIza******"
# Alternative Google API key name (for compatibility)
GOOGLE_API_KEY="AIza******"
# Enable/disable AI response generation
AI_RESPONSE_ENABLED=true
# AI API timeout in seconds
AI_RESPONSE_TIMEOUT=30
# Rate limiting for AI API calls (calls per minute)
AI_RATE_LIMIT_CALLS=50
# Rate limiting window in seconds
AI_RATE_LIMIT_WINDOW=60
# Default LLM provider (openai, anthropic, google)
AI_DEFAULT_PROVIDER="google"

## Frontend settings
# GraphQL API URL for the frontend
VITE_GRAPHQL_API_URL=http://localhost:8000/graphql
# Set to true to enable HTTPS in frontend development (requires SSL certificates)
VITE_USE_HTTPS=false
