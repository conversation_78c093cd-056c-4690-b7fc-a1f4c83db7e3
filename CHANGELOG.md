# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Security
- **CRITICAL FIX**: Resolved high-severity security vulnerability AVD-GCP-0001 in GCS static hosting module:
  - Removed dangerous `allUsers` IAM member that granted public access to GCS bucket
  - Implemented `public_access_prevention = "enforced"` for enhanced bucket security
  - Replaced public access with service account-based access control for CDN and deployment
  - Added comprehensive security documentation and validation scripts
  - Enhanced CDN migration specification to emphasize GitHub Actions-managed deployment
  - Created security validation tools: `scripts/check-bucket-security.sh` and `scripts/validate-cdn-migration.sh`

### Added
- Enhanced HTTPS/SSL development support with flexible configuration:
  - Added `VITE_USE_HTTPS` environment variable for frontend HTTPS enablement
  - Added `ENFORCE_HTTPS` environment variable for backend HTTPS enforcement
  - Created `apps/backend/src/a2a_platform/api/https_entrypoint.sh` for conditional backend HTTPS startup
  - Enhanced SSL certificate validation with detailed error messages and debugging information
  - Added support for both Docker and local SSL certificate paths in Vite configuration
- Improved frontend development infrastructure:
  - Created enhanced entrypoint scripts (`enhanced-entrypoint.sh`, `enhanced-start-storybook.sh`) with permission checking
  - Added `fix-permissions.sh` script for automatic Docker permission management
  - Implemented comprehensive web code auto-fixing with package.json scripts
  - Added `scripts/fix-web-permissions.sh` for resolving Docker container permission issues
  - Created `docs/web-container-permissions.md` documentation for permission troubleshooting
- Enhanced Storybook configuration and development experience:
  - Upgraded Storybook dependencies and configuration for better compatibility
  - Added enhanced Storybook startup script with permission checks
  - Improved Storybook Docker integration with proper SSL certificate mounting
  - Updated Storybook stories to use environment-based Clerk configuration
- Migrated frontend package management from npm to Bun:
  - Updated all package management commands to use Bun for improved performance
  - Removed `package-lock.json` in favor of `bun.lock`
  - Updated Docker configurations to use Bun runtime
  - Enhanced pre-commit hooks to use Bun for ESLint execution

### Fixed
- Resolved Docker container permission issues in frontend development:
  - Fixed user creation order in Dockerfile to prevent permission conflicts
  - Improved volume mounting strategy for `node_modules` to avoid permission issues
  - Enhanced entrypoint scripts to automatically detect and fix permission problems
  - Added comprehensive permission checking and error reporting
- Enhanced security headers implementation:
  - Added relaxed Content Security Policy (CSP) for Swagger UI routes to allow CDN resources
  - Maintained strict CSP for all other application routes
  - Improved CSP configuration for development and production environments
- Improved SSL certificate handling and validation:
  - Enhanced certificate path resolution for both Docker and local environments
  - Added comprehensive SSL certificate validation with detailed error messages
  - Improved certificate file format validation and debugging output
  - Fixed SSL certificate mounting in Docker Compose configurations
- Updated Storybook and frontend testing infrastructure:
  - Fixed Storybook import paths and type definitions for better compatibility
  - Updated test configurations to use HTTPS URLs where appropriate
  - Resolved Storybook addon compatibility issues
  - Fixed environment variable handling in Storybook stories

### Changed
- Enhanced Docker Compose development configuration:
  - Updated `docker-compose.dev.yml` with improved SSL certificate mounting
  - Added `DOCKER_ENV=true` environment variable for container detection
  - Improved service dependencies and health checks for HTTPS support
  - Enhanced Storybook service configuration with backend dependency
- Improved frontend build and development configuration:
  - Updated Vite configuration with enhanced SSL certificate handling
  - Added comprehensive environment variable validation and exposure
  - Improved proxy configuration for GraphQL requests
  - Enhanced development server startup with better error handling
- Updated pre-commit configuration:
  - Modified ESLint hook to use Bun with auto-fix capabilities
  - Improved linting workflow with better error handling
  - Enhanced code quality checks for TypeScript files
- Streamlined frontend development workflow:
  - Simplified entrypoint scripts with better error handling
  - Improved development server startup process
  - Enhanced debugging and logging for development issues
- Enhanced CI/CD deployment infrastructure with modular architecture:
  - Created reusable deployment workflow (`.github/workflows/reusable-deploy-service.yml`) to standardize deployment patterns across all services
  - Implemented composite actions for common deployment tasks:
    - `.github/actions/gcp-auth-setup/action.yml` for GCP authentication using Workload Identity Federation
    - `.github/actions/docker-build-push/action.yml` for Docker image building and pushing to Artifact Registry
    - `.github/actions/gcloud-run-deploy/action.yml` for Cloud Run deployment with act-friendly testing support
  - Added comprehensive local testing infrastructure with manual workflow testing:
    - Created diagnostic script (`scripts/diagnose-deploy-errors.sh`) for workflow issue identification
    - Added setup script (`scripts/setup-deployment-testing.sh`) for quick environment preparation
    - Implemented full check script (`scripts/run-full-check.sh`) for comprehensive pre-commit validation
    - Added manual workflow testing capability via `scripts/run-act.sh` (removed from automated precommit process)
  - Created `.secrets` file template with mock values for secure local workflow testing
  - Enhanced YAML code quality with yamllint integration:
    - Added yamllint to pre-commit configuration for comprehensive YAML linting
    - Configured `.yamllint` with sensible defaults (disabled document-start and line-length rules)
    - Ensures consistent YAML formatting across GitHub Actions workflows, Docker Compose files, and configuration files

### Fixed
- Resolved critical deployment workflow issues:
  - Standardized secret naming across all deployment workflows by removing `_STAGING` and `_PRODUCTION` suffixes
  - Fixed parameter naming consistency in deployment workflows (standardized on kebab-case)
  - Corrected corrupted `deploy-workers.yml` workflow file with proper YAML structure
  - Updated all service deployment workflows (`deploy-api.yml`, `deploy-web.yml`, `deploy-workers.yml`) to use the reusable workflow pattern
- Corrected `gcloud run deploy` flags for API service in GitHub Actions workflow (`.github/workflows/deploy.yml`):
  - Changed `--allow-unauthenticated` to `--no-allow-unauthenticated` for staging and production API deployments.
- Resolved `ModuleNotFoundError: No module named 'psycopg2'` for the API service during deployment by adding `psycopg2-binary` to `apps/backend/pyproject.toml` dependencies.
- Resolved `ModuleNotFoundError: No module named a2a_platform.workers.main` for the Workers service by updating `apps/backend/src/a2a_platform/workers/entrypoint.sh` to execute `a2a_platform.workers.rq_worker`.
- Corrected `gcloud run deploy` command for the Workers service in GitHub Actions workflow (`.github/workflows/deploy.yml`):
  - Updated to use `--startup-probe=""` to disable the startup probe.
  - Removed erroneous trailing backslashes from `--set-env-vars` arguments.
- Fixed PostgreSQL connection issues in CI:
  - Increased database readiness retry count from 10 to 20
  - Added missing 'runner' role creation to fix database access
  - Added psycopg2-binary installation to GitHub Actions workflow to fix database migration failures
  - Configured GitHub Actions workflow to use Cloud SQL Auth Proxy for database connections to Cloud SQL, resolving direct public IP connection timeouts.
- Clerk webhook integration test for invalid user.deleted data now correctly expects a 400 error (not 500) when required fields are missing. Exception handling in the webhook route was updated to re-raise HTTPException for proper FastAPI error propagation.
- Fixed code quality issues to ensure pre-commit hooks pass:
  - Added missing `DeclarativeBase` import in database module
  - Corrected database settings reference in engine creation
  - Added GraphQL `createCliToken` mutation to use the existing resolver
  - Fixed imports for CLI token related GraphQL types
  - Fixed YAML syntax error in GitHub Actions workflow file
  - Enhanced YAML formatting consistency with yamllint integration
  - Improved pre-commit configuration documentation and organization
  - Resolved yamllint errors across all YAML files in the repository:
    - Fixed trailing spaces in GitHub Actions workflows and composite actions
    - Added missing newlines at end of files
    - Improved comment formatting in pre-commit configuration
    - Ensured consistent YAML formatting standards
- Fixed TypeScript configuration errors:
  - Removed invalid `erasableSyntaxOnly` compiler option from tsconfig files
- Fixed MyPy typing errors in GitHub Actions workflow:
  - Added return type annotation to `ClerkAuthMiddleware.__init__` method
  - Added explicit type annotation for `clerk_auth_service` in AuthMiddleware

### Changed
- Streamlined pre-commit workflow by removing act (GitHub Actions local testing) from automated hooks:
  - Removed act-based deployment workflow testing from pre-commit configuration to improve development speed
  - Maintained manual workflow testing capability via `scripts/run-act.sh` for when needed
  - Updated documentation to reflect manual-only workflow testing approach
- Temporarily disabled deployment of the Messaging and Workers services in the GitHub Actions workflow (`.github/workflows/deploy.yml`) by commenting out their respective build, push, and deploy steps.
- Disabled all production deployments in the GitHub Actions workflow (`.github/workflows/deploy.yml`) by commenting out the `deploy_production` job.

### Added
- Implemented frontend support for US2.6 CLI Token via Web UI:
  - Created Apollo client configuration with authentication via Clerk
  - Implemented GraphQL mutations for CLI token creation
  - Developed CliTokenManager React component with:
    - Token creation modal with description input
    - Secure token display with copy functionality
    - Warning messaging for one-time token visibility
  - Enhanced Docker configuration to properly set environment variables
  - Added runtime config to ensure GraphQL endpoint accessibility
  - Fixed browser-side environment variable access for production builds
- Added integration test for CLI token authentication workflow (pending completion):
  - Created end-to-end test to verify the CLI token creation and authentication flow
  - Test covers token creation using the createCliToken mutation
  - Verifies authentication using both X-A2A-CLI-Token header and Authorization Bearer header
  - Validates correct user data is returned from authenticated GraphQL requests
- Implemented backend support for US2.1 User Registration and Login:
  - Created secure webhook handler for Clerk `user.created` events
  - Added service for creating VEDAVIVI user records from Clerk events
  - Added JWT validation middleware for GraphQL API to authenticate users
  - Created database migration for users table with Clerk integration
  - Added unit and integration tests for the new components
- Implemented backend support for US2.5 Account Deletion via Clerk:
  - Enhanced webhook handling for Clerk `user.created` and `user.deleted` events
  - Added asynchronous worker tasks with proper idempotency checks
  - Implemented comprehensive user data deletion across all database tables
  - Created service for converting Clerk user data to VEDAVIVI user records
  - Added extensive unit tests for webhook handlers, services, and worker tasks
  - Updated documentation on Clerk integration
- Enhanced developer tooling:
  - Renamed `docker-start.sh` to `app-runner.sh` with expanded functionality
  - Added commands to stop and check status of running containers
- Implemented GitHub Actions CI/CD pipeline for Python backend services (API, Workers, Messaging) to Google Cloud Run (GCR) based on US4.1.1:
  - Created `.github/workflows/deploy.yml` for automated build, test (implicitly via branch protection), and deployment to staging (on `main` branch push) and production (manual approval) environments on GCR.
  - Workflow includes Docker containerization, image push to Google Artifact Registry, and Alembic database migrations.
  - Utilizes GitHub Environments and GCP Workload Identity Federation for secure credential management.
- Implemented GitHub Actions CI Checks workflow for Python backend based on US4.1.1:
  - Created `.github/workflows/ci-checks.yml` to automatically run linters (Ruff, Black) and unit tests (Pytest) on pushes to any branch and on pull requests targeting `main`.
  - Configured to integrate with `pyproject.toml` for dependency management and tool configuration.
  - This workflow is intended to be a required status check for branch protection rules on `main`.
- Created placeholder Dockerfiles for API, Workers, and Messaging backend services.
- Enhanced CI stability and backend code quality for US4.1.1 implementation:
  - Performed extensive MyPy static type checking fixes across the backend codebase, including:
    - Addition of `py.typed` files and `__init__.py` files in relevant directories to ensure PEP 561 package type information discovery.
    - Refinement of MyPy configuration within `pyproject.toml` and adjustments to command-line execution for MyPy.
    - Successful integration and configuration of the `pydantic-mypy` plugin to resolve Pydantic model type errors.
    - Correction of numerous type hints related to database engine/session management, Pydantic models, Clerk authentication services, and GraphQL context and middleware components.
    - Addition of necessary type stubs like `types-python-jose` and MyPy error code overrides for external libraries (e.g., `svix.webhooks`).
    - Refined `AuthMiddleware` in `apps/backend/src/a2a_platform/api/graphql/middleware/auth_middleware.py` to fully comply with `strawberry.extensions.SchemaExtension` typing for MyPy by:
      - Ensuring `on_operation` method is correctly typed as an `AsyncGenerator`.
      - Aligning the `resolve` method signature (parameter types like `info: GraphQLResolveInfo`, `*args: Any`, and return type `Awaitable[object]`) with the superclass, using `GraphQLResolveInfo` from the `graphql` library.
      - Converting `resolve` from an `async def` to a synchronous `def` method that returns an `Awaitable[object]`, using local `async def` helpers to construct awaitables for error-raising paths.
