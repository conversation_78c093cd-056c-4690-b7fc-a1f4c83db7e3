# 🧪 QA: {US_ID}: {Title}

**Goal:** 🎯 {High-level testing objective}

## 🎯 Objectives
**Functional:** ⚙️ {Core functionality validation} | **NFR:** 📏 {Performance/security/a11y} | **Edge:** ⚠️ {Boundary/error cases} | **Security:** 🔒 {Auth/authz validation} | **DB:** 🗄️ {Schema/infrastructure} | **Integration:** 🔗 {External systems}

## 📋 Strategy
**Scope:** 🎯 {In/out scope} | **Env:** 🐳 Docker(default)/.env.test | CI(--ci flag) | **Backend:** 🖥️ apps/backend + ./scripts/run-backend-tests.sh + pytest + postgresql+asyncpg + DATABASE_URL(required) | **Frontend:** 🌐 bun + ./scripts/run-frontend-tests.sh + port:5173 + --e2e(Cypress) | **DB:** 🗄️ ./scripts/db-migrate.sh + upgrade/downgrade | **Tools:** 🛠️ pytest/bun/Docker/Alembic/Cypress | **Deps:** 🔗 {Prerequisites} | **Data:** 📊 {Test data needs} | **Risks:** ⚠️ {High-risk areas}

## 🎬 Context
**Before:** Features:⚙️{existing} | Env:🔧{setup/config} | Data:📊{prep requirements} | [None]
**After:** Deliverables:📦{artifacts} | Reports:📄{generated docs} | Defects:🐛{tracking/resolution} | [None]

## 📝 Tasks
{N}. **{task_name}** - {desc}
```test_execution
**Do:** 🎯 {test instruction} | **Type:** 🧪 {Infrastructure/Unit/Integration/E2E} | **Cmd:** 💻 {commands} | **Assert:** ✅ {assertions} | **Data:** 📊 {test data} | **Result:** 🎯 {expected outcomes}
```

## 📋 Test Cases

### 🧪 {Category}: {Description}
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| {ID}-TC-{N} | {Test case desc} | {step1} → {step2} → {stepN} | {Expected outcome} | {API/DB/UI/Integration} |

### ❌ Negative & Error
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| {ID}-ERR-{N} | {Error scenario} | {step1} → {step2} | {Error response/behavior} | {Negative/Security} |

### 📋 Contract & External
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| {ID}-CT-{N} | {Contract test} | Mock → Verify → Validate | {Contract compliance} | {Contract/Integration} |

### ⚙️ Environment & Config
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| {ID}-ENV-{N} | {Env test} | {setup} → {test} → {verify} | {Env behavior} | {Environment} |

## 📊 Coverage
**Functional:** ⚙️ {Core validation/workflows/APIs} | **NFR:** 📏 {Performance/security/a11y} | **Edge:** ⚠️ {Boundary/error/validation}

## 🔧 Setup
**Backend:** 🖥️ apps/backend + ./scripts/run-backend-tests.sh + pytest + postgresql+asyncpg + DATABASE_URL(required) | **Frontend:** 🌐 bun + ./scripts/run-frontend-tests.sh + port:5173 + dev mode | **DB:** 🗄️ ./scripts/db-migrate.sh + Docker Compose env vars + upgrade/downgrade

## 📦 Data
**Env Vars:** ⚙️ DATABASE_URL + POSTGRES_* + CLERK_JWT_PUBLIC_KEY + .env/.env.test | **Config:** 🔧 .env.test + Docker + --ci flags

## 🎭 Scenarios

### 🎬 {Scenario}: {Description}
**TC {N}.{N}:** {Test case desc}
- **Given:** 📋 {Initial conditions} | **When:** ⚡ {Actions} | **Then:** ✅ {Expected outcomes} | **And:** ➕ {Additional verification} | [None]

## 🤖 Automation
**Pre-commit:** 🔧 MyPy parity + auto-fixes + SKIP=pytest-backend + flags + Ruff | **CI/CD:** 🚀 postgres://test_user:test_password@localhost:5432/test_database + Docker/CI parity + apps/backend + GitHub Actions | **Env Mgmt:** 🐳 Docker(default) + .env.test + --ci flag + idempotent DB scripts

## 📦 Deliverables
📄 QA spec + execution log + bug reports + sign-off report

## ⚠️ Risks
**Risk {N}:** 🚨 {Risk description → mitigation strategy} | [None]

---

## Usage
**Symbols:** {text}=required | [text]=optional | {text}|[default]=required+fallback
**Check:** [ ] {} replaced | [ ] [] evaluated | [ ] Test cases defined | [ ] Scenarios mapped | [ ] Automation considered
