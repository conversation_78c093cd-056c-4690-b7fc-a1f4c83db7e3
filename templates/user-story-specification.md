# 📋 {US_ID}: {Title}

**Story:** As {persona}, I want {capability} so that {value}.
**Steps:** {step1} → {step2} → {stepN}
**Epic:** 🎯 {epic_name}

## ✅ AC
- [ ] Given {condition}, When {action}, Then {outcome} [{source}]
- [ ] Given {condition}, When {action}, Then {outcome}
- [ ] Given {error}, When {action}, Then {handling}

## 🔗 Deps
**Stories:** {US#.#(STATUS)} | **Ext:** 🌐 {API@ver} | **Tech:** ⚙️ {lib/fw} | **Data:** 🗄️ {schema} | **Team:** 👥 {approval} | [None]

## 💰 Value
**Biz:** 📈 {value} | **UX:** ✨ {improvement} | **KPI:** 📊 {metrics} | **Why:** 🎯 {priority}

## 📐 ADRs
**Refs:** 📄 {ADR-###} | **Limits:** ⚠️ {constraints} | **Patterns:** 🏗️ {required} | [None]

## 🛠️ Impl
**Arch:** 🏛️ {patterns/tech} | **Data:** 🗄️ {schema} | **API:** 🔌 {endpoints} | **Sec:** 🔒 {auth/authz} | **Perf:** ⚡ {reqs} | **Int:** 🌐 {external} | **Err:** ❌ {handling} | **Std:** 📏 {patterns} | [Standard/None]

## 🧪 Test
**Unit:** 🔬 {funcs} | **Int:** 🔗 {systems} | **Contract:** 📋 {APIs} | **E2E:** 🎭 {journeys} | **Perf:** 🚀 {load} | **Sec:** 🛡️ {vuln} | [Standard/None]

## 📏 NFRs
**Perf:** ⚡ {response/throughput} | **A11y:** ♿ {WCAG} | **Sec:** 🔒 {auth} | **Scale:** 📈 {load/users} | **Compat:** 🌍 {browser/device} | [Standard]

## ✅ DoD
- [ ] AC met | [ ] Tests pass | [ ] Security review | [ ] Perf benchmarks | [ ] Docs updated | [ ] Code review | [ ] QA complete

## ⚠️ Risk
**Tech:** 💻 {risk→mitigation} | **Biz:** 📊 {risk→mitigation} | **Time:** ⏰ {risk→mitigation} | **Deps:** 🔗 {risk→mitigation} | [None]

## 📊 Est
**Pts:** 🎯 {size+why} | **Hrs:** ⏱️ {dev+test+review} | **Complex:** 🧩 {unknowns/deps} | **Conf:** 📈 {H/M/L+reason} | **Vars:** 🔄 {changers} | [None]

## 📦 Data
**API:** 🔌 {req/resp} | **DB:** 🗄️ {records} | **UI:** 🖥️ {state} | **Config:** ⚙️ {env} | [None]

## 🎨 Visual
**Layout:** 📐 {ASCII/text} | **Flow:** 🌊 {mermaid} | **States:** 🔄 {transitions} | **Seq:** 📋 {interactions} | [None]

## 🤔 Assume
**Sys:** 🖥️ {DB/svc/config} | **User:** 👤 {actions/perms} | **Env:** 🌐 {net/browser/device} | **Data:** 📊 {fmt/vol/qual} | [Standard]

## 🎬 Context
**Before:** Files:📁{existing} | Schema:🗄️{tables} | Components:⚙️{svc/api/ui} | Config:⚙️{env} | [None/Standard]
**After:** Files:📁{created/modified} | Schema:🗄️{new tables/cols} | Components:⚙️{new svc/endpoints/ui} | Config:⚙️{changes} | [None]

## 📝 Tasks
{N}. **{task_name}** - {desc}
```coder_llm
**Do:** 🎯 {instruction} | **File:** 📁 {path} | **Target:** 🎯 {func/class/comp} | **Check:** ✅ {criteria} | **Needs:** 🔧 {prereqs} | [New/Multiple/None]
```

## 🔧 Refactor
**Quality:** 📏 {structure/readability} | **Perf:** ⚡ {optimizations} | **Reuse:** ♻️ {extract patterns} | **Debt:** 💳 {created/addressed} | **Test:** 🧪 {coverage} | **Docs:** 📚 {improvements} | [None/Sufficient]

---

## Usage
**Symbols:** {text}=required | [text]=optional | {text}|[default]=required+fallback
**Check:** [ ] {} replaced | [ ] [] evaluated | [ ] ADRs ref'd | [ ] Tasks actionable | [ ] AC=G-W-T | [ ] Deps ID'd
