# A2A Platform - GitHub Copilot Custom Instructions

## 🏗️ Technology Stack & Architecture

**Backend**: Python 3.12, FastAPI, SQLAlchemy 2.0 (async), PostgreSQL, Redis, RQ workers
**Frontend**: React 19, TypeScript, Apollo Client (GraphQL), Tailwind CSS, Vite, <PERSON><PERSON><PERSON> (state)
**Infrastructure**: Docker Compose, Google Cloud Platform, Terraform
**Testing**: pytest (backend), <PERSON><PERSON> (frontend), <PERSON><PERSON> (e2e)
**Package Management**: Use `bun` for frontend, `pyproject.toml` for backend dependencies


## Baseline Instructions

See [docs/llm-instructions.md](../docs/llm-instructions.md) for additional instructions.

## 📁 Project Structure Conventions

- Place script files in `./scripts/` folder, not project root
- Backend code lives in `apps/backend/`, frontend in `apps/web/`
- Use `apps/backend/pyproject.toml` for Python dependency management
- Store specifications in `./specs/` with user story and QA testing files
- Architecture Decision Records (ADRs) are in `./adrs/` folder

## 🐳 Docker & Environment Management

- Use `docker compose` command (not `docker-compose`) - project has docker-compose.yml
- Prefer Docker Compose for environment variable management over .env files
- Use `docker-compose.dev.yml` for development with `Dockerfile.dev` for frontend
- Environment variables should be passed properly to applications without fallback values
- Use environment-neutral variable names without prefixes like STAGING_ or PRODUCTION_ (e.g., use GCS_BUCKET instead of PRODUCTION_GCS_BUCKET)
- Use GitHub Environments to manage environment-specific secrets rather than prefixing variables
- Use `depends_on` for service startup ordering in development environments
- RQ workers run as separate auto-starting services in docker-compose.yml

## 🧪 Testing Patterns & Scripts

**Backend Testing**:
- Always use `./scripts/run-backend-tests.sh` from project root (not direct pytest)
- Tests must run from apps/backend directory context
- Use mocks for database operations in unit tests
- Integration tests should use real resolvers with proper async session management
- Required test environment variables: REDIS_URL, CLERK_API_KEY, CLERK_JWT_PUBLIC_KEY, CLERK_WEBHOOK_SECRET, STORAGE_BUCKET, CDN_URL, PUBSUB_PROJECT_ID

**Frontend Testing**:
- Always use `./scripts/run-frontend-tests.sh` from project root
- Use `--unit` flag for unit tests, `--e2e` flag for end-to-end tests
- Script automatically loads .env.test environment variables
- Test user credentials: <EMAIL> / password

**Database Testing**:
- In CI environments, database hostname must be 'localhost' (not 'db')
- Use transactional database sessions with rollback for test isolation
- Prefer structured Pydantic models over dictionaries for data handling

## 💻 Code Style & Standards

**General**:
- Use double quotes as standard for string literals (not single quotes)
- Write efficient, modular code with proper error handling
- Group code in modules with comprehensive unit tests

**Python/Backend**:
- Use SQLAlchemy ORM or Core Expression Language (never f-string SQL interpolation)
- Use Pydantic models for structured data instead of dictionaries
- Use environment variables for database connections (no hardcoded values)
- GraphQL mutations should use camelCase naming (e.g., updateMyProfile)
- API endpoints for internal tasks use '/api/internal/' pattern
- Use TaskCreate schema for internal task management (not raw dictionaries)

**TypeScript/Frontend**:
- Use TypeScript path aliases (baseUrl and paths in tsconfig.json) for imports
- Suggest @lib/* aliases for lib directory imports to resolve module issues
- Follow React 19 patterns with proper async handling

## 🔒 Security & Authentication

- Use Clerk for authentication with proper webhook signature verification
- Clerk webhook signing secrets begin with 'whsec_' prefix
- Implement proper CORS handling as comma-separated string (not JSON array)
- Use structured logging and metrics for webhook event handling with registry pattern
- Include standardized user context in A2A communication (user_id, initiating_agent_id, request_timestamp)
- Use GitHub Environments feature to manage environment-specific secrets instead of prefixing variable names

## 📊 Database & API Patterns

**Database**:
- Use 'postgresql+asyncpg://' for async connections, 'postgresql://' for sync
- Prefer environment variables for database connection strings
- Use proper type handling between GraphQL enums and Python enums in Strawberry
- Implement deep merging for user preferences (PostgreSQL JSONB with server_default='{}')

**API Design**:
- Use registry pattern (dispatch table) for webhook event handling
- Support both synchronous (HTTP) and asynchronous (queue-based) communication
- Avoid 'vedavivi_' prefix in A2A communication schema

## 🛠️ Development Workflow

**Pre-commit & Quality**:
- Use standard 'pre-commit' command directly (not ./scripts/precommit-checks.sh)
- Configure pre-commit to automatically fix issues when possible
- Ensure MyPy parity between pre-commit-config.yaml and GitHub CI workflow

**Scripts & Environment**:
- Scripts should check for required environment variables at runtime
- Use environment-neutral variable names (e.g., DATABASE_URL, REDIS_URL) without environment prefixes
- Use DATABASE_URL and REDIS_URL environment variables (throw clear errors when missing)
- For testing, prefer Docker as default with --ci flags for CI alternatives

## 🚀 Performance & Optimization

- Apply optimization prompt templates from docs/code-optimization.md
- Use performance canary decorators for monitoring endpoint performance
- Implement proper caching strategies and query optimization patterns
- Consider JIT compilation for CPU-intensive operations

## 🐛 Debugging Approach

- Follow structured debugging patterns from docs/debugging.md
- Use comprehensive context in debug requests (environment, component, exact error)
- Implement proper instrumentation and logging for diagnosis
- Consider edge cases: race conditions, null inputs, extreme load, network issues

## 🔄 Refactoring Guidelines

- Follow systematic refactoring approach from docs/refactoring.md
- Use goal-oriented refactoring with clear success metrics
- Implement incremental changes with proper validation
- Consider design patterns and architectural coherence

## 📝 Documentation & Specifications

- Use information-dense, high-fidelity vocabulary in templates
- Include strategic emoji placement for visual categorization
- Generate specifications with atomic acceptance criteria (GIVEN-WHEN-THEN format)
- Link to relevant ADRs in implementation notes
- Create granular low-level tasks with specific file paths and function names

