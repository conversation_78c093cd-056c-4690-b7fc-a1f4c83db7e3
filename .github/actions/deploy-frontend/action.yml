name: 'Deploy Frontend to Cloudflare R2'
description: 'Builds and deploys frontend assets to Cloudflare R2 storage with comprehensive verification'

inputs:
  environment:
    description: 'Deployment environment (staging or production)'
    required: true
  api-url:
    description: 'API URL for the environment'
    required: true
  graphql-url:
    description: 'GraphQL API URL for the environment'
    required: true
  ws-url:
    description: 'WebSocket URL for the environment'
    required: true
  assets-url:
    description: 'Assets URL for the environment'
    required: true
  cdn-url:
    description: 'CDN URL for the environment'
    required: true
  r2-bucket:
    description: 'R2 bucket name for the environment'
    required: true
  node-version:
    description: 'Node.js version to use'
    required: false
    default: '18'
  cloudflare-account-id:
    description: 'Cloudflare Account ID'
    required: true
  r2-access-key-id:
    description: 'R2 Access Key ID'
    required: true
  r2-secret-access-key:
    description: 'R2 Secret Access Key'
    required: true
  clerk-publishable-key:
    description: 'Clerk Publishable Key'
    required: true
  cloudflare-api-token:
    description: 'Cloudflare API Token'
    required: false
  cloudflare-zone-id:
    description: 'Cloudflare Zone ID'
    required: false
  cache-invalidation-enabled:
    description: 'Whether to invalidate Cloudflare cache'
    required: false
    default: 'true'

outputs:
  deployment-success:
    description: 'Whether the deployment was successful'
    value: ${{ steps.deployment.outputs.success }}

runs:
  using: 'composite'
  steps:
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: ${{ inputs.node-version }}

    - name: Setup Bun
      uses: oven-sh/setup-bun@v1
      with:
        bun-version: latest

    - name: Set environment variables
      id: env_vars
      shell: bash
      run: |
        # Set environment-specific configuration
        R2_ENDPOINT="https://${{ inputs.cloudflare-account-id }}.r2.cloudflarestorage.com"

        echo "DEPLOY_ENV=${{ inputs.environment }}" >> $GITHUB_OUTPUT
        echo "R2_BUCKET=${{ inputs.r2-bucket }}" >> $GITHUB_OUTPUT
        echo "R2_ENDPOINT=$R2_ENDPOINT" >> $GITHUB_OUTPUT
        echo "CDN_URL=${{ inputs.cdn-url }}" >> $GITHUB_OUTPUT

        echo "Configuration:"
        echo "  Environment: ${{ inputs.environment }}"
        echo "  R2 Bucket: ${{ inputs.r2-bucket }}"
        echo "  R2 Endpoint: $R2_ENDPOINT"
        echo "  CDN URL: ${{ inputs.cdn-url }}"

    - name: Install dependencies
      shell: bash
      working-directory: apps/web
      run: |
        echo "Installing dependencies with bun..."
        echo "📦 Installing fresh dependencies (no caching)"
        bun install --frozen-lockfile
        echo "Dependencies installed successfully"

    - name: Debug environment variables
      shell: bash
      run: |
        echo "🔍 Checking environment variables..."
        echo "VITE_CLERK_PUBLISHABLE_KEY is set: ${{ inputs.clerk-publishable-key != '' }}"
        echo "VITE_CLERK_PUBLISHABLE_KEY starts with pk_: ${{ startsWith(inputs.clerk-publishable-key, 'pk_') }}"
        echo "Branch: ${{ github.ref }}"
        echo "Event: ${{ github.event_name }}"
        echo "Repository: ${{ github.repository }}"
        echo "Actor: ${{ github.actor }}"

    - name: Build frontend
      shell: bash
      working-directory: apps/web
      env:
        NODE_ENV: production
        VITE_DEPLOY_ENV: ${{ inputs.environment }}
        VITE_API_URL: ${{ inputs.api-url }}
        VITE_GRAPHQL_API_URL: ${{ inputs.graphql-url }}
        VITE_WS_URL: ${{ inputs.ws-url }}
        VITE_ASSETS_URL: ${{ inputs.assets-url }}
        VITE_CLERK_PUBLISHABLE_KEY: ${{ inputs.clerk-publishable-key }}
        VITE_ENABLE_WEBSOCKET: "true"
        VITE_ENABLE_ANALYTICS: "true"
      run: |
        # Enable strict error handling
        set -euo pipefail

        echo "🔍 Build environment check..."
        echo "VITE_CLERK_PUBLISHABLE_KEY is set: ${VITE_CLERK_PUBLISHABLE_KEY:+yes}"
        echo "VITE_CLERK_PUBLISHABLE_KEY starts with pk_: $(echo $VITE_CLERK_PUBLISHABLE_KEY | grep -q '^pk_' && echo 'yes' || echo 'no')"
        echo "VITE_API_URL: ${VITE_API_URL}"
        echo "VITE_GRAPHQL_API_URL: ${VITE_GRAPHQL_API_URL}"
        echo "🔨 Starting build..."

        bun run build

        echo "🔍 Verifying built files contain expected environment variables..."
        # Check if the built JS files contain the Clerk key
        if find dist -name "*.js" -exec grep -l "pk_" {} \; | head -1; then
          echo "✅ Found Clerk key in built JavaScript files"
        else
          echo "❌ Clerk key NOT found in built JavaScript files"
          exit 1
        fi

        # Check if the built JS files contain the GraphQL API URL domain
        API_DOMAIN=$(echo "${VITE_GRAPHQL_API_URL}" | sed 's|https\?://||' | sed 's|/.*||')
        echo "Looking for API domain: ${API_DOMAIN}"
        if find dist -name "*.js" -exec grep -l "${API_DOMAIN}" {} \; | head -1; then
          echo "✅ Found API domain (${API_DOMAIN}) in built JavaScript files"
        else
          echo "❌ API domain (${API_DOMAIN}) NOT found in built JavaScript files"
          exit 1
        fi

    - name: Verify build output
      shell: bash
      working-directory: apps/web
      run: |
        echo "🔨 Build completed successfully"
        echo "📦 Build directory contents:"
        ls -la dist/

        if [ -d "dist/assets" ]; then
          echo "📁 Assets directory:"
          ls -la dist/assets/
        else
          echo "❌ No assets directory found"
          exit 1
        fi

    - name: Setup AWS CLI for R2
      shell: bash
      run: |
        # Enable strict error handling
        set -euo pipefail

        echo "Installing AWS CLI for R2 compatibility..."

        # Download AWS CLI
        curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"

        # Verify checksum for security
        echo "Verifying AWS CLI binary integrity..."

        # Try to download checksum file, but don't fail if it doesn't work
        if curl -f "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip.sha256" -o "awscliv2.zip.sha256" 2>/dev/null; then
          # Get expected checksum from AWS (extract just the hash)
          EXPECTED_CHECKSUM=$(cut -d' ' -f1 < awscliv2.zip.sha256 2>/dev/null | tr -d '\n\r')

          # Only proceed with verification if we got a valid-looking checksum
          if [[ "$EXPECTED_CHECKSUM" =~ ^[a-f0-9]{64}$ ]]; then
            # Calculate actual checksum of downloaded file
            ACTUAL_CHECKSUM=$(sha256sum awscliv2.zip | cut -d' ' -f1)

            echo "Expected checksum: $EXPECTED_CHECKSUM"
            echo "Actual checksum:   $ACTUAL_CHECKSUM"

            if [ "$EXPECTED_CHECKSUM" != "$ACTUAL_CHECKSUM" ]; then
              echo "❌ AWS CLI binary checksum verification failed"
              echo "   Expected: $EXPECTED_CHECKSUM"
              echo "   Actual:   $ACTUAL_CHECKSUM"
              exit 1
            fi
            echo "✅ AWS CLI binary checksum verification passed"
          else
            echo "⚠️  Could not retrieve valid checksum from AWS, skipping verification"
            echo "   Note: AWS CLI will be installed but checksum verification was skipped"
          fi
        else
          echo "⚠️  Could not download checksum file from AWS, skipping verification"
          echo "   Note: AWS CLI will be installed but checksum verification was skipped"
        fi

        unzip awscliv2.zip
        sudo ./aws/install --update

        # AWS credentials are already set via environment variables
        echo "AWS CLI configured for R2 (credentials via environment variables)"

    - name: Verify R2 Authentication
      shell: bash
      env:
        AWS_ACCESS_KEY_ID: ${{ inputs.r2-access-key-id }}
        AWS_SECRET_ACCESS_KEY: ${{ inputs.r2-secret-access-key }}
        AWS_DEFAULT_REGION: auto
      run: |
        # Enable strict error handling
        set -euo pipefail

        echo "Verifying Cloudflare R2 authentication..."
        echo "R2 Bucket: ${{ steps.env_vars.outputs.R2_BUCKET }}"
        echo "R2 Endpoint: ${{ steps.env_vars.outputs.R2_ENDPOINT }}"

        # Test R2 access by listing bucket contents
        if ! aws s3 ls s3://${{ steps.env_vars.outputs.R2_BUCKET }}/ --endpoint-url=${{ steps.env_vars.outputs.R2_ENDPOINT }}; then
          echo "❌ R2 bucket access verification failed"
          exit 1
        fi
        echo "✅ R2 bucket access verified"

    - name: Deploy to Cloudflare R2
      id: deployment
      shell: bash
      working-directory: apps/web
      env:
        AWS_ACCESS_KEY_ID: ${{ inputs.r2-access-key-id }}
        AWS_SECRET_ACCESS_KEY: ${{ inputs.r2-secret-access-key }}
        AWS_DEFAULT_REGION: auto
      run: |
        # Enable strict error handling
        set -euo pipefail

        echo "Starting deployment to R2 bucket: ${{ steps.env_vars.outputs.R2_BUCKET }}"

        R2_ENDPOINT="${{ steps.env_vars.outputs.R2_ENDPOINT }}"
        R2_BUCKET="${{ steps.env_vars.outputs.R2_BUCKET }}"

        # Clear existing assets first to avoid conflicts
        echo "Clearing existing assets to avoid MIME type conflicts..."
        if ! aws s3 rm s3://$R2_BUCKET/assets/ --recursive --endpoint-url=$R2_ENDPOINT 2>/dev/null; then
          echo "ℹ️  No existing assets to clear (or bucket is empty)"
        fi

        # Upload assets with explicit MIME types
        echo "Uploading assets with correct MIME types..."
        if [ ! -d "dist/assets" ]; then
          echo "❌ No assets directory found"
          echo "Build directory contents:"
          ls -la dist/
          exit 1
        fi

        echo "Found assets directory, uploading..."
        echo "Assets directory contents:"
        ls -la dist/assets/
        echo ""

        # Upload JavaScript files with explicit MIME type
        echo "🔧 Uploading JavaScript files with application/javascript MIME type..."
        find dist/assets -name "*.js" -type f | while read file; do
          relative_path=${file#dist/assets/}
          echo "  Uploading: $file → assets/$relative_path"
          if ! aws s3 cp "$file" "s3://$R2_BUCKET/assets/$relative_path" \
            --endpoint-url=$R2_ENDPOINT \
            --cache-control "public, max-age=31536000, immutable" \
            --content-type "application/javascript"; then
            echo "❌ Failed to upload $file"
            exit 1
          fi
        done

        # Upload CSS files with explicit MIME type
        echo "🎨 Uploading CSS files with text/css MIME type..."
        find dist/assets -name "*.css" -type f | while read file; do
          relative_path=${file#dist/assets/}
          echo "  Uploading: $file → assets/$relative_path"
          if ! aws s3 cp "$file" "s3://$R2_BUCKET/assets/$relative_path" \
            --endpoint-url=$R2_ENDPOINT \
            --cache-control "public, max-age=31536000, immutable" \
            --content-type "text/css"; then
            echo "❌ Failed to upload $file"
            exit 1
          fi
        done

        # Upload other asset files (source maps, images, etc.)
        echo "📁 Uploading other asset files..."
        find dist/assets -type f ! -name "*.js" ! -name "*.css" | while read file; do
          relative_path=${file#dist/assets/}
          echo "  Uploading: $file → assets/$relative_path"
          if ! aws s3 cp "$file" "s3://$R2_BUCKET/assets/$relative_path" \
            --endpoint-url=$R2_ENDPOINT \
            --cache-control "public, max-age=31536000, immutable"; then
            echo "❌ Failed to upload $file"
            exit 1
          fi
        done

        echo "✅ Assets upload completed"

        # Upload HTML files with short-term caching
        echo "Uploading HTML files with short-term cache headers..."
        find dist -maxdepth 1 -name "*.html" -type f | while read file; do
          filename=$(basename "$file")
          echo "  Uploading HTML file: $filename"
          if ! aws s3 cp "$file" "s3://$R2_BUCKET/$filename" \
            --endpoint-url=$R2_ENDPOINT \
            --cache-control "public, max-age=300" \
            --content-type "text/html"; then
            echo "❌ Failed to upload HTML file $filename"
            exit 1
          fi
        done

        echo "✅ HTML files upload completed successfully"

        # Upload other root files (excluding JS/CSS which are in assets/)
        echo "Uploading root files (favicon, etc.)..."
        find dist -maxdepth 1 -type f ! -name "*.html" ! -name "*.js" ! -name "*.css" | while read file; do
          filename=$(basename "$file")
          echo "  Uploading root file: $filename"
          if ! aws s3 cp "$file" "s3://$R2_BUCKET/$filename" \
            --endpoint-url=$R2_ENDPOINT \
            --cache-control "public, max-age=86400"; then
            echo "❌ Failed to upload root file $filename"
            exit 1
          fi
        done

        echo "✅ Root files upload completed successfully"
        echo "success=true" >> $GITHUB_OUTPUT

    - name: Verify deployment
      shell: bash
      env:
        AWS_ACCESS_KEY_ID: ${{ inputs.r2-access-key-id }}
        AWS_SECRET_ACCESS_KEY: ${{ inputs.r2-secret-access-key }}
        AWS_DEFAULT_REGION: auto
      run: |
        # Enable strict error handling
        set -euo pipefail

        echo "Verifying deployment to R2..."
        R2_ENDPOINT="${{ steps.env_vars.outputs.R2_ENDPOINT }}"
        R2_BUCKET="${{ steps.env_vars.outputs.R2_BUCKET }}"

        echo "Root files:"
        aws s3 ls s3://$R2_BUCKET/ --endpoint-url=$R2_ENDPOINT
        echo ""
        echo "Assets directory:"
        aws s3 ls s3://$R2_BUCKET/assets/ --endpoint-url=$R2_ENDPOINT
        echo ""

        # Count total objects
        TOTAL_OBJECTS=$(aws s3 ls s3://$R2_BUCKET/ --endpoint-url=$R2_ENDPOINT --recursive | wc -l)
        echo "Total objects in bucket: $TOTAL_OBJECTS"

        if [ "$TOTAL_OBJECTS" -eq 0 ]; then
          echo "❌ No objects found in bucket - deployment failed"
          exit 1
        fi

        echo "✅ Deployment verification completed"

    - name: Invalidate Cloudflare Cache
      shell: bash
      if: inputs.cache-invalidation-enabled == 'true'
      continue-on-error: true
      run: |
        echo "Purging Cloudflare cache..."
        if [[ -n "${{ inputs.cloudflare-api-token }}" && -n "${{ inputs.cloudflare-zone-id }}" ]]; then
          response=$(curl -s -X POST "https://api.cloudflare.com/client/v4/zones/${{ inputs.cloudflare-zone-id }}/purge_cache" \
            -H "Authorization: Bearer ${{ inputs.cloudflare-api-token }}" \
            -H "Content-Type: application/json" \
            --data '{"purge_everything":true}')

          # Check for success (handle both compact and formatted JSON)
          if echo "$response" | grep -q '"success"[[:space:]]*:[[:space:]]*true'; then
            echo "✅ Cache invalidation successful"
          else
            echo "❌ Cache invalidation failed"
            echo "Response: $response"
          fi
        else
          echo "❌ Missing Cloudflare secrets - cache invalidation skipped"
        fi

    - name: Comprehensive Post-Deployment Verification
      shell: bash
      env:
        AWS_ACCESS_KEY_ID: ${{ inputs.r2-access-key-id }}
        AWS_SECRET_ACCESS_KEY: ${{ inputs.r2-secret-access-key }}
        AWS_DEFAULT_REGION: auto
      run: |
        # Enable strict error handling for critical tests, but allow some tests to fail gracefully
        set -euo pipefail

        echo "🔍 Comprehensive Deployment Verification"
        echo "======================================="
        echo "Environment: ${{ inputs.environment }}"
        echo "R2 Bucket: ${{ steps.env_vars.outputs.R2_BUCKET }}"
        echo "R2 Endpoint: ${{ steps.env_vars.outputs.R2_ENDPOINT }}"
        echo "CDN URL: ${{ steps.env_vars.outputs.CDN_URL }}"
        echo "API endpoint: ${{ inputs.api-url }}"
        echo "WebSocket endpoint: ${{ inputs.ws-url }}"
        echo ""

        # Test critical assets accessibility
        echo "🔍 Testing critical asset accessibility..."
        CDN_URL="${{ steps.env_vars.outputs.CDN_URL }}"

        echo "Testing CDN URL: $CDN_URL"

        # Wait a moment for CDN to propagate
        echo "⏳ Waiting 10 seconds for CDN propagation..."
        sleep 10

        # Test index.html (critical) with retry logic
        echo "🔍 Testing index.html accessibility..."
        MAX_RETRIES=3
        RETRY_COUNT=0
        INDEX_ACCESSIBLE=false

        while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
          echo "  Attempt $((RETRY_COUNT + 1))/$MAX_RETRIES: Testing $CDN_URL/index.html"

          # Get response details for debugging (single curl call for efficiency)
          RESPONSE=$(curl -s -I -w "HTTP_CODE:%{http_code}" "$CDN_URL/index.html" 2>&1)
          HTTP_CODE=$(echo "$RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)

          echo "  HTTP Code: $HTTP_CODE"

          if [ "$HTTP_CODE" = "200" ]; then
            echo "✅ index.html is accessible and returns 200 OK"
            INDEX_ACCESSIBLE=true

            # Verify Content-Type for index.html
            CONTENT_TYPE=$(echo "$RESPONSE" | grep -i "content-type" | cut -d: -f2 | tr -d ' \r\n' || echo "unknown")
            if [[ "$CONTENT_TYPE" == *"text/html"* ]]; then
              echo "✅ index.html has correct MIME type: $CONTENT_TYPE"
            else
              echo "⚠️  index.html MIME type unexpected: $CONTENT_TYPE"
            fi
            break
          else
            echo "  ❌ Attempt failed with HTTP code: $HTTP_CODE"
            if [ $RETRY_COUNT -lt $((MAX_RETRIES - 1)) ]; then
              echo "  ⏳ Waiting 15 seconds before retry..."
              sleep 15
            fi
          fi

          RETRY_COUNT=$((RETRY_COUNT + 1))
        done

        if [ "$INDEX_ACCESSIBLE" = false ]; then
          echo "❌ index.html is not accessible after $MAX_RETRIES attempts"
          echo "   This might indicate a CDN configuration issue or propagation delay"
          echo "   Final HTTP code: $HTTP_CODE"
          echo "   CDN URL tested: $CDN_URL/index.html"

          # Don't fail immediately - let's check R2 direct access
          echo "🔍 Checking if file exists in R2 bucket directly..."
          R2_ENDPOINT="${{ steps.env_vars.outputs.R2_ENDPOINT }}"
          R2_BUCKET="${{ steps.env_vars.outputs.R2_BUCKET }}"

          if aws s3 ls s3://$R2_BUCKET/index.html --endpoint-url=$R2_ENDPOINT >/dev/null 2>&1; then
            echo "✅ index.html exists in R2 bucket - this is likely a CDN propagation delay"
            echo "⚠️  Deployment succeeded but CDN access verification failed"
            echo "   The files should become accessible shortly via CDN"
          else
            echo "❌ index.html not found in R2 bucket - deployment may have failed"
            exit 1
          fi
        fi

        # Test favicon.ico (non-critical)
        if curl -f -s -I "$CDN_URL/favicon.ico" >/dev/null 2>&1; then
          echo "✅ favicon.ico is accessible"
        else
          echo "⚠️  favicon.ico is not accessible (may not exist)"
        fi

        # Test if we can access a sample JS asset (critical for app functionality)
        echo "🔍 Testing JavaScript asset accessibility..."

        # Only test JS assets if index.html was accessible
        if [ "$INDEX_ACCESSIBLE" = true ]; then
          SAMPLE_JS=$(curl -s "$CDN_URL/index.html" | grep -o 'assets/[^"]*\.js' | head -1)
          if [[ -n "$SAMPLE_JS" ]]; then
            echo "Found JS asset to test: $SAMPLE_JS"

            # Apply retry logic for JS asset accessibility
            JS_MAX_RETRIES=3
            JS_RETRY_COUNT=0
            JS_ACCESSIBLE=false

            while [ $JS_RETRY_COUNT -lt $JS_MAX_RETRIES ]; do
              echo "  Attempt $((JS_RETRY_COUNT + 1))/$JS_MAX_RETRIES: Testing $CDN_URL/$SAMPLE_JS"

              # Single curl call for efficiency
              JS_RESPONSE=$(curl -s -I -w "HTTP_CODE:%{http_code}" "$CDN_URL/$SAMPLE_JS" 2>&1)
              JS_HTTP_CODE=$(echo "$JS_RESPONSE" | grep "HTTP_CODE:" | cut -d: -f2)
              echo "  HTTP Code: $JS_HTTP_CODE"

              if [ "$JS_HTTP_CODE" = "200" ]; then
                echo "✅ Sample JavaScript asset ($SAMPLE_JS) is accessible"
                JS_ACCESSIBLE=true

                # Verify JS MIME type
                JS_CONTENT_TYPE=$(echo "$JS_RESPONSE" | grep -i "content-type" | cut -d: -f2 | tr -d ' \r\n' || echo "unknown")
                if [[ "$JS_CONTENT_TYPE" == *"javascript"* ]] || [[ "$JS_CONTENT_TYPE" == *"application/javascript"* ]]; then
                  echo "✅ JavaScript asset has correct MIME type: $JS_CONTENT_TYPE"
                else
                  echo "⚠️  JavaScript asset MIME type unexpected: $JS_CONTENT_TYPE"
                fi
                break
              else
                echo "  ❌ Attempt failed with HTTP code: $JS_HTTP_CODE"
                if [ $JS_RETRY_COUNT -lt $((JS_MAX_RETRIES - 1)) ]; then
                  echo "  ⏳ Waiting 10 seconds before retry..."
                  sleep 10
                fi
              fi

              JS_RETRY_COUNT=$((JS_RETRY_COUNT + 1))
            done

            if [ "$JS_ACCESSIBLE" = false ]; then
              echo "❌ Sample JavaScript asset ($SAMPLE_JS) is not accessible after $JS_MAX_RETRIES attempts"
              echo "   This might indicate a CDN configuration issue or propagation delay"
              echo "   Final HTTP code: $JS_HTTP_CODE"

              # Check if JS file exists in R2 bucket
              if aws s3 ls s3://$R2_BUCKET/$SAMPLE_JS --endpoint-url=$R2_ENDPOINT >/dev/null 2>&1; then
                echo "✅ JavaScript asset exists in R2 bucket - this is likely a CDN propagation delay"
                echo "⚠️  JS asset verification failed but file exists in R2"
              else
                echo "❌ JavaScript asset not found in R2 bucket - deployment may have failed"
                exit 1
              fi
            fi
          else
            echo "⚠️  Could not find JavaScript assets in index.html"
            echo "   This might be normal if the app uses a different asset structure"
          fi
        else
          echo "⚠️  Skipping JavaScript asset test since index.html was not accessible via CDN"
        fi

        # Enhanced smoke tests for API connectivity
        echo "🔍 Testing API endpoint connectivity..."
        API_HEALTH_URL="${{ inputs.api-url }}/health"
        if curl -f -s -m 10 "$API_HEALTH_URL" >/dev/null 2>&1; then
          echo "✅ API health endpoint is accessible"
        else
          echo "⚠️  API health endpoint not accessible (may be expected in some environments)"
        fi

        # Test CORS headers for frontend-backend communication
        echo "🔍 Testing CORS configuration..."
        CORS_HEADERS=$(curl -s -I -H "Origin: $CDN_URL" "${{ inputs.graphql-url }}" | grep -i "access-control" || echo "No CORS headers found")
        echo "CORS headers: $CORS_HEADERS"

        echo ""
        echo "✅ Frontend assets successfully deployed to Cloudflare R2 (${{ inputs.environment }})"
        echo "✅ Cache invalidation completed"
        echo "✅ Assets now available via Cloudflare CDN"
        echo "✅ Comprehensive verification passed"
        echo ""
        echo "📋 Rollback Information:"
        echo "If rollback is needed, previous versions can be restored from:"
        echo "- R2 bucket versioning (if enabled)"
        echo "- Previous Git commit deployment via manual dispatch"
        if [[ "${{ inputs.environment }}" == "production" ]]; then
          echo "- Emergency rollback: Redeploy staging version to production"
          echo "- Contact DevOps team for immediate assistance"
        else
          echo "- Contact DevOps team for emergency rollback procedures"
        fi
