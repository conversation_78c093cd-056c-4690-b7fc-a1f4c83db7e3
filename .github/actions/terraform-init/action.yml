name: 'Terraform Init'
description: 'Initialize Terraform with appropriate backend configuration'
inputs:
  terraform_dir:
    description: 'Path to the Terraform environment directory'
    required: true
  environment:
    description: 'Target environment (staging or production)'
    required: true
  backend_type:
    description: 'Backend type: local (for planning) or gcs (for apply)'
    required: true
    default: 'local'
  state_bucket:
    description: 'GCS state bucket (required for gcs backend)'
    required: false
runs:
  using: 'composite'
  steps:
    - name: Initialize Terraform
      shell: bash
      run: |
        cd ${{ inputs.terraform_dir }}

        if [[ "${{ inputs.backend_type }}" == "local" ]]; then
          echo "Initializing Terraform with local backend for planning phase..."

          # Try to copy backend.tf.local from current directory, then from root terraform directory
          if [[ -f "backend.tf.local" ]]; then
            echo "Found backend.tf.local in current directory"
            cp backend.tf.local backend.tf
          elif [[ -f "../../backend.tf.local" ]]; then
            echo "Found backend.tf.local in root terraform directory, copying..."
            cp ../../backend.tf.local backend.tf
          else
            echo "backend.tf.local not found, creating minimal local backend configuration"
            echo 'terraform {' > backend.tf
            echo '  backend "local" {' >> backend.tf
            echo '    path = "terraform.tfstate.d/${{ inputs.environment }}/terraform.tfstate"' >> backend.tf
            echo '  }' >> backend.tf
            echo '}' >> backend.tf
          fi

          terraform init -reconfigure

        elif [[ "${{ inputs.backend_type }}" == "gcs" ]]; then
          echo "Initializing Terraform with GCS backend for apply phase..."

          # Check if backend.tf already exists with GCS configuration
          if [[ -f "backend.tf" ]] && grep -q 'backend "gcs"' backend.tf; then
            echo "Found existing GCS backend configuration in backend.tf"
            echo "Using existing backend configuration..."
            terraform init -reconfigure
          else
            # Remove local backend file if it exists
            rm -f backend.tf

            # Validate state bucket is provided
            if [[ -z "${{ inputs.state_bucket }}" ]]; then
              echo "❌ Error: state_bucket is required for GCS backend"
              exit 1
            fi

            echo "Using state bucket: ${{ inputs.state_bucket }} for environment: ${{ inputs.environment }}"

            # Initialize with environment-specific GCS backend
            terraform init -reconfigure \
              -backend-config="bucket=${{ inputs.state_bucket }}" \
              -backend-config="prefix=terraform/state/${{ inputs.environment }}"
          fi

        else
          echo "❌ Error: Invalid backend_type '${{ inputs.backend_type }}'. Must be 'local' or 'gcs'"
          exit 1
        fi

        echo "✅ Terraform initialization completed with ${{ inputs.backend_type }} backend"

    - name: Select Terraform Workspace
      shell: bash
      run: |
        cd ${{ inputs.terraform_dir }}
        echo "Checking workspace: ${{ inputs.environment }}"

        # Check if workspace exists (using explicit pattern to avoid partial matches)
        if terraform workspace list | grep -qE "^\*?\s+${{ inputs.environment }}\s*$"; then
          echo "Workspace '${{ inputs.environment }}' exists, selecting..."
          terraform workspace select ${{ inputs.environment }}
        else
          echo "Workspace '${{ inputs.environment }}' doesn't exist, creating..."
          terraform workspace new ${{ inputs.environment }}
        fi

        echo "✅ Terraform workspace '${{ inputs.environment }}' selected"
