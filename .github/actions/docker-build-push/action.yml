name: 'Docker Build and Push'
description: 'Build Docker image and push to Artifact Registry'

inputs:
  dockerfile-path:
    description: 'Path to the Dockerfile'
    required: true
  build-context:
    description: 'Docker build context path'
    required: true
  image-name:
    description: 'Name of the Docker image'
    required: true
  environment:
    description: 'Environment (staging/production)'
    required: true
  artifact-registry-host:
    description: 'Artifact Registry host'
    required: true
  repository:
    description: 'Container repository name'
    required: true
  project-id:
    description: 'GCP Project ID'
    required: true
  build-args-json:
    description: 'JSON string of build arguments (e.g., {"ARG1": "val1", "ARG2": "val2"})'
    required: false
    default: ''

outputs:
  image-url:
    description: 'Full URL of the pushed image'
    value: ${{ steps.build.outputs.image-url }}

runs:
  using: 'composite'
  steps:
    - name: Build Docker image
      id: build
      shell: bash
      run: |
        IMAGE_URL="${{ inputs.artifact-registry-host }}/${{ inputs.project-id }}/${{ inputs.repository }}/${{ inputs.image-name }}:${{ inputs.environment }}-${{ github.sha }}"

        echo "Building Docker image: $IMAGE_URL"

        BUILD_ARGS_STRING=""
        if [[ -n "${{ inputs.build-args-json }}" && "${{ inputs.build-args-json }}" != "{}" ]]; then
          BUILD_ARGS_STRING=$(echo "${{ inputs.build-args-json }}" | jq -r 'to_entries | .[] | "--build-arg \(.key)=\(.value|@json)"' | tr '\n' ' ')
          # @json ensures that values are properly quoted if they contain spaces or special characters
        fi
        echo "Using build arguments: $BUILD_ARGS_STRING"

        docker build \
          -f "${{ inputs.dockerfile-path }}" \
          $BUILD_ARGS_STRING \
          -t "$IMAGE_URL" \
          "${{ inputs.build-context }}"

        echo "image-url=$IMAGE_URL" >> $GITHUB_OUTPUT

    - name: Push Docker image
      shell: bash
      run: |
        IMAGE_URL="${{ inputs.artifact-registry-host }}/${{ inputs.project-id }}/${{ inputs.repository }}/${{ inputs.image-name }}:${{ inputs.environment }}-${{ github.sha }}"

        echo "Pushing Docker image: $IMAGE_URL"
        docker push "$IMAGE_URL"

        # Also tag and push as latest for the environment
        LATEST_TAG="${{ inputs.artifact-registry-host }}/${{ inputs.project-id }}/${{ inputs.repository }}/${{ inputs.image-name }}:${{ inputs.environment }}-latest"
        docker tag "$IMAGE_URL" "$LATEST_TAG"
        docker push "$LATEST_TAG"

        echo "Successfully pushed images:"
        echo "  - $IMAGE_URL"
        echo "  - $LATEST_TAG"
