---
name: 'Deploy to Cloud Run'
description: 'Deploy service to Google Cloud Run'

inputs:
  service-name:
    description: 'Name of the Cloud Run service'
    required: true
  environment:
    description: 'Environment (staging/production)'
    required: true
  region:
    description: 'GCP region'
    required: true
  image:
    description: 'Docker image URL'
    required: true
  vpc-connector:
    description: 'VPC connector'
    required: false
  allow-unauthenticated:
    description: 'Allow unauthenticated access'
    required: false
    default: 'false'
  ingress:
    description: 'Ingress traffic sources. Must be one of: all, internal, internal-and-cloud-load-balancing'
    required: false
    default: 'all'
  port:
    description: 'Container port'
    required: false
    default: '8080'
  startup-probe:
    description: 'Startup probe HTTP path (e.g., /api/health). Must start with /'
    required: false
  min-instances:
    description: 'Minimum instances'
    required: false
    default: '0'
  max-instances:
    description: 'Maximum instances'
    required: false
    default: 'default'
  cpu:
    description: 'CPU allocation'
    required: false
    default: '1'
  memory:
    description: 'Memory allocation'
    required: false
    default: '512Mi'
  concurrency:
    description: 'Request concurrency'
    required: false
    default: '80'
  timeout:
    description: 'Request timeout'
    required: false
    default: '300'
  env_vars_json: # Renamed from env-vars for clarity
    description: 'Environment variables as a JSON string'
    required: false
  run-migrations:
    description: 'Whether to run database migrations'
    required: false
    default: 'false'
  # Secret inputs
  db-url:
    description: 'Database URL'
    required: false
  redis-url:
    description: 'Redis URL'
    required: false
  clerk-api-key:
    description: 'Clerk API Key'
    required: false
  clerk-jwt-public-key:
    description: 'Clerk JWT Public Key'
    required: false
  clerk-webhook-secret:
    description: 'Clerk Webhook Secret'
    required: false
  storage-bucket:
    description: 'Storage bucket'
    required: false
  cdn-url:
    description: 'CDN URL'
    required: false
  vite-graphql-api-url:
    description: 'GraphQL API URL'
    required: false
  db-instance-name:
    description: 'Database instance name'
    required: false
  alembic-db-url:
    description: 'Alembic database URL'
    required: false
  cloudsql-instances:
    description: 'Cloud SQL instances to connect to (format: PROJECT_ID:REGION:INSTANCE_NAME)'
    required: false

outputs:
  generated_gcloud_command:
    description: 'The generated gcloud run deploy command that was executed.'
    value: ${{ steps.deploy_step.outputs.generated_deploy_command }}

runs:
  using: 'composite'
  steps:
    - name: Run database migrations (if needed)
      if: inputs.run-migrations == 'true'
      shell: bash
      env: # Make alembic specific env vars available to the migration script
        DATABASE_URL: ${{ inputs.alembic-db-url }}
        DB_INSTANCE_NAME: ${{ inputs.db-instance-name }}
        ENVIRONMENT: ${{ inputs.environment }}
      run: |
        echo "🚀 Running database migrations for ${{ inputs.service-name }} in ${{ inputs.environment }}"

        # Validate required environment variables
        if [ -z "$DATABASE_URL" ]; then
          echo "❌ Error: DATABASE_URL is required for migrations"
          exit 1
        fi

        echo "📊 Migration Environment:"
        echo "  Service: ${{ inputs.service-name }}"
        echo "  Environment: ${{ inputs.environment }}"
        echo "  Database URL: [REDACTED - $(echo "$DATABASE_URL" | wc -c) characters]"

        # Check if DATABASE_URL uses Unix socket (Cloud SQL)
        if [[ "$DATABASE_URL" == *"/cloudsql/"* ]]; then
          echo "🔧 Detected Cloud SQL Unix socket connection, setting up Cloud SQL Auth Proxy..."

          # Verify GCP authentication is available
          echo "🔐 Checking GCP authentication..."
          if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -1 > /dev/null; then
            echo "❌ Error: No active GCP authentication found"
            echo "💡 Ensure 'Authenticate to Google Cloud' step runs before migrations"
            exit 1
          fi

          ACTIVE_ACCOUNT=$(gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -1)
          echo "✅ Active GCP account: $ACTIVE_ACCOUNT"

          # Create /cloudsql directory with secure permissions
          echo "📁 Creating /cloudsql directory with secure permissions..."
          sudo mkdir -p /cloudsql
          sudo chown $(whoami):$(whoami) /cloudsql
          chmod 755 /cloudsql

          # Extract instance connection name from DATABASE_URL
          INSTANCE_CONNECTION_NAME=$(echo "$DATABASE_URL" | grep -o '/cloudsql/[^/]*' | sed 's|/cloudsql/||')
          echo "  Instance: [REDACTED]"

          # The Cloud SQL Auth Proxy will create the instance directory with correct permissions
          echo "📁 Proxy will create instance directory automatically with proper permissions"

          # Install Cloud SQL Auth Proxy
          echo "📦 Installing Cloud SQL Auth Proxy..."
          curl -o cloud-sql-proxy https://storage.googleapis.com/cloud-sql-connectors/cloud-sql-proxy/v2.8.0/cloud-sql-proxy.linux.amd64
          chmod +x cloud-sql-proxy

          # Add signal handling for proper cleanup
          cleanup_proxy() {
            if [[ -n "$PROXY_PID" ]]; then
              echo "🧹 Cleaning up Cloud SQL Auth Proxy (PID: $PROXY_PID)..."
              kill $PROXY_PID 2>/dev/null || true
              wait $PROXY_PID 2>/dev/null || true
              echo "✅ Proxy cleanup completed"
            fi
          }
          trap 'cleanup_proxy' EXIT INT TERM

          # Start Cloud SQL Auth Proxy in background using gcloud authentication
          echo "🚀 Starting Cloud SQL Auth Proxy..."
          ./cloud-sql-proxy --unix-socket /cloudsql "$INSTANCE_CONNECTION_NAME" &
          PROXY_PID=$!

          # Wait for proxy to be ready with exponential backoff
          wait_for_proxy() {
            local max_attempts=10
            local attempt=1
            local delay=1
            
            echo "⏳ Waiting for Cloud SQL Auth Proxy to be ready..."
            while [[ $attempt -le $max_attempts ]]; do
              # Check if proxy process is still running
              if ! kill -0 $PROXY_PID 2>/dev/null; then
                echo "❌ Cloud SQL Auth Proxy process died"
                return 1
              fi
              
              # Check if socket is ready
              if [[ -S "/cloudsql/$INSTANCE_CONNECTION_NAME/.s.PGSQL.5432" ]]; then
                echo "✅ Cloud SQL Auth Proxy is ready"
                return 0
              fi
              
              echo "  Attempt $attempt/$max_attempts: Waiting for socket (${delay}s delay)..."
              sleep $delay
              delay=$((delay * 2))  # Exponential backoff
              attempt=$((attempt + 1))
            done
            
            echo "❌ Proxy failed to start after $max_attempts attempts"
            return 1
          }

          if ! wait_for_proxy; then
            cleanup_proxy
            exit 1
          fi
        else
          echo "🔧 Using direct database connection (non-Cloud SQL)"
        fi

        # Check if we have access to the backend source code
        if [ -d "apps/backend" ]; then
          echo "📁 Found backend directory, running migrations from source..."
          cd apps/backend

          # Install Python dependencies if requirements exist
          if [ -f "requirements.txt" ]; then
            echo "📦 Installing Python dependencies..."
            pip3 install -r requirements.txt
          elif [ -f "pyproject.toml" ]; then
            echo "📦 Installing Python dependencies from pyproject.toml..."
            pip3 install .
          else
            echo "📦 Installing basic migration dependencies..."
            pip3 install alembic psycopg2-binary sqlalchemy
          fi

          # Run migrations using the project's alembic setup with coordination
          if [ -f "alembic.ini" ]; then
            echo "🔧 Using project alembic.ini..."
            echo "🔒 Coordinating migrations across instances..."
            
            # Run migrations with PostgreSQL advisory locks for coordination
            python3 -c "
            import psycopg2
            import sys
            import subprocess
            import time
            import os
            
            # Connect to database
            try:
                conn = psycopg2.connect(os.environ['DATABASE_URL'])
                cur = conn.cursor()
                
                # Try to acquire migration lock (wait up to 5 minutes)
                print('🔒 Acquiring migration coordination lock...')
                cur.execute('SELECT pg_try_advisory_lock_timeout(hashtext(\'alembic_migration_lock\'), 300000)')
                lock_acquired = cur.fetchone()[0]
                
                if lock_acquired:
                    print('✅ Migration lock acquired, running migrations...')
                    
                    # Run the migration
                    result = subprocess.run(['python3', '-m', 'alembic', 'upgrade', 'head'], 
                                          capture_output=True, text=True, cwd='.')
                    
                    if result.returncode != 0:
                        print(f'❌ Migration failed: {result.stderr}')
                        sys.exit(1)
                    else:
                        print('✅ Migrations completed successfully')
                        print(result.stdout)
                else:
                    print('⏳ Another instance is running migrations, waiting for completion...')
                    
                    # Wait for other migration to complete (check every 10 seconds)
                    for i in range(30):  # Max 5 minutes
                        time.sleep(10)
                        cur.execute('SELECT pg_try_advisory_lock(hashtext(\'alembic_migration_lock\'))')
                        if cur.fetchone()[0]:
                            print('✅ Other migration completed, this instance can proceed')
                            cur.execute('SELECT pg_advisory_unlock(hashtext(\'alembic_migration_lock\'))')
                            break
                    else:
                        print('❌ Timeout waiting for other migration to complete')
                        sys.exit(1)
                        
            except Exception as e:
                print(f'❌ Migration coordination failed: {e}')
                sys.exit(1)
            finally:
                # Always release the lock
                try:
                    if 'cur' in locals():
                        cur.execute('SELECT pg_advisory_unlock_all()')
                    if 'conn' in locals():
                        conn.close()
                except:
                    pass
            "
          elif [ -d "alembic" ]; then
            echo "🔧 Found alembic directory, running coordinated migrations..."
            echo "🔒 Coordinating migrations across instances..."
            
            # Same coordination logic for alembic directory
            python3 -c "
            import psycopg2
            import sys
            import subprocess
            import time
            import os
            
            # Connect to database
            try:
                conn = psycopg2.connect(os.environ['DATABASE_URL'])
                cur = conn.cursor()
                
                # Try to acquire migration lock (wait up to 5 minutes)
                print('🔒 Acquiring migration coordination lock...')
                cur.execute('SELECT pg_try_advisory_lock_timeout(hashtext(\'alembic_migration_lock\'), 300000)')
                lock_acquired = cur.fetchone()[0]
                
                if lock_acquired:
                    print('✅ Migration lock acquired, running migrations...')
                    
                    # Run the migration
                    result = subprocess.run(['python3', '-m', 'alembic', 'upgrade', 'head'], 
                                          capture_output=True, text=True, cwd='.')
                    
                    if result.returncode != 0:
                        print(f'❌ Migration failed: {result.stderr}')
                        sys.exit(1)
                    else:
                        print('✅ Migrations completed successfully')
                        print(result.stdout)
                else:
                    print('⏳ Another instance is running migrations, waiting for completion...')
                    
                    # Wait for other migration to complete (check every 10 seconds)
                    for i in range(30):  # Max 5 minutes
                        time.sleep(10)
                        cur.execute('SELECT pg_try_advisory_lock(hashtext(\'alembic_migration_lock\'))')
                        if cur.fetchone()[0]:
                            print('✅ Other migration completed, this instance can proceed')
                            cur.execute('SELECT pg_advisory_unlock(hashtext(\'alembic_migration_lock\'))')
                            break
                    else:
                        print('❌ Timeout waiting for other migration to complete')
                        sys.exit(1)
                        
            except Exception as e:
                print(f'❌ Migration coordination failed: {e}')
                sys.exit(1)
            finally:
                # Always release the lock
                try:
                    if 'cur' in locals():
                        cur.execute('SELECT pg_advisory_unlock_all()')
                    if 'conn' in locals():
                        conn.close()
                except:
                    pass
            "
          else
            echo "❌ No alembic configuration found in backend directory"
            exit 1
          fi
        else
          echo "❌ Backend directory not found - migrations require source code access"
          echo "💡 Ensure the repository is checked out before running migrations"
          exit 1
        fi

        MIGRATION_EXIT_CODE=$?

        # Cleanup will be handled by the trap signal handler
        # The trap ensures proper cleanup even if script is interrupted

        if [ $MIGRATION_EXIT_CODE -eq 0 ]; then
          echo "✅ Database migrations completed successfully"
        else
          echo "❌ Database migrations failed"
          exit 1
        fi

    - name: Deploy to Cloud Run
      id: deploy_step # Added id
      shell: bash
      env: # Added env block
        INPUT_ENV_VARS_JSON: ${{ inputs.env_vars_json }}
      run: |
        echo "ACTION_SCRIPT_VERSION_NOVEMBER_2025_DEBUG"
        SERVICE_NAME="${{ inputs.service-name }}-${{ inputs.environment }}"

        echo "Deploying ${{ inputs.service-name }} to ${{ inputs.environment }} environment"
        echo "Service name: $SERVICE_NAME"
        echo "Image: ${{ inputs.image }}"
        echo "Region: ${{ inputs.region }}"

        # Debug: Show all input values
        echo "Debug: All input values:"
        echo "  environment: [${{ inputs.environment }}]"
        echo "  ingress: [${{ inputs.ingress }}]"
        echo "  startup-probe: [${{ inputs.startup-probe }}]"
        echo "  service-name: [${{ inputs.service-name }}]"

        # Validate environment parameter
        environment="${{ inputs.environment }}"
        case "$environment" in
          "dev"|"staging"|"production")
            echo "✅ Valid environment: $environment"
            ;;
          *)
            echo "❌ Error: Invalid environment '$environment'. Supported values are 'dev', 'staging', 'production'."
            echo "Debug: Environment value length: ${#environment}"
            echo "Debug: Environment value hex dump:"
            echo -n "$environment" | hexdump -C
            exit 1
            ;;
        esac

        # Validate ingress setting
        ingress="${{ inputs.ingress }}"
        case "$ingress" in
          "all"|"internal"|"internal-and-cloud-load-balancing")
            echo "✅ Valid ingress value: $ingress"
            ;;
          *)
            echo "❌ Error: Invalid ingress value '$ingress'. Supported values are 'all', 'internal', 'internal-and-cloud-load-balancing'."
            echo "Debug: Ingress value length: ${#ingress}"
            echo "Debug: Ingress value hex dump:"
            echo -n "$ingress" | hexdump -C
            exit 1
            ;;
        esac

        # Validate startup probe format if specified
        if [ -n "${{ inputs.startup-probe }}" ]; then
          if [[ "${{ inputs.startup-probe }}" != /* ]]; then
            echo "Error: Startup probe path '${{ inputs.startup-probe }}' must start with '/' (e.g., '/api/health')."
            exit 1
          fi
          echo "Valid startup probe path: ${{ inputs.startup-probe }}"
        fi

        # Build the gcloud run deploy command
        DEPLOY_CMD="gcloud run deploy $SERVICE_NAME"
        DEPLOY_CMD="$DEPLOY_CMD --image=${{ inputs.image }}"
        DEPLOY_CMD="$DEPLOY_CMD --region=${{ inputs.region }}"
        DEPLOY_CMD="$DEPLOY_CMD --platform=managed"
        DEPLOY_CMD="$DEPLOY_CMD --port=${{ inputs.port }}"
        DEPLOY_CMD="$DEPLOY_CMD --cpu=${{ inputs.cpu }}"
        DEPLOY_CMD="$DEPLOY_CMD --memory=${{ inputs.memory }}"
        DEPLOY_CMD="$DEPLOY_CMD --concurrency=${{ inputs.concurrency }}"
        DEPLOY_CMD="$DEPLOY_CMD --timeout=${{ inputs.timeout }}"
        DEPLOY_CMD="$DEPLOY_CMD --min-instances=${{ inputs.min-instances }}"

        if [ "${{ inputs.max-instances }}" != "default" ]; then
          DEPLOY_CMD="$DEPLOY_CMD --max-instances=${{ inputs.max-instances }}"
        fi

        if [ "${{ inputs.allow-unauthenticated }}" = "true" ]; then
          DEPLOY_CMD="$DEPLOY_CMD --allow-unauthenticated"
        else
          DEPLOY_CMD="$DEPLOY_CMD --no-allow-unauthenticated"
        fi

        # Add ingress setting
        DEPLOY_CMD="$DEPLOY_CMD --ingress=${{ inputs.ingress }}"

        if [ -n "${{ inputs.vpc-connector }}" ]; then
          DEPLOY_CMD="$DEPLOY_CMD --vpc-connector=${{ inputs.vpc-connector }}"
        fi

        # Add Cloud SQL instances if specified
        if [ -n "${{ inputs.cloudsql-instances }}" ]; then
          DEPLOY_CMD="$DEPLOY_CMD --add-cloudsql-instances=${{ inputs.cloudsql-instances }}"
        fi

        # Add startup probe if specified
        if [ -n "${{ inputs.startup-probe }}" ]; then
          DEPLOY_CMD="$DEPLOY_CMD --startup-probe=httpGet.path=${{ inputs.startup-probe }},httpGet.port=${{ inputs.port }}"
        fi

        # Convert JSON string of env vars to gcloud format
        echo "Debug: Raw INPUT_ENV_VARS_JSON is: [$INPUT_ENV_VARS_JSON]"

        # Check if INPUT_ENV_VARS_JSON is empty or null
        if [ -z "$INPUT_ENV_VARS_JSON" ] || [ "$INPUT_ENV_VARS_JSON" = "null" ]; then
          echo "Debug: INPUT_ENV_VARS_JSON is empty or null. Skipping environment variables."
        else
          # Validate JSON syntax first
          if ! echo "$INPUT_ENV_VARS_JSON" | jq empty 2>/dev/null; then
            echo "❌ Error: Invalid JSON syntax in INPUT_ENV_VARS_JSON"
            echo "JSON content: $INPUT_ENV_VARS_JSON"
            exit 1
          fi

          # Check if it's a valid object with keys
          json_check_result=$(echo "$INPUT_ENV_VARS_JSON" | jq -e '. != null and (if type == "object" then (keys | length > 0) else true end)' > /dev/null; echo $?)
          echo "Debug: jq check exit code is: [$json_check_result]"

          if [ "$json_check_result" -eq 0 ]; then
            echo "✅ JSON validation passed. Proceeding to create env_vars_file."

            # Create a temporary file for environment variables in YAML format for gcloud
            env_vars_file=$(mktemp)

            # Convert JSON to YAML format that gcloud expects
            echo "$INPUT_ENV_VARS_JSON" | jq -r 'to_entries | map("\(.key): \"\(.value|tostring)\"") | .[]' > "$env_vars_file"

            echo "Debug: Created env vars file in YAML format with contents:"
            cat "$env_vars_file"

            # Validate the YAML file format
            if [ -s "$env_vars_file" ]; then
              # Check if the file has the expected YAML format (key: value)
              if grep -q "^[A-Z_][A-Z0-9_]*: " "$env_vars_file"; then
                echo "✅ env_vars_file is valid YAML format. Using --env-vars-file."
                DEPLOY_CMD="$DEPLOY_CMD --env-vars-file=$env_vars_file"
              else
                echo "❌ Error: env_vars_file does not have valid YAML format"
                echo "Expected format: KEY: \"value\""
                exit 1
              fi
            else
              echo "Debug: env_vars_file is empty. Not appending --env-vars-file."
            fi
          else
            echo "❌ Error: jq check failed with exit code [$json_check_result]. Invalid JSON structure."
            exit 1
          fi
        fi

        # For act testing, just show what would be deployed
        if [ "${ACT:-false}" = "true" ]; then
          echo "🎭 ACT MODE: Would execute the following command:"
          echo "------------------------------------------------------------------"
          echo "Generated gcloud run deploy command (ACT MODE):"
          echo "$DEPLOY_CMD"
          echo "------------------------------------------------------------------"
          echo ""
          echo "✅ Deployment simulation completed successfully"
        else
          echo "Executing deployment command:"
          echo "------------------------------------------------------------------"
          echo "Generated gcloud run deploy command:"
          echo "$DEPLOY_CMD"
          echo "------------------------------------------------------------------"
          eval "$DEPLOY_CMD"
          echo "✅ Deployment command evaluation completed."
          echo "✅ Deployment completed successfully" # This line was already there, keeping it.

          # Clean up temporary env vars file if it exists
          if [ -n "$env_vars_file" ] && [ -f "$env_vars_file" ]; then
            rm -f "$env_vars_file"
            echo "Debug: Cleaned up temporary env vars file"
          fi
        fi

    - name: Get service URL
      shell: bash
      run: |
        # Ensuring script readability and clean EOL
        SERVICE_NAME="${{ inputs.service-name }}-${{ inputs.environment }}"

        if [ "${ACT:-false}" = "true" ]; then
          echo "🎭 ACT MODE: Service URL would be available at:"
          echo "https://$SERVICE_NAME-${{ inputs.region }}-${{ github.repository_owner }}.run.app"
        else
          SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=${{ inputs.region }} --format="value(status.url)")
          echo "🚀 Service deployed successfully!"
          echo "📍 Service URL: $SERVICE_URL"
        fi
        # End of script
