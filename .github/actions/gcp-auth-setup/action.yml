name: 'GCP Authentication Setup'
description: 'Set up GCP authentication using Workload Identity'

inputs:
  project-id:
    description: 'GCP Project ID'
    required: true
  service-account:
    description: 'GCP Service Account email'
    required: true
  workload-identity-provider:
    description: 'Workload Identity Provider'
    required: true

runs:
  using: 'composite'
  steps:
    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        service_account: ${{ inputs['service-account'] }}
        workload_identity_provider: ${{ inputs['workload-identity-provider'] }}

    - name: Set up gcloud CLI
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ inputs['project-id'] }}

    - name: Configure Docker for Artifact Registry
      shell: bash
      run: |
        gcloud auth configure-docker us-central1-docker.pkg.dev --quiet

    - name: Verify authentication
      shell: bash
      run: |
        gcloud auth list
        gcloud config list project
