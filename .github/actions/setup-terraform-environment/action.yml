name: 'Setup Terraform Environment'
description: 'Verify Terraform directory structure and setup common environment variables'
inputs:
  environment:
    description: 'Target environment (staging or production)'
    required: true
  project_id:
    description: 'GCP Project ID'
    required: true
  project_number:
    description: 'GCP Project Number'
    required: true
outputs:
  terraform_dir:
    description: 'Path to the Terraform environment directory'
    value: ${{ steps.verify.outputs.terraform_dir }}
runs:
  using: 'composite'
  steps:
    - name: Verify Terraform Directory Structure
      id: verify
      shell: bash
      run: |
        echo "Verifying Terraform directory structure..."

        # Composite actions always run from repository root, regardless of job working-directory
        TERRAFORM_DIR="terraform/environments/${{ inputs.environment }}"
        TERRAFORM_ROOT_CHECK="terraform/environments/"

        if [[ ! -d "$TERRAFORM_DIR" ]]; then
          echo "❌ Error: Terraform environment directory not found: $TERRAFORM_DIR"
          echo "Available directories:"
          ls -la "$TERRAFORM_ROOT_CHECK" || echo "$TERRAFORM_ROOT_CHECK directory does not exist"
          ls -la "$TERRAFORM_ROOT_CHECK" || echo "$TERRAFORM_ROOT_CHECK directory does not exist"
          exit 1
        fi

        echo "✅ Terraform environment directory found: $TERRAFORM_DIR"

        # Verify required files exist
        if [[ ! -f "$TERRAFORM_DIR/main.tf" && ! -f "$TERRAFORM_DIR/versions.tf" ]]; then
          echo "❌ Warning: No main.tf or versions.tf found in $TERRAFORM_DIR"
          echo "Available files:"
          ls -la "$TERRAFORM_DIR"
        fi

        echo "terraform_dir=$TERRAFORM_DIR" >> $GITHUB_OUTPUT

    - name: Setup Standard Terraform Variables
      id: setup
      shell: bash
      run: |
        echo "Setting standard variables for Terraform execution"
        echo "TF_VAR_project_id=${{ inputs.project_id }}" >> $GITHUB_ENV

        echo "✅ Standard Terraform variables configured"
