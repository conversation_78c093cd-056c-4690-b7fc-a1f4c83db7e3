name: Deploy Frontend Assets

# This workflow deploys frontend assets to Cloudflare R2 storage
# - Triggered by: CI Pipeline completion (ensures tests pass before deployment)
# - Push to main: Deploys to staging automatically when web changes detected, then production with manual approval
# - Manual dispatch: Choose environment (staging/production) - bypasses change detection and CI dependency
# Updated for R2 migration: Uses Cloudflare R2 instead of GCS + proxy
# Updated: Added CI Pipeline dependency for quality gates
# Updated: Automatic triggers use change detection, manual dispatch bypasses it
# Updated: Added automatic production deployment after staging with manual approval
# Updated: Refactored to reduce code duplication and improve security
# Performance Note: Staging and production builds are separate to ensure environment-specific
# configurations are correctly embedded. Future optimization could reuse artifacts if builds
# become environment-agnostic.
# Updated: Eliminated code duplication using reusable composite action

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
  workflow_run:
    workflows: ["CI Pipeline (Static Analysis & Tests)"]
    types:
      - completed
    branches: [main]

env:
  NODE_VERSION: '18'
  CACHE_INVALIDATION_ENABLED: true
  # Cloudflare R2 configuration
  CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
  CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}

permissions:
  contents: read

jobs:
  check-ci-status:
    name: 'Check CI Pipeline Status'
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_run'
    outputs:
      ci_success: ${{ steps.ci_check.outputs.success }}
    steps:
      - name: Check CI Pipeline Result
        id: ci_check
        run: |
          if [[ "${{ github.event.workflow_run.conclusion }}" == "success" ]]; then
            echo "✅ CI Pipeline passed successfully"
            echo "success=true" >> $GITHUB_OUTPUT
          else
            echo "❌ CI Pipeline failed with conclusion: ${{ github.event.workflow_run.conclusion }}"
            echo "success=false" >> $GITHUB_OUTPUT
            exit 1
          fi

  changes:
    name: 'Detect Changes'
    runs-on: ubuntu-latest
    needs: [check-ci-status]
    if: always() && (needs.check-ci-status.outputs.ci_success == 'true' || github.event_name != 'workflow_run')
    outputs:
      web: ${{ steps.changes.outputs.any_changed }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check for web changes
        id: changes
        uses: tj-actions/changed-files@v46
        with:
          files: |
            apps/web/**

      - name: Display change information
        run: |
          echo "📊 Change Detection Results"
          echo "==========================="
          echo "Web changes detected: ${{ steps.changes.outputs.any_changed }}"
          if [[ "${{ github.event_name }}" == "workflow_run" ]]; then
            echo "Note: Automatic deployment will only proceed if changes detected"
          else
            echo "Note: Manual deployment will proceed regardless of changes"
          fi
          echo "web=${{ steps.changes.outputs.any_changed }}" >> $GITHUB_OUTPUT

  deploy-staging:
    name: 'Build and Deploy to Staging'
    runs-on: ubuntu-latest
    needs: [check-ci-status, changes]
    if: |
      always() && (
        (
          github.event_name == 'workflow_run' &&
          needs.changes.outputs.web == 'true' &&
          needs.check-ci-status.outputs.ci_success == 'true'
        ) || (
          github.event_name == 'workflow_dispatch' &&
          github.event.inputs.environment == 'staging'
        )
      )
    environment: staging
    outputs:
      deployment_success: ${{ steps.deploy-action.outputs.deployment-success }}

    steps:
      - name: Display Deployment Information
        run: |
          echo "🚀 Staging Deployment Information"
          echo "========================================"
          echo "Environment: staging"
          echo "Trigger: ${{ github.event_name }}"
          if [[ "${{ github.event_name }}" == "workflow_run" ]]; then
            echo "CI Pipeline Status: ${{ github.event.workflow_run.conclusion }}"
            echo "CI Pipeline Run ID: ${{ github.event.workflow_run.id }}"
            echo "CI Pipeline URL: ${{ github.event.workflow_run.html_url }}"
          elif [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "Manual staging deployment triggered"
            echo "Target environment: ${{ github.event.inputs.environment }}"
          fi
          echo "========================================"

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy Frontend
        id: deploy-action
        uses: ./.github/actions/deploy-frontend
        with:
          environment: staging
          api-url: https://api-staging.vedavivi.app
          graphql-url: https://api-staging.vedavivi.app/graphql
          ws-url: wss://ws-staging.vedavivi.app
          assets-url: https://www-staging.vedavivi.app/assets
          cdn-url: https://www-staging.vedavivi.app
          r2-bucket: a2a-platform-web-assets-staging
          node-version: ${{ env.NODE_VERSION }}
          cloudflare-account-id: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          r2-access-key-id: ${{ secrets.R2_ACCESS_KEY_ID }}
          r2-secret-access-key: ${{ secrets.R2_SECRET_ACCESS_KEY }}
          clerk-publishable-key: ${{ secrets.VITE_CLERK_PUBLISHABLE_KEY }}
          cloudflare-api-token: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          cloudflare-zone-id: ${{ secrets.CLOUDFLARE_ZONE_ID }}
          cache-invalidation-enabled: ${{ env.CACHE_INVALIDATION_ENABLED }}

  deploy-production:
    name: 'Build and Deploy to Production'
    runs-on: ubuntu-latest
    needs: [check-ci-status, changes, deploy-staging]
    if: |
      always() && (
        (
          github.event_name == 'workflow_run' &&
          needs.deploy-staging.outputs.deployment_success == 'true'
        ) || (
          github.event_name == 'workflow_dispatch' &&
          github.event.inputs.environment == 'production'
        )
      )
    environment: production

    steps:
      - name: Display Deployment Information
        run: |
          echo "🚀 Production Deployment Information"
          echo "========================================"
          echo "Environment: production"
          echo "Trigger: ${{ github.event_name }}"
          if [[ "${{ github.event_name }}" == "workflow_run" ]]; then
            echo "Production deployment following successful staging"
          elif [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "Manual production deployment triggered"
            echo "Target environment: ${{ github.event.inputs.environment }}"
          fi
          echo "========================================"

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy Frontend
        id: deploy-action
        uses: ./.github/actions/deploy-frontend
        with:
          environment: production
          api-url: https://api.vedavivi.app
          graphql-url: https://api.vedavivi.app/graphql
          ws-url: wss://ws.vedavivi.app
          assets-url: https://vedavivi.app/assets
          cdn-url: https://vedavivi.app
          r2-bucket: a2a-platform-web-assets-production
          node-version: ${{ env.NODE_VERSION }}
          cloudflare-account-id: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          r2-access-key-id: ${{ secrets.R2_ACCESS_KEY_ID }}
          r2-secret-access-key: ${{ secrets.R2_SECRET_ACCESS_KEY }}
          clerk-publishable-key: ${{ secrets.VITE_CLERK_PUBLISHABLE_KEY }}
          cloudflare-api-token: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          cloudflare-zone-id: ${{ secrets.CLOUDFLARE_ZONE_ID }}
          cache-invalidation-enabled: ${{ env.CACHE_INVALIDATION_ENABLED }}
