name: Deploy Infrastructure

# Combined infrastructure deployment workflow
# Supports both automatic (CI-triggered) and manual deployments
# Automatic: Triggered by successful Terraform Module Tests (staging only)
# Manual: Triggered by workflow_dispatch (any environment)

on:
  workflow_run:
    workflows: ["Terraform Module Tests"]
    types:
      - completed
    branches: [main]

  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - dev
          - staging
          - production
      skip_tests_check:
        description: 'Skip Terraform tests verification (manual override)'
        required: false
        default: false
        type: boolean

env:
  TF_LOG: INFO
  TF_VERSION: "1.12.0"
  GCP_PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  EFFECTIVE_ENVIRONMENT: ${{ github.event.inputs.environment || 'staging' }}
  # CDN-specific environment variables
  TF_VAR_cloudflare_api_token: ${{ secrets.CLOUDFLARE_API_TOKEN }}
  TF_VAR_cloudflare_account_id: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
  TF_VAR_cloudflare_zone_id: ${{ secrets.CLOUDFLARE_ZONE_ID }}
  TF_VAR_backend_origin_url: ${{ secrets.BACKEND_ORIGIN_URL }}
  TF_VAR_websocket_origin_url: ${{ secrets.WEBSOCKET_ORIGIN_URL }}
  TF_VAR_root_domain: ${{ secrets.ROOT_DOMAIN || 'vedavivi.app' }}
  # Authentication environment variables
  WORKLOAD_IDENTITY_PROVIDER: ${{ secrets.WORKLOAD_IDENTITY_PROVIDER }}
  GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}
  # Cloudflare Worker variables
  CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
  CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
  # Resource creation control from GitHub Actions secrets
  TF_VAR_create_dns_records: ${{ secrets.CREATE_DNS_RECORDS || 'false' }}   # Default: do NOT create DNS records (they exist)
  TF_VAR_create_page_rules: ${{ secrets.CREATE_PAGE_RULES || 'false' }}    # Default: import existing page rules
  TF_VAR_create_workers: ${{ secrets.CREATE_WORKERS || 'false' }}          # Default: do NOT create Cloudflare Workers (they may exist)
  # Cache configuration environment variables
  TF_VAR_cache_api_enabled: ${{ vars.CACHE_API_ENABLED || '' }}
  TF_VAR_cache_graphql_enabled: ${{ vars.CACHE_GRAPHQL_ENABLED || '' }}
  TF_VAR_cache_development_mode: ${{ vars.CACHE_DEVELOPMENT_MODE || '' }}
  TF_VAR_cache_static_assets_ttl: ${{ vars.CACHE_STATIC_ASSETS_TTL || '' }}
  TF_VAR_cache_html_ttl: ${{ vars.CACHE_HTML_TTL || '' }}
  TF_VAR_cache_api_ttl: ${{ vars.CACHE_API_TTL || '' }}
  TF_VAR_cache_graphql_ttl: ${{ vars.CACHE_GRAPHQL_TTL || '' }}

permissions:
  contents: read
  id-token: write  # For Workload Identity Federation

jobs:
  check-prerequisites:
    name: 'Check Prerequisites'
    runs-on: ubuntu-latest
    outputs:
      should_deploy: ${{ steps.check.outputs.should_deploy }}
      deployment_type: ${{ steps.check.outputs.deployment_type }}
      deployment_reason: ${{ steps.check.outputs.deployment_reason }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Check for infrastructure changes
        id: check_infra_changes
        if: github.event_name == 'workflow_run'
        uses: tj-actions/changed-files@v46
        with:
          files: |
            terraform/**
            .github/workflows/deploy-infra.yml
            .github/actions/setup-terraform-environment/**
            .github/actions/terraform-init/**

      - name: Determine Deployment Type and Prerequisites
        id: check
        run: |
          echo "🔍 Determining deployment type and prerequisites..."

          if [[ "${{ github.event_name }}" == "workflow_run" ]]; then
            echo "deployment_type=automatic" >> $GITHUB_OUTPUT
            echo "🤖 Automatic deployment triggered by CI pipeline"
            echo "Environment: ${{ env.EFFECTIVE_ENVIRONMENT }}"
            echo "Triggered by: Terraform Module Tests completion"

            # Check for infrastructure changes first
            if [[ "${{ steps.check_infra_changes.outputs.any_changed }}" == "true" ]]; then
              echo "📁 Infrastructure changes detected:"
              echo "${{ steps.check_infra_changes.outputs.all_changed_files }}"
              # Check test results for automatic deployment
              if [[ "${{ github.event.workflow_run.conclusion }}" == "success" ]]; then
                echo "✅ Terraform Module Tests passed successfully"
                echo "should_deploy=true" >> $GITHUB_OUTPUT
                echo "deployment_reason=tests_passed_with_changes" >> $GITHUB_OUTPUT
              else
                echo "❌ Terraform Module Tests failed with conclusion: ${{ github.event.workflow_run.conclusion }}"
                echo "should_deploy=false" >> $GITHUB_OUTPUT
                echo "deployment_reason=tests_failed" >> $GITHUB_OUTPUT
              fi
            else
              echo "⏭️ No infrastructure changes detected - skipping deployment"
              echo "should_deploy=false" >> $GITHUB_OUTPUT
              echo "deployment_reason=no_infrastructure_changes" >> $GITHUB_OUTPUT
            fi
          else
            echo "deployment_type=manual" >> $GITHUB_OUTPUT
            echo "🔧 Manual deployment triggered"
            echo "Environment: ${{ env.EFFECTIVE_ENVIRONMENT }}"
            echo "Triggered by: ${{ github.actor }}"
            echo "Skip tests check: ${{ github.event.inputs.skip_tests_check }}"

            # Manual deployment logic (always deploy for manual triggers)
            if [[ "${{ github.event.inputs.skip_tests_check }}" == "true" ]]; then
              echo "⚠️ Skipping tests verification (manual override)"
              echo "should_deploy=true" >> $GITHUB_OUTPUT
              echo "deployment_reason=manual_override" >> $GITHUB_OUTPUT
            else
              echo "✅ Manual deployment approved (tests verification not required)"
              echo "should_deploy=true" >> $GITHUB_OUTPUT
              echo "deployment_reason=manual_approved" >> $GITHUB_OUTPUT
            fi
          fi

          echo "📊 Deployment Decision Summary:"
          echo "  Type: $(cat $GITHUB_OUTPUT | grep deployment_type | cut -d'=' -f2)"
          echo "  Should Deploy: $(cat $GITHUB_OUTPUT | grep should_deploy | cut -d'=' -f2)"
          echo "  Reason: $(cat $GITHUB_OUTPUT | grep deployment_reason | cut -d'=' -f2)"

  terraform-plan:
    name: "Terraform Plan (${{ needs.check-prerequisites.outputs.deployment_type }} - ${{ github.event.inputs.environment || 'staging' }})"
    runs-on: ubuntu-latest
    needs: [check-prerequisites]
    if: needs.check-prerequisites.outputs.should_deploy == 'true'

    defaults:
      run:
        shell: bash

    steps:
      - name: Display Deployment Info
        run: |
          echo "🚀 Infrastructure Deployment"
          echo "================================"
          echo "Type: ${{ needs.check-prerequisites.outputs.deployment_type }}"
          echo "Environment: ${{ env.EFFECTIVE_ENVIRONMENT }}"
          echo "Reason: ${{ needs.check-prerequisites.outputs.deployment_reason }}"
          echo "Triggered by: ${{ github.actor }}"
          echo "================================"

      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Debug Environment
        run: |
          echo "EFFECTIVE_ENVIRONMENT: ${{ env.EFFECTIVE_ENVIRONMENT }}"
          echo "GCP_PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}"
          if [[ -n "${{ secrets.GCP_TF_STATE_BUCKET }}" ]]; then
            echo "✅ GCP_TF_STATE_BUCKET secret is configured"
          else
            echo "❌ GCP_TF_STATE_BUCKET secret is not set"
          fi

      - name: Authenticate to Google Cloud
        if: ${{ env.WORKLOAD_IDENTITY_PROVIDER != '' }}
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: ${{ env.WORKLOAD_IDENTITY_PROVIDER }}
          service_account: ${{ secrets.GCP_SA_EMAIL }}
          create_credentials_file: true
          export_environment_variables: true
          cleanup_credentials: true

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Get GCP Project Number
        run: |
          PROJECT_NUMBER=$(gcloud projects describe ${{ secrets.GCP_PROJECT_ID }} --format="value(projectNumber)")
          echo "PROJECT_NUMBER=$PROJECT_NUMBER" >> $GITHUB_ENV

      - name: Setup Terraform Environment
        id: setup
        uses: ./.github/actions/setup-terraform-environment
        with:
          environment: ${{ env.EFFECTIVE_ENVIRONMENT }}
          project_id: ${{ secrets.GCP_PROJECT_ID }}
          project_number: ${{ env.PROJECT_NUMBER }}

      - name: Initialize Terraform
        uses: ./.github/actions/terraform-init
        with:
          terraform_dir: ${{ steps.setup.outputs.terraform_dir }}
          environment: ${{ env.EFFECTIVE_ENVIRONMENT }}
          backend_type: local

      - name: Terraform Validate
        run: |
          cd ${{ steps.setup.outputs.terraform_dir }}
          terraform validate

      - name: Terraform Plan
        run: |
          cd ${{ steps.setup.outputs.terraform_dir }}
          echo "Creating Terraform plan for ${{ env.EFFECTIVE_ENVIRONMENT }} environment..."

          # Set default values for resource creation flags if not provided
          export TF_VAR_create_network_resources="${TF_VAR_create_network_resources:-false}"
          export TF_VAR_create_database_resources="${TF_VAR_create_database_resources:-false}"
          export TF_VAR_create_redis_resources="${TF_VAR_create_redis_resources:-false}"
          export TF_VAR_create_storage_resources="${TF_VAR_create_storage_resources:-false}"
          export TF_VAR_create_dns_records="${TF_VAR_create_dns_records:-false}"
          export TF_VAR_create_page_rules="${TF_VAR_create_page_rules:-false}"

          echo "🔧 Resource Creation Configuration:"
          echo "  Network Resources: $TF_VAR_create_network_resources"
          echo "  Database Resources: $TF_VAR_create_database_resources"
          echo "  Redis Resources: $TF_VAR_create_redis_resources"
          echo "  Storage Resources: $TF_VAR_create_storage_resources"
          echo "  DNS Records: $TF_VAR_create_dns_records"
          echo "  Page Rules: $TF_VAR_create_page_rules"

          terraform plan -parallelism=1 \
            -var="project_id=${{ secrets.GCP_PROJECT_ID }}" \
            -var="project_number=${{ env.PROJECT_NUMBER }}" \
            -var-file="terraform.tfvars" \
            -input=false \
            -out=tfplan.${{ env.EFFECTIVE_ENVIRONMENT }}

      - name: Upload Terraform Plan
        uses: actions/upload-artifact@v4
        with:
          name: terraform-plan-${{ env.EFFECTIVE_ENVIRONMENT }}-${{ needs.check-prerequisites.outputs.deployment_type }}
          path: ./${{ steps.setup.outputs.terraform_dir }}/tfplan.${{ env.EFFECTIVE_ENVIRONMENT }}
          retention-days: 3

  terraform-apply:
    name: "Terraform Apply (${{ needs.check-prerequisites.outputs.deployment_type }} - ${{ github.event.inputs.environment || 'staging' }})"
    needs: [terraform-plan, check-prerequisites]
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'staging' }}
    if: needs.terraform-plan.result == 'success'

    defaults:
      run:
        shell: bash

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Authenticate to Google Cloud
        if: ${{ env.WORKLOAD_IDENTITY_PROVIDER != '' }}
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: ${{ env.WORKLOAD_IDENTITY_PROVIDER }}
          service_account: ${{ secrets.GCP_SA_EMAIL }}
          create_credentials_file: true
          export_environment_variables: true
          cleanup_credentials: true

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Get GCP Project Number
        run: |
          PROJECT_NUMBER=$(gcloud projects describe ${{ secrets.GCP_PROJECT_ID }} --format="value(projectNumber)")
          echo "PROJECT_NUMBER=$PROJECT_NUMBER" >> $GITHUB_ENV

      - name: Setup Terraform Environment
        id: setup
        uses: ./.github/actions/setup-terraform-environment
        with:
          environment: ${{ env.EFFECTIVE_ENVIRONMENT }}
          project_id: ${{ secrets.GCP_PROJECT_ID }}
          project_number: ${{ env.PROJECT_NUMBER }}

      - name: Debug State Bucket Configuration
        run: |
          echo "🔍 Checking GCS state bucket configuration..."
          echo "Environment: ${{ env.EFFECTIVE_ENVIRONMENT }}"
          if [[ -z "${{ secrets.GCP_TF_STATE_BUCKET }}" ]]; then
            echo "❌ Missing GCS state bucket configuration."
            echo "Please configure this secret in GitHub repository settings:"
            echo "  Repository → Settings → Secrets and variables → Actions → Repository secrets"
            echo "  Name: GCP_TF_STATE_BUCKET"
            echo "  Value: vedavivi-tf-state-a2a-staging (or appropriate bucket for your environment)"
            exit 1
          fi
          echo "✅ State bucket configured for Terraform: ${{ secrets.GCP_TF_STATE_BUCKET }}"

      - name: Initialize Terraform
        uses: ./.github/actions/terraform-init
        with:
          terraform_dir: ${{ steps.setup.outputs.terraform_dir }}
          environment: ${{ env.EFFECTIVE_ENVIRONMENT }}
          backend_type: gcs
          state_bucket: ${{ secrets.GCP_TF_STATE_BUCKET }}

      - name: Download Terraform Plan
        uses: actions/download-artifact@v4
        with:
          name: terraform-plan-${{ env.EFFECTIVE_ENVIRONMENT }}-${{ needs.check-prerequisites.outputs.deployment_type }}
          path: ./${{ steps.setup.outputs.terraform_dir }}

      - name: Terraform Apply
        run: |
          cd ${{ steps.setup.outputs.terraform_dir }}
          echo "Applying Terraform plan for ${{ env.EFFECTIVE_ENVIRONMENT }} environment..."

          # Set default values for resource creation flags if not provided
          export TF_VAR_create_network_resources="${TF_VAR_create_network_resources:-false}"
          export TF_VAR_create_database_resources="${TF_VAR_create_database_resources:-false}"
          export TF_VAR_create_redis_resources="${TF_VAR_create_redis_resources:-false}"
          export TF_VAR_create_storage_resources="${TF_VAR_create_storage_resources:-false}"
          export TF_VAR_create_dns_records="${TF_VAR_create_dns_records:-false}"
          export TF_VAR_create_page_rules="${TF_VAR_create_page_rules:-false}"

          terraform apply -parallelism=1 -auto-approve tfplan.${{ env.EFFECTIVE_ENVIRONMENT }}

      - name: Output Infrastructure Details
        id: output
        run: |
          cd ${{ steps.setup.outputs.terraform_dir }}
          echo "Extracting infrastructure details from Terraform outputs..."

          echo "✅ ${{ env.EFFECTIVE_ENVIRONMENT }} deployment completed successfully!"
          echo ""
          echo "🌐 CDN & Frontend URLs:"
          if terraform output web_url >/dev/null 2>&1; then
            echo "  Web URL: $(terraform output -raw web_url)"
            echo "  API URL: $(terraform output -raw api_url)"
            echo "  WebSocket URL: $(terraform output -raw websocket_url)"
          else
            echo "  CDN outputs not found"
          fi

          echo ""
          echo "🗄️ Database & Cache:"
          if terraform output database_instance_name >/dev/null 2>&1; then
            echo "  Database Instance: $(terraform output -raw database_instance_name)"
            echo "  Redis Instance: $(terraform output -raw redis_instance_name)"
            echo "  Network: $(terraform output -raw network_name)"
          else
            echo "  Core infrastructure outputs not found"
          fi
