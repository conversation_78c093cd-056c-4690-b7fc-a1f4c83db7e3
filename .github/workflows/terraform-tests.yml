name: Terraform Module Tests

on:
  push:
    branches:
      - main
    paths:
      - 'terraform/**'
      - '.github/workflows/terraform-**.yml'
      - 'scripts/setup-backend.sh'
      - 'scripts/run_tests.sh'
  pull_request:
    branches:
      - main
    paths:
      - 'terraform/**'
      - '.github/workflows/terraform-**.yml'
      - 'scripts/setup-backend.sh'
      - 'scripts/run_tests.sh'
  workflow_dispatch:
    inputs:
      force_run:
        description: 'Force run tests regardless of changes'
        type: boolean
        default: false

env:
  TF_VERSION: "1.12.0"

permissions:
  contents: read
  pull-requests: read # Add permission to read pull requests
  id-token: write

jobs:
  # Since we're using path-based triggers, we can simplify change detection
  determine_changes:
    name: "Determine Test Execution"
    runs-on: ubuntu-latest
    outputs:
      terraform_changed: ${{ (github.event_name == 'workflow_dispatch' && inputs.force_run) || github.event_name == 'pull_request' || github.event_name == 'push' }}

    steps:
      - name: Summarize execution reason
        run: |
          TERRAFORM_CHANGED="${{ (github.event_name == 'workflow_dispatch' && inputs.force_run) || github.event_name == 'pull_request' || github.event_name == 'push' }}"

          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            if [[ "${{ inputs.force_run }}" == "true" ]]; then
              echo "🔧 Manual execution with force_run=true - tests will run"
              echo "terraform_changed=true"
            else
              echo "🔧 Manual execution with force_run=false - tests will be skipped"
              echo "terraform_changed=false"
            fi
          elif [[ "${{ github.event_name }}" == "pull_request" ]]; then
            echo "🔍 Pull request validation - tests will run automatically"
            echo "Event: ${{ github.event_name }}"
            echo "PR: #${{ github.event.number }}"
            echo "Branch: ${{ github.head_ref }}"
            echo "terraform_changed=true"
          elif [[ "${{ github.event_name }}" == "push" ]]; then
            echo "📁 Push to main branch - tests will run automatically"
            echo "Event: ${{ github.event_name }}"
            echo "Branch: ${{ github.ref_name }}"
            echo "terraform_changed=true"
          fi

          echo "Final terraform_changed value: $TERRAFORM_CHANGED"

  terraform-lint:
    name: "Terraform Lint"
    needs: determine_changes
    # Run if terraform files changed or workflow was manually triggered with force_run=true
    if: needs.determine_changes.outputs.terraform_changed == 'true'
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          # For pull requests, explicitly checkout the head of the PR
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Terraform Format
        run: terraform fmt -check -recursive -diff
        working-directory: terraform

      - name: Terraform Init
        run: terraform init -backend=false
        working-directory: terraform

      - name: Create and select test workspace
        run: |
          # Create a test workspace to avoid validation errors with "default" workspace
          terraform workspace new dev 2>/dev/null || terraform workspace select dev
        working-directory: terraform

      - name: Terraform Validate
        run: terraform validate -no-color
        working-directory: terraform

  terraform-security:
    name: "Terraform Security Check"
    needs: [determine_changes]
    # Run if terraform files changed or workflow was manually triggered with force_run=true
    if: needs.determine_changes.outputs.terraform_changed == 'true'
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          # For pull requests, explicitly checkout the head of the PR
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Run Trivy vulnerability scanner for Terraform
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'config'
          scan-ref: 'terraform'
          format: 'table'
          exit-code: '1'
          ignore-unfixed: false
          severity: 'CRITICAL,HIGH'

  terraform-mock-tests:
    name: "Terraform Mock Tests"
    needs: [determine_changes, terraform-lint, terraform-security]
    # Run if terraform files changed and previous jobs were successful
    if: |
      needs.determine_changes.outputs.terraform_changed == 'true' &&
      needs.terraform-lint.result == 'success' &&
      needs.terraform-security.result == 'success'
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          # For pull requests, explicitly checkout the head of the PR
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Setup Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.21'

      - name: Verify Go files exist
        run: |
          echo "Checking for Go module files..."
          ls -la terraform/test/

          # Track missing files
          missing_files=0

          if [ -f "terraform/test/go.sum" ]; then
            echo "✅ go.sum found"
          else
            echo "❌ go.sum not found"
            missing_files=$((missing_files + 1))
          fi

          if [ -f "terraform/test/go.mod" ]; then
            echo "✅ go.mod found"
          else
            echo "❌ go.mod not found"
            missing_files=$((missing_files + 1))
          fi

          # Fail the step if any required files are missing
          if [ $missing_files -gt 0 ]; then
            echo "❌ Error: $missing_files required Go module file(s) missing"
            echo "Please ensure go.mod and go.sum exist in terraform/test/ directory"
            echo "Run 'go mod init' and 'go mod tidy' in terraform/test/ to create them"
            exit 1
          fi

          echo "✅ All required Go module files found"

      - name: Cache Go modules
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('terraform/test/go.sum', 'terraform/test/go.mod') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Download dependencies
        run: |
          cd terraform/test
          go mod tidy

      - name: Set execute permissions for scripts
        run: |
          chmod +x terraform/scripts/run_tests.sh
          chmod +x terraform/scripts/setup-backend.sh

      - name: Run Mock Tests
        run: |
          echo "Running mock tests (safe mode, no real infrastructure creation)"
          ./terraform/scripts/run_tests.sh --mock

  terraform-unit-tests:
    name: "Terraform Unit Tests"
    needs: [determine_changes, terraform-mock-tests]
    # Run comprehensive tests on PRs, main branch merges, or manual dispatch
    if: |
      needs.determine_changes.outputs.terraform_changed == 'true' &&
      needs.terraform-mock-tests.result == 'success' &&
      (github.event_name == 'workflow_dispatch' ||
       github.event_name == 'pull_request' ||
       (github.event_name == 'push' && github.ref == 'refs/heads/main'))
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          # For pull requests, explicitly checkout the head of the PR
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Setup Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.21'

      - name: Auth to GCP
        id: auth
        uses: google-github-actions/auth@v1
        with:
          # Workload Identity Federation configuration
          workload_identity_provider: ${{ secrets.WORKLOAD_IDENTITY_PROVIDER }}
          service_account: ${{ secrets.GCP_SA_EMAIL }}
          create_credentials_file: true
          export_environment_variables: true
          cleanup_credentials: true
          access_token_lifetime: 3600s
          access_token_scopes: https://www.googleapis.com/auth/cloud-platform
          retries: 3
          backoff: 250
          id_token_include_email: false
        env:
          GOOGLE_CLOUD_PROJECT: ${{ secrets.GCP_PROJECT_ID }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Debug Authentication
        run: |
          echo "Project ID: ${{ secrets.GCP_PROJECT_ID }}"
          echo "Service Account: ${{ secrets.GCP_SA_EMAIL }}"
          echo "GOOGLE_CLOUD_PROJECT: $GOOGLE_CLOUD_PROJECT"
          echo "GOOGLE_APPLICATION_CREDENTIALS: $GOOGLE_APPLICATION_CREDENTIALS"
          if [ -f "$GOOGLE_APPLICATION_CREDENTIALS" ]; then
            echo "Credentials file exists"
            jq -r '.project_id' < "$GOOGLE_APPLICATION_CREDENTIALS"
          else
            echo "Credentials file not found"
          fi

      - name: Terraform Init
        run: |
          # Use local state for tests instead of GCS to avoid authentication issues
          terraform init -backend=false
        working-directory: terraform
        env:
          GOOGLE_CLOUD_PROJECT: ${{ secrets.GCP_PROJECT_ID }}

      - name: Create and select test workspace
        run: |
          # Create a test workspace to avoid validation errors with "default" workspace
          terraform workspace new dev 2>/dev/null || terraform workspace select dev
        working-directory: terraform

      - name: Set Terraform Path
        run: |
          echo "TERRATEST_TFPATH=$(which terraform)" >> $GITHUB_ENV

      - name: Verify Go files exist
        run: |
          echo "Checking for Go module files..."
          ls -la terraform/test/

          # Track missing files
          missing_files=0

          if [ -f "terraform/test/go.sum" ]; then
            echo "✅ go.sum found"
          else
            echo "❌ go.sum not found"
            missing_files=$((missing_files + 1))
          fi

          if [ -f "terraform/test/go.mod" ]; then
            echo "✅ go.mod found"
          else
            echo "❌ go.mod not found"
            missing_files=$((missing_files + 1))
          fi

          # Fail the step if any required files are missing
          if [ $missing_files -gt 0 ]; then
            echo "❌ Error: $missing_files required Go module file(s) missing"
            echo "Please ensure go.mod and go.sum exist in terraform/test/ directory"
            echo "Run 'go mod init' and 'go mod tidy' in terraform/test/ to create them"
            exit 1
          fi

          echo "✅ All required Go module files found"

      - name: Cache Go modules
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('terraform/test/go.sum', 'terraform/test/go.mod') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Download dependencies
        run: |
          cd terraform/test
          go mod tidy

      - name: Run Syntax Tests
        run: |
          cd terraform/test
          go test -v -run TestMainIntegration -timeout 10m
        env:
          TF_VAR_project_id: ${{ secrets.GCP_PROJECT_ID }}

        # Uncomment these tests if you have a test GCP project that can be used
      # - name: Run Network Tests
      #   run: |
      #     cd terraform/test
      #     go test -v -run TestNetworkModule -timeout 15m
      #   env:
      #     TF_VAR_project_id: ${{ secrets.GCP_PROJECT_ID_DEV }}

      # - name: Run GCS Tests
      #   run: |
      #     cd terraform/test
      #     go test -v -run TestGCSModule -timeout 15m
      #   env:
      #     TF_VAR_project_id: ${{ secrets.GCP_PROJECT_ID_DEV }}
