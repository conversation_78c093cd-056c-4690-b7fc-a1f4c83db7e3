name: CDN Performance and Validation Testing

# This workflow validates CDN performance, domain resolution, and rollback procedures
# Runs on schedule and can be manually triggered for specific environments

on:
  schedule:
    # Run daily at 6 AM UTC for continuous monitoring
    - cron: '0 6 * * *'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to validate'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      test_type:
        description: 'Type of validation to run'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - performance
          - domain
          - rollback
      baseline_comparison:
        description: 'Include baseline performance comparison'
        required: false
        default: false
        type: boolean

env:
  TERRAFORM_VERSION: '1.12.0'

jobs:
  performance-validation:
    name: 'CDN Performance Validation'
    runs-on: ubuntu-latest
    if: ${{ inputs.test_type == 'all' || inputs.test_type == 'performance' }}
    environment: ${{ inputs.environment || 'staging' }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            curl \
            bc \
            jq \
            dnsutils

      - name: Make scripts executable
        run: |
          chmod +x scripts/*.sh

      - name: Run CDN Performance Validation
        id: performance_test
        run: |
          echo "🚀 Running CDN performance validation..."

          if [[ "${{ inputs.baseline_comparison }}" == "true" ]]; then
            ./scripts/validate-cdn-performance.sh ${{ inputs.environment || 'staging' }} --baseline
          else
            ./scripts/validate-cdn-performance.sh ${{ inputs.environment || 'staging' }}
          fi

      - name: Upload Performance Report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: cdn-performance-report-${{ inputs.environment || 'staging' }}-${{ github.run_number }}
          path: cdn-performance-report.json
          retention-days: 30

  domain-validation:
    name: 'Domain Resolution Validation'
    runs-on: ubuntu-latest
    if: ${{ inputs.test_type == 'all' || inputs.test_type == 'domain' }}
    environment: ${{ inputs.environment || 'staging' }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            curl \
            openssl \
            dnsutils \
            jq

      - name: Make scripts executable
        run: |
          chmod +x scripts/*.sh

      - name: Run Domain Resolution Validation
        id: domain_test
        run: |
          echo "🌐 Running domain resolution validation..."
          ./scripts/validate-domain-resolution.sh ${{ inputs.environment || 'staging' }}

      - name: Upload Domain Validation Report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: domain-validation-report-${{ inputs.environment || 'staging' }}-${{ github.run_number }}
          path: domain-validation-report.json
          retention-days: 30

  rollback-testing:
    name: 'Rollback Procedure Testing'
    runs-on: ubuntu-latest
    if: ${{ inputs.test_type == 'all' || inputs.test_type == 'rollback' }}
    environment: ${{ inputs.environment || 'staging' }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TERRAFORM_VERSION }}

      - name: Setup system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            curl \
            jq \
            dnsutils

      - name: Make scripts executable
        run: |
          chmod +x scripts/*.sh

      - name: Run Rollback Procedure Testing (Dry Run)
        id: rollback_test
        run: |
          echo "🔄 Running rollback procedure testing..."
          ./scripts/test-cdn-rollback.sh ${{ inputs.environment || 'staging' }} --dry-run

      - name: Upload Rollback Test Report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: rollback-test-report-${{ inputs.environment || 'staging' }}-${{ github.run_number }}
          path: rollback-test-report.json
          retention-days: 30

  comprehensive-validation:
    name: 'Comprehensive CDN Validation'
    runs-on: ubuntu-latest
    needs: [performance-validation, domain-validation, rollback-testing]
    if: always() && (inputs.test_type == 'all')
    environment: ${{ inputs.environment || 'staging' }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: validation-reports

      - name: Setup system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y jq

      - name: Generate Comprehensive Report
        id: comprehensive_report
        run: |
          echo "📊 Generating comprehensive validation report..."

          REPORT_FILE="comprehensive-validation-report.json"
          ENVIRONMENT="${{ inputs.environment || 'staging' }}"

          # Initialize report structure
          cat > "$REPORT_FILE" << EOF
          {
            "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "environment": "$ENVIRONMENT",
            "validation_type": "comprehensive",
            "github_run_id": "${{ github.run_id }}",
            "github_run_number": "${{ github.run_number }}",
            "triggered_by": "${{ github.event_name }}",
            "test_results": {
              "performance_validation": {},
              "domain_validation": {},
              "rollback_testing": {}
            },
            "overall_status": "UNKNOWN"
          }
          EOF

          # Merge individual reports if they exist
          OVERALL_STATUS="PASSED"

          # Check performance validation results
          if [[ -f "validation-reports/cdn-performance-report-${ENVIRONMENT}-${{ github.run_number }}/cdn-performance-report.json" ]]; then
            PERF_REPORT=$(cat "validation-reports/cdn-performance-report-${ENVIRONMENT}-${{ github.run_number }}/cdn-performance-report.json")
            echo "$PERF_REPORT" | jq '.test_results.tests_failed' | grep -q '^0$' || OVERALL_STATUS="FAILED"

            # Update comprehensive report
            jq --argjson perf "$PERF_REPORT" '.test_results.performance_validation = $perf' "$REPORT_FILE" > tmp.json && mv tmp.json "$REPORT_FILE"
          fi

          # Check domain validation results
          if [[ -f "validation-reports/domain-validation-report-${ENVIRONMENT}-${{ github.run_number }}/domain-validation-report.json" ]]; then
            DOMAIN_REPORT=$(cat "validation-reports/domain-validation-report-${ENVIRONMENT}-${{ github.run_number }}/domain-validation-report.json")
            echo "$DOMAIN_REPORT" | jq '.test_results.tests_failed' | grep -q '^0$' || OVERALL_STATUS="FAILED"

            # Update comprehensive report
            jq --argjson domain "$DOMAIN_REPORT" '.test_results.domain_validation = $domain' "$REPORT_FILE" > tmp.json && mv tmp.json "$REPORT_FILE"
          fi

          # Check rollback testing results
          if [[ -f "validation-reports/rollback-test-report-${ENVIRONMENT}-${{ github.run_number }}/rollback-test-report.json" ]]; then
            ROLLBACK_REPORT=$(cat "validation-reports/rollback-test-report-${ENVIRONMENT}-${{ github.run_number }}/rollback-test-report.json")
            echo "$ROLLBACK_REPORT" | jq '.test_results.tests_failed' | grep -q '^0$' || OVERALL_STATUS="FAILED"

            # Update comprehensive report
            jq --argjson rollback "$ROLLBACK_REPORT" '.test_results.rollback_testing = $rollback' "$REPORT_FILE" > tmp.json && mv tmp.json "$REPORT_FILE"
          fi

          # Update overall status
          jq --arg status "$OVERALL_STATUS" '.overall_status = $status' "$REPORT_FILE" > tmp.json && mv tmp.json "$REPORT_FILE"

          echo "OVERALL_STATUS=$OVERALL_STATUS" >> $GITHUB_OUTPUT
          echo "Comprehensive report generated: $REPORT_FILE"

      - name: Create Validation Summary
        id: validation_summary
        run: |
          echo "📋 Creating validation summary..."

          SUMMARY_FILE="validation-summary.md"
          ENVIRONMENT="${{ inputs.environment || 'staging' }}"
          OVERALL_STATUS="${{ steps.comprehensive_report.outputs.OVERALL_STATUS }}"

          cat > "$SUMMARY_FILE" << EOF
          # CDN Validation Summary

          **Environment:** $ENVIRONMENT
          **Date:** $(date -u '+%Y-%m-%d %H:%M:%S UTC')
          **Trigger:** ${{ github.event_name }}
          **Overall Status:** $(if [[ "$OVERALL_STATUS" == "PASSED" ]]; then echo "✅ PASSED"; else echo "❌ FAILED"; fi)

          ## Test Results

          | Validation Type | Status | Details |
          |----------------|--------|---------|
          | Performance Validation | ${{ needs.performance-validation.result == 'success' && '✅ PASSED' || '❌ FAILED' }} | Load time, TTFB, cache performance |
          | Domain Resolution | ${{ needs.domain-validation.result == 'success' && '✅ PASSED' || '❌ FAILED' }} | DNS, SSL certificates, domain patterns |
          | Rollback Testing | ${{ needs.rollback-testing.result == 'success' && '✅ PASSED' || '❌ FAILED' }} | Rollback procedures and validation |

          ## Next Steps

          EOF

          if [[ "$OVERALL_STATUS" == "PASSED" ]]; then
            cat >> "$SUMMARY_FILE" << EOF
          ✅ **All validations passed successfully!**

          - CDN performance meets targets
          - Domain resolution is working correctly
          - Rollback procedures are validated
          - Ready for production deployment
          EOF
          else
            cat >> "$SUMMARY_FILE" << EOF
          ❌ **Some validations failed.**

          - Review failed test results in artifacts
          - Fix identified issues
          - Re-run validation after fixes
          - Do not proceed with deployment until all tests pass
          EOF
          fi

          # Output summary for GitHub
          echo "VALIDATION_SUMMARY<<EOF" >> $GITHUB_OUTPUT
          cat "$SUMMARY_FILE" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Upload Comprehensive Report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: comprehensive-validation-report-${{ inputs.environment || 'staging' }}-${{ github.run_number }}
          path: |
            comprehensive-validation-report.json
            validation-summary.md
          retention-days: 90

      - name: Set Job Summary
        if: always()
        run: |
          echo "${{ steps.validation_summary.outputs.VALIDATION_SUMMARY }}" >> $GITHUB_STEP_SUMMARY

      - name: Fail job if validations failed
        if: steps.comprehensive_report.outputs.OVERALL_STATUS == 'FAILED'
        run: |
          echo "❌ CDN validation failed. Check the reports for details."
          exit 1
