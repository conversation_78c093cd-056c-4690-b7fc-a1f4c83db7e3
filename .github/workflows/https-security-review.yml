name: HTTPS Security Review

on:
  schedule:
    # Run daily at 2 AM UTC for continuous monitoring
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to run security review against'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - production
      api_domain:
        description: 'API domain to test (e.g., api.a2a-platform.org)'
        required: true
        default: 'localhost:8000'
        type: string
      frontend_domain:
        description: 'Frontend domain to test (e.g., a2a-platform.org)'
        required: true
        default: 'localhost:5173'
        type: string

jobs:
  security-review:
    name: Run HTTPS Security Review
    runs-on: ubuntu-latest

    env:
      # Default values for scheduled runs
      ENVIRONMENT: ${{ inputs.environment || 'staging' }}
      API_DOMAIN: ${{ inputs.api_domain || 'api.vedavivi.com' }}
      FRONTEND_DOMAIN: ${{ inputs.frontend_domain || 'vedavivi.com' }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y openssl curl apache2-utils
          npm install -g ws

      - name: Make scripts executable
        run: |
          chmod +x scripts/*.sh

      - name: Run automated validation tests
        run: |
          mkdir -p security-review-results

          # Create report directory
          TIMESTAMP=$(date +"%Y%m%d-%H%M%S")
          REVIEW_DIR="security-review-results/${TIMESTAMP}-https-security-review"
          mkdir -p "${REVIEW_DIR}"

          # Run validation tests
          echo "Running HTTPS implementation validation..."
          scripts/validate-https-implementation.sh ${{ inputs.environment }} ${{ inputs.api_domain }} ${{ inputs.frontend_domain }} > "${REVIEW_DIR}/validate-https-implementation.log" 2>&1 || echo "Validation failed"

          echo "Running security headers check..."
          scripts/check-security-headers.sh ${{ inputs.api_domain }} > "${REVIEW_DIR}/check-security-headers.log" 2>&1 || echo "Headers check failed"

          echo "Running WebSocket security test..."
          WS_TEST_DOMAIN=${{ inputs.api_domain }} node scripts/websocket-ssl-test.js > "${REVIEW_DIR}/websocket-ssl-test.log" 2>&1 || echo "WebSocket test failed"

          if [ "${{ inputs.environment }}" != "dev" ]; then
            echo "Running SSL performance test..."
            scripts/ssl-performance-test.sh ${{ inputs.api_domain }} > "${REVIEW_DIR}/ssl-performance-test.log" 2>&1 || echo "Performance test failed"
          else
            echo "Skipping SSL performance test in dev environment"
          fi

      - name: Generate security review report
        run: |
          TIMESTAMP=$(date +"%Y%m%d-%H%M%S")
          REVIEW_DIR="security-review-results/${TIMESTAMP}-https-security-review"

          # Create report markdown file
          cat > "${REVIEW_DIR}/security-review-report.md" << EOF
          # HTTPS Security Review Report (Automated)

          **Date:** $(date)
          **Environment:** ${{ inputs.environment }}
          **API Domain:** ${{ inputs.api_domain }}
          **Frontend Domain:** ${{ inputs.frontend_domain }}

          ## Automated Tests Results

          This report contains results from automated security tests only. A complete security review
          should also include manual verification steps as outlined in the HTTPS review process.

          For detailed test results, see the log files in this directory.

          ## Next Steps

          1. Review the automated test results
          2. Perform manual verification steps from \`docs/security/https-review-process.md\`
          3. Complete the external security assessments (SSL Labs, Security Headers)
          4. Run the full security review process locally using \`scripts/run-security-review.sh\`

          ## GitHub Actions Run

          - Workflow: ${GITHUB_WORKFLOW}
          - Run ID: ${GITHUB_RUN_ID}
          - Triggered by: ${GITHUB_ACTOR}
          EOF

          echo "Generated security review report: ${REVIEW_DIR}/security-review-report.md"

      - name: Upload review artifacts
        uses: actions/upload-artifact@v3
        with:
          name: security-review-results
          path: security-review-results/
          retention-days: 30
