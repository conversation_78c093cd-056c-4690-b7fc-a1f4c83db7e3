name: Deploy API to GCP Cloud Run

permissions:
  id-token: write
  contents: read

on:
  workflow_run:
    workflows: ["CI Pipeline (Static Analysis & Tests)"]
    types:
      - completed
    branches: [main]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Choose the environment to deploy (staging or production). CI-triggered runs will deploy to staging, then production.'
        required: true
        type: choice
        options:
          - staging
          - production
        default: 'staging'

jobs:
  check-ci-status:
    name: 'Check CI Pipeline Status'
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_run'
    outputs:
      ci_success: ${{ steps.ci_check.outputs.success }}
    steps:
      - name: Check CI Pipeline Result
        id: ci_check
        run: |
          if [[ "${{ github.event.workflow_run.conclusion }}" == "success" ]]; then
            echo "✅ CI Pipeline passed successfully"
            echo "success=true" >> $GITHUB_OUTPUT
          else
            echo "❌ CI Pipeline failed with conclusion: ${{ github.event.workflow_run.conclusion }}"
            echo "success=false" >> $GITHUB_OUTPUT
            exit 1
          fi

  detect-api-changes:
    name: 'Detect API Changes'
    runs-on: ubuntu-latest
    needs: [check-ci-status]
    if: always() && (needs.check-ci-status.outputs.ci_success == 'true' || github.event_name != 'workflow_run')
    outputs:
      api_changed: ${{ steps.changes.outputs.any_changed }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event_name == 'workflow_run' && github.event.workflow_run.head_sha || github.ref }}
          fetch-depth: 0

      - name: Check for API related changes
        id: changes
        uses: tj-actions/changed-files@v46
        with:
          files: |
            apps/backend/src/a2a_platform/api/**
            apps/backend/pyproject.toml
            .github/workflows/deploy-api.yml
            .github/actions/gcp-auth-setup/**
            .github/actions/docker-build-push/**
            .github/actions/gcloud-run-deploy/**

      - name: Display change information
        run: |
          echo "📊 API Change Detection Results"
          echo "==============================="
          echo "API changes detected: ${{ steps.changes.outputs.any_changed }}"
          if [[ "${{ github.event_name }}" == "workflow_run" ]]; then
            echo "Note: Automatic deployment will only proceed if changes detected"
          else
            echo "Note: Manual deployment will proceed regardless of changes"
          fi

  deploy_api_staging:
    name: Build and Deploy API to Staging
    runs-on: ubuntu-latest
    needs: [check-ci-status, detect-api-changes]
    if: always() && ((github.event_name == 'workflow_run' && needs.detect-api-changes.outputs.api_changed == 'true' && needs.check-ci-status.outputs.ci_success == 'true') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging'))
    environment: staging
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event_name == 'workflow_run' && github.event.workflow_run.head_sha || github.ref }}
          fetch-depth: 0

      - name: Set up GCP authentication
        uses: ./.github/actions/gcp-auth-setup
        with:
          project-id: ${{ secrets.GCP_PROJECT_ID }}
          service-account: ${{ secrets.GCP_SERVICE_ACCOUNT }}
          workload-identity-provider: ${{ secrets.WORKLOAD_IDENTITY_PROVIDER }}

      - name: Build and push Docker image
        id: build_push_staging
        uses: ./.github/actions/docker-build-push
        with:
          dockerfile-path: apps/backend/src/a2a_platform/api/Dockerfile
          build-context: apps/backend
          image-name: api
          environment: staging
          artifact-registry-host: us-central1-docker.pkg.dev
          repository: a2a-staging-containers  # Staging specific
          project-id: ${{ secrets.GCP_PROJECT_ID }}
          # build-args-json: | # Example
          #   {
          #     "API_SPECIFIC_BUILD_ARG": "some_value_for_staging"
          #   }

      - name: Prepare Environment Variables JSON for Staging
        id: prepare_env_vars_staging
        shell: bash
        run: |
          # Start with a clean empty object and build it step by step
          env_vars_json=$(echo '{}' | jq '.')

          # Add ENVIRONMENT first
          env_vars_json=$(echo "$env_vars_json" | jq '. + {"ENVIRONMENT": "staging"}')

          # Configure database URLs for staging environment
          if [[ -n "${{ secrets.DATABASE_URL }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.DATABASE_URL }}" '. + {"DATABASE_URL": $v}')
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.DATABASE_URL }}" '. + {"ALEMBIC_DB_URL": $v}')  # For API service
          fi

          if [[ -n "${{ secrets.DATABASE_ASYNC_URL }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.DATABASE_ASYNC_URL }}" '. + {"DATABASE_ASYNC_URL": $v}')
          fi
          if [[ -n "${{ secrets.REDIS_URL }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.REDIS_URL }}" '. + {"REDIS_URL": $v}')
          fi
          if [[ -n "${{ secrets.CLERK_API_KEY }}" ]]; then  # Assuming CLERK_API_KEY is the one needed, adjust if CLERK_SECRET_KEY is used by API
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.CLERK_API_KEY }}" '. + {"CLERK_API_KEY": $v}')
          fi
          if [[ -n "${{ secrets.CLERK_JWT_PUBLIC_KEY }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.CLERK_JWT_PUBLIC_KEY }}" '. + {"CLERK_JWT_PUBLIC_KEY": $v}')
          fi
          if [[ -n "${{ secrets.CLERK_WEBHOOK_SECRET }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.CLERK_WEBHOOK_SECRET }}" '. + {"CLERK_WEBHOOK_SECRET": $v}')
          fi

          # Add CORS origins for staging environment
          if [[ -n "${{ vars.CORS_ORIGINS }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ vars.CORS_ORIGINS }}" '. + {"CORS_ORIGINS": $v}')
          else
            # Fallback to default staging origins if not configured
            env_vars_json=$(echo "$env_vars_json" | jq '. + {"CORS_ORIGINS": "https://www-staging.vedavivi.app,https://staging.vedavivi.app"}')
          fi

          # Add trusted hosts for staging environment
          if [[ -n "${{ vars.TRUSTED_HOSTS }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ vars.TRUSTED_HOSTS }}" '. + {"TRUSTED_HOSTS": $v}')
          else
            # Fallback to default staging hosts if not configured
            env_vars_json=$(echo "$env_vars_json" | jq '. + {"TRUSTED_HOSTS": "api-staging.vedavivi.app,www-staging.vedavivi.app,staging.vedavivi.app"}')
          fi

          if [[ -n "${{ secrets.STORAGE_BUCKET }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.STORAGE_BUCKET }}" '. + {"STORAGE_BUCKET": $v}')
          fi
          if [[ -n "${{ secrets.CDN_URL }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.CDN_URL }}" '. + {"CDN_URL": $v}')
          fi
          if [[ -n "${{ secrets.DB_INSTANCE_NAME }}" ]]; then  # For API service
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.DB_INSTANCE_NAME }}" '. + {"DB_INSTANCE_NAME": $v}')
          fi
          # Add PUBSUB_PROJECT_ID if defined and needed by API
          if [[ -n "${{ secrets.PUBSUB_PROJECT_ID }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.PUBSUB_PROJECT_ID }}" '. + {"PUBSUB_PROJECT_ID": $v}')
          fi

          # Add TRUSTED_HOSTS for staging to allow both Cloud Run and custom domain
          env_vars_json=$(echo "$env_vars_json" | jq '. + {"TRUSTED_HOSTS": "api-staging.vedavivi.app,*.run.app"}')

          # Validate the final JSON
          echo "Debug: Final env_vars_json:"
          echo "$env_vars_json" | jq .
          if ! echo "$env_vars_json" | jq empty; then
            echo "❌ Error: Invalid JSON generated for environment variables"
            exit 1
          fi
          echo "✅ JSON validation passed"

          echo "env_vars_json<<EOF" >> $GITHUB_OUTPUT
          echo "$env_vars_json" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Deploy API to Staging Cloud Run
        uses: ./.github/actions/gcloud-run-deploy
        with:
          service-name: api
          environment: staging
          region: us-central1
          image: ${{ steps.build_push_staging.outputs.image-url }}  # Use output from build step
          vpc-connector: projects/${{ secrets.GCP_PROJECT_ID }}/locations/us-central1/connectors/a2a-staging-vpc-connector  # Staging specific
          allow-unauthenticated: true  # Enable for staging to allow Cloudflare access
          ingress: all  # Allow all traffic for CDN routing (fixed HTTPS middleware for Cloudflare)
          port: 8080
          startup-probe: /api/health
          min-instances: '0'
          max-instances: 'default'
          cloudsql-instances: ${{ secrets.CLOUDSQL_INSTANCE }}  # Enable Cloud SQL connection
          cpu: '1'
          memory: '512Mi'
          concurrency: '80'
          timeout: '300'
          env_vars_json: ${{ steps.prepare_env_vars_staging.outputs.env_vars_json }}
          run-migrations: true  # API specific
          alembic-db-url: ${{ secrets.MIGRATION_DATABASE_URL || secrets.DATABASE_URL }}  # Use migration-specific URL if available, fallback to regular DATABASE_URL
          db-instance-name: ${{ secrets.DB_INSTANCE_NAME }}

  deploy_api_production:
    name: Deploy API to Production
    runs-on: ubuntu-latest
    needs: [check-ci-status, detect-api-changes, deploy_api_staging]
    if: github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production' && needs.deploy_api_staging.result == 'success'
    environment: production  # Requires manual approval for production deployment
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}  # For workflow_dispatch
          fetch-depth: 0

      - name: Set up GCP authentication
        uses: ./.github/actions/gcp-auth-setup
        with:
          project-id: ${{ secrets.GCP_PROJECT_ID }}
          service-account: ${{ secrets.GCP_SERVICE_ACCOUNT }}
          workload-identity-provider: ${{ secrets.WORKLOAD_IDENTITY_PROVIDER }}

      - name: Build and push Docker image for Production
        id: build_push_production
        uses: ./.github/actions/docker-build-push
        with:
          dockerfile-path: apps/backend/src/a2a_platform/api/Dockerfile
          build-context: apps/backend
          image-name: api
          environment: production
          artifact-registry-host: us-central1-docker.pkg.dev
          repository: a2a-production-containers  # Production specific
          project-id: ${{ secrets.GCP_PROJECT_ID }}
          # build-args-json: | # Example
          #   {
          #     "API_SPECIFIC_BUILD_ARG": "some_value_for_production"
          #   }

      - name: Prepare Environment Variables JSON for Production
        id: prepare_env_vars_production
        shell: bash
        run: |
          # Start with a clean empty object and build it step by step
          env_vars_json=$(echo '{}' | jq '.')

          # Add ENVIRONMENT first
          env_vars_json=$(echo "$env_vars_json" | jq '. + {"ENVIRONMENT": "production"}')

          # Configure database URLs for production environment
          if [[ -n "${{ secrets.DATABASE_URL }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.DATABASE_URL }}" '. + {"DATABASE_URL": $v}')
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.DATABASE_URL }}" '. + {"ALEMBIC_DB_URL": $v}')  # For API service
          fi

          if [[ -n "${{ secrets.DATABASE_ASYNC_URL }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.DATABASE_ASYNC_URL }}" '. + {"DATABASE_ASYNC_URL": $v}')
          fi
          if [[ -n "${{ secrets.REDIS_URL }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.REDIS_URL }}" '. + {"REDIS_URL": $v}')
          fi
          if [[ -n "${{ secrets.CLERK_API_KEY }}" ]]; then  # Assuming CLERK_API_KEY is the one needed
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.CLERK_API_KEY }}" '. + {"CLERK_API_KEY": $v}')
          fi
          if [[ -n "${{ secrets.CLERK_JWT_PUBLIC_KEY }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.CLERK_JWT_PUBLIC_KEY }}" '. + {"CLERK_JWT_PUBLIC_KEY": $v}')
          fi
          if [[ -n "${{ secrets.CLERK_WEBHOOK_SECRET }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.CLERK_WEBHOOK_SECRET }}" '. + {"CLERK_WEBHOOK_SECRET": $v}')
          fi
          if [[ -n "${{ secrets.STORAGE_BUCKET }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.STORAGE_BUCKET }}" '. + {"STORAGE_BUCKET": $v}')
          fi
          if [[ -n "${{ secrets.CDN_URL }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.CDN_URL }}" '. + {"CDN_URL": $v}')
          fi
          if [[ -n "${{ secrets.DB_INSTANCE_NAME }}" ]]; then  # For API service
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.DB_INSTANCE_NAME }}" '. + {"DB_INSTANCE_NAME": $v}')
          fi
          # Add PUBSUB_PROJECT_ID if defined and needed by API
          if [[ -n "${{ secrets.PUBSUB_PROJECT_ID }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.PUBSUB_PROJECT_ID }}" '. + {"PUBSUB_PROJECT_ID": $v}')
          fi

          # Add CORS origins for production environment
          if [[ -n "${{ vars.CORS_ORIGINS }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ vars.CORS_ORIGINS }}" '. + {"CORS_ORIGINS": $v}')
          else
            # Fallback to default production origins if not configured
            env_vars_json=$(echo "$env_vars_json" | jq '. + {"CORS_ORIGINS": "https://vedavivi.app,https://www.vedavivi.app"}')
          fi

          # Add trusted hosts for production environment
          if [[ -n "${{ vars.TRUSTED_HOSTS }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ vars.TRUSTED_HOSTS }}" '. + {"TRUSTED_HOSTS": $v}')
          else
            # Fallback to default production hosts if not configured
            env_vars_json=$(echo "$env_vars_json" | jq '. + {"TRUSTED_HOSTS": "api.vedavivi.app,vedavivi.app,www.vedavivi.app"}')
          fi


          # Add TRUSTED_HOSTS for production to allow both Cloud Run and custom domain
          env_vars_json=$(echo "$env_vars_json" | jq '. + {"TRUSTED_HOSTS": "api.vedavivi.app,*.run.app"}')

          # Validate the final JSON
          echo "Debug: Final env_vars_json:"
          echo "$env_vars_json" | jq .
          if ! echo "$env_vars_json" | jq empty; then
            echo "❌ Error: Invalid JSON generated for environment variables"
            exit 1
          fi
          echo "✅ JSON validation passed"

          echo "env_vars_json<<EOF" >> $GITHUB_OUTPUT
          echo "$env_vars_json" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Deploy API to Production Cloud Run
        uses: ./.github/actions/gcloud-run-deploy
        with:
          service-name: api
          environment: production
          region: us-central1
          image: ${{ steps.build_push_production.outputs.image-url }}  # Use output from build step
          vpc-connector: projects/${{ secrets.GCP_PROJECT_ID }}/locations/us-central1/connectors/a2a-production-vpc-connector  # Production specific
          allow-unauthenticated: true
          ingress: internal-and-cloud-load-balancing  # More restrictive for production
          port: 8080
          startup-probe: /api/health
          min-instances: '0'  # Consider adjusting for production
          max-instances: 'default'
          cloudsql-instances: ${{ secrets.CLOUDSQL_INSTANCE }}  # Enable Cloud SQL connection
          cpu: '1'
          memory: '512Mi'
          concurrency: '80'
          timeout: '300'
          env_vars_json: ${{ steps.prepare_env_vars_production.outputs.env_vars_json }}
          run-migrations: true  # API specific
          alembic-db-url: ${{ secrets.MIGRATION_DATABASE_URL || secrets.DATABASE_URL }}  # Use migration-specific URL if available, fallback to regular DATABASE_URL
          db-instance-name: ${{ secrets.DB_INSTANCE_NAME }}
