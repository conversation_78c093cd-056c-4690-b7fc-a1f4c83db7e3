name: Deploy Workers to GCP Cloud Run

permissions:
  id-token: write
  contents: read

on:
  workflow_run:
    workflows: ["CI Pipeline (Static Analysis & Tests)"]
    types:
      - completed
    branches: [main]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Choose the environment to deploy (staging or production). CI-triggered runs will deploy to staging, then production.'
        required: true
        type: choice
        options:
          - staging
          - production
        default: 'staging'

jobs:
  check-ci-status:
    name: 'Check CI Pipeline Status'
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_run'
    outputs:
      ci_success: ${{ steps.ci_check.outputs.success }}
    steps:
      - name: Check CI Pipeline Result
        id: ci_check
        run: |
          if [[ "${{ github.event.workflow_run.conclusion }}" == "success" ]]; then
            echo "✅ CI Pipeline passed successfully"
            echo "success=true" >> $GITHUB_OUTPUT
          else
            echo "❌ CI Pipeline failed with conclusion: ${{ github.event.workflow_run.conclusion }}"
            echo "success=false" >> $GITHUB_OUTPUT
            exit 1
          fi

  detect-workers-changes:
    name: 'Detect Workers Changes'
    runs-on: ubuntu-latest
    needs: [check-ci-status]
    if: always() && (needs.check-ci-status.outputs.ci_success == 'true' || github.event_name != 'workflow_run')
    outputs:
      workers_changed: ${{ steps.changes.outputs.any_changed }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event_name == 'workflow_run' && github.event.workflow_run.head_sha || github.ref }}
          fetch-depth: 0

      - name: Check for Workers related changes
        id: changes
        uses: tj-actions/changed-files@v46
        with:
          files: |
            apps/backend/src/a2a_platform/workers/**
            apps/backend/pyproject.toml
            .github/workflows/deploy-workers.yml
            .github/actions/gcp-auth-setup/**
            .github/actions/docker-build-push/**
            .github/actions/gcloud-run-deploy/**

      - name: Display change information
        run: |
          echo "📊 Workers Change Detection Results"
          echo "==================================="
          echo "Workers changes detected: ${{ steps.changes.outputs.any_changed }}"
          if [[ "${{ github.event_name }}" == "workflow_run" ]]; then
            echo "Note: Automatic deployment will only proceed if changes detected"
          else
            echo "Note: Manual deployment will proceed regardless of changes"
          fi

  deploy_workers_staging:
    name: Build and Deploy Workers to Staging
    runs-on: ubuntu-latest
    needs: [check-ci-status, detect-workers-changes]
    if: always() && ((github.event_name == 'workflow_run' && needs.detect-workers-changes.outputs.workers_changed == 'true' && needs.check-ci-status.outputs.ci_success == 'true') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging'))
    environment: staging
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event_name == 'workflow_run' && github.event.workflow_run.head_sha || github.ref }}
          fetch-depth: 0

      - name: Set up GCP authentication
        uses: ./.github/actions/gcp-auth-setup
        with:
          project-id: ${{ secrets.GCP_PROJECT_ID }}
          service-account: ${{ secrets.GCP_SERVICE_ACCOUNT }}
          workload-identity-provider: ${{ secrets.WORKLOAD_IDENTITY_PROVIDER }}

      - name: Build and push Docker image
        id: build_push_staging
        uses: ./.github/actions/docker-build-push
        with:
          dockerfile-path: apps/backend/src/a2a_platform/workers/Dockerfile
          build-context: apps/backend
          image-name: workers
          environment: staging
          artifact-registry-host: us-central1-docker.pkg.dev
          repository: a2a-staging-containers
          project-id: ${{ secrets.GCP_PROJECT_ID }}
          # build-args-json: |
          #   {
          #     "WORKERS_SPECIFIC_BUILD_ARG": "some_value_for_staging"
          #   }

      - name: Prepare Environment Variables JSON for Staging
        id: prepare_env_vars_staging
        shell: bash
        run: |
          env_vars_json=$(echo '{"ENVIRONMENT": "staging"}' | jq '.')

          # Configure database URLs for staging environment
          if [[ -n "${{ secrets.DATABASE_URL }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.DATABASE_URL }}" '. + {"DATABASE_URL": $v}')
          fi

          if [[ -n "${{ secrets.DATABASE_ASYNC_URL }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.DATABASE_ASYNC_URL }}" '. + {"DATABASE_ASYNC_URL": $v}')
          fi
          if [[ -n "${{ secrets.REDIS_URL }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.REDIS_URL }}" '. + {"REDIS_URL": $v}')
          fi
          # Add other worker-specific secrets
          if [[ -n "${{ secrets.CLERK_SECRET_KEY }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.CLERK_SECRET_KEY }}" '. + {"CLERK_SECRET_KEY": $v}')  # Keeping this as CLERK_SECRET_KEY if it's distinct
          fi
          if [[ -n "${{ secrets.CLERK_API_KEY }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.CLERK_API_KEY }}" '. + {"CLERK_API_KEY": $v}')
          fi
          if [[ -n "${{ secrets.CLERK_JWT_PUBLIC_KEY }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.CLERK_JWT_PUBLIC_KEY }}" '. + {"CLERK_JWT_PUBLIC_KEY": $v}')
          fi
          if [[ -n "${{ secrets.CLERK_WEBHOOK_SECRET }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.CLERK_WEBHOOK_SECRET }}" '. + {"CLERK_WEBHOOK_SECRET": $v}')
          fi
          if [[ -n "${{ secrets.PUBSUB_PROJECT_ID }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.PUBSUB_PROJECT_ID }}" '. + {"PUBSUB_PROJECT_ID": $v}')
          fi
          if [[ -n "${{ secrets.STORAGE_BUCKET_STAGING }}" ]]; then  # Assuming staging-specific bucket
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.STORAGE_BUCKET_STAGING }}" '. + {"STORAGE_BUCKET": $v}')
          elif [[ -n "${{ secrets.STORAGE_BUCKET }}" ]]; then  # Fallback to a general one
             env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.STORAGE_BUCKET }}" '. + {"STORAGE_BUCKET": $v}')
          fi
          if [[ -n "${{ secrets.CDN_URL_STAGING }}" ]]; then  # Assuming staging-specific CDN URL
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.CDN_URL_STAGING }}" '. + {"CDN_URL": $v}')
          elif [[ -n "${{ secrets.CDN_URL }}" ]]; then  # Fallback to a general one
             env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.CDN_URL }}" '. + {"CDN_URL": $v}')
          fi

          echo "env_vars_json<<EOF" >> $GITHUB_OUTPUT
          echo "$env_vars_json" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Deploy Workers to Staging Cloud Run
        uses: ./.github/actions/gcloud-run-deploy
        with:
          service-name: workers
          environment: staging
          region: us-central1
          image: ${{ steps.build_push_staging.outputs.image-url }}
          vpc-connector: projects/${{ secrets.GCP_PROJECT_ID }}/locations/us-central1/connectors/a2a-staging-vpc-connector
          allow-unauthenticated: false
          port: 8081 # Port for the health check server
          startup-probe: "/health"
          min-instances: '0'
          max-instances: 'default'
          cloudsql-instances: ${{ secrets.CLOUDSQL_INSTANCE }}  # Enable Cloud SQL connection for migrations
          cpu: '1'
          memory: '512Mi'
          concurrency: '80' # May not be relevant for non-HTTP workers
          timeout: '300'
          env_vars_json: ${{ steps.prepare_env_vars_staging.outputs.env_vars_json }}
          run-migrations: true  # Enable migrations for workers
          alembic-db-url: ${{ secrets.MIGRATION_DATABASE_URL || secrets.DATABASE_URL }}  # Use migration-specific URL if available, fallback to regular DATABASE_URL

  deploy_workers_production:
    name: Deploy Workers to Production
    runs-on: ubuntu-latest
    needs: [check-ci-status, detect-workers-changes, deploy_workers_staging]
    if: github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production' && needs.deploy_workers_staging.result == 'success'
    environment: production  # Requires manual approval for production deployment
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }} # For workflow_dispatch
          fetch-depth: 0

      # Note: No changed-files check for production as it's purely manual dispatch
      - name: Set up GCP authentication
        uses: ./.github/actions/gcp-auth-setup
        with:
          project-id: ${{ secrets.GCP_PROJECT_ID }}
          service-account: ${{ secrets.GCP_SERVICE_ACCOUNT }}
          workload-identity-provider: ${{ secrets.WORKLOAD_IDENTITY_PROVIDER }}

      - name: Build and push Docker image for Production
        id: build_push_production
        uses: ./.github/actions/docker-build-push
        with:
          dockerfile-path: apps/backend/src/a2a_platform/workers/Dockerfile
          build-context: apps/backend
          image-name: workers
          environment: production
          artifact-registry-host: us-central1-docker.pkg.dev
          repository: a2a-production-containers
          project-id: ${{ secrets.GCP_PROJECT_ID }}
          # build-args-json: |
          #   {
          #     "WORKERS_SPECIFIC_BUILD_ARG": "some_value_for_production"
          #   }

      - name: Prepare Environment Variables JSON for Production
        id: prepare_env_vars_production
        shell: bash
        run: |
          env_vars_json=$(echo '{"ENVIRONMENT": "production"}' | jq '.')

          # Configure database URLs for production environment
          if [[ -n "${{ secrets.DATABASE_URL }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.DATABASE_URL }}" '. + {"DATABASE_URL": $v}')
          fi

          if [[ -n "${{ secrets.DATABASE_ASYNC_URL }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.DATABASE_ASYNC_URL }}" '. + {"DATABASE_ASYNC_URL": $v}')
          fi
          if [[ -n "${{ secrets.REDIS_URL }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.REDIS_URL }}" '. + {"REDIS_URL": $v}')
          fi
          # Add other worker-specific secrets
          if [[ -n "${{ secrets.CLERK_SECRET_KEY }}" ]]; then  # Note: This was CLERK_SECRET_KEY, the error log shows CLERK_API_KEY. Assuming CLERK_API_KEY is correct.
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.CLERK_SECRET_KEY }}" '. + {"CLERK_SECRET_KEY": $v}')  # Keeping this as CLERK_SECRET_KEY if it's distinct
          fi
          if [[ -n "${{ secrets.CLERK_API_KEY }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.CLERK_API_KEY }}" '. + {"CLERK_API_KEY": $v}')
          fi
          if [[ -n "${{ secrets.CLERK_JWT_PUBLIC_KEY }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.CLERK_JWT_PUBLIC_KEY }}" '. + {"CLERK_JWT_PUBLIC_KEY": $v}')
          fi
          if [[ -n "${{ secrets.CLERK_WEBHOOK_SECRET }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.CLERK_WEBHOOK_SECRET }}" '. + {"CLERK_WEBHOOK_SECRET": $v}')
          fi
          if [[ -n "${{ secrets.PUBSUB_PROJECT_ID }}" ]]; then
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.PUBSUB_PROJECT_ID }}" '. + {"PUBSUB_PROJECT_ID": $v}')
          fi
          if [[ -n "${{ secrets.STORAGE_BUCKET_PRODUCTION }}" ]]; then  # Assuming production-specific bucket
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.STORAGE_BUCKET_PRODUCTION }}" '. + {"STORAGE_BUCKET": $v}')
          elif [[ -n "${{ secrets.STORAGE_BUCKET }}" ]]; then  # Fallback to a general one
             env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.STORAGE_BUCKET }}" '. + {"STORAGE_BUCKET": $v}')
          fi
          if [[ -n "${{ secrets.CDN_URL_PRODUCTION }}" ]]; then  # Assuming production-specific CDN URL
            env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.CDN_URL_PRODUCTION }}" '. + {"CDN_URL": $v}')
          elif [[ -n "${{ secrets.CDN_URL }}" ]]; then  # Fallback to a general one
             env_vars_json=$(echo "$env_vars_json" | jq --arg v "${{ secrets.CDN_URL }}" '. + {"CDN_URL": $v}')
          fi

          echo "env_vars_json<<EOF" >> $GITHUB_OUTPUT
          echo "$env_vars_json" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Deploy Workers to Production Cloud Run
        uses: ./.github/actions/gcloud-run-deploy
        with:
          service-name: workers
          environment: production
          region: us-central1
          image: ${{ steps.build_push_production.outputs.image-url }}
          vpc-connector: projects/${{ secrets.GCP_PROJECT_ID }}/locations/us-central1/connectors/a2a-production-vpc-connector
          allow-unauthenticated: false
          port: 8081 # Port for the health check server
          startup-probe: "/health"
          min-instances: '0' # Consider adjusting for production
          max-instances: 'default'
          cloudsql-instances: ${{ secrets.CLOUDSQL_INSTANCE }}  # Enable Cloud SQL connection for migrations
          cpu: '1'
          memory: '512Mi'
          concurrency: '80' # May not be relevant for non-HTTP workers
          timeout: '300'
          env_vars_json: ${{ steps.prepare_env_vars_production.outputs.env_vars_json }}
          run-migrations: true  # Enable migrations for workers
          alembic-db-url: ${{ secrets.MIGRATION_DATABASE_URL || secrets.DATABASE_URL }}  # Use migration-specific URL if available, fallback to regular DATABASE_URL
