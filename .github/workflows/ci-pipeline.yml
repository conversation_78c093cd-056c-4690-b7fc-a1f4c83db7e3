name: <PERSON><PERSON> (Static Analysis & Tests)

on:
  push:
    branches:
      - main
      - 'feature/**'
      - 'fix/**'
      - 'hotfix/**'
      - 'bugfix/**'
    paths-ignore:
      - '**.md'
      - 'docs/**'
      - 'LICENSE'
      - '.gitignore'

# Workflow level environment variables shared across jobs
env:
  PUBSUB_PROJECT_ID: test_project_id
  CLERK_API_KEY: test_clerk_api_key
  CLERK_JWT_PUBLIC_KEY: test_clerk_jwt_public_key
  CLERK_WEBHOOK_SECRET: test_clerk_webhook_signing_secret
  STORAGE_BUCKET: test_storage_bucket
  CDN_URL: https://test-cdn.example.com
  CORS_ORIGINS: ${{ github.event_name == 'pull_request' && 'http://127.0.0.1:5173,http://localhost:5173,https://localhost:5173' || 'https://localhost:5173' }}
  POSTGRES_USER: postgres
  POSTGRES_PASSWORD: postgres
  POSTGRES_DB: a2a_platform_test
  PGPASSWORD: postgres
  # Database environment variables for CI
  DATABASE_URL: postgresql://postgres:postgres@localhost:5432/a2a_platform_test
  DATABASE_ASYNC_URL: postgresql+asyncpg://postgres:postgres@localhost:5432/a2a_platform_test
  REDIS_URL: redis://localhost:6379/1
  OPENAI_API_KEY: "sk-******"
  ANTHROPIC_API_KEY: "sk-ant-******"
  GEMINI_API_KEY: "AIza******"
  GOOGLE_API_KEY: "AIza******"
  AI_RESPONSE_ENABLED: true
  AI_RESPONSE_TIMEOUT: 30
  AI_RATE_LIMIT_CALLS: 50
  AI_RATE_LIMIT_WINDOW: 60
  AI_DEFAULT_PROVIDER: "google"
  ENABLE_TEST_AUTH_BYPASS: true

jobs:
  determine_changes:
    name: Determine Changed Files
    runs-on: ubuntu-latest
    outputs:
      terraform_changed: ${{ steps.filter.outputs.terraform }}
      backend_changed: ${{ steps.filter.outputs.backend }}
      frontend_changed: ${{ steps.filter.outputs.frontend }}
      rq_changed: ${{ steps.filter.outputs.rq }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Filter changed files
        uses: dorny/paths-filter@v3
        id: filter
        with:
          filters: |
            terraform:
              - 'terraform/**'
              - '.github/workflows/terraform-**.yml'
            backend:
              - 'apps/backend/**'
              - '.github/workflows/**.yml'
            frontend:
              - 'apps/web/**'
              - '.github/workflows/**.yml'
            rq:
              - 'apps/backend/src/a2a_platform/messaging/rq_client.py'
              - 'apps/backend/src/a2a_platform/workers/rq_worker.py'
              - 'apps/backend/test_rq_*.py'
              - 'apps/backend/tests/unit/messaging/test_rq_*.py'
              - '.github/workflows/**.yml'

  # Consolidated Testing Job - All tests run sequentially
  consolidated_tests:
    name: Consolidated Tests (Static Analysis + All Tests Sequential)
    needs: determine_changes
    if: ${{ needs.determine_changes.outputs.backend_changed == 'true' || needs.determine_changes.outputs.rq_changed == 'true' }}
    runs-on: ubuntu-latest
    timeout-minutes: 45

    env:
      RQ_TEST_REDUCED_LOAD: true
      CI: true

    defaults:
      run:
        working-directory: ./apps/backend

    services:
      postgres:
        image: postgres:14
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: a2a_platform_test

      redis:
        image: redis:alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12.10'

      - name: Cache pip dependencies
        uses: actions/cache@v4
        id: cache-pip
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/pyproject.toml') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install .[dev]
          pip install psutil

      # Static Analysis Phase
      - name: Run Linters (Ruff and Black)
        run: |
          echo "Running Linters..."
          ruff check src/a2a_platform/

      - name: Run MyPy (Type Checking)
        run: |
          echo "Running MyPy for type checking..."
          mypy -p a2a_platform --config-file ./mypy.ini

      # Database Setup for PostgreSQL Tests
      - name: Wait for PostgreSQL to be ready
        run: |
          for i in {1..10}; do
            pg_isready -h localhost -p 5432 && break
            echo "PostgreSQL is unavailable - sleeping"
            sleep 1
          done
        shell: bash

      - name: Create PostgreSQL roles
        run: |
          echo "Creating required PostgreSQL roles..."
          PGPASSWORD=postgres psql -h localhost -U postgres -d postgres -c "
          DO \$\$
          BEGIN
            IF NOT EXISTS (SELECT FROM pg_roles WHERE rolname = 'root') THEN
              CREATE ROLE root WITH LOGIN PASSWORD 'postgres';
              ALTER ROLE root CREATEDB;
            END IF;
            IF NOT EXISTS (SELECT FROM pg_roles WHERE rolname = 'runner') THEN
              CREATE ROLE runner WITH LOGIN PASSWORD 'runner';
              ALTER ROLE runner CREATEDB;
            END IF;
          END
          \$\$;" || echo "Roles may already exist"
        shell: bash

      - name: Ensure test database exists
        run: |
          echo "Ensuring test database exists..."
          PGPASSWORD=postgres createdb -h localhost -U postgres -O postgres a2a_platform_test 2>/dev/null || echo "Database may already exist"
        shell: bash

      - name: Setup test database
        working-directory: ./
        run: |
          echo "Setting up test database..."
          echo "DATABASE_URL: $DATABASE_URL"
          ./scripts/setup-test-db.sh --ci

      # Testing Phase 3: PostgreSQL Tests
      - name: Run PostgreSQL Tests
        working-directory: ./
        run: |
          echo "Running PostgreSQL Integration Tests..."
          echo "DATABASE_URL: $DATABASE_URL"
          echo "DATABASE_ASYNC_URL: $DATABASE_ASYNC_URL"
          start_time=$(date +%s)
          ./scripts/run-backend-tests.sh --ci --verbose --coverage
          end_time=$(date +%s)
          duration=$((end_time - start_time))
          echo "PostgreSQL tests completed in ${duration} seconds"
          echo "POSTGRESQL_TEST_DURATION=${duration}" >> $GITHUB_ENV

      - name: Upload PostgreSQL Test Results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: postgresql-test-results
          path: apps/backend/test-results/
          retention-days: 7

      - name: Upload Coverage Reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: coverage-reports
          path: apps/backend/htmlcov/
          retention-days: 7

      # Testing Phase 4: RQ Tests
      - name: Run RQ Tests
        if: ${{ needs.determine_changes.outputs.rq_changed == 'true' }}
        working-directory: ./
        run: |
          echo "Running RQ Tests..."
          chmod +x ./scripts/run-rq-tests.sh
          ./scripts/run-rq-tests.sh --ci --reduced-load --verbose

      # Performance Summary
      - name: Generate Performance Report
        if: always()
        run: |
          echo "# 🚀 Consolidated Testing Performance Report" > performance-report.md
          echo "" >> performance-report.md
          echo "## Test Execution Summary (Sequential)" >> performance-report.md
          echo "" >> performance-report.md
          echo "✅ **Static Analysis**: Completed (Linting + Type Checking)" >> performance-report.md
          echo "✅ **PostgreSQL Tests**: Completed" >> performance-report.md
          echo "   - Full integration tests with PostgreSQL" >> performance-report.md
          echo "   - Comprehensive test coverage with database integration" >> performance-report.md
          if [ "${{ needs.determine_changes.outputs.rq_changed }}" == "true" ]; then
            echo "✅ **RQ Tests**: Completed" >> performance-report.md
            echo "   - Redis Queue integration tests" >> performance-report.md
          fi
          if [ "${{ needs.determine_changes.outputs.frontend_changed }}" == "true" ]; then
            echo "✅ **Frontend Tests**: Completed in parallel job" >> performance-report.md
            echo "   - Unit tests with Jest" >> performance-report.md
            echo "   - GraphQL E2E tests with type validation" >> performance-report.md
            echo "   - TypeScript type checking" >> performance-report.md
            echo "   - Consolidated code quality checks (Prettier + ESLint)" >> performance-report.md
            echo "   - Test coverage reporting" >> performance-report.md
          fi
          echo "" >> performance-report.md
          echo "## Performance Benefits" >> performance-report.md
          echo "- **Sequential Execution**: All tests run in order for simplified debugging" >> performance-report.md
          echo "- **Parallel Frontend Testing**: Frontend tests run independently for faster feedback" >> performance-report.md
          echo "- **Resource Optimization**: Single job reduces CI resource usage" >> performance-report.md
          echo "- **Fast Feedback**: Early failure stops subsequent tests" >> performance-report.md
          echo "- **Focused Testing**: PostgreSQL integration tests provide comprehensive coverage" >> performance-report.md
          echo "- **Selective Testing**: Run only relevant test categories based on changes" >> performance-report.md
          echo "" >> performance-report.md
          echo "Generated at: $(date)" >> performance-report.md
          cat performance-report.md

      - name: Upload Performance Report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: performance-report
          path: performance-report.md
          retention-days: 30

  # Frontend Unit Testing Job
  frontend_tests:
    name: Frontend Unit Tests
    needs: determine_changes
    if: ${{ needs.determine_changes.outputs.frontend_changed == 'true' }}
    runs-on: ubuntu-latest
    timeout-minutes: 5

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: '1.2.15'

      - name: Cache dependencies
        uses: actions/cache@v4
        id: cache-deps
        with:
          path: ~/.bun/install/cache
          key: ${{ runner.os }}-bun-${{ hashFiles('**/bun.lock') }}
          restore-keys: |
            ${{ runner.os }}-bun-

      - name: Install dependencies
        working-directory: ./apps/web
        run: |
          echo "Installing frontend dependencies..."
          bun install --frozen-lockfile

      - name: Type checking
        working-directory: ./apps/web
        run: |
          echo "Running TypeScript type checking..."
          bun run type-check

      - name: Code Quality Check (Prettier + ESLint)
        working-directory: ./apps/web
        run: |
          echo "Running consolidated code quality checks (Prettier + ESLint)..."
          bun run code:check:all

      - name: Run unit tests with coverage
        working-directory: ./apps/web
        run: |
          echo "Running frontend unit tests with coverage..."
          ../../scripts/run-frontend-tests.sh --unit --coverage

      - name: Run GraphQL E2E tests
        working-directory: ./apps/web
        run: |
          echo "Running GraphQL E2E tests..."
          bun run test:e2e:graphql

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: frontend-test-results
          path: |
            apps/web/coverage/
            apps/web/test-results/
          retention-days: 7

      - name: Upload coverage to artifacts
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: frontend-coverage-reports
          path: apps/web/coverage/
          retention-days: 7
