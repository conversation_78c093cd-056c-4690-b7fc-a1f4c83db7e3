name: CDN Migration Security Checklist Validation

# This workflow validates CDN migration security checklist on pull requests
# and can be manually triggered for specific environments

permissions:
  contents: read
  pull-requests: write
  issues: write

on:
  pull_request:
    branches:
      - main
    paths:
      - "terraform/**"
      - ".github/workflows/deploy-infra.yml"
      - ".github/workflows/deploy-frontend-cdn.yml"
      - ".github/workflows/cdn-security-checklist.yml"
      - ".github/workflows/cdn-performance-validation.yml"
      - "scripts/check-r2-security.sh"
      - "scripts/validate-cdn-migration.sh"
      - "scripts/precommit-cdn-security-check.sh"
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to validate'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      skip_live_tests:
        description: 'Skip live environment tests (DNS, SSL, etc.)'
        required: false
        default: false
        type: boolean

env:
  TERRAFORM_VERSION: '1.12.0'

jobs:
  security-checklist:
    name: 'CDN Security Checklist Validation'
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment || 'staging' }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Need full history for git diff operations

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TERRAFORM_VERSION }}

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            curl \
            openssl \
            bc \
            dnsutils \
            jq

      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pyyaml

      - name: Make scripts executable
        run: |
          chmod +x scripts/*.sh

      - name: Configure Cloudflare Credentials (if available)
        if: ${{ !inputs.skip_live_tests }}
        run: |
          echo "🔧 Setting up Cloudflare credentials for R2 validation..."
          if [[ -n "${{ secrets.CLOUDFLARE_API_TOKEN }}" ]]; then
            echo "✅ Cloudflare API token is configured"
          else
            echo "⚠️ Cloudflare API token not configured - some R2 tests may be skipped"
          fi
        continue-on-error: true

      - name: Determine validation environment
        id: env_setup
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "VALIDATION_ENV=${{ inputs.environment }}" >> $GITHUB_OUTPUT
            echo "SKIP_LIVE_TESTS=${{ inputs.skip_live_tests }}" >> $GITHUB_OUTPUT
          else
            echo "VALIDATION_ENV=staging" >> $GITHUB_OUTPUT
            echo "SKIP_LIVE_TESTS=true" >> $GITHUB_OUTPUT
          fi

      - name: Run Pre-commit CDN Security Check
        id: precommit_check
        run: |
          echo "🔍 Running pre-commit CDN security validation..."

          # Create a temporary commit to test the pre-commit hook
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"

          # Run the pre-commit security check script directly
          ./scripts/precommit-cdn-security-check.sh

      - name: Validate Terraform Configuration
        id: terraform_validation
        run: |
          echo "🏗️ Validating Terraform configuration..."

          # Find all terraform environments
          for env_dir in terraform/environments/*/; do
            if [[ -f "$env_dir/main.tf" ]]; then
              env_name=$(basename "$env_dir")
              echo "Validating Terraform environment: $env_name"

              cd "$env_dir"

              # Initialize without backend for validation
              terraform init -backend=false

              # Validate configuration
              terraform validate

              # Format check
              terraform fmt -check=true -diff=true

              cd - > /dev/null
            fi
          done

      - name: Run Terraform Security Scan
        id: terraform_security
        run: |
          echo "🔒 Running Terraform security scan..."

          # Install trivy if not available
          if ! command -v trivy &> /dev/null; then
            echo "Installing trivy..."
            sudo apt-get install wget apt-transport-https gnupg lsb-release
            wget -qO - https://aquasecurity.github.io/trivy-repo/deb/public.key | sudo apt-key add -
            echo "deb https://aquasecurity.github.io/trivy-repo/deb $(lsb_release -sc) main" | sudo tee -a /etc/apt/sources.list.d/trivy.list
            sudo apt-get update
            sudo apt-get install trivy
          fi

          # Scan terraform files for security issues
          echo "Scanning Terraform files for security issues..."
          trivy config --severity HIGH,CRITICAL terraform/

      - name: Run CDN Migration Validation (Live Tests)
        if: ${{ steps.env_setup.outputs.SKIP_LIVE_TESTS == 'false' }}
        id: cdn_validation
        run: |
          echo "🌐 Running CDN migration validation for ${{ steps.env_setup.outputs.VALIDATION_ENV }}..."
          ./scripts/validate-cdn-migration.sh ${{ steps.env_setup.outputs.VALIDATION_ENV }}

      - name: Run CDN Migration Validation (Syntax Only)
        if: ${{ steps.env_setup.outputs.SKIP_LIVE_TESTS == 'true' }}
        id: cdn_validation_syntax
        run: |
          echo "🌐 Running CDN migration validation (syntax only)..."
          ./scripts/validate-cdn-migration.sh ${{ steps.env_setup.outputs.VALIDATION_ENV }} --validate-only

      - name: Run R2 Storage Security Check (Live Tests)
        if: ${{ steps.env_setup.outputs.SKIP_LIVE_TESTS == 'false' }}
        id: r2_security
        run: |
          echo "🪣 Running R2 storage security validation for ${{ steps.env_setup.outputs.VALIDATION_ENV }}..."
          ./scripts/check-r2-security.sh ${{ steps.env_setup.outputs.VALIDATION_ENV }}

      - name: Run R2 Storage Security Check (Syntax Only)
        if: ${{ steps.env_setup.outputs.SKIP_LIVE_TESTS == 'true' }}
        id: r2_security_syntax
        run: |
          echo "🪣 Running R2 storage security validation (syntax only)..."
          ./scripts/check-r2-security.sh ${{ steps.env_setup.outputs.VALIDATION_ENV }} --validate-only

      - name: Generate Security Report
        if: always()
        id: security_report
        run: |
          echo "📊 Generating security validation report..."

          REPORT_FILE="cdn-security-validation-report.md"

          cat > "$REPORT_FILE" << EOF
          # CDN Migration Security Validation Report

          **Environment:** ${{ steps.env_setup.outputs.VALIDATION_ENV }}
          **Date:** $(date -u '+%Y-%m-%d %H:%M:%S UTC')
          **Trigger:** ${{ github.event_name }}
          **Branch:** ${{ github.ref_name }}
          **Commit:** ${{ github.sha }}

          ## Validation Results

          | Check | Status | Details |
          |-------|--------|---------|
          | Pre-commit Security Check | ${{ steps.precommit_check.outcome == 'success' && '✅ PASSED' || '❌ FAILED' }} | CDN security checklist validation |
          | Terraform Validation | ${{ steps.terraform_validation.outcome == 'success' && '✅ PASSED' || '❌ FAILED' }} | Terraform syntax and configuration |
          | Terraform Security Scan | ${{ steps.terraform_security.outcome == 'success' && '✅ PASSED' || '❌ FAILED' }} | Security vulnerability scan |
          | CDN Migration Validation | ${{ (steps.cdn_validation.outcome == 'success' || steps.cdn_validation_syntax.outcome == 'success') && '✅ PASSED' || '❌ FAILED' }} | CDN configuration and connectivity |
          | R2 Storage Security Check | ${{ (steps.r2_security.outcome == 'success' || steps.r2_security_syntax.outcome == 'success') && '✅ PASSED' || '❌ FAILED' }} | Cloudflare R2 storage security validation |

          ## Summary

          EOF

          # Determine overall status
          if [[ "${{ steps.precommit_check.outcome }}" == "success" && \
                "${{ steps.terraform_validation.outcome }}" == "success" && \
                "${{ steps.terraform_security.outcome }}" == "success" && \
                ("${{ steps.cdn_validation.outcome }}" == "success" || "${{ steps.cdn_validation_syntax.outcome }}" == "success") && \
                ("${{ steps.r2_security.outcome }}" == "success" || "${{ steps.r2_security_syntax.outcome }}" == "success") ]]; then
            echo "**Overall Status: ✅ PASSED**" >> "$REPORT_FILE"
            echo "" >> "$REPORT_FILE"
            echo "All CDN migration security validations have passed successfully." >> "$REPORT_FILE"
            echo "The changes are approved for deployment." >> "$REPORT_FILE"
          else
            echo "**Overall Status: ❌ FAILED**" >> "$REPORT_FILE"
            echo "" >> "$REPORT_FILE"
            echo "Some CDN migration security validations have failed." >> "$REPORT_FILE"
            echo "Please review and fix the issues before proceeding with deployment." >> "$REPORT_FILE"
          fi

          echo "" >> "$REPORT_FILE"
          echo "## Next Steps" >> "$REPORT_FILE"
          echo "" >> "$REPORT_FILE"
          echo "- Review any failed checks above" >> "$REPORT_FILE"
          echo "- Fix identified security issues" >> "$REPORT_FILE"
          echo "- Re-run validation after fixes" >> "$REPORT_FILE"
          echo "- Proceed with deployment only after all checks pass" >> "$REPORT_FILE"

          # Output report content for GitHub summary
          echo "SECURITY_REPORT_CONTENT<<EOF" >> $GITHUB_OUTPUT
          cat "$REPORT_FILE" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Upload Security Report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: cdn-security-report-${{ steps.env_setup.outputs.VALIDATION_ENV }}-${{ github.run_number }}
          path: cdn-security-validation-report.md
          retention-days: 30

      - name: Add PR Comment with Security Report
        if: github.event_name == 'pull_request' && always()
        uses: actions/github-script@v7
        continue-on-error: true
        with:
          script: |
            try {
              const report = `${{ steps.security_report.outputs.SECURITY_REPORT_CONTENT }}`;

              await github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: report
              });

              console.log('✅ Successfully posted security report to PR');
            } catch (error) {
              console.log('⚠️ Failed to post comment to PR:', error.message);
              console.log('📊 Security report is still available in job summary and artifacts');
            }

      - name: Set Job Summary
        if: always()
        run: |
          echo "${{ steps.security_report.outputs.SECURITY_REPORT_CONTENT }}" >> $GITHUB_STEP_SUMMARY
