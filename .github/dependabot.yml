# AUTOGENERATED BY THE ASSISTANT TOOL. DO NOT EDIT DIRECTLY.
# Please edit the plan instead.
# For more information on how to use this file, see https://docs.github.com/en/code-security/dependabot/dependabot-version-updates/configuration-options-for-the-dependabot.yml-file

version: 2
updates:
  # Python dependencies for the backend application
  - package-ecosystem: "pip"
    directory: "/apps/backend"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00" # UTC, chosen as a common time for morning checks
    pull-request-branch-name:
      separator: "-"
    commit-message:
      prefix: "chore(deps)"
      include: "scope"
    labels:
      - "dependencies"
      - "backend"
    open-pull-requests-limit: 10 # Limit the number of open PRs to avoid overwhelming reviewers
    # Major version updates require manual review and are ignored for automated PR creation for now.
    # Patch and minor updates will have PRs created.
    ignore:
      - dependency-name: "*" # Apply to all dependencies
        update-types: ["version-update:semver-major"]
    rebase-strategy: "auto" # Attempt to rebase PRs automatically when conflicts arise

  # Node.js dependencies for the web/frontend application
  - package-ecosystem: "npm"
    directory: "/apps/web"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00" # UTC
    pull-request-branch-name:
      separator: "-"
    commit-message:
      prefix: "chore(deps)"
      include: "scope"
    labels:
      - "dependencies"
      - "frontend"
    open-pull-requests-limit: 10
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-major"]
    rebase-strategy: "auto"

  # GitHub Actions used in workflows
  - package-ecosystem: "github-actions"
    directory: "/" # Scans .github/workflows by default
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00" # UTC
    pull-request-branch-name:
      separator: "-"
    commit-message:
      prefix: "ci(deps)" # Using 'ci' prefix for workflow related changes
      include: "scope"
    labels:
      - "dependencies"
      - "github-actions"
    open-pull-requests-limit: 5 # Fewer PRs for actions, as updates are generally less frequent
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-major"]
    rebase-strategy: "auto"
