# Performance Optimization Assistant

Your goal is to optimize code performance following A2A Platform optimization strategies from docs/code-optimization.md.

Ask for the code to optimize, performance metrics, and optimization goals if not provided.

## 🎯 Performance Optimization Framework

### Performance Threshold Matrix
| System Area | Acceptable | Warning | Critical |
|-------------|------------|---------|----------|
| API Response Time | < 250ms | 250-500ms | > 500ms |
| UI Render Speed | < 100ms | 100-300ms | > 300ms |
| Database Query Time | < 50ms | 50-200ms | > 200ms |
| Memory Consumption | < 75% baseline | 75-125% | > 125% |
| Network Payload Size | < 250KB | 250KB-1MB | > 1MB |

### Optimization Categories
- **🕰️ Runtime Performance**: Request latency, function execution time
- **💾 Memory Utilization**: Memory leaks, excessive allocations
- **🔄 Network Efficiency**: API call frequency, payload size
- **📝 Query Optimization**: Database query patterns, N+1 issues
- **🧬 Component Rendering**: React re-renders, DOM operations

## ⚡ Frontend Performance Optimizations

### React Component Optimization
```typescript
// ❌ BEFORE: Problematic implementation
const AgentList = () => {
  const agents = useAgentsStore(state => state.agents);
  // Inefficient: Recreated every render
  const processedAgents = agents.map(agent => ({
    ...agent,
    isCompatible: checkCompatibility(agent)
  }));

  return (
    <>
      {processedAgents.map(agent => (
        <AgentCard key={agent.id} agent={agent} />
      ))}
    </>
  );
};

// ✅ AFTER: Optimized implementation
const AgentList = () => {
  const agents = useAgentsStore(state => state.agents);

  // Optimization 1: Memoize expensive derivations
  const processedAgents = useMemo(() =>
    agents.map(agent => ({
      ...agent,
      isCompatible: checkCompatibility(agent)
    })),
    [agents]
  );

  // Optimization 2: Virtualize long lists
  return (
    <VirtualizedList
      data={processedAgents}
      renderItem={({item: agent}) => <AgentCard agent={agent} />}
      keyExtractor={agent => agent.id}
      windowSize={5}
    />
  );
};
```

### Network Request Optimization
```typescript
// ❌ BEFORE: Multiple separate fetch requests
async function loadDashboardData(userId: string) {
  const userData = await fetchUserData(userId);
  const agentData = await fetchUserAgents(userId);
  const notificationData = await fetchNotifications(userId);
  return { userData, agentData, notificationData };
}

// ✅ AFTER: Single consolidated GraphQL query
async function loadDashboardData(userId: string) {
  const result = await graphqlClient.query({
    query: DASHBOARD_DATA_QUERY,
    variables: { userId }
  });

  return {
    userData: result.data.user,
    agentData: result.data.userAgents,
    notificationData: result.data.notifications
  };
}
```

## 🚀 Backend Performance Optimizations

### Database Query Optimization
```python
# ❌ BEFORE: Inefficient query
async def search_agents(query: str, filters: dict, page: int = 1):
    stmt = (
        select(Agent)
        .where(Agent.name.ilike(f"%{query}%"))
        .where(Agent.is_public.is_(True))
    )

    # Dynamically add filters - inefficient
    for field, value in filters.items():
        stmt = stmt.where(getattr(Agent, field) == value)

    # Inefficient pagination
    stmt = stmt.offset((page - 1) * 10).limit(10)

    result = await db.execute(stmt)
    return result.scalars().all()

# ✅ AFTER: Optimized query with proper indexing
async def search_agents(query: str, filters: dict, pagination_token: str = None):
    # Base query with covering index for common filters
    stmt = (
        select(Agent)
        .where(Agent.is_public.is_(True))
    )

    # Full-text search instead of LIKE
    if query:
        stmt = stmt.where(
            Agent.search_vector.match(func.plainto_tsquery(query))
        )

    # Construct filters efficiently
    filter_conditions = []
    for field, value in filters.items():
        if field in INDEXED_FIELDS:
            filter_conditions.append(getattr(Agent, field) == value)

    if filter_conditions:
        stmt = stmt.where(and_(*filter_conditions))

    # Keyset pagination (more efficient than offset/limit)
    if pagination_token:
        last_id, last_created_at = decode_pagination_token(pagination_token)
        stmt = stmt.where(
            or_(
                Agent.created_at < last_created_at,
                and_(
                    Agent.created_at == last_created_at,
                    Agent.id > last_id
                )
            )
        )

    # Order optimized for keyset pagination
    stmt = stmt.order_by(desc(Agent.created_at), asc(Agent.id)).limit(10)

    result = await db.execute(stmt)
    agents = result.scalars().all()

    # Generate pagination token for next page
    next_token = None
    if agents and len(agents) == 10:
        last_agent = agents[-1]
        next_token = encode_pagination_token(last_agent.id, last_agent.created_at)

    return agents, next_token
```

### Caching Strategy Implementation
```python
from cachetools import TTLCache

class PermissionCache:
    def __init__(self):
        self.cache = TTLCache(maxsize=10000, ttl=300)  # 5-minute TTL

    async def batch_check_permissions(self, user_id: str, resource_ids: list[str], action: str):
        # Check cache first
        results = {}
        uncached_ids = []

        for resource_id in resource_ids:
            cache_key = f"{user_id}:{resource_id}:{action}"
            if cache_key in self.cache:
                results[resource_id] = self.cache[cache_key]
            else:
                uncached_ids.append(resource_id)

        # Batch fetch permissions for uncached resources
        if uncached_ids:
            db_results = await permission_repo.batch_check_permissions(
                user_id, uncached_ids, action
            )

            # Update cache and results
            for resource_id, allowed in db_results.items():
                cache_key = f"{user_id}:{resource_id}:{action}"
                self.cache[cache_key] = allowed
                results[resource_id] = allowed

        return results
```

## 📊 Performance Monitoring

### Performance Canary Implementation
```python
from functools import wraps
import time
from prometheus_client import Histogram

REQUEST_LATENCY = Histogram(
    'request_latency_seconds',
    'Request latency in seconds',
    ['endpoint', 'method', 'status_code']
)

def performance_canary(threshold_ms=250, alert_threshold_ms=500):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()

            try:
                result = await func(*args, **kwargs)
                status_code = getattr(result, 'status_code', 200)
            except Exception as e:
                status_code = 500
                raise e
            finally:
                execution_time = time.time() - start_time
                execution_ms = execution_time * 1000

                # Record metrics
                REQUEST_LATENCY.labels(
                    endpoint=func.__name__,
                    method=get_request_method(),
                    status_code=status_code
                ).observe(execution_time)

                # Check thresholds and alert if necessary
                if execution_ms > alert_threshold_ms:
                    log_critical_performance_issue(
                        endpoint=func.__name__,
                        execution_ms=execution_ms
                    )

            return result
        return wrapper
    return decorator
```

## 🔍 Optimization Analysis Process

### 1. Identify Bottlenecks
- Profile code execution with appropriate tools
- Analyze database query patterns
- Monitor network request waterfalls
- Check memory allocation patterns

### 2. Apply Optimization Techniques
- Algorithm optimization (reduce time complexity)
- Database query optimization (indexes, query structure)
- Caching strategies (memory, Redis, CDN)
- Code splitting and lazy loading
- Component memoization

### 3. Measure Impact
- Before/after performance metrics
- Load testing results
- User experience metrics (Core Web Vitals)
- Resource utilization monitoring

### 4. Validate Changes
- Ensure functionality remains intact
- Verify no regressions introduced
- Test edge cases and error scenarios
- Monitor production metrics

## ✅ Optimization Checklist
- [ ] Baseline performance metrics captured
- [ ] Bottlenecks identified and prioritized
- [ ] Optimization strategy selected
- [ ] Implementation completed
- [ ] Performance improvement measured
- [ ] Functionality validated
- [ ] Production monitoring in place
- [ ] Documentation updated
