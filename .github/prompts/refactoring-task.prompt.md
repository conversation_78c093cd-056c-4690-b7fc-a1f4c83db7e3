# Systematic Refactoring Assistant

Your goal is to perform systematic code refactoring following A2A Platform refactoring guidelines from docs/refactoring.md.

Ask for the code to refactor, refactoring goals, and constraints if not provided.

## 🎯 Refactoring Decision Framework

### When to Refactor (Rule of Three)
1. **Third instance** of similar code (avoid duplication)
2. **Third modification** to same component (improve adaptability)
3. **Third time** spending longer than expected (improve maintainability)

### Refactoring Objective Matrix
| Category | Goal | Success Metrics | When to Apply |
|----------|------|----------------|---------------|
| 📊 **Performance** | Optimize speed, reduce complexity | Execution time, memory usage | CPU/memory intensive operations |
| 📚 **Readability** | Clarify, rename, document | Cognitive complexity score | Complex business logic |
| 🔧 **Maintainability** | Decouple, modularize, SRP | Cyclomatic complexity | Frequently modified components |
| 🔒 **Security** | Secure, sanitize, validate | OWASP compliance | Auth flows, data processing |
| 🧩 **Scalability** | Parallelize, distribute | Request throughput | High-traffic components |

## 🔄 Refactoring Process

### 1. Assessment Phase
```
🔍 REFACTORING ASSESSMENT:

📊 Current Issues:
- [Describe specific problems with current code]

🎯 Primary Goal: [Performance/Readability/Maintainability/Security/Scalability]
📊 Secondary Goals: [If applicable]

📏 Success Criteria:
- [Specific measurable outcome 1]
- [Specific measurable outcome 2]

🚫 Constraints:
- [Must preserve X behavior]
- [Must maintain compatibility with Y]
- [Must not introduce regression in Z]
```

### 2. Strategy Selection

#### Code Smell Indicators
- **🔄 Duplicated Code**: Same logic in multiple places
- **🔮 Shotgun Surgery**: Changes require updates in many files
- **📚 Large Class/Module**: Too many responsibilities (>500 lines)
- **⛓️ Long Parameter List**: Functions with 5+ parameters
- **🥾 Feature Envy**: Function uses more features of another class

#### A2A Platform Specific Triggers
| Component | Refactoring Indicator | Potential Solution |
|-----------|----------------------|-------------------|
| 🤖 **Agent Marketplace** | API payloads unwieldy | Extract DTOs, normalize responses |
| 🔌 **Integration Services** | New provider needs 5+ file changes | Extract adapter pattern |
| 🔐 **Auth Flow** | Auth checks scattered | Implement uniform middleware |
| 🔄 **State Management** | Components re-fetching same data | Implement query caching |

### 3. Implementation Patterns

#### Python/Backend Refactoring
```python
# ❌ BEFORE: Problematic implementation
async def register_agent(agent_data: AgentCreate, db: AsyncSession):
    # Check if agent exists
    existing = await db.execute(
        select(Agent).where(Agent.name == agent_data.name)
    )
    existing_agent = existing.scalars().first()
    if existing_agent:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Agent with name {agent_data.name} already exists",
        )

    # Create new agent
    new_agent = Agent(
        name=agent_data.name,
        description=agent_data.description,
        capabilities=agent_data.capabilities,
        api_key=generate_api_key(),
        status="active",
    )
    db.add(new_agent)
    await db.commit()
    await db.refresh(new_agent)

    # Log registration
    logger.info(f"New agent registered: {new_agent.name}")

    return new_agent

# ✅ AFTER: Refactored with separation of concerns
class AgentRegistrationService:
    def __init__(self, agent_repo: AgentRepository, logger: Logger):
        self.agent_repo = agent_repo
        self.logger = logger

    async def register_agent(self, agent_data: AgentCreate) -> Agent:
        """Register a new agent with validation and logging."""
        await self._validate_agent_uniqueness(agent_data.name)

        new_agent = await self._create_agent(agent_data)

        self.logger.info(f"New agent registered: {new_agent.name}")

        return new_agent

    async def _validate_agent_uniqueness(self, name: str) -> None:
        """Validate that agent name is unique."""
        if await self.agent_repo.exists_by_name(name):
            raise AgentAlreadyExistsError(f"Agent with name {name} already exists")

    async def _create_agent(self, agent_data: AgentCreate) -> Agent:
        """Create and persist new agent."""
        agent = Agent(
            name=agent_data.name,
            description=agent_data.description,
            capabilities=agent_data.capabilities,
            api_key=generate_api_key(),
            status=AgentStatus.ACTIVE,
        )

        return await self.agent_repo.create(agent)
```

#### React/Frontend Refactoring
```typescript
// ❌ BEFORE: Problematic implementation
function AgentDashboard({ userId }: { userId: string }) {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAgents = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/users/${userId}/agents`);
        if (!response.ok) {
          throw new Error('Failed to fetch agents');
        }
        const data = await response.json();
        setAgents(data);
        setIsLoading(false);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        setIsLoading(false);
      }
    };

    fetchAgents();
  }, [userId]);

  if (isLoading) return <div>Loading agents...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="dashboard-container">
      {agents.map(agent => (
        <AgentCard key={agent.id} agent={agent} />
      ))}
    </div>
  );
}

// ✅ AFTER: Refactored with Apollo Client and proper separation
function AgentDashboard({ userId }: { userId: string }) {
  const {
    data,
    loading,
    error
  } = useQuery(GET_USER_AGENTS, {
    variables: { userId },
    pollInterval: 5 * 60 * 1000, // 5 minutes
  });

  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorDisplay error={error} />;

  const agents = data?.userAgents || [];

  return (
    <div className="dashboard-container">
      <AgentGrid agents={agents} />
    </div>
  );
}

// Extracted components for better separation
function AgentGrid({ agents }: { agents: Agent[] }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {agents.map(agent => (
        <AgentCard key={agent.id} agent={agent} />
      ))}
    </div>
  );
}
```

## 🧪 Refactoring Validation

### Testing Strategy
```python
# Test generation for refactored code
class TestAgentRegistrationService:
    @pytest.fixture
    def service(self):
        mock_repo = Mock(spec=AgentRepository)
        mock_logger = Mock(spec=Logger)
        return AgentRegistrationService(mock_repo, mock_logger)

    @pytest.mark.asyncio
    async def test_register_agent_success(self, service):
        """Test successful agent registration."""
        # Arrange
        agent_data = AgentCreate(name="test-agent")
        service.agent_repo.exists_by_name.return_value = False
        service.agent_repo.create.return_value = Agent(id=1, name="test-agent")

        # Act
        result = await service.register_agent(agent_data)

        # Assert
        assert result.name == "test-agent"
        service.agent_repo.exists_by_name.assert_called_once_with("test-agent")
        service.logger.info.assert_called_once()

    @pytest.mark.asyncio
    async def test_register_agent_duplicate_name(self, service):
        """Test registration with duplicate name."""
        # Arrange
        agent_data = AgentCreate(name="existing-agent")
        service.agent_repo.exists_by_name.return_value = True

        # Act & Assert
        with pytest.raises(AgentAlreadyExistsError):
            await service.register_agent(agent_data)
```

## 📋 Refactoring Checklist

### Pre-Refactoring
- [ ] Identify specific code smells or issues
- [ ] Define clear refactoring goals
- [ ] Ensure comprehensive test coverage exists
- [ ] Document current behavior and constraints

### During Refactoring
- [ ] Make incremental changes
- [ ] Run tests after each change
- [ ] Maintain existing API contracts
- [ ] Follow established coding standards
- [ ] Use double quotes for string literals (frontend) / single quotes (backend Python)
- [ ] Apply proper error handling patterns

### Post-Refactoring
- [ ] Verify all tests pass
- [ ] Confirm performance hasn't degraded
- [ ] Update documentation if needed
- [ ] Review code for new potential improvements
- [ ] Monitor production metrics

### A2A Platform Specific
- [ ] Use SQLAlchemy ORM (no f-string SQL)
- [ ] Implement Pydantic models for data
- [ ] Follow async/await patterns correctly
- [ ] Use environment variables for configuration
- [ ] Apply proper TypeScript typing
- [ ] Implement proper error boundaries
