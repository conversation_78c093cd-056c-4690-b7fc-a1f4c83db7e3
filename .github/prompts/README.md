# A2A Platform - GitHub Copilot Prompt Files

This directory contains reusable prompt files for GitHub Copilot Chat in VS Code. These files help standardize common development tasks and provide consistent context for AI-assisted development.

## 📁 Available Prompt Files

- `new-react-component.prompt.md` - Generate new React components following project standards
- `api-endpoint.prompt.md` - Create new FastAPI endpoints with proper patterns
- `graphql-schema.prompt.md` - Generate GraphQL schemas, resolvers, and mutations with Strawberry
- `database-migration.prompt.md` - Generate Alembic database migrations
- `test-generation.prompt.md` - Create comprehensive test suites
- `security-review.prompt.md` - Perform security reviews of code changes
- `performance-optimization.prompt.md` - Optimize code for performance
- `refactoring-task.prompt.md` - Systematic code refactoring guidance

## 🚀 How to Use Prompt Files

1. **Enable prompt files** in VS Code workspace settings:
   ```json
   {
     "chat.promptFiles": true
   }
   ```

2. **Access prompts** in Copilot Chat:
   - Click the "Attach context" icon (📎)
   - Select "Prompt..." from dropdown
   - Choose the relevant prompt file

3. **Combine prompts** with additional context:
   - Attach multiple prompt files if needed
   - Add specific files or code snippets
   - Provide additional instructions in chat

## 💡 Best Practices

- Use prompt files for repetitive tasks that require consistent patterns
- Combine prompts with project-specific context files
- Reference relevant ADRs and specifications in prompts
- Update prompts based on evolving project standards

## 🔄 Contributing

When adding new prompt files:
- Follow the naming convention: `task-description.prompt.md`
- Include clear instructions and context
- Reference project standards and conventions
- Test prompts with various scenarios before committing
