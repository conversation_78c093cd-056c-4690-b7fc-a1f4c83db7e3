# New React Component Generator

Your goal is to generate a new React component following A2A Platform standards.

Ask for the component name, purpose, and props if not provided.

## 🎯 Requirements for React Components

### Technology Stack
- **React 19** with TypeScript
- **Apollo Client** for GraphQL data fetching and caching
- **Tailwind CSS** for styling
- **Double quotes** for string literals

### Component Structure
```typescript
// ComponentName.tsx
import { useState, useEffect } from "react";
import { useQuery } from "@apollo/client";
import { GET_COMPONENT_DATA } from "@lib/graphql/queries";

interface ComponentNameProps {
  // Define props with TypeScript types
}

export function ComponentName({ prop1, prop2 }: ComponentNameProps) {
  // Component implementation
}

export default ComponentName;
```

### Styling Guidelines
- Use Tailwind CSS classes for styling
- Follow responsive design patterns
- Use consistent spacing and color schemes
- Implement proper hover and focus states

### State Management
- Use **Apollo Client** for GraphQL server state and caching
- Use `useState` for local component state
- Implement proper loading and error states with Apollo's built-in states
- Use Apollo's optimistic updates and cache management
- Leverage Zustand for global application state when needed

### TypeScript Standards
- Define clear interfaces for props
- Use proper type annotations
- Leverage TypeScript path aliases (@lib/*, @components/*)
- Ensure type safety for all data flows
- Use double quotes for string literals consistently

### Testing Considerations
- Make components testable with clear data-testid attributes
- Separate business logic from presentation
- Use proper prop validation
- Consider accessibility requirements

## 📁 File Location
Place new components in:
- `apps/web/src/components/` for shared components
- `apps/web/src/features/[feature]/components/` for feature-specific components

## 🔗 Related Files
Reference these project files for context:
- TypeScript Config: `apps/web/tsconfig.json`
- Tailwind Config: `apps/web/tailwind.config.js`
- Component Examples: `apps/web/src/components/`

## ✅ Checklist
- [ ] TypeScript interfaces defined
- [ ] Proper imports and exports with double quotes
- [ ] Apollo Client GraphQL queries/mutations implemented
- [ ] Tailwind CSS styling applied
- [ ] Responsive design considerations
- [ ] Error handling with Apollo error states
- [ ] Loading states with Apollo loading indicators
- [ ] Accessibility attributes (ARIA, semantic HTML)
- [ ] Test-friendly structure with data-testid attributes
- [ ] Double quotes used consistently for string literals
