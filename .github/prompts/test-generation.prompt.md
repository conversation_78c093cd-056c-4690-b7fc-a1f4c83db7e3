# Comprehensive Test Generator

Your goal is to generate comprehensive test suites following A2A Platform testing standards.

Ask for the code to test, test type (unit/integration/e2e), and specific scenarios if not provided.

## 🎯 Testing Requirements

### Backend Testing (Python/pytest)
- Always use `./scripts/run-backend-tests.sh` from project root
- Tests must run from apps/backend directory context
- Use mocks for database operations in unit tests
- Integration tests should use real resolvers with proper async session management

#### Unit Test Structure
```python
import pytest
from unittest.mock import AsyncMock, patch
from app.services.your_service import YourService
from app.schemas.your_schema import YourCreateSchema

class TestYourService:
    @pytest.mark.asyncio
    async def test_create_success(self):
        """Test successful creation of resource."""
        # Arrange
        mock_db = AsyncMock()
        test_data = YourCreateSchema(name='test')

        # Act
        result = await YourService.create(test_data, mock_db)

        # Assert
        assert result.name == 'test'
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_validation_error(self):
        """Test creation with invalid data."""
        # Test implementation
```

#### Integration Test Structure
```python
import pytest
from httpx import AsyncClient
from app.main import app

class TestYourEndpoint:
    @pytest.mark.asyncio
    async def test_create_endpoint_success(self, async_client: AsyncClient):
        """Test successful endpoint creation."""
        # Arrange
        test_data = {'name': 'test'}

        # Act
        response = await async_client.post('/api/endpoint/', json=test_data)

        # Assert
        assert response.status_code == 201
        assert response.json()['name'] == 'test'
```

### Frontend Testing (Jest/Vitest)
- Always use `./scripts/run-frontend-tests.sh` from project root
- Use `--unit` flag for unit tests, `--e2e` flag for end-to-end tests
- Script automatically loads .env.test environment variables

#### Component Test Structure
```typescript
import { render, screen, fireEvent } from "@testing-library/react";
import { MockedProvider } from "@apollo/client/testing";
import { YourComponent } from "./YourComponent";
import { GET_COMPONENT_DATA } from "@lib/graphql/queries";

describe("YourComponent", () => {
  const mocks = [
    {
      request: {
        query: GET_COMPONENT_DATA,
        variables: { id: "test-id" },
      },
      result: {
        data: {
          componentData: {
            id: "test-id",
            name: "Test Component",
          },
        },
      },
    },
  ];

  const renderComponent = (props = {}, apolloMocks = mocks) => {
    return render(
      <MockedProvider mocks={apolloMocks} addTypename={false}>
        <YourComponent {...props} />
      </MockedProvider>
    );
  };

  it("should render component correctly", async () => {
    renderComponent();
    expect(await screen.findByText("Test Component")).toBeInTheDocument();
  });

  it("should handle user interaction", async () => {
    renderComponent();
    const button = screen.getByRole("button");
    fireEvent.click(button);
    // Assert expected behavior
  });
});
```

### End-to-End Testing (Cypress)
- Test user credentials: <EMAIL> / password
- Use proper authentication patterns from Clerk's cypress testing approach

#### E2E Test Structure
```typescript
describe('Feature Flow', () => {
  beforeEach(() => {
    cy.visit('/feature-page');
  });

  it('should complete user workflow', () => {
    // Arrange
    cy.login('<EMAIL>', 'password');

    // Act
    cy.get('[data-testid="action-button"]').click();
    cy.get('[data-testid="input-field"]').type('test data');
    cy.get('[data-testid="submit-button"]').click();

    // Assert
    cy.get('[data-testid="success-message"]').should('be.visible');
  });
});
```

## 🔧 Test Environment Setup

### Required Environment Variables
```bash
REDIS_URL=redis://localhost:6379
CLERK_API_KEY=test_key
CLERK_JWT_PUBLIC_KEY=test_public_key
CLERK_WEBHOOK_SECRET=whsec_test_secret
STORAGE_BUCKET=test_bucket
CDN_URL=https://test-cdn.com
PUBSUB_PROJECT_ID=test_project
```

### Database Testing
- In CI environments, database hostname must be 'localhost' (not 'db')
- Use transactional database sessions with rollback for test isolation
- Prefer structured Pydantic models over dictionaries for data handling

## 📁 Test File Locations
- Backend unit tests: `apps/backend/tests/unit/`
- Backend integration tests: `apps/backend/tests/integration/`
- Frontend tests: `apps/web/src/components/__tests__/`
- E2E tests: `apps/web/cypress/e2e/`

## ✅ Test Coverage Checklist
- [ ] Happy path scenarios
- [ ] Error handling cases
- [ ] Edge cases and boundary conditions
- [ ] Authentication/authorization
- [ ] Input validation
- [ ] Database operations
- [ ] API responses
- [ ] User interactions
- [ ] Performance considerations
- [ ] Security scenarios
