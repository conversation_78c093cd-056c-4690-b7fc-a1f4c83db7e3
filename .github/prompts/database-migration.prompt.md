# Database Migration Generator

Your goal is to generate Alembic database migrations following A2A Platform database standards.

Ask for the migration purpose, table changes, and data migration needs if not provided.

## 🗄️ Database Migration Requirements

### Technology Stack
- **SQLAlchemy 2.0** with async support
- **Alembic** for database migrations
- **PostgreSQL** as the database engine
- **Environment variables** for database connections

### Migration File Structure
```python
"""Migration description

Revision ID: {revision_id}
Revises: {previous_revision}
Create Date: {timestamp}
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = '{revision_id}'
down_revision = '{previous_revision}'
branch_labels = None
depends_on = None

def upgrade() -> None:
    """Upgrade database schema."""
    # Migration logic here
    pass

def downgrade() -> None:
    """Downgrade database schema."""
    # Rollback logic here
    pass
```

## 📊 Common Migration Patterns

### Creating New Tables
```python
def upgrade() -> None:
    """Create new table with proper constraints."""
    op.create_table(
        'user_preferences',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.String(length=255), nullable=False),
        sa.Column('preferences', postgresql.JSONB(astext_type=sa.Text()), 
                 nullable=False, server_default='{}'),
        sa.Column('timezone', sa.String(length=50), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), 
                 server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), 
                 server_default=sa.text('now()'), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.Index('ix_user_preferences_user_id', 'user_id')
    )

def downgrade() -> None:
    """Drop the table."""
    op.drop_table('user_preferences')
```

### Adding Columns
```python
def upgrade() -> None:
    """Add new columns with proper defaults."""
    # Add nullable column first
    op.add_column('agents', sa.Column('status', sa.String(length=20), nullable=True))
    
    # Set default values for existing records
    op.execute("UPDATE agents SET status = 'active' WHERE status IS NULL")
    
    # Make column non-nullable
    op.alter_column('agents', 'status', nullable=False)
    
    # Add check constraint
    op.create_check_constraint(
        'ck_agents_status_valid',
        'agents',
        "status IN ('active', 'inactive', 'pending')"
    )

def downgrade() -> None:
    """Remove the column."""
    op.drop_constraint('ck_agents_status_valid', 'agents', type_='check')
    op.drop_column('agents', 'status')
```

### Creating Indexes
```python
def upgrade() -> None:
    """Create indexes for better query performance."""
    # Standard index
    op.create_index('ix_agents_name', 'agents', ['name'])
    
    # Partial index
    op.create_index(
        'ix_agents_active_created_at',
        'agents',
        ['created_at'],
        postgresql_where=sa.text("status = 'active'")
    )
    
    # Full-text search index
    op.execute("""
        ALTER TABLE agents 
        ADD COLUMN search_vector tsvector 
        GENERATED ALWAYS AS (
            to_tsvector('english', coalesce(name, '') || ' ' || coalesce(description, ''))
        ) STORED
    """)
    
    op.create_index(
        'ix_agents_search_vector',
        'agents',
        ['search_vector'],
        postgresql_using='gin'
    )

def downgrade() -> None:
    """Remove indexes."""
    op.drop_index('ix_agents_search_vector')
    op.drop_column('agents', 'search_vector')
    op.drop_index('ix_agents_active_created_at')
    op.drop_index('ix_agents_name')
```

### Data Migrations
```python
def upgrade() -> None:
    """Migrate data with proper error handling."""
    # Create new table structure first
    op.create_table(
        'agent_capabilities_new',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('agent_id', sa.Integer(), nullable=False),
        sa.Column('capability_name', sa.String(length=100), nullable=False),
        sa.Column('capability_config', postgresql.JSONB(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['agent_id'], ['agents.id'], ondelete='CASCADE')
    )
    
    # Migrate data from old structure
    connection = op.get_bind()
    
    # Fetch data from old table
    result = connection.execute(sa.text("""
        SELECT id, capabilities 
        FROM agents 
        WHERE capabilities IS NOT NULL
    """))
    
    # Transform and insert data
    for row in result:
        agent_id = row.id
        capabilities = row.capabilities  # Assuming JSON array
        
        if capabilities:
            for capability in capabilities:
                connection.execute(sa.text("""
                    INSERT INTO agent_capabilities_new (agent_id, capability_name)
                    VALUES (:agent_id, :capability_name)
                """), {
                    'agent_id': agent_id,
                    'capability_name': capability
                })
    
    # Drop old column and rename new table
    op.drop_column('agents', 'capabilities')
    op.rename_table('agent_capabilities_new', 'agent_capabilities')

def downgrade() -> None:
    """Reverse data migration."""
    # Add back old column
    op.add_column('agents', sa.Column('capabilities', postgresql.JSONB(), nullable=True))
    
    # Migrate data back
    connection = op.get_bind()
    
    result = connection.execute(sa.text("""
        SELECT agent_id, array_agg(capability_name) as capabilities
        FROM agent_capabilities
        GROUP BY agent_id
    """))
    
    for row in result:
        connection.execute(sa.text("""
            UPDATE agents 
            SET capabilities = :capabilities 
            WHERE id = :agent_id
        """), {
            'agent_id': row.agent_id,
            'capabilities': row.capabilities
        })
    
    # Drop new table
    op.drop_table('agent_capabilities')
```

## 🔒 Migration Security & Best Practices

### Safe Migration Patterns
```python
def upgrade() -> None:
    """Safe migration with proper transaction handling."""
    # Use explicit transactions for data migrations
    connection = op.get_bind()
    trans = connection.begin()
    
    try:
        # Perform migration steps
        op.add_column('users', sa.Column('email_verified', sa.Boolean(), nullable=True))
        
        # Set default values
        connection.execute(sa.text("UPDATE users SET email_verified = false"))
        
        # Make column non-nullable
        op.alter_column('users', 'email_verified', nullable=False)
        
        trans.commit()
    except Exception:
        trans.rollback()
        raise
```

### Environment-Specific Considerations
```python
def upgrade() -> None:
    """Handle different environments appropriately."""
    # Check if running in CI environment
    connection = op.get_bind()
    
    # For CI, use localhost hostname
    if os.getenv('CI'):
        # CI-specific migration logic
        pass
    else:
        # Local/production migration logic
        pass
```

## 📋 Migration Checklist

### Pre-Migration
- [ ] **Backup database** before running migration
- [ ] **Test migration** on development environment
- [ ] **Review SQL** generated by Alembic
- [ ] **Check dependencies** on other tables/columns
- [ ] **Estimate downtime** for production deployment

### Migration Content
- [ ] **Proper column types** and constraints
- [ ] **Foreign key relationships** correctly defined
- [ ] **Indexes created** for query performance
- [ ] **Default values** set appropriately
- [ ] **Data migration** logic included if needed

### Post-Migration
- [ ] **Verify data integrity** after migration
- [ ] **Test application** functionality
- [ ] **Monitor performance** of new queries
- [ ] **Update documentation** if schema changed
- [ ] **Clean up** any temporary migration artifacts

### A2A Platform Specific
- [ ] Use **PostgreSQL JSONB** for JSON data with server_default='{}'
- [ ] Implement **deep merging** for user preferences
- [ ] Use **environment variables** for database connections
- [ ] Follow **async SQLAlchemy** patterns
- [ ] Create **proper indexes** for search functionality
- [ ] Handle **timezone-aware** datetime fields

## 🚨 Common Migration Pitfalls

### Avoid These Patterns
- ❌ **Large data migrations** without batching
- ❌ **Dropping columns** without proper backup
- ❌ **Missing rollback** logic in downgrade()
- ❌ **Hardcoded values** instead of environment variables
- ❌ **Missing indexes** on foreign keys
- ❌ **Nullable foreign keys** without proper handling

### Safe Alternatives
- ✅ **Batch large data** migrations in chunks
- ✅ **Rename columns** instead of dropping immediately
- ✅ **Test rollback** scenarios thoroughly
- ✅ **Use environment variables** for configuration
- ✅ **Create indexes** for all foreign keys
- ✅ **Use CASCADE** options appropriately
