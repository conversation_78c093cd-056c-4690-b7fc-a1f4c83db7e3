# FastAPI Endpoint Generator

Your goal is to generate a new FastAPI endpoint following A2A Platform backend standards.

Ask for the endpoint purpose, HTTP method, path, and data models if not provided.

## 🎯 Requirements for API Endpoints

### Technology Stack
- **Python 3.12** with FastAPI
- **SQLAlchemy 2.0** (async) for database operations
- **Pydantic models** for request/response validation
- **Single quotes** for string literals

### Endpoint Structure
```python
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import get_db
from app.schemas.your_schema import YourCreateSchema, YourResponseSchema
from app.services.your_service import YourService

router = APIRouter(prefix='/api/endpoint', tags=['endpoint'])

@router.post('/', status_code=status.HTTP_201_CREATED, response_model=YourResponseSchema)
async def create_endpoint(
    data: YourCreateSchema,
    db: AsyncSession = Depends(get_db)
) -> YourResponseSchema:
    """
    Create a new resource.

    Args:
        data: The resource creation data
        db: Database session

    Returns:
        The created resource

    Raises:
        HTTPException: If creation fails
    """
    try:
        result = await YourService.create(data, db)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
```

### Database Patterns
- Use **SQLAlchemy ORM** or Core Expression Language
- **Never use f-string SQL interpolation** (security risk)
- Use environment variables for database connections
- Implement proper async session management
- Use Pydantic models instead of raw dictionaries

### API Design Standards
- Use `/api/internal/` pattern for internal endpoints
- Implement proper HTTP status codes
- Use camelCase for GraphQL mutations (e.g., updateMyProfile)
- Include comprehensive error handling
- Add proper OpenAPI documentation

### Authentication & Security
- Use Clerk authentication where required
- Implement proper CORS handling
- Validate all input data with Pydantic
- Use structured logging for security events
- Include rate limiting considerations

### Error Handling
```python
from app.core.exceptions import ValidationError, NotFoundError

try:
    result = await service_method()
    return result
except NotFoundError as e:
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail=str(e)
    )
except ValidationError as e:
    raise HTTPException(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        detail=str(e)
    )
```

## 📁 File Location
Place new endpoints in:
- `apps/backend/src/app/api/routes/` for route definitions
- `apps/backend/src/app/services/` for business logic
- `apps/backend/src/app/schemas/` for Pydantic models

## 🔗 Related Files
Reference these project files for context:
- Database Models: `apps/backend/src/app/models/`
- Existing Services: `apps/backend/src/app/services/`
- Schema Examples: `apps/backend/src/app/schemas/`

## ✅ Checklist
- [ ] Proper async/await usage
- [ ] Pydantic schema validation
- [ ] SQLAlchemy ORM usage
- [ ] Error handling implemented
- [ ] Authentication considered
- [ ] OpenAPI documentation
- [ ] Single quotes used
- [ ] Environment variables for config
