# GraphQL Schema Generator

Your goal is to generate GraphQL schemas, resolvers, and mutations following A2A Platform GraphQL standards from docs/graphql-guidelines.md.

Ask for the GraphQL component type (schema/resolver/mutation), entity name, and functionality if not provided.

## 🎯 Requirements for GraphQL Components

### Technology Stack
- **Strawberry GraphQL** for schema definitions and type annotations
- **FastAPI** integration with GraphQLRouter
- **SQLAlchemy 2.0** (async) for database operations
- **Clerk Authentication** via AuthMiddleware
- **Pydantic schemas** for data validation and conversion

### GraphQL Schema Structure
```python
import strawberry
import uuid
from datetime import datetime
from typing import Optional
from strawberry.types import Info

from a2a_platform.schemas.your_entity import YourEntityRead
from a2a_platform.services.your_service import YourService

@strawberry.type
class YourEntity:
    """GraphQL type for YourEntity."""

    id: uuid.UUID
    name: str
    description: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    @classmethod
    def from_pydantic(cls, entity: YourEntityRead) -> "YourEntity":
        """Convert from Pydantic schema to GraphQL type."""
        return cls(
            id=entity.id,
            name=entity.name,
            description=entity.description,
            created_at=entity.created_at,
            updated_at=entity.updated_at,
        )

@strawberry.input
class CreateYourEntityInput:
    """Input type for creating a new entity."""
    name: str
    description: Optional[str] = None

@strawberry.input
class UpdateYourEntityInput:
    """Input type for updating an entity."""
    id: uuid.UUID
    name: Optional[str] = None
    description: Optional[str] = None

@strawberry.type
class CreateYourEntityPayload:
    """Payload type for create entity mutation."""
    entity: Optional[YourEntity] = None
    errors: Optional[list[str]] = None
```

## 🔧 Resolver Implementation Patterns

### Query Resolvers
```python
async def resolve_get_your_entity(info: Info, id: uuid.UUID) -> Optional[YourEntity]:
    """
    Get a specific entity by ID.

    Args:
        info: GraphQL resolver info containing the context
        id: The entity ID to retrieve

    Returns:
        The entity if found, None otherwise

    Raises:
        Exception: If authentication required and user not authenticated
    """
    context = info.context

    # Authentication check (if required)
    if not context.clerk_user_id:
        raise Exception("Authentication required for this operation")

    # Database session validation
    if not hasattr(context, "db_session") or context.db_session is None:
        raise Exception("Server configuration error: No database session available")

    try:
        service = YourService(context.db_session)
        entity_model = await service.get_by_id(id)

        if not entity_model:
            return None

        # Convert to Pydantic schema first, then to GraphQL type
        entity_schema = YourEntityRead.model_validate(entity_model)
        return YourEntity.from_pydantic(entity_schema)

    except Exception as e:
        logger.error(f"Error retrieving entity {id}: {str(e)}")
        raise Exception(f"Failed to retrieve entity: {str(e)}")

async def resolve_list_your_entities(
    info: Info,
    first: Optional[int] = 10,
    after: Optional[str] = None,
    filter_status: Optional[str] = None
) -> list[YourEntity]:
    """
    List entities with pagination and filtering.

    Args:
        info: GraphQL resolver info containing the context
        first: Number of items to return (max 100)
        after: Cursor for pagination
        filter_status: Optional status filter

    Returns:
        List of entities
    """
    context = info.context

    # Validate pagination parameters
    if first and first > 100:
        raise Exception("Cannot request more than 100 items at once")

    try:
        service = YourService(context.db_session)
        entities = await service.list_entities(
            limit=first or 10,
            cursor=after,
            status_filter=filter_status
        )

        # Convert to GraphQL types
        return [
            YourEntity.from_pydantic(YourEntityRead.model_validate(entity))
            for entity in entities
        ]

    except Exception as e:
        logger.error(f"Error listing entities: {str(e)}")
        raise Exception(f"Failed to list entities: {str(e)}")
```

### Mutation Resolvers (camelCase naming)
```python
async def resolve_create_your_entity(
    info: Info, input: CreateYourEntityInput
) -> CreateYourEntityPayload:
    """
    Create a new entity.

    Args:
        info: GraphQL resolver info containing the context
        input: The entity creation data

    Returns:
        The creation payload with entity or errors
    """
    context = info.context

    # Authentication required for mutations
    if not context.clerk_user_id:
        return CreateYourEntityPayload(
            entity=None,
            errors=["Authentication required for this operation"]
        )

    try:
        # Get user for authorization
        user = await get_user_by_clerk_id(context.db_session, context.clerk_user_id)
        if not user:
            return CreateYourEntityPayload(
                entity=None,
                errors=["User not found"]
            )

        # Create entity via service
        service = YourService(context.db_session)
        entity_model = await service.create_entity(
            user_id=user.id,
            name=input.name,
            description=input.description
        )

        # Convert to GraphQL type
        entity_schema = YourEntityRead.model_validate(entity_model)
        entity_graphql = YourEntity.from_pydantic(entity_schema)

        return CreateYourEntityPayload(entity=entity_graphql, errors=None)

    except ValueError as e:
        # Business logic errors
        return CreateYourEntityPayload(
            entity=None,
            errors=[str(e)]
        )
    except Exception as e:
        logger.error(f"Error creating entity: {str(e)}")
        return CreateYourEntityPayload(
            entity=None,
            errors=["Failed to create entity"]
        )

async def resolve_update_your_entity(
    info: Info, input: UpdateYourEntityInput
) -> UpdateYourEntityPayload:
    """Update an existing entity (camelCase naming)."""
    # Similar pattern to create, with update logic
    pass
```

## 🔒 Authentication & Authorization Patterns

### Clerk Authentication Integration
```python
from a2a_platform.api.graphql.middleware.auth_middleware import GraphQLContext
from a2a_platform.services.user_service import get_user_by_clerk_id

async def resolve_protected_operation(info: Info) -> YourEntity:
    """Example of protected resolver with authentication."""
    context: GraphQLContext = info.context

    # Check authentication
    if not context.clerk_user_id:
        raise Exception("Authentication required for this operation")

    # Check authorization (if needed)
    if not context.is_admin and not context.is_developer:
        raise Exception("Insufficient permissions for this operation")

    # Get authenticated user
    user = await get_user_by_clerk_id(context.db_session, context.clerk_user_id)
    if not user:
        raise Exception("User not found")

    # Proceed with operation
    # ...
```

### Role-Based Authorization
```python
from a2a_platform.core.auth.role_based_auth import UserRole

async def resolve_admin_only_operation(info: Info) -> bool:
    """Example of admin-only resolver."""
    context: GraphQLContext = info.context

    if not context.clerk_user_id:
        raise Exception("Authentication required")

    if UserRole.ADMIN not in context.roles:
        raise Exception("Admin access required")

    # Admin operation logic
    return True
```

## 🎭 Type Safety & Enum Handling

### GraphQL Enum Definitions
```python
import strawberry
from enum import Enum

class EntityStatusEnum(str, Enum):
    """Python enum for entity status."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"

@strawberry.enum
class EntityStatus:
    """GraphQL enum for entity status."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"

# Conversion between GraphQL and Python enums
def convert_graphql_to_python_enum(graphql_status: EntityStatus) -> EntityStatusEnum:
    """Convert GraphQL enum to Python enum."""
    return EntityStatusEnum(graphql_status.value)

def convert_python_to_graphql_enum(python_status: EntityStatusEnum) -> EntityStatus:
    """Convert Python enum to GraphQL enum."""
    return EntityStatus(python_status.value)
```

### JSON Field Handling
```python
import strawberry
from strawberry.scalars import JSON

@strawberry.type
class EntityWithConfig:
    """Entity with JSON configuration field."""
    id: uuid.UUID
    name: str
    configuration: Optional[JSON] = None  # type: ignore

    @strawberry.field
    def config_summary(self) -> str:
        """Computed field based on configuration."""
        if not self.configuration:
            return "No configuration"
        return f"Config with {len(self.configuration)} keys"
```

## 🚀 Performance Optimization

### N+1 Problem Prevention
```python
from strawberry.dataloader import DataLoader
from typing import List

class EntityLoader:
    """DataLoader to prevent N+1 queries."""

    def __init__(self, db_session):
        self.db_session = db_session

    async def load_entities(self, entity_ids: List[uuid.UUID]) -> List[Optional[YourEntity]]:
        """Batch load entities by IDs."""
        service = YourService(self.db_session)
        entities = await service.get_entities_by_ids(entity_ids)

        # Create a mapping for efficient lookup
        entity_map = {entity.id: entity for entity in entities}

        # Return entities in the same order as requested IDs
        return [
            YourEntity.from_pydantic(YourEntityRead.model_validate(entity_map.get(entity_id)))
            if entity_id in entity_map else None
            for entity_id in entity_ids
        ]

# Usage in resolver
async def resolve_entities_with_relations(info: Info) -> List[YourEntity]:
    """Resolver that efficiently loads related data."""
    context = info.context

    # Use DataLoader for efficient batch loading
    loader = EntityLoader(context.db_session)
    entity_ids = await get_entity_ids_for_user(context.clerk_user_id)

    return await loader.load_entities(entity_ids)
```

### Pagination Implementation
```python
@strawberry.type
class EntityConnection:
    """Connection type for cursor-based pagination."""
    edges: List["EntityEdge"]
    page_info: "PageInfo"
    total_count: int

@strawberry.type
class EntityEdge:
    """Edge type for pagination."""
    node: YourEntity
    cursor: str

@strawberry.type
class PageInfo:
    """Page info for pagination."""
    has_next_page: bool
    has_previous_page: bool
    start_cursor: Optional[str] = None
    end_cursor: Optional[str] = None

async def resolve_entities_paginated(
    info: Info,
    first: Optional[int] = None,
    after: Optional[str] = None,
    last: Optional[int] = None,
    before: Optional[str] = None
) -> EntityConnection:
    """Resolver with proper cursor-based pagination."""
    # Implement cursor-based pagination logic
    # following GraphQL Cursor Connections Specification
    pass
```

## 🛡️ Error Handling Patterns

### Structured Error Responses
```python
@strawberry.type
class UserError:
    """Structured error type for user-facing errors."""
    field: Optional[str] = None
    message: str
    code: Optional[str] = None

@strawberry.type
class CreateEntityPayload:
    """Payload with structured error handling."""
    entity: Optional[YourEntity] = None
    errors: Optional[List[UserError]] = None
    success: bool = False

async def resolve_create_with_validation(
    info: Info, input: CreateYourEntityInput
) -> CreateEntityPayload:
    """Create resolver with comprehensive error handling."""
    try:
        # Validation
        validation_errors = []
        if not input.name or len(input.name.strip()) == 0:
            validation_errors.append(
                UserError(field="name", message="Name is required", code="REQUIRED")
            )

        if len(input.name) > 100:
            validation_errors.append(
                UserError(field="name", message="Name too long", code="MAX_LENGTH")
            )

        if validation_errors:
            return CreateEntityPayload(errors=validation_errors, success=False)

        # Business logic
        # ... entity creation logic ...

        return CreateEntityPayload(entity=created_entity, success=True)

    except ValueError as e:
        return CreateEntityPayload(
            errors=[UserError(message=str(e), code="VALIDATION_ERROR")],
            success=False
        )
    except Exception as e:
        logger.error(f"Unexpected error in create_entity: {str(e)}")
        return CreateEntityPayload(
            errors=[UserError(message="Internal server error", code="INTERNAL_ERROR")],
            success=False
        )
```

## 📁 File Organization

### Schema Files
Place GraphQL schemas in:
- `apps/backend/src/a2a_platform/api/graphql/schemas/your_entity_schemas.py`

### Resolver Files
Place resolvers in:
- `apps/backend/src/a2a_platform/api/graphql/resolvers/your_entity_resolvers.py`

### Integration
Add to main GraphQL module:
- `apps/backend/src/a2a_platform/api/graphql/__init__.py`

## 🔗 Related Files
Reference these project files for context:
- GraphQL Guidelines: `docs/graphql-guidelines.md`
- Existing Schemas: `apps/backend/src/a2a_platform/api/graphql/schemas/`
- Auth Middleware: `apps/backend/src/a2a_platform/api/graphql/middleware/auth_middleware.py`
- User Service: `apps/backend/src/a2a_platform/services/user_service.py`

## ✅ GraphQL Development Checklist

### Schema Design
- [ ] Use descriptive PascalCase for types (e.g., `UserProfile`, `OrderItem`)
- [ ] Use camelCase for fields and arguments (e.g., `firstName`, `productId`)
- [ ] Use UPPER_CASE for enum values (e.g., `PENDING`, `SHIPPED`)
- [ ] Provide clear descriptions for all types and fields
- [ ] Design granular queries to avoid over-fetching
- [ ] Implement proper input and payload types for mutations

### Resolver Implementation
- [ ] Use async/await patterns consistently
- [ ] Implement proper authentication checks
- [ ] Handle database sessions correctly via context
- [ ] Convert between Pydantic and GraphQL types properly
- [ ] Implement structured error handling
- [ ] Use camelCase naming for mutations (e.g., `updateMyProfile`)

### Performance & Security
- [ ] Implement pagination for list queries
- [ ] Use DataLoaders to prevent N+1 problems
- [ ] Validate all inputs rigorously
- [ ] Implement proper authorization checks
- [ ] Handle enum conversions between GraphQL and Python
- [ ] Log errors appropriately without exposing sensitive data

### Testing & Documentation
- [ ] Write unit tests for resolvers
- [ ] Test authentication and authorization flows
- [ ] Test error handling scenarios
- [ ] Document schema changes and deprecations
- [ ] Verify GraphQL schema introspection works correctly
