# Terraform Engineering Best Practices Prompt

## Overview
This prompt provides comprehensive guidelines for Terraform development, following industry best practices and the specific patterns established in this project.

## Core Principles

### 1. Infrastructure as Code (IaC) Standards
- **Declarative Configuration**: Write Terraform configurations that describe the desired end state
- **Version Control**: All Terraform code must be version controlled with meaningful commit messages
- **Immutable Infrastructure**: Prefer replacing resources over modifying them in place
- **State Management**: Use remote state backends with proper locking mechanisms

### 2. Code Organization and Structure

#### Module Design
```hcl
# Follow this module structure:
terraform/
├── modules/
│   ├── module-name/
│   │   ├── main.tf          # Primary resource definitions
│   │   ├── variables.tf     # Input variable declarations
│   │   ├── outputs.tf       # Output value declarations
│   │   ├── versions.tf      # Provider version constraints
│   │   └── README.md        # Module documentation
├── environments/
│   ├── staging/
│   ├── production/
│   └── shared/
└── shared/
    └── cache-variables.tf   # Shared variable definitions
```

#### File Naming Conventions
- Use lowercase with hyphens for file names: `gcs-static-hosting.tf`
- Group related resources in logical files: `security.tf`, `networking.tf`
- Use descriptive names that indicate purpose: `cloudflare-cdn.tf`

### 3. Variable and Resource Naming

#### Variables
```hcl
# Use descriptive names with consistent patterns
variable "environment" {
  description = "The deployment environment (staging, production)"
  type        = string
  validation {
    condition     = contains(["staging", "production"], var.environment)
    error_message = "Environment must be staging or production."
  }
}

# Use optional() for object attributes with defaults
variable "cache_config" {
  type = object({
    enabled = optional(bool, false)
    ttl     = optional(number, 3600)
  })
  default = {}
}
```

#### Resources
```hcl
# Use consistent naming: resource_type.descriptive_name
resource "google_storage_bucket" "web_assets" {
  name = "${var.environment}-web-assets"
}

# Use count/for_each for conditional resources
resource "cloudflare_zone" "main" {
  count = var.create_zone ? 1 : 0
  # ...
}
```

### 4. Security Best Practices

#### Sensitive Data Handling
```hcl
# Mark sensitive variables
variable "api_token" {
  type      = string
  sensitive = true
}

# Use data sources for existing resources when possible
data "google_storage_bucket" "existing" {
  count = var.use_existing_bucket ? 1 : 0
  name  = var.bucket_name
}
```

#### Access Control
```hcl
# Implement least privilege access
resource "google_storage_bucket_iam_member" "deployer_access" {
  bucket = local.bucket_name
  role   = "roles/storage.objectAdmin"  # Specific role, not owner
  member = "serviceAccount:${local.service_account_email}"
}

# Prevent public access explicitly
resource "google_storage_bucket" "web_assets" {
  public_access_prevention = "enforced"
  uniform_bucket_level_access = true
}
```

### 5. Conditional Logic and Flexibility

#### Safe Conditional Expressions
```hcl
# Use locals for complex conditional logic
locals {
  bucket_name = var.create_bucket ? google_storage_bucket.main[0].name : data.google_storage_bucket.existing[0].name

  # Ensure consistent types in conditionals
  zone_config = var.use_existing_zone ? {
    zone_id    = data.cloudflare_zone.existing[0].id
    zone_name  = data.cloudflare_zone.existing[0].name
    source     = "existing"
    # Ensure all branches have same attributes
    type       = null  # Not available from data source
  } : {
    zone_id    = cloudflare_zone.main[0].id
    zone_name  = cloudflare_zone.main[0].zone
    source     = "created"
    type       = cloudflare_zone.main[0].type
  }
}
```

#### Safe Attribute Access
```hcl
# Use lookup() for safe access to optional object attributes
resource "cloudflare_zone" "main" {
  jump_start = lookup(var.zone_config, "jump_start", false)
  paused     = lookup(var.zone_config, "paused", false)
  type       = lookup(var.zone_config, "type", "full")
}
```

### 6. Error Handling and Validation

#### Input Validation
```hcl
variable "environment" {
  validation {
    condition     = contains(["staging", "production"], var.environment)
    error_message = "Environment must be staging or production."
  }
}

variable "cloudflare_plan" {
  validation {
    condition     = contains(["free", "pro", "business"], var.cloudflare_plan)
    error_message = "Invalid Cloudflare plan specified."
  }
}
```

#### Graceful Degradation
```hcl
# Handle missing resources gracefully
resource "cloudflare_page_rule" "api_cache" {
  count = var.cache_config.api_cache_enabled ? 1 : 0
  # Configuration when caching is enabled
}

resource "cloudflare_page_rule" "api_bypass" {
  count = var.cache_config.api_cache_enabled ? 0 : 1
  # Configuration when caching is disabled
}
```

### 7. Documentation and Comments

#### Inline Documentation
```hcl
# Explain complex logic and business requirements
# trivy:ignore:AVD-GCP-0066 - Public access prevention enforced via separate setting
resource "google_storage_bucket" "web_assets" {
  # Explicitly prevent public access - critical security setting
  public_access_prevention = "enforced"
}

# Document conditional logic
locals {
  # Calculate domain names based on environment
  # Production uses root domain, others use subdomain
  web_domain = var.environment == "production" ? var.root_domain : "${var.environment}.${var.root_domain}"
}
```

#### Output Documentation
```hcl
output "zone_config" {
  description = "Complete zone configuration for observability and debugging - reflects actual deployed state"
  value = {
    zone_id = local.zone_id
    source  = var.use_existing_zone ? "existing" : "created"
  }
}
```

### 8. Testing and Validation

#### Pre-deployment Validation
- Always run `terraform validate` before applying changes
- Use `terraform plan` to review changes before applying
- Implement automated testing with tools like Terratest
- Use pre-commit hooks for consistent validation

#### State Management
```bash
# Use remote state with locking
terraform {
  backend "gcs" {
    bucket = "project-terraform-state"
    prefix = "environments/staging"
  }
}
```

### 9. Terraform Lock Files (.terraform.lock.hcl)

#### Understanding Lock Files
Terraform lock files ensure consistent provider versions across all team members and environments. They record the exact provider versions and checksums used during `terraform init`.

#### Lock File Best Practices

##### ✅ Always Commit Lock Files
```bash
# Lock files should be committed to version control
git add .terraform.lock.hcl
git commit -m "feat: add terraform lock file for consistent provider versions"
```

##### ✅ Lock File Location Strategy
```
# Each environment should have its own lock file
terraform/
├── environments/
│   ├── staging/
│   │   ├── main.tf
│   │   ├── terraform.tfvars
│   │   └── .terraform.lock.hcl     # ✅ Environment-specific lock
│   ├── production/
│   │   ├── main.tf
│   │   ├── terraform.tfvars
│   │   └── .terraform.lock.hcl     # ✅ Environment-specific lock
└── modules/
    ├── gcs-proxy/
    │   ├── main.tf
    │   ├── versions.tf
    │   └── .terraform.lock.hcl     # ✅ Module-specific lock (optional)
```

##### ✅ Provider Version Consistency
```hcl
# In versions.tf - Use consistent version constraints across modules
terraform {
  required_version = ">= 1.12.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 6.35.0"  # ✅ Consistent across all modules
    }
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 4.0"     # ✅ Consistent across all modules
    }
  }
}
```

##### ✅ Lock File Management Commands
```bash
# Initialize and create lock file
terraform init

# Upgrade providers and update lock file
terraform init -upgrade

# Verify lock file integrity
terraform providers lock

# Lock for multiple platforms (important for CI/CD)
terraform providers lock \
  -platform=linux_amd64 \
  -platform=darwin_amd64 \
  -platform=darwin_arm64
```

#### Lock File Workflow Patterns

##### Team Development Workflow
```bash
# 1. Developer adds new provider or changes version constraint
# 2. Run terraform init to update lock file
terraform init

# 3. Commit both the configuration change and lock file
git add versions.tf .terraform.lock.hcl
git commit -m "feat: upgrade google provider to v6.35.0"

# 4. Other team members pull and init to use same versions
git pull
terraform init  # Uses locked versions
```

##### CI/CD Integration
```yaml
# GitHub Actions workflow pattern
- name: Setup Terraform
  uses: hashicorp/setup-terraform@v3
  with:
    terraform_version: 1.12.0

- name: Terraform Init
  run: |
    cd terraform/environments/${{ matrix.environment }}
    # Lock file ensures consistent provider versions in CI
    terraform init

- name: Verify Provider Versions
  run: |
    cd terraform/environments/${{ matrix.environment }}
    # Verify we're using expected provider versions
    terraform providers
```

#### Lock File Security Considerations

##### ✅ Checksum Verification
```bash
# Lock files contain checksums for security
# Example .terraform.lock.hcl content:
provider "registry.terraform.io/hashicorp/google" {
  version     = "6.35.0"
  constraints = "~> 6.35.0"
  hashes = [
    "h1:abc123...",  # Multiple checksums for different platforms
    "h1:def456...",
    "zh:789xyz...",
  ]
}
```

##### ✅ Platform-Specific Locks
```bash
# Lock for all platforms your team uses
terraform providers lock \
  -platform=linux_amd64 \    # CI/CD runners
  -platform=darwin_amd64 \   # Intel Macs
  -platform=darwin_arm64 \   # Apple Silicon Macs
  -platform=windows_amd64    # Windows developers
```

#### Troubleshooting Lock File Issues

##### Provider Version Conflicts
```bash
# Problem: Different modules require incompatible provider versions
# Solution: Standardize provider versions across all modules

# Check current provider versions
terraform providers

# Update all modules to use consistent versions
# Update versions.tf in each module, then:
terraform init -upgrade
```

##### Missing Platform Checksums
```bash
# Problem: "could not retrieve providers for platform"
# Solution: Add missing platform checksums

terraform providers lock -platform=linux_amd64
git add .terraform.lock.hcl
git commit -m "fix: add linux_amd64 platform to terraform lock file"
```

##### Lock File Corruption
```bash
# Problem: Corrupted or invalid lock file
# Solution: Regenerate lock file

rm .terraform.lock.hcl
terraform init
git add .terraform.lock.hcl
git commit -m "fix: regenerate terraform lock file"
```

#### Environment-Specific Lock File Strategy

##### Staging Environment
```bash
# Staging can use newer provider versions for testing
# terraform/environments/staging/versions.tf
terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 6.35.0"  # Latest stable
    }
  }
}
```

##### Production Environment
```bash
# Production uses proven versions
# terraform/environments/production/versions.tf
terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "6.35.0"     # Exact version for stability
    }
  }
}
```

#### Lock File Anti-Patterns

##### ❌ Don't Do This
```bash
# Don't ignore lock files
echo ".terraform.lock.hcl" >> .gitignore  # ❌ Never ignore lock files

# Don't manually edit lock files
vim .terraform.lock.hcl  # ❌ Always use terraform commands

# Don't use different provider versions across environments without reason
# staging: google = "~> 6.35.0"
# production: google = "~> 5.0"  # ❌ Unnecessary version drift
```

##### ✅ Do This Instead
```bash
# Always commit lock files
git add .terraform.lock.hcl
git commit -m "feat: add terraform provider lock file"

# Use terraform commands to manage lock files
terraform init -upgrade  # ✅ Update providers and lock file
terraform providers lock  # ✅ Add platform checksums

# Keep provider versions consistent across environments
# Use same major.minor versions with appropriate constraints
```

#### Lock File Monitoring and Maintenance

##### Regular Updates
```bash
# Monthly provider update workflow
# 1. Check for provider updates
terraform init -upgrade

# 2. Test in staging first
cd terraform/environments/staging
terraform plan
terraform apply

# 3. Update production after validation
cd terraform/environments/production
terraform init -upgrade
terraform plan
terraform apply

# 4. Commit updated lock files
git add terraform/environments/*/terraform.lock.hcl
git commit -m "chore: update terraform providers to latest versions"
```

##### Security Scanning
```bash
# Use tools to scan for vulnerable provider versions
# Example with checkov
checkov -f .terraform.lock.hcl

# Example with trivy
trivy config terraform/
```

### 10. Performance and Optimization

#### Resource Dependencies
```hcl
# Use explicit dependencies when implicit ones aren't sufficient
resource "google_storage_bucket_iam_member" "access" {
  depends_on = [google_storage_bucket.main]
  # ...
}

# Prefer implicit dependencies through references
resource "cloudflare_record" "web" {
  zone_id = cloudflare_zone.main.id  # Implicit dependency
}
```

#### Efficient Resource Management
```hcl
# Use for_each for multiple similar resources
resource "cloudflare_record" "subdomains" {
  for_each = var.subdomains

  zone_id = local.zone_id
  name    = each.key
  content = each.value.target
}
```

### 11. Environment-Specific Patterns

#### Shared Configuration
```hcl
# Use shared variables for consistency
# terraform/shared/cache-variables.tf
variable "cache_config" {
  # Shared cache configuration definition
}
```

#### Environment Overrides
```hcl
# Allow environment-specific overrides
locals {
  effective_cache_config = merge(var.cache_config, {
    development_mode = var.environment == "staging" ? true : var.cache_config.development_mode
  })
}
```

## Common Anti-Patterns to Avoid

### ❌ Don't Do This
```hcl
# Hard-coded values
resource "google_storage_bucket" "web_assets" {
  name = "my-hardcoded-bucket-name"  # ❌ Not reusable
}

# Inconsistent conditional types
value = condition ? "string" : 123  # ❌ Type mismatch

# Missing validation
variable "environment" {
  type = string  # ❌ No validation
}

# Overly complex expressions
resource "example" "complex" {
  name = var.env == "prod" ? var.prod_name : var.env == "staging" ? var.staging_name : var.dev_name  # ❌ Hard to read
}
```

### ✅ Do This Instead
```hcl
# Parameterized values
resource "google_storage_bucket" "web_assets" {
  name = "${var.environment}-${var.project_name}-web-assets"  # ✅ Reusable
}

# Consistent conditional types
value = condition ? "string" : "default"  # ✅ Same type

# Proper validation
variable "environment" {
  type = string
  validation {
    condition     = contains(["staging", "production"], var.environment)
    error_message = "Environment must be staging or production."
  }
}

# Clear conditional logic
locals {
  bucket_name = {
    production = var.prod_name
    staging    = var.staging_name
    default    = var.dev_name
  }
}
resource "example" "clear" {
  name = lookup(local.bucket_name, var.environment, local.bucket_name.default)  # ✅ Clear and maintainable
}
```

## Checklist for Terraform Changes

- [ ] All variables have descriptions and appropriate types
- [ ] Sensitive variables are marked as `sensitive = true`
- [ ] Resources follow consistent naming conventions
- [ ] Conditional logic uses consistent types
- [ ] Complex logic is documented with comments
- [ ] Validation rules are implemented for critical variables
- [ ] Security best practices are followed
- [ ] `terraform validate` passes
- [ ] `terraform plan` output is reviewed
- [ ] Documentation is updated
- [ ] Lock files (.terraform.lock.hcl) are committed to version control
- [ ] Provider versions are consistent across environments
- [ ] Lock files include checksums for all required platforms
- [ ] Provider version constraints are appropriate (not too restrictive/permissive)

## Project-Specific Patterns

### A2A Platform Architecture
This project implements a multi-environment CDN architecture with the following components:

#### GCS Static Hosting Module
```hcl
# Conditional resource creation pattern
module "gcs_static_hosting" {
  source                 = "../../modules/gcs-static-hosting"
  create_bucket          = false  # Use existing bucket
  create_service_account = false  # Use existing service account
  bucket_name            = "a2a-platform-web-assets-staging"
  environment            = "staging"
}
```

#### Cloudflare CDN Module
```hcl
# Zone management with fallback pattern
module "cloudflare_cdn" {
  source            = "../../modules/cloudflare-cdn"
  cloudflare_zone_id = var.cloudflare_zone_id  # Use existing zone if provided
  zone_config = {
    jump_start = false
    paused     = false
    type       = "full"
  }
}
```

### Environment Variable Integration
```hcl
# Pattern for environment variable overrides
locals {
  effective_cache_config = merge(var.cache_config, {
    api_cache_enabled = var.cache_api_enabled != "" ? tobool(var.cache_api_enabled) : var.cache_config.api_cache_enabled
    development_mode  = var.cache_development_mode != "" ? tobool(var.cache_development_mode) : var.cache_config.development_mode
  })
}
```

## Advanced Patterns

### 1. Resource Import Handling
```hcl
# Pattern for handling existing resources that may need to be imported
variable "create_bucket" {
  description = "Whether to create the bucket or use an existing one"
  type        = bool
  default     = true
}

resource "google_storage_bucket" "web_assets" {
  count = var.create_bucket ? 1 : 0
  # Resource configuration
}

data "google_storage_bucket" "existing" {
  count = var.create_bucket ? 0 : 1
  name  = var.bucket_name
}

locals {
  bucket_name = var.create_bucket ? google_storage_bucket.web_assets[0].name : data.google_storage_bucket.existing[0].name
}
```

### 2. Multi-Provider Resource Coordination
```hcl
# Pattern for coordinating resources across providers
resource "google_storage_bucket" "web_assets" {
  name = var.bucket_name
}

resource "cloudflare_record" "web" {
  zone_id = local.zone_id
  name    = var.environment == "production" ? "@" : var.environment
  content = "storage.googleapis.com/${google_storage_bucket.web_assets.name}"
  type    = "CNAME"
  proxied = true
}
```

### 3. Dynamic Configuration Based on Environment
```hcl
# Pattern for environment-specific behavior
locals {
  # Different configurations per environment
  cors_origins = var.environment == "production" ? [
    "https://${var.root_domain}"
  ] : [
    "https://staging.${var.root_domain}",
    "https://${var.root_domain}",
    "http://localhost:3000"  # Development access
  ]

  # Environment-specific resource naming
  resource_prefix = var.environment == "production" ? var.root_domain : "${var.environment}-${var.root_domain}"
}
```

## Troubleshooting Common Issues

### Type Inconsistency in Conditionals
```hcl
# ❌ Problem: Inconsistent types
value = condition ? data.source.attr : resource.source.attr  # Different types

# ✅ Solution: Ensure consistent types
value = condition ? {
  id   = data.source.existing[0].id
  name = data.source.existing[0].name
  type = null  # Not available from data source
} : {
  id   = resource.source.main[0].id
  name = resource.source.main[0].name
  type = resource.source.main[0].type
}
```

### Resource Dependencies
```hcl
# ❌ Problem: Implicit dependency not working
resource "google_storage_bucket_iam_member" "access" {
  bucket = var.bucket_name  # May not exist yet
}

# ✅ Solution: Use resource reference
resource "google_storage_bucket_iam_member" "access" {
  bucket = google_storage_bucket.main.name  # Implicit dependency
}
```

### State Management Issues
```bash
# Import existing resources
terraform import module.gcs_static_hosting.google_storage_bucket.web_assets bucket-name
terraform import module.gcs_static_hosting.google_service_account.cdn_deployer projects/PROJECT_ID/serviceAccounts/EMAIL

# Handle state drift
terraform refresh
terraform plan  # Review differences
```

## CI/CD Integration

### GitHub Actions Workflow Pattern
```yaml
# Pattern for Terraform in CI/CD
- name: Terraform Plan
  run: |
    cd terraform/environments/${{ matrix.environment }}
    terraform plan \
      -var="cloudflare_zone_id=${{ secrets.CLOUDFLARE_ZONE_ID }}" \
      -var="project_id=${{ vars.GCP_PROJECT_ID }}" \
      -out=tfplan

- name: Terraform Apply
  if: github.ref == 'refs/heads/main'
  run: |
    cd terraform/environments/${{ matrix.environment }}
    terraform apply tfplan
```

### Environment-Specific Secrets
```yaml
# Use environment-specific configuration
environment: ${{ matrix.environment }}
env:
  TF_VAR_cloudflare_api_token: ${{ secrets.CLOUDFLARE_API_TOKEN }}
  TF_VAR_cloudflare_account_id: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
  GOOGLE_CREDENTIALS: ${{ secrets.GCP_SA_KEY }}
```

## Security Considerations

### Secrets Management
```hcl
# Use external secret management
data "google_secret_manager_secret_version" "api_key" {
  secret = "cloudflare-api-token"
}

# Avoid hardcoded secrets
variable "api_token" {
  type      = string
  sensitive = true
  # Never set default values for secrets
}
```

### Network Security
```hcl
# Implement proper network controls
resource "google_storage_bucket" "web_assets" {
  # Prevent public access
  public_access_prevention = "enforced"

  # Use IAM for access control
  uniform_bucket_level_access = true
}

# Restrict access to specific origins
resource "google_storage_bucket" "web_assets" {
  cors {
    origin = var.allowed_origins  # Controlled list
    method = ["GET", "HEAD", "OPTIONS"]  # Minimal required methods
  }
}
```

## Performance Optimization

### Resource Efficiency
```hcl
# Use data sources to avoid recreating existing resources
data "google_project" "current" {}

# Minimize provider calls with locals
locals {
  project_id = data.google_project.current.project_id
  region     = var.region

  # Compute once, use many times
  resource_labels = {
    environment = var.environment
    project     = local.project_id
    managed_by  = "terraform"
  }
}
```

### State File Optimization
```hcl
# Use targeted applies for large infrastructures
# terraform apply -target=module.specific_module

# Separate state files for different components
terraform {
  backend "gcs" {
    bucket = "terraform-state-bucket"
    prefix = "components/cdn"  # Component-specific state
  }
}
```

## Additional Resources

- [Terraform Best Practices Guide](https://www.terraform.io/docs/cloud/guides/recommended-practices/index.html)
- [Google Cloud Terraform Best Practices](https://cloud.google.com/docs/terraform/best-practices-for-terraform)
- [Cloudflare Terraform Provider Documentation](https://registry.terraform.io/providers/cloudflare/cloudflare/latest/docs)
- [Terraform Security Best Practices](https://blog.gruntwork.io/a-comprehensive-guide-to-terraform-b3d32832baca)
- [Infrastructure as Code Security](https://www.terraform.io/docs/cloud/sentinel/index.html)
