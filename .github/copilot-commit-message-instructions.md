# A2A Platform - Commit Message Guidelines

## 📝 Commit Message Format

Use conventional commit format with detailed descriptions:

```
<type>(<scope>): <description>

<body>

<footer>
```

## 🏷️ Commit Types

- **feat**: New feature implementation
- **fix**: Bug fixes and issue resolution
- **docs**: Documentation updates
- **style**: Code style changes (formatting, missing semicolons, etc.)
- **refactor**: Code refactoring without functionality changes
- **test**: Adding or updating tests
- **chore**: Maintenance tasks, dependency updates
- **ci**: CI/CD pipeline changes
- **perf**: Performance improvements
- **security**: Security-related changes

## 🎯 Scope Examples

- **backend**: Changes to apps/backend/
- **frontend**: Changes to apps/web/
- **docker**: Docker configuration changes
- **scripts**: Changes to ./scripts/
- **terraform**: Infrastructure changes
- **docs**: Documentation updates
- **specs**: Specification file changes
- **tests**: Test-related changes

## 🎨 Emoji Usage Guidelines

### Strategic Emoji Placement
- **🚀 feat**: New feature launches and capability expansions
- **🔧 fix**: Bug resolution and system stabilization
- **📚 docs**: Documentation and knowledge base enhancements
- **🎨 style**: Code aesthetics and formatting standardization
- **⚙️ refactor**: Architectural improvements without functionality changes
- **🧪 test**: Quality assurance and validation framework updates
- **🏗️ chore**: Infrastructure maintenance and dependency management
- **🤖 ci**: Pipeline automation and deployment orchestration
- **⚡ perf**: Performance optimization and efficiency gains
- **🔒 security**: Vulnerability mitigation and access control enhancements

### Contextual Enhancement Emojis
- **📁** File system operations
- **🔄** Process transformations
- **🎯** Targeted improvements
- **📊** Data and analytics
- **🔍** Investigation and debugging
- **🌐** Network and API operations
- **💾** Database and storage
- **🎪** User experience enhancements

## 📋 Description Guidelines

- **🎯 Information-dense vocabulary**: Use precise, technical terminology that conveys maximum meaning efficiently
- **🚀 Strategic emoji placement**: Include relevant emojis for visual categorization and quick recognition
- **📁 Granular file specificity**: Detail exact file changes and architectural reasoning
- **⚡ Imperative verb patterns**: Start with action verbs (add, fix, update, remove, refactor, implement, enhance)
- **🔍 Component impact mapping**: Specify affected services, modules, and integration points
- **🎪 Business value articulation**: Explain strategic necessity and user-facing benefits
- **🔗 Issue traceability**: Reference ticket numbers and related documentation

## 💡 Body Content

Include information-dense, high-fidelity descriptions:
- **🔄 What changed**: Granular file modifications with architectural context
- **🎯 Why changed**: Strategic business drivers or technical debt resolution
- **⚙️ How changed**: Implementation methodology and design patterns applied
- **📊 Impact radius**: Downstream effects on users, systems, and operational workflows
- **🧪 Validation strategy**: Comprehensive testing approach and quality assurance measures
- **🏗️ Architecture coherence**: How changes align with existing system design principles

## 🔗 Footer Examples

- `Fixes #123` - Closes an issue
- `Refs #456` - References related issue
- `Breaking-change: API endpoint removed` - Breaking changes
- `Co-authored-by: Name <email>` - Multiple authors

## ✅ Good Examples

```
🚀 feat(backend): implement deep-merge user preference orchestration

- 🏗️ Architect UserModel with PostgreSQL JSONB server_default='{}'
- ⚙️ Refactor UserService.update_preferences() with recursive merge algorithm
- 🧪 Comprehensive test coverage for edge-case merge scenarios
- 📊 Performance optimization for large preference object handling

Fixes #234
```

```
🔧 fix(frontend): resolve TypeScript module resolution infrastructure

- 🎯 Configure @lib/* path aliases in tsconfig.json baseUrl architecture
- 🔄 Refactor import statements across components/AgentCard.tsx ecosystem
- ⚡ Eliminate module resolution bottlenecks in lib/utils directory
- 🏗️ Ensure Vite build pipeline compatibility and bundling efficiency

Refs #567
```

```
📚 docs(platform): enhance commit message guidelines with emoji taxonomy

- 🎨 Integrate strategic emoji placement for visual categorization
- 📝 Promote information-dense vocabulary standards
- 🎯 Establish granular documentation patterns
- 🚀 Align with existing high-fidelity documentation framework

Enhancement request from development workflow optimization
```

## 🚫 Avoid

- ❌ Generic descriptions: "fix bug" or "update code" lack specificity
- ❌ Scope ambiguity: Missing architectural context for targeted changes
- ❌ Verbose single-line descriptions: Compress information density
- ❌ Missing rationale: Unexplained technical decisions reduce future maintainability
- ❌ Emoji overuse: Strategic placement over decorative abundance
- ❌ Low-fidelity vocabulary: Prefer technical precision over casual language
