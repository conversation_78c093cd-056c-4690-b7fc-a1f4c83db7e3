# 🤖 GitHub Copilot Custom Instructions Setup

This document explains the GitHub Copilot custom instructions configuration for the A2A Platform repository.

## 📁 Files Created

### Core Custom Instructions
- **`.github/copilot-instructions.md`** - Main repository custom instructions
- **`.github/copilot-commit-message-instructions.md`** - Commit message guidelines

### Prompt Files (VS Code)
- **`.github/prompts/README.md`** - Documentation for prompt files
- **`.github/prompts/new-react-component.prompt.md`** - React component generation
- **`.github/prompts/api-endpoint.prompt.md`** - FastAPI endpoint creation
- **`.github/prompts/graphql-schema.prompt.md`** - GraphQL schemas, resolvers, and mutations
- **`.github/prompts/database-migration.prompt.md`** - Alembic migration generation
- **`.github/prompts/test-generation.prompt.md`** - Comprehensive test creation
- **`.github/prompts/security-review.prompt.md`** - Security analysis and review
- **`.github/prompts/performance-optimization.prompt.md`** - Performance optimization guidance
- **`.github/prompts/refactoring-task.prompt.md`** - Systematic refactoring assistance

## 🎯 What These Instructions Provide

### Repository-Wide Context
- **Technology Stack**: Python 3.12, FastAPI, React 19, TypeScript, Apollo Client, Docker, PostgreSQL
- **Project Structure**: Apps organization, script locations, specification patterns
- **Development Workflow**: Testing scripts, Docker usage, environment management

### Coding Standards
- **Code Style**: Double quotes (frontend), TypeScript path aliases, proper error handling
- **Backend Patterns**: SQLAlchemy ORM usage, Pydantic models, async patterns
- **Frontend Patterns**: React 19 best practices, Apollo Client GraphQL, Tailwind CSS, Zustand state management
- **GraphQL Patterns**: Strawberry GraphQL, camelCase mutations, proper authentication
- **Security Guidelines**: Clerk authentication, input validation, CORS handling

### Testing & Quality
- **Testing Scripts**: Proper usage of `./scripts/run-backend-tests.sh` and `./scripts/run-frontend-tests.sh`
- **Test Patterns**: Unit tests, integration tests, e2e tests with Cypress
- **Quality Tools**: Pre-commit hooks, MyPy configuration, linting standards

## 🚀 How to Use

### For All Copilot Features
The main custom instructions in `.github/copilot-instructions.md` are automatically applied to:
- GitHub Copilot Chat in VS Code, Visual Studio, and GitHub.com
- GitHub Copilot coding agent when assigned to issues
- Code completion suggestions

### For VS Code Prompt Files
1. **Enable prompt files** in workspace settings:
   ```json
   {
     "chat.promptFiles": true
   }
   ```

2. **Use prompts** in Copilot Chat:
   - Click the "Attach context" icon (📎)
   - Select "Prompt..." from dropdown
   - Choose relevant prompt file

3. **Combine with context**:
   - Attach specific files or code snippets
   - Add additional instructions in chat
   - Reference project documentation

## 💡 Key Benefits

### Consistency
- Standardized coding patterns across the team
- Consistent commit message format
- Unified testing and deployment practices

### Efficiency
- Reduced need to repeat project context in prompts
- Pre-built templates for common development tasks
- Automated adherence to project standards

### Quality
- Built-in security review guidelines
- Performance optimization patterns
- Comprehensive testing strategies

## 🔄 Maintenance

### Updating Instructions
- Modify `.github/copilot-instructions.md` for repository-wide changes
- Update specific prompt files for task-specific improvements
- Keep instructions aligned with evolving project standards

### Adding New Prompts
- Create new `.prompt.md` files in `.github/prompts/`
- Follow the naming convention: `task-description.prompt.md`
- Include clear instructions and project-specific context
- Test prompts with various scenarios before committing

### Team Adoption
- Share effective prompt patterns with team members
- Document successful debugging and optimization workflows
- Iterate on instructions based on team feedback and usage patterns

## 📚 Related Documentation

- [LLM Instructions](../docs/llm-instructions.md) - Foundational development guidelines
- [GraphQL Guidelines](../docs/graphql-guidelines.md) - GraphQL development standards
- [Code Optimization](../docs/code-optimization.md) - Performance optimization strategies
- [Debugging Guide](../docs/debugging.md) - Structured debugging approaches
- [Refactoring Guide](../docs/refactoring.md) - Systematic refactoring patterns

## 🎉 Getting Started

1. **Verify Copilot access** in your development environment
2. **Enable custom instructions** in your Copilot settings
3. **Try a prompt file** for your next development task
4. **Provide feedback** on instruction effectiveness
5. **Contribute improvements** through pull requests

The custom instructions are designed to make GitHub Copilot more effective for A2A Platform development by providing rich context about our technology stack, coding standards, and development practices.
