# 🧪 QA: US7.1: Send Text Message to Personal Assistant via Chat Interface

**Goal:** 🎯 Verify that a user can successfully send a text message to their Personal Assistant via the chat interface, the message is persisted, and displayed in the chat history.

## 🎯 Objectives
**Functional:** ⚙️ Validate message sending, persistence, and UI display. GraphQL mutation `sendMessage` functionality. | **NFR:** 📏 Message send/display latency. | **Edge:** ⚠️ Empty messages, messages with special characters, network interruptions. | **Security:** 🔒 Ensure only authenticated users can send messages to their own PA conversations. | **DB:** 🗄️ Verify `chat_messages` table schema and data integrity. | **Integration:** 🔗 Frontend chat UI ↔ Backend GraphQL API ↔ Database.

## 📋 Strategy
**Scope:** 🎯 In scope: Sending text messages, UI update, backend persistence. Out of scope: Real-time updates from PA (covered in US7.3), complex message types (e.g., attachments). | **Env:** 🐳 Docker(default)/.env.test | CI(--ci flag) | **Backend:** 🖥️ apps/backend + ./scripts/run-backend-tests.sh + pytest + postgresql+asyncpg + DATABASE_URL(required) | **Frontend:** 🌐 bun + ./scripts/run-frontend-tests.sh + port:5173 + --e2e(Cypress) | **DB:** 🗄️ ./scripts/db-migrate.sh + upgrade/downgrade | **Tools:** 🛠️ pytest/bun/Docker/Alembic/Cypress/pgAdmin or similar DB client | **Deps:** 🔗 User authenticated, conversation with PA exists. US2.1, US2.3. | **Data:** 📊 Test user accounts, pre-existing conversation IDs. | **Risks:** ⚠️ Optimistic UI updates not matching backend state, race conditions if US7.3 is partially implemented.

## 🎬 Context
**Before:** Features:⚙️User authentication, basic chat interface shell. Env:🔧Backend and frontend services running, DB accessible. Data:📊Test user exists and is logged in. A conversation between the test user and their PA exists in the `conversations` table. | [None]
**After:** Deliverables:📦QA test execution logs. Reports:📄Bug reports for any issues found. Defects:🐛Tracked in issue tracker. | [None]

## 📝 Tasks
1. **Setup Test Environment & Data** - Ensure all services are running and necessary test data (user, PA, conversation) is in place.
```test_execution
**Do:** 🎯 Start Docker containers (backend, frontend, DB). Verify test user and conversation exist. | **Type:** 🧪 Environment | **Cmd:** 💻 `docker compose -f docker-compose.dev.yml up -d`, SQL queries to verify data. | **Assert:** ✅ Services running, user and conversation present. | **Data:** 📊 Test user credentials, known `conversation_id`. | **Result:** 🎯 Environment ready for testing.
```
2. **Execute Frontend Tests (Unit/Integration)** - Run Jest tests for chat input component and GraphQL service.
```test_execution
**Do:** 🎯 Run frontend unit and integration tests related to message sending. | **Type:** 🧪 Unit/Integration | **Cmd:** 💻 `cd apps/frontend && bun test` (or specific test files) | **Assert:** ✅ All relevant tests pass. | **Data:** 📊 Mocked GraphQL responses. | **Result:** 🎯 Frontend components for sending messages are functioning as expected in isolation.
```
3. **Execute Backend Tests (Unit/Integration)** - Run Pytest tests for `sendMessage` resolver and DB interactions.
```test_execution
**Do:** 🎯 Run backend unit and integration tests for the `sendMessage` mutation. | **Type:** 🧪 Unit/Integration | **Cmd:** 💻 `./scripts/run-backend-tests.sh` (or specific test files like `test_chat_mutations.py`) | **Assert:** ✅ All relevant tests pass. | **Data:** 📊 Mocked DB calls, sample message payloads. | **Result:** 🎯 Backend logic for message persistence and GraphQL resolution is correct.
```
4. **Execute E2E Tests (Cypress)** - Run Cypress tests for the end-to-end message sending flow.
```test_execution
**Do:** 🎯 Run Cypress E2E tests covering the scenario of a user typing and sending a message. | **Type:** 🧪 E2E | **Cmd:** 💻 `cd apps/frontend && bun run cypress:run` (or specific spec file) | **Assert:** ✅ User can send message, it appears in UI, and (if checked by test) is in DB. | **Data:** 📊 Test user credentials. | **Result:** 🎯 The full user flow for sending a message is working correctly.
```

## 📋 Test Cases

### 🧪 Functional: Send Message
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-TC-1 | Send a valid text message | 1. Login as test user. 2. Navigate to PA chat. 3. Type "Hello PA" in input. 4. Click Send. | Message "Hello PA" appears in chat history. GraphQL `sendMessage` mutation called. Message persisted in `chat_messages` with correct `conversation_id`, `user_id`, `content`, `sender_role='user'`. | API/DB/UI |
| US7.1-TC-2 | Send a message with special characters | 1. Login. 2. Go to chat. 3. Type "Test message with !@#$%^&*()_+" 4. Send. | Message appears correctly in UI and DB, special characters preserved. | API/DB/UI |
| US7.1-TC-3 | Send multiple messages sequentially | 1. Login. 2. Go to chat. 3. Send "Msg1". 4. Send "Msg2". | Both messages appear in UI in correct order. Both persisted in DB. | API/DB/UI |
| US7.1-TC-4 | Send a long message | 1. Login. 2. Go to chat. 3. Type a message of 500 characters. 4. Send. | Message appears correctly in UI and DB. (Assuming no hard limit below 500 chars). | API/DB/UI |

### ❌ Negative & Error
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-ERR-1 | Attempt to send an empty message | 1. Login. 2. Go to chat. 3. Click Send without typing. | UI should prevent sending (e.g., button disabled or error message). No API call made. | UI |
| US7.1-ERR-2 | Attempt to send message with network offline (frontend) | 1. Login. 2. Go to chat. 3. Type message. 4. Simulate network offline. 5. Click Send. | UI shows an error message. Message does not appear as sent. (Optimistic update might revert). | UI/API |
| US7.1-ERR-3 | Backend `sendMessage` mutation fails (e.g., DB error) | 1. (Simulate backend error for `sendMessage`). 2. User sends message. | UI shows an error. Message not persisted. | API/UI |

### 📋 Contract & External
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-CT-1 | Verify `sendMessage` GraphQL mutation request | 1. Intercept frontend API call. 2. Send a message. | Request payload matches `sendMessage(conversationId: ID!, content: String!)` schema. `conversationId` and `content` are correct. | API Contract |
| US7.1-CT-2 | Verify `sendMessage` GraphQL mutation response | 1. Intercept backend API response. 2. Send a message. | Response payload matches `Message { id, content, sender, timestamp }` schema. | API Contract |

### ⚙️ Environment & Config
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-ENV-1 | Verify message persistence in DB | 1. Send a message via UI. 2. Query `chat_messages` table. | Record exists with correct `conversation_id`, `user_id`, `content`, `sender_role='user'`, and `created_at` timestamp. | DB |

## 📊 Coverage
**Functional:** ⚙️ Core `sendMessage` workflow, message content variations, UI display, DB persistence. | **NFR:** 📏 Manual observation of send/display speed. | **Edge:** ⚠️ Empty message, network issues (simulated).

## 🔧 Setup
**Backend:** 🖥️ `apps/backend` + `./scripts/run-backend-tests.sh` + `pytest` + `postgresql+asyncpg` + `DATABASE_URL`(required) | **Frontend:** 🌐 `bun` + `./scripts/run-frontend-tests.sh` + port:5173 + dev mode (`bun run dev -- --host 0.0.0.0`) | **DB:** 🗄️ `./scripts/db-migrate.sh` + Docker Compose env vars + `upgrade head`.

## 📦 Data
**Env Vars:** ⚙️ `DATABASE_URL` (e.g., `postgresql+asyncpg://test_user:test_password@localhost:5432/test_database`), `CLERK_JWT_PUBLIC_KEY`. Use `.env.test` for local Docker testing. | **Config:** 🔧 `.env.test` for test-specific settings. Docker for service orchestration. `--ci` flag for CI-specific DB connection if different.

## 🎭 Scenarios

### 🎬 Scenario: Successful Message Sending
**TC US7.1-TC-1:** Send a valid text message
- **Given:** 📋 User is logged in and on the chat page with their PA. The chat input field is visible and enabled.
- **When:** ⚡ User types "Hello PA, how are you?" into the input field AND clicks the "Send" button.
- **Then:** ✅ The message "Hello PA, how are you?" appears in the user's chat history in the UI. AND The `sendMessage` GraphQL mutation is called with the correct `conversationId` and message content.
- **And:** ➕ A new record is created in the `chat_messages` table in the database with `content` = "Hello PA, how are you?", the correct `conversation_id`, `user_id`, and `sender_role` = 'user'.

### 🎬 Scenario: Attempt to Send Empty Message
**TC US7.1-ERR-1:** Attempt to send an empty message
- **Given:** 📋 User is logged in and on the chat page with their PA. The chat input field is empty.
- **When:** ⚡ User clicks the "Send" button.
- **Then:** ✅ The UI either disables the send button OR displays a message indicating an empty message cannot be sent. AND No GraphQL mutation is called.
- **And:** ➕ No new record is created in the `chat_messages` table.

## 🤖 Automation
**Pre-commit:** 🔧 MyPy parity + auto-fixes + `SKIP=pytest-backend` (if needed for speed) + Ruff. | **CI/CD:** 🚀 `postgres://test_user:test_password@localhost:5432/test_database` + Docker/CI parity + `apps/backend` + GitHub Actions. | **Env Mgmt:** 🐳 Docker(default) + `.env.test` + `--ci` flag + idempotent DB scripts (`db-migrate.sh`).

## 📦 Deliverables
📄 QA spec + execution log (manual and automated) + bug reports + sign-off report.

## ⚠️ Risks
- Optimistic UI updates might lead to perceived success even if backend fails; requires robust error handling and potential rollback/indicator in UI.
- Dependency on US7.3 for real-time display from PA; current tests only verify user message sending and immediate display.
