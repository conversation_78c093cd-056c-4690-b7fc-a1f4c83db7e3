# 🧪 QA Testing Specification: US7.1 Send Text Message to Personal Assistant via Chat Interface

**Goal:** 🎯 Verify that users can successfully send text messages to their Personal Assistant via the chat interface with proper validation, persistence, and UI feedback.

## 🎯 Objectives
**Functional:** ⚙️ Validate message transmission, persistence, and display | **NFR:** 📏 Verify performance of message sending | **Edge:** ⚠️ Test error conditions and input validation | **Security:** 🔒 Confirm proper authorization checks | **DB:** 🗄️ Verify correct database records | **Integration:** 🔗 Test frontend-to-backend communication flow

## 📋 Strategy
**Scope:** 🎯 Frontend chat input, GraphQL sendMessage mutation, message persistence, UI updates | **Env:** 🐳 Docker(default)/.env.test | CI(--ci flag) | **Backend:** 🖥️ apps/backend + ./scripts/run-backend-tests.sh + pytest + postgresql+asyncpg + DATABASE_URL(required) | **Frontend:** 🌐 bun + ./scripts/run-frontend-tests.sh + port:5173 + --e2e(Cypress) | **DB:** 🗄️ ./scripts/db-migrate.sh + upgrade/downgrade | **Tools:** 🛠️ pytest/bun/Docker/Apollo Client Test Tools | **Deps:** 🔗 Existing conversation, authenticated user session | **Data:** 📊 Test messages of various lengths and content types | **Risks:** ⚠️ Race conditions in real-time updates

## 🎬 Context
**Before:** Features:⚙️ Authentication, conversation creation | Env:🔧 Clean test database with pre-created user and conversation | Data:📊 Test user account, sample conversation records
**After:** Deliverables:📦 Test execution reports, code coverage | Reports:📄 Test validation documentation | Defects:🐛 Any identified issues tracked in GitHub

## 📝 Tasks
1. **Setup Test Environment** - Prepare database and authentication
```test_execution
**Do:** 🎯 Initialize test database with users and conversation | **Type:** 🧪 Infrastructure | **Cmd:** 💻 ./scripts/db-reset.sh; ./scripts/db-migrate.sh upgrade | **Assert:** ✅ Database ready with test data | **Data:** 📊 Test user, conversation fixtures | **Result:** 🎯 Clean test environment ready
```

2. **Run Backend Unit Tests** - Test resolver logic
```test_execution
**Do:** 🎯 Run backend unit tests for sendMessage resolver | **Type:** 🧪 Unit | **Cmd:** 💻 cd apps/backend && python -m pytest tests/unit/resolvers/test_chat_resolvers.py -v | **Assert:** ✅ All tests pass | **Data:** 📊 Test fixtures | **Result:** 🎯 Resolver functions correctly
```

3. **Run Backend Integration Tests** - Test GraphQL API
```test_execution
**Do:** 🎯 Run GraphQL API integration tests | **Type:** 🧪 Integration | **Cmd:** 💻 cd apps/backend && python -m pytest tests/integration/test_graphql_chat.py -v | **Assert:** ✅ GraphQL API correctly processes mutations | **Data:** 📊 Test requests | **Result:** 🎯 API behaves as expected
```

4. **Run Frontend Component Tests** - Test chat input component
```test_execution
**Do:** 🎯 Run frontend tests for ChatInput component | **Type:** 🧪 Unit | **Cmd:** 💻 cd apps/web && bun test src/components/chat/ChatInput.test.tsx | **Assert:** ✅ Component renders and functions correctly | **Data:** 📊 Mock Apollo client responses | **Result:** 🎯 UI component behaves as expected
```

5. **Run E2E Tests** - Test complete user flow
```test_execution
**Do:** 🎯 Run end-to-end tests for message sending flow | **Type:** 🧪 E2E | **Cmd:** 💻 cd apps/web && bun run test:e2e | **Assert:** ✅ User can send message and see it in UI | **Data:** 📊 Test user credentials | **Result:** 🎯 Complete flow works end-to-end
```

## 📋 Test Cases

### 🧪 Message Sending: Basic Functionality
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-TC-1 | Send valid text message | 1. Navigate to chat page<br>2. Type message in input<br>3. Click send button | Message appears in chat history and is persisted in database | API/UI |
| US7.1-TC-2 | Send empty message | 1. Navigate to chat page<br>2. Leave input field empty<br>3. Click send button | Send button disabled or validation error shown | UI |
| US7.1-TC-3 | Send very long message | 1. Navigate to chat page<br>2. Type message with 5000 characters<br>3. Click send button | Message is sent successfully and displayed correctly | API/UI |
| US7.1-TC-4 | Send message with special characters | 1. Navigate to chat page<br>2. Type message with emojis, Unicode, and special symbols<br>3. Click send button | Message sent with all characters preserved | API/UI |

### ❌ Negative & Error
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-ERR-1 | Send to nonexistent conversation | 1. Prepare API call with invalid conversation ID<br>2. Send mutation request | Error response with appropriate message | Negative |
| US7.1-ERR-2 | Send message while unauthenticated | 1. Log out user<br>2. Attempt to send message | Authentication error, redirect to login | Security |
| US7.1-ERR-3 | Send message to another user's conversation | 1. Prepare API call with conversation ID not owned by user<br>2. Send mutation request | Authorization error with appropriate message | Security |
| US7.1-ERR-4 | Send during network disruption | 1. Navigate to chat page<br>2. Type message<br>3. Disable network<br>4. Click send | Error message shown, retry mechanism available | Negative |

### 📋 Contract & External
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-CT-1 | GraphQL mutation schema test | 1. Verify response schema<br>2. Check error schema | Matches documented schema for ChatMessage type | Contract |
| US7.1-CT-2 | Message content structure | 1. Send various content structures<br>2. Verify parsing | Content structure preserved in database and API responses | Contract |

### ⚙️ Environment & Config
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-ENV-1 | Test in Docker environment | 1. Start with docker compose<br>2. Run tests | All tests pass in isolated Docker environment | Environment |
| US7.1-ENV-2 | Test in CI environment | 1. Run with --ci flag<br>2. Check results | Tests pass in CI configuration | Environment |

## 📊 Coverage
**Functional:** ⚙️ Message input, sending, persistence, display | **NFR:** 📏 Message sending performance, input validation | **Edge:** ⚠️ Network errors, validation failures, authorization edge cases

## 🔧 Setup
**Backend:** 🖥️ apps/backend + ./scripts/run-backend-tests.sh + pytest + postgresql+asyncpg + DATABASE_URL(required) | **Frontend:** 🌐 bun + ./scripts/run-frontend-tests.sh + port:5173 + dev mode | **DB:** 🗄️ ./scripts/db-migrate.sh + Docker Compose env vars + pre-populated test conversations

## 📦 Data
**Env Vars:** ⚙️ DATABASE_URL + POSTGRES_* + CLERK_JWT_PUBLIC_KEY + .env/.env.test | **Config:** 🔧 .env.test + Docker + --ci flags

## 🎭 Scenarios

### 🎬 Happy Path: User Sends Message
**TC US7.1-TC-1:** Send valid text message
- **Given:** 📋 User is authenticated and viewing chat interface with existing conversation | **When:** ⚡ User types "Hello, Assistant" and clicks send | **Then:** ✅ Message appears in chat history | **And:** ➕ Database contains new message record

### 🎬 Error Case: Authorization Failure
**TC US7.1-ERR-3:** Send message to another user's conversation
- **Given:** 📋 User is authenticated but trying to access conversation not owned by them | **When:** ⚡ User attempts to send message to unauthorized conversation | **Then:** ✅ Server returns 403 error | **And:** ➕ Error message displayed to user | **And:** ➕ No message record created in database

### 🎬 Edge Case: Network Resilience
**TC US7.1-ERR-4:** Send during network disruption
- **Given:** 📋 User has composed message but network connection is lost | **When:** ⚡ User attempts to send message | **Then:** ✅ User sees network error notification | **And:** ➕ UI provides retry option | **And:** ➕ Message is preserved in input field

## 🤖 Automation
**Pre-commit:** 🔧 MyPy parity + auto-fixes + SKIP=pytest-backend + flags + Ruff | **CI/CD:** 🚀 postgres://test_user:test_password@localhost:5432/test_database + Docker/CI parity + apps/backend + GitHub Actions | **Env Mgmt:** 🐳 Docker(default) + .env.test + --ci flag + idempotent DB scripts

## 📦 Deliverables
📄 Test results, coverage report, performance metrics (message send latency)

## ⚠️ Risks
**Risk 1:** 🚨 Race conditions in UI updates when messages are sent rapidly → Test with rapid sequential message sending
**Risk 2:** 🚨 Memory/performance issues with large message history → Test with large number of accumulated messages
