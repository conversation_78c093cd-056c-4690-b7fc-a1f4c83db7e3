# 📋 User Story Specification: US7.1

**Story:** As a user, I want to send a text message to my Personal Assistant via a chat interface so that I can communicate my requests and information.
**Steps:** User types message → User clicks send → Message appears in UI → Message saved in DB
**Epic:** 🎯 Core Conversational Interface & Interaction

## ✅ AC
- [ ] Given I am in the chat interface with my <PERSON>, When I type a message (e.g., "Hello world!") in the input field and send it, Then the message is transmitted to the backend via a GraphQL mutation `sendMessage` (type: `sendMessage(conversationId: UUID!, content: JSONString!) : ChatMessage!`, where `content` is e.g., `"{\"text\":\"Hello world!\"}"`). [Source 1259, 1298, ADR-002 Sec 3.2]
- [ ] Given the backend receives the message from an authenticated user, When the `sendMessage` resolver is invoked, Then the message is persisted in the `chat_messages` table (columns: `id`, `conversation_id`, `sender_role` (SQLAlchemy Enum: `USER` or `ASSISTANT`), `content` (JSONB: `{"text": "message"}`), `created_at`, `updated_at`) associated with the correct `conversation_id`, and `sender_role` is `USER`. [Source 1307, ADR-002 Sec 4.1]
- [ ] Given the `sendMessage` mutation is successful, When the UI receives the confirmation, Then the sent message (content and sender) appears in my chat history in the UI optimistically.
- [ ] Given an error occurs during message transmission (e.g., network issue, server error), When I attempt to send a message, Then the UI displays an appropriate error notification (e.g., "Message failed to send. Please try again.") and the message input field retains the unsent message.
- [ ] Given I attempt to send an empty message (e.g., only whitespace), When I click send, Then the UI prevents the message from being sent and displays a hint (e.g., "Cannot send an empty message.").
- [ ] Given I attempt to send a message exceeding the maximum length (e.g., 10,000 characters, defined as `MAX_MESSAGE_LENGTH` constant), When I click send, Then the UI prevents the message from being sent and displays a hint (e.g., "Message exceeds maximum length.").

## 🔗 Deps
**Stories:** US7.0 (Basic Chat UI - COMPLETED) | US7.3 (Real-time message display - PENDING) | **Ext:** 🌐 Clerk.com (Authentication - ACTIVE) @vX.Y.Z | **Tech:** ⚙️ Strawberry GraphQL, Apollo Client, Zustand, SQLAlchemy | **Data:** 🗄️ `chat_messages`, `conversations` schemas (ADR-002 Sec 4.1) | **Team:** 👥 [UX Team for UI feedback, Backend team for API integration]

## 💰 Value
**Biz:** 📈 Enables core product functionality, driving user engagement and task completion. | **UX:** ✨ Provides a primary communication channel, intuitive interaction model. | **KPI:** 📊 Daily Active Users (DAU), Messages Sent per User, Task Completion Rate. | **Why:** 🎯 Critical path for user-PA interaction; fundamental to product value.

## 📐 ADRs
**Refs:** 📄 ADR-001 (System Architecture) | 📄 ADR-002 (Backend Overview - Sec 3.2 GraphQL, Sec 4.1 Data Models, Sec 5.1 Security) | 📄 ADR-003 (Frontend Overview - Sec 2.1 UI Components, Sec 2.3 State Management) | **Limits:** ⚠️ Message size limit (10k chars via `MAX_MESSAGE_LENGTH` constant), rate limiting (future consideration, see USX.X). | **Patterns:** 🏗️ Optimistic UI updates, GraphQL mutations for CQS.

## 🛠️ Impl
**Arch:** 🏛️ Frontend (React/TS/Apollo/Zustand) <-> GraphQL API (Python/Strawberry/SQLAlchemy) <-> PostgreSQL DB (ADR-001, ADR-002, ADR-003) | **Data:** 🗄️ `chat_messages` (new fields: `id` UUID PK, `conversation_id` UUID FK (indexed), `sender_role` sqlalchemy.Enum('USER', 'ASSISTANT', name='sender_role_enum'), `content` JSONB, `created_at` TIMESTAMPTZ (indexed), `updated_at` TIMESTAMPTZ). `conversations` (existing, links `user_id`, `assistant_id`) (ADR-002, Sec 4.1). | **API:** 🔌 GraphQL Enum `SenderRole { USER, ASSISTANT }`. GraphQL Mutation: `sendMessage(conversationId: UUID!, content: JSONString!) : ChatMessage!`. `ChatMessage` type: `{ id: UUID!, conversationId: UUID!, senderRole: SenderRole!, content: JSONString!, createdAt: DateTime!, updatedAt: DateTime! }`. `JSONString` for content is `{"text": "actual message string"}`. (ADR-002 Sec 3.2) | **Sec:** 🔒 Authentication via Clerk JWT. Authorization: User can only send messages to conversations they are part of (verified in resolver via `conversation_id` and `user_id` from JWT). Input sanitization for `content.text` against XSS using an appropriate library (e.g., `bleach`). (ADR-002 Sec 5.1) | **Perf:** ⚡ Message persistence < 100ms. UI update < 50ms after send. | **Int:** 🌐 [None for this specific story beyond existing Clerk auth] | **Err:** ❌ GraphQL errors for failed mutations (e.g., auth error, validation error, server error). UI error states for network/validation issues. Log errors to backend monitoring system. | **Std:** 📏 Linting (ESLint, Ruff), Formatting (Prettier, Black), Typing (TypeScript, MyPy). `MAX_MESSAGE_LENGTH` defined as a constant in the relevant backend/frontend modules.

## 🧪 Test
**Unit:** 🔬 Frontend: Send button handler, input validation (empty, max length), optimistic update logic (Zustand store actions and reducers). Backend: `sendMessage` resolver logic (authN, authZ, validation, DB persistence), `ChatMessage` model methods if any. | **Int:** 🔗 Frontend <-> Backend: `sendMessage` mutation call, successful response, and error responses. Backend <-> DB: Message saving, retrieval, and constraint checks. | **Contract:** 📋 GraphQL `sendMessage` mutation signature, `ChatMessage` type, and `SenderRole` enum. | **E2E:** 🎭 User logs in, opens chat, sends message, verifies message appears in UI and is stored in DB with correct `sender_role` and `content`. Test error states (empty message, max length, unauthorized send attempt). | **Perf:** 🚀 Load test `sendMessage` mutation (e.g., 100 msgs/sec target). Measure API response time under load. | **Sec:** 🛡️ Test XSS prevention on message content (ensure sanitization works). Test authN: unauthorized user cannot send messages. Test authZ: user cannot send messages to conversations they don't belong to.

## 📏 NFRs
**Perf:** ⚡ API response for `sendMessage` < 200ms (p95). Client-side optimistic update perceived < 50ms. | **A11y:** ♿ Chat input accessible via keyboard (WCAG 2.1 Tab, Enter key usage). Screen reader compatible: input field labelled, send button announced, error messages clearly announced. | **Sec:** 🔒 Secure transmission (HTTPS). Input validation (length, format, XSS). User authentication enforced. Authorization correctly scopes access. | **Scale:** 📈 System handles 100 messages/second peak load for `sendMessage` mutation. Database connections and resolver performance accommodate this. | **Compat:** 🌍 Latest 2 versions of Chrome, Firefox, Safari, Edge.

## ✅ DoD
- [ ] AC met | [ ] Tests pass (Unit >90%, Int >80%, E2E critical paths including error cases) | [ ] Security review (XSS, AuthZ, input validation checks by peer) | [ ] Perf benchmarks (API <200ms p95 under defined load) | [ ] Docs updated (API docs for `sendMessage`, `ChatMessage`, `SenderRole`) | [ ] Code review (Frontend, Backend by at least one peer each) | [ ] QA complete (Manual exploratory testing of key flows and error cases)

## ⚠️ Risk
**Tech:** 💻 Potential issues with WebSocket setup for US7.3 (real-time) impacting immediate display → Mitigation: Fallback to polling if US7.3 delayed, clearly communicate this limitation. Optimistic UI update complexity → Mitigation: Thorough testing of optimistic update edge cases and rollback mechanisms; simplify logic if necessary. | **Biz:** 📊 Delay in core feature impacts user adoption → Mitigation: Prioritize and resource accordingly; phased rollout if full feature set is delayed. | **Time:** ⏰ Learning curve for new team members on GraphQL/Zustand → Mitigation: Pair programming, dedicated learning time, comprehensive documentation. | **Deps:** 🔗 US7.3 delay impacts full UX → Mitigation: Plan for non-real-time display initially; ensure current story provides standalone value.

## 📊 Est
**Pts:** 🎯 5 (Medium complexity: involves FE, BE, DB changes, new API endpoint, optimistic updates) | **Hrs:** ⏱️ Dev: 24h (FE: 10h, BE: 14h), Test: 8h (Unit, Int, E2E scripting), Review: 4h = Total 36h | **Complex:** 🧩 Optimistic UI state management, robust error handling across FE/BE, AuthZ logic in resolver. | **Conf:** 📈 Medium (Well-defined patterns but integration points and optimistic logic require care). | **Vars:** 🔄 Availability and stability of US7.0 UI components; final schema details for `chat_messages` confirmed by DB admin/team.

## 📦 Data
**API:** 🔌 Req: `sendMessage(conversationId: "valid-uuid", content: "{\"text\":\"Hello there!\"}")`. Resp (Success): `{ "data": { "sendMessage": { "id": "new-uuid", "conversationId": "valid-uuid", "senderRole": "USER", "content": "{\"text\":\"Hello there!\"}", "createdAt": "iso-timestamp", "updatedAt": "iso-timestamp" } } }`. Resp (Error examples): `{ "errors": [{ "message": "Unauthorized", "extensions": { "code": "AUTH_NOT_AUTHENTICATED" } }] }`, `{ "errors": [{ "message": "Message content cannot be empty.", "extensions": { "code": "VALIDATION_ERROR" } }] }` | **DB:** 🗄️ `chat_messages`: `{id: uuid, conversation_id: uuid, sender_role: 'USER', content: '{"text": "Hello there!"}', created_at: timestamptz, updated_at: timestamptz}`. `conversations`: `{id: uuid, user_id: uuid, assistant_id: uuid, ...}` | **UI:** 🖥️ Zustand store: `chatMessages: [{id: string, conversationId: string, senderRole: 'USER' | 'ASSISTANT', content: {text: string}, timestamp: number, status: 'PENDING' | 'SENT' | 'ERROR'}]`. Input field state: `string`. Error message state: `string | null`. | **Config:** ⚙️ [No new environment variables for this story; `MAX_MESSAGE_LENGTH` is a code constant.]

## 🎨 Visual
**Layout:** 📐
```
+---------------------------------------------------+
| Chat with [PA Name]                               |
+---------------------------------------------------+
| [Previous messages scrollable area]               |
| User: Hi there!                                   |
| PA: Hello! How can I help?                        |
| User: Can you help me with X?                     |
|                                                   |
+---------------------------------------------------+
| [Message Input Field (type here...)] [Send Button] |
| [Optional: Error message displayed here]          |
+---------------------------------------------------+
```
| **Flow:** 🌊 User types message -> Clicks Send -> UI optimistic update (message appears as 'PENDING') & `useSendMessage` hook called -> GQL Mutation sent -> Backend Resolver (AuthN/AuthZ, Validate, Persist) -> DB Persist `chat_message` -> GQL Response -> UI updates message status (SENT/ERROR), displays error if any. | **States:** 🔄 Message in UI: PENDING -> SENT | ERROR. Input field: Active -> Disabled (during send) -> Active. Send button: Enabled -> Disabled (during send) -> Enabled. | **Seq:** 📋 User focuses input -> Types message -> Clicks Send -> Message added to UI list (status: PENDING) -> `sendMessage` GQL mutation sent -> On success: message status in UI changes to SENT -> On failure: message status changes to ERROR, error notification shown.

## 🤔 Assume
**Sys:** 🖥️ PostgreSQL DB is running and accessible with `a2a_platform` schema. Clerk auth service is operational and configured. GraphQL endpoint is `/graphql`. `conversations` table exists and has relevant entries linking users to PAs. | **User:** 👤 User is authenticated via Clerk JWT. User has an existing conversation (`conversation_id`) with a PA. User understands basic chat interface paradigms. | **Env:** 🌐 Stable internet connection for message sending. Modern browser (as per Compat NFRs) with JavaScript enabled. | **Data:** 📊 `conversation_id` provided to the UI component is valid and belongs to the authenticated user. Message `content.text` is UTF-8 encoded.

## 🎬 Context
**Before:** Files:📁`apps/frontend/src/components/ChatView.tsx` (basic structure from US7.0), `apps/backend/src/a2a_platform/graphql/schema.py` (no `sendMessage` mutation or `ChatMessage` type). | Schema:🗄️ `conversations` table exists. No `chat_messages` table. | Components:⚙️ Basic UI chat container from US7.0. Backend GraphQL server running with basic queries. | Config:⚙️ Standard env vars for DB (`DATABASE_URL`), Clerk (`CLERK_API_KEY`).
**After:** Files:📁`apps/frontend/src/features/chat/SendMessageForm.tsx` (new), `apps/frontend/src/hooks/useSendMessage.ts` (new), `apps/frontend/src/stores/chatStore.ts` (updated with message actions/state). `apps/backend/src/a2a_platform/graphql/mutations/chat_mutations.py` (new, contains `sendMessage`), `apps/backend/src/a2a_platform/graphql/types/chat_types.py` (new, contains `ChatMessage`, `SenderRole`), `apps/backend/src/a2a_platform/models/chat.py` (new `ChatMessage` model), `apps/backend/alembic/versions/TIMESTAMP_create_chat_messages_table.py` (new migration). | Schema:🗄️ `chat_messages` table created with specified columns and indexes. `sender_role_enum` created in DB. | Components:⚙️ `sendMessage` GraphQL mutation and its resolver. `ChatMessage` and `SenderRole` GraphQL types. Frontend components for message input and sending logic. Zustand store updated for optimistic updates and message status. | Config:⚙️ No new env vars. `MAX_MESSAGE_LENGTH` managed as a constant in relevant code modules.

## 📝 Tasks
1.  **DB: Create `chat_messages` table migration** - Create Alembic migration for `chat_messages` table (id, conversation_id, sender_role, content, created_at, updated_at) with FK to `conversations`. Add indexes.
    ```coder_llm
    **Do:** 🎯 Create Alembic migration script. | **File:** 📁 `apps/backend/alembic/versions/YYYYMMDDHHMM_create_chat_messages_table.py` | **Target:** 🎯 Define `upgrade()` and `downgrade()` functions. | **Check:** ✅ Migration creates table with specified columns, types, FK. `sender_role` uses `sqlalchemy.Enum` creating a DB ENUM. `content` is JSONB. Indexes on `conversation_id` and `created_at` are created. | **Needs:** 🔧 Alembic setup, `conversations` table definition.
    ```
2.  **BE: Define `ChatMessage` SQLAlchemy model** - Create `ChatMessage` model in `models/chat.py` mapping to `chat_messages` table, including `sender_role` enum.
    ```coder_llm
    **Do:** 🎯 Define SQLAlchemy model class. | **File:** 📁 `apps/backend/src/a2a_platform/models/chat.py` | **Target:** 🎯 `ChatMessage` class with `sender_role = Column(sqlalchemy.Enum('USER', 'ASSISTANT', name='sender_role_enum', create_type=False), nullable=False)`. | **Check:** ✅ Model attributes match table columns. Relationships to `Conversation` defined. `sender_role_enum` matches migration. | **Needs:** 🔧 SQLAlchemy setup, `Conversation` model.
    ```
3.  **BE: Implement `sendMessage` GraphQL mutation & types** - Define `SenderRole` enum, `ChatMessage` type, `sendMessage` mutation, and its resolver. Resolver handles auth, validation, message persistence.
    ```coder_llm
    **Do:** 🎯 Implement GraphQL types, mutation, and resolver. | **File:** 📁 `apps/backend/src/a2a_platform/graphql/types/chat_types.py` (for `ChatMessage`, `SenderRole`), `apps/backend/src/a2a_platform/graphql/mutations/chat_mutations.py` (for `sendMessage` resolver). Ensure they are added to overall schema. | **Target:** 🎯 `SenderRole` enum, `ChatMessage` type, `sendMessage` resolver function. | **Check:** ✅ Mutation accepts `conversationId`, `content`. Authenticates user (via Clerk). Authorizes user for `conversationId`. Validates input (`MAX_MESSAGE_LENGTH`, not empty). Persists message with `sender_role='USER'`. Returns `ChatMessage`. Handles errors gracefully. | **Needs:** 🔧 `ChatMessage` model, DB session, Clerk auth utilities.
    ```
4.  **FE: Define `sendMessage` GraphQL mutation query & types** - Create Apollo Client GraphQL query for `sendMessage`. Define TypeScript types for mutation variables and result based on GraphQL schema.
    ```coder_llm
    **Do:** 🎯 Define GraphQL mutation string and associated TS types. | **File:** 📁 `apps/frontend/src/graphql/mutations/chatMutations.ts` (or similar). | **Target:** 🎯 `SEND_MESSAGE_MUTATION` (gql string), `SendMessageMutationVariables`, `SendMessageMutationData` types. | **Check:** ✅ Mutation matches backend definition. Types are accurate. Includes necessary fields in response. | **Needs:** 🔧 Backend `sendMessage` schema finalization. GraphQL codegen tool (optional but recommended).
    ```
5.  **FE: Create `useSendMessage` hook** - Develop a React hook to call `sendMessage` mutation. Manages loading, error states, optimistic updates via Zustand.
    ```coder_llm
    **Do:** 🎯 Create custom React hook. | **File:** 📁 `apps/frontend/src/hooks/useSendMessage.ts` | **Target:** 🎯 `useSendMessage` function. | **Check:** ✅ Hook provides a function to send message. Manages loading/error states. Updates Zustand store optimistically (adds message with 'PENDING' status) and then updates with 'SENT' or 'ERROR' status on mutation completion/failure. | **Needs:** 🔧 Apollo Client setup, `SEND_MESSAGE_MUTATION`, Zustand chat store actions.
    ```
6.  **FE: Update Zustand store for chat messages** - Add/modify state and actions in chat store for managing messages, including their optimistic states and error handling.
    ```coder_llm
    **Do:** 🎯 Modify Zustand store. | **File:** 📁 `apps/frontend/src/stores/chatStore.ts` | **Target:** 🎯 `chatMessages` state, actions like `addOptimisticMessage`, `updateMessageStatus`. | **Check:** ✅ Store adds new message with 'PENDING' status. Updates status to 'SENT' or 'ERROR' correctly. Handles potential race conditions if any. | **Needs:** 🔧 Existing Zustand store structure.
    ```
7.  **FE: Create `SendMessageForm` component** - Build UI component with text input and send button. Uses `useSendMessage` hook. Handles input validation (empty, `MAX_MESSAGE_LENGTH`).
    ```coder_llm
    **Do:** 🎯 Create React component. | **File:** 📁 `apps/frontend/src/features/chat/components/SendMessageForm.tsx` | **Target:** 🎯 `SendMessageForm` component. | **Check:** ✅ Component allows typing, triggers send via hook. Displays UI hints/errors for validation. Disables send button during submission. Clears input on successful send. | **Needs:** 🔧 `useSendMessage` hook, UI library components, `MAX_MESSAGE_LENGTH` constant.
    ```
8.  **FE: Integrate `SendMessageForm` into `ChatView`** - Add `SendMessageForm` to the main chat interface, ensuring it's correctly positioned and functional.
    ```coder_llm
    **Do:** 🎯 Update existing component. | **File:** 📁 `apps/frontend/src/pages/ChatPage/ChatView.tsx` (or similar main chat UI file from US7.0) | **Target:** 🎯 Integration of `SendMessageForm`. | **Check:** ✅ Form appears correctly in chat view. Messages sent via form appear in chat history (optimistically then confirmed). Overall layout remains coherent. | **Needs:** 🔧 `SendMessageForm` component, Chat history display area.
    ```
9.  **Test: Write Unit & Integration tests** - Develop unit tests (Pytest, Vitest) for backend resolver (including authZ, validation), frontend hook, store, and component validation. Write integration tests (API level) for `sendMessage` mutation.
    ```coder_llm
    **Do:** 🎯 Create/update test files. | **File:** 📁 `apps/backend/tests/graphql/mutations/test_chat_mutations.py`, `apps/frontend/src/hooks/__tests__/useSendMessage.test.ts`, etc. | **Target:** 🎯 Test functions covering new logic, ACs, error handling, validation. | **Check:** ✅ Test coverage meets DoD (Unit >90%, Int >80%). Critical paths and error conditions tested. AuthZ rules for conversation access are tested. | **Needs:** 🔧 Pytest, Vitest/React Testing Library, Apollo MockedProvider, test DB fixtures.
    ```
10. **Docs: Update API documentation** - Add `sendMessage` mutation, `ChatMessage` type, and `SenderRole` enum to GraphQL API documentation.
    ```coder_llm
    **Do:** 🎯 Update documentation. | **File:** 📁 `docs/api/graphql.md` (or auto-generated if using tools like Sphinx with Strawberry). | **Target:** 🎯 Sections for `sendMessage`, `ChatMessage`, `SenderRole`. | **Check:** ✅ Documentation accurately reflects signatures, inputs, outputs, enums, and error codes. Example usage provided. | **Needs:** 🔧 Finalized API design. Access to documentation system/files.
    ```

## 🔧 Refactor
**Quality:** 📏 Resolver logic is clean, follows single responsibility (auth, validation, persistence steps are distinct). Hook logic is well-structured and handles state updates predictably. Constants like `MAX_MESSAGE_LENGTH` used over magic numbers. | **Perf:** ⚡ DB query for message insertion is simple; ensure indexes on FKs and timestamps are utilized (as per Task 1). Minimize re-renders in frontend related to message list or input state. | **Reuse:** ♻️ Input validation logic (empty, length) for messages might be shareable between FE and BE; for now, implemented separately but note for future if more complex validation arises. | **Debt:** 💳 Monitor optimistic update complexity; if bugs arise, consider simplifying or adding more robust state tracking. | **Test:** 🧪 Ensure thorough coverage for optimistic update scenarios (success, failure, rapid messages). AuthZ tests are critical. | **Docs:** 📚 GraphQL schema comments (descriptions for types, fields, mutations) are clear and will be used for auto-generated documentation if possible.

---

## Usage
**Symbols:** {text}=required | [text]=optional | {text}|[default]=required+fallback
**Check:** [X] {} replaced | [X] [] evaluated | [X] ADRs ref'd | [X] Tasks actionable | [X] AC=G-W-T | [X] Deps ID'd
