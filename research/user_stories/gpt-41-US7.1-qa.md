# 🧪 QA: US7.1: Send Text Message to Personal Assistant via Chat Interface

**Goal:** 🎯 Validate that users can send text messages to their Personal Assistant via the chat interface, ensuring backend persistence and UI display.

## 🎯 Objectives
**Functional:** ⚙️ Message send, persist, display | **NFR:** 📏 <200ms latency, auth required | **Edge:** ⚠️ Empty/oversized message, network loss | **Security:** 🔒 Auth, input validation | **DB:** 🗄️ chat_messages, conversations | **Integration:** 🔗 GraphQL API, DB

## 📋 Strategy
**Scope:** 🎯 FE chat UI, BE GraphQL, DB | **Env:** 🐳 Docker(default)/.env.test | CI(--ci flag) | **Backend:** 🖥️ apps/backend + ./scripts/run-backend-tests.sh + pytest + postgresql+asyncpg + DATABASE_URL(required) | **Frontend:** 🌐 bun + ./scripts/run-frontend-tests.sh + port:5173 + --e2e(Cypress) | **DB:** 🗄️ ./scripts/db-migrate.sh + upgrade/downgrade | **Tools:** 🛠️ pytest/bun/Docker/Alembic/Cypress | **Deps:** 🔗 US7.3, chat_messages schema | **Data:** 📊 Valid/invalid messages | **Risks:** ⚠️ Real-time sync, DB errors

## 🎬 Context
**Before:** Features:⚙️chat UI, GraphQL, DB | Env:🔧Docker, .env | Data:📊conversations exist | [None]
**After:** Deliverables:📦Test cases, reports | Reports:📄QA logs | Defects:🐛Bugs tracked | [None]

## 📝 Tasks
1. **Send Message Mutation** - Test GraphQL sendMessage mutation
```test_execution
**Do:** 🎯 Call sendMessage with valid input | **Type:** 🧪 Integration | **Cmd:** 💻 GraphQL mutation | **Assert:** ✅ Message in DB, correct conversation | **Data:** 📊 Valid text | **Result:** 🎯 Message saved, returned
```
2. **UI Message Display** - Test message appears in chat history
```test_execution
**Do:** 🎯 Send message via UI | **Type:** 🧪 E2E | **Cmd:** 💻 Cypress test | **Assert:** ✅ Message visible in chat | **Data:** 📊 Valid text | **Result:** 🎯 Message shown in UI
```
3. **Edge/Negative Cases** - Test empty, oversized, unauthenticated
```test_execution
**Do:** 🎯 Send empty/oversized/unauthenticated | **Type:** 🧪 Negative | **Cmd:** 💻 GraphQL/UI | **Assert:** ✅ Error returned, not saved | **Data:** 📊 Invalid input | **Result:** 🎯 Error, no DB entry
```

## 📋 Test Cases

### 🧪 API: Send Message
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| 7.1-TC-1 | Send valid message | Open chat → Type → Send | Message in DB, UI | API/DB/UI |

### ❌ Negative & Error
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| 7.1-ERR-1 | Send empty message | Type empty → Send | Error, not saved | Negative |
| 7.1-ERR-2 | Send oversized message | Type >limit → Send | Error, not saved | Negative |
| 7.1-ERR-3 | Unauthenticated send | Logout → Send | Auth error | Security |

### 📋 Contract & External
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| 7.1-CT-1 | GraphQL contract | Mock mutation → Validate | Schema compliance | Contract |

### ⚙️ Environment & Config
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| 7.1-ENV-1 | DB config | Set DATABASE_URL → Start | Connects, persists | Environment |

## 📊 Coverage
**Functional:** ⚙️ sendMessage, DB persist, UI display | **NFR:** 📏 latency, auth | **Edge:** ⚠️ input validation, errors

## 🔧 Setup
**Backend:** 🖥️ apps/backend + ./scripts/run-backend-tests.sh + pytest + postgresql+asyncpg + DATABASE_URL(required) | **Frontend:** 🌐 bun + ./scripts/run-frontend-tests.sh + port:5173 + dev mode | **DB:** 🗄️ ./scripts/db-migrate.sh + Docker Compose env vars + upgrade/downgrade

## 📦 Data
**Env Vars:** ⚙️ DATABASE_URL + POSTGRES_* | **Config:** 🔧 .env/.env.test + Docker + --ci flags
