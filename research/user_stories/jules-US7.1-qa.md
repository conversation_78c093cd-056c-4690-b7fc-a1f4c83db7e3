# 🧪 QA Testing Specification: US7.1

**Goal:** 🎯 Ensure the "Send Text Message to Personal Assistant via Chat Interface" functionality (US7.1) is robust, reliable, secure, and performs according to specifications, covering all ACs and NFRs with enhanced clarity and test data strategy.

## 🎯 Objectives
**Functional:** ⚙️ Validate successful message sending, persistence, UI display (including optimistic updates), and comprehensive error handling as per ACs. | **NFR:** 📏 Verify performance (API response, UI update), accessibility (WCAG 2.1 AA with potential for automated checks), security (XSS, AuthN/AuthZ), and scalability (100 msgs/sec). | **Edge:** ⚠️ Test empty messages, max length messages, Unicode/emoji messages, network interruptions, server errors, invalid inputs. | **Security:** 🔒 Validate user authentication, robust authorization for conversation access, and effective XSS protection. | **DB:** 🗄️ Verify `chat_messages` table schema, data integrity, correct `sender_role` persistence, and index usage. | **Integration:** 🔗 Test Frontend <-> Backend (GraphQL mutation), Backend <-> DB interactions, and Clerk authentication integration thoroughly.

## 📋 Strategy
**Scope:** 🎯 In-scope: All functionalities defined in US7.1 ACs, optimistic UI, error handling, backend logic, DB persistence, NFRs (Perf, Sec, A11y). Out-of-scope: US7.3 real-time aspects (WebSockets), PA responses, advanced message formatting/attachments. | **Env:** 🐳 Docker(default)/.env.test (ADR-004 aligned) | CI(--ci flag using Dockerized services) | **Backend:** 🖥️ apps/backend + ./scripts/run-backend-tests.sh + pytest + postgresql+asyncpg + DATABASE_URL(required) | **Frontend:** 🌐 bun + ./scripts/run-frontend-tests.sh + port:5173 + --e2e(Cypress) | **DB:** 🗄️ ./scripts/db-migrate.sh + upgrade (for `chat_messages` table). Seed scripts for test data. | **Tools:** 🛠️ pytest/bun/Docker/Alembic/Cypress/Apollo Client Devtools/Browser DevTools/axe-core (for A11y checks) | **Deps:** 🔗 US7.0 (Basic Chat UI) functional. Clerk sandbox user accounts (predefined for tests). `conversations` table populated via seed script. | **Data:** 📊 Predefined test users (e.g., `<EMAIL>`, `<EMAIL>`) with known conversation IDs. Messages: empty, normal, max length, over max, Unicode/emojis (e.g., "Hello 👋", "你好世界"). Malicious strings for XSS. Invalid UUIDs. | **Risks:** ⚠️ Optimistic UI complexity. AuthZ bypass. XSS. Perf bottlenecks. Test data management in CI.

## 🎬 Context
**Before:** Features:⚙️US7.0 basic chat UI present. User authentication (Clerk) functional. `conversations` table schema exists. No `chat_messages` table. No `sendMessage` GQL mutation. | Env:🔧Local Docker environment setup as per ADR-004. `.env.test` configured. Test database migrated to the state before US7.1 changes. Scripted test data (users, conversations) can be seeded. | Data:📊Predefined test user accounts in Clerk sandbox. Script to seed `conversations` in DB for these users (e.g., `testuser1_conv_pa1`, `testuser2_conv_pa2`).
**After:** Deliverables:📦Tested `sendMessage` GQL mutation, `SendMessageForm` FE component, `useSendMessage` hook, updated `chatStore`. New `chat_messages` DB table. | Reports:📄Test execution summary (automated & manual), list of passed/failed tests, detailed bug reports with reproduction steps. A11y scan reports. | Defects:🐛Bugs logged in GitHub Issues with labels `us7.1`, `bug`, severity, and linked to test cases.

## 📝 Tasks
1.  **Setup & Seed Test Environment** - Ensure Docker, DB, Clerk test users, base code (US7.0) are ready. Run seed script for conversations.
    ```test_execution
    **Do:** 🎯 Prepare and seed testing environment. | **Type:** 🧪 Infrastructure | **Cmd:** 💻 `docker-compose up -d db`, `./scripts/db-migrate.sh upgrade head`, `./scripts/seed-test-conversations.sh` (new script). | **Assert:** ✅ DB accessible, Clerk users available, seeded conversations present for test users. | **Data:** 📊 Predefined test user credentials, known seeded conversation IDs. | **Result:** 🎯 Environment consistently ready for US7.1 testing, including CI.
    ```
2.  **Execute Backend Unit & Integration Tests** - Run pytest for `sendMessage` resolver, models, including AuthZ.
    ```test_execution
    **Do:** 🎯 Run backend tests. | **Type:** 🧪 Unit/Integration | **Cmd:** 💻 `cd apps/backend && pytest tests/graphql/mutations/test_chat_mutations.py tests/models/test_chat.py` | **Assert:** ✅ All tests pass (authN, authZ, validation, persistence, model logic). | **Data:** 📊 Test fixtures within Pytest using seeded user/conversation IDs. | **Result:** 🎯 Backend logic for `sendMessage` robustly verified.
    ```
3.  **Execute Frontend Unit & Component Tests** - Run Vitest for hooks, store, components.
    ```test_execution
    **Do:** 🎯 Run frontend tests. | **Type:** 🧪 Unit | **Cmd:** 💻 `cd apps/frontend && bun test src/hooks/__tests__/useSendMessage.test.ts src/stores/__tests__/chatStore.test.ts src/features/chat/components/__tests__/SendMessageForm.test.ts` | **Assert:** ✅ All tests pass (optimistic updates, validation, UI logic, error display). | **Data:** 📊 Mocked GQL responses, store states, predefined component inputs. | **Result:** 🎯 Frontend logic for message sending verified.
    ```
4.  **Execute E2E Automated Tests (Cypress)** - Run Cypress tests for key user flows, including A11y checks.
    ```test_execution
    **Do:** 🎯 Run E2E tests with A11y checks. | **Type:** 🧪 E2E | **Cmd:** 💻 `cd apps/frontend && bun run cypress:run --spec "cypress/e2e/chatSendMessage.cy.ts"` (Integrate `axe-core` via `cypress-axe`). | **Assert:** ✅ Key scenarios pass. No critical A11y violations reported by `axe`. | **Data:** 📊 Seeded test user credentials. | **Result:** 🎯 Core E2E scenarios and basic A11y verified.
    ```
5.  **Perform Manual Exploratory & Focused NFR Testing** - UI/UX, complex interactions, advanced A11y, and specific performance profiling.
    ```test_execution
    **Do:** 🎯 Manually test edge cases, UI responsiveness, A11y. Utilize browser performance profiling tools. | **Type:** 🧪 Manual/Exploratory | **Cmd:** 💻 Interact with chat UI, use screen reader, browser dev tools (Lighthouse, Profiler). | **Assert:** ✅ UI behaves as expected, no visual bugs. A11y checks (manual) pass. Performance profiles meet expectations (e.g., for UI updates). | **Data:** 📊 Varied message inputs, simulated network conditions. | **Result:** 🎯 UI/UX quality, robustness, and deeper NFR aspects confirmed.
    ```

## 📋 Test Cases

### 🧪 Functional: Message Sending & Display (Happy Path)
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-TC-1 | Send a valid message | Login as `testuser1` → Navigate to chat with `testuser1_conv_pa1` → Enter "Hello PA" → Click Send | Message "Hello PA" appears in UI (optimistically, PENDING then SENT). Transmitted via `sendMessage`. Stored in `chat_messages` (user `testuser1`, convo `testuser1_conv_pa1`, role `USER`). | E2E/API/DB/UI |
| US7.1-TC-2 | Send message with various text characters (including JSON-problematic and Unicode) | Login → Chat → Input: `Text with "quotes", \backslashes, and emojis 👋, 你好` → Send | Message appears correctly rendered in UI. `content.text` in DB stores the exact input string faithfully. | E2E/UI/DB |
| US7.1-TC-3 | Send message to a different, active conversation | Login as `testuser1` → Send "Msg1" to `testuser1_conv_pa1` → Navigate to chat with `testuser1_conv_pa2` → Send "Msg2" to `testuser1_conv_pa2` | "Msg1" appears only in `testuser1_conv_pa1` history. "Msg2" appears only in `testuser1_conv_pa2` history. DB entries correct. | E2E/UI/DB |

### 🧪 Functional: Optimistic UI Updates
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-TC-4 | Verify optimistic message status (PENDING) | Login → Chat → Type message → Click Send (observe UI, possibly throttle network via DevTools) | Message appears in UI immediately with 'PENDING' status/indicator before GQL success response. | UI/E2E |
| US7.1-TC-5 | Verify message status updates to SENT on success | Login → Chat → Send valid message (normal network) | Message initially 'PENDING', then updates to 'SENT' (or indicator removed) upon successful GQL response. | UI/E2E |
| US7.1-TC-6 | Verify message status updates to ERROR on failure (simulated GQL error) | Login → Chat → Send message (use tool like Apollo DevTools or Cypress intercept to mock GQL 500 error for `sendMessage`) | Message 'PENDING', then 'ERROR'. Error notification "Message failed to send..." shown. Input field retains message. | UI/E2E |

### ❌ Negative & Error: Input Validations
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-ERR-1 | Attempt to send empty message (whitespace only) | Login → Chat → Enter "   " → Click Send | UI prevents send. Hint "Cannot send an empty message." No GQL call. | UI/E2E |
| US7.1-ERR-2 | Attempt to send message exceeding MAX_MESSAGE_LENGTH | Login → Chat → Enter text > 10,000 chars → Click Send | UI prevents send. Hint "Message exceeds maximum length." No GQL call. | UI/E2E |
| US7.1-ERR-3 | Attempt to send message at exact MAX_MESSAGE_LENGTH | Login → Chat → Enter text = 10,000 chars → Click Send | Message sent successfully (as per US7.1-TC-1). | UI/E2E/API |

### ❌ Negative & Error: Transmission & Server Errors
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-ERR-4 | Network error during send (simulated) | Login → Chat → Type message → Click Send (use Browser DevTools to go offline before GQL call completes) | UI shows error "Message failed to send...". Input field retains message. Message status 'ERROR'. | UI/E2E |
| US7.1-ERR-5 | GraphQL server returns specific error (e.g., validation error from resolver) | Call `sendMessage` GQL directly with content that passes frontend validation but fails backend (e.g. hypothetical future server-side only check) | GQL returns specific error message. UI displays generic "Message failed to send..." or a more specific one if mapped. | API/UI |
| US7.1-ERR-6 | Invalid `conversationId` (format, null, non-existent) provided to GQL mutation | API Call: `sendMessage` with `conversationId` as "invalid-uuid-format", `null`, or a valid UUID not in DB. | GQL returns validation error (for format/null) or a "not found/access denied" error. Message not persisted. | API |

### 🔒 Security: AuthN/AuthZ & XSS
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-SEC-1 | Send message without authentication (no JWT) | API Call: `sendMessage` with no `Authorization` header. | GQL returns authentication error (e.g., 401/403, specific error code). Message not persisted. | API |
| US7.1-SEC-2 | Send message to conversation not belonging to user | Login as `testuser1`. API Call: `sendMessage` with `conversationId` known to belong to `testuser2`. | GQL returns authorization error. Message not persisted. | API |
| US7.1-SEC-3 | Send message with XSS payload | Login → Chat → Enter `<img src=x onerror=alert(1)>` → Send. | Message in UI sanitized (e.g., `&lt;img src=x onerror=alert(1)&gt;`). No JS alert. Sanitized version in DB. | UI/API/DB/E2E |

### 📏 NFR: Performance & Accessibility
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-NFR-1 | API Response Time (p95 @ 100msgs/sec) | Load test `sendMessage` (k6/Locust) with 100 unique users/sec for 1 min. | 95th percentile response time < 200ms. Error rate < 1%. | API/Perf |
| US7.1-NFR-2 | UI Optimistic Update Time & Rendering Perf | Send message. Manually observe or use Browser DevTools (Profiler/Performance tabs). | Perceived UI update < 50ms. No noticeable jank/layout shift. | UI/Manual/Perf |
| US7.1-NFR-3 | A11y: Keyboard navigation & operation | Login → Chat → Tab to input, type, Tab to Send, Enter. Tab through other interactive elements. | All elements focusable & operable. Focus order logical. | UI/Manual/A11y |
| US7.1-NFR-4 | A11y: Screen reader compatibility & automated checks | Use screen reader (NVDA/JAWS/VoiceOver). Run `axe-core` via Cypress. | Elements labelled. Messages/errors announced. No critical `axe` violations. | UI/Manual/Auto/A11y |

### 📋 Contract: GraphQL API
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-CT-1 | Verify `sendMessage` mutation signature & types | Introspect GraphQL schema. | `sendMessage(conversationId: UUID!, content: JSONString!): ChatMessage!` exists. `UUID`, `JSONString`, `ChatMessage` types correct per US spec. | API/Contract |
| US7.1-CT-2 | Verify `ChatMessage` type fields | Introspect `ChatMessage` type. | Fields `id: UUID!`, `conversationId: UUID!`, `senderRole: SenderRole!`, `content: JSONString!`, `createdAt: DateTime!`, `updatedAt: DateTime!` exist with correct types. | API/Contract |
| US7.1-CT-3 | Verify `SenderRole` enum | Introspect `SenderRole` enum. | Enum `USER`, `ASSISTANT` defined. | API/Contract |

### ⚙️ Environment & Config
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-ENV-1 | Test with `MAX_MESSAGE_LENGTH` constant (10000) | Execute US7.1-ERR-2 and US7.1-ERR-3. | Behavior aligns with 10k limit. | Functional/Config |

## 📊 Coverage
**Functional:** ⚙️ All ACs from US7.1 spec covered. Message sending variations (valid, special chars, Unicode, different convos). Optimistic UI states. Input validations (empty, max length). Transmission/server errors. | **NFR:** 📏 Performance (API load, UI responsiveness). Accessibility (keyboard, screen reader, automated checks). Security (AuthN, AuthZ, XSS). | **Edge:** ⚠️ Empty/max length/exact max/Unicode messages. Network/server errors. Invalid `conversationId` types. Different conversations. | **Integration:** 🔗 FE-BE GQL, BE-DB, Clerk Auth.

## 🔧 Setup
**Backend:** 🖥️ `cd apps/backend && pytest` (uses `DATABASE_URL` from `.env.test`). PostgreSQL running. Alembic migrations to `head`. `./scripts/seed-test-conversations.sh` run. | **Frontend:** 🌐 `cd apps/frontend && bun test` (unit). `bun run cypress:open` or `bun run cypress:run --e2e` (E2E with `axe-core`). Dev server `http://localhost:5173`. | **DB:** 🗄️ `chat_messages` table by migration. Test data: predefined Clerk users, conversations seeded by script.

## 📦 Data
**Env Vars:** ⚙️ `DATABASE_URL`, `CLERK_API_KEY`, `CLERK_PUBLISHABLE_KEY`, `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY`, `NEXT_PUBLIC_GRAPHQL_URL`. Use `.env.test`. `MAX_MESSAGE_LENGTH` (code constant). | **Seed Script:** 📄 `scripts/seed-test-conversations.sh` creates known conversations for `<EMAIL>`, `<EMAIL>`.

## 🎭 Scenarios

### 🎬 Scenario 1: Successful Message Send (Valid Text & Unicode/Emoji)
**TCs: US7.1-TC-1, US7.1-TC-2 (Unicode part), US7.1-TC-4, US7.1-TC-5**
- **Given:** 📋 `testuser1` logged in, on chat page with `testuser1_conv_pa1`. Input empty.
- **When:** ⚡ User types "Hello PA 👋 你好" → Clicks Send.
- **Then:** ✅ Message "Hello PA 👋 你好" appears in UI (PENDING then SENT). `sendMessage` GQL called with correct `conversationId` and `content='{"text":"Hello PA 👋 你好"}'`.
- **And:** ➕ Message in `chat_messages` table for `testuser1_conv_pa1`, `sender_role='USER'`, content stored accurately.

### 🎬 Scenario 2: Attempt to Send Empty Message then Max Length Message
**TCs: US7.1-ERR-1, US7.1-ERR-3**
- **Given:** 📋 `testuser1` logged in, on chat page. Input empty.
- **When:** ⚡ User types "   " → Clicks Send.
- **Then:** ✅ UI hint "Cannot send an empty message." No GQL call. Input field retains "   ".
- **When:** ⚡ User clears input → Types message of exactly 10,000 chars → Clicks Send.
- **Then:** ✅ Message sent successfully, appears in UI (PENDING then SENT). Persisted in DB.

### 🎬 Scenario 3: Message Send Fails (Network Error), then Resend
**TCs: US7.1-ERR-4, US7.1-TC-6 (Error part), then successful send (like TC1)**
- **Given:** 📋 `testuser1` logged in, on chat page. Types "Important Info".
- **When:** ⚡ Clicks Send. Network disconnected (simulated via DevTools).
- **Then:** ✅ Message "Important Info" in UI (PENDING then ERROR). Error notification "Message failed to send...". Input field retains "Important Info".
- **When:** ⚡ Network reconnected. User clicks Send again (or a retry mechanism if implemented).
- **Then:** ✅ Message "Important Info" successfully sent (PENDING then SENT). Persisted in DB.

### 🎬 Scenario 4: Security - AuthZ & XSS Combination
**TCs: US7.1-SEC-2 (API), US7.1-SEC-3 (UI/E2E)**
- **Given:** 📋 `testuser1` logged in. `testuser2_conv_pa1` belongs to `testuser2`.
- **When:** ⚡ `testuser1` attempts API call to `sendMessage` with `conversationId = testuser2_conv_pa1`.
- **Then:** ✅ GQL returns AuthZ error. Message not persisted.
- **When:** ⚡ `testuser1` navigates to own chat `testuser1_conv_pa1` → Enters XSS payload `<img src=x onerror=alert('XSS')>` → Clicks Send.
- **Then:** ✅ Message sanitized in UI & DB. No alert.

### 🎬 Scenario 5: Performance & A11y Quick Check
**TCs: US7.1-NFR-1 (conceptual), US7.1-NFR-2, US7.1-NFR-3, US7.1-NFR-4 (automated part)**
- **Given:** 📋 `testuser1` logged in, on chat page. Automated load test script ready. Cypress with `axe-core` ready.
- **When:** ⚡ Execute 30-sec load test snippet (e.g., 50 users/sec). Simultaneously, manually send a message and observe UI update. Then run Cypress E2E for chat send.
- **Then:** ✅ API p95 response times within acceptable short test range. UI update perceived <50ms. Cypress tests pass, including no critical `axe-core` A11y violations on chat page during send. Keyboard navigation for send works.

## 🤖 Automation
**Pre-commit:** 🔧 MyPy, Ruff, ESLint, Prettier. Targeted backend (`pytest -k "chat_mutations or chat_model"`) & frontend (`bun test SendMessageForm useSendMessage chatStore`) tests. | **CI/CD:** 🚀 Full Pytest & Vitest/Bun test suites. Cypress E2E suite (`chatSendMessage.cy.ts` and others) with `axe-core` checks. Dockerized services for consistency (ADR-004). Scripted DB seeding. | **Env Mgmt:** 🐳 Docker Compose, `.env.test`. Standardized CI env vars. Idempotent DB migration & seed scripts.

## 📦 Deliverables
📄 This QA spec. Test execution logs (Pytest, Vitest, Cypress JSON/HTML reports). A11y scan reports (`axe-core` output). Bug reports (GitHub Issues). QA Sign-off.

## ⚠️ Risks
**Risk 1:** 🚨 Optimistic UI state doesn't correctly reflect backend reality under flaky network/errors. → Mitigation: Extensive scenario testing for optimistic updates (TCs TC4-6, ERR4-5). Clear visual cues for PENDING/ERROR states.
**Risk 2:** 🚨 AuthZ bypass allowing message sending to unauthorized conversations. → Mitigation: Explicit API-level AuthZ tests (US7.1-SEC-2). Rigorous code reviews of resolver AuthZ logic.
**Risk 3:** 🚨 XSS via message content. → Mitigation: Test with diverse XSS payloads (US7.1-SEC-3). Ensure backend sanitization is robust. Content Security Policy (CSP) if applicable.
**Risk 4:** 🚨 `sendMessage` API performance degradation under load. → Mitigation: Focused load tests (US7.1-NFR-1). Ensure DB indexes are effective. Profile resolver logic.
**Risk 5:** 🚨 Test data setup in CI is unreliable or slow. → Mitigation: Robust, idempotent conversation seeding script. Predefined Clerk test users.

---

## Usage
**Symbols:** {text}=required | [text]=optional | {text}|[default]=required+fallback
**Check:** [X] {} replaced | [X] [] evaluated | [X] Test cases defined | [X] Scenarios mapped | [X] Automation considered
