# 📋 User Story Specification: US7.1 Send Text Message to Personal Assistant via Chat Interface

**Story:** As a user, I want to send a text message to my Personal Assistant via a chat interface, so that I can communicate my requests and information.
**Steps:** User enters message → Frontend sends GraphQL mutation → Backend persists message → UI updates with new message
**Epic:** 🎯 Core Conversational Interface & Interaction

## ✅ AC
- [ ] Given I am in the chat interface with my <PERSON>, When I type a message in the input field and send it, Then the message is transmitted to the backend via a GraphQL mutation (sendMessage). [Source 1259, 1298]
- [ ] Given the backend receives the message, Then it is persisted in the chat_messages table, associated with the correct conversation. [Source 1307]
- [ ] Given the message is sent, Then it appears in my chat history in the UI.

## 🔗 Deps
**Stories:** US7.3(Future) - Real-time message display | **Ext:** 🌐 None | **Tech:** ⚙️ Strawberry GraphQL, Apollo Client, PostgreSQL, WebSocket | **Data:** 🗄️ conversations, chat_messages | **Team:** 👥 None

## 💰 Value
**Biz:** 📈 Core communication functionality for PA interaction | **UX:** ✨ Seamless, intuitive user-to-PA interaction | **KPI:** 📊 Message send success rate, UI response time < 500ms | **Why:** 🎯 Critical foundation for all assistant interactions

## 📐 ADRs
**Refs:** 📄 ADR-002 Sec 2.5 (Chat Interface Support), ADR-003 Sec 3.4 (Chat Interface) | **Limits:** ⚠️ Sync GraphQL mutation via HTTP, not WebSocket | **Patterns:** 🏗️ GraphQL Mutations, OBAC (clerk_user_id)

## 🛠️ Impl
**Arch:** 🏛️ GraphQL mutation for sending, conversation association | **Data:** 🗄️ chat_messages(id, conversation_id, sender_role, content, timestamp, metadata), conversations(id, user_id, assistant_id, created_at, last_message_at) | **API:** 🔌 sendMessage(conversationId: ID!, content: JSON!): ChatMessage! | **Sec:** 🔒 Clerk authentication, OBAC validation for conversation access | **Perf:** ⚡ Message persistence < 200ms | **Int:** 🌐 None | **Err:** ❌ Network failure handling, validation errors | **Std:** 📏 JSON content structure

## 🧪 Test
**Unit:** 🔬 Resolver logic, auth validation | **Int:** 🔗 GraphQL API, database persistence | **Contract:** 📋 sendMessage mutation schema | **E2E:** 🎭 Send message flow | **Perf:** 🚀 Message submission latency | **Sec:** 🛡️ Unauthorized conversation access

## 📏 NFRs
**Perf:** ⚡ Message send-to-persist < 500ms | **A11y:** ♿ Chat interface keyboard navigation | **Sec:** 🔒 Clerk JWT validation | **Scale:** 📈 100+ messages/sec per user | **Compat:** 🌍 All modern browsers

## ✅ DoD
- [ ] AC met | [ ] Tests pass | [ ] Security review | [ ] Perf benchmarks | [ ] Docs updated | [ ] Code review | [ ] QA complete

## ⚠️ Risk
**Tech:** 💻 GraphQL resolver complexity → Use established patterns from existing mutations | **Biz:** 📊 None | **Time:** ⏰ None | **Deps:** 🔗 Conversation context might not be ready → Ensure conversation creation logic exists

## 📊 Est
**Pts:** 🎯 3 (Medium-simple implementation using established patterns) | **Hrs:** ⏱️ 8 (3 dev + 3 test + 2 review) | **Complex:** 🧩 Low (follows established mutation pattern) | **Conf:** 📈 High (similar to other mutations) | **Vars:** 🔄 Message content structure variations

## 📦 Data
**API:** 🔌
```
mutation SendMessage($conversationId: ID!, $content: JSON!) {
  sendMessage(conversationId: $conversationId, content: $content) {
    id
    senderRole
    content
    timestamp
  }
}
```

**DB:** 🗄️
```sql
INSERT INTO chat_messages (conversation_id, sender_role, content, timestamp)
VALUES ($1, 'user', $2, now())
RETURNING id, conversation_id, sender_role, content, timestamp
```

**UI:** 🖥️ Message input field, send button, message history component state | **Config:** ⚙️ GraphQL endpoint URL

## 🎨 Visual
**Layout:** 📐
```
+---------------------------+
| Chat Interface            |
+---------------------------+
| [Message History]         |
|                           |
|                           |
| [User]: New message text  |
|                           |
+---------------------------+
| [Input Field]     [Send] |
+---------------------------+
```

**Flow:** 🌊
```mermaid
sequenceDiagram
  participant User
  participant Frontend
  participant GraphQL API
  participant Database

  User->>Frontend: Enter message
  User->>Frontend: Click send
  Frontend->>GraphQL API: sendMessage mutation
  GraphQL API->>Database: Insert into chat_messages
  GraphQL API->>Frontend: Return ChatMessage
  Frontend->>User: Update UI with message
```

## 🤔 Assume
**Sys:** 🖥️ Conversation exists in DB, PostgreSQL available | **User:** 👤 Authenticated with valid Clerk session | **Env:** 🌐 Stable network connection | **Data:** 📊 Valid message content (text, potentially structured JSON)

## 🎬 Context
**Before:**
Files:📁 apps/backend/src/a2a_platform/api/graphql/__init__.py, apps/backend/src/a2a_platform/db/models/message.py, apps/web/src/components/chat/ChatInterface.tsx | Schema:🗄️ conversations, chat_messages tables exist | Components:⚙️ GraphQL API, Chat UI components | Config:⚙️ Apollo Client, GraphQL endpoint

**After:**
Files:📁 apps/backend/src/a2a_platform/api/graphql/resolvers/chat_resolvers.py (updated), apps/backend/src/a2a_platform/api/graphql/schemas/chat_schemas.py (updated), apps/web/src/graphql/mutations.tsx (updated), apps/web/src/components/chat/ChatInput.tsx (updated) | Schema:🗄️ No changes to schema | Components:⚙️ New sendMessage mutation, updated chat input component | Config:⚙️ No changes

## 📝 Tasks
1. **Define GraphQL Mutation Schema** - Create the sendMessage mutation schema
```coder_llm
**Do:** 🎯 Define the sendMessage mutation in the GraphQL schema | **File:** 📁 apps/backend/src/a2a_platform/api/graphql/schemas/chat_schemas.py | **Target:** 🎯 ChatMutation class | **Check:** ✅ Mutation accepts conversationId and content parameters | **Needs:** 🔧 Existing chat schemas
```

2. **Implement Resolver Function** - Create the resolver for the sendMessage mutation
```coder_llm
**Do:** 🎯 Implement the resolver function for the sendMessage mutation | **File:** 📁 apps/backend/src/a2a_platform/api/graphql/resolvers/chat_resolvers.py | **Target:** 🎯 resolve_send_message function | **Check:** ✅ Function extracts clerk_user_id, validates conversation ownership, inserts message, returns response | **Needs:** 🔧 Database models for chat_messages, conversations
```

3. **Register Mutation in Schema** - Add the sendMessage mutation to the root schema
```coder_llm
**Do:** 🎯 Register the sendMessage mutation in the GraphQL schema | **File:** 📁 apps/backend/src/a2a_platform/api/graphql/__init__.py | **Target:** 🎯 Mutation class | **Check:** ✅ sendMessage mutation is properly registered with the schema | **Needs:** 🔧 Resolver function implementation
```

4. **Add Frontend Mutation Definition** - Define the GraphQL mutation in the frontend
```coder_llm
**Do:** 🎯 Add the sendMessage GraphQL mutation definition | **File:** 📁 apps/web/src/graphql/mutations.tsx | **Target:** 🎯 New SEND_MESSAGE_MUTATION constant | **Check:** ✅ Mutation correctly defined with proper fields | **Needs:** 🔧 None
```

5. **Implement Chat Input Component** - Enhance the chat input to send messages
```coder_llm
**Do:** 🎯 Implement the chat input component to use the sendMessage mutation | **File:** 📁 apps/web/src/components/chat/ChatInput.tsx | **Target:** 🎯 ChatInput component | **Check:** ✅ Component captures input, sends mutation, handles loading/error states | **Needs:** 🔧 Apollo client, SEND_MESSAGE_MUTATION
```

6. **Write Backend Tests** - Create tests for the sendMessage mutation
```coder_llm
**Do:** 🎯 Write tests for the sendMessage resolver | **File:** 📁 apps/backend/tests/unit/resolvers/test_chat_resolvers.py | **Target:** 🎯 TestSendMessageResolver class | **Check:** ✅ Tests authorization, validation, successful message creation | **Needs:** 🔧 Resolver implementation
```

7. **Write Frontend Tests** - Create tests for the chat input component
```coder_llm
**Do:** 🎯 Write tests for the chat input component | **File:** 📁 apps/web/src/components/chat/ChatInput.test.tsx | **Target:** 🎯 Test suite for ChatInput | **Check:** ✅ Tests input handling, form submission, mutation calling, error handling | **Needs:** 🔧 ChatInput component implementation
```

## 🔧 Refactor
**Quality:** 📏 Follow established resolver patterns from existing mutations | **Perf:** ⚡ Use parameterized queries for DB inserts | **Reuse:** ♻️ Leverage existing message formatting utilities | **Debt:** 💳 None | **Test:** 🧪 Ensure mutation validation coverage | **Docs:** 📚 Add JSDoc comments to frontend components
