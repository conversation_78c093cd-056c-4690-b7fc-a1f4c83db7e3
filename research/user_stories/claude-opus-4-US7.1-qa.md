# 🧪 QA Testing Specification: US7.1

**Goal:** 🎯 Validate complete message sending functionality from UI input through backend persistence with proper error handling and performance

## 🎯 Objectives
**Functional:** ⚙️ Message creation, transmission, persistence, display | **NFR:** 📏 <200ms response, WCAG 2.1 AA, 10 msg/min rate limit | **Edge:** ⚠️ Empty messages, oversized content, network failures | **Security:** 🔒 JWT validation, XSS prevention, input sanitization | **DB:** 🗄️ chat_messages integrity, proper indexing | **Integration:** 🔗 GraphQL mutations, WebSocket subscriptions

## 📋 Strategy
**Scope:** 🎯 In: Message send flow, validation, persistence | Out: Message retrieval, real-time updates (US7.3) | **Env:** 🐳 Docker(default)/.env.test | CI(--ci flag) | **Backend:** 🖥️ apps/backend + ./scripts/run-backend-tests.sh + pytest + postgresql+asyncpg + DATABASE_URL(required) | **Frontend:** 🌐 bun + ./scripts/run-frontend-tests.sh + port:5173 + --e2e(Cypress) | **DB:** 🗄️ ./scripts/db-migrate.sh + upgrade/downgrade | **Tools:** 🛠️ pytest/bun/Docker/Alembic/Cypress | **Deps:** 🔗 Clerk auth setup, conversations table exists | **Data:** 📊 Test users with PAs, active conversations | **Risks:** ⚠️ WebSocket stability, rate limiting edge cases

## 🎬 Context
**Before:** Features:⚙️ Basic chat UI, user auth, conversations table | Env:🔧 Docker PostgreSQL, GraphQL endpoint | Data:📊 Test user with PA and conversation
**After:** Deliverables:📦 Test results, coverage report | Reports:📄 Performance benchmarks | Defects:🐛 Tracked in GitHub issues

## 📝 Tasks
1. **Setup test environment** - Infrastructure preparation
```test_execution
**Do:** 🎯 Configure Docker test environment with required services | **Type:** 🧪 Infrastructure | **Cmd:** 💻 docker compose -f docker-compose.test.yml up -d | **Assert:** ✅ PostgreSQL running, Redis available, GraphQL endpoint accessible | **Data:** 📊 .env.test with DATABASE_URL | **Result:** 🎯 Test environment ready
```

2. **Create test fixtures** - Data preparation
```test_execution
**Do:** 🎯 Generate test users, PAs, and conversations | **Type:** 🧪 Unit | **Cmd:** 💻 cd apps/backend && pytest tests/fixtures/test_data.py | **Assert:** ✅ Test user created, PA configured, conversation exists | **Data:** 📊 Mock Clerk tokens, UUID references | **Result:** 🎯 Test data available
```

3. **Test GraphQL mutation** - API validation
```test_execution
**Do:** 🎯 Validate sendMessage mutation contract and behavior | **Type:** 🧪 Integration | **Cmd:** 💻 cd apps/backend && pytest tests/integration/test_chat_mutations.py::test_send_message | **Assert:** ✅ Mutation accepts valid input, returns ChatMessage type, persists to DB | **Data:** 📊 Valid conversation ID, message content | **Result:** 🎯 API contract validated
```

4. **Test frontend components** - UI validation
```test_execution
**Do:** 🎯 Validate ChatInput component behavior | **Type:** 🧪 Unit | **Cmd:** 💻 cd apps/web && bun test src/components/chat/ChatInput.test.tsx | **Assert:** ✅ Input validation, keyboard shortcuts, disabled states | **Data:** 📊 Various input scenarios | **Result:** 🎯 Component behavior verified
```

5. **Run E2E tests** - Full flow validation
```test_execution
**Do:** 🎯 Test complete message send flow through UI | **Type:** 🧪 E2E | **Cmd:** 💻 cd apps/web && bun run cypress:run --spec cypress/e2e/chat/send-message.cy.ts | **Assert:** ✅ Message sent, appears in UI, persisted to DB | **Data:** 📊 Test user credentials, message content | **Result:** 🎯 End-to-end flow validated
```

6. **Performance testing** - Load validation
```test_execution
**Do:** 🎯 Validate performance under concurrent load | **Type:** 🧪 Integration | **Cmd:** 💻 cd apps/backend && pytest tests/performance/test_chat_load.py -k concurrent_messages | **Assert:** ✅ 95th percentile <200ms, no errors under 100 concurrent users | **Data:** 📊 100 simulated users, 10 messages each | **Result:** 🎯 Performance requirements met
```

7. **Accessibility testing** - WCAG compliance
```test_execution
**Do:** 🎯 Validate WCAG 2.1 AA compliance for chat interface | **Type:** 🧪 E2E | **Cmd:** 💻 cd apps/web && bun run cypress:run --spec cypress/e2e/a11y/chat-accessibility.cy.ts | **Assert:** ✅ No WCAG violations, keyboard navigation works, screen reader compatible | **Data:** 📊 axe-core rules, keyboard events | **Result:** 🎯 Accessibility standards met
```

8. **Security testing** - Input validation
```test_execution
**Do:** 🎯 Test input sanitization and injection prevention | **Type:** 🧪 Integration | **Cmd:** 💻 cd apps/backend && pytest tests/security/test_input_validation.py | **Assert:** ✅ XSS attempts blocked, SQL injection prevented, content properly escaped | **Data:** 📊 Malicious payloads, injection attempts | **Result:** 🎯 Security vulnerabilities mitigated
```

## 📋 Test Cases

### 🧪 Functional: Core Message Sending
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-TC-01 | Send valid text message | 1. Open chat → 2. Type "Hello PA" → 3. Click Send | Message appears in chat, persisted to DB | UI/API/DB |
| US7.1-TC-02 | Send message with Enter key | 1. Type message → 2. Press Enter | Message sent successfully | UI |
| US7.1-TC-03 | Multi-line message with Shift+Enter | 1. Type line 1 → 2. Shift+Enter → 3. Type line 2 → 4. Send | Multi-line message preserved | UI |
| US7.1-TC-04 | Send button disabled while sending | 1. Send message → 2. Check button state | Button disabled during request | UI |
| US7.1-TC-05 | Message timestamp accuracy | 1. Send message → 2. Check timestamp | UTC timestamp within 1 second of send time | API/DB |
| US7.1-TC-06 | Message content structure | 1. Send text → 2. Verify JSON structure | Content follows {parts: [{type, content}]} format | API |
| US7.1-TC-07 | Accessibility - keyboard nav | 1. Tab to input → 2. Type → 3. Tab to send | All controls keyboard accessible | UI |

### ❌ Negative & Error
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-ERR-01 | Empty message validation | 1. Leave input empty → 2. Try to send | Error: "Message cannot be empty" | Negative |
| US7.1-ERR-02 | Oversized message (>10k chars) | 1. Paste 10,001 chars → 2. Try to send | Error: "Message too long" | Negative |
| US7.1-ERR-03 | Invalid conversation ID | 1. Modify conversationId → 2. Send message | 404 Error: "Conversation not found" | Security |
| US7.1-ERR-04 | Unauthorized user | 1. Use invalid JWT → 2. Send message | 401 Error: "Unauthorized" | Security |
| US7.1-ERR-05 | Rate limit exceeded | 1. Send 11 messages in 1 minute | 429 Error: "Rate limit exceeded" | Negative |
| US7.1-ERR-06 | Network failure handling | 1. Disconnect network → 2. Send message | Error shown, message queued for retry | Negative |
| US7.1-ERR-07 | XSS injection attempt | 1. Send `<script>alert(1)</script>` | Content sanitized, no script execution | Security |
| US7.1-ERR-08 | SQL injection in content | 1. Send `'; DROP TABLE--` | Content stored safely as text | Security |

### 📋 Contract & External
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-CT-01 | GraphQL mutation contract | Mock → Send → Validate response | Response matches ChatMessage schema | Contract |
| US7.1-CT-02 | Clerk JWT validation | Mock Clerk response → Validate token | Token accepted, user context extracted | Integration |
| US7.1-CT-03 | WebSocket connection | Connect → Send → Check subscription | Message delivered via subscription | Integration |
| US7.1-CT-04 | Message content validation | Send various content → Validate sanitization | Dangerous content neutralized | Contract |

### ⚙️ Environment & Config
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-ENV-01 | Docker test environment | Start containers → Run tests → Verify | All services healthy, tests pass | Environment |
| US7.1-ENV-02 | CI environment parity | Run in CI → Compare to local | Same test results in both environments | Environment |
| US7.1-ENV-03 | Database migration | Run migration → Test schema → Rollback | Migration applies cleanly, rollback works | Environment |
| US7.1-ENV-04 | Test data cleanup | Run tests → Check DB state → Cleanup | No test data persists between runs | Environment |

## 📊 Coverage
**Functional:** ⚙️ Message send mutation, UI input validation, DB persistence, error handling | **NFR:** 📏 Response time <200ms, WCAG compliance, rate limiting | **Edge:** ⚠️ Empty/oversized messages, network failures, concurrent sends

## 🔧 Setup
**Backend:** 🖥️ apps/backend + ./scripts/run-backend-tests.sh + pytest + postgresql+asyncpg + DATABASE_URL(required) | **Frontend:** 🌐 bun + ./scripts/run-frontend-tests.sh + port:5173 + dev mode | **DB:** 🗄️ ./scripts/db-migrate.sh + Docker Compose env vars + chat_messages table migration

## 📦 Data
**Env Vars:** ⚙️ DATABASE_URL + POSTGRES_* + CLERK_JWT_PUBLIC_KEY + .env/.env.test | **Config:** 🔧 .env.test + Docker + --ci flags + MAX_MESSAGE_LENGTH=10000

## 🎭 Scenarios

### 🎬 Happy Path: Successful Message Send
**TC 1.1:** User sends first message
- **Given:** 📋 User authenticated and in chat interface | **When:** ⚡ Types "Hello, PA!" and clicks Send | **Then:** ✅ Message appears in chat history | **And:** ➕ Message persisted to chat_messages table with correct conversation_id

**TC 1.2:** Rapid message sending
- **Given:** 📋 User in active conversation | **When:** ⚡ Sends 5 messages quickly | **Then:** ✅ All messages appear in order | **And:** ➕ No messages lost, timestamps sequential

### 🎬 Error Handling: Network Issues
**TC 2.1:** Network disconnection during send
- **Given:** 📋 User typing message | **When:** ⚡ Network disconnects, user clicks Send | **Then:** ✅ Error message shown | **And:** ➕ Message added to retry queue in localStorage

**TC 2.2:** WebSocket reconnection
- **Given:** 📋 WebSocket connection lost | **When:** ⚡ Connection restored | **Then:** ✅ Queued messages sent automatically | **And:** ➕ User notified of reconnection

### 🎬 Security: Input Validation
**TC 3.1:** XSS attempt prevention
- **Given:** 📋 Malicious user | **When:** ⚡ Sends "<script>alert('xss')</script>" | **Then:** ✅ Content sanitized before storage | **And:** ➕ Script tags removed, text preserved

## 🤖 Automation
**Pre-commit:** 🔧 MyPy type checking + Ruff formatting + SKIP=pytest-backend | **CI/CD:** 🚀 GitHub Actions + postgres://test_user:test_password@localhost:5432/test_database + Docker parity | **Env Mgmt:** 🐳 Docker(default) + .env.test + idempotent migrations

## 📦 Deliverables
📄 QA spec + test execution logs + coverage report (target: 90%) + performance benchmarks + defect report

## ⚠️ Risks
**Risk 1:** 🚨 WebSocket instability in test environment → Use polling fallback for critical tests
**Risk 2:** 🚨 Rate limiting affects concurrent tests → Implement test isolation and cleanup
**Risk 3:** 🚨 Mock Clerk tokens expire → Generate fresh tokens for each test run

---

## Usage
**Symbols:** {text}=required | [text]=optional | {text}|[default]=required+fallback
**Check:** [x] {} replaced | [x] [] evaluated | [x] Test cases defined | [x] Scenarios mapped | [x] Automation considered
