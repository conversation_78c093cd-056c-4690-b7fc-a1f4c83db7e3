# 📋 User Story Specification: US7.1

**Story:** As a user, I want to send a text message to my Personal Assistant via a chat interface so that I can communicate my requests and information.
**Steps:** Open chat interface → Type message → Send message → View in chat history
**Epic:** 🎯 Core Conversational Interface & Interaction

## ✅ AC
- [ ] Given I am in the chat interface with my PA, When I type a message in the input field and send it, Then the message is transmitted to the backend via a GraphQL mutation (e.g., sendMessage) [Source 1259, 1298]
- [ ] Given the backend receives the message, When it is processed, Then it is persisted in the chat_messages table, associated with the correct conversation [Source 1307]
- [ ] Given the message is sent, When the mutation completes successfully, Then it appears in my chat history in the UI
- [ ] Given I send a message, When the backend processes it, Then the message timestamp is recorded accurately in UTC
- [ ] Given I send an empty message, When I click send, Then the UI prevents submission and shows appropriate feedback
- [ ] Given the message content exceeds 10,000 characters, When I attempt to send, Then the UI shows a validation error
- [ ] Given network connectivity issues, When I send a message, Then the UI shows appropriate error handling and retry options
- [ ] Given the WebSocket connection is lost, When I send a message, Then the system falls back to HTTP polling until reconnected

## 🔗 Deps
**Stories:** US7.0: Basic chat UI components (COMPLETED) | US6.2: User authentication system (IN PROGRESS) | US7.3: Real-time message display (PLANNED) | **Ext:** 🌐 Clerk.com@latest for auth | **Tech:** ⚙️ Strawberry GraphQL, Apollo Client, Zustand | **Data:** 🗄️ conversations, chat_messages tables | **Team:** 👥 None

## 💰 Value
**Biz:** 📈 Core MVP functionality enabling user-PA communication | **UX:** ✨ Natural conversational interface for all interactions | **KPI:** 📊 Message send success rate >99%, Response time <200ms | **Why:** 🎯 Foundation for all PA capabilities and user value delivery

## 📐 ADRs
**Refs:** 📄 ADR-002 Sec 2.5 (Chat Interface Support), ADR-002 Sec 4 (GraphQL Schema), ADR-003 Sec 3.4 (Chat Interface) | **Limits:** ⚠️ One PA per user, GraphQL Subscriptions for real-time | **Patterns:** 🏗️ GraphQL mutations for message sending, WebSocket for subscriptions

## 🛠️ Impl
**Arch:** 🏛️ GraphQL mutation pattern with Apollo Client, Zustand for local state, WebSocket with HTTP fallback | **Data:** 🗄️ chat_messages: {id: UUID, conversation_id: UUID FK, sender_role: TEXT CHECK('user'|'agent'), content: JSONB, timestamp: TIMESTAMPTZ, metadata: JSONB} | **API:** 🔌 sendMessage(conversationId: UUID!, content: MessageContentInput!): ChatMessage! | **Sec:** 🔒 Clerk JWT validation, bleach library for sanitization, content length validation | **Perf:** ⚡ <200ms mutation response, optimistic UI updates, connection pooling | **Int:** 🌐 Redis for WebSocket state management | **Err:** ❌ Network failures with exponential backoff, validation errors, rate limiting (429 status) | **Std:** 📏 GIVEN-WHEN-THEN testing, TypeScript strict mode, error boundary components

## 🧪 Test
**Unit:** 🔬 Message validation, content sanitization, GraphQL resolver | **Int:** 🔗 Database persistence, conversation association | **Contract:** 📋 GraphQL mutation schema compliance | **E2E:** 🎭 Complete message send flow | **Perf:** 🚀 Load test 100 concurrent users | **Sec:** 🛡️ XSS prevention, JWT validation

## 📏 NFRs
**Perf:** ⚡ 95th percentile <200ms, support 1000 concurrent users | **A11y:** ♿ WCAG 2.1 AA compliant, keyboard navigation | **Sec:** 🔒 Content sanitization, rate limiting 10 msg/min | **Scale:** 📈 Horizontal scaling via connection pooling | **Compat:** 🌍 Chrome/Firefox/Safari latest 2 versions

## ✅ DoD
- [ ] AC met | [ ] Unit/integration tests pass | [ ] Security review complete | [ ] Performance benchmarks met | [ ] GraphQL schema documented | [ ] Code review approved | [ ] E2E tests pass

## ⚠️ Risk
**Tech:** 💻 WebSocket connection stability → Implement exponential backoff reconnection with max 5 retries | **Biz:** 📊 Message delivery failures → Add retry queue with persistence to localStorage | **Time:** ⏰ Real-time sync complexity → Implement HTTP polling fallback first, add WebSocket incrementally | **Deps:** 🔗 US6.2 auth delays → Use mock Clerk tokens for development, implement auth middleware stub

## 📊 Est
**Pts:** 🎯 5 points (moderate complexity with clear requirements) | **Hrs:** ⏱️ 16h (8h dev + 4h test + 4h review) | **Complex:** 🧩 GraphQL subscription setup, state management | **Conf:** 📈 High - well-defined requirements | **Vars:** 🔄 Real-time requirements may add complexity

## 📦 Data
**API:** 🔌 Request: {conversationId: "550e8400-e29b-41d4-a716-************", content: {parts: [{type: "text", content: "Hello PA"}]}} | Response: {id: "660e8400-e29b-41d4-a716-************", conversation: {id: "...", user: {...}, assistant: {...}}, senderRole: "user", content: {parts: [{type: "text", content: "Hello PA"}]}, timestamp: "2024-01-15T10:30:00Z"} | **DB:** 🗄️ INSERT INTO chat_messages (id, conversation_id, sender_role, content, timestamp) VALUES (gen_random_uuid(), $1, $2, $3, now()) with INDEX ON (conversation_id, timestamp) | **UI:** 🖥️ Zustand store: {messages: Message[], sending: boolean, error: Error | null, optimisticMessages: Message[], retryQueue: Message[]} | **Config:** ⚙️ GRAPHQL_ENDPOINT, WS_ENDPOINT, MAX_MESSAGE_LENGTH=10000, RATE_LIMIT_PER_MINUTE=10

## 🎨 Visual
**Layout:** 📐
```
┌─────────────────────────────────────┐
│ Chat with Personal Assistant        │
├─────────────────────────────────────┤
│ ┌─ PA: Hello! How can I help?       │
│ └─ You: [Previous message]          │
│                                     │
├─────────────────────────────────────┤
│ [Type your message...]    [Send]    │
└─────────────────────────────────────┘
```

## 🤔 Assume
**Sys:** 🖥️ Conversations table exists, user authenticated via Clerk | **User:** 👤 Has created PA, has active conversation | **Env:** 🌐 Stable internet, WebSocket support | **Data:** 📊 Text-only messages for MVP, JSON message format

## 🎬 Context
**Before:** Files:📁 Basic UI components exist | Schema:🗄️ conversations table with user_id | Components:⚙️ Apollo Client configured | Config:⚙️ GraphQL endpoint available
**After:** Files:📁 ChatInput.tsx, useSendMessage hook created | Schema:🗄️ chat_messages populated | Components:⚙️ sendMessage mutation integrated | Config:⚙️ No changes

## 📝 Tasks
1. **Create sendMessage GraphQL mutation** - Backend implementation
```coder_llm
**Do:** 🎯 Create sendMessage mutation in Strawberry GraphQL with input validation and error handling | **File:** 📁 apps/backend/src/a2a_platform/api/graphql/schemas/chat_schemas.py (create new) | **Target:** 🎯 @strawberry.type class ChatMutation with async def send_message method | **Check:** ✅ Validates content length, sanitizes input, persists to DB, returns typed ChatMessage | **Needs:** 🔧 Import ChatMessage model, auth_required decorator, bleach for sanitization
```

2. **Implement message persistence** - Database layer
```coder_llm
**Do:** 🎯 Create ChatMessage SQLAlchemy model with proper constraints and indexes | **File:** 📁 apps/backend/src/a2a_platform/db/models/chat_message.py (create new) | **Target:** 🎯 class ChatMessage(Base) with __tablename__ = "chat_messages" | **Check:** ✅ Foreign key to conversations, sender_role CHECK constraint, composite index on (conversation_id, timestamp) | **Needs:** 🔧 Import Base from db.base_class, UUID type, relationship to Conversation model
```

3. **Create ChatInput component** - Frontend UI
```coder_llm
**Do:** 🎯 Build accessible controlled input component with validation and keyboard shortcuts | **File:** 📁 apps/web/src/components/chat/ChatInput.tsx (create new) | **Target:** 🎯 export const ChatInput: React.FC<ChatInputProps> with textarea and send button | **Check:** ✅ Validates empty/long messages, Enter to send, Shift+Enter for newline, disabled while sending, ARIA labels | **Needs:** 🔧 Import Button, Textarea from components/ui, useSendMessage hook
```

4. **Implement useSendMessage hook** - Frontend logic
```coder_llm
**Do:** 🎯 Create custom hook for sendMessage mutation | **File:** 📁 apps/web/src/hooks/useSendMessage.ts | **Target:** 🎯 Apollo useMutation wrapper with error handling | **Check:** ✅ Handles loading, error, optimistic updates | **Needs:** 🔧 GraphQL mutation definition
```

5. **Add message to chat history** - State management
```coder_llm
**Do:** 🎯 Create Zustand store with optimistic updates and retry queue | **File:** 📁 apps/web/src/stores/chatStore.ts (create new) | **Target:** 🎯 interface ChatStore with addMessage, removeOptimisticMessage, addToRetryQueue actions | **Check:** ✅ Optimistic updates with unique temp IDs, rollback on error, persist retry queue to localStorage | **Needs:** 🔧 Import create from zustand, persist middleware, Message type definition
```

6. **Write integration tests** - Quality assurance
```coder_llm
**Do:** 🎯 Test complete message flow including error cases and rate limiting | **File:** 📁 apps/backend/tests/integration/test_chat_mutations.py (create new) | **Target:** 🎯 class TestChatMutations with test_send_message_success, test_validation_errors, test_rate_limiting | **Check:** ✅ Message persisted, response correct, validation works, rate limit enforced | **Needs:** 🔧 pytest fixtures for test DB, mock user auth, GraphQL test client
```

7. **Implement WebSocket reconnection** - Connection resilience
```coder_llm
**Do:** 🎯 Add reconnection logic to Apollo Client WebSocket link | **File:** 📁 apps/web/src/lib/apolloClient.ts | **Target:** 🎯 Update WebSocketLink configuration with reconnection options | **Check:** ✅ Exponential backoff, max 5 retries, fallback to HTTP, connection state in UI | **Needs:** 🔧 Import retry utilities, update link chain configuration
```

## 🔧 Refactor
**Quality:** 📏 Extract message validation to shared utility | **Perf:** ⚡ Implement message batching for high volume | **Reuse:** ♻️ Create generic mutation error handler | **Debt:** 💳 None - greenfield implementation | **Test:** 🧪 Aim for 90% coverage | **Docs:** 📚 Document GraphQL schema changes

---

## Usage
**Symbols:** {text}=required | [text]=optional | {text}|[default]=required+fallback
**Check:** [x] {} replaced | [x] [] evaluated | [x] ADRs ref'd | [x] Tasks actionable | [x] AC=G-W-T | [x] Deps ID'd
