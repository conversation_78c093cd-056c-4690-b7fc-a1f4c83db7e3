# 📋 US7.1: Send Text Message to Personal Assistant via Chat Interface

**Story:** As a user, I want to send a text message to my Personal Assistant via a chat interface so that I can communicate my requests and information.
**Steps:** Open chat → Type message → Send → Backend mutation → Persist message → Display in chat history
**Epic:** 🎯 Core Conversational Interface & Interaction

## ✅ AC
- [ ] Given I am in the chat interface with my PA, When I type a message in the input field and send it, Then the message is transmitted to the backend via a GraphQL mutation (e.g., sendMessage). [Source 1259, 1298]
- [ ] Given the backend receives the message, Then it is persisted in the chat_messages table, associated with the correct conversation. [Source 1307]
- [ ] Given the message is sent, Then it appears in my chat history in the UI.

## 🔗 Deps
**Stories:** US7.3(IN PROGRESS) | **Ext:** 🌐 GraphQL API | **Tech:** ⚙️ GraphQL, SQLAlchemy, React, Bun | **Data:** 🗄️ chat_messages, conversations | **Team:** 👥 FE/BE approval | [None]

## 💰 Value
**Biz:** 📈 Enables user-PA communication | **UX:** ✨ Real-time, seamless chat | **KPI:** 📊 Message delivery rate, latency | **Why:** 🎯 Core interaction channel

## 📐 ADRs
**Refs:** 📄 ADR-002, ADR-003 | **Limits:** ⚠️ Message size, rate limits | **Patterns:** 🏗️ CQRS, event-driven | [None]

## 🛠️ Impl
**Arch:** 🏛️ GraphQL mutation/resolver, async backend | **Data:** 🗄️ chat_messages, conversations | **API:** 🔌 sendMessage mutation | **Sec:** 🔒 Auth required | **Perf:** ⚡ Sub-200ms delivery | **Int:** 🌐 None | **Err:** ❌ Input validation, DB errors | **Std:** 📏 Consistent error handling | [Standard]

## 🧪 Test
**Unit:** 🔬 sendMessage resolver | **Int:** 🔗 DB persistence | **Contract:** 📋 GraphQL schema | **E2E:** 🎭 User sends/receives message | **Perf:** 🚀 Message latency | **Sec:** 🛡️ Auth required | [Standard]

## 📏 NFRs
**Perf:** ⚡ <200ms message roundtrip | **A11y:** ♿ Keyboard accessible | **Sec:** 🔒 Auth enforced | **Scale:** 📈 10k concurrent users | **Compat:** 🌍 Chrome, Firefox, Safari | [Standard]

## ✅ DoD
- [ ] AC met | [ ] Tests pass | [ ] Security review | [ ] Perf benchmarks | [ ] Docs updated | [ ] Code review | [ ] QA complete

## ⚠️ Risk
**Tech:** 💻 GraphQL/DB sync issues→retry logic | **Biz:** 📊 User confusion if message lost→UI feedback | **Time:** ⏰ Real-time perf tuning | **Deps:** 🔗 US7.3 delay→stub fallback | [None]

## 📊 Est
**Pts:** 🎯 3 (core, well-scoped) | **Hrs:** ⏱️ 8 dev, 4 test, 2 review | **Complex:** 🧩 GraphQL/DB edge cases | **Conf:** 📈 M (standard chat, some async) | **Vars:** 🔄 Real-time, schema changes | [None]

## 📦 Data
**API:** 🔌 sendMessage(input: {conversationId, text}) → Message | **DB:** 🗄️ chat_messages (id, conversation_id, sender_id, text, created_at) | **UI:** 🖥️ Chat history state | **Config:** ⚙️ DATABASE_URL, POSTGRES_* | [None]

## 🎨 Visual
**Layout:** 📐 Chat input, message list | **Flow:** 🌊 User→GraphQL→DB→UI | **States:** 🔄 Sending, sent, error | **Seq:** 📋 User types→sends→backend→DB→UI update | [None]

## 🤔 Assume
**Sys:** 🖥️ DB, GraphQL API, FE/BE up | **User:** 👤 Authenticated, has PA | **Env:** 🌐 Modern browser, Docker | **Data:** 📊 Valid conversation/message | [Standard]

## 🎬 Context
**Before:** Files:📁chat UI, backend GraphQL, DB models | Schema:🗄️chat_messages, conversations | Components:⚙️sendMessage, chat UI | Config:⚙️DATABASE_URL | [Standard]
**After:** Files:📁sendMessage mutation/resolver, tests | Schema:🗄️chat_messages updated | Components:⚙️chat UI, backend | Config:⚙️No change | [None]
