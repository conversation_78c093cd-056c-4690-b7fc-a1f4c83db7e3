# 📋 [DRAFT] User Story Specification: US7.1

🎯 **Feature:** Send Text Message to Personal Assistant via Chat Interface
*US7.1 - Core Conversational Interface & Interaction*

📝 **Description**
{As a user, I want to send a text message to my Personal Assistant via a chat interface, so that I can communicate my requests and information.}

✅ **Acceptance Criteria**
1. ✨ Message Transmission
```gherkin
GIVEN I am in the chat interface with my PA
WHEN I type a message in the input field and send it
THEN the message is transmitted to the backend via GraphQL mutation 'sendMessage'
```

2. 💾 Message Persistence
```gherkin
GIVEN the backend receives the message
THEN it is persisted in the chat_messages table
AND it is associated with the correct conversation
```

3. 🔄 UI Update
```gherkin
GIVEN the message is sent
THEN it appears in my chat history in the UI
```

🔗 **Dependencies**
- Frontend chat UI components (apps/frontend/src/components/chat)
- GraphQL mutation `sendMessage` and resolver implementation [Source 1298]
- Database tables: `chat_messages` and `conversations` [Source 1305, 1307]
- US7.3: Real-time message display functionality

📐 **Technical Architecture**
*References: ADR-001 Sec 2.3, ADR-002 Sec 3.1, ADR-003 Sec 2.2*

1. 🎨 Frontend:
   - React components using Chakra UI for chat interface
   - Apollo Client for GraphQL communication
   - Real-time WebSocket subscription for message updates
   - Message cache management via Apollo Cache

2. 🔄 API Layer:
   - GraphQL mutation: `sendMessage(input: SendMessageInput!): SendMessagePayload!`
   - WebSocket subscription: `onMessageSent(conversationId: ID!): Message!`
   - Error handling with GraphQL error types

3. 💾 Database:
   - Tables: chat_messages, conversations
   - Foreign key relationships for message-conversation association
   - Indexes for efficient message retrieval

🛠️ **Implementation Notes**

1. GraphQL Schema:
```graphql
input SendMessageInput {
  conversationId: ID!
  content: String!
}

type SendMessagePayload {
  message: Message!
  conversation: Conversation!
}

type Message {
  id: ID!
  content: String!
  timestamp: DateTime!
  conversation: Conversation!
}
```

2. Database Schema:
```sql
CREATE TABLE chat_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  content TEXT NOT NULL,
  conversation_id UUID NOT NULL REFERENCES conversations(id),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_chat_messages_conversation_id ON chat_messages(conversation_id);
```

📝 **Tasks**
1. 🎨 Frontend Implementation
   - Create ChatInput component with text input and send button
   - Implement sendMessage GraphQL mutation hook
   - Add message to Apollo Cache on successful send
   - Show loading/error states during transmission

2. 🔄 Backend Implementation
   - Define sendMessage GraphQL mutation schema
   - Implement mutation resolver with database persistence
   - Add message validation and sanitization
   - Set up proper error handling

3. 💾 Database Implementation
   - Create migration for chat_messages table
   - Add indexes for performance optimization
   - Implement message-conversation association

⚠️ **Risks**
1. Message delivery failure handling
2. Network connectivity issues
3. Race conditions in real-time updates
4. Message order consistency

📏 **Non-Functional Requirements**
1. Performance
   - Message sending latency < 500ms
   - UI update latency < 100ms
2. Security
   - Input sanitization
   - Conversation access control
3. Reliability
   - Message persistence guarantees
   - Offline message queueing

💰 **Business Value**
- Enables core communication functionality
- Provides immediate user feedback
- Foundation for conversational interface

🎬 **Context Boundaries**
- Start: User opens chat interface
- End: Message persisted and displayed in UI

🤔 **Assumptions**
1. User has active conversation
2. WebSocket connection available
3. Database operational

📊 **Success Metrics**
1. Message delivery success rate > 99.9%
2. Average message round-trip time < 1s
3. Zero data loss incidents
