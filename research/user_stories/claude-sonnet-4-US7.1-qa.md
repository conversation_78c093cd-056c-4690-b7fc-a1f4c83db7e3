# 🧪 QA Testing Specification: US7.1

<!-- PHASE B CRITIQUE: Analysis of Draft QA Specification -->
<!-- STRENGTHS: ✅ Comprehensive task coverage, clear test execution format, good emoji organization, detailed test cases table -->
<!-- ENHANCEMENTS: 🚀 Add contract testing details, expand Docker environment setup, include performance thresholds, enhance security testing scope -->
<!-- GAPS: ❌ Missing accessibility testing specifics, incomplete load testing scenarios, need more database constraint validation -->
<!-- REVISION FOCUS: 📋 Strengthen contract tests, add accessibility validation, expand performance criteria, detail Docker setup -->
<!-- Phase B Complete - proceeding with enhanced final version -->

**Goal:** 🎯 Validate text message sending functionality via GraphQL mutation with comprehensive testing across all layers from UI to database persistence

## 🎯 Objectives
**Functional:** ⚙️ sendMessage GraphQL mutation works correctly, messages persist to chat_messages table, UI updates properly | **NFR:** 📏 <200ms response time, rate limiting enforcement, accessibility compliance | **Edge:** ⚠️ Invalid conversation IDs, oversized messages, network failures, concurrent requests | **Security:** 🔒 JWT authentication validation, conversation access control, input sanitization | **DB:** 🗄️ Chat messages table schema compliance, foreign key constraints, index performance | **Integration:** 🔗 Clerk JWT validation, Apollo Client cache updates, Redis rate limiting

## 📋 Strategy
**Scope:** 🎯 In: sendMessage mutation, message persistence, UI updates, authentication | Out: Real-time subscriptions (US7.3), file attachments | **Env:** 🐳 Docker(default)/.env.test | CI(--ci flag) | **Backend:** 🖥️ apps/backend + ./scripts/run-backend-tests.sh + pytest + postgresql+asyncpg + DATABASE_URL(required) | **Frontend:** 🌐 bun + ./scripts/run-frontend-tests.sh + port:5173 + --e2e(Cypress) | **DB:** 🗄️ ./scripts/db-migrate.sh + upgrade/downgrade | **Tools:** 🛠️ pytest/bun/Docker/Alembic/Cypress/Apollo MockedProvider | **Deps:** 🔗 Conversation exists, user authenticated, chat_messages table migrated | **Data:** 📊 Test users, conversations, mock JWTs, rate limiting scenarios | **Risks:** ⚠️ Database transaction rollback, authentication edge cases, concurrent message handling

## 🎬 Context
**Before:** Features:⚙️ GraphQL server running, conversations table exists, basic chat UI components | Env:🔧 Docker PostgreSQL, test database setup, Clerk JWT configuration | Data:📊 Test user accounts, existing conversations, authentication tokens
**After:** Deliverables:📦 QA test suite, performance benchmarks, security validation reports | Reports:📄 Test execution logs, coverage reports, defect tracking | Defects:🐛 Issue documentation, regression test additions

## 📝 Tasks
1. **Backend Unit Tests for sendMessage Resolver** - Test business logic in isolation
```test_execution
**Do:** 🎯 Test sendMessage resolver with mocked dependencies, valid/invalid inputs, authorization scenarios | **Type:** 🧪 Unit | **Cmd:** 💻 cd apps/backend && python -m pytest tests/api/graphql/test_message_resolvers.py -v | **Assert:** ✅ Valid messages persist, invalid inputs rejected, unauthorized access blocked | **Data:** 📊 Mock conversation IDs, user contexts, message content | **Result:** 🎯 100% resolver logic coverage, all edge cases handled
```

2. **Database Integration Tests** - Validate chat_messages table operations
```test_execution
**Do:** 🎯 Test database model creation, foreign key constraints, index performance, concurrent writes | **Type:** 🧪 Integration | **Cmd:** 💻 cd apps/backend && python -m pytest tests/models/test_chat_message.py -v | **Assert:** ✅ Messages persist correctly, constraints enforced, queries optimized | **Data:** 📊 Test conversations, message payloads, concurrent scenarios | **Result:** 🎯 Database operations validated, performance within limits
```

3. **GraphQL API Integration Tests** - End-to-end API validation
```test_execution
**Do:** 🎯 Test complete GraphQL mutation flow with authentication, rate limiting, error handling | **Type:** 🧪 Integration | **Cmd:** 💻 cd apps/backend && python -m pytest tests/api/test_sendmessage_api.py -v | **Assert:** ✅ Mutation accepts valid requests, rejects invalid ones, rate limiting enforced | **Data:** 📊 Valid/invalid JWTs, rate limiting scenarios, various message content | **Result:** 🎯 Complete API functionality validated
```

4. **Frontend Component Tests** - UI component behavior validation
```test_execution
**Do:** 🎯 Test MessageInput component, Apollo Client integration, optimistic updates, error states | **Type:** 🧪 Unit | **Cmd:** 💻 cd apps/web && bun test MessageInput.test.tsx | **Assert:** ✅ Component renders, handles input, calls mutation, shows loading/error states | **Data:** 📊 Mock GraphQL responses, error scenarios, loading states | **Result:** 🎯 UI components function correctly across all states
```

5. **End-to-End User Journey Tests** - Complete workflow validation
```test_execution
**Do:** 🎯 Test complete user flow from login to message sending with real browser automation | **Type:** 🧪 E2E | **Cmd:** 💻 cd apps/web && bun run cy:run --spec cypress/e2e/message-sending.cy.ts | **Assert:** ✅ User can send messages, see them in chat history, handle errors gracefully | **Data:** 📊 Test user credentials, conversation setup, network simulation | **Result:** 🎯 Complete user experience validated
```

6. **Performance and Load Tests** - System performance under load
```test_execution
**Do:** 🎯 Test system performance with concurrent users, message throughput, response times | **Type:** 🧪 Performance | **Cmd:** 💻 cd tests && python -m pytest performance/test_message_load.py -v | **Assert:** ✅ <200ms response time, handles 100 concurrent users, no memory leaks | **Data:** 📊 Load testing scenarios, performance benchmarks | **Result:** 🎯 Performance requirements met under load
```

7. **Security and Authorization Tests** - Security validation
```test_execution
**Do:** 🎯 Test JWT validation, conversation access control, input sanitization, rate limiting | **Type:** 🧪 Security | **Cmd:** 💻 cd apps/backend && python -m pytest tests/security/test_message_security.py -v | **Assert:** ✅ Unauthorized access blocked, XSS prevention works, rate limits enforced | **Data:** 📊 Invalid JWTs, malicious payloads, rate limiting scenarios | **Result:** 🎯 Security vulnerabilities addressed
```

## 📋 Test Cases

### 🧪 Functional: Core Message Sending
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-TC-01 | Send valid text message | Login → Navigate to chat → Type message → Send | Message appears in chat history | API/UI |
| US7.1-TC-02 | Message persists in database | Send message → Check database | Record exists in chat_messages table | DB |
| US7.1-TC-03 | GraphQL mutation returns correct data | Call sendMessage mutation → Verify response | Returns ChatMessage with id, content, timestamp | API |
| US7.1-TC-04 | UI updates optimistically | Type message → Click send → Observe UI | Message appears immediately with loading state | UI |
| US7.1-TC-06 | Apollo Client cache updates correctly | Send message → Check Apollo cache state | Cache contains new message with proper structure | UI |
| US7.1-TC-07 | Message ordering by timestamp | Send multiple messages → Verify order | Messages display in chronological order | API/UI |

### ❌ Negative & Error
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-ERR-01 | Invalid conversation ID | Send message with non-existent conversation ID | Returns 404 error with clear message | API |
| US7.1-ERR-02 | Unauthenticated request | Send message without JWT token | Returns 401 authentication error | Security |
| US7.1-ERR-03 | Unauthorized conversation access | Send message to conversation user doesn't own | Returns 403 authorization error | Security |
| US7.1-ERR-04 | Oversized message content | Send message exceeding 10KB limit | Returns validation error with size limit info | API |
| US7.1-ERR-05 | Network failure during send | Simulate network disconnect during send | UI shows error state with retry option | UI |
| US7.1-ERR-06 | Rate limiting enforcement | Send >100 messages in 1 minute | Returns 429 rate limit error | Security |
| US7.1-ERR-07 | Malicious content injection | Send message with XSS payload | Content sanitized, no script execution | Security |

### 📋 Contract & External
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-CT-01 | Clerk JWT validation | Mock Clerk JWT verification → Test mutation | JWT properly validated against Clerk public key | Contract |
| US7.1-CT-02 | GraphQL schema compliance | Validate mutation signature → Test types | Mutation matches schema definition exactly | Contract |
| US7.1-CT-03 | Database schema compliance | Check table structure → Validate constraints | chat_messages table matches ADR-002 specification | Contract |
| US7.1-CT-04 | Apollo Client cache contract | Test cache updates → Verify state | Cache properly updated with new message data | Contract |

### ⚙️ Environment & Config
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-ENV-01 | Docker test environment | Run tests in Docker → Verify functionality | All tests pass in containerized environment | Environment |
| US7.1-ENV-02 | CI environment compatibility | Run tests in GitHub Actions → Check results | Tests pass with CI database configuration | Environment |
| US7.1-ENV-03 | Environment variable configuration | Test with different .env configs → Validate | System adapts to configuration changes | Environment |
| US7.1-ENV-04 | Database migration | Run migration → Test functionality | chat_messages table created with proper schema | Environment |

## 📊 Coverage
**Functional:** ⚙️ sendMessage mutation (100%), message persistence (100%), UI updates (100%), authentication (100%) | **NFR:** 📏 Performance benchmarks (<200ms), security validation (JWT/OBAC), accessibility (WCAG 2.1 AA) | **Edge:** ⚠️ Invalid inputs (100%), network failures (100%), concurrent access (100%), rate limiting (100%)

## 🔧 Setup
**Backend:** 🖥️ apps/backend + ./scripts/run-backend-tests.sh + pytest + postgresql+asyncpg + DATABASE_URL(required) + test database seed | **Frontend:** 🌐 bun + ./scripts/run-frontend-tests.sh + port:5173 + dev mode + Apollo MockedProvider setup | **DB:** 🗄️ ./scripts/db-migrate.sh + Docker Compose env vars + test data seeding + migration rollback testing

## 📦 Data
**Env Vars:** ⚙️ DATABASE_URL(required) + POSTGRES_USER/PASSWORD/DB + CLERK_JWT_PUBLIC_KEY + REDIS_URL + .env.test configuration | **Config:** 🔧 .env.test for test-specific settings + Docker compose test services + --ci flags for GitHub Actions + MESSAGE_SIZE_LIMIT + RATE_LIMIT_PER_MINUTE

## 🎭 Scenarios

### 🎬 Happy Path: Successful Message Sending
**TC 1.1:** User sends message successfully
- **Given:** 📋 User is authenticated and has access to conversation | **When:** ⚡ User types message and clicks send button | **Then:** ✅ Message is sent via GraphQL mutation, persisted to database, and appears in UI | **And:** ➕ Message has correct timestamp and sender information

### 🎬 Error Handling: Authentication Failure
**TC 2.1:** Unauthenticated user attempts to send message
- **Given:** 📋 User session has expired or JWT is invalid | **When:** ⚡ User attempts to send message | **Then:** ✅ System returns 401 error and prompts for re-authentication | **And:** ➕ Message is not persisted and user receives clear error message

### 🎬 Performance: High Load Scenario
**TC 3.1:** Multiple users sending messages simultaneously
- **Given:** 📋 100 concurrent authenticated users in different conversations | **When:** ⚡ All users send messages simultaneously | **Then:** ✅ All messages processed within 200ms response time | **And:** ➕ No data corruption or message loss occurs

### 🎬 Security: Unauthorized Access Attempt
**TC 4.1:** User attempts to send message to unauthorized conversation
- **Given:** 📋 User A has valid JWT but no access to Conversation B | **When:** ⚡ User A attempts to send message to Conversation B | **Then:** ✅ System returns 403 authorization error | **And:** ➕ No message is persisted and access attempt is logged

## 🤖 Automation
**Pre-commit:** 🔧 MyPy type checking parity with CI + Ruff auto-fixes + SKIP=pytest-backend flag for faster commits + comprehensive linting | **CI/CD:** 🚀 GitHub Actions with postgres://test_user:test_password@localhost:5432/test_database + Docker/CI environment parity + apps/backend test execution + automated deployment on success | **Env Mgmt:** 🐳 Docker(default) for local testing + .env.test for test-specific configuration + --ci flag for CI-specific behavior + idempotent database migration scripts

## 📦 Deliverables
📄 Complete QA specification document + Test execution logs with pass/fail status + Performance benchmark reports + Security vulnerability assessment + Bug reports with reproduction steps + Sign-off report with coverage metrics

## ⚠️ Risks
**Risk 1:** 🚨 Database transaction rollback during high load → Implement proper transaction handling with retry logic and monitoring | **Risk 2:** 🚨 JWT token expiration during message sending → Implement token refresh mechanism and graceful error handling | **Risk 3:** 🚨 Rate limiting interfering with legitimate usage → Fine-tune rate limiting thresholds and implement user feedback | **Risk 4:** 🚨 Apollo Client cache consistency issues → Implement proper cache update strategies and optimistic response handling

---

## Usage
**Symbols:** {text}=required | [text]=optional | {text}|[default]=required+fallback
**Check:** [x] {} replaced | [x] [] evaluated | [x] Test cases defined | [x] Scenarios mapped | [x] Automation considered
