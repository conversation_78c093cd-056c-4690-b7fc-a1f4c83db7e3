# 📋 User Story Specification: US7.1

**Story:** As a user, I want to send a text message to my Personal Assistant via a chat interface so that I can communicate my requests and information.
**Steps:** Open chat interface → Type message → Send via GraphQL → Backend persistence → UI display
**Epic:** 🎯 Core Conversational Interface & Interaction

## ✅ AC
- [ ] Given I am in the chat interface with my <PERSON>, When I type a message in the input field and send it, Then the message is transmitted to the backend via a GraphQL mutation (e.g., sendMessage) [Source 1259, 1298]
- [ ] Given the backend receives the message, When the sendMessage mutation is called, Then it is persisted in the chat_messages table, associated with the correct conversation [Source 1307]
- [ ] Given the message is sent, When the backend confirms persistence, Then it appears in my chat history in the UI with proper formatting and timestamp
- [ ] Given an error occurs during sending, When the mutation fails, Then the user receives appropriate error feedback and retry options
- [ ] Given unauthorized access attempt, When user lacks conversation access, Then mutation returns authorization error with 403 status

## 🔗 Deps
**Stories:** US7.3(BLOCKED - Real-time message display) | **Ext:** 🌐 Clerk@v1 JWT validation | **Tech:** ⚙️ Strawberry GraphQL, Apollo Client, React, TypeScript | **Data:** 🗄️ chat_messages table schema, conversations table schema per ADR-002 Sec 4 | **Team:** 👥 None

## 💰 Value
**Biz:** 📈 Enable core conversational AI interaction, foundational for product value proposition and user engagement | **UX:** ✨ Seamless messaging experience enabling natural communication with Personal Assistant | **KPI:** 📊 Message send success rate >99%, response time <200ms, user engagement >80% | **Why:** 🎯 Critical path for MVP functionality, enables primary user value of AI assistant interaction

## 📐 ADRs
**Refs:** 📄 ADR-002 Sec 2.5 (Chat Interface Support), ADR-002 Sec 4 (Data Model), ADR-002 Sec 6 (GraphQL Schema), ADR-003 Sec 3.4 (Frontend Chat Interface) | **Limits:** ⚠️ Message size limit 10KB, rate limiting 100 msgs/min per user, WebSocket connection limits | **Patterns:** 🏗️ Strawberry GraphQL mutations, Apollo Client optimistic updates, OBAC authorization patterns

## 🛠️ Impl
**Arch:** 🏛️ GraphQL mutation sendMessage → Resolver validation → Database persistence via chat_messages table → Apollo cache update → UI state synchronization | **Data:** 🗄️ chat_messages(id UUID PRIMARY KEY, conversation_id UUID NOT NULL REFERENCES conversations(id), sender_role TEXT CHECK IN ('user', 'agent'), content JSONB NOT NULL, timestamp TIMESTAMPTZ DEFAULT now(), metadata JSONB DEFAULT '{}') per ADR-002 | **API:** 🔌 sendMessage(conversationId: UUID!, content: JSON!): ChatMessage! returning {id, content, timestamp, senderRole, conversationId} | **Sec:** 🔒 Clerk JWT validation via middleware, OBAC conversation access control, input sanitization with bleach library, Redis-based rate limiting (100 msgs/min) | **Perf:** ⚡ <200ms mutation response time, async processing with connection pooling, Apollo cache optimization with optimistic updates | **Int:** 🌐 Apollo Client cache writeQuery for immediate UI updates, GraphQL subscription preparation for US7.3 | **Err:** ❌ Network retry logic with exponential backoff, validation error handling with specific messages, authentication failure responses | **Std:** 📏 GraphQL best practices with Strawberry decorators, TypeScript strict mode, React component patterns with hooks

## 🧪 Test
**Unit:** 🔬 sendMessage resolver logic with mock database, message validation functions with edge cases, conversation access checks with unauthorized scenarios, content sanitization with XSS attempts | **Int:** 🔗 GraphQL endpoint testing with real database, authentication middleware integration with valid/invalid JWTs, rate limiting with Redis backend | **Contract:** 📋 Clerk JWT verification with token validation, GraphQL schema compliance with type checking, API contract validation with OpenAPI specs | **E2E:** 🎭 Complete message send workflow from UI to database, error scenario handling with network failures, rate limiting behavior with rapid requests | **Perf:** 🚀 Load testing with 100 concurrent users sending messages, mutation response time benchmarks, database query optimization validation | **Sec:** 🛡️ SQL injection prevention with parameterized queries, XSS protection with content sanitization, unauthorized access attempts with invalid JWTs, rate limiting enforcement with Redis

## 📏 NFRs
**Perf:** ⚡ <200ms GraphQL mutation response, <100ms UI update latency, 1000+ concurrent users support | **A11y:** ♿ WCAG 2.1 AA compliance for message input, screen reader support, keyboard navigation | **Sec:** 🔒 JWT token validation, input sanitization with bleach, SQL injection prevention, rate limiting | **Scale:** 📈 10K messages/hour throughput, Redis-based session management, horizontal scaling support | **Compat:** 🌍 Chrome 90+, Firefox 88+, Safari 14+, mobile responsive design

## ✅ DoD
- [ ] AC met with automated tests | [ ] Unit/integration/E2E tests pass | [ ] Security review completed | [ ] Performance benchmarks met | [ ] API documentation updated | [ ] Code review approved | [ ] QA testing complete

## ⚠️ Risk
**Tech:** 💻 GraphQL subscription complexity for real-time updates→Start with mutations only, defer subscriptions to US7.3 | **Biz:** 📊 Message ordering/consistency issues→Implement timestamp-based ordering with proper indexing | **Time:** ⏰ Authentication integration complexity→Parallel development of auth middleware | **Deps:** 🔗 Database schema coordination with US7.3→Early schema alignment meetings

## 📊 Est
**Pts:** 🎯 5 points (medium complexity with well-defined scope and established patterns) | **Hrs:** ⏱️ 16h development + 8h testing + 4h review = 28h total | **Complex:** 🧩 GraphQL resolver patterns, real-time UI state management, authentication integration | **Conf:** 📈 High confidence (established patterns in ADR-002, clear requirements, existing auth infrastructure) | **Vars:** 🔄 Authentication edge cases, UI state synchronization challenges, rate limiting configuration

## 📦 Data
**API:** 🔌 Input: SendMessageInput{conversationId: UUID!, content: JSON!}, Output: ChatMessage{id: UUID!, conversationId: UUID!, senderRole: String!, content: JSON!, timestamp: DateTime!, metadata: JSON} | **DB:** 🗄️ INSERT INTO chat_messages(conversation_id, sender_role, content, timestamp, metadata) with UNIQUE index on (conversation_id, timestamp) for ordering | **UI:** 🖥️ Apollo Client cache.writeQuery to update message list, optimistic response with temporary ID, error state management | **Config:** ⚙️ MESSAGE_SIZE_LIMIT=10KB, RATE_LIMIT_PER_MINUTE=100, JWT_PUBLIC_KEY from Clerk, REDIS_URL for rate limiting

## 🎨 Visual
**Layout:** 📐 Chat input field at bottom with send button right-aligned, message list above with scrolling, loading indicators during send | **Flow:** 🌊 User types message → Click send → Loading state → Optimistic UI update → Backend confirmation → Final UI state | **States:** 🔄 idle, typing, sending, sent, error, retry | **Seq:** 📋 User input → GraphQL mutation → Database write → Apollo cache update → UI refresh

## 🤔 Assume
**Sys:** 🖥️ PostgreSQL database available, GraphQL server running, authentication middleware active, Redis for rate limiting | **User:** 👤 Authenticated user with valid JWT, existing conversation access, modern browser support | **Env:** 🌐 Stable network connection, JavaScript enabled, WebSocket support available | **Data:** 📊 UTF-8 text content, maximum 10KB message size, valid conversation exists in database

## 🎬 Context
**Before:** Files:📁 apps/backend/src/a2a_platform/api/graphql/__init__.py (basic mutations), apps/web/src/graphql/mutations.tsx (existing patterns) | Schema:🗄️ conversations table exists, users table with Clerk integration | Components:⚙️ Apollo Client configured, basic chat UI components, authentication middleware | Config:⚙️ DATABASE_URL, CLERK_JWT_PUBLIC_KEY, basic GraphQL setup
**After:** Files:📁 sendMessage resolver implemented, message mutation types defined, MessageInput UI component functional | Schema:🗄️ chat_messages table with proper indexes and foreign keys | Components:⚙️ Complete GraphQL sendMessage endpoint, functional message input with validation | Config:⚙️ Rate limiting configuration, message size limits, error handling patterns

## 📝 Tasks
1. **Create sendMessage GraphQL mutation schema** - Define mutation signature and return types
```coder_llm
**Do:** 🎯 Add sendMessage(conversationId: UUID!, content: JSON!): ChatMessage! to GraphQL schema with input validation for content size and conversation existence | **File:** 📁 apps/backend/src/a2a_platform/api/graphql/schemas/chat_schemas.py | **Target:** 🎯 ChatMutation class with @strawberry.field decorated sendMessage method | **Check:** ✅ Schema validates input types, mutation accepts UUID and JSON, returns complete ChatMessage with all fields | **Needs:** 🔧 Strawberry GraphQL decorators, ChatMessage type with id/content/timestamp/senderRole fields | New
```

2. **Implement sendMessage resolver logic** - Business logic for message persistence and validation
```coder_llm
**Do:** 🎯 Create async resolver with Clerk JWT authentication, conversation ownership validation, content sanitization with bleach, database persistence with error handling | **File:** 📁 apps/backend/src/a2a_platform/api/graphql/resolvers/chat_resolvers.py | **Target:** 🎯 resolve_send_message async function with proper error handling and logging | **Check:** ✅ Validates user authentication, checks conversation access via OBAC, sanitizes content, persists with proper sender_role | **Needs:** 🔧 Authentication context from Clerk, database session, bleach for sanitization, logging setup | New
```

3. **Create ChatMessage database model** - SQLAlchemy ORM model for chat_messages table
```coder_llm
**Do:** 🎯 Define ChatMessage model with exact ADR-002 schema including constraints, relationships to Conversation, and optimized indexes | **File:** 📁 apps/backend/src/a2a_platform/models/chat_message.py | **Target:** 🎯 ChatMessage class with __tablename__, columns, relationships, and proper typing | **Check:** ✅ Model matches database schema, foreign key relationships work, indexes optimize conversation queries | **Needs:** 🔧 SQLAlchemy imports, UUID and DateTime types, relationship to Conversation model | New
```

4. **Add database migration for chat_messages table** - Alembic migration for new table structure
```coder_llm
**Do:** 🎯 Create Alembic migration script for chat_messages table with columns per ADR-002 specification | **File:** 📁 apps/backend/alembic/versions/[timestamp]_create_chat_messages_table.py | **Target:** 🎯 Migration with table creation and proper indexes | **Check:** ✅ Migration runs successfully, table created with constraints, indexes optimize performance | **Needs:** 🔧 Alembic commands, PostgreSQL UUID and JSONB types | New
```

5. **Implement message input validation** - Content validation and sanitization utilities
```coder_llm
**Do:** 🎯 Create validation functions for message content size, format, and sanitization to prevent XSS/injection | **File:** 📁 apps/backend/src/a2a_platform/utils/message_validation.py | **Target:** 🎯 validate_message_content, sanitize_content functions | **Check:** ✅ Validates size limits, sanitizes content, prevents malicious input | **Needs:** 🔧 bleach library for sanitization, content size checking | New
```

6. **Add authentication middleware for GraphQL** - Ensure mutation requires valid authentication
```coder_llm
**Do:** 🎯 Implement authentication check in sendMessage resolver using Clerk JWT validation | **File:** 📁 apps/backend/src/a2a_platform/api/middleware/auth.py | **Target:** 🎯 require_authentication decorator or context check | **Check:** ✅ Blocks unauthenticated requests, validates JWT tokens, provides user context | **Needs:** 🔧 Clerk JWT verification, GraphQL context access | Multiple
```

7. **Implement rate limiting for messages** - Prevent spam and abuse with Redis-based limiting
```coder_llm
**Do:** 🎯 Add rate limiting middleware checking Redis for message send frequency per user | **File:** 📁 apps/backend/src/a2a_platform/api/middleware/rate_limit.py | **Target:** 🎯 rate_limit_messages decorator with configurable limits | **Check:** ✅ Limits requests to 100/minute per user, uses Redis storage, returns proper errors | **Needs:** 🔧 Redis client, sliding window algorithm, error response handling | New
```

8. **Create Apollo Client sendMessage mutation** - Frontend GraphQL mutation with optimistic updates
```coder_llm
**Do:** 🎯 Define GraphQL mutation in Apollo Client with optimistic UI updates and error handling | **File:** 📁 apps/web/src/graphql/mutations.tsx | **Target:** 🎯 SEND_MESSAGE_MUTATION constant and useSendMessage hook | **Check:** ✅ Mutation sends properly, optimistic updates work, error states handled | **Needs:** 🔧 Apollo Client imports, GraphQL query syntax, TypeScript types | Multiple
```

9. **Build MessageInput React component** - UI component for message composition and sending
```coder_llm
**Do:** 🎯 Create React component with input field, send button, validation, loading states, and accessibility features | **File:** 📁 apps/web/src/components/chat/MessageInput.tsx | **Target:** 🎯 MessageInput component with TypeScript props | **Check:** ✅ Component renders, handles input, calls mutation, shows loading/error states | **Needs:** 🔧 React hooks, Apollo Client mutation hook, ARIA accessibility | New
```

10. **Implement message list updates** - Update UI to display newly sent messages
```coder_llm
**Do:** 🎯 Modify message list component to handle Apollo cache updates and display new messages with proper ordering | **File:** 📁 apps/web/src/components/chat/MessageList.tsx | **Target:** 🎯 MessageList component cache integration | **Check:** ✅ New messages appear immediately, proper ordering by timestamp, optimistic updates work | **Needs:** 🔧 Apollo cache reading/writing, message ordering logic | Multiple
```

11. **Add comprehensive error handling** - Handle network, validation, and authentication errors
```coder_llm
**Do:** 🎯 Implement error handling for all failure scenarios with user-friendly messages and retry options | **File:** 📁 apps/web/src/components/chat/MessageInput.tsx, apps/backend/src/a2a_platform/api/graphql/resolvers/chat_resolvers.py | **Target:** 🎯 Error handling in both frontend and backend | **Check:** ✅ All error types handled, user gets clear feedback, retry mechanisms work | **Needs:** 🔧 Error type definitions, user feedback components | Multiple
```

12. **Write comprehensive test suite** - Unit, integration, and E2E tests for message sending
```coder_llm
**Do:** 🎯 Create test files covering resolver logic, database operations, GraphQL API, and UI interactions | **File:** 📁 tests/backend/test_message_resolvers.py, tests/frontend/MessageInput.test.tsx, tests/e2e/message-sending.spec.ts | **Target:** 🎯 Complete test coverage for message sending workflow | **Check:** ✅ All tests pass, coverage >90%, edge cases covered | **Needs:** 🔧 pytest, React Testing Library, Playwright, test database setup | New
```

## 🔧 Refactor
**Quality:** 📏 Extract common GraphQL patterns into reusable utilities, improve error handling consistency | **Perf:** ⚡ Optimize database queries with proper indexing, implement connection pooling | **Reuse:** ♻️ Create reusable message validation utilities, common UI components for chat interface | **Debt:** 💳 Address authentication middleware integration complexity, simplify error handling patterns | **Test:** 🧪 Increase test coverage for edge cases, add performance benchmarks | **Docs:** 📚 Document GraphQL API patterns, message validation rules, UI component usage

---

## Usage
**Symbols:** {text}=required | [text]=optional | {text}|[default]=required+fallback
**Check:** [x] {} replaced | [x] [] evaluated | [x] ADRs ref'd | [x] Tasks actionable | [x] AC=G-W-T | [x] Deps ID'd
