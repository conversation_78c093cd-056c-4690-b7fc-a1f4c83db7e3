# 📋 US7.1: Send Text Message to Personal Assistant via Chat Interface

**Story:** As a user, I want to send a text message to my Personal Assistant via a chat interface, so that I can communicate my requests and information.
**Steps:** Type message → Send message → Message appears in UI & persisted
**Epic:** 🎯 Core Conversational Interface & Interaction

## ✅ AC
- [ ] Given I am in the chat interface with my PA, When I type a message in the input field and send it, Then the message is transmitted to the backend via a GraphQL mutation (e.g., sendMessage). [Source 1259, 1298]
- [ ] Given the backend receives the message, Then it is persisted in the chat_messages table, associated with the correct conversation. [Source 1307]
- [ ] Given the message is sent, Then it appears in my chat history in the UI.

## 🔗 Deps
**Stories:** US7.3 (Real-time message display)(STATUS: TODO) | **Ext:** 🌐 [None] | **Tech:** ⚙️ ReactJS, GraphQL (Apollo Client), FastAPI, SQLAlchemy, PostgreSQL | **Data:** 🗄️ `chat_messages` table, `conversations` table | **Team:** 👥 [None]

## 💰 Value
**Biz:** 📈 Enables core user interaction with PA, fundamental for task delegation and information exchange. | **UX:** ✨ Provides a familiar and intuitive way for users to communicate with their PA. | **KPI:** 📊 Number of messages sent, successful message persistence rate. | **Why:** 🎯 Core functionality for user-PA communication.

## 📐 ADRs
**Refs:** 📄 ADR-002-Backend-Overview (GraphQL, PostgreSQL), ADR-003-Frontend-Overview (React, GraphQL, Apollo Client) | **Limits:** ⚠️ [None] | **Patterns:** 🏗️ GraphQL Mutation for sending messages.

## 🛠️ Impl
**Arch:** 🏛️ Frontend (React component for chat input & display) -> GraphQL Mutation (sendMessage) -> Backend (FastAPI resolver) -> Database (PostgreSQL). | **Data:** 🗄️ `chat_messages` (user_id, conversation_id, message_content, timestamp, sender_role='user'), `conversations` (user_id, pa_id). | **API:** 🔌 GraphQL Mutation: `sendMessage(conversationId: ID!, content: String!): Message`. | **Sec:** 🔒 User authentication (Clerk) to identify sender, authorization to ensure user can only send to their PA's conversation. | **Perf:** ⚡ Message transmission and persistence should be near real-time (<500ms). | **Int:** 🌐 [None for this specific action, but part of a larger system] | **Err:** ❌ Handle network errors, mutation failures, database errors. Display user-friendly error messages. | **Std:** 📏 Standard GraphQL mutation patterns, frontend state management for optimistic updates.

## 🧪 Test
**Unit:** 🔬 Frontend: input handling, message formatting, API call. Backend: `sendMessage` resolver logic, DB interaction. | **Int:** 🔗 Frontend <-> Backend API (GraphQL `sendMessage`). Backend <-> Database. | **Contract:** 📋 GraphQL `sendMessage` mutation schema. | **E2E:** 🎭 User types message, sends, message appears in chat history, message verified in DB. | **Perf:** 🚀 Load test `sendMessage` mutation. | **Sec:** 🛡️ Test sending messages as unauthenticated user, test sending to unauthorized conversations.

## 📏 NFRs
**Perf:** ⚡ Message send/receive latency < 500ms. | **A11y:** ♿ Chat input and messages should be accessible (WCAG AA). | **Sec:** 🔒 Authenticated and authorized message sending. | **Scale:** 📈 System should handle concurrent message sending from multiple users. | **Compat:** 🌍 Modern browsers (Chrome, Firefox, Safari, Edge).

## ✅ DoD
- [ ] AC met | [ ] Tests pass | [ ] Security review | [ ] Perf benchmarks | [ ] Docs updated | [ ] Code review | [ ] QA complete

## ⚠️ Risk
**Tech:** 💻 Potential issues with WebSocket connection for real-time updates (covered by US7.3), optimistic UI update failures. Mitigation: Robust error handling, fallback mechanisms. | **Biz:** 📊 [None] | **Time:** ⏰ [None] | **Deps:** 🔗 US7.3 completion for full real-time experience. Mitigation: This story can be completed with polling or manual refresh if US7.3 is delayed, but real-time is preferred.

## 📊 Est
**Pts:** 🎯 3 (Frontend component, GraphQL mutation, backend resolver, DB interaction) | **Hrs:** ⏱️ Dev: 8h, Test: 4h, Review: 2h | **Complex:** 🧩 Medium (involves full stack slice) | **Conf:** 📈 High | **Vars:** 🔄 [None]

## 📦 Data
**API:** 🔌 `sendMessage(conversationId: "...", content: "Hello PA")` returns `{ id: "msg123", content: "Hello PA", sender: "user", timestamp: "..." }`. | **DB:** 🗄️ `chat_messages` record: `{ id: ..., conversation_id: ..., user_id: ..., content: "Hello PA", sender_role: "user", created_at: ... }`. | **UI:** 🖥️ Chat input field state, chat history array of message objects. | **Config:** ⚙️ GraphQL endpoint URL.

## 🎨 Visual
**Layout:** 📐
```
+------------------------------------_--+
| [PA Avatar] PA Name                 |
+-------------------------------------+
|                                     |
|  [PA Message 1]                     |
|                     [User Message 1]|
|  [PA Message 2]                     |
|                                     |
+-------------------------------------+
| [Type your message...] [Send]       |
+-------------------------------------+
```
**Flow:** 🌊
```mermaid
sequenceDiagram
    User->>+Chat UI: Types message
    User->>+Chat UI: Clicks Send
    Chat UI->>+GraphQL Client: Calls sendMessage mutation
    GraphQL Client->>+Backend API: Sends sendMessage request
    Backend API->>+Database: Persists message
    Database-->>-Backend API: Confirms persistence
    Backend API-->>-GraphQL Client: Returns new message
    GraphQL Client-->>-Chat UI: Updates chat history
    Chat UI-->>-User: Displays new message
end
```
**States:** 🔄 ChatInput: Idle -> Typing -> Sending -> Sent/Error. ChatHistory: Updated. | **Seq:** 📋 User types -> User clicks send -> UI optimistically updates -> API call -> Backend processes -> DB saves -> API response -> UI confirms/corrects.

## 🤔 Assume
**Sys:** 🖥️ User is authenticated. A conversation with the PA already exists. | **User:** 👤 Knows how to use a chat interface. | **Env:** 🌐 Stable internet connection. | **Data:** 📊 Message content is text-based.

## 🎬 Context
**Before:** Files:📁`ChatInterface.tsx`, `apiService.ts`, `chatResolvers.py`, `models.py`. Schema:🗄️`users`, `conversations`. Components:⚙️Existing chat display area. Config:⚙️`GRAPHQL_ENDPOINT`.
**After:** Files:📁Modified `ChatInterface.tsx` (input, send logic), `apiService.ts` (add `sendMessage` mutation), `chatResolvers.py` (add `sendMessage` resolver), `models.py` (ensure `chat_messages` model). Schema:🗄️`chat_messages` table created/verified. Components:⚙️New input field and send button logic. Config:⚙️[None]

## 📝 Tasks
1. **Frontend: Implement Chat Input & Send Button** - Create/update React component for message input and send button.
```coder_llm
**Do:** 🎯 Implement a text input field and a send button. On send, call the GraphQL mutation `sendMessage` with `conversationId` and message content. Optimistically update the UI. | **File:** 📁 `apps/frontend/src/components/ChatInterface.tsx` | **Target:** 🎯 `ChatInput` component | **Check:** ✅ Message sent via GraphQL, UI updates. | **Needs:** 🔧 `conversationId` prop, GraphQL client setup. | [New/Multiple]
```
2. **Frontend: Define sendMessage GraphQL Mutation** - Add the `sendMessage` mutation to the GraphQL client queries/mutations.
```coder_llm
**Do:** 🎯 Define the `sendMessage(conversationId: ID!, content: String!): Message` GraphQL mutation string and a function to call it using Apollo Client. | **File:** 📁 `apps/frontend/src/services/graphql/mutations.ts` (or similar) | **Target:** 🎯 `SEND_MESSAGE_MUTATION` | **Check:** ✅ Mutation is correctly defined and callable. | **Needs:** 🔧 Apollo Client instance. | [New/Multiple]
```
3. **Backend: Create sendMessage GraphQL Resolver** - Implement the `sendMessage` resolver in FastAPI/Strawberry.
```coder_llm
**Do:** 🎯 Create a GraphQL mutation resolver `sendMessage` that takes `conversationId` and `content`. It should save the message to `chat_messages` table, associating it with the user and conversation. Return the new message. | **File:** 📁 `apps/backend/src/app/graphql/chat_mutations.py` (or similar) | **Target:** 🎯 `sendMessage` resolver | **Check:** ✅ Message saved to DB, correct message object returned. | **Needs:** 🔧 DB session, authenticated user context, `chat_messages` model. | [New/Multiple]
```
4. **Backend: Define ChatMessage DB Model & Schema** - Ensure `chat_messages` SQLAlchemy model and Alembic migration exist.
```coder_llm
**Do:** 🎯 Define/verify the `ChatMessage` SQLAlchemy model with fields: `id`, `conversation_id` (FK), `user_id` (FK), `sender_role` (e.g., 'user', 'assistant'), `content` (TEXT), `created_at`. Create/update Alembic migration script. | **File:** 📁 `apps/backend/src/app/models/chat.py`, `apps/backend/alembic/versions/xxxx_create_chat_messages.py` | **Target:** 🎯 `ChatMessage` class, migration script | **Check:** ✅ Table created in DB with correct schema. | **Needs:** 🔧 SQLAlchemy setup, Alembic setup. | [New/Multiple]
```
5. **Testing: Implement Unit and Integration Tests** - Write tests for frontend components, backend resolver, and API interaction.
```coder_llm
**Do:** 🎯 Write unit tests for chat input component logic, `sendMessage` resolver logic (mocking DB). Write integration tests for frontend-backend `sendMessage` flow. | **File:** 📁 `apps/frontend/src/components/ChatInterface.test.tsx`, `apps/backend/src/tests/graphql/test_chat_mutations.py` | **Target:** 🎯 Test functions | **Check:** ✅ Tests pass, cover ACs. | **Needs:** 🔧 Jest, React Testing Library, Pytest. | [New/Multiple]
```
6. **Testing: Implement E2E Test** - Create an E2E test for the send message flow.
```coder_llm
**Do:** 🎯 Create a Cypress E2E test: user logs in, navigates to chat, types a message, sends it, verifies message appears in UI, and (optionally) checks DB. | **File:** 📁 `apps/frontend/cypress/e2e/chat.cy.ts` | **Target:** 🎯 `it('should send and display a message')` | **Check:** ✅ E2E test passes. | **Needs:** 🔧 Cypress setup, test user. | [New/Multiple]
```

## 🔧 Refactor
**Quality:** 📏 {structure/readability} | **Perf:** ⚡ {optimizations} | **Reuse:** ♻️ {extract patterns} | **Debt:** 💳 {created/addressed} | **Test:** 🧪 {coverage} | **Docs:** 📚 {improvements} | [None/Sufficient]

---

## Usage
**Symbols:** {text}=required | [text]=optional | {text}|[default]=required+fallback
**Check:** [ ] {} replaced | [ ] [] evaluated | [ ] ADRs ref'd | [ ] Tasks actionable | [ ] AC=G-W-T | [ ] Deps ID'd
