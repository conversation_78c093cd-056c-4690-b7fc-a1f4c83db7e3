# 🧪 QA Testing Specification: US7.1

**Goal:** 🎯 Validate complete message sending functionality from frontend input through GraphQL mutation to database persistence with proper error handling and user feedback

## 🎯 Objectives
**Functional:** ⚙️ Verify sendMessage mutation creates chat_messages records with proper conversation association and user authorization | **NFR:** 📏 Validate <200ms response time, 4000 char limit enforcement, XSS prevention | **Edge:** ⚠️ Test empty messages, unauthorized conversations, network failures, concurrent sends | **Security:** 🔒 Verify JWT authentication, conversation ownership authorization, input sanitization | **DB:** 🗄️ Validate foreign key constraints, JSONB content structure, timestamp accuracy | **Integration:** 🔗 Test Apollo Client cache updates, GraphQL schema compliance, React component state management

## 📋 Strategy
**Scope:** 🎯 In: sendMessage mutation, chat_messages persistence, MessageInput component, error handling | Out: real-time subscriptions, message history display, PA response generation | **Env:** 🐳 Docker(default)/.env.test | CI(--ci flag) | **Backend:** 🖥️ apps/backend + ./scripts/run-backend-tests.sh + pytest + postgresql+asyncpg + DATABASE_URL(required) | **Frontend:** 🌐 bun + ./scripts/run-frontend-tests.sh + port:5173 + --e2e(Cypress) | **DB:** 🗄️ ./scripts/db-migrate.sh + upgrade/downgrade | **Tools:** 🛠️ pytest/bun/Docker/Alembic/Cypress/GraphQL Playground | **Deps:** 🔗 User authentication, conversation setup, assistant creation | **Data:** 📊 Test users, conversations, mock JWT tokens | **Risks:** ⚠️ Database transaction rollback, GraphQL schema changes, authentication middleware

## 🎬 Context
**Before:** Features:⚙️ User authentication, assistant creation, basic GraphQL schema | Env:🔧 Docker containers running, test database migrated, test users created | Data:📊 Test conversations, valid JWT tokens, mock assistant data | [None]
**After:** Deliverables:📦 Test reports, coverage metrics, performance benchmarks | Reports:📄 GraphQL mutation test results, component test output, integration test logs | Defects:🐛 GitHub issues for any failures, performance regression tracking | [None]

## 📝 Tasks
1. **Setup Test Database Schema** - Ensure conversations and chat_messages tables exist
```test_execution
**Do:** 🎯 Run Alembic migrations to create conversations and chat_messages tables in test database, verify foreign key constraints and indexes | **Type:** 🧪 Infrastructure | **Cmd:** 💻 ./scripts/db-migrate.sh --env test && docker compose exec db psql -U postgres -d test_database -c "\d conversations; \d chat_messages;" | **Assert:** ✅ Tables exist with correct schema, foreign keys functional, indexes created | **Data:** 📊 Clean test database | **Result:** 🎯 Database ready for message testing
```

2. **Test ChatMessage Model Creation** - Validate SQLAlchemy model functionality
```test_execution
**Do:** 🎯 Create unit tests for ChatMessage model in tests/unit/models/test_chat_message.py testing model creation, JSONB content handling, sender_role validation, relationship to Conversation | **Type:** 🧪 Unit | **Cmd:** 💻 ./scripts/run-backend-tests.sh tests/unit/models/test_chat_message.py | **Assert:** ✅ Model creates successfully, JSONB serialization works, sender_role constraint enforced, relationships functional | **Data:** 📊 Mock conversation, test message content | **Result:** 🎯 ChatMessage model validated
```

3. **Test sendMessage GraphQL Resolver** - Validate backend mutation logic
```test_execution
**Do:** 🎯 Create integration tests for sendMessage resolver in tests/integration/graphql/test_chat_mutations.py testing authentication, authorization, input validation, message persistence, error handling | **Type:** 🧪 Integration | **Cmd:** 💻 ./scripts/run-backend-tests.sh tests/integration/graphql/test_chat_mutations.py | **Assert:** ✅ Resolver authenticates users, validates conversation ownership, persists messages correctly, handles errors gracefully | **Data:** 📊 Test users, conversations, valid/invalid JWT tokens | **Result:** 🎯 sendMessage resolver fully functional
```

4. **Test Frontend MessageInput Component** - Validate React component behavior
```test_execution
**Do:** 🎯 Create unit tests for MessageInput component in apps/web/src/components/chat/__tests__/MessageInput.test.tsx testing input handling, validation, mutation calls, loading states, error display | **Type:** 🧪 Unit | **Cmd:** 💻 cd apps/web && bun test MessageInput.test.tsx | **Assert:** ✅ Component renders, handles input changes, validates before submit, shows loading/error states, calls mutation correctly | **Data:** 📊 Mock Apollo Client, test conversation ID | **Result:** 🎯 MessageInput component validated
```

5. **Test End-to-End Message Flow** - Validate complete user journey
```test_execution
**Do:** 🎯 Create E2E test in cypress/e2e/chat/send-message.cy.ts testing complete flow: login → navigate to chat → type message → click send → verify message appears in UI and database | **Type:** 🧪 E2E | **Cmd:** 💻 cd apps/web && bun run cypress:run --spec "cypress/e2e/chat/send-message.cy.ts" | **Assert:** ✅ Complete flow works, message persisted in database, UI updates correctly, no console errors | **Data:** 📊 Test user credentials, conversation setup | **Result:** 🎯 End-to-end message sending validated
```

## 📋 Test Cases

### 🧪 Functional: Message Creation and Persistence
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-TC-1 | Valid message sending | Login → Navigate to chat → Type "Hello PA" → Click Send | Message appears in chat history, persisted in database with correct conversation_id, sender_role='user' | API/DB/UI |
| US7.1-TC-2 | Message content structure | Send message with special chars "Hello! @#$%" | Content stored as JSONB {"text": "Hello! @#$%"}, timestamp in UTC, metadata empty object | DB/API |
| US7.1-TC-3 | Conversation association | Send message in specific conversation | Message linked to correct conversation_id, user_id matches authenticated user | DB/API |

### ❌ Negative & Error
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-ERR-1 | Empty message validation | Type empty string → Click Send | GraphQL validation error, UI shows "Message cannot be empty", no database record | Negative/Validation |
| US7.1-ERR-2 | Message length limit | Type 4001 character message → Click Send | GraphQL validation error, UI shows "Message too long (max 4000 chars)", no submission | Negative/Validation |
| US7.1-ERR-3 | Unauthorized conversation | Send message to conversation user doesn't own | GraphQL authorization error, UI shows "Access denied", no database record | Security/Authorization |
| US7.1-ERR-4 | Invalid conversation ID | Send message with non-existent conversationId | GraphQL error, UI shows "Conversation not found", no database record | Negative/Validation |
| US7.1-ERR-5 | Unauthenticated request | Send message without valid JWT | GraphQL authentication error, UI redirects to login | Security/Authentication |

### 📋 Contract & External
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-CT-1 | GraphQL schema compliance | Query GraphQL schema → Verify sendMessage mutation | Mutation exists with correct signature: sendMessage(conversationId: ID!, content: String!): ChatMessage | Contract/Schema |
| US7.1-CT-2 | Apollo Client integration | Mock GraphQL response → Test component | MessageInput component handles Apollo Client responses correctly, cache updates | Contract/Integration |

### ⚙️ Environment & Config
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.1-ENV-1 | Database connection | Start test environment → Check database connectivity | PostgreSQL accessible, migrations applied, test data available | Environment |
| US7.1-ENV-2 | GraphQL endpoint | Start backend → Query GraphQL endpoint | GraphQL server responds, schema introspection works, sendMessage mutation available | Environment |

## 📊 Coverage
**Functional:** ⚙️ sendMessage mutation execution, message persistence, conversation association, user authorization, input validation | **NFR:** 📏 Response time <200ms, character limit enforcement, XSS prevention, concurrent user handling | **Edge:** ⚠️ Empty messages, oversized content, invalid conversation IDs, network failures, authentication errors

## 🔧 Setup
**Backend:** 🖥️ apps/backend + ./scripts/run-backend-tests.sh + pytest + postgresql+asyncpg + DATABASE_URL(required) + test fixtures for users/conversations | **Frontend:** 🌐 bun + ./scripts/run-frontend-tests.sh + port:5173 + dev mode + Apollo Client MockedProvider | **DB:** 🗄️ ./scripts/db-migrate.sh + Docker Compose env vars + test database with clean state per test

## 📦 Data
**Env Vars:** ⚙️ DATABASE_URL + POSTGRES_* + CLERK_JWT_PUBLIC_KEY + .env/.env.test + GraphQL endpoint configuration | **Config:** 🔧 .env.test + Docker + --ci flags + test user credentials + mock JWT tokens

## 🎭 Scenarios

### 🎬 Happy Path: Successful Message Sending
**TC 7.1.1:** Complete message sending flow
- **Given:** 📋 User authenticated with valid JWT, conversation exists, MessageInput component loaded | **When:** ⚡ User types "Hello PA" and clicks Send button | **Then:** ✅ GraphQL mutation called with correct variables, message persisted in database, UI shows message in chat history | **And:** ➕ Loading state shown during send, success feedback provided, input field cleared

### 🎬 Error Handling: Invalid Input
**TC 7.1.2:** Empty message validation
- **Given:** 📋 User in chat interface, MessageInput component loaded | **When:** ⚡ User clicks Send with empty input field | **Then:** ✅ Client-side validation prevents submission, error message displayed, no GraphQL call made | **And:** ➕ Send button remains disabled, input field retains focus

### 🎬 Security: Unauthorized Access
**TC 7.1.3:** Conversation ownership validation
- **Given:** 📋 User authenticated but attempting to send to unauthorized conversation | **When:** ⚡ User submits message with invalid conversation ID | **Then:** ✅ GraphQL resolver validates ownership, returns authorization error, UI shows access denied message | **And:** ➕ No database record created, user redirected appropriately

## 🤖 Automation
**Pre-commit:** 🔧 MyPy parity + auto-fixes + SKIP=pytest-backend + flags + Ruff + GraphQL schema validation | **CI/CD:** 🚀 postgres://test_user:test_password@localhost:5432/test_database + Docker/CI parity + apps/backend + GitHub Actions + E2E test execution | **Env Mgmt:** 🐳 Docker(default) + .env.test + --ci flag + idempotent DB scripts + test data seeding

## 📦 Deliverables
📄 QA spec + test execution logs + coverage reports + performance benchmarks + GraphQL schema validation + security test results + bug reports + sign-off documentation

## ⚠️ Risks
**Risk 1:** 🚨 Database transaction rollback during testing → Use test database with proper isolation and cleanup | **Risk 2:** 🚨 GraphQL schema changes breaking tests → Implement schema validation in CI pipeline | **Risk 3:** 🚨 Authentication middleware changes → Mock JWT tokens with proper claims structure | **Risk 4:** 🚨 Frontend component state management issues → Use Apollo MockedProvider for predictable testing
