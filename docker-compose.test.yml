services:
  backend:
    build: ./apps/backend
    environment:
      - DEBUG=true
      - CI=true
      - TESTING=true
      - DOCKER_ENVIRONMENT=true
      - DATABASE_URL=**************************************/a2a_platform_test
      - DATABASE_ASYNC_URL=postgresql+asyncpg://postgres:postgres@db:5432/a2a_platform_test
      - REDIS_URL=redis://redis:6379/1
      - CLERK_API_KEY=test_clerk_api_key
      - CLERK_JWT_PUBLIC_KEY=test_clerk_jwt_public_key
      - CLERK_WEBHOOK_SECRET=test_clerk_webhook_signing_secret
      - CORS_ORIGINS=https://localhost:5173
      - STORAGE_BUCKET=test_storage_bucket
      - CDN_URL=https://test-cdn.example.com
      - PUBSUB_PROJECT_ID=test_project_id
      - ENABLE_TEST_AUTH_BYPASS=true
      - PYTHONPATH=/app/src
    volumes:
      - ./apps/backend:/app
      - ./.claude:/app/.claude
    depends_on:
      - db
      - redis
    command: >
      sh -c "cd /app &&
             python -m pytest"
  frontend:
    build: ./apps/web
    environment:
      - VITE_GRAPHQL_API_URL=${VITE_GRAPHQL_API_URL}
      - VITE_CLERK_PUBLISHABLE_KEY=${VITE_CLERK_PUBLISHABLE_KEY}
    ports:
      - "5173:5173"
    depends_on:
      - backend

  db:
    image: postgres:14
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=a2a_platform_test
      - POSTGRES_INITDB_ARGS=--auth-host=trust
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./scripts/postgres-init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d a2a_platform_test"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c work_mem=4MB
      -c min_wal_size=1GB
      -c max_wal_size=4GB

  redis:
    image: redis:alpine
    ports:
      - "6380:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 3
      start_period: 5s
    command: >
      redis-server
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
      --save ""
      --appendonly no

volumes:
  postgres_test_data:
