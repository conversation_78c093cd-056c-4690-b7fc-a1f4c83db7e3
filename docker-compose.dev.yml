services:
  backend:
    build: ./apps/backend
    command: ./src/a2a_platform/api/https_entrypoint.sh
    ports:
      - "8000:8000"
    environment:
      - AI_DEFAULT_PROVIDER=${AI_DEFAULT_PROVIDER}
      - AI_RATE_LIMIT_CALLS=${AI_RATE_LIMIT_CALLS}
      - AI_RATE_LIMIT_WINDOW=${AI_RATE_LIMIT_WINDOW}
      - AI_RESPONSE_ENABLED=${AI_RESPONSE_ENABLED}
      - AI_RESPONSE_TIMEOUT=${AI_RESPONSE_TIMEOUT}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - CDN_URL=${CDN_URL}
      - CLERK_API_KEY=${CLERK_API_KEY}
      - CLERK_JWT_PUBLIC_KEY=${CLERK_JWT_PUBLIC_KEY}
      - CLERK_WEBHOOK_SECRET=${CLERK_WEBHOOK_SECRET}
      - CORS_ORIGINS=${CORS_ORIGINS}
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}
      - DATABASE_ASYNC_URL=postgresql+asyncpg://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}
      - DEBUG=${DEBUG}
      - ENFORCE_HTTPS=${ENFORCE_HTTPS}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - HSTS_INCLUDE_SUBDOMAINS=${HSTS_INCLUDE_SUBDOMAINS:-true}
      - HSTS_MAX_AGE=${HSTS_MAX_AGE:-31536000}
      - HSTS_PRELOAD=${HSTS_PRELOAD:-true}
      - LOG_LEVEL=${LOG_LEVEL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - PUBSUB_PROJECT_ID=${PUBSUB_PROJECT_ID}
      - REDIS_URL=${REDIS_URL}
      - RQ_DEFAULT_TIMEOUT=${RQ_DEFAULT_TIMEOUT:-180}
      - RQ_DEFAULT_TTL=${RQ_DEFAULT_TTL:-86400}
      - RQ_RESULT_TTL=${RQ_RESULT_TTL:-3600}
      - RQ_WORKER_COUNT=${RQ_WORKER_COUNT:-2}
      - SSL_DOMAIN=${SSL_DOMAIN:-domain.com}
      - STORAGE_BUCKET=${STORAGE_BUCKET}
    volumes:
      - ./apps/backend:/app
      - backend_data:/app/.data
      - ./ssl-certs:/app/ssl-certs  # Mount SSL certificates
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "python", "-c", "import socket; socket.create_connection(('localhost', 8000), timeout=5)"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s

  frontend:
    build:
      context: ./apps/web
      dockerfile: Dockerfile.dev
    command: ["/app/enhanced-entrypoint.sh"]
    environment:
      - DOCKER_ENV=true
      - VITE_API_URL=${VITE_API_URL}
      - VITE_CLERK_PUBLISHABLE_KEY=${VITE_CLERK_PUBLISHABLE_KEY}
      - VITE_GRAPHQL_API_URL=${VITE_GRAPHQL_API_URL}
      - VITE_USE_HTTPS=${VITE_USE_HTTPS}
    ports:
      - "5173:5173"
    volumes:
      - ./apps/web:/app
      - ./apps/web/node_modules:/app/node_modules
      # Mount SSL certificates for HTTPS development
      - ./ssl-certs:/app/ssl-certs:ro
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "bun", "run", "--silent", "-e", "try { fetch('https://localhost:5173').then(r => process.exit(r.ok ? 0 : 1)).catch(() => process.exit(1)) } catch(e) { process.exit(1) }"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s

  storybook:
    build:
      context: ./apps/web
      dockerfile: Dockerfile.dev
    command: ["/app/enhanced-start-storybook.sh"]
    depends_on:
      backend:
        condition: service_healthy
    environment:
      - DOCKER_ENV=true
      - STORYBOOK_SKIP_COMPATIBILITY_CHECK=true
      - VITE_API_URL=${VITE_API_URL}
      - VITE_CLERK_PUBLISHABLE_KEY=${VITE_CLERK_PUBLISHABLE_KEY}
      - VITE_GRAPHQL_API_URL=${VITE_GRAPHQL_API_URL}
      - VITE_USE_HTTPS=${VITE_USE_HTTPS}
    ports:
      - "6006:6006"
    volumes:
      - ./apps/web:/app
      - ./apps/web/node_modules:/app/node_modules
      # Mount SSL certificates for HTTPS development
      - ./ssl-certs:/app/ssl-certs:ro
    healthcheck:
      test: ["CMD", "bun", "run", "--silent", "-e", "const url = process.env.VITE_USE_HTTPS === 'true' ? 'https://localhost:6006' : 'http://localhost:6006'; const opts = process.env.VITE_USE_HTTPS === 'true' ? {rejectUnauthorized: false} : {}; fetch(url, opts).then(r => process.exit(r.ok ? 0 : 1)).catch(() => process.exit(1))"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 15s

  db:
    image: postgres:14
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_USER=${POSTGRES_USER}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s

  rq-worker:
    build: ./apps/backend
    command: python -m a2a_platform.workers.rq_worker --queues default a2a-messages --workers ${RQ_WORKER_COUNT:-2}
    environment:
      - CDN_URL=${CDN_URL}
      - CLERK_API_KEY=${CLERK_API_KEY}
      - CLERK_JWT_PUBLIC_KEY=${CLERK_JWT_PUBLIC_KEY}
      - CLERK_WEBHOOK_SECRET=${CLERK_WEBHOOK_SECRET}
      - CORS_ORIGINS=${CORS_ORIGINS}
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}
      - DATABASE_ASYNC_URL=postgresql+asyncpg://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}
      - DEBUG=${DEBUG}
      - LOG_LEVEL=${LOG_LEVEL}
      - PUBSUB_PROJECT_ID=${PUBSUB_PROJECT_ID}
      - REDIS_URL=${REDIS_URL}
      - RQ_DEFAULT_TIMEOUT=${RQ_DEFAULT_TIMEOUT:-180}
      - RQ_DEFAULT_TTL=${RQ_DEFAULT_TTL:-86400}
      - RQ_RESULT_TTL=${RQ_RESULT_TTL:-3600}
      - RQ_WORKER_COUNT=${RQ_WORKER_COUNT:-2}
      - STORAGE_BUCKET=${STORAGE_BUCKET}
    volumes:
      - ./apps/backend:/app
    depends_on:
      redis:
        condition: service_healthy
      db:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "python", "-c", "import redis; redis.from_url('${REDIS_URL}').ping()"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s

volumes:
  postgres_data:
  backend_data:
