# .pre-commit-config.yaml
# This configuration targets Python linting and formatting hooks specifically to the apps/backend directory
# General hooks like check-yaml apply to the entire repository
#
# Note: act (GitHub Actions local testing) hooks have been removed to streamline the precommit process.
# For manual workflow testing, use ./scripts/run-act.sh when needed.
#
# To install pre-commit: pip install pre-commit
# To set up the git hooks: pre-commit install
# To run manually: pre-commit run --files <file-path>
# To run all hooks on all files: pre-commit run --all-files
repos:
  # Ruff for linting and formatting
  # These hooks automatically fix issues when possible
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.11.9  # Use the latest stable version
    hooks:
      - id: ruff
        # Auto-fix issues and fail if fixes were made (or if errors remain)
        args: [--fix, --exit-non-zero-on-fix, --show-fixes]
        files: ^apps/backend/(src|tests)/.*\.py$
      - id: ruff-format # Automatically formats code
        files: ^apps/backend/(src|tests)/.*\.py$

  # Standard pre-commit hooks for various file checks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0  # Use the latest stable version
    hooks:
      - id: check-yaml
        # This hook applies to all YAML files in the repo
        # Validates YAML syntax but doesn't auto-fix
        files: \.(yaml|yml)$
      - id: check-json
        # Validates JSON syntax but doesn't auto-fix
        # Keeping this as Ruff doesn't handle JSON files specifically by default
        # and it's good to have a dedicated JSON checker.
        files: \.json$ # Apply to JSON files only (excluding JSONC files like tsconfig)
        exclude: ^apps/web/tsconfig.*\.json$ # Exclude TypeScript config files which use JSONC format
      - id: check-ast
        # Validates Python syntax
        files: ^apps/backend/(src|tests)/.*\.py$
        types: [python]
      - id: check-docstring-first
        # Ensures docstrings are before code
        files: ^apps/backend/(src|tests)/.*\.py$
        types: [python]
      - id: debug-statements
        # Checks for debugger imports and breakpoints
        files: ^apps/backend/(src|tests)/.*\.py$
        types: [python]

  # yamllint for comprehensive YAML linting
  - repo: https://github.com/adrienverge/yamllint.git
    rev: v1.35.1  # Use the latest stable version
    hooks:
      - id: yamllint
        # Comprehensive YAML linting with style and syntax checks
        # Uses .yamllint config file if present, otherwise uses default rules
        args: [--config-file, .yamllint]

  # Vulture for detecting unused code
  - repo: https://github.com/jendrikseipp/vulture
    rev: v2.14 # Use the latest stable version (as previously used)
    hooks:
      - id: vulture
        # Detects unused code in the backend source directory
        # To ignore false positives, add "# vulture: ignore" comment to the line
        # This hook only reports issues and doesn't auto-fix
        args: [--min-confidence, '80', '--exclude', 'apps/backend/venv/', 'apps/backend/src/']
        files: ^apps/backend/src/.*\.py$

  # Bandit for Python security linting
  - repo: https://github.com/pycqa/bandit
    rev: 1.7.8 # Using latest stable tag
    hooks:
      - id: bandit
        files: ^apps/backend/src/.*\.py$
        args: ['-r', '--severity-level', 'medium', '--confidence-level', 'medium']
        types: [python]
        # Example of how to use a baseline/config file if needed in the future:
        # args: ['-r', '-c', 'apps/backend/bandit.yaml']

  # mypy for static type checking - using local hook to match CI workflow
  - repo: local
    hooks:
      - id: mypy-backend
        name: mypy-backend
        description: Run mypy type checking on backend code
        # Explicitly use the same command as in CI workflow
        entry: bash -c 'cd apps/backend && mypy -p a2a_platform'
        language: system
        pass_filenames: false
        types: [python]
        files: ^apps/backend/(src|tests)/.*\.py$

  # Web Development Tools (Prettier + ESLint + TypeScript) - Using package.json scripts
  - repo: local
    hooks:
      # Combined formatting and linting using package.json scripts
      - id: web-code-quality
        name: web-code-quality
        description: Format with Prettier + lint with ESLint using bun scripts
        entry: bash -c 'cd apps/web && bun run code:fix:all'
        language: system
        files: ^apps/web/(src|cypress|storybook)/.*\.(ts|tsx|js|jsx|json|css|scss|md|yaml|yml|html)$
        pass_filenames: false

      # TypeScript type checking for frontend
      - id: typescript-check-web
        name: typescript-check-web
        description: Run TypeScript type checking on frontend code using bun
        entry: bash -c 'cd apps/web && bun run type-check'
        language: system
        files: ^apps/web/(src|cypress|storybook)/.*\.(ts|tsx)$
        pass_filenames: false


  # Install trivy locally if needed
  - repo: local
    hooks:
      - id: install-trivy
        name: Install trivy if needed
        description: Checks if trivy is installed and installs it if not
        entry: bash -c 'if ! command -v trivy &> /dev/null; then echo "Installing trivy..."; if [[ "$OSTYPE" == "darwin"* ]]; then brew install trivy || (echo "Failed to install trivy via brew, please install manually"; exit 1); elif [[ "$OSTYPE" == "linux-gnu"* ]] && command -v apt-get &> /dev/null; then sudo apt-get update && sudo apt-get install -y trivy || (echo "Failed to install trivy via apt, please install manually"; exit 1); else echo "Unsupported operating system for automatic trivy installation."; echo "Please install trivy manually following the official documentation:"; echo "https://aquasecurity.github.io/trivy/latest/getting-started/installation/"; exit 1; fi; fi'
        language: system
        pass_filenames: false
        always_run: true

  # Install terraform-docs locally if needed
  - repo: local
    hooks:
      - id: install-terraform-docs
        name: Install terraform-docs if needed
        description: Checks if terraform-docs is installed and installs it if not
        entry: bash -c 'if ! command -v terraform-docs &> /dev/null; then echo "Installing terraform-docs..."; if [[ "$OSTYPE" == "darwin"* ]]; then brew install terraform-docs || (echo "Failed to install terraform-docs via brew, please install manually"; exit 1); elif [[ "$OSTYPE" == "linux-gnu"* ]]; then TERRAFORM_DOCS_VERSION="0.16.0"; OS=$(uname | tr "[:upper:]" "[:lower:]"); ARCH=$(uname -m | sed -e "s/x86_64/amd64/"); curl -sSLo ./terraform-docs.tar.gz "https://terraform-docs.io/dl/v${TERRAFORM_DOCS_VERSION}/terraform-docs-v${TERRAFORM_DOCS_VERSION}-${OS}-${ARCH}.tar.gz" && tar -xzf terraform-docs.tar.gz && chmod +x terraform-docs && sudo mv terraform-docs /usr/local/bin/ || (echo "Failed to install terraform-docs, please install manually"; exit 1); else echo "Unsupported operating system for automatic terraform-docs installation."; echo "Please install terraform-docs manually following the official documentation:"; echo "https://terraform-docs.io/user-guide/installation/"; exit 1; fi; fi'
        language: system
        pass_filenames: false
        always_run: true

  # Terraform hooks
  - repo: https://github.com/antonbabenko/pre-commit-terraform
    rev: v1.88.0
    hooks:
      - id: terraform_fmt
        description: Automatically formats all Terraform code
        args:
          - --args=-recursive
        files: ^terraform/.*\.tf$

      - id: terraform_validate
        description: Validates Terraform modules and environment configurations
        files: ^terraform/(modules|environments)/.*\.tf$
        exclude: \.terraform/.*

      - id: terraform_trivy
        description: Runs trivy to check for security issues (replacement for tfsec)
        args:
          - --args=--severity=HIGH,CRITICAL
        files: ^terraform/(modules|environments)/.*\.tf$
        exclude: \.terraform/.*

      - id: terraform_docs
        description: Automatically updates Terraform module documentation
        args:
          - --args=--lockfile=false
        files: ^terraform/.*\.tf$
        exclude: \.terraform/.*

  # CDN Migration Security Validation
  - repo: local
    hooks:
      - id: cdn-security-checklist
        name: CDN Migration Security Checklist
        description: Validates CDN migration security checklist on Terraform and workflow changes
        entry: ./scripts/precommit-cdn-security-check.sh
        language: script
        files: ^(terraform/|\.github/workflows/(terraform-cdn|frontend-cdn-deploy)\.yml|scripts/(validate-cdn-migration|precommit-cdn-security-check)\.sh)
        pass_filenames: false
        stages: [pre-commit]
        verbose: true
