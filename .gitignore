# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
# Python lib directories (but not frontend src/lib)
apps/backend/lib/
**/python/lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/
.env
.venv

# IDE
.idea/
.vscode/
*.swp
*.swo

# Dagger
.dagger/
.cue/
.cache/

# Logs
*.log

# Local development
.env.local
.env.development.local
.env.test
.env.test.local
.env.production.local
.secrets

.DS_Store

# Terraform
terraform/.terraform/
terraform/**/.terraform/
terraform/**/.terraform/providers/**
terraform/**/terraform-provider-*
terraform/*.tfstate
terraform/*.tfstate.*
terraform/terraform.tfstate.d/
terraform/backend.tf
# Allow environment-specific backend configurations
!terraform/environments/*/backend.tf
# Note: .terraform.lock.hcl files should be committed for reproducible builds
terraform/**/test/*/.terraform/
terraform/**/test/**/.terraform/

# GitHub Actions local testing
.github/workflows/event.json

*.pem
*.out
*.bak
apps/backend/.coverage
apps/web/coverage/
apps/web/storybook-static/
tmp/
CURRENT-PROBLEM.md
.github/instructions/local.instructions.md

# Think Tank knowledge graph data
.think-tank/
