#!/usr/bin/env python3
"""
Example demonstrating AI-powered Personal Assistant response generation.

This example shows how to use the AI response services to generate
intelligent responses for Personal Assistants.
"""

import asyncio
import uuid
from datetime import datetime, UTC

# Mock database models for demonstration
class MockAssistant:
    def __init__(self, name, backstory, configuration=None):
        self.id = uuid.uuid4()
        self.name = name
        self.backstory = backstory
        self.configuration = configuration or {}

class MockChatMessage:
    def __init__(self, content_text, sender_role="user"):
        self.id = uuid.uuid4()
        self.conversation_id = uuid.uuid4()
        self.sender_role = sender_role
        self.content = {"parts": [{"type": "text", "content": content_text}]}
        self.timestamp = datetime.now(UTC)
        self.message_metadata = {}


async def demonstrate_ai_response_generation():
    """Demonstrate AI response generation with fallback."""
    print("🤖 AI-Powered Personal Assistant Response Generation Demo")
    print("=" * 60)

    # Import the AI service
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
    from a2a_platform.services.pydantic_ai_response_service import (
        PydanticAIResponseService,
    )

    # Create AI service instance
    ai_service = PydanticAIResponseService()

    # Create a mock assistant
    assistant = MockAssistant(
        name="Alex",
        backstory="I am a helpful and friendly personal assistant who loves to help users with their daily tasks.",
        configuration={
            "ai": {
                "enabled": True,
                "model": "gpt-3.5-turbo",
                "temperature": 0.7,
                "max_tokens": 200,
                "system_prompt_template": "You are {name}, {backstory} Keep responses concise and helpful."
            }
        }
    )

    # Create conversation history
    conversation_history = [
        MockChatMessage("Hi there! I'm new here.", "user"),
        MockChatMessage("Welcome! I'm Alex, your personal assistant. How can I help you today?", "agent"),
        MockChatMessage("I need help organizing my schedule.", "user"),
        MockChatMessage("I'd be happy to help you organize your schedule! What specific aspects would you like to work on?", "agent"),
    ]

    # Test user message
    user_message = "Can you help me plan my day tomorrow?"

    print(f"👤 User: {user_message}")
    print("\n🔄 Generating AI response...")

    try:
        # Generate response (will use fallback since no API keys configured)
        response_text, is_ai_generated = await ai_service.generate_response(
            assistant=assistant,
            user_message=user_message,
            conversation_history=conversation_history
        )

        print(f"\n🤖 {assistant.name}: {response_text}")
        print(f"📊 AI Generated: {is_ai_generated}")

        if not is_ai_generated:
            print("💡 Note: This is a fallback response since no API keys are configured.")
            print("   To use real AI responses, set OPENAI_API_KEY or ANTHROPIC_API_KEY environment variables.")

    except Exception as e:
        print(f"❌ Error: {str(e)}")

    print("\n" + "=" * 60)


def demonstrate_rate_limiting():
    """Demonstrate rate limiting functionality."""
    print("⏱️  Rate Limiting Demo")
    print("=" * 30)

    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
    from a2a_platform.services.pydantic_ai_response_service import RateLimiter

    # Create a rate limiter (3 calls per 5 seconds for demo)
    limiter = RateLimiter(max_calls=3, window_seconds=5)

    print("Testing rate limiter (3 calls per 5 seconds):")

    for i in range(5):
        allowed = limiter.acquire()
        print(f"Call {i+1}: {'✅ Allowed' if allowed else '❌ Rate limited'}")

        if not allowed:
            wait_time = limiter.time_until_next_call()
            print(f"   ⏳ Wait {wait_time:.1f}s until next call allowed")

    print("\n" + "=" * 30)


def demonstrate_assistant_configuration():
    """Demonstrate different assistant configurations."""
    print("⚙️  Assistant Configuration Demo")
    print("=" * 40)

    from apps.backend.src.a2a_platform.services.pydantic_ai_response_service import (
        PydanticAIResponseService,
    )

    ai_service = PydanticAIResponseService()

    # Different assistant configurations
    assistants = [
        MockAssistant(
            name="Professional Bot",
            backstory="I am a professional business assistant focused on productivity and efficiency.",
            configuration={
                "ai": {
                    "model": "gpt-4",
                    "temperature": 0.3,  # More focused
                    "max_tokens": 150,
                    "enabled": True
                }
            }
        ),
        MockAssistant(
            name="Creative Bot",
            backstory="I am a creative assistant who loves brainstorming and artistic endeavors.",
            configuration={
                "ai": {
                    "model": "gpt-3.5-turbo",
                    "temperature": 0.9,  # More creative
                    "max_tokens": 300,
                    "enabled": True
                }
            }
        ),
        MockAssistant(
            name="Disabled Bot",
            backstory="I am an assistant with AI responses disabled.",
            configuration={
                "ai": {
                    "enabled": False  # AI disabled
                }
            }
        )
    ]

    for assistant in assistants:
        config = ai_service._get_ai_config(assistant)
        print(f"\n🤖 {assistant.name}:")
        print(f"   Model: {config.model}")
        print(f"   Temperature: {config.temperature}")
        print(f"   Max Tokens: {config.max_tokens}")
        print(f"   Enabled: {assistant.configuration.get('ai', {}).get('enabled', True)}")

        # Build system prompt
        system_prompt = ai_service._build_system_prompt(assistant, config)
        print(f"   System Prompt: {system_prompt[:80]}...")

    print("\n" + "=" * 40)


def demonstrate_conversation_context():
    """Demonstrate conversation context building."""
    print("💬 Conversation Context Demo")
    print("=" * 35)

    from apps.backend.src.a2a_platform.services.pydantic_ai_response_service import (
        PydanticAIResponseService,
    )

    ai_service = PydanticAIResponseService()

    # Create a longer conversation
    messages = []
    conversation = [
        ("user", "Hello, I'm planning a trip to Japan."),
        ("agent", "That sounds exciting! Japan is a wonderful destination. What time of year are you planning to visit?"),
        ("user", "I'm thinking about spring, maybe March or April."),
        ("agent", "Perfect timing! Spring is cherry blossom season. Are you interested in seeing the sakura?"),
        ("user", "Yes! Where are the best places to see cherry blossoms?"),
        ("agent", "Some of the best spots include Tokyo's Ueno Park, Kyoto's Philosopher's Path, and Mount Yoshino in Nara."),
        ("user", "What about food? I love trying local cuisine."),
    ]

    for sender_role, content in conversation:
        messages.append(MockChatMessage(content, sender_role))

    # Build context with different limits
    for limit in [3, 5, 10]:
        context = ai_service._build_conversation_context(messages, max_context=limit)
        print(f"\nContext with last {limit} messages:")
        for i, msg in enumerate(context):
            role_emoji = "👤" if msg["role"] == "user" else "🤖"
            print(f"  {i+1}. {role_emoji} {msg['content'][:50]}...")

    print("\n" + "=" * 35)


async def main():
    """Run all demonstrations."""
    print("🚀 AI Response Generation System Demo")
    print("=" * 50)
    print()

    # Run demonstrations
    await demonstrate_ai_response_generation()
    print()

    demonstrate_rate_limiting()
    print()

    demonstrate_assistant_configuration()
    print()

    demonstrate_conversation_context()
    print()

    print("✅ All demonstrations completed!")
    print("\n📚 Next steps:")
    print("1. Set up API keys (OPENAI_API_KEY or ANTHROPIC_API_KEY)")
    print("2. Configure assistant AI settings in the database")
    print("3. Test with real conversations in the application")
    print("4. Monitor response quality and adjust configurations")


if __name__ == "__main__":
    asyncio.run(main())
