#!/usr/bin/env python3
"""
PydanticAI Multi-Provider Example for A2A Platform.

This example demonstrates the new PydanticAI-based AI response system with
support for multiple LLM providers: OpenAI, Anthropic Claude, and Google Gemini.
"""

import asyncio
import uuid
from datetime import datetime, UTC

# Mock database models for demonstration
class MockAssistant:
    def __init__(self, name, backstory, configuration=None):
        self.id = uuid.uuid4()
        self.name = name
        self.backstory = backstory
        self.configuration = configuration or {}

class MockChatMessage:
    def __init__(self, content_text, sender_role="user"):
        self.id = uuid.uuid4()
        self.conversation_id = uuid.uuid4()
        self.sender_role = sender_role
        self.content = {"parts": [{"type": "text", "content": content_text}]}
        self.timestamp = datetime.now(UTC)
        self.message_metadata = {}


async def demonstrate_openai_integration():
    """Demonstrate OpenAI GPT integration with PydanticAI."""
    print("🤖 OpenAI GPT Integration Demo")
    print("=" * 40)

    # Import the PydanticAI service
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
    from a2a_platform.services.pydantic_ai_response_service import PydanticAIResponseService

    # Create AI service instance
    ai_service = PydanticAIResponseService()

    # Create a mock assistant with OpenAI configuration
    assistant = MockAssistant(
        name="GPT Assistant",
        backstory="I am a helpful OpenAI GPT-powered assistant specialized in providing clear and concise answers.",
        configuration={
            "ai": {
                "enabled": True,
                "model": "openai:gpt-4o",
                "temperature": 0.7,
                "max_tokens": 200,
                "system_prompt_template": "You are {name}, {backstory} Keep responses helpful and engaging."
            }
        }
    )

    # Create conversation history
    conversation_history = [
        MockChatMessage("Hi there! I'm new to AI assistants.", "user"),
        MockChatMessage("Welcome! I'm here to help you learn about AI and answer any questions you have.", "agent"),
    ]

    # Test user message
    user_message = "What makes OpenAI's GPT models special?"

    print(f"👤 User: {user_message}")
    print("\n🔄 Generating OpenAI GPT response...")

    try:
        # Generate response (will use fallback since no API keys configured)
        response_text, is_ai_generated = await ai_service.generate_response(
            assistant=assistant,
            user_message=user_message,
            conversation_history=conversation_history
        )

        print(f"\n🤖 {assistant.name}: {response_text}")
        print(f"📊 AI Generated: {is_ai_generated}")

        if not is_ai_generated:
            print("💡 Note: This is a fallback response since no API keys are configured.")

    except Exception as e:
        print(f"❌ Error: {str(e)}")

    print("\n" + "=" * 40)


async def demonstrate_anthropic_integration():
    """Demonstrate Anthropic Claude integration with PydanticAI."""
    print("🧠 Anthropic Claude Integration Demo")
    print("=" * 40)

    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
    from a2a_platform.services.pydantic_ai_response_service import PydanticAIResponseService

    ai_service = PydanticAIResponseService()

    # Create a mock assistant with Anthropic configuration
    assistant = MockAssistant(
        name="Claude Assistant",
        backstory="I am Claude, an AI assistant created by Anthropic. I'm thoughtful, helpful, and aim to be honest and harmless.",
        configuration={
            "ai": {
                "enabled": True,
                "model": "anthropic:claude-3-5-sonnet-latest",
                "temperature": 0.5,
                "max_tokens": 300,
                "system_prompt_template": "You are {name}, {backstory} Provide thoughtful and nuanced responses."
            }
        }
    )

    conversation_history = [
        MockChatMessage("I'm interested in learning about AI ethics.", "user"),
        MockChatMessage("AI ethics is a fascinating and important field. I'd be happy to discuss various aspects with you.", "agent"),
    ]

    user_message = "What are the key principles of responsible AI development?"

    print(f"👤 User: {user_message}")
    print("\n🔄 Generating Anthropic Claude response...")

    try:
        response_text, is_ai_generated = await ai_service.generate_response(
            assistant=assistant,
            user_message=user_message,
            conversation_history=conversation_history
        )

        print(f"\n🧠 {assistant.name}: {response_text}")
        print(f"📊 AI Generated: {is_ai_generated}")

        if not is_ai_generated:
            print("💡 Note: This is a fallback response since no API keys are configured.")

    except Exception as e:
        print(f"❌ Error: {str(e)}")

    print("\n" + "=" * 40)


async def demonstrate_gemini_integration():
    """Demonstrate Google Gemini integration with PydanticAI."""
    print("✨ Google Gemini Integration Demo")
    print("=" * 40)

    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
    from a2a_platform.services.pydantic_ai_response_service import PydanticAIResponseService

    ai_service = PydanticAIResponseService()

    # Create a mock assistant with Gemini configuration
    assistant = MockAssistant(
        name="Gemini Assistant",
        backstory="I am Gemini, Google's advanced AI assistant. I'm creative, knowledgeable, and great at understanding context.",
        configuration={
            "ai": {
                "enabled": True,
                "model": "google-gla:gemini-2.5-flash-preview-05-20",
                "temperature": 0.8,
                "max_tokens": 250,
                "system_prompt_template": "You are {name}, {backstory} Be creative and insightful in your responses."
            }
        }
    )

    conversation_history = [
        MockChatMessage("I'm working on a creative writing project.", "user"),
        MockChatMessage("That sounds exciting! I love helping with creative projects. What kind of writing are you working on?", "agent"),
    ]

    user_message = "Can you help me brainstorm ideas for a science fiction story about AI?"

    print(f"👤 User: {user_message}")
    print("\n🔄 Generating Google Gemini response...")

    try:
        response_text, is_ai_generated = await ai_service.generate_response(
            assistant=assistant,
            user_message=user_message,
            conversation_history=conversation_history
        )

        print(f"\n✨ {assistant.name}: {response_text}")
        print(f"📊 AI Generated: {is_ai_generated}")

        if not is_ai_generated:
            print("💡 Note: This is a fallback response since no API keys are configured.")

    except Exception as e:
        print(f"❌ Error: {str(e)}")

    print("\n" + "=" * 40)


async def demonstrate_model_availability_testing():
    """Demonstrate model availability testing across providers."""
    print("🔍 Model Availability Testing Demo")
    print("=" * 40)

    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
    from a2a_platform.services.pydantic_ai_response_service import PydanticAIResponseService

    ai_service = PydanticAIResponseService()

    # Get supported models
    supported_models = ai_service.get_supported_models()

    print("📋 Supported Models by Provider:")
    for provider, models in supported_models.items():
        print(f"\n{provider.upper()}:")
        for model in models[:3]:  # Show first 3 models
            print(f"  • {model}")
        if len(models) > 3:
            print(f"  ... and {len(models) - 3} more")

    print("\n🧪 Testing Model Availability:")
    test_models = [
        "openai:gpt-4o",
        "anthropic:claude-3-5-sonnet-latest",
        "google-gla:gemini-2.0-flash"
    ]

    for model in test_models:
        try:
            # This will use fallback since no API keys are configured
            available = await ai_service.test_model_availability(model)
            status = "✅ Available" if available else "❌ Unavailable"
            print(f"  {model}: {status}")
        except Exception as e:
            print(f"  {model}: ❌ Error - {str(e)}")

    print("\n💡 Note: Actual availability depends on API key configuration.")
    print("\n" + "=" * 40)


def demonstrate_configuration_examples():
    """Demonstrate different configuration patterns."""
    print("⚙️  Configuration Examples Demo")
    print("=" * 40)

    print("📝 Example Assistant Configurations:\n")

    # OpenAI Configuration
    openai_config = {
        "ai": {
            "enabled": True,
            "model": "openai:gpt-4",
            "temperature": 0.7,
            "max_tokens": 500,
            "system_prompt_template": "You are {name}, {backstory} Be professional and helpful."
        }
    }
    print("🤖 OpenAI GPT-4 Configuration:")
    print(f"   Model: {openai_config['ai']['model']}")
    print(f"   Temperature: {openai_config['ai']['temperature']}")
    print(f"   Max Tokens: {openai_config['ai']['max_tokens']}")

    # Anthropic Configuration
    anthropic_config = {
        "ai": {
            "enabled": True,
            "model": "anthropic:claude-3-5-sonnet-latest",
            "temperature": 0.5,
            "max_tokens": 1000,
            "min_message_length": 5,
            "response_probability": 0.9,
            "system_prompt_template": "You are {name}, {backstory} Provide thoughtful, well-reasoned responses."
        }
    }
    print("\n🧠 Anthropic Claude Configuration:")
    print(f"   Model: {anthropic_config['ai']['model']}")
    print(f"   Temperature: {anthropic_config['ai']['temperature']}")
    print(f"   Response Probability: {anthropic_config['ai']['response_probability']}")

    # Google Gemini Configuration
    gemini_config = {
        "ai": {
            "enabled": True,
            "model": "google-gla:gemini-2.5-flash-preview-05-20",
            "temperature": 0.9,
            "max_tokens": 750,
            "system_prompt_template": "You are {name}, {backstory} Be creative and engaging in your responses."
        }
    }
    print("\n✨ Google Gemini Configuration:")
    print(f"   Model: {gemini_config['ai']['model']}")
    print(f"   Temperature: {gemini_config['ai']['temperature']}")
    print(f"   Max Tokens: {gemini_config['ai']['max_tokens']}")

    print("\n💡 Configuration Tips:")
    print("   • Lower temperature (0.1-0.5) for factual, consistent responses")
    print("   • Higher temperature (0.7-1.0) for creative, varied responses")
    print("   • Adjust max_tokens based on desired response length")
    print("   • Use response_probability for gradual rollouts")

    print("\n" + "=" * 40)


async def main():
    """Run all demonstrations."""
    print("🚀 PydanticAI Multi-Provider Demo for A2A Platform")
    print("=" * 60)
    print()

    # Run demonstrations
    await demonstrate_openai_integration()
    print()

    await demonstrate_anthropic_integration()
    print()

    await demonstrate_gemini_integration()
    print()

    await demonstrate_model_availability_testing()
    print()

    demonstrate_configuration_examples()
    print()

    print("✅ All demonstrations completed!")
    print("\n📚 Next steps:")
    print("1. Set up API keys:")
    print("   • OPENAI_API_KEY for OpenAI models")
    print("   • ANTHROPIC_API_KEY for Claude models")
    print("   • GEMINI_API_KEY or GOOGLE_API_KEY for Gemini models")
    print("2. Configure assistant AI settings in the database")
    print("3. Test with real conversations in the application")
    print("4. Monitor response quality and adjust configurations")
    print("5. Use different providers for different use cases")


if __name__ == "__main__":
    asyncio.run(main())
