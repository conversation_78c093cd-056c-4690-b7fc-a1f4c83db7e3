#!/usr/bin/env python3
"""
Example demonstrating PydanticAI-powered Personal Assistant response generation.

This example shows how to use the PydanticAI response service to generate
intelligent responses for Personal Assistants using multiple LLM providers.
"""

import asyncio
import uuid
from datetime import datetime, UTC

# Mock database models for demonstration
class MockAssistant:
    def __init__(self, name, backstory, configuration=None):
        self.id = uuid.uuid4()
        self.name = name
        self.backstory = backstory
        self.configuration = configuration or {}

class MockChatMessage:
    def __init__(self, content_text, sender_role="user"):
        self.id = uuid.uuid4()
        self.conversation_id = uuid.uuid4()
        self.sender_role = sender_role
        self.content = {"parts": [{"type": "text", "content": content_text}]}
        self.timestamp = datetime.now(UTC)
        self.message_metadata = {}


async def demonstrate_pydantic_ai_response_generation():
    """Demonstrate PydanticAI response generation with fallback."""
    print("🤖 PydanticAI-Powered Personal Assistant Response Generation Demo")
    print("=" * 70)

    # Import the PydanticAI service
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
    from a2a_platform.services.pydantic_ai_response_service import (
        PydanticAIResponseService,
    )

    # Create AI service instance
    ai_service = PydanticAIResponseService()

    # Create a mock assistant with PydanticAI configuration
    assistant = MockAssistant(
        name="Alex",
        backstory="I am a helpful and friendly personal assistant.",
        configuration={
            "ai": {
                "enabled": True,
                "model": "openai:gpt-3.5-turbo",  # PydanticAI format
                "temperature": 0.7,
                "max_tokens": 200,
                "system_prompt_template": (
                    "You are {name}, {backstory} "
                    "Keep responses concise and helpful."
                )
            }
        }
    )

    # Create conversation history
    conversation_history = [
        MockChatMessage("Hi there! I'm new here.", "user"),
        MockChatMessage(
            "Welcome! I'm Alex, your personal assistant. "
            "How can I help you today?", "agent"
        ),
        MockChatMessage("I need help organizing my schedule.", "user"),
        MockChatMessage(
            "I'd be happy to help you organize your schedule! "
            "What specific aspects would you like to work on?", "agent"
        ),
    ]

    # Test user message
    user_message = "Can you help me plan my day tomorrow?"

    print(f"👤 User: {user_message}")
    print("\n🔄 Generating PydanticAI response...")

    try:
        # Generate response (will use fallback since no API keys configured)
        response_text, is_ai_generated = await ai_service.generate_response(
            assistant=assistant,
            user_message=user_message,
            conversation_history=conversation_history
        )

        print(f"\n🤖 {assistant.name}: {response_text}")
        print(f"📊 AI Generated: {is_ai_generated}")

        if not is_ai_generated:
            print("💡 Note: This is a fallback response since no API keys are configured.")
            print("   To use real AI responses, set these environment variables:")
            print("   - OPENAI_API_KEY for OpenAI GPT models")
            print("   - ANTHROPIC_API_KEY for Claude models")
            print("   - GEMINI_API_KEY or GOOGLE_API_KEY for Google models")

    except Exception as e:
        print(f"❌ Error: {str(e)}")

    print("\n" + "=" * 70)


def demonstrate_multi_provider_configuration():
    """Demonstrate different LLM provider configurations."""
    print("🌍 Multi-Provider Configuration Demo")
    print("=" * 40)

    # Different provider configurations
    configurations = [
        {
            "name": "OpenAI GPT-4 Assistant",
            "model": "openai:gpt-4",
            "temperature": 0.3,
            "description": "Professional, focused responses"
        },
        {
            "name": "Claude Opus Assistant",
            "model": "anthropic:claude-opus-4-20250514",
            "temperature": 0.7,
            "description": "Thoughtful, detailed responses"
        },
        {
            "name": "Gemini Assistant",
            "model": "google:gemini-2.5-flash-preview",
            "temperature": 0.9,
            "description": "Creative, dynamic responses"
        },
        {
            "name": "OpenAI o3 Reasoning",
            "model": "openai:o3",
            "temperature": 0.1,
            "description": "Deep reasoning and analysis"
        }
    ]

    for config in configurations:
        print(f"\n🤖 {config['name']}:")
        print(f"   Model: {config['model']}")
        print(f"   Temperature: {config['temperature']}")
        print(f"   Use Case: {config['description']}")

    print("\n💡 To use different providers, update the assistant configuration:")
    print('   {"ai": {"model": "provider:model-name", "temperature": 0.7}}')
    print("\n" + "=" * 40)


def demonstrate_rate_limiting():
    """Demonstrate rate limiting functionality."""
    print("⏱️  Rate Limiting Demo")
    print("=" * 30)

    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
    from a2a_platform.services.pydantic_ai_response_service import RateLimiter

    # Create a rate limiter (3 calls per 5 seconds for demo)
    limiter = RateLimiter(max_calls=3, window_seconds=5)

    print("Testing rate limiter (3 calls per 5 seconds):")

    for i in range(5):
        allowed = limiter.acquire()
        print(f"Call {i+1}: {'✅ Allowed' if allowed else '❌ Rate limited'}")

        if not allowed:
            wait_time = limiter.time_until_next_call()
            print(f"   ⏳ Wait {wait_time:.1f}s until next call allowed")

    print("\n" + "=" * 30)


def demonstrate_fallback_responses():
    """Demonstrate fallback response system."""
    print("🛡️  Fallback Response Demo")
    print("=" * 35)

    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
    from a2a_platform.services.pydantic_ai_response_service import (
        PydanticAIResponseService,
    )

    ai_service = PydanticAIResponseService()

    print("Available fallback responses:")
    for i, response in enumerate(ai_service.fallback_responses, 1):
        print(f"  {i}. {response}")

    print("\n💡 Fallback responses are used when:")
    print("   - API keys are not configured")
    print("   - Rate limits are exceeded")
    print("   - Network connectivity issues")
    print("   - Provider service outages")
    print("\n" + "=" * 35)


async def main():
    """Run all demonstrations."""
    print("🚀 PydanticAI Response Generation System Demo")
    print("=" * 55)
    print()

    # Run demonstrations
    await demonstrate_pydantic_ai_response_generation()
    print()

    demonstrate_multi_provider_configuration()
    print()

    demonstrate_rate_limiting()
    print()

    demonstrate_fallback_responses()
    print()

    print("✅ All demonstrations completed!")
    print("\n📚 Next steps:")
    print("1. Set up API keys for your preferred LLM providers")
    print("2. Configure assistant AI settings in the database")
    print("3. Test with real conversations in the application")
    print("4. Monitor response quality and adjust configurations")
    print("5. Experiment with different models and temperatures")


if __name__ == "__main__":
    asyncio.run(main())
