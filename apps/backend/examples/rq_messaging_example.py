"""
Example of using RQClient with A2AMessageProducer and A2AMessageConsumer.

This example demonstrates how to use RQClient with A2AMessageProducer and A2AMessageConsumer
to send and receive messages using Redis Queue (RQ).
"""

import asyncio
import logging
from typing import Any, Dict

from a2a_platform.messaging.a2a_consumer import A2AMessageConsumer
from a2a_platform.messaging.a2a_producer import A2AMessageProducer
from a2a_platform.messaging.queue_factory import create_queue_client
from a2a_platform.schemas.a2a_context import UserContext

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


class ExampleMessageConsumer(A2AMessageConsumer):
    """
    Example message consumer that processes messages from a queue.
    """

    async def _handle_message(
        self, user_context: UserContext, payload: Dict[str, Any]
    ) -> bool:
        """
        Handle a message with the given user context and payload.

        Args:
            user_context: The user context extracted from the message
            payload: The payload extracted from the message

        Returns:
            bool: True if the message was handled successfully, False otherwise
        """
        logger.info(f"Received message for user {user_context.user_id}")
        logger.info(f"Payload: {payload}")
        return True


async def producer_example() -> None:
    """
    Example of using A2AMessageProducer with RQClient.
    """
    # Create RQ client
    queue_client = await create_queue_client(queue_type="rq")

    # Create producer
    producer = A2AMessageProducer(queue_client=queue_client)

    # Send a message
    message_id = await producer.send_message(
        queue_name="example-queue",
        payload={"action": "test", "data": {"key": "value"}},
        user_id="user123",
        initiating_agent_id="agent456",
    )

    logger.info(f"Sent message with ID: {message_id}")


async def consumer_example() -> None:
    """
    Example of using A2AMessageConsumer with RQClient.
    """
    # Create RQ client
    queue_client = await create_queue_client(queue_type="rq")

    # Create consumer
    consumer = ExampleMessageConsumer(queue_client=queue_client)

    # Start consuming messages
    await consumer.start_consumer(queue_name="example-queue")

    # Keep the consumer running
    while True:
        await asyncio.sleep(1)


async def main() -> None:
    """
    Main entry point for the example.
    """
    # Parse command line arguments
    import argparse

    parser = argparse.ArgumentParser(description="RQ messaging example")
    parser.add_argument(
        "--mode",
        type=str,
        choices=["producer", "consumer"],
        required=True,
        help="Mode to run in (producer or consumer)",
    )
    args = parser.parse_args()

    # Run in the specified mode
    if args.mode == "producer":
        await producer_example()
    elif args.mode == "consumer":
        await consumer_example()


if __name__ == "__main__":
    asyncio.run(main())
