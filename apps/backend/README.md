# A2A Platform Backend

This is the backend service for the A2A Platform, built with:

- FastAPI
- Strawberry GraphQL
- SQLAlchemy
- PostgreSQL
- Redis
- Redis Queue (RQ) for asynchronous task processing
- Clerk.com for authentication

## Getting Started

### Prerequisites

- Python 3.12
- PostgreSQL
- Redis
- Docker (optional, for containerized setup)

### Installation

#### Option 1: Local Development Setup

1. Clone the repository
2. Set up a Python virtual environment (recommended):
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```
3. Install dependencies:
   ```bash
   pip install -e ".[dev]"
   ```
   This installs the package in development mode with all dev dependencies.
4. Use Docker Compose for environment variable management (recommended)
5. Run database migrations:
   ```bash
   alembic upgrade head
   ```

#### Option 2: Docker Setup

1. Start services using Docker Compose:
   ```bash
   # Start just the database and <PERSON><PERSON>
   docker compose up -d db redis

   # Or start all services including the backend
   docker compose up -d
   ```

2. Run database migrations:
   ```bash
   # If you started all services including backend
   docker compose exec backend alembic upgrade head

   # Or if you only started db and redis
   docker compose run --rm \
     -e DATABASE_URL="**************************************/a2a_platform" \
     backend alembic upgrade head
   ```

3. Access the backend services:
   - API Documentation: http://localhost:8000/docs
   - GraphQL Playground: http://localhost:8000/graphql

#### Docker Troubleshooting

1. Check container status:
   ```bash
   docker compose ps
   ```

2. View container logs:
   ```bash
   docker compose logs backend
   ```

3. Restart a container:
   ```bash
   docker compose restart backend
   ```

4. Stop and remove containers:
   ```bash
   docker compose down
   ```

5. For a complete cleanup:
   ```bash
   docker compose down -v  # This will also remove volumes
   ```

### Managing Dependencies

The project uses `pyproject.toml` for dependency management. Dependencies are divided into two groups:

1. **Main dependencies**: Required for the application to run
   ```bash
   # View main dependencies
   grep -A 20 "dependencies =" pyproject.toml
   ```

2. **Development dependencies**: Required for development and testing
   ```bash
   # View development dependencies
   grep -A 10 "dev = \[" pyproject.toml
   ```

To add a new dependency:
1. Add it to the appropriate section in `pyproject.toml`
2. Reinstall the package:
   ```bash
   pip install -e ".[dev]"
   ```

Alternatively, you can use the requirements files:
```bash
# Install main dependencies only
pip install -r requirements.txt

# Install development dependencies
pip install -r requirements-dev.txt  # If available
```

### Development

Start the development server:

```bash
uvicorn a2a_platform.main:app --reload
```

## Database Migrations

For detailed information on database migrations, please refer to the Database Migrations section in the main [README.md](../../README.md#database-migrations) file.

For database changes affecting RQ workers, make sure to:
1. Stop the workers before migrating
2. Apply the migrations
3. Start the workers again

You can manage workers with:
```bash
./scripts/manage-rq-workers.sh --action stop
# ... apply migrations ...
./scripts/manage-rq-workers.sh --action start
```

### Redis Queue (RQ) Workers

The A2A Platform uses Redis Queue (RQ) for asynchronous task processing, particularly for the A2A messaging system. RQ workers are responsible for processing jobs from Redis queues.

#### Running RQ Workers

In development with Docker, RQ workers are automatically started as part of the `rq-worker` service in the Docker Compose configuration. However, you can also manage workers manually using the provided script:

```bash
# From the project root directory
./scripts/manage-rq-workers.sh --action start
```

For more options and commands, see the help:

```bash
./scripts/manage-rq-workers.sh --help
```

#### Implementing RQ in Your Code

To use RQ in your code, you can use the provided `RQClient` implementation of the `QueueClient` interface:

```python
from a2a_platform.messaging.queue_factory import create_queue_client

# Create an RQClient instance
queue_client = await create_queue_client(queue_type="rq")

# Use the client to send a message
message_id = await queue_client.send_message(
    queue_name="default",
    message_body="Hello, world!",
    message_attributes={"priority": "high"}
)
```

For more complex use cases, see the examples in `apps/backend/examples/rq_messaging_example.py`.

### Worker Configuration

Configure workers through environment variables or command line:

- `RQ_WORKER_COUNT`: Number of worker processes (default: 2)
- `RQ_DEFAULT_TIMEOUT`: Job timeout in seconds (default: 180)
- `RQ_DEFAULT_TTL`: Result time-to-live in seconds (default: 86400)

Command line options:
```bash
./scripts/manage-rq-workers.sh --workers 4 --queues "default high-priority"
```

### Pre-commit Hooks

The project uses pre-commit hooks to ensure code quality and consistency. These hooks run automatically when you commit changes, but you can also run them manually.

#### Installation

```bash
# Install pre-commit
pip install pre-commit

# Install the git hooks into the .git directory
pre-commit install

# Migrate any existing configuration to the latest format
pre-commit migrate-config
```


# Run with verbose output
./scripts/run-backend-tests.sh --verbose

# Run with database setup
./scripts/run-backend-tests.sh --setup
```

For CI environments, use the `--ci` flag:

```bash
./scripts/run-backend-tests.sh --ci
```

You can also run pytest directly, but this requires proper environment setup:

```bash
# From the apps/backend directory
pytest
pytest --cov=a2a_platform
pytest tests/unit/
pytest tests/unit/test_specific_file.py
pytest -v
```

#### Test Environment

Tests use a separate environment configuration:

- **Docker Environment (Default)**:
  - Uses Docker Compose with `docker-compose.test.yml`
  - Test database: `a2a_platform_test` in Docker PostgreSQL
  - Test Redis: Redis DB 1 in Docker Redis
  - Environment variables provided directly through Docker Compose

- **CI Environment**:
  - Uses local PostgreSQL instance
  - Test database: `a2a_platform_test` in local PostgreSQL
  - Environment variables provided through CI configuration

For more details, see [docs/testing-with-docker.md](../../docs/testing-with-docker.md).

#### Manual Testing

For manual testing of the user registration flow:
```bash
./tests/e2e/manual_test_user_signup.sh
```

### Clerk Integration

The backend uses Clerk.com for authentication. The integration includes:

1. JWT validation for API requests
2. A webhook endpoint to process Clerk events (e.g., `user.created`, `user.updated`, `user.deleted`)
3. User record creation and management in the database upon Clerk events

For full documentation on the Clerk integration, see [docs/clerk_integration.md](docs/clerk_integration.md).

To manually test the user registration flow:

```bash
./tests/e2e/manual_test_user_signup.sh
```

### Making Authenticated Requests

The A2A Platform supports two authentication methods for API requests:

1. **Clerk JWT Authentication**: For web applications and standard API calls
2. **CLI Token Authentication**: For command-line tools and scripts

#### Using Clerk JWT Authentication

For web applications, you'll typically receive a JWT token from Clerk after user authentication. Include this token in the `Authorization` header of your HTTP requests:

```bash
# Example with curl
curl -X POST http://localhost:8000/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_CLERK_JWT_TOKEN" \
  -d '{"query": "query { me { id email } }"}'

# Example with httpx in Python
import httpx

async def make_authenticated_request():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/graphql",
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {your_clerk_jwt_token}"
            },
            json={
                "query": "query { me { id email } }"
            }
        )
        return response.json()
```

#### Using CLI Tokens

CLI tokens are designed for command-line tools and scripts that need to authenticate with the API. To use CLI tokens:

1. **Generate a CLI Token**: Use the web UI to generate a token (via the Settings page)
2. **Include the Token in Requests**: Use the token in the `Authorization` header

```bash
# Example with curl
curl -X POST http://localhost:8000/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer a2a_cli_12345678_your_token_secret_part" \
  -d '{"query": "query { me { id email } }"}'

# Example with httpx in Python
import httpx

async def make_cli_authenticated_request():
    cli_token = "a2a_cli_12345678_your_token_secret_part"
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8000/graphql",
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {cli_token}"
            },
            json={
                "query": "query { me { id email } }"
            }
        )
        return response.json()
```

#### Token Format

CLI tokens have a specific format: `a2a_cli_<user_id_prefix>_<token_secret>`

- `a2a_cli_`: Fixed prefix for all CLI tokens
- `<user_id_prefix>`: First 8 characters of the user's UUID (without hyphens)
- `<token_secret>`: Random secret part generated for this token

#### Testing Authentication in Your Code

When writing tests for authenticated endpoints, you can use the patterns from our test suite. Here are examples for both direct resolver calls and HTTP requests:

1. **Direct Resolver Testing**:

```python
# Example based on test_authenticated_direct_call in test_auth_requests.py
from unittest.mock import AsyncMock, MagicMock

# Create a mock authenticated context
mock_context = MagicMock(spec=GraphQLContext)
mock_context.clerk_user_id = "test_user_id"  # Set authenticated user ID
mock_context.db_session = AsyncMock()  # Mock database session

# Create mock GraphQL info with our context
mock_info = MagicMock()
mock_info.context = mock_context

# Call resolver directly with authenticated context
result = await your_resolver_function(mock_info, input_data)
```

2. **HTTP Request Testing**:

```python
# Example based on test_authenticated_http_request in test_auth_requests.py
from fastapi.security.http import HTTPAuthorizationCredentials
from unittest.mock import AsyncMock, patch

# Mock auth credentials that would normally come from Clerk middleware
mock_auth_credentials = HTTPAuthorizationCredentials(
    scheme="Bearer",
    credentials="test_user_id"
)

# Make authenticated request with mocked auth
async with AsyncClient(app=app, base_url="http://test") as ac:
    with patch(
        "a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__",
        new_callable=AsyncMock,
        return_value=mock_auth_credentials,
    ):
        response = await ac.post(
            "/graphql",
            json={"query": your_query, "variables": your_variables},
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer test_token"
            },
        )
```

For more examples and detailed implementations, see the authentication test file:
- `tests/unit/api/graphql/test_auth_requests.py`

## Adding a New Clerk Webhook Event Handler

Our backend processes webhook events from Clerk (e.g., user creation, updates) to keep our system synchronized. This guide outlines the standard procedure for adding a handler for a new Clerk event type, ensuring consistency with our existing patterns (like those for `user.created` and `user.deleted`).

The general flow is:
1.  An event occurs in Clerk.
2.  Clerk sends an HTTP POST request to our `/api/v1/webhooks/clerk` endpoint. [498]
3.  The endpoint validates the request (signature, payload structure) and identifies the event type.
4.  A new asynchronous task is enqueued with the event data. [392, 503]
5.  A background worker picks up the task and executes the business logic via a dedicated service function.

Follow these steps to add a handler for a new Clerk event (e.g., `organization.created`, `session.revoked`, etc.):

1.  **Define Pydantic Models for the Event Payload:**
    * **File:** `apps/backend/schemas/clerk_events.py` (or similar)
    * **Action:**
        * Consult the official Clerk Webhooks documentation for the exact JSON payload structure of the new event type.
        * Create new Pydantic models to represent this payload. For an event like `some.event`, you'd typically define `SomeEventData` (for the `data` object) and `SomeEvent(BaseModel)` (for the overall event structure including `type` and `object`).
        * Ensure all necessary fields are included with correct types. Use `Optional` for fields that might not always be present.

2.  **Update the Webhook Router:**
    * **File:** `apps/backend/webhooks/clerk_router.py` (or similar, containing the `POST /api/v1/webhooks/clerk` endpoint)
    * **Action:**
        * Import the new Pydantic event model you created in step 1.
        * Add a new `elif event_type == "your.new.event_type":` block to the main webhook handler function.
        * Inside this block:
            * Validate the incoming JSON against your new Pydantic model (e.g., `parsed_event = YourNewEventModel(**event_json)`).
            * Enqueue a new asynchronous task (see step 3) to process the event, passing the validated `parsed_event.data.dict()`.
            * Return an appropriate HTTP response (e.g., `202 Accepted`).
        * Ensure comprehensive error handling (e.g., for Pydantic validation errors).

3.  **Create an Asynchronous Worker Task:**
    * **File:** `apps/backend/worker/tasks/clerk_tasks.py` (or your project's equivalent for background tasks)
    * **Action:**
        * Define a new asynchronous task function (e.g., `process_your_new_event(event_data_dict: dict)`).
        * This task will be responsible for calling the appropriate service function (see step 4) to handle the business logic.
        * It should include robust error logging and potentially retry mechanisms (depending on the task queue's capabilities).
        * For clarity, you can re-instantiate the specific Pydantic `EventData` model from `event_data_dict` at the beginning of the task.

4.  **Implement Service Function(s):**
    * **File:** `apps/backend/services/your_relevant_service.py` (e.g., `user_service.py`, `organization_service.py`)
    * **Action:**
        * Implement the core business logic for handling the new event. This typically involves:
            * Fetching relevant data from the database.
            * Creating, updating, or deleting records based on the event data.
            * Ensuring operations are atomic (use database transactions).
        * The function signature should clearly accept the necessary data parsed from the event (e.g., `handle_clerk_some_event(db: Session, relevant_id: str, event_data: YourNewEventDataModel)`).

5.  **Add Comprehensive Tests:**
    * **Files:** Corresponding test files in `apps/backend/tests/` (e.g., `tests/webhooks/`, `tests/worker/tasks/`, `tests/services/`)
    * **Action:**
        * **Unit Tests:**
            * Test the new Pydantic models with valid and invalid data.
            * Test the new logic in the webhook router (mocking Svix verification and task enqueuing).
            * Test the new asynchronous task (mocking the service call).
            * Test the new service function(s) thoroughly with various scenarios (mocking database interactions).
        * **Integration Tests:**
            * Ideally, test the end-to-end flow from a mocked webhook request to the expected database change.

**Important Considerations:**

* **Idempotency:** Ensure your worker task or service logic is idempotent, meaning processing the same event multiple times doesn't have unintended side effects. This can be achieved through careful database operations or by tracking processed event IDs (e.g., using Redis, as mentioned for webhook idempotency [490]).
* **Error Handling & Logging:** Implement robust error handling and detailed logging in all new components. This is crucial for debugging webhook issues.
* **Security:** The webhook router should already have signature verification (e.g., using Svix) for the `POST /api/v1/webhooks/clerk` endpoint. Ensure this protection covers all event types. [498]
* **Clerk Documentation:** Always refer to the [official Clerk Webhooks documentation](https://clerk.com/docs/integration/webhooks) for the most up-to-date payload structures and best practices.

By following this pattern, we maintain a consistent, robust, and understandable approach to handling Clerk webhooks. If you have any questions during implementation, please reach out to a senior engineer.

## Project Structure

```
apps/backend/
├── alembic/               # Database migrations
│   └── versions/         # Migration scripts
├── src/
│   └── a2a_platform/
│       ├── api/          # API layer
│       │   ├── graphql/  # GraphQL API
│       │   │   ├── resolvers/
│       │   │   └── schema/
│       │   └── rest/     # REST API
│       │       ├── controllers/
│       │       └── routes/
│       ├── auth/         # Authentication & authorization
│       ├── config/       # Configuration management
│       ├── db/           # Database models and config
│       │   └── models/   # SQLAlchemy models
│       ├── messaging/    # Messaging system
│       │   ├── pub_sub/  # Pub/Sub implementation
│       │   ├── queue/    # Message queue handlers
│       │   ├── rq_client.py  # Redis Queue client
│       │   └── queue_factory.py  # Factory for creating queue clients
│       ├── schemas/      # Pydantic models
│       ├── services/     # Business logic
│       ├── storage/      # File storage & CDN
│       ├── utils/        # Shared utilities
│       ├── vector_db/    # Vector database integration
│       └── workers/      # Background workers
│           ├── clerk_event_handlers.py  # Handlers for Clerk webhook events
│           └── rq_worker.py  # Redis Queue worker script
└── tests/                # Test suite
    ├── e2e/             # End-to-end tests
    ├── integration/     # Integration tests
    └── unit/           # Unit tests
```
