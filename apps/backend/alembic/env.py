import os
import logging
from logging.config import fileConfig
from urllib.parse import urlparse, urlunparse

from sqlalchemy import engine_from_config, pool

from a2a_platform.config.settings import get_settings
from a2a_platform.db import Base
from alembic import context

# Set up logger
logger = logging.getLogger(__name__)

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# Get database URL from environment variable or settings
db_url = os.getenv("DATABASE_URL")
if db_url is None:
    # If neither is set, try to get it from settings
    try:
        settings = get_settings()
        db_url = settings.DATABASE_URL
    except Exception as e:
        raise ValueError(f"Could not get database URL from settings: {e}")

if db_url is None:
    raise ValueError(
        "DATABASE_URL environment variable is required for database connections"
    )

# Import environment utilities
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from a2a_platform.utils.environment import (
    is_docker_environment,
    is_ci_environment,
    should_use_cloud_sql_proxy,
)

# Check if we're in a Cloud Run environment or should use Cloud SQL proxy (don't adjust URLs)
if should_use_cloud_sql_proxy(db_url):
    logger.debug(f"Cloud Run/Production environment detected. Preserving original database URL.")
else:
    # Check if we're in a CI environment (but not inside Docker)
    is_ci = is_ci_environment() and not is_docker_environment()

    # Adjust the database URL for CI environments
    if is_ci:
        # Always use 'localhost' in CI environments
        if "@db:" in db_url:
            # Parse the URL
            parsed_url = urlparse(db_url)

            # Replace 'db' with 'localhost' in the netloc part
            netloc = parsed_url.netloc.replace("@db:", "@localhost:")

            # Reconstruct the URL
            db_url = urlunparse(
                (
                    parsed_url.scheme,
                    netloc,
                    parsed_url.path,
                    parsed_url.params,
                    parsed_url.query,
                    parsed_url.fragment,
                )
            )

            logger.debug(
                f"CI environment detected. Adjusted database URL to use 'localhost' instead of 'db': {db_url}"
            )
    else:
        # Always use 'db' in Docker environments
        if "@localhost:" in db_url:
            # Parse the URL
            parsed_url = urlparse(db_url)

            # Replace 'localhost' with 'db' in the netloc part
            netloc = parsed_url.netloc.replace("@localhost:", "@db:")

            # Reconstruct the URL
            db_url = urlunparse(
                (
                    parsed_url.scheme,
                    netloc,
                    parsed_url.path,
                    parsed_url.params,
                    parsed_url.query,
                    parsed_url.fragment,
                )
            )

            logger.debug(
                f"Docker environment detected. Adjusted database URL to use 'db' instead of 'localhost': {db_url}"
            )

# Convert asyncpg URL to psycopg2 URL if needed
if "asyncpg" in db_url:
    db_url = db_url.replace("postgresql+asyncpg", "postgresql")

config.set_main_option("sqlalchemy.url", db_url)

# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = Base.metadata


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode."""
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode."""
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(connection=connection, target_metadata=target_metadata)

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
