"""create assistant_objectives table

Revision ID: 2023_06_15_create_assistant_objectives_table
Revises: 1a2b3c4d5e6f
Create Date: 2023-06-15 12:00:00.000000

"""
from typing import Sequence, Union

import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB, UUID

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "20250520173817"
down_revision: str = "20250520173816"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create the assistant_objectives table
    op.create_table(
        "assistant_objectives",
        sa.Column(
            "id",
            UUID(as_uuid=True),
            primary_key=True,
            server_default=sa.text("gen_random_uuid()"),
        ),
        sa.Column(
            "assistant_id",
            UUID(as_uuid=True),
            # sa.ForeignKey("assistants.id", ondelete="CASCADE"),  # Uncomment when assistants table exists
            nullable=False,
            index=True,
        ),
        sa.Column("objective_text", sa.Text(), nullable=False),
        sa.Column(
            "status",
            sa.String(20),
            nullable=False,
            server_default=sa.text("'active'"),
        ),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
            onupdate=sa.text("now()"),
        ),
        sa.Column("completed_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column(
            "metadata_json", JSONB, nullable=True, server_default=sa.text("'{}'::jsonb")
        ),
        sa.CheckConstraint(
            "status IN ('active', 'completed', 'cancelled')",
            name="assistant_objectives_status_check",
        ),
    )

    # Create an index for efficient retrieval of active objectives
    op.create_index(
        "idx_assistant_objectives_assistant_id_status",
        "assistant_objectives",
        ["assistant_id", "status"],
    )


def downgrade() -> None:
    # Drop the assistant_objectives table
    op.drop_index("idx_assistant_objectives_assistant_id_status")
    op.drop_table("assistant_objectives")
