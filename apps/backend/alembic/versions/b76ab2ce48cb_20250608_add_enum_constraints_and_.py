"""20250608_add_enum_constraints_and_assistant_status

Revision ID: b76ab2ce48cb
Revises: a55b5d004d44
Create Date: 2025-06-08 00:07:13.403928

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "b76ab2ce48cb"
down_revision = "a55b5d004d44"
branch_labels = None
depends_on = None


def upgrade():
    # Add status column to assistants table
    op.add_column(
        "assistants",
        sa.Column(
            "status",
            sa.String(20),
            nullable=False,
            server_default="setup_pending",
        ),
    )

    # Add check constraint for assistant status
    op.create_check_constraint(
        "ck_assistants_status",
        "assistants",
        "status IN ('active', 'inactive', 'setup_pending')",
    )

    # Add check constraint for agent review status (registered_agents table)
    op.create_check_constraint(
        "ck_registered_agents_review_status",
        "registered_agents",
        "review_status IN ('pending', 'approved', 'rejected') OR review_status IS NULL",
    )

    # Update the existing chat message sender_role constraint to use our new enum values
    # (The constraint already exists, but we want to make sure it matches our enum)
    op.drop_constraint("ck_chat_message_sender_role", "chat_messages", type_="check")
    op.create_check_constraint(
        "ck_chat_message_sender_role",
        "chat_messages",
        "sender_role IN ('user', 'agent')",
    )


def downgrade():
    # Remove assistant status column
    op.drop_column("assistants", "status")

    # Remove agent review status constraint
    op.drop_constraint("ck_registered_agents_review_status", "registered_agents", type_="check")

    # Restore original chat message constraint (this is actually the same, so just recreate it)
    op.drop_constraint("ck_chat_message_sender_role", "chat_messages", type_="check")
    op.create_check_constraint(
        "ck_chat_message_sender_role",
        "chat_messages",
        "sender_role IN ('user', 'agent')",
    )
