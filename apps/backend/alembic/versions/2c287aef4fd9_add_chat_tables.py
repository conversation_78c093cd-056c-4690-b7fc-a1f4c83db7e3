"""add_chat_tables

Revision ID: 2c287aef4fd9
Revises: 5184db28a987
Create Date: 2025-05-25 01:09:46.489059

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "2c287aef4fd9"
down_revision = "5184db28a987"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "conversations",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("user_id", sa.UUID(), nullable=False),
        sa.Column("assistant_id", sa.UUID(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("last_message_at", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["assistant_id"], ["assistants.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "user_id", "assistant_id", name="uq_user_assistant_conversation"
        ),
    )
    op.create_index(
        op.f("ix_conversations_assistant_id"),
        "conversations",
        ["assistant_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_conversations_user_id"), "conversations", ["user_id"], unique=False
    )
    op.create_table(
        "chat_messages",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("conversation_id", sa.UUID(), nullable=False),
        sa.Column("sender_role", sa.Text(), nullable=False),
        sa.Column("content", postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column("timestamp", sa.DateTime(timezone=True), nullable=False),
        sa.Column(
            "message_metadata",
            postgresql.JSONB(astext_type=sa.Text()),
            server_default="{}",
            nullable=True,
        ),
        sa.CheckConstraint(
            "sender_role IN ('user', 'agent')", name="ck_chat_message_sender_role"
        ),
        sa.ForeignKeyConstraint(
            ["conversation_id"], ["conversations.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_chat_messages_conversation_id"),
        "chat_messages",
        ["conversation_id"],
        unique=False,
    )
    op.create_index(
        "ix_chat_messages_conversation_timestamp",
        "chat_messages",
        ["conversation_id", "timestamp"],
        unique=False,
    )
    op.alter_column(
        "assistant_objectives",
        "metadata_json",
        existing_type=postgresql.JSONB(astext_type=sa.Text()),
        type_=sa.JSON(),
        existing_nullable=True,
        existing_server_default=sa.text("'{}'::jsonb"),
    )
    op.drop_index(op.f("idx_assistants_user_id"), table_name="assistants")
    op.alter_column(
        "cli_tokens",
        "description",
        existing_type=sa.VARCHAR(),
        type_=sa.Text(),
        existing_nullable=True,
    )
    op.alter_column(
        "cli_tokens",
        "created_at",
        existing_type=postgresql.TIMESTAMP(timezone=True),
        type_=sa.DateTime(),
        existing_nullable=False,
        existing_server_default=sa.text("now()"),
    )
    op.alter_column(
        "cli_tokens",
        "last_used_at",
        existing_type=postgresql.TIMESTAMP(timezone=True),
        type_=sa.DateTime(),
        existing_nullable=True,
    )
    op.alter_column(
        "cli_tokens",
        "expires_at",
        existing_type=postgresql.TIMESTAMP(timezone=True),
        type_=sa.DateTime(),
        existing_nullable=True,
    )
    op.create_index(
        op.f("ix_cli_tokens_user_id"), "cli_tokens", ["user_id"], unique=False
    )
    op.drop_constraint(
        op.f("cli_tokens_user_id_fkey"), "cli_tokens", type_="foreignkey"
    )
    op.create_foreign_key(None, "cli_tokens", "users", ["user_id"], ["id"])
    op.alter_column(
        "tasks",
        "metadata_json",
        existing_type=postgresql.JSONB(astext_type=sa.Text()),
        type_=sa.JSON(),
        existing_nullable=True,
        existing_server_default=sa.text("'{}'::jsonb"),
    )
    op.alter_column(
        "users",
        "timezone",
        existing_type=sa.VARCHAR(length=50),
        type_=sa.String(length=64),
        existing_nullable=True,
        existing_server_default=sa.text("'UTC'::character varying"),
    )
    op.alter_column(
        "users",
        "preferences",
        existing_type=postgresql.JSON(astext_type=sa.Text()),
        type_=postgresql.JSONB(astext_type=sa.Text()),
        existing_nullable=True,
        existing_server_default=sa.text("'{}'::jsonb"),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "users",
        "preferences",
        existing_type=postgresql.JSONB(astext_type=sa.Text()),
        type_=postgresql.JSON(astext_type=sa.Text()),
        existing_nullable=True,
        existing_server_default=sa.text("'{}'::jsonb"),
    )
    op.alter_column(
        "users",
        "timezone",
        existing_type=sa.String(length=64),
        type_=sa.VARCHAR(length=50),
        existing_nullable=True,
        existing_server_default=sa.text("'UTC'::character varying"),
    )
    op.alter_column(
        "tasks",
        "metadata_json",
        existing_type=sa.JSON(),
        type_=postgresql.JSONB(astext_type=sa.Text()),
        existing_nullable=True,
        existing_server_default=sa.text("'{}'::jsonb"),
    )
    op.drop_constraint(None, "cli_tokens", type_="foreignkey")
    op.create_foreign_key(
        op.f("cli_tokens_user_id_fkey"),
        "cli_tokens",
        "users",
        ["user_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.drop_index(op.f("ix_cli_tokens_user_id"), table_name="cli_tokens")
    op.alter_column(
        "cli_tokens",
        "expires_at",
        existing_type=sa.DateTime(),
        type_=postgresql.TIMESTAMP(timezone=True),
        existing_nullable=True,
    )
    op.alter_column(
        "cli_tokens",
        "last_used_at",
        existing_type=sa.DateTime(),
        type_=postgresql.TIMESTAMP(timezone=True),
        existing_nullable=True,
    )
    op.alter_column(
        "cli_tokens",
        "created_at",
        existing_type=sa.DateTime(),
        type_=postgresql.TIMESTAMP(timezone=True),
        existing_nullable=False,
        existing_server_default=sa.text("now()"),
    )
    op.alter_column(
        "cli_tokens",
        "description",
        existing_type=sa.Text(),
        type_=sa.VARCHAR(),
        existing_nullable=True,
    )
    op.create_index(
        op.f("idx_assistants_user_id"), "assistants", ["user_id"], unique=False
    )
    op.alter_column(
        "assistant_objectives",
        "metadata_json",
        existing_type=sa.JSON(),
        type_=postgresql.JSONB(astext_type=sa.Text()),
        existing_nullable=True,
        existing_server_default=sa.text("'{}'::jsonb"),
    )
    op.drop_index("ix_chat_messages_conversation_timestamp", table_name="chat_messages")
    op.drop_index(op.f("ix_chat_messages_conversation_id"), table_name="chat_messages")
    op.drop_table("chat_messages")
    op.drop_index(op.f("ix_conversations_user_id"), table_name="conversations")
    op.drop_index(op.f("ix_conversations_assistant_id"), table_name="conversations")
    op.drop_table("conversations")
    # ### end Alembic commands ###
