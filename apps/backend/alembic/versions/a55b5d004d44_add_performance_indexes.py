"""add_performance_indexes

Revision ID: a55b5d004d44
Revises: 2c287aef4fd9
Create Date: 2025-06-05 15:26:56.926181

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "a55b5d004d44"
down_revision = "2c287aef4fd9"
branch_labels = None
depends_on = None


def upgrade():
    """Add performance indexes for chat operations."""

    # Add composite index on conversations for user + assistant lookups
    op.create_index(
        'ix_conversations_user_assistant',
        'conversations',
        ['user_id', 'assistant_id'],
        unique=False
    )

    # Add index on conversations.user_id with last_message_at for faster user conversation lookups
    op.create_index(
        'ix_conversations_user_id_last_message',
        'conversations',
        ['user_id', 'last_message_at'],
        unique=False
    )

    # Add descending timestamp index for chat_messages for faster recent message queries
    op.create_index(
        'ix_chat_messages_conversation_timestamp_desc',
        'chat_messages',
        ['conversation_id', sa.text('timestamp DESC')],
        unique=False
    )


def downgrade():
    """Remove performance indexes."""

    # Drop the indexes we created
    op.drop_index('ix_conversations_user_assistant', table_name='conversations')
    op.drop_index('ix_conversations_user_id_last_message', table_name='conversations')
    op.drop_index('ix_chat_messages_conversation_timestamp_desc', table_name='chat_messages')
