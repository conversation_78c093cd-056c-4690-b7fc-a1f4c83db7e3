"""create_assistants_table

Revision ID: b63b0f35b43f
Revises: 20250522024302
Create Date: 2025-05-23 00:42:11.884389

"""

import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB, UUID

from alembic import op

# revision identifiers, used by Alembic.
revision = "b63b0f35b43f"
down_revision = "20250522024302"
branch_labels = None
depends_on = None


def upgrade():
    # Create the assistants table
    op.create_table(
        "assistants",
        sa.Column(
            "id",
            UUID(as_uuid=True),
            primary_key=True,
            server_default=sa.text("gen_random_uuid()"),
        ),
        sa.Column(
            "user_id",
            UUID(as_uuid=True),
            sa.<PERSON>ey("users.id", ondelete="CASCADE"),
            nullable=False,
            unique=True,  # Enforces one assistant per user
            index=True,
        ),
        sa.Column("name", sa.Text(), nullable=False),
        sa.Column("backstory", sa.Text(), nullable=False),
        sa.Column(
            "avatar_file_id",
            UUID(as_uuid=True),
            # Note: file_metadata table doesn't exist yet, this will be
            # uncommented
            # when it's created
            # sa.ForeignKey("file_metadata.id", ondelete="SET NULL"),
            nullable=True,
        ),
        sa.Column(
            "configuration",
            JSONB,
            nullable=True,
            server_default=sa.text("'{}'::jsonb"),
        ),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
            onupdate=sa.text("now()"),
        ),
    )

    # Create index for user_id (already unique, but good for performance)
    op.create_index("idx_assistants_user_id", "assistants", ["user_id"])


def downgrade():
    # Drop the assistants table
    op.drop_index("idx_assistants_user_id", "assistants")
    op.drop_table("assistants")
