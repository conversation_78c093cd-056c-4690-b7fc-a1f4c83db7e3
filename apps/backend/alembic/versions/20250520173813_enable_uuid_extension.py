"""Enable UUID extension

This migration enables the uuid-ossp extension which is required for generating UUIDs.
"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "20250520173813"
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    op.execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"')


def downgrade():
    op.execute('DROP EXTENSION IF EXISTS "uuid-ossp" CASCADE')
