"""create registered agents table

Revision ID: create_registered_agents_table
Revises: 1a2b3c4d5e6f
Create Date: 2023-07-14 12:00:00.000000

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "20250520173815"
down_revision: str = "20250520173814"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create registered_agents table
    op.create_table(
        "registered_agents",
        sa.Column(
            "agent_definition_id",
            sa.Text(),
            primary_key=True,
            index=True,
            comment="Unique identifier for the SA type/version (e.g., 'summarizer_v1').",
        ),
        sa.Column(
            "name",
            sa.Text(),
            nullable=False,
            comment="Human-readable name of the SA type.",
        ),
        sa.Column(
            "description",
            sa.Text(),
            nullable=True,
            comment="Description of the SA's purpose.",
        ),
        sa.Column(
            "version",
            sa.Text(),
            nullable=False,
            comment="Version of this agent definition.",
        ),
        sa.Column(
            "endpoint_url",
            sa.Text(),
            nullable=True,
            comment="Internal URL for synchronous HTTP calls (if supported).",
        ),
        sa.Column(
            "async_queue_name",
            sa.Text(),
            nullable=True,
            comment="Message queue name for asynchronous calls (if supported).",
        ),
        sa.Column(
            "capabilities",
            sa.JSON(),
            nullable=True,
            server_default=sa.text("'{}'::jsonb"),
            comment='Agent capabilities (e.g., { "streaming": true } based on A2A schema).',
        ),
        sa.Column(
            "skills",
            sa.JSON(),
            nullable=True,
            server_default=sa.text("'[]'::jsonb"),
            comment="List of skills (based on AgentSkill structure from A2A schema).",
        ),
        sa.Column(
            "authentication_info",
            sa.JSON(),
            nullable=True,
            server_default=sa.text("'{}'::jsonb"),
            comment="Info on how internal services should authenticate (if needed).",
        ),
        sa.Column(
            "status",
            sa.Text(),
            nullable=False,
            server_default=sa.text("'active'"),
            comment="Whether this agent type is currently available ('active' or 'inactive').",
        ),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.func.now(),
            nullable=False,
            comment="Timestamp of record creation.",
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.func.now(),
            onupdate=sa.func.now(),
            nullable=False,
            comment="Timestamp of last update.",
        ),
        sa.CheckConstraint(
            "status IN ('active', 'inactive')", name="registered_agents_status_check"
        ),
    )


def downgrade() -> None:
    # Drop registered_agents table
    op.drop_table("registered_agents")
