"""add_assistant_foreign_keys

Revision ID: 50f080b89bc0
Revises: b63b0f35b43f
Create Date: 2025-05-23 00:50:07.156618

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "50f080b89bc0"
down_revision = "b63b0f35b43f"
branch_labels = None
depends_on = None


def upgrade():
    # Clean up orphaned records before adding foreign key constraints
    # Delete assistant_objectives that reference non-existent assistants
    op.execute("""
        DELETE FROM assistant_objectives
        WHERE assistant_id NOT IN (SELECT id FROM assistants)
    """)

    # Delete tasks that reference non-existent assistants
    op.execute("""
        DELETE FROM tasks
        WHERE assistant_id NOT IN (SELECT id FROM assistants)
    """)

    # Add foreign key constraint from assistant_objectives to assistants
    op.create_foreign_key(
        "fk_assistant_objectives_assistant_id",
        "assistant_objectives",
        "assistants",
        ["assistant_id"],
        ["id"],
        ondelete="CASCADE",
    )

    # Add foreign key constraint from tasks to assistants
    op.create_foreign_key(
        "fk_tasks_assistant_id",
        "tasks",
        "assistants",
        ["assistant_id"],
        ["id"],
        ondelete="CASCADE",
    )


def downgrade():
    # Drop foreign key constraints
    op.drop_constraint("fk_tasks_assistant_id", "tasks", type_="foreignkey")
    op.drop_constraint(
        "fk_assistant_objectives_assistant_id",
        "assistant_objectives",
        type_="foreignkey",
    )
