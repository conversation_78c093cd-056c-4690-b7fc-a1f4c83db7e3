"""add_is_pa_setup_complete_to_users

Revision ID: 5184db28a987
Revises: 50f080b89bc0
Create Date: 2025-05-23 04:18:45.103593

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "5184db28a987"
down_revision = "50f080b89bc0"
branch_labels = None
depends_on = None


def upgrade():
    # Add is_pa_setup_complete column to users table
    op.add_column(
        "users",
        sa.Column(
            "is_pa_setup_complete",
            sa.<PERSON>(),
            nullable=False,
            server_default=sa.text("false"),
        ),
    )


def downgrade():
    # Remove is_pa_setup_complete column from users table
    op.drop_column("users", "is_pa_setup_complete")
