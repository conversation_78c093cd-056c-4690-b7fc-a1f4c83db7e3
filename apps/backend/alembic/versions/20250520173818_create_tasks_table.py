"""create tasks table

Revision ID: 20250520173818
Revises: 20250520173817
Create Date: 2025-05-21 06:00:00.000000

"""
from typing import Sequence, Union

import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB, UUID

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "20250520173818"
down_revision: str = "20250520173817"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create the tasks table
    op.create_table(
        "tasks",
        sa.Column(
            "id",
            UUID(as_uuid=True),
            primary_key=True,
            server_default=sa.text("gen_random_uuid()"),
        ),
        sa.Column(
            "assistant_id",
            UUID(as_uuid=True),
            # sa.ForeignKey("assistants.id", ondelete="CASCADE"),  # Uncomment when assistants table exists
            nullable=False,
            index=True,
        ),
        sa.Column(
            "objective_id",
            UUID(as_uuid=True),
            sa.<PERSON>("assistant_objectives.id", ondelete="SET NULL"),
            nullable=True,
            index=True,
        ),
        sa.Column(
            "parent_task_id",
            UUID(as_uuid=True),
            sa.ForeignKey("tasks.id", ondelete="CASCADE"),
            nullable=True,
            index=True,
        ),
        sa.Column(
            "depends_on_task_id",
            UUID(as_uuid=True),
            sa.ForeignKey("tasks.id", ondelete="SET NULL"),
            nullable=True,
            index=True,
        ),
        sa.Column(
            "idempotency_key",
            sa.Text(),
            nullable=True,
            index=True,
        ),
        sa.Column("description", sa.Text(), nullable=False),
        sa.Column(
            "status",
            sa.String(30),
            nullable=False,
            server_default=sa.text("'todo'"),
            index=True,
        ),
        sa.Column("due_date", sa.DateTime(timezone=True), nullable=True),
        sa.Column("completed_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
            onupdate=sa.text("now()"),
        ),
        sa.Column("retry_count", sa.Integer(), nullable=False, server_default=sa.text("0")),
        sa.Column("last_progress_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("lease_owner_id", sa.Text(), nullable=True),
        sa.Column("lease_acquired_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column(
            "lease_expires_at",
            sa.DateTime(timezone=True),
            nullable=True,
            index=True,
        ),
        sa.Column(
            "metadata_json",
            JSONB,
            nullable=True,
            server_default=sa.text("'{}'::jsonb"),
        ),
        sa.CheckConstraint(
            "status IN ('todo', 'leased', 'in_progress', 'pending_dependency', 'pending_human_input', "
            "'pending_user_clarification', 'pending_admin_review', 'done', 'cancelled', 'retrying', "
            "'failed_timeout', 'failed_max_retries', 'failed_human_timeout', 'quarantined', 'escalated')",
            name="tasks_status_check",
        ),
    )

    # Create indexes
    op.create_index("idx_tasks_assistant_id_status", "tasks", ["assistant_id", "status"])
    op.create_index("idx_tasks_status_lease_expires", "tasks", ["status", "lease_expires_at"])
    op.create_index("idx_tasks_status_updated", "tasks", ["status", "updated_at"])
    op.create_index(
        "idx_tasks_assistant_idempotency",
        "tasks",
        ["assistant_id", "idempotency_key"],
        unique=True,
    )


def downgrade() -> None:
    # Drop the tasks table
    op.drop_index("idx_tasks_assistant_idempotency")
    op.drop_index("idx_tasks_status_updated")
    op.drop_index("idx_tasks_status_lease_expires")
    op.drop_index("idx_tasks_assistant_id_status")
    op.drop_table("tasks")
