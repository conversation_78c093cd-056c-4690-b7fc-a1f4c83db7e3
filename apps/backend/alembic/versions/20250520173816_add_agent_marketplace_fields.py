"""add agent marketplace fields

Revision ID: add_agent_marketplace_fields
Revises: 1a2b3c4d5e6f
Create Date: 2023-07-15 12:00:00.000000

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "20250520173816"
down_revision: str = "20250520173815"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add new columns to registered_agents table
    op.add_column(
        "registered_agents",
        sa.Column(
            "developer_id",
            sa.Text(),
            nullable=True,
            comment="Identifier for the developer who created this agent (for marketplace).",
        ),
    )
    op.add_column(
        "registered_agents",
        sa.Column(
            "pricing_info",
            sa.JSON(),
            nullable=True,
            server_default=sa.text("'{}'::jsonb"),
            comment="Pricing information for the agent in the marketplace (e.g., subscription, per-call).",
        ),
    )
    op.add_column(
        "registered_agents",
        sa.Column(
            "review_status",
            sa.Text(),
            nullable=True,
            comment="Review status for marketplace agents (e.g., 'pending', 'approved', 'rejected').",
        ),
    )


def downgrade() -> None:
    # Remove columns from registered_agents table
    op.drop_column("registered_agents", "review_status")
    op.drop_column("registered_agents", "pricing_info")
    op.drop_column("registered_agents", "developer_id")
