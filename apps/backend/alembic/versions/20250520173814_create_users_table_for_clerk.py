"""create users table for clerk

Revision ID: 1a2b3c4d5e6f
Revises:
Create Date: 2023-05-14 12:00:00.000000

"""

from typing import Sequence, Union

import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "20250520173814"
down_revision: str = "20250520173813"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        "users",
        sa.Column(
            "id",
            UUID(as_uuid=True),
            primary_key=True,
            server_default=sa.text("gen_random_uuid()"),
        ),
        sa.Column(
            "clerk_user_id", sa.String(64), nullable=False, unique=True, index=True
        ),
        sa.Column("email", sa.String(255), nullable=False, unique=True, index=True),
        sa.Column("timezone", sa.String(50), nullable=True),
        sa.Column(
            "preferences", sa.JSON, nullable=True, server_default=sa.text("'{}'::jsonb")
        ),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
            onupdate=sa.text("now()"),
        ),
    )


def downgrade() -> None:
    op.drop_table("users")
