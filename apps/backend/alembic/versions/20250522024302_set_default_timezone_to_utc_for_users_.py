"""Set default timezone to UTC for users.timezone

Revision ID: 20250522024302
Revises: 20250521171118
Create Date: 2025-05-22 02:12:39.931539

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "20250522024302"
down_revision = "20250521171118"  # Points to create_cli_tokens_table
branch_labels = None
depends_on = None


def upgrade():
    # Set default timezone to UTC for users.timezone column
    op.alter_column(
        "users",
        "timezone",
        server_default=sa.text("'UTC'")
    )


def downgrade():
    # Remove the default timezone from users.timezone column
    op.alter_column(
        "users",
        "timezone",
        server_default=None
    )
