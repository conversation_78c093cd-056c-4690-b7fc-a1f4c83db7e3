#!/usr/bin/env python
"""
Test script to verify that the RQ worker monkey patch works when imported.
"""

import sys
import os
import importlib.util

# First, let's import the Worker class from rq
from rq import Worker

# Check if the Worker class has the register_task_function method
# It should not have it yet
if hasattr(Worker, "register_task_function"):
    print("ERROR: Worker class already has register_task_function method before import")
    sys.exit(1)

# Now let's manually import just the part of the module that applies the monkey patch
# This avoids importing the settings module which requires environment variables

# Get the path to the rq_worker.py file
rq_worker_path = os.path.join(
    os.path.dirname(__file__),
    "src", "a2a_platform", "workers", "rq_worker.py"
)

# Read the file content
with open(rq_worker_path, "r") as f:
    content = f.read()

# Extract the monkey patch code
monkey_patch_start = content.find("# Add the register_task_function method")
monkey_patch_end = content.find("from a2a_platform.config.settings import get_settings")
monkey_patch_code = content[monkey_patch_start:monkey_patch_end]

# Create a temporary module to execute the monkey patch
spec = importlib.util.spec_from_loader("temp_module", loader=None)
temp_module = importlib.util.module_from_spec(spec)

# Add necessary imports to the temporary module
temp_module.__dict__["Worker"] = Worker
temp_module.__dict__["logger"] = __import__("logging").getLogger(__name__)
temp_module.__dict__["Any"] = __import__("typing").Any
temp_module.__dict__["Dict"] = __import__("typing").Dict
temp_module.__dict__["Protocol"] = __import__("typing").Protocol

# Define the TaskFunction protocol in the temporary module
from typing import Protocol, Dict, Any

class TaskFunction(Protocol):
    async def __call__(self, message_dict: Dict[str, Any]) -> bool: ...

temp_module.__dict__["TaskFunction"] = TaskFunction

# Execute the monkey patch code in the context of the temporary module
exec(monkey_patch_code, temp_module.__dict__)

# Check if the Worker class has the register_task_function method now
if __name__ == "__main__":
    if hasattr(Worker, "register_task_function"):
        print("SUCCESS: Worker class has register_task_function method after monkey patch")
        sys.exit(0)
    else:
        print("ERROR: Worker class does not have register_task_function method after monkey patch")
        sys.exit(1)
