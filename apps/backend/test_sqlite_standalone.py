#!/usr/bin/env python3
"""
Standalone SQLite test that doesn't depend on PostgreSQL or main application modules.

This script demonstrates that SQLite tests can run completely independently
when the PostgreSQL database is down.

Run with: python test_sqlite_standalone.py
"""

import asyncio
import uuid
from datetime import datetime, timezone

from sqlalchemy import (
    JSON,
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    Index,
    MetaData,
    String,
    Table,
    Text,
    create_engine,
    event,
    text,
)
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.pool import StaticPool
from sqlalchemy.sql import func


def create_sqlite_engine():
    """Create a SQLite engine for testing."""
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        poolclass=StaticPool,
        connect_args={
            "check_same_thread": False,
        },
        echo=False,
    )

    # Enable foreign key constraints for async engine
    @event.listens_for(engine.sync_engine, "connect")
    def set_sqlite_pragma_async(dbapi_connection, connection_record):
        cursor = dbapi_connection.cursor()
        cursor.execute("PRAGMA foreign_keys=ON")
        cursor.close()

    return engine


def create_tables(connection):
    """Create SQLite-compatible tables."""
    sqlite_metadata = MetaData()

    # Define SQLite-compatible User table
    Table(
        "users",
        sqlite_metadata,
        Column("id", String(36), primary_key=True),
        Column("clerk_user_id", String(64), unique=True, nullable=False),
        Column("email", String(255), unique=True, nullable=False),
        Column("timezone", String(64), nullable=True, server_default=text("'UTC'")),
        Column("preferences", JSON, nullable=True, server_default=text("'{}'")),
        Column(
            "is_pa_setup_complete",
            Boolean,
            nullable=False,
            default=False,
            server_default=text("0"),
        ),
        Column(
            "created_at",
            DateTime(timezone=True),
            nullable=False,
            server_default=func.current_timestamp(),
        ),
        Column(
            "updated_at",
            DateTime(timezone=True),
            nullable=False,
            server_default=func.current_timestamp(),
        ),
    )

    # Define SQLite-compatible Assistant table
    assistants_table = Table(
        "assistants",
        sqlite_metadata,
        Column("id", String(36), primary_key=True),
        Column(
            "user_id",
            String(36),
            ForeignKey("users.id", ondelete="CASCADE"),
            nullable=False,
            unique=True,
        ),
        Column("name", String(255), nullable=False),
        Column("backstory", Text, nullable=False),
        Column("avatar_file_id", String(36), nullable=True),
        Column("configuration", JSON, nullable=True),
        Column(
            "created_at",
            DateTime(timezone=True),
            nullable=False,
            server_default=func.current_timestamp(),
        ),
        Column(
            "updated_at",
            DateTime(timezone=True),
            nullable=False,
            server_default=func.current_timestamp(),
        ),
    )

    # Create indexes
    Index("ix_assistants_user_id", assistants_table.c.user_id)

    # Create all tables
    sqlite_metadata.create_all(connection)


async def test_sqlite_basic_operations():
    """Test basic SQLite operations without PostgreSQL dependency."""
    print("🚀 Starting SQLite standalone test...")
    
    # Create engine and tables
    engine = create_sqlite_engine()
    
    async with engine.begin() as conn:
        await conn.run_sync(create_tables)
    
    print("✅ Tables created successfully")
    
    # Create session
    async_session_maker = async_sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session_maker() as session:
        # Test 1: Create a user
        user_id = str(uuid.uuid4())
        await session.execute(
            text("""
                INSERT INTO users (id, clerk_user_id, email, timezone)
                VALUES (:id, :clerk_user_id, :email, :timezone)
            """),
            {
                "id": user_id,
                "clerk_user_id": f"test_{uuid.uuid4()}",
                "email": f"test_{uuid.uuid4()}@example.com",
                "timezone": "UTC",
            },
        )
        await session.commit()
        print("✅ User created successfully")
        
        # Test 2: Create an assistant
        assistant_id = str(uuid.uuid4())
        await session.execute(
            text("""
                INSERT INTO assistants (id, user_id, name, backstory)
                VALUES (:id, :user_id, :name, :backstory)
            """),
            {
                "id": assistant_id,
                "user_id": user_id,
                "name": "Test Assistant",
                "backstory": "Test backstory",
            },
        )
        await session.commit()
        print("✅ Assistant created successfully")
        
        # Test 3: Verify foreign key constraint
        try:
            await session.execute(
                text("""
                    INSERT INTO assistants (id, user_id, name, backstory)
                    VALUES (:id, :user_id, :name, :backstory)
                """),
                {
                    "id": str(uuid.uuid4()),
                    "user_id": str(uuid.uuid4()),  # Non-existent user
                    "name": "Invalid Assistant",
                    "backstory": "Should fail",
                },
            )
            await session.commit()
            print("❌ Foreign key constraint test failed - should have raised error")
        except Exception as e:
            print(f"✅ Foreign key constraint working: {type(e).__name__}")
            await session.rollback()
        
        # Test 4: Verify unique constraint
        try:
            await session.execute(
                text("""
                    INSERT INTO assistants (id, user_id, name, backstory)
                    VALUES (:id, :user_id, :name, :backstory)
                """),
                {
                    "id": str(uuid.uuid4()),
                    "user_id": user_id,  # Same user as before
                    "name": "Duplicate Assistant",
                    "backstory": "Should fail",
                },
            )
            await session.commit()
            print("❌ Unique constraint test failed - should have raised error")
        except Exception as e:
            print(f"✅ Unique constraint working: {type(e).__name__}")
            await session.rollback()
    
    await engine.dispose()
    print("🎉 All SQLite tests passed! Database is working independently.")


if __name__ == "__main__":
    asyncio.run(test_sqlite_basic_operations())
