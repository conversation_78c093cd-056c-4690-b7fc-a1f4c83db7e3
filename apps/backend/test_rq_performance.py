#!/usr/bin/env python
"""
Performance test script for Redis Queue (RQ).

This script tests the performance of the RQ implementation by:
1. Measuring throughput (messages per second)
2. Monitoring resource usage
3. Testing queue backlog handling

The script supports a reduced load mode for CI environments by setting
the RQ_TEST_REDUCED_LOAD environment variable to 'true'.
"""

import asyncio
import logging
import os
import sys
import time
import uuid
import psutil
from typing import List, Dict, Any, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath("src"))

# Import the necessary modules
import redis
from rq import Queue
from a2a_platform.messaging.rq_client import RQClient
from a2a_platform.config.settings import get_settings

# Get settings
settings = get_settings()

# Check if we're running in reduced load mode (for CI)
REDUCED_LOAD = os.environ.get("RQ_TEST_REDUCED_LOAD", "").lower() == "true"
if REDUCED_LOAD:
    logger.info("Running in reduced load mode for CI environment")


async def measure_throughput(
    num_messages: int = 100, batch_size: int = 10
) -> Tuple[float, float]:
    """
    Measure the throughput of the RQ implementation.

    Args:
        num_messages: Number of messages to send
        batch_size: Number of messages to send in each batch

    Returns:
        Tuple[float, float]: (messages per second, average latency in ms)
    """
    logger.info(f"Measuring throughput with {num_messages} messages")

    # Create an RQ client
    rq_client = RQClient(redis_url=settings.REDIS_URL)

    # Generate test messages
    test_messages = []
    for i in range(num_messages):
        test_message_id = str(uuid.uuid4())
        test_message_body = f"Performance test message {i}"
        test_attributes = {"test_id": test_message_id, "index": i}
        test_messages.append((test_message_body, test_attributes))

    # Measure the time it takes to send all messages
    start_time = time.time()

    # Send messages in batches
    for i in range(0, num_messages, batch_size):
        batch = test_messages[i:i+batch_size]
        tasks = []
        for message_body, message_attributes in batch:
            task = rq_client.send_message(
                queue_name="default",
                message_body=message_body,
                message_attributes=message_attributes
            )
            tasks.append(task)

        # Wait for all tasks in the batch to complete
        await asyncio.gather(*tasks)

    end_time = time.time()

    # Calculate throughput
    elapsed_time = end_time - start_time
    throughput = num_messages / elapsed_time
    avg_latency = (elapsed_time / num_messages) * 1000  # in milliseconds

    logger.info(f"Throughput: {throughput:.2f} messages/second")
    logger.info(f"Average latency: {avg_latency:.2f} ms")

    return throughput, avg_latency


async def monitor_resource_usage(duration: int = 10) -> Dict[str, float]:
    """
    Monitor resource usage during RQ operations.

    Args:
        duration: Duration to monitor in seconds

    Returns:
        Dict[str, float]: Resource usage metrics
    """
    logger.info(f"Monitoring resource usage for {duration} seconds")

    # Get the current process
    process = psutil.Process()

    # Initialize metrics
    cpu_percentages = []
    memory_usages = []

    # Monitor resource usage for the specified duration
    start_time = time.time()
    while time.time() - start_time < duration:
        # Get CPU usage
        cpu_percent = process.cpu_percent(interval=0.1)
        cpu_percentages.append(cpu_percent)

        # Get memory usage
        memory_info = process.memory_info()
        memory_usages.append(memory_info.rss / 1024 / 1024)  # in MB

        # Sleep for a short time
        await asyncio.sleep(0.5)

    # Calculate average metrics
    avg_cpu = sum(cpu_percentages) / len(cpu_percentages)
    avg_memory = sum(memory_usages) / len(memory_usages)

    # Get Redis memory usage
    redis_conn = redis.from_url(settings.REDIS_URL)
    redis_info = redis_conn.info(section="memory")
    redis_memory = redis_info["used_memory"] / 1024 / 1024  # in MB

    metrics = {
        "avg_cpu_percent": avg_cpu,
        "avg_memory_mb": avg_memory,
        "redis_memory_mb": redis_memory,
    }

    logger.info(f"Resource usage metrics: {metrics}")

    return metrics


async def test_queue_backlog(
    num_messages: int = 1000, processing_delay: float = 0.01
) -> Tuple[int, float]:
    """
    Test queue backlog handling by sending many messages and processing them slowly.

    Args:
        num_messages: Number of messages to send
        processing_delay: Simulated processing delay in seconds

    Returns:
        Tuple[int, float]: (max queue length, time to process all messages)
    """
    logger.info(f"Testing queue backlog with {num_messages} messages")

    # Create an RQ client
    rq_client = RQClient(redis_url=settings.REDIS_URL)

    # Connect to Redis
    redis_conn = redis.from_url(settings.REDIS_URL)

    # Create a test queue
    test_queue_name = f"test-backlog-{uuid.uuid4()}"
    queue = Queue(name=test_queue_name, connection=redis_conn)

    # Send messages to the queue
    logger.info(f"Sending {num_messages} messages to {test_queue_name}")
    for i in range(num_messages):
        test_message = {
            "id": str(uuid.uuid4()),
            "content": f"Backlog test message {i}",
            "timestamp": time.time()
        }

        queue.enqueue(
            "a2a_platform.workers.rq_worker.process_message",
            test_message,
            job_id=test_message["id"]
        )

    # Check the queue length
    queue_length = len(queue.get_job_ids())
    logger.info(f"Queue length after sending: {queue_length}")

    # Process messages with a delay
    start_time = time.time()
    processed = 0
    max_queue_length = queue_length

    while processed < num_messages:
        # Get the current queue length
        current_length = len(queue.get_job_ids())
        max_queue_length = max(max_queue_length, current_length)

        # If there are no more messages, we're done
        if current_length == 0:
            break

        # Process a batch of messages
        batch_size = min(10, current_length)
        for _ in range(batch_size):
            # Simulate processing delay
            await asyncio.sleep(processing_delay)
            processed += 1

        logger.info(f"Processed {processed}/{num_messages} messages, queue length: {current_length}")

    end_time = time.time()
    processing_time = end_time - start_time

    logger.info(f"Max queue length: {max_queue_length}")
    logger.info(f"Time to process all messages: {processing_time:.2f} seconds")

    return max_queue_length, processing_time


async def main() -> int:
    """
    Run all performance tests.

    Returns:
        int: 0 if all tests pass, 1 otherwise
    """
    try:
        # Adjust test parameters based on whether we're in reduced load mode
        if REDUCED_LOAD:
            # Use smaller values for CI environment
            throughput_messages = 20
            monitor_duration = 3
            backlog_messages = 20
        else:
            # Use normal values for local testing
            throughput_messages = 100
            monitor_duration = 10
            backlog_messages = 100

        # Measure throughput
        throughput, latency = await measure_throughput(num_messages=throughput_messages)

        # Monitor resource usage
        metrics = await monitor_resource_usage(duration=monitor_duration)

        # Test queue backlog
        max_queue_length, processing_time = await test_queue_backlog(num_messages=backlog_messages)

        # Print summary
        logger.info("Performance Test Summary:")
        logger.info(f"Throughput: {throughput:.2f} messages/second")
        logger.info(f"Average latency: {latency:.2f} ms")
        logger.info(f"CPU usage: {metrics['avg_cpu_percent']:.2f}%")
        logger.info(f"Memory usage: {metrics['avg_memory_mb']:.2f} MB")
        logger.info(f"Redis memory usage: {metrics['redis_memory_mb']:.2f} MB")
        logger.info(f"Max queue length: {max_queue_length}")
        logger.info(f"Processing time: {processing_time:.2f} seconds")

        return 0
    except Exception as e:
        logger.error(f"Error in performance tests: {str(e)}")
        return 1


if __name__ == "__main__":
    # Run the main function
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
