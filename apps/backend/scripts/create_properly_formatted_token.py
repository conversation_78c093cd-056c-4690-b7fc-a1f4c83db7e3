import asyncio
import logging
import os
import sys
import uuid
from datetime import datetime, timezone

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set required environment variables
os.environ["DATABASE_URL"] = os.environ.get("DATABASE_URL") or "postgresql://postgres:postgres@localhost:5432/a2a_platform_test"

# Import necessary SQLAlchemy components
from sqlalchemy import text, select
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

async def create_token_with_correct_format():
    """Create a CLI token with the correct format: a2a_cli_<user_id_prefix>_<token_secret>."""
    # Get database URL from environment
    database_url = os.environ.get("DATABASE_URL")
    logger.info(f"Using database URL: {database_url}")
    
    # Convert to asyncpg format if needed
    if database_url.startswith("postgresql://"):
        async_database_url = database_url.replace("postgresql://", "postgresql+asyncpg://")
    else:
        async_database_url = database_url
    
    logger.info(f"Using async database URL: {async_database_url}")
    
    # Create engine and session
    engine = create_async_engine(async_database_url)
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    try:
        async with async_session() as session:
            # First, get the user ID for our test user
            logger.info("Getting user ID for test user...")
            stmt = select(text("id")).select_from(text("users")).where(text("clerk_user_id = :clerk_user_id"))
            result = await session.execute(stmt, {"clerk_user_id": "user_test_12345"})
            user_row = result.fetchone()
            
            if not user_row:
                logger.error("Test user not found! Make sure to run create_test_user.py first.")
                return
            
            user_id = user_row[0]
            logger.info(f"Found user with ID: {user_id}")
            
            # Generate token components
            token_prefix = "a2a_cli"
            
            # Use first 8 chars of user ID (without hyphens) for the user_id_prefix
            user_id_str = str(user_id).replace("-", "")[:8]
            
            # Generate a token secret
            token_secret = uuid.uuid4().hex[:16]
            
            # Create the full token with the correct format
            full_token = f"{token_prefix}_{user_id_str}_{token_secret}"
            
            # Create a CLI token in the database
            logger.info("Creating CLI token...")
            insert_sql = """
            INSERT INTO cli_tokens (
                id, user_id, token_prefix, hashed_token, salt_hex, description, created_at
            ) VALUES (
                :id, :user_id, :token_prefix, :hashed_token, :salt_hex, :description, :created_at
            ) RETURNING id
            """
            
            result = await session.execute(
                text(insert_sql),
                {
                    "id": uuid.uuid4(),
                    "user_id": user_id,
                    "token_prefix": token_prefix,
                    "hashed_token": token_secret,  # Store the secret part directly
                    "salt_hex": user_id_str,  # Store the user ID prefix as the "salt"
                    "description": "Created with correct format",
                    "created_at": datetime.now()  # Use timezone-naive datetime
                }
            )
            await session.commit()
            
            token_id = result.fetchone()[0]
            logger.info(f"CLI token created successfully with ID: {token_id}")
            logger.info(f"Full token (for use): {full_token}")
            
    except Exception as e:
        logger.error(f"Error creating token: {e}", exc_info=True)
    finally:
        await engine.dispose()


if __name__ == "__main__":
    logger.info("Starting token creation script with correct format")
    asyncio.run(create_token_with_correct_format()) 