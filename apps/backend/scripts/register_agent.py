import argparse
import json
import os
import sys

from pydantic import ValidationError

# Adjust the Python path to import from the src directory
# This assumes the script is in apps/backend/scripts/ and src is apps/backend/src/
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
SRC_DIR = os.path.join(os.path.dirname(SCRIPT_DIR), "src")
sys.path.insert(0, SRC_DIR)

# Conditional import for type checking, actual import within main after path adjustment
from typing import TYPE_CHECKING, Optional

if TYPE_CHECKING:
    from sqlalchemy.orm import Session

    from a2a_platform.services.agent_service import AgentService

    # from a2a_platform.db.session import SessionLocal # Example: your DB session factory


def get_db_session_for_script() -> "Session":  # Forward reference for Session
    """
    Placeholder for getting a database session.
    In a real application, this would import your actual database session setup.
    Example: from a2a_platform.db.session import SessionLocal
    """
    try:
        # This import should resolve correctly now that SRC_DIR is in sys.path
        from a2a_platform.db.session import SessionLocal

        db = SessionLocal()
        return db
    except ImportError:
        print(
            "ERROR: Database session factory (e.g., a2a_platform.db.session.SessionLocal) not found."
        )
        print("Please ensure it is configured in src/a2a_platform/db/session.py.")
        print("This script cannot connect to the database without it.")
        sys.exit(1)
    except Exception as e:
        print(f"ERROR: Could not create database session: {e}")
        sys.exit(1)


def main():
    parser = argparse.ArgumentParser(
        description="Register Specialized Agents from a JSON configuration file."
    )
    parser.add_argument(
        "config_file",
        type=str,
        help="Path to the JSON file containing agent configurations (single object or list).",
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Validate agent configurations without writing to the database.",
    )
    args = parser.parse_args()

    # Now that sys.path is adjusted, perform the actual imports
    from a2a_platform.schemas.agent_schemas import RegisteredAgentCreate
    from a2a_platform.services.agent_service import AgentService

    try:
        with open(args.config_file, "r") as f:
            agent_configs_data = json.load(f)
    except FileNotFoundError:
        print(f"ERROR: Configuration file not found: {args.config_file}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"ERROR: Invalid JSON in configuration file {args.config_file}: {e}")
        sys.exit(1)

    if not isinstance(agent_configs_data, list):
        agent_configs_data = [agent_configs_data]  # Allow single object or list

    db_session: Optional["Session"] = None
    agent_service_instance: Optional["AgentService"] = None

    if not args.dry_run:
        db_session = get_db_session_for_script()
        agent_service_instance = AgentService(db_session=db_session)

    success_count = 0
    failure_count = 0

    for i, config_data in enumerate(agent_configs_data, 1):
        print(f"\nProcessing agent configuration #{i}...")
        try:
            # Validate data with Pydantic schema
            agent_create_input = RegisteredAgentCreate(**config_data)
            print(
                f"  Validated: Agent ID '{agent_create_input.agent_definition_id}', Name: '{agent_create_input.name}'"
            )

            if not args.dry_run and db_session and agent_service_instance:
                try:
                    created_agent = agent_service_instance.register_agent(
                        agent_create_input
                    )
                    print(
                        f"  SUCCESS: Registered agent '{created_agent.agent_definition_id}' (Name: {created_agent.name})."
                    )
                    success_count += 1
                except (
                    ValueError
                ) as e:  # Raised by service for duplicate ID or other DB issues
                    print(
                        f"  ERROR registering agent '{agent_create_input.agent_definition_id}': {e}"
                    )
                    failure_count += 1
                except Exception as e:
                    print(
                        f"  UNEXPECTED ERROR registering agent '{agent_create_input.agent_definition_id}': {e}"
                    )
                    failure_count += 1
            elif args.dry_run:
                print(
                    f"  DRY RUN: Agent '{agent_create_input.agent_definition_id}' would be registered."
                )
                success_count += (
                    1  # Count as success for validation purposes in dry run
                )

        except ValidationError as e:
            agent_id_info = config_data.get("agent_definition_id", f"Config #{i}")
            print(f"  ERROR: Validation failed for agent '{agent_id_info}':")
            for error in e.errors():
                loc_str = ".".join(str(loc_item) for loc_item in error["loc"])
                print(f"    - Field '{loc_str}': {error['msg']}")
            failure_count += 1
        except Exception as e:
            agent_id_info = config_data.get("agent_definition_id", f"Config #{i}")
            print(f"  UNEXPECTED ERROR processing agent '{agent_id_info}': {e}")
            failure_count += 1

    print("\n--- Registration Summary ---")
    if args.dry_run:
        print(f"Dry run complete. Validated configurations: {success_count}")
    else:
        print(f"Successfully registered agents: {success_count}")
    print(f"Failed to process/register agents: {failure_count}")

    if db_session and not args.dry_run:
        db_session.close()


if __name__ == "__main__":
    main()
