import asyncio
import logging
import os
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database URL for Docker environment
DATABASE_URL = "**************************************/a2a_platform_test"

async def check_users():
    """List all users in the database"""
    logger.info(f"Connecting to database: {DATABASE_URL}")
    
    # Convert to asyncpg format
    async_db_url = DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")
    
    # Create engine and session
    engine = create_async_engine(async_db_url)
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    try:
        async with async_session() as session:
            # Query all users
            result = await session.execute(text("SELECT id, clerk_user_id, email FROM users"))
            users = result.fetchall()
            
            logger.info(f"Found {len(users)} users in the database:")
            for user in users:
                logger.info(f"id={user[0]}, clerk_user_id={user[1]}, email={user[2]}")
    except Exception as e:
        logger.error(f"Error checking users: {str(e)}")
    finally:
        await engine.dispose()

if __name__ == "__main__":
    asyncio.run(check_users()) 