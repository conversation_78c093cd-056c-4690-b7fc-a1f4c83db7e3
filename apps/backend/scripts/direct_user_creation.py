import asyncio
import logging
import os
import sys
import uuid

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set required environment variables directly
os.environ["DATABASE_URL"] = "postgresql://postgres:postgres@localhost:5432/a2a_platform_test"
os.environ["REDIS_URL"] = "redis://localhost:6379/1"
os.environ["CLERK_API_KEY"] = "sk_test_r31UYPnfYQz2EaOhjCX9apAw9aNaRFBAitjtbudxZu"
os.environ["CLERK_JWT_PUBLIC_KEY"] = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAq0MHKZfdYUE1G5q2rgKb
eXCHE7m2pHl34ttmk5NUiLcQy8XGGoSa57Xnriz/PJyPrtHQl1dSqQotdLIMgHCr
H7DipU1bRqGJEYhfGoEQmBLYRKrn3YtcaStkzwRbLfQPuGkpyNLoK1FmQkSBTkKw
O87t6VMndeAte1As9/VvG+MXJjJQ0qM+0wwi0sN8tVacSikVuAblf3gLD1t8ByV8
+9PkPGnly5TqBRlky6xjxCmjaUFWZ8arfTSnadOBdPTfdIWw2XFoAJVmsHLp4WpO
d7TURT7Lq6H9w/S5lc9XAXYttSkpeJpYp8Gce430dO6wbl/j2xveeZlYSiR/C8V9
nwIDAQAB
-----END PUBLIC KEY-----"""
os.environ["CLERK_WEBHOOK_SECRET"] = "test_clerk_webhook_signing_secret"
os.environ["STORAGE_BUCKET"] = "a2a-dev-storage"
os.environ["CDN_URL"] = "http://localhost:8000"
os.environ["PUBSUB_PROJECT_ID"] = "clean-algebra-459903-b1"
os.environ["DEBUG"] = "true"

# Now import the modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "src")))

# Import these after environment variables are set
from sqlalchemy import Column, String, TIMESTAMP, JSON, text, select
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.dialects.postgresql import UUID

# Create base
Base = declarative_base()

# Define User model
class User(Base):
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    clerk_user_id = Column(String, nullable=False, unique=True)
    email = Column(String, nullable=False, unique=True)
    timezone = Column(String, nullable=True)
    preferences = Column(JSON, nullable=True)
    created_at = Column(TIMESTAMP, server_default=text('NOW()'))
    updated_at = Column(TIMESTAMP, server_default=text('NOW()'))

async def create_user_directly():
    """Create a test user directly in the database"""
    # Get database URL from environment
    database_url = os.environ.get("DATABASE_URL")
    logger.info(f"Using database URL: {database_url}")
    
    # Convert to asyncpg format if needed
    if database_url.startswith("postgresql://"):
        async_database_url = database_url.replace("postgresql://", "postgresql+asyncpg://")
    else:
        async_database_url = database_url
    
    logger.info(f"Converted to async URL: {async_database_url}")
    
    # Create engine and session
    engine = create_async_engine(async_database_url)
    async_session_factory = sessionmaker(
        bind=engine, 
        class_=AsyncSession, 
        expire_on_commit=False
    )
    
    # Test clerk_user_id
    clerk_user_id = "user_test_12345"
    email = "<EMAIL>"
    
    logger.info(f"Creating user with clerk_user_id: {clerk_user_id}")
    
    # Create session and add user
    async with async_session_factory() as session:
        try:
            # Check if user already exists
            result = await session.execute(
                select(User).where(User.clerk_user_id == clerk_user_id)
            )
            existing_user = result.scalar_one_or_none()
            
            if existing_user:
                logger.info(f"User already exists with ID: {existing_user.id}")
                return
                
            # Create a new user
            user = User(
                clerk_user_id=clerk_user_id,
                email=email,
                preferences={}
            )
            
            session.add(user)
            await session.commit()
            
            logger.info(f"User created successfully with ID: {user.id}")
        except Exception as e:
            logger.error(f"Error creating user: {e}")
            await session.rollback()
            raise

if __name__ == "__main__":
    asyncio.run(create_user_directly()) 