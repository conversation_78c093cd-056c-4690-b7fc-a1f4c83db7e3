#!/bin/bash

# setup_backend_env.sh
# Installation and setup script for the A2A Platform backend environment.

# Exit immediately if a command exits with a non-zero status.
set -e

# --- Configuration ---
PYTHON_CMD="python3.12" # Change to "python" if python3 is not your default Python 3 command
PIP_CMD="pip3"       # Change to "pip" if pip3 is not your default pip for Python 3
VENV_DIR="venv"      # Name of the virtual environment directory
REQUIREMENTS_FILE="requirements.txt"
ENV_EXAMPLE_FILE=".env.example"

# --- Helper Functions ---
info() {
    echo "[INFO] $1"
}

warn() {
    echo "[WARN] $1" >&2
}

error() {
    echo "[ERROR] $1" >&2
    exit 1
}

# --- Main Script Logic ---

# Ensure the script is run from the apps/backend directory or adjust paths accordingly
if [[ "$(basename "$(pwd)")" != "backend" && "$(basename "$(pwd)")" != "scripts" ]]; then
    if [ -d "apps/backend" ]; then
        info "Changing directory to apps/backend"
        cd apps/backend
    elif [ -d "../" ] && [ -f "../requirements.txt" ]; then # If in scripts dir
        info "Changing directory to parent (should be apps/backend)"
        cd ..
    else
        error "This script should be run from the 'apps/backend' directory or its 'scripts' subdirectory."
    fi
elif [[ "$(basename "$(pwd)")" == "scripts" ]]; then
    info "Running from scripts directory. Changing to parent (apps/backend)."
    cd ..
fi

info "Starting backend environment setup..."

# 1. Check for Python 3
if ! command -v $PYTHON_CMD &> /dev/null; then
    error "$PYTHON_CMD is not installed. Please install Python 3."
fi
info "Python 3 found: $($PYTHON_CMD --version)"

# 2. Check for pip
if ! $PYTHON_CMD -m pip --version &> /dev/null; then # Check pip associated with $PYTHON_CMD
    error "pip for $PYTHON_CMD is not available. Please ensure pip is installed for your Python 3 environment."
fi
info "pip found: $($PYTHON_CMD -m pip --version)"

# 3. Create Virtual Environment if it doesn't exist
if [ ! -d "$VENV_DIR" ]; then
    info "Creating Python virtual environment in '$VENV_DIR/'..."
    $PYTHON_CMD -m venv $VENV_DIR
    if [ $? -ne 0 ]; then
        error "Failed to create virtual environment. Check your Python installation."
    fi
    info "Virtual environment created."
else
    info "Virtual environment '$VENV_DIR/' already exists."
fi

# 4. Activate Virtual Environment (Instructions)
info "---------------------------------------------------------------------"
info "To activate the virtual environment, run the following command:"
info "  source $VENV_DIR/bin/activate"
info "You need to do this in your current shell session after this script finishes."
info "---------------------------------------------------------------------"

# 5. Install Dependencies (using the pip from the virtual environment)
info "Installing dependencies into $VENV_DIR (editable mode with dev extras)..."
# Ensure pip from venv is used
"$VENV_DIR/bin/pip" install --upgrade pip # Upgrade pip first
"$VENV_DIR/bin/pip" install -e ".[dev]"
if [ $? -ne 0 ]; then
    error "Failed to install dependencies using 'pip install -e \".[dev]\". Check pyproject.toml and your network connection."
fi
info "Dependencies installed successfully."

# 6. Environment Variables
info "---------------------------------------------------------------------"
info "Environment Variables:"
info "This application uses environment variables for configuration."
info "Docker Compose is the exclusive method for managing environment variables."
info "All environment variables are defined in docker-compose.yml and docker-compose.test.yml."
info "See the .env.example file for a reference of required variables."
info "---------------------------------------------------------------------"

# 7. Database Migrations (Instructional)
info "---------------------------------------------------------------------"
info "Database Migrations (Alembic):"
info "Once your environment variables are configured with the correct DATABASE_URL:"
info " 1. Ensure your database server is running and accessible."
info " 2. Activate the virtual environment: source $VENV_DIR/bin/activate"
info " 3. Run database migrations: alembic upgrade head"
info "    (You might need to run 'alembic revision -m \"create_initial_tables\" --autogenerate' first if you have new models and no revisions)"
info "---------------------------------------------------------------------"


# 8. Completion Message
info "Backend environment setup script finished!"
info "Please activate the virtual environment (\`source $VENV_DIR/bin/activate\`) to use the installed packages."
info "Remember to configure your environment variables and run database migrations if needed."

exit 0
