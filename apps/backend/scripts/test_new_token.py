import asyncio
import json
import logging

import aiohttp

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_new_cli_token():
    """Test authentication with a newly created CLI token"""

    # Replace this with one of the newly created tokens
    new_token = "a2a_cli_ee85b0d9_j77Zq2iPBJCsLWMcCm1gS6jPSrLZBX0C1h8e5lRvPoI"

    # Set up headers with the CLI token in X-A2A-CLI-Token
    headers = {
        'Content-Type': 'application/json',
        'X-A2A-CLI-Token': new_token
    }

    logger.info("Testing with newly created token in X-A2A-CLI-Token header...")
    logger.info(f"Headers: {headers}")

    # GraphQL query for the me query
    query = '''
    query {
      me {
        id
        email
      }
    }
    '''

    logger.info(f"Query: {query}")

    # Send the request
    async with aiohttp.ClientSession() as session:
        async with session.post(
            'https://localhost:8000/graphql',
            headers=headers,
            json={'query': query}
        ) as response:
            data = await response.text()
            logger.info(f'Status: {response.status}')
            logger.info(f'Response: {data}')

            if response.status == 200:
                try:
                    json_data = json.loads(data)
                    if 'data' in json_data and json_data['data'] and 'me' in json_data['data']:
                        me_data = json_data['data']['me']
                        logger.info(f"✅ Successfully authenticated with the new CLI token")
                        logger.info(f"User data: {me_data}")
                    else:
                        if 'errors' in json_data:
                            for error in json_data['errors']:
                                logger.error(f"GraphQL error: {error['message']}")
                        else:
                            logger.error("No user data in response")
                except json.JSONDecodeError:
                    logger.error("Failed to parse JSON response")
            else:
                logger.error(f"Request failed with status: {response.status}")

if __name__ == "__main__":
    logger.info("Starting test with newly created CLI token")
    asyncio.run(test_new_cli_token())
