import asyncio
import json
import logging

import aiohttp

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_me_query():
    """Test the 'me' query which should require authentication"""
    query = '''
    query {
      me {
        id
        email
      }
    }
    '''

    headers = {
        'Content-Type': 'application/json',
        'X-Clerk-User-Id': 'user_test_12345'  # Our test user ID
    }

    logger.info("Testing 'me' query with X-Clerk-User-Id header...")
    logger.info(f"Query: {query}")
    logger.info(f"Headers: {headers}")

    # Send the request
    async with aiohttp.ClientSession() as session:
        async with session.post(
            'https://localhost:8000/graphql',
            headers=headers,
            json={'query': query}
        ) as response:
            data = await response.text()
            logger.info(f'Status: {response.status}')
            logger.info(f'Response: {data}')

            if response.status == 200:
                try:
                    json_data = json.loads(data)
                    if 'data' in json_data and json_data['data'] and 'me' in json_data['data']:
                        me_data = json_data['data']['me']
                        logger.info(f"✅ Successfully authenticated. User data: {me_data}")
                    else:
                        if 'errors' in json_data:
                            for error in json_data['errors']:
                                logger.error(f"GraphQL error: {error['message']}")
                        else:
                            logger.error("No user data in response")
                except json.JSONDecodeError:
                    logger.error("Failed to parse JSON response")
            else:
                logger.error(f"Request failed with status: {response.status}")

if __name__ == "__main__":
    asyncio.run(test_me_query())
