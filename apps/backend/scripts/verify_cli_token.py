import asyncio
import logging
import uuid

import aiohttp

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_cli_token_auth():
    """Test authentication using a CLI token with the correct format"""
    # Create a proper CLI token format: a2a_cli_<user_id_prefix>_<token_secret>
    user_id = "ee85b0d9-1c7c-4fae-bd4c-10726df4eeec"  # Our test user ID from previous scripts
    user_id_str = user_id.replace("-", "")[:8]  # First 8 chars of UUID without hyphens
    token_secret = "test_token_secret"
    full_token = f"a2a_cli_{user_id_str}_{token_secret}"
    
    logger.info(f"Created formatted token: {full_token}")
    
    # Set up headers with the CLI token in the Authorization header
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {full_token}'
    }
    
    # Simple query to test authentication
    query = '''
    query {
      me {
        id
        email
      }
    }
    '''
    
    logger.info("Testing authentication with correctly formatted CLI token...")
    logger.info(f"Headers: {headers}")
    logger.info(f"Query: {query}")
    
    # Send the request
    async with aiohttp.ClientSession() as session:
        async with session.post(
            'http://localhost:8000/graphql',
            headers=headers,
            json={'query': query}
        ) as response:
            data = await response.text()
            logger.info(f'Status: {response.status}')
            logger.info(f'Response: {data}')
            
            if response.status == 200:
                try:
                    json_data = json.loads(data)
                    if 'data' in json_data and json_data['data'] and 'me' in json_data['data']:
                        me_data = json_data['data']['me']
                        logger.info(f"✅ Successfully authenticated with CLI token")
                        logger.info(f"User data: {me_data}")
                    else:
                        if 'errors' in json_data:
                            for error in json_data['errors']:
                                logger.error(f"GraphQL error: {error['message']}")
                        else:
                            logger.error("No user data in response")
                except json.JSONDecodeError:
                    logger.error("Failed to parse JSON response")
            else:
                logger.error(f"Request failed with status: {response.status}")

if __name__ == "__main__":
    import json  # Import json module at the global level
    logger.info("Starting CLI token authentication test with correct format")
    asyncio.run(test_cli_token_auth()) 