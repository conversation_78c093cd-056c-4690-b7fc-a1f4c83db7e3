import asyncio
import hashlib
import logging
import os
import sys
import uuid
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set required environment variables
os.environ["DATABASE_URL"] = os.environ.get("DATABASE_URL") or "postgresql://postgres:postgres@localhost:5432/a2a_platform_test"

# Import necessary SQLAlchemy components
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine

async def create_token_directly():
    """Create a CLI token directly in the database using raw SQL."""
    # Get database URL from environment
    database_url = os.environ.get("DATABASE_URL")
    logger.info(f"Using database URL: {database_url}")
    
    # Convert to asyncpg format if needed
    if database_url.startswith("postgresql://"):
        async_database_url = database_url.replace("postgresql://", "postgresql+asyncpg://")
    else:
        async_database_url = database_url
    
    logger.info(f"Using async database URL: {async_database_url}")
    
    # Create engine
    engine = create_async_engine(async_database_url)
    
    try:
        # First, get the user ID for our test user
        async with engine.connect() as conn:
            logger.info("Getting user ID for test user...")
            result = await conn.execute(
                text("SELECT id FROM users WHERE clerk_user_id = :clerk_user_id"),
                {"clerk_user_id": "user_test_12345"}
            )
            user_row = result.fetchone()
            
            if not user_row:
                logger.error("Test user not found! Make sure to run create_test_user.py first.")
                return
            
            user_id = user_row[0]
            logger.info(f"Found user with ID: {user_id}")
            
            # Generate token components
            token_prefix = "cli_"
            random_part = uuid.uuid4().hex[:16]
            token = f"{token_prefix}{random_part}"
            
            # Hash the token
            salt = os.urandom(16).hex()
            hashed_token = hashlib.sha256((salt + token).encode()).hexdigest()
            
            # Create a CLI token
            logger.info("Creating CLI token...")
            insert_sql = """
            INSERT INTO cli_tokens (
                id, user_id, token_prefix, hashed_token, salt_hex, description, created_at
            ) VALUES (
                :id, :user_id, :token_prefix, :hashed_token, :salt_hex, :description, :created_at
            ) RETURNING id
            """
            
            result = await conn.execute(
                text(insert_sql),
                {
                    "id": uuid.uuid4(),
                    "user_id": user_id,
                    "token_prefix": token_prefix,
                    "hashed_token": hashed_token,
                    "salt_hex": salt,
                    "description": "Created by debug script",
                    "created_at": datetime.now()
                }
            )
            await conn.commit()
            
            token_id = result.fetchone()[0]
            logger.info(f"CLI token created successfully with ID: {token_id}")
            logger.info(f"Full token (for use): {token}")
            
    except Exception as e:
        logger.error(f"Error creating token: {e}", exc_info=True)
    finally:
        await engine.dispose()


if __name__ == "__main__":
    logger.info("Starting direct token creation script")
    asyncio.run(create_token_directly()) 