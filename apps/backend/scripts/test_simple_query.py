import asyncio
import json
import logging

import aiohttp

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_simple_query():
    """Test a simple GraphQL query that doesn't require authentication"""
    # Simple query to get schema information
    query = '''
    query {
      __schema {
        queryType {
          name
        }
      }
    }
    '''
    
    headers = {
        'Content-Type': 'application/json'
    }
    
    logger.info("Testing simple GraphQL query...")
    logger.info(f"Query: {query}")
    
    # Send the request
    async with aiohttp.ClientSession() as session:
        async with session.post(
            'http://localhost:8000/graphql',
            headers=headers,
            json={'query': query}
        ) as response:
            data = await response.text()
            logger.info(f'Status: {response.status}')
            logger.info(f'Response: {data}')
            
            if response.status == 200:
                try:
                    json_data = json.loads(data)
                    logger.info(f"GraphQL schema response: {json.dumps(json_data, indent=2)}")
                except json.JSONDecodeError:
                    logger.error("Failed to parse JSON response")
            else:
                logger.error(f"Request failed with status: {response.status}")

if __name__ == "__main__":
    asyncio.run(test_simple_query()) 