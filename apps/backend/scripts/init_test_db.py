#!/usr/bin/env python3
"""
Enhanced test database initialization script that integrates with existing container manager.
Provides idempotent database setup with direct SQL checks and migration optimization.
"""

import os
import asyncio
import hashlib
import subprocess
import sys
from pathlib import Path
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.exc import ProgrammingError, OperationalError

# Use consistent paths with existing container manager
STATE_DIR = Path("/tmp")
MIGRATION_CHECKSUM_FILE = STATE_DIR / "a2a-migration-checksum"

async def get_migration_checksum():
    """Calculate checksum of migration files (compatible with existing container manager)"""
    migrations_dir = Path("/app/alembic/versions")
    if not migrations_dir.exists():
        return None
    
    # Get all migration files and sort them (same logic as container manager)
    migration_files = sorted(f.name for f in migrations_dir.glob("*.py"))
    if not migration_files:
        return None
    
    # Calculate checksum using same method as container manager
    hasher = hashlib.md5()
    for filename in migration_files:
        file_path = migrations_dir / filename
        hasher.update(file_path.read_bytes())
    
    return hasher.hexdigest()

async def check_database_exists(db_url, db_name):
    """Check if database exists using direct SQL query with retry logic"""
    # Create a connection URL to the postgres database
    admin_url = db_url.rsplit("/", 1)[0] + "/postgres"
    
    print(f"🔍 Checking if database '{db_name}' exists...")
    engine = create_async_engine(admin_url, isolation_level="AUTOCOMMIT")
    
    # Retry logic for database connection
    max_retries = 10
    retry_delay = 2
    
    try:
        for attempt in range(max_retries):
            try:
                async with engine.connect() as conn:
                    result = await conn.execute(
                        text("SELECT 1 FROM pg_database WHERE datname = :db_name"),
                        {"db_name": db_name}
                    )
                    exists = result.scalar() is not None
                    
                    if exists:
                        print(f"✅ Database '{db_name}' already exists")
                    else:
                        print(f"❌ Database '{db_name}' does not exist")
                    
                    return exists
                    
            except (OperationalError, OSError) as e:
                if attempt < max_retries - 1:
                    print(f"🔄 Connection attempt {attempt + 1}/{max_retries} failed, retrying in {retry_delay}s...")
                    await asyncio.sleep(retry_delay)
                    continue
                else:
                    print(f"❌ Failed to connect to database after {max_retries} attempts: {e}")
                    raise
    finally:
        await engine.dispose()

async def create_database(db_url, db_name):
    """Create database if it doesn't exist with retry logic"""
    admin_url = db_url.rsplit("/", 1)[0] + "/postgres"
    engine = create_async_engine(admin_url, isolation_level="AUTOCOMMIT")
    
    # Retry logic for database connection
    max_retries = 5
    retry_delay = 2
    
    try:
        for attempt in range(max_retries):
            try:
                async with engine.connect() as conn:
                    print(f"🔨 Creating database '{db_name}'...")
                    await conn.execute(text(f'CREATE DATABASE "{db_name}"'))
                    print(f"✅ Database '{db_name}' created successfully")
                    return True
            except (OperationalError, OSError) as e:
                if attempt < max_retries - 1:
                    print(f"🔄 Create database attempt {attempt + 1}/{max_retries} failed, retrying in {retry_delay}s...")
                    await asyncio.sleep(retry_delay)
                    continue
                else:
                    print(f"❌ Failed to create database after {max_retries} attempts: {e}")
                    return False
            except Exception as e:
                print(f"❌ Error creating database: {e}")
                return False
        return False
    finally:
        await engine.dispose()

async def check_migrations_needed():
    """Check if migrations need to be run based on checksum"""
    current_checksum = await get_migration_checksum()
    if not current_checksum:
        print("ℹ️  No migrations found")
        return False, None
    
    if MIGRATION_CHECKSUM_FILE.exists():
        stored_checksum = MIGRATION_CHECKSUM_FILE.read_text().strip()
        if current_checksum == stored_checksum:
            print("✅ Migrations are up-to-date")
            return False, current_checksum
    
    print("🔄 Migration changes detected")
    return True, current_checksum

async def run_migrations():
    """Run Alembic migrations"""
    print("🚀 Running database migrations...")
    
    result = subprocess.run(
        ["alembic", "upgrade", "head"],
        capture_output=True,
        text=True,
        cwd="/app"
    )
    
    if result.returncode == 0:
        print("✅ Migrations completed successfully")
        return True
    else:
        print(f"❌ Migration failed: {result.stderr}")
        if result.stdout:
            print(f"Stdout: {result.stdout}")
        return False

def update_migration_checksum(checksum):
    """Update the migration checksum file"""
    MIGRATION_CHECKSUM_FILE.write_text(checksum)
    print("📝 Migration checksum updated")

async def main():
    """Main function to initialize test database"""
    print("🏗️  A2A Platform - Enhanced Test Database Initialization")
    print("=" * 60)
    
    # Parse command line arguments
    force_setup = "--force" in sys.argv
    skip_migrations = "--skip-migrations" in sys.argv
    
    if force_setup:
        print("🔄 Force setup requested")
        # Clear checksum to force migration run
        if MIGRATION_CHECKSUM_FILE.exists():
            MIGRATION_CHECKSUM_FILE.unlink()
            print("🗑️  Cleared migration checksum")
    
    # Get database configuration
    db_url = os.getenv("DATABASE_URL", "postgresql+asyncpg://postgres:postgres@db:5432/a2a_platform_test")
    db_name = db_url.split("/")[-1]
    
    print(f"🎯 Target database: {db_name}")
    print(f"🔗 Connection URL: {db_url.replace('postgres:postgres@', 'postgres:***@')}")
    
    # Check if database exists
    db_exists = await check_database_exists(db_url, db_name)
    
    # Create database if needed
    if not db_exists:
        if not await create_database(db_url, db_name):
            print("❌ Failed to create database")
            sys.exit(1)
        # Force migrations after creating new database
        force_setup = True
    
    # Handle migrations
    if skip_migrations:
        print("⏭️  Skipping migrations as requested")
    else:
        migrations_needed, current_checksum = await check_migrations_needed()
        
        if migrations_needed or force_setup:
            if await run_migrations():
                if current_checksum:
                    update_migration_checksum(current_checksum)
            else:
                print("❌ Database initialization failed")
                sys.exit(1)
    
    print("=" * 60)
    print("🎉 Database initialization complete!")

if __name__ == "__main__":
    asyncio.run(main())
