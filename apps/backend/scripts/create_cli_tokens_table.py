import asyncio
import logging
import os
import sys

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set required environment variables
os.environ["DATABASE_URL"] = os.environ.get("DATABASE_URL") or "postgresql://postgres:postgres@localhost:5432/a2a_platform_test"
os.environ["REDIS_URL"] = os.environ.get("REDIS_URL") or "redis://localhost:6379/0"
os.environ["CLERK_API_KEY"] = os.environ.get("CLERK_API_KEY") or "test_key"
os.environ["CLERK_JWT_PUBLIC_KEY"] = os.environ.get("CLERK_JWT_PUBLIC_KEY") or "test_key"
os.environ["CLERK_WEBHOOK_SECRET"] = os.environ.get("CLERK_WEBHOOK_SECRET") or "test_secret"
os.environ["STORAGE_BUCKET"] = os.environ.get("STORAGE_BUCKET") or "local-dev-bucket"
os.environ["CDN_URL"] = os.environ.get("CDN_URL") or "https://localhost:8000"
os.environ["PUBSUB_PROJECT_ID"] = os.environ.get("PUBSUB_PROJECT_ID") or "dev-project-id"

# Add parent directory to path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "src")))

# Import necessary SQLAlchemy components
from sqlalchemy import Column, DateTime, ForeignKey, String, create_engine, text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker
from sqlalchemy.sql import func
import uuid

# Create a Base class for models
Base = declarative_base()

# Define the CliToken model
class CliToken(Base):
    __tablename__ = "cli_tokens"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    token_prefix = Column(String, nullable=False)
    hashed_token = Column(String, nullable=False)
    salt_hex = Column(String, nullable=False)
    description = Column(String, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    last_used_at = Column(DateTime(timezone=True), nullable=True)
    expires_at = Column(DateTime(timezone=True), nullable=True)


async def create_table():
    """Create the cli_tokens table if it doesn't exist."""
    # Get database URL from environment
    database_url = os.environ.get("DATABASE_URL")
    logger.info(f"Using database URL: {database_url}")

    # Convert to asyncpg format if needed
    if database_url.startswith("postgresql://"):
        async_database_url = database_url.replace("postgresql://", "postgresql+asyncpg://")
    else:
        async_database_url = database_url

    # Create engine
    engine = create_async_engine(async_database_url)

    try:
        # Create the table
        async with engine.begin() as conn:
            # Check if the table exists first
            result = await conn.execute(text(
                "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'cli_tokens')"
            ))
            row = await result.fetchone()
            table_exists = row[0]

            if table_exists:
                logger.info("Table 'cli_tokens' already exists, skipping creation")
            else:
                logger.info("Creating 'cli_tokens' table...")
                await conn.run_sync(Base.metadata.create_all, tables=[CliToken.__table__])
                logger.info("Table created successfully")

    except Exception as e:
        logger.error(f"Error creating table: {e}")
        raise
    finally:
        await engine.dispose()


if __name__ == "__main__":
    logger.info("Starting table creation script")
    asyncio.run(create_table())
