import asyncio
import logging
import sys
import os

# Adjust the Python path to include the src directory
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "src")))

from a2a_platform.db import AsyncSessionFactory
from a2a_platform.db.models.user import User
from a2a_platform.services.user_service import get_user_by_clerk_id

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def create_test_user():
    # Define a test clerk_user_id
    clerk_user_id = 'user_test_12345'  # This is a placeholder ID
    test_email = '<EMAIL>'

    try:
        # Get a database session using proper async context manager
        async with AsyncSessionFactory() as session:
            logger.info(f"Connected to database, session type: {type(session)}")

            # Check if user exists
            logger.info(f"Checking if user with clerk_user_id {clerk_user_id} exists")
            user = await get_user_by_clerk_id(session, clerk_user_id)

            if not user:
                logger.info(f'Creating new user with clerk_user_id: {clerk_user_id}')
                # Create a new user
                new_user = User(
                    clerk_user_id=clerk_user_id,
                    email=test_email,
                    preferences={}
                )
                session.add(new_user)
                await session.commit()
                logger.info(f'User created successfully with ID: {new_user.id}')
            else:
                logger.info(f'User already exists with ID: {user.id}')

            # Return success
            return True
    except Exception as e:
        logger.error(f"Error connecting to database: {str(e)}")
        return False

if __name__ == "__main__":
    asyncio.run(create_test_user())
