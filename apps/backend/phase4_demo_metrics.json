{"summary": {"total_tests": 2, "total_duration": 1.0020694732666016, "average_duration": 0.5010347366333008, "median_duration": 1.0017740726470947, "max_duration": 1.0017740726470947, "min_duration": 0.00029540061950683594, "slow_tests_count": 0, "slow_tests": [], "status_breakdown": {"passed": 2, "failed": 0, "skipped": 0}}, "metrics": [{"test_name": "tests/performance/test_bulk_operations_demo.py::TestPerformanceMonitoring::test_slow_operation_detection", "duration": 1.0017740726470947, "status": "passed", "db_connections_used": 0, "memory_peak_mb": 141.828125, "timestamp": 1749172419.9681997}, {"test_name": "tests/performance/test_bulk_operations_demo.py::TestPerformanceMonitoring::test_fast_operation_baseline", "duration": 0.00029540061950683594, "status": "passed", "db_connections_used": 0, "memory_peak_mb": 141.828125, "timestamp": 1749172419.9748368}]}