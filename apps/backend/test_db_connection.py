import asyncio
import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = str(Path(__file__).parent.absolute())
sys.path.insert(0, project_root)

from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

# Database URL - using the Docker service name as the host
DATABASE_URL = "postgresql+asyncpg://postgres:postgres@db:5432/a2a_platform"


async def test_connection():
    # Create async engine
    engine = create_async_engine(DATABASE_URL, echo=True)

    # Create a session
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

    try:
        # Test the connection
        async with async_session() as session:
            print("Testing database connection...")
            result = await session.execute(text("SELECT version()"))
            version = result.scalar()
            print(f"Database version: {version}")

            # Try to list tables (should be empty initially)
            result = await session.execute(
                text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                """)
            )
            tables = result.scalars().all()
            print(f"\nTables in the database: {tables}")

            return True

    except Exception as e:
        print(f"Error connecting to the database: {e}")
        return False
    finally:
        await engine.dispose()


if __name__ == "__main__":
    asyncio.run(test_connection())
