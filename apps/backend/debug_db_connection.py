#!/usr/bin/env python3
"""
Debug script to find where database connections are happening.
"""
import os
import sys
from unittest.mock import patch, MagicMock

# Set environment variables before any imports
os.environ["TEST_DATABASE_MODE"] = "no_db"
os.environ["REDIS_URL"] = "redis://localhost:6379"

# Mock database connections at the very beginning
original_create_engine = None
original_create_async_engine = None

def mock_create_engine(*args, **kwargs):
    print(f"🚨 SYNC ENGINE CREATION ATTEMPTED: {args}, {kwargs}")
    import traceback
    traceback.print_stack()
    return MagicMock()

def mock_create_async_engine(*args, **kwargs):
    print(f"🚨 ASYNC ENGINE CREATION ATTEMPTED: {args}, {kwargs}")
    import traceback
    traceback.print_stack()
    return MagicMock()

# Patch before any SQLAlchemy imports
with patch('sqlalchemy.create_engine', side_effect=mock_create_engine), \
     patch('sqlalchemy.ext.asyncio.create_async_engine', side_effect=mock_create_async_engine):
    
    print("Starting import test...")
    
    try:
        print("1. Importing a2a_platform.db...")
        import a2a_platform.db
        print("✅ a2a_platform.db imported successfully")
    except Exception as e:
        print(f"❌ Failed to import a2a_platform.db: {e}")
    
    try:
        print("2. Importing a2a_platform.db.session...")
        import a2a_platform.db.session
        print("✅ a2a_platform.db.session imported successfully")
    except Exception as e:
        print(f"❌ Failed to import a2a_platform.db.session: {e}")
    
    try:
        print("3. Importing test module...")
        from tests.unit.api.rest.routes.test_specialized_agent_api import TestSpecializedAgentAPI
        print("✅ Test module imported successfully")
    except Exception as e:
        print(f"❌ Failed to import test module: {e}")
        import traceback
        traceback.print_exc()

print("Import test completed.")
