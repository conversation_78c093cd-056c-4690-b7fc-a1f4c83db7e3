"""
Standalone tests for ConditionalTrustedHost middleware.

These tests don't depend on any database fixtures or complex setup.
"""

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient

from a2a_platform.middleware.conditional_trusted_host import (
    ConditionalTrustedHostMiddleware,
)


# Mark all tests in this module to not use database fixtures
pytestmark = pytest.mark.no_db


def test_health_check_routes_bypass_host_validation():
    """Test that health check routes work without valid host headers."""
    app = FastAPI()

    app.add_middleware(
        ConditionalTrustedHostMiddleware,
        allowed_hosts=["example.com"],
    )

    @app.get("/api/health")
    async def health():
        return {"status": "OK"}

    client = TestClient(app)

    # Test /api/health with invalid host - should work
    response = client.get("/api/health", headers={"host": "invalid.com"})
    assert response.status_code == 200
    assert response.json() == {"status": "OK"}


def test_protected_routes_require_valid_host():
    """Test that non-health routes require valid host headers."""
    app = FastAPI()

    app.add_middleware(
        ConditionalTrustedHostMiddleware,
        allowed_hosts=["example.com"],
    )

    @app.get("/api/protected")
    async def protected():
        return {"message": "protected endpoint"}

    client = TestClient(app)

    # Test with invalid host - should fail
    response = client.get("/api/protected", headers={"host": "invalid.com"})
    assert response.status_code == 400
    assert response.text == "Invalid host header"


def test_exact_host_match_allowed():
    """Test that exact host matches are allowed."""
    app = FastAPI()

    app.add_middleware(
        ConditionalTrustedHostMiddleware,
        allowed_hosts=["example.com"],
    )

    @app.get("/api/protected")
    async def protected():
        return {"message": "protected endpoint"}

    client = TestClient(app)

    # Test with valid host - should work
    response = client.get("/api/protected", headers={"host": "example.com"})
    assert response.status_code == 200
    assert response.json() == {"message": "protected endpoint"}


def test_wildcard_subdomain_match():
    """Test that wildcard subdomain matches work."""
    app = FastAPI()

    app.add_middleware(
        ConditionalTrustedHostMiddleware,
        allowed_hosts=["*.trusted.com"],
    )

    @app.get("/api/protected")
    async def protected():
        return {"message": "protected endpoint"}

    client = TestClient(app)

    # Test various subdomains - should work
    response = client.get("/api/protected", headers={"host": "api.trusted.com"})
    assert response.status_code == 200

    response = client.get("/api/protected", headers={"host": "staging.trusted.com"})
    assert response.status_code == 200

    # Test invalid domain - should fail
    response = client.get("/api/protected", headers={"host": "nottrusted.com"})
    assert response.status_code == 400


def test_cloud_run_startup_probe_scenario():
    """Test the specific Cloud Run startup probe scenario."""
    app = FastAPI()

    app.add_middleware(
        ConditionalTrustedHostMiddleware,
        allowed_hosts=["api-staging.vedavivi.app"],
    )

    @app.get("/api/health")
    async def health():
        return {"status": "OK"}

    client = TestClient(app)

    # Simulate Cloud Run startup probe with internal host
    response = client.get(
        "/api/health",
        headers={
            "host": "api-staging-59178042229.us-central1.run.app",
            "user-agent": "GoogleHC/1.0",
        },
    )
    # Should work even though host is not in allowed list
    assert response.status_code == 200
    assert response.json() == {"status": "OK"}


def test_allow_all_hosts_wildcard():
    """Test that * in allowed_hosts allows all hosts."""
    app = FastAPI()

    app.add_middleware(
        ConditionalTrustedHostMiddleware,
        allowed_hosts=["*"],
    )

    @app.get("/api/test")
    async def test_endpoint():
        return {"message": "test"}

    client = TestClient(app)

    # Any host should be allowed
    response = client.get("/api/test", headers={"host": "any.domain.com"})
    assert response.status_code == 200

    response = client.get("/api/test", headers={"host": "malicious.com"})
    assert response.status_code == 200


def test_custom_health_check_paths():
    """Test that custom health check paths work."""
    app = FastAPI()

    app.add_middleware(
        ConditionalTrustedHostMiddleware,
        allowed_hosts=["example.com"],
        health_check_paths=["/custom/health", "/readiness"],
    )

    @app.get("/custom/health")
    async def custom_health():
        return {"status": "OK"}

    @app.get("/readiness")
    async def readiness():
        return {"ready": True}

    @app.get("/api/protected")
    async def protected():
        return {"message": "protected"}

    client = TestClient(app)

    # Custom health paths should bypass validation
    response = client.get("/custom/health", headers={"host": "invalid.com"})
    assert response.status_code == 200

    response = client.get("/readiness", headers={"host": "invalid.com"})
    assert response.status_code == 200

    # Protected route should still require valid host
    response = client.get("/api/protected", headers={"host": "invalid.com"})
    assert response.status_code == 400


if __name__ == "__main__":
    # Run tests manually if needed
    test_health_check_routes_bypass_host_validation()
    test_protected_routes_require_valid_host()
    test_exact_host_match_allowed()
    test_wildcard_subdomain_match()
    test_cloud_run_startup_probe_scenario()
    test_allow_all_hosts_wildcard()
    test_custom_health_check_paths()
    print("All tests passed!")
