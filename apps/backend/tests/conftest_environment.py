"""
# Set environment variable to force database-free mode
export TEST_DATABASE_MODE=no_db
./scripts/run-backend-tests.sh

# Auto-detect mode (will use no_db if database unavailable)
export TEST_DATABASE_MODE=auto
./scripts/run-backend-tests.sh

# Force PostgreSQL mode (will fail if database unavailable)
export TEST_DATABASE_MODE=postgresql
./scripts/run-backend-tests.sh
"""

import os
import socket
from typing import Async<PERSON>enerator
from unittest.mock import AsyncMock

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.database.session import get_db_session


def is_database_available() -> bool:
    """
    Check if PostgreSQL database is available.

    Returns:
        bool: True if database is reachable, False otherwise
    """
    try:
        # Try to connect to the database host
        db_host = os.environ.get("DATABASE_HOST", "localhost")
        db_port = int(os.environ.get("DATABASE_PORT", "5432"))

        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)  # 1 second timeout
        result = sock.connect_ex((db_host, db_port))
        sock.close()

        return result == 0
    except Exception:
        return False


@pytest.fixture(scope="session")
def database_mode():
    """
    Determine the database mode based on environment and availability.

    Returns:
        str: 'no_db', 'fast_db', or 'postgresql'
    """
    # Check environment variable first
    mode = os.environ.get("TEST_DATABASE_MODE", "auto")

    if mode == "no_db":
        return "no_db"
    elif mode == "fast_db":
        return "fast_db"
    elif mode == "postgresql":
        return "postgresql"
    elif mode == "auto":
        # Auto-detect based on database availability
        if is_database_available():
            return "postgresql"
        else:
            return "no_db"
    else:
        raise ValueError(f"Invalid TEST_DATABASE_MODE: {mode}")


@pytest.fixture(scope="function")
async def adaptive_db_session(
    database_mode: str, request
) -> AsyncGenerator[AsyncSession, None]:
    """
    Adaptive database session that selects the appropriate backend
    based on environment and test markers.

    This fixture automatically chooses between:
    - Mock session for database-free tests
    - SQLite session for fast tests
    - PostgreSQL session for integration tests
    """
    # Check test markers
    test_markers = [mark.name for mark in request.node.iter_markers()]

    # Override database mode based on test markers
    if "no_db" in test_markers:
        effective_mode = "no_db"
    elif "fast_db" in test_markers:
        effective_mode = "fast_db"
    else:
        effective_mode = database_mode

    if effective_mode == "no_db":
        # Create mock session
        mock_session = AsyncMock(spec=AsyncSession)
        mock_session.add = AsyncMock()
        mock_session.commit = AsyncMock()
        mock_session.rollback = AsyncMock()
        mock_session.close = AsyncMock()
        mock_session.refresh = AsyncMock()
        mock_session.flush = AsyncMock()
        mock_session.execute = AsyncMock()
        mock_session.scalar = AsyncMock()
        mock_session.scalars = AsyncMock()

        yield mock_session

    elif effective_mode == "fast_db":
        # Use SQLite session (would need to import from conftest_sqlite.py)
        from .conftest_sqlite import sqlite_session

        async for session in sqlite_session:
            yield session

    else:  # postgresql
        # Use PostgreSQL session (would need to import from conftest.py)
        from .conftest import db_session_real

        async for session in db_session_real:
            yield session


@pytest.fixture(scope="function", autouse=True)
async def environment_based_override(adaptive_db_session):
    """
    Automatically override the database session based on environment.
    """
    from a2a_platform.api.main import app

    # Store original override
    original_override = app.dependency_overrides.get(get_db_session)

    # Set the adaptive session
    app.dependency_overrides[get_db_session] = lambda: adaptive_db_session

    yield

    # Restore original override
    if original_override:
        app.dependency_overrides[get_db_session] = original_override
    else:
        app.dependency_overrides.pop(get_db_session, None)
