"""
Performance tests for A2A context propagation.

These tests measure the performance impact of A2A context propagation
on the system under various loads.
"""

import asyncio
import time
from datetime import UTC, datetime
from typing import <PERSON>ple
from unittest.mock import AsyncMock, patch

import pytest
from fastapi.testclient import TestClient

from a2a_platform.main import app
from a2a_platform.schemas.a2a_context import UserContext

client = TestClient(app)

# Test configuration
NUM_REQUESTS = 100  # Number of requests for load testing
CONCURRENT_REQUESTS = 10  # Number of concurrent requests


class TestA2AContextPerformance:
    """Performance tests for A2A context handling."""

    @pytest.fixture
    def valid_user_context(self):
        """Create a valid user context for testing."""
        return UserContext(
            user_id="user123",
            initiating_agent_id="agent456",
            request_timestamp=datetime.now(UTC).isoformat(),
        )

    @pytest.fixture
    def valid_a2a_message(self, valid_user_context):
        """Create a valid A2A message for testing."""
        # Convert to dict to ensure proper serialization
        message_dict = {
            "user_context": valid_user_context.model_dump(),
            "payload": {"action": "test_action", "data": {"key": "value"}},
        }
        # Ensure timestamps are properly serialized
        if isinstance(message_dict["user_context"]["request_timestamp"], datetime):
            message_dict["user_context"]["request_timestamp"] = message_dict[
                "user_context"
            ]["request_timestamp"].isoformat()
        return message_dict

    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_context_propagation_latency(self, valid_a2a_message):
        """Test the latency impact of context propagation."""
        # Make a single request to measure baseline latency
        start_time = time.perf_counter()

        with patch("httpx.AsyncClient.post") as mock_post:
            # Setup mock response
            mock_response = AsyncMock()
            mock_response.json.return_value = {"status": "success"}
            mock_post.return_value = mock_response

            response = client.post(
                "/api/internal/specialized-agent/process",
                json=valid_a2a_message,  # Already a dict with proper serialization
                headers={
                    "Content-Type": "application/json",
                    "X-Forwarded-Proto": "https",
                },
            )

            end_time = time.perf_counter()
            latency_ms = (end_time - start_time) * 1000  # Convert to milliseconds

            # Verify the response
            assert response.status_code == 200

            # Log the latency for analysis
            print(f"\nSingle request latency: {latency_ms:.2f}ms")

    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_concurrent_requests_with_context(self, valid_a2a_message):
        """Test the system's ability to handle concurrent requests with context."""

        async def make_request(user_id: str, agent_id: str) -> float:
            """Helper function to make a single request and return timing."""
            # Create a copy of the message with the specified user and agent IDs
            message = valid_a2a_message.copy()
            message["user_context"]["user_id"] = user_id
            message["user_context"]["initiating_agent_id"] = agent_id

            start_time = time.perf_counter()
            client.post(
                "/api/internal/specialized-agent/process",
                json=message,
                headers={
                    "Content-Type": "application/json",
                    "X-Forwarded-Proto": "https",
                },
            )
            end_time = time.perf_counter()

            latency = (end_time - start_time) * 1000  # Convert to milliseconds
            return latency

        # Setup mock for the external service call
        with patch("httpx.AsyncClient.post") as mock_post:
            mock_response = AsyncMock()
            mock_response.json.return_value = {"status": "success"}
            mock_post.return_value = mock_response

            # Create tasks for concurrent requests
            tasks = []
            for i in range(CONCURRENT_REQUESTS):
                user_id = f"user_{i}"
                agent_id = f"agent_{i % 5}"  # Reuse some agent IDs
                tasks.append(make_request(user_id, agent_id))

            # Run requests concurrently
            start_time = time.perf_counter()
            results = await asyncio.gather(*tasks, return_exceptions=True)
            total_time = (time.perf_counter() - start_time) * 1000  # Convert to ms

            # Process results
            latencies = []
            for result in results:
                if isinstance(result, Exception):
                    pytest.fail(f"Request failed: {result}")
                latencies.append(result)

            # Calculate statistics
            avg_latency = sum(latencies) / len(latencies)
            max_latency = max(latencies)
            min_latency = min(latencies)

            # Log the results
            print(f"\nConcurrent requests ({CONCURRENT_REQUESTS}):")
            print(f"  Total time: {total_time:.2f}ms")
            print(f"  Average latency: {avg_latency:.2f}ms")
            print(f"  Min latency: {min_latency:.2f}ms")
            print(f"  Max latency: {max_latency:.2f}ms")

            # Assert that all requests were successful
            assert len(results) == CONCURRENT_REQUESTS
            assert max_latency < 1000, f"Maximum latency too high: {max_latency:.2f}ms"

    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_high_volume_requests(self, valid_a2a_message):
        """
        Test the system's ability to handle a high volume of requests with context.

        This test sends a large number of requests with different contexts
        and verifies that all are processed correctly.
        """

        async def make_request(request_id: int) -> Tuple[int, float]:
            """Helper function to make a single request and return timing."""
            user_id = f"user_{request_id % 100}"  # Reuse user IDs
            agent_id = f"agent_{request_id % 10}"  # Reuse agent IDs

            # Create a new message for each request
            message = {
                "user_context": {
                    "user_id": user_id,
                    "initiating_agent_id": agent_id,
                    "request_timestamp": datetime.now(UTC).isoformat(),
                },
                "payload": {
                    "action": "test_action",
                    "data": {"key": f"value_{request_id}"},
                },
            }

            start_time = time.perf_counter()
            client.post(
                "/api/internal/specialized-agent/process",
                json=message,
                headers={
                    "Content-Type": "application/json",
                    "X-Forwarded-Proto": "https",
                },
            )
            end_time = time.perf_counter()

            latency = (end_time - start_time) * 1000  # Convert to milliseconds
            return request_id, latency

        # Setup mock for the external service call
        with patch("httpx.AsyncClient.post") as mock_post:
            mock_response = AsyncMock()
            mock_response.json.return_value = {"status": "success"}
            mock_post.return_value = mock_post  # Return the mock for chaining
            mock_post.return_value = mock_response

            # Create semaphore to limit concurrency
            semaphore = asyncio.Semaphore(CONCURRENT_REQUESTS)

            async def bounded_make_request(request_id: int) -> Tuple[int, float]:
                """Make a request with concurrency control."""
                async with semaphore:
                    return await make_request(request_id)

            # Create tasks for all requests
            start_time = time.perf_counter()
            tasks = [bounded_make_request(i) for i in range(NUM_REQUESTS)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            total_time = (time.perf_counter() - start_time) * 1000  # Convert to ms

            # Process results
            latencies = []
            errors = 0
            for result in results:
                if isinstance(result, Exception):
                    errors += 1
                    continue
                request_id, latency = result
                latencies.append(latency)

            # Calculate statistics
            total_requests = len(results)
            successful_requests = len(latencies)
            error_rate = (errors / total_requests) * 100 if total_requests > 0 else 0

            if latencies:
                avg_latency = sum(latencies) / len(latencies)
                max_latency = max(latencies)
                min_latency = min(latencies)
                req_per_sec = (
                    (successful_requests / (total_time / 1000)) if total_time > 0 else 0
                )
            else:
                avg_latency = max_latency = min_latency = req_per_sec = 0

            print(f"\nTotal Requests: {total_requests}")
            print(f"Successful Requests: {successful_requests}")
            print(f"Failed Requests: {errors} ({error_rate:.2f}%)")
            print(f"Total time: {total_time / 1000:.2f}s")
            print(f"Average latency: {avg_latency:.2f}ms")
            print(f"Min latency: {min_latency:.2f}ms")
            print(f"Max latency: {max_latency:.2f}ms")
            print(f"Requests per second: {req_per_sec:.2f}")

            # Assert that the error rate is acceptable
            assert error_rate < 1.0, f"Error rate too high: {error_rate:.2f}%"

            # Assert that the average latency is reasonable (adjust threshold as needed)
            assert avg_latency < 100, f"Average latency too high: {avg_latency:.2f}ms"

            # Verify that all requests were processed
            assert successful_requests == NUM_REQUESTS, (
                f"Expected {NUM_REQUESTS} successful requests, got {successful_requests}"
            )
