"""
Performance test configuration.

This conftest.py enables performance monitoring for performance tests.
"""

import pytest
from unittest.mock import MagicMock

try:
    from tests.utils.pytest_performance_plugin import (
        performance_monitor,
        connection_tracker,
    )
except ImportError:
    performance_monitor = MagicMock()
    connection_tracker = MagicMock()


@pytest.fixture
def performance_metrics():
    """Provide access to the performance monitor for tests."""
    return performance_monitor


@pytest.fixture
def connection_metrics():
    """Provide access to the connection tracker for tests."""
    return connection_tracker
