"""
Performance Demonstration Tests

These tests demonstrate the performance improvements from Phase 4 optimizations:
- Bulk data creation
- SQLite mode for compatible tests
- Performance monitoring
"""

import asyncio  # Moved import to top
import pytest
import time

from a2a_platform.db.models.user import User


@pytest.mark.performance
@pytest.mark.bulk_data
class TestBulkOperationsPerformance:
    """Demonstrate bulk operations performance improvements."""

    @pytest.mark.asyncio
    async def test_bulk_user_creation_performance(self, bulk_data_factory):
        """Test that bulk user creation is significantly faster than individual creation."""
        start_time = time.time()

        # Create 50 users in bulk
        users = await bulk_data_factory.create_users_bulk(50)

        bulk_time = time.time() - start_time

        assert len(users) == 50
        assert all(isinstance(user, User) for user in users)
        assert bulk_time < 2.0  # Should complete in under 2 seconds

        # Log performance for comparison
        print(f"Bulk creation of 50 users: {bulk_time:.3f}s")

    @pytest.mark.asyncio
    async def test_complete_scenario_creation(self, bulk_data_factory):
        """Test creating a complete test scenario efficiently."""
        start_time = time.time()

        # Create a complete scenario with multiple users
        scenario = await bulk_data_factory.create_complete_test_scenario(
            user_count=10, messages_per_conversation=5
        )

        total_time = time.time() - start_time

        # Verify scenario completeness
        assert len(scenario["users"]) == 10
        assert len(scenario["assistants"]) == 10
        assert len(scenario["conversations"]) == 10
        assert len(scenario["messages"]) == 50  # 10 conversations * 5 messages each

        # Should complete quickly
        assert total_time < 5.0

        print(f"Complete scenario creation (10 users, 50 messages): {total_time:.3f}s")


@pytest.mark.fast_db
@pytest.mark.performance
class TestSQLitePerformance:
    """Demonstrate SQLite performance for compatible operations."""

    @pytest.mark.asyncio
    async def test_sqlite_user_operations(self, sqlite_mode, bulk_data_factory):
        """Test that user operations work correctly in SQLite mode."""
        # This test will automatically use SQLite for faster execution
        users = await bulk_data_factory.create_users_bulk(20)

        assert len(users) == 20
        assert all(user.id is not None for user in users)

    @pytest.mark.asyncio
    async def test_sqlite_assistant_operations(self, sqlite_mode, bulk_data_factory):
        """Test assistant operations in SQLite mode."""
        users = await bulk_data_factory.create_users_bulk(5)
        assistants = await bulk_data_factory.create_assistants_bulk(users)

        assert len(assistants) == 5
        assert all(
            assistant.user_id == users[i].id for i, assistant in enumerate(assistants)
        )


@pytest.mark.performance
class TestPerformanceMonitoring:
    """Demonstrate performance monitoring capabilities."""

    @pytest.mark.asyncio
    async def test_slow_operation_detection(self, performance_metrics):
        """Test that slow operations are properly detected and logged."""
        # Simulate a slow operation
        await self._simulate_slow_operation(1.0)  # 1 second delay

        # The performance plugin should automatically track this
        # In real usage, this would generate a warning about slow test execution

    @pytest.mark.asyncio
    async def test_fast_operation_baseline(self, performance_metrics):
        """Test a fast operation for baseline comparison."""
        # Quick operation
        result = sum(range(1000))
        assert result > 0

    async def _simulate_slow_operation(self, delay: float):
        """Simulate a slow database operation."""
        await asyncio.sleep(delay)
        return "slow_operation_complete"


@pytest.mark.integration
@pytest.mark.bulk_data
class TestRealWorldScenario:
    """Test real-world scenarios using bulk operations for efficiency."""

    @pytest.mark.asyncio
    async def test_multi_user_chat_scenario(self, multi_user_scenario):
        """Test a realistic multi-user chat scenario created efficiently."""
        scenario = multi_user_scenario

        # Verify the scenario is set up correctly
        assert len(scenario["users"]) >= 5
        assert len(scenario["assistants"]) >= 5
        assert len(scenario["conversations"]) >= 5
        assert len(scenario["messages"]) >= 10

        # Test that users have unique emails
        emails = [user.email for user in scenario["users"]]
        assert len(emails) == len(set(emails))  # All unique

        # Test that conversations belong to the correct users
        for i, conversation in enumerate(scenario["conversations"]):
            if i < len(scenario["users"]):
                assert conversation.user_id == scenario["users"][i].id
