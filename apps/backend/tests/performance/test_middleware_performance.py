"""
Performance tests for ConditionalTrustedHost middleware.

Tests middleware performance under various load conditions and with
different configuration sizes.
"""

import time
from concurrent.futures import ThreadPoolExecutor

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient

from a2a_platform.middleware.conditional_trusted_host import (
    ConditionalTrustedHostMiddleware,
)


# Mark all tests in this module to not use database fixtures
pytestmark = pytest.mark.no_db


class TestMiddlewarePerformance:
    """Test performance characteristics of the middleware."""

    def test_large_trusted_host_list_performance(self):
        """Test performance with large trusted host lists."""
        app = FastAPI()

        # Create a large list of trusted hosts (1000 hosts)
        large_host_list = []
        for i in range(500):
            large_host_list.append(f"api{i}.example.com")
            large_host_list.append(f"*.subdomain{i}.example.com")

        app.add_middleware(
            ConditionalTrustedHostMiddleware,
            allowed_hosts=large_host_list,
        )

        @app.get("/api/test")
        async def test_endpoint():
            return {"message": "test"}

        client = TestClient(app)

        # Measure time for exact match (should be fast - O(1))
        start_time = time.time()
        for _ in range(100):
            response = client.get("/api/test", headers={"host": "api0.example.com"})
            assert response.status_code == 200
        exact_match_time = time.time() - start_time

        # Measure time for wildcard match (should be reasonable - O(n))
        start_time = time.time()
        for _ in range(100):
            response = client.get(
                "/api/test", headers={"host": "test.subdomain0.example.com"}
            )
            assert response.status_code == 200
        wildcard_match_time = time.time() - start_time

        # Measure time for rejection (should be fast)
        start_time = time.time()
        for _ in range(100):
            response = client.get("/api/test", headers={"host": "malicious.com"})
            assert response.status_code == 400
        rejection_time = time.time() - start_time

        # Basic performance assertions
        # Exact matches should be very fast (under 1.5 seconds for 100 requests)
        # Increased threshold to account for CI environment variability
        assert exact_match_time < 1.5, f"Exact match too slow: {exact_match_time}s"

        # Wildcard matches should be reasonable (under 2 seconds for 100 requests)
        assert wildcard_match_time < 2.0, (
            f"Wildcard match too slow: {wildcard_match_time}s"
        )

        # Rejections should be fast (under 1.5 seconds for 100 requests)
        # Increased threshold to account for CI environment variability
        assert rejection_time < 1.5, f"Rejection too slow: {rejection_time}s"

        print(f"Performance results with {len(large_host_list)} trusted hosts:")
        print(f"  Exact match: {exact_match_time:.3f}s for 100 requests")
        print(f"  Wildcard match: {wildcard_match_time:.3f}s for 100 requests")
        print(f"  Rejection: {rejection_time:.3f}s for 100 requests")

    def test_concurrent_request_handling(self):
        """Test middleware under concurrent load."""
        app = FastAPI()

        app.add_middleware(
            ConditionalTrustedHostMiddleware,
            allowed_hosts=["trusted.com", "*.api.com"],
        )

        @app.get("/api/test")
        async def test_endpoint():
            return {"message": "test"}

        client = TestClient(app)

        def make_request(host: str) -> int:
            """Make a single request and return status code."""
            response = client.get("/api/test", headers={"host": host})
            return response.status_code

        # Test concurrent valid requests
        hosts_to_test = ["trusted.com"] * 50 + ["api.api.com"] * 50

        start_time = time.time()
        with ThreadPoolExecutor(max_workers=10) as executor:
            results = list(executor.map(make_request, hosts_to_test))
        concurrent_time = time.time() - start_time

        # All should be successful
        assert all(status == 200 for status in results), (
            "Some concurrent requests failed"
        )

        # Should complete in reasonable time (under 5 seconds for 100 concurrent requests)
        assert concurrent_time < 5.0, (
            f"Concurrent requests too slow: {concurrent_time}s"
        )

        print(
            f"Concurrent performance: {concurrent_time:.3f}s for 100 concurrent requests"
        )

    def test_wildcard_pattern_efficiency(self):
        """Compare performance of different wildcard configurations."""
        # Test with many exact hosts
        exact_hosts = [f"host{i}.example.com" for i in range(100)]
        app_exact = FastAPI()
        app_exact.add_middleware(
            ConditionalTrustedHostMiddleware,
            allowed_hosts=exact_hosts,
        )

        @app_exact.get("/api/test")
        async def test_exact():
            return {"message": "test"}

        client_exact = TestClient(app_exact)

        # Test with many wildcard hosts
        wildcard_hosts = [f"*.domain{i}.com" for i in range(100)]
        app_wildcard = FastAPI()
        app_wildcard.add_middleware(
            ConditionalTrustedHostMiddleware,
            allowed_hosts=wildcard_hosts,
        )

        @app_wildcard.get("/api/test")
        async def test_wildcard():
            return {"message": "test"}

        client_wildcard = TestClient(app_wildcard)

        # Measure exact host performance
        start_time = time.time()
        for _ in range(50):
            response = client_exact.get(
                "/api/test", headers={"host": "host0.example.com"}
            )
            assert response.status_code == 200
        exact_time = time.time() - start_time

        # Measure wildcard performance
        start_time = time.time()
        for _ in range(50):
            response = client_wildcard.get(
                "/api/test", headers={"host": "api.domain0.com"}
            )
            assert response.status_code == 200
        wildcard_time = time.time() - start_time

        print("Pattern efficiency comparison:")
        print(f"  100 exact hosts: {exact_time:.3f}s for 50 requests")
        print(f"  100 wildcard hosts: {wildcard_time:.3f}s for 50 requests")

        # Exact hosts should be faster than wildcards
        # Allow some tolerance since this depends on system performance
        assert exact_time < wildcard_time * 2, (
            "Exact matching should be significantly faster"
        )

    def test_health_check_bypass_performance(self):
        """Test that health check bypass doesn't add significant overhead."""
        app = FastAPI()

        # Large host list to stress-test
        large_host_list = [f"host{i}.example.com" for i in range(500)]
        app.add_middleware(
            ConditionalTrustedHostMiddleware,
            allowed_hosts=large_host_list,
        )

        @app.get("/api/health")
        async def health():
            return {"status": "OK"}

        @app.get("/api/test")
        async def test():
            return {"message": "test"}

        client = TestClient(app)

        # Measure health check performance (should bypass host validation)
        start_time = time.time()
        for _ in range(100):
            response = client.get("/api/health", headers={"host": "malicious.com"})
            assert response.status_code == 200
        health_time = time.time() - start_time

        # Measure regular endpoint performance with valid host
        start_time = time.time()
        for _ in range(100):
            response = client.get("/api/test", headers={"host": "host0.example.com"})
            assert response.status_code == 200
        regular_time = time.time() - start_time

        print("Health check bypass performance:")
        print(f"  Health endpoint (bypassed): {health_time:.3f}s for 100 requests")
        print(f"  Regular endpoint (validated): {regular_time:.3f}s for 100 requests")

        # Health checks should be very fast since they bypass validation
        # Increased threshold to account for CI environment variability
        assert health_time < 1.5, f"Health check bypass too slow: {health_time}s"

    def test_memory_usage_with_large_configs(self):
        """Test memory usage doesn't grow excessively with large configurations."""
        # This is a basic test - in a real scenario you might use memory profiling tools

        # Create middleware with very large configuration
        huge_host_list = []
        for i in range(1000):
            huge_host_list.append(f"exact{i}.example.com")
            huge_host_list.append(f"*.wildcard{i}.example.com")

        app = FastAPI()
        middleware = ConditionalTrustedHostMiddleware(
            app=app,
            allowed_hosts=huge_host_list,
        )

        # Verify the middleware was created successfully
        assert len(middleware.exact_hosts) == 1000
        assert len(middleware.wildcard_patterns) == 1000
        assert not middleware.allow_all

        # Basic functionality test with large config
        @app.get("/api/test")
        async def test():
            return {"message": "test"}

        app.add_middleware(
            ConditionalTrustedHostMiddleware, allowed_hosts=huge_host_list
        )
        client = TestClient(app)

        # Should still work correctly
        response = client.get("/api/test", headers={"host": "exact0.example.com"})
        assert response.status_code == 200

        response = client.get(
            "/api/test", headers={"host": "api.wildcard0.example.com"}
        )
        assert response.status_code == 200

        response = client.get("/api/test", headers={"host": "malicious.com"})
        assert response.status_code == 400


class TestPerformanceRegression:
    """Test for performance regressions between old and new implementations."""

    def test_regex_compilation_overhead(self):
        """Test that regex compilation doesn't add significant startup overhead."""
        wildcard_hosts = [f"*.domain{i}.com" for i in range(100)]

        # Measure middleware initialization time
        start_time = time.time()
        app = FastAPI()
        app.add_middleware(
            ConditionalTrustedHostMiddleware,
            allowed_hosts=wildcard_hosts,
        )
        init_time = time.time() - start_time

        # Should initialize quickly (under 1 second even with 100 wildcards)
        assert init_time < 1.0, f"Middleware initialization too slow: {init_time}s"

        print(f"Initialization time for 100 wildcard patterns: {init_time:.3f}s")

    def test_pattern_matching_efficiency(self):
        """Test that new pattern matching is more efficient than string operations."""
        # This test verifies that our regex-based approach is faster than
        # the old string-based approach for wildcard matching

        app = FastAPI()
        app.add_middleware(
            ConditionalTrustedHostMiddleware,
            allowed_hosts=["*.example.com", "*.test.org", "*.api.net"],
        )

        @app.get("/api/test")
        async def test():
            return {"message": "test"}

        client = TestClient(app)

        # Test many wildcard matches
        test_hosts = [
            "api.example.com",
            "staging.example.com",
            "www.test.org",
            "admin.api.net",
        ] * 25  # 100 total requests

        start_time = time.time()
        for host in test_hosts:
            response = client.get("/api/test", headers={"host": host})
            assert response.status_code == 200
        pattern_time = time.time() - start_time

        # Should complete quickly
        assert pattern_time < 2.0, f"Pattern matching too slow: {pattern_time}s"

        print(f"Pattern matching time for 100 wildcard requests: {pattern_time:.3f}s")
