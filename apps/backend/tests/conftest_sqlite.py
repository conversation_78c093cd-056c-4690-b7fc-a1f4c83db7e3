"""
SQLite-specific test fixtures for fast database testing.

This module provides SQLite-based fixtures that can be used as drop-in
replacements for PostgreSQL fixtures in tests that don't require
PostgreSQL-specific features.
"""

from typing import AsyncGenerator
from unittest.mock import AsyncMock, MagicMock

import pytest
import pytest_asyncio
from sqlalchemy import create_engine, event
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.pool import StaticPool

# Note: We don't import get_db_session here to avoid PostgreSQL dependency
# get_db_session will be imported only when needed in specific fixtures

# Import SQLAlchemy types for SQLite compatibility
from sqlalchemy import JSON
import uuid
import os


def is_postgresql_available():
    """Check if PostgreSQL is available for SQLite tests."""
    return os.environ.get("SQLITE_POSTGRESQL_AVAILABLE", "true").lower() == "true"


def uuid_to_str(uuid_obj):
    """Convert UUID object to string for SQLite compatibility."""
    if isinstance(uuid_obj, uuid.UUID):
        return str(uuid_obj)
    return uuid_obj


@pytest.fixture(scope="session")
def sqlite_engine():
    """Create a SQLite engine for testing."""
    # Use in-memory SQLite database
    engine = create_engine(
        "sqlite:///:memory:",
        poolclass=StaticPool,
        connect_args={
            "check_same_thread": False,
        },
        echo=False,
    )

    # Enable foreign key constraints in SQLite
    @event.listens_for(engine, "connect")
    def set_sqlite_pragma(dbapi_connection, connection_record):
        cursor = dbapi_connection.cursor()
        cursor.execute("PRAGMA foreign_keys=ON")
        # Verify foreign keys are enabled
        cursor.execute("PRAGMA foreign_keys")
        result = cursor.fetchone()
        print(f"SQLite foreign_keys pragma: {result}")
        cursor.close()

    return engine


@pytest.fixture(scope="session")
def sqlite_async_engine():
    """Create an async SQLite engine for testing."""
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        poolclass=StaticPool,
        connect_args={
            "check_same_thread": False,
        },
        echo=False,
    )

    # Enable foreign key constraints for async engine
    @event.listens_for(engine.sync_engine, "connect")
    def set_sqlite_pragma_async(dbapi_connection, connection_record):
        cursor = dbapi_connection.cursor()
        cursor.execute("PRAGMA foreign_keys=ON")
        # Verify foreign keys are enabled
        cursor.execute("PRAGMA foreign_keys")
        result = cursor.fetchone()
        print(f"SQLite async foreign_keys pragma: {result}")
        cursor.close()

    return engine


@pytest_asyncio.fixture(scope="function")
async def sqlite_tables(sqlite_async_engine):
    """Create all tables in the SQLite database with SQLite-compatible types."""

    def create_sqlite_compatible_tables(connection):
        """Create tables with SQLite-compatible column types."""
        # Import here to avoid circular imports
        from sqlalchemy import (
            Boolean,
            Column,
            DateTime,
            ForeignKey,
            Integer,
            String,
            Table,
            Text,
            text,
        )
        from sqlalchemy.sql import func

        # Create a new metadata object for SQLite-compatible tables
        from sqlalchemy import MetaData

        sqlite_metadata = MetaData()

        # Define SQLite-compatible User table
        Table(
            "users",
            sqlite_metadata,
            Column("id", String(36), primary_key=True),  # UUID as string
            Column("clerk_user_id", String(64), unique=True, nullable=False),
            Column("email", String(255), unique=True, nullable=False),
            Column("timezone", String(64), nullable=True, server_default=text("'UTC'")),
            Column("preferences", JSON, nullable=True, server_default=text("'{}'")),
            Column(
                "is_pa_setup_complete",
                Boolean,
                nullable=False,
                default=False,
                server_default=text("0"),
            ),
            Column(
                "created_at",
                DateTime(timezone=True),
                nullable=False,
                server_default=func.current_timestamp(),
            ),
            Column(
                "updated_at",
                DateTime(timezone=True),
                nullable=False,
                server_default=func.current_timestamp(),
            ),
        )

        # Define SQLite-compatible Assistant table
        assistants_table = Table(
            "assistants",
            sqlite_metadata,
            Column("id", String(36), primary_key=True),  # UUID as string
            Column(
                "user_id",
                String(36),
                ForeignKey("users.id", ondelete="CASCADE"),
                nullable=False,
                unique=True,
            ),
            Column("name", String(255), nullable=False),
            Column("backstory", Text, nullable=False),
            Column("avatar_file_id", String(36), nullable=True),  # UUID as string
            Column("configuration", JSON, nullable=True),
            Column(
                "created_at",
                DateTime(timezone=True),
                nullable=False,
                server_default=func.current_timestamp(),
            ),
            Column(
                "updated_at",
                DateTime(timezone=True),
                nullable=False,
                server_default=func.current_timestamp(),
            ),
        )

        # Define additional tables referenced by relationships
        Table(
            "conversations",
            sqlite_metadata,
            Column("id", String(36), primary_key=True),
            Column(
                "user_id",
                String(36),
                ForeignKey("users.id", ondelete="CASCADE"),
                nullable=False,
            ),
            Column(
                "assistant_id",
                String(36),
                ForeignKey("assistants.id", ondelete="CASCADE"),
                nullable=False,
            ),
            Column(
                "created_at",
                DateTime(timezone=True),
                nullable=False,
                server_default=func.current_timestamp(),
            ),
            Column("last_message_at", DateTime(timezone=True), nullable=True),
        )

        Table(
            "cli_tokens",
            sqlite_metadata,
            Column("id", String(36), primary_key=True),
            Column(
                "user_id",
                String(36),
                ForeignKey("users.id", ondelete="CASCADE"),
                nullable=False,
            ),
            Column("token_prefix", String(8), nullable=False),
            Column("hashed_token", String(64), nullable=False),
            Column("salt_hex", String(32), nullable=False),
            Column("description", String(255), nullable=True),
            Column(
                "created_at",
                DateTime(timezone=True),
                nullable=False,
                server_default=func.current_timestamp(),
            ),
            Column("last_used_at", DateTime(timezone=True), nullable=True),
            Column("expires_at", DateTime(timezone=True), nullable=True),
        )

        # Define assistant_objectives table
        Table(
            "assistant_objectives",
            sqlite_metadata,
            Column("id", String(36), primary_key=True),
            Column(
                "assistant_id",
                String(36),
                ForeignKey("assistants.id", ondelete="CASCADE"),
                nullable=False,
            ),
            Column("objective_text", Text, nullable=False),
            Column("status", String(50), nullable=False, default="active"),
            Column("metadata_json", JSON, nullable=True),
            Column(
                "created_at",
                DateTime(timezone=True),
                nullable=False,
                server_default=func.current_timestamp(),
            ),
            Column(
                "updated_at",
                DateTime(timezone=True),
                nullable=False,
                server_default=func.current_timestamp(),
            ),
            Column("completed_at", DateTime(timezone=True), nullable=True),
        )

        # Define tasks table with all required columns
        Table(
            "tasks",
            sqlite_metadata,
            Column("id", String(36), primary_key=True),
            Column(
                "assistant_id",
                String(36),
                ForeignKey("assistants.id", ondelete="CASCADE"),
                nullable=False,
            ),
            Column("description", Text, nullable=False),
            Column("status", String(50), nullable=False, default="todo"),
            Column(
                "parent_task_id",
                String(36),
                ForeignKey("tasks.id", ondelete="CASCADE"),
                nullable=True,
            ),
            Column(
                "objective_id",
                String(36),
                ForeignKey("assistant_objectives.id", ondelete="SET NULL"),
                nullable=True,
            ),
            Column(
                "depends_on_task_id",
                String(36),
                ForeignKey("tasks.id", ondelete="SET NULL"),
                nullable=True,
            ),
            Column("idempotency_key", String(255), nullable=True),
            Column("due_date", DateTime(timezone=True), nullable=True),
            Column("completed_at", DateTime(timezone=True), nullable=True),
            Column("retry_count", Integer, nullable=False, default=0),
            Column("last_progress_at", DateTime(timezone=True), nullable=True),
            Column("lease_owner_id", String(255), nullable=True),
            Column("lease_acquired_at", DateTime(timezone=True), nullable=True),
            Column("lease_expires_at", DateTime(timezone=True), nullable=True),
            Column("metadata_json", JSON, nullable=True, default=text("'{}'")),
            Column(
                "created_at",
                DateTime(timezone=True),
                nullable=False,
                server_default=func.current_timestamp(),
            ),
            Column(
                "updated_at",
                DateTime(timezone=True),
                nullable=False,
                server_default=func.current_timestamp(),
            ),
        )

        # Create indexes for the assistants table
        from sqlalchemy import Index

        Index("ix_assistants_user_id", assistants_table.c.user_id)

        # Create all tables
        sqlite_metadata.create_all(connection)

    async with sqlite_async_engine.begin() as conn:
        await conn.run_sync(create_sqlite_compatible_tables)
    yield

    def drop_all_tables(connection):
        from sqlalchemy import MetaData

        sqlite_metadata = MetaData()
        sqlite_metadata.reflect(bind=connection)
        sqlite_metadata.drop_all(connection)

    async with sqlite_async_engine.begin() as conn:
        await conn.run_sync(drop_all_tables)


@pytest_asyncio.fixture
async def sqlite_session(
    sqlite_async_engine, sqlite_tables
) -> AsyncGenerator[AsyncSession, None]:
    """
    Provide a SQLite database session for testing.

    This fixture can be used as a drop-in replacement for db_session_real
    in tests that don't require PostgreSQL-specific features.
    """
    async_session_maker = async_sessionmaker(
        sqlite_async_engine, class_=AsyncSession, expire_on_commit=False
    )

    async with async_session_maker() as session:
        try:
            yield session
        finally:
            await session.rollback()
            await session.close()


@pytest.fixture
def mock_db_session():
    """
    Provide a completely mocked database session for database-free tests.

    This fixture should be used for tests marked with @pytest.mark.no_db
    that test pure business logic without any database operations.
    """
    mock_session = AsyncMock(spec=AsyncSession)

    # Configure common mock behaviors
    mock_session.add = MagicMock()
    mock_session.commit = AsyncMock()
    mock_session.rollback = AsyncMock()
    mock_session.close = AsyncMock()
    mock_session.refresh = AsyncMock()
    mock_session.flush = AsyncMock()
    mock_session.execute = AsyncMock()
    mock_session.scalar = AsyncMock()
    mock_session.scalars = AsyncMock()

    return mock_session


# Alias for compatibility with main conftest
@pytest_asyncio.fixture
async def db_session_real(
    sqlite_session: AsyncSession,
) -> AsyncGenerator[AsyncSession, None]:
    """
    Alias for sqlite_session to maintain compatibility with main conftest.
    This allows tests to use db_session_real in SQLite mode.
    """
    yield sqlite_session


# Test fixtures for integration tests
@pytest_asyncio.fixture
async def test_user(db_session_real: AsyncSession):
    """Create a test user in the database for integration tests."""
    import uuid
    from a2a_platform.schemas.user import UserCreate
    from a2a_platform.services.user_service import create_user

    clerk_user_id = f"test_clerk_user_{uuid.uuid4()}"
    email = f"test_{uuid.uuid4()}@example.com"
    user_data = UserCreate(
        clerk_user_id=clerk_user_id,
        email=email,
        timezone="UTC",
    )
    user = await create_user(db_session_real, user_data)
    return user


@pytest_asyncio.fixture
async def test_assistant_id(test_user, db_session_real: AsyncSession):
    """Create a test assistant in the database and return its ID."""
    from a2a_platform.schemas.assistant_schemas import AssistantCreate
    from a2a_platform.services.assistant_service import AssistantService

    assistant_service = AssistantService(db_session=db_session_real)
    assistant_data = AssistantCreate(
        name="Test Assistant",
        backstory="A test assistant for integration testing",
        avatar_file_id=None,
        configuration={},
    )

    assistant = await assistant_service.create_personal_assistant(
        user_id=test_user.id, assistant_data=assistant_data
    )
    return assistant.id


@pytest_asyncio.fixture
async def test_objective(test_assistant_id, db_session_real: AsyncSession):
    """Create a test objective in the database."""
    from a2a_platform.schemas.objective_schemas import ObjectiveCreate
    from a2a_platform.services.objective_service import ObjectiveService

    objective_service = ObjectiveService(db_session=db_session_real)
    objective_data = ObjectiveCreate(
        assistant_id=test_assistant_id,
        objective_text="Test objective for integration testing",
        status="active",
        metadata={"test": True},
    )
    objective = await objective_service.add_assistant_objective(objective_data)
    return objective


@pytest_asyncio.fixture
async def task_service(db_session_real: AsyncSession):
    """Create a TaskService instance with a real database session."""
    from a2a_platform.services.task_service import TaskService

    return TaskService(session=db_session_real)


@pytest_asyncio.fixture
async def objective_service(db_session_real: AsyncSession):
    """Create an ObjectiveService instance with a real database session."""
    from a2a_platform.services.objective_service import ObjectiveService

    return ObjectiveService(db_session=db_session_real)


@pytest_asyncio.fixture
async def chat_processor_service(db_session_real: AsyncSession):
    """Create a ChatProcessorService instance with a real database session."""
    from a2a_platform.services.chat_processor_service import ChatProcessorService

    return ChatProcessorService(db_session=db_session_real)


@pytest_asyncio.fixture
async def test_client(db_session_real: AsyncSession):
    """
    Provide a test client for API testing with SQLite database.
    """
    from httpx import AsyncClient, ASGITransport

    try:
        from a2a_platform.main import app
        from a2a_platform.db import get_db_session

        # Override the database session dependency
        async def get_sqlite_session():
            yield db_session_real

        app.dependency_overrides[get_db_session] = get_sqlite_session

        async with AsyncClient(
            transport=ASGITransport(app=app, raise_app_exceptions=False),
            base_url="http://test",
        ) as client:
            yield client

        # Clean up
        app.dependency_overrides.clear()
    except ImportError:
        # If imports fail, provide a mock client
        from unittest.mock import AsyncMock

        yield AsyncMock()


@pytest.fixture
def override_db_session_for_sqlite(sqlite_session):
    """
    Override the database session dependency to use SQLite.

    This fixture should be used in tests that need to override
    the FastAPI dependency injection to use SQLite instead of PostgreSQL.

    Note: This fixture imports the app and get_db_session locally to avoid
    PostgreSQL dependency issues when the database is down.
    """
    if not is_postgresql_available():
        # PostgreSQL is not available, skip FastAPI dependency override
        yield
        return

    try:
        from a2a_platform.main import app
        from a2a_platform.db import get_db_session

        async def get_sqlite_session():
            async for session in sqlite_session:
                yield session

        app.dependency_overrides[get_db_session] = get_sqlite_session
        yield
        app.dependency_overrides.clear()
    except ImportError:
        # If imports fail (e.g., when PostgreSQL is down), skip this fixture
        yield


@pytest.fixture
def override_db_session_for_mock(mock_db_session):
    """
    Override the database session dependency to use a mock.

    This fixture should be used in tests marked with @pytest.mark.no_db
    that need to override FastAPI dependency injection.

    Note: This fixture imports the app and get_db_session locally to avoid
    PostgreSQL dependency issues when the database is down.
    """
    if not is_postgresql_available():
        # PostgreSQL is not available, skip FastAPI dependency override
        yield
        return

    try:
        from a2a_platform.main import app
        from a2a_platform.db import get_db_session

        async def get_mock_session():
            yield mock_db_session

        app.dependency_overrides[get_db_session] = get_mock_session
        yield
        app.dependency_overrides.clear()
    except ImportError:
        # If imports fail (e.g., when PostgreSQL is down), skip this fixture
        yield


# Alternative Solution 2: Separate fixture hierarchy for database-free tests
@pytest.fixture(scope="function", autouse=True)
async def no_db_session_override(request):
    """
    Alternative fixture that provides complete database isolation.
    Only activates for tests marked with @pytest.mark.no_db.

    Note: This fixture imports the app and get_db_session locally to avoid
    PostgreSQL dependency issues when the database is down.
    """
    from unittest.mock import AsyncMock

    # Check if this is a database-free test
    test_markers = [mark.name for mark in request.node.iter_markers()]

    if "no_db" not in test_markers:
        # Not a database-free test, do nothing
        yield
        return

    try:
        from a2a_platform.main import app
        from a2a_platform.db import get_db_session

        # Store original override
        original_override = app.dependency_overrides.get(get_db_session)

        # Create a completely isolated mock session
        mock_session = AsyncMock()
        mock_session.add = AsyncMock()
        mock_session.commit = AsyncMock()
        mock_session.rollback = AsyncMock()
        mock_session.close = AsyncMock()
        mock_session.refresh = AsyncMock()
        mock_session.flush = AsyncMock()
        mock_session.execute = AsyncMock()
        mock_session.scalar = AsyncMock()
        mock_session.scalars = AsyncMock()

        # Override the dependency
        app.dependency_overrides[get_db_session] = lambda: mock_session

        yield

        # Restore original override
        if original_override:
            app.dependency_overrides[get_db_session] = original_override
        else:
            app.dependency_overrides.pop(get_db_session, None)
    except ImportError:
        # If imports fail (e.g., when PostgreSQL is down), skip this fixture
        yield


# Performance monitoring fixtures
@pytest.fixture(autouse=True)
def performance_monitor(request):
    """
    Monitor test performance and categorize by database usage.

    This fixture automatically runs for all tests and helps identify
    performance bottlenecks.
    """
    import time

    start_time = time.time()

    # Determine test category
    test_markers = [mark.name for mark in request.node.iter_markers()]
    if "no_db" in test_markers:
        category = "database-free"
    elif "fast_db" in test_markers:
        category = "sqlite"
    else:
        category = "postgresql"

    yield

    end_time = time.time()
    duration = end_time - start_time

    # Log performance data (can be extended to write to file or metrics system)
    if duration > 1.0:  # Log slow tests
        print(
            f"\n⚠️  Slow test detected: {request.node.name} ({category}) took {duration:.2f}s"
        )


# Utility functions for test migration
def requires_postgresql_features(test_func):
    """
    Decorator to mark tests that require PostgreSQL-specific features.

    Use this decorator on tests that cannot be migrated to SQLite due to
    PostgreSQL-specific functionality (JSONB, arrays, custom types, etc.).
    """
    return pytest.mark.slow(test_func)


def database_free(test_func):
    """
    Decorator to mark tests as database-free.

    Use this decorator on tests that test pure business logic and don't
    require any database operations.
    """
    return pytest.mark.no_db(test_func)


def sqlite_compatible(test_func):
    """
    Decorator to mark tests as SQLite-compatible.

    Use this decorator on tests that can run against SQLite for faster
    execution while still testing database operations.
    """
    return pytest.mark.fast_db(test_func)
