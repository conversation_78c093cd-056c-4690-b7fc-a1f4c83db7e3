"""Integration tests for HTTPS security middleware.

This module contains integration tests for the HTTPS security middleware stack,
including HTTPS redirection, security headers, and WebSocket security.
"""

import pytest
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.testclient import Test<PERSON><PERSON>

from a2a_platform.middleware import (
    get_https_redirect_middleware,
    get_security_headers_middleware,
    get_websocket_security_middleware,
)


@pytest.fixture
def app():
    """Create a test FastAPI application with security middleware."""
    app = FastAPI()

    # Add security middleware in the correct order
    app.add_middleware(get_https_redirect_middleware())
    app.add_middleware(
        get_websocket_security_middleware(websocket_paths=["/ws", "/graphql"])
    )
    app.add_middleware(get_security_headers_middleware())

    # Add a test REST endpoint
    @app.get("/api/test")
    async def test_endpoint():
        return {"status": "ok"}

    # Add a test WebSocket endpoint
    @app.websocket("/ws")
    async def websocket_endpoint(websocket: WebSocket):
        await websocket.accept()
        try:
            while True:
                data = await websocket.receive_text()
                await websocket.send_text(f"Echo: {data}")
        except WebSocketDisconnect:
            pass

    # Add a GraphQL WebSocket endpoint for subscriptions
    @app.websocket("/graphql")
    async def graphql_ws_endpoint(websocket: WebSocket):
        await websocket.accept()
        try:
            while True:
                data = await websocket.receive_json()
                if data.get("type") == "connection_init":
                    await websocket.send_json({"type": "connection_ack"})
                elif data.get("type") == "subscribe":
                    # Echo back subscription data
                    await websocket.send_json(
                        {
                            "type": "next",
                            "id": data.get("id"),
                            "payload": {"data": {"echo": "Subscription data"}},
                        }
                    )
        except WebSocketDisconnect:
            pass

    return app


@pytest.fixture
def client(app):
    """Create a test client for the FastAPI app."""
    return TestClient(app)


def test_https_redirect():
    """Test that HTTP requests are redirected to HTTPS."""
    # Create a simple app with just the HTTPS redirect middleware
    app = FastAPI()
    app.add_middleware(get_https_redirect_middleware())

    @app.get("/test")
    async def test_endpoint():
        return {"status": "ok"}

    client = TestClient(app)

    # Test HTTP request with X-Forwarded-Proto header
    # Note: In test environments, HTTPS enforcement is bypassed
    # So we test that the request proceeds normally
    response = client.get(
        "/test?param=value",
        headers={"X-Forwarded-Proto": "http"},
    )

    # In test environment, request should proceed normally (not redirect)
    assert response.status_code == 200
    assert response.json() == {"status": "ok"}


def test_security_headers():
    """Test that security headers are properly added to responses."""
    # Create a simple app with just the security headers middleware
    app = FastAPI()
    app.add_middleware(get_security_headers_middleware())

    @app.get("/test")
    async def test_endpoint():
        return {"status": "ok"}

    client = TestClient(app)

    # Test with HTTPS
    response = client.get("/test", headers={"X-Forwarded-Proto": "https"})

    # Verify security headers
    assert response.status_code == 200
    assert "strict-transport-security" in response.headers
    assert (
        "max-age=31536000; includeSubDomains; preload"
        in response.headers["strict-transport-security"]
    )
    assert "content-security-policy" in response.headers
    assert "x-content-type-options" in response.headers
    assert "x-frame-options" in response.headers
    assert "referrer-policy" in response.headers
    assert "x-xss-protection" in response.headers


def test_websocket_security():
    """Test that WebSocket connections are properly secured."""
    # Create a simple app with just the WebSocket security middleware
    app = FastAPI()
    app.add_middleware(get_websocket_security_middleware(websocket_paths=["/ws"]))

    @app.websocket("/ws")
    async def websocket_endpoint(websocket: WebSocket):
        await websocket.accept()
        data = await websocket.receive_text()
        await websocket.send_text(f"Echo: {data}")

    client = TestClient(app)

    # Test WebSocket connection - in test environment this should work normally
    # since security middleware is bypassed in tests
    with client.websocket_connect("/ws") as websocket:
        websocket.send_text("test message")
        data = websocket.receive_text()
        assert data == "Echo: test message"


def test_all_middleware_together(client):
    """Test all middleware components working together."""
    # Test HTTP request (in test environment, no redirect happens)
    http_response = client.get("/api/test", headers={"X-Forwarded-Proto": "http"})
    assert http_response.status_code == 200
    assert http_response.json() == {"status": "ok"}

    # Test security headers on HTTPS request
    https_response = client.get("/api/test", headers={"X-Forwarded-Proto": "https"})
    assert https_response.status_code == 200
    assert "strict-transport-security" in https_response.headers
    assert "content-security-policy" in https_response.headers

    # Test WebSocket connection (works normally in test environment)
    with client.websocket_connect("/ws") as websocket:
        websocket.send_text("test")
        data = websocket.receive_text()
        assert data == "Echo: test"
