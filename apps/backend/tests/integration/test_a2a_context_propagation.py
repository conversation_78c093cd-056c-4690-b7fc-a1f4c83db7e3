"""
Integration tests for A2A context propagation.

These tests verify that user context is properly propagated
across service boundaries in A2A communication.
"""

import asyncio
import copy
from datetime import UTC, datetime, timedelta
from unittest.mock import AsyncMock, patch

import pytest
from fastapi.testclient import TestClient

from a2a_platform.main import app
from a2a_platform.schemas.a2a_context import UserContext

client = TestClient(app)


class TestA2AContextPropagation:
    """Integration tests for A2A context propagation."""

    @pytest.fixture
    def valid_user_context(self):
        """Create a valid user context for testing."""
        return UserContext(
            user_id="user123",
            initiating_agent_id="agent456",
            request_timestamp=datetime.now(UTC).isoformat(),
        )

    @pytest.fixture
    def valid_a2a_message(self, valid_user_context):
        """Create a valid A2A message for testing."""
        # Convert to dict to ensure proper serialization
        message_dict = {
            "user_context": valid_user_context.model_dump(),
            "payload": {"action": "test_action", "data": {"key": "value"}},
        }
        # Ensure timestamps are properly serialized
        if isinstance(message_dict["user_context"]["request_timestamp"], datetime):
            message_dict["user_context"]["request_timestamp"] = message_dict[
                "user_context"
            ]["request_timestamp"].isoformat()
        return message_dict

    @pytest.mark.asyncio
    async def test_context_propagation_through_http(self, valid_a2a_message):
        """
        Test that user context is properly propagated through HTTP requests.
        """
        # For this test, we'll just verify that the endpoint is reachable
        # and returns a successful response with the expected structure
        response = client.post(
            "/api/internal/specialized-agent/process",
            json=valid_a2a_message,
            headers={"Content-Type": "application/json", "X-Forwarded-Proto": "https"},
        )

        # Verify the response
        assert response.status_code == 200
        response_data = response.json()
        assert "status" in response_data
        assert "message" in response_data
        assert "data" in response_data

    @pytest.mark.asyncio
    async def test_context_validation_failure(self):
        """Test that invalid user context is properly rejected."""
        # Missing required fields
        invalid_message = {
            "payload": {"action": "test"},
            # Missing user_context
        }

        response = client.post(
            "/api/internal/specialized-agent/process",
            json=invalid_message,
            headers={"Content-Type": "application/json", "X-Forwarded-Proto": "https"},
        )

        assert response.status_code == 400
        assert "No user_context found in A2A message" in response.text

    @pytest.mark.asyncio
    async def test_context_tampering_detection(self, valid_a2a_message):
        """Test that tampering with context is handled."""
        # Tamper with the context
        tampered_message = valid_a2a_message.copy()
        tampered_message["user_context"]["user_id"] = "malicious_user"

        # Mock the external service call
        with patch("httpx.AsyncClient.post") as mock_post:
            mock_response = AsyncMock()
            mock_response.json.return_value = {"status": "success"}
            mock_post.return_value = mock_response

            # Make the request
            response = client.post(
                "/api/internal/specialized-agent/process",
                json=tampered_message,
                headers={
                    "Content-Type": "application/json",
                    "X-Forwarded-Proto": "https",
                },
            )

            # The endpoint should still return 200 with the mocked response
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["status"] == "success"
            assert "message" in response_data
            assert "data" in response_data

    @pytest.mark.asyncio
    async def test_concurrent_requests(self, valid_a2a_message):
        """Test that concurrent requests with different contexts don't interfere."""

        # Make multiple concurrent requests with different user contexts
        async def make_request(user_id: str, agent_id: str):
            message = valid_a2a_message.copy()
            message["user_context"]["user_id"] = user_id
            message["user_context"]["initiating_agent_id"] = agent_id

            # Mock the external service call
            with patch("httpx.AsyncClient.post") as mock_post:
                mock_response = AsyncMock()
                mock_response.json.return_value = {"status": "success"}
                mock_post.return_value = mock_response

                response = client.post(
                    "/api/internal/specialized-agent/process",
                    json=message,
                    headers={
                        "Content-Type": "application/json",
                        "X-Forwarded-Proto": "https",
                    },
                )
                return response

        # Create and run concurrent requests
        tasks = []
        for i in range(5):
            tasks.append(make_request(f"user_{i}", f"agent_{i}"))

        responses = await asyncio.gather(*tasks)

        # All requests should succeed
        for response in responses:
            assert response.status_code == 200

    @pytest.mark.asyncio
    async def test_context_expiration(self, valid_a2a_message):
        """Test that expired context is handled."""
        # Create an expired context by copying the valid message and updating the timestamp
        expired_context = copy.deepcopy(valid_a2a_message)
        expired_timestamp = datetime.now(UTC) - timedelta(hours=25)  # 25 hours old
        expired_context["user_context"]["request_timestamp"] = (
            expired_timestamp.isoformat()
        )

        # Make the request
        response = client.post(
            "/api/internal/specialized-agent/process",
            json=expired_context,
            headers={"Content-Type": "application/json", "X-Forwarded-Proto": "https"},
        )

        # The endpoint should still return 200
        assert response.status_code == 200
        response_data = response.json()
        assert "status" in response_data
        assert "message" in response_data
        assert "data" in response_data
