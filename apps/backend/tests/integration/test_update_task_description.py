"""
Implementation of missing test case TC_US5.5_021: Update Description.

This test verifies that a task's description can be updated.
"""

import uuid

import pytest
from httpx import AsyncClient
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.task import Task
from a2a_platform.schemas.task_schemas import TaskCreate, TaskUpdate


@pytest.mark.asyncio
async def test_update_task_description(
    test_client: AsyncClient,
    test_assistant_id: uuid.UUID,
    db_session_real: AsyncSession,
):
    """
    TC_US5.5_021: Test updating a task description.

    Setup: Create a task.
    Action: PUT /internal/tasks/{task_id} with a new description.
    Expected: 200 OK. Task description and updated_at updated.
    """
    # Arrange - Create a task to update
    task_data = TaskCreate(description="Initial task description").model_dump()
    response = await test_client.post(
        "/api/internal/assistants/my/tasks",
        json=task_data,
        headers={"X-Assistant-ID": str(test_assistant_id)},
    )

    assert response.status_code == 201
    task = response.json()
    task_id = task["id"]
    initial_updated_at = task["updated_at"]

    # Act - Update the task description
    new_description = "Updated task description"
    update_data = TaskUpdate(description=new_description).model_dump()

    update_response = await test_client.put(
        f"/api/internal/tasks/{task_id}", json=update_data
    )

    # Assert
    assert update_response.status_code == 200
    updated_task = update_response.json()
    assert updated_task["description"] == new_description
    assert (
        updated_task["updated_at"] != initial_updated_at
    )  # updated_at should be changed

    # Verify in database
    query = select(Task).where(Task.id == uuid.UUID(task_id))
    result = await db_session_real.execute(query)
    db_task = result.scalar_one_or_none()

    assert db_task is not None
    assert db_task.description == new_description
