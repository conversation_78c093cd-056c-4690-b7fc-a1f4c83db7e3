import uuid
from unittest.mock import Async<PERSON>ock, MagicMock, patch

import pytest
from fastapi import Request
from fastapi.testclient import <PERSON><PERSON>lient
from graphql import GraphQLResolveInfo
from sqlalchemy.ext.asyncio import AsyncSession
from strawberry.types.execution import ExecutionContext

from a2a_platform.api.graphql.middleware.auth_middleware import (
    AuthMiddleware,
    GraphQLContext,
    get_graphql_context,
)
from a2a_platform.auth.clerk import ClerkAuthMiddleware
from a2a_platform.db.models.user import User
from a2a_platform.main import app


@pytest.fixture
def test_client():
    return TestClient(app)


@pytest.fixture
def mock_clerk_user_id():
    return f"clerk_user_{uuid.uuid4()}"


@pytest.fixture
def mock_user(mock_clerk_user_id):
    return User(
        id=uuid.uuid4(),
        clerk_user_id=mock_clerk_user_id,
        email="<EMAIL>",
        preferences={},
    )


@pytest.fixture
def mock_request():
    """Create a mock Request object for testing."""
    return MagicMock(spec=Request)


@pytest.mark.asyncio
async def test_get_graphql_context(mock_request, db_session_real: AsyncSession):
    """Test that get_graphql_context returns a GraphQLContext instance with db_session"""
    # Call the function with a database session and mock request
    context = await get_graphql_context(
        request=mock_request, db_session=db_session_real
    )

    # Assert the context is a GraphQLContext instance with clerk_user_id initialized to None
    # and db_session set to the provided session
    assert isinstance(context, GraphQLContext)
    assert context.clerk_user_id is None
    assert context.db_session is db_session_real


@pytest.mark.asyncio
async def test_get_graphql_context_no_auth(mock_request, db_session_real: AsyncSession):
    """Test that get_graphql_context returns a GraphQLContext with no clerk_user_id but with db_session"""
    # Call the function with mock request
    context = await get_graphql_context(
        request=mock_request, db_session=db_session_real
    )

    # Assert the context has no clerk_user_id but has db_session
    assert isinstance(context, GraphQLContext)
    assert context.clerk_user_id is None
    assert context.db_session is db_session_real


@pytest.mark.asyncio
async def test_auth_middleware_protected_field(mock_clerk_user_id, mock_user):
    """Test that AuthMiddleware correctly protects fields that require auth"""
    # Create middleware instance
    middleware = AuthMiddleware()

    # Create a mock execution context with GraphQLContext that has clerk_user_id set
    mock_context = GraphQLContext(clerk_user_id=mock_clerk_user_id)
    mock_execution_context = MagicMock(spec=ExecutionContext)
    mock_execution_context.context = mock_context
    middleware.execution_context = mock_execution_context

    # Create a mock info object for a protected field
    mock_info = MagicMock(spec=GraphQLResolveInfo)
    mock_info.field_name = "me"  # This is a protected field
    mock_info.context = mock_context

    # Create a mock resolver that returns a value
    mock_resolver = AsyncMock(return_value={"id": str(mock_user.id)})

    # Call the resolve method
    result = await middleware.resolve(mock_resolver, None, mock_info)

    # Assert the resolver was called and the result is returned
    mock_resolver.assert_called_once()
    assert result == {"id": str(mock_user.id)}
    # Assert the clerk_user_id is still set in the context
    assert mock_info.context.clerk_user_id == mock_clerk_user_id


@pytest.mark.asyncio
async def test_auth_middleware_protected_field_no_auth():
    """Test that AuthMiddleware raises an exception for protected fields without auth"""
    # Create middleware instance
    middleware = AuthMiddleware()

    # Create a mock execution context
    mock_execution_context = MagicMock(spec=ExecutionContext)
    mock_execution_context.context = GraphQLContext()
    mock_execution_context.context.request = MagicMock()
    mock_execution_context.context.request.headers = {}
    middleware.execution_context = mock_execution_context

    # Mock the clerk auth middleware to return None (no valid token)
    with patch.object(ClerkAuthMiddleware, "__call__", return_value=None):
        # Run the on_operation method
        on_op_gen = middleware.on_operation()
        await anext(on_op_gen)  # This will run until the first yield

        # Create a mock info object for a protected field
        mock_info = MagicMock(spec=GraphQLResolveInfo)
        mock_info.field_name = "me"  # This is a protected field
        mock_info.context = mock_execution_context.context

        # Create a mock resolver
        mock_resolver = AsyncMock()

        # Create a mock for the _operation_requires_auth method to ensure it returns True
        with patch.object(middleware, "_operation_requires_auth", return_value=True):
            # Call the resolve method and expect an exception
            with pytest.raises(Exception) as excinfo:
                await middleware.resolve(mock_resolver, None, mock_info)

            # Verify the exception message
            assert "Authentication required" in str(excinfo.value)

        # Assert the resolver was not called
        mock_resolver.assert_not_called()


@pytest.mark.asyncio
async def test_auth_middleware_unprotected_field():
    """Test that AuthMiddleware allows access to unprotected fields without auth"""
    # Create middleware instance
    middleware = AuthMiddleware()

    # Create a mock execution context
    mock_execution_context = MagicMock(spec=ExecutionContext)
    mock_execution_context.context = GraphQLContext()
    mock_execution_context.context.request = MagicMock()
    mock_execution_context.context.request.headers = {}
    middleware.execution_context = mock_execution_context

    # Mock the clerk auth middleware to return None (no valid token)
    with patch.object(ClerkAuthMiddleware, "__call__", return_value=None):
        # Run the on_operation method
        on_op_gen = middleware.on_operation()
        await anext(on_op_gen)  # This will run until the first yield

        # Create a mock info object for an unprotected field
        mock_info = MagicMock(spec=GraphQLResolveInfo)
        mock_info.field_name = "publicData"  # This is not a protected field
        mock_info.context = mock_execution_context.context

        # Create a mock resolver that returns a value
        mock_resolver = AsyncMock(return_value={"public": "data"})

        # Call the resolve method
        result = await middleware.resolve(mock_resolver, None, mock_info)

        # Assert the resolver was called and the result is returned
        mock_resolver.assert_called_once()
        assert result == {"public": "data"}
