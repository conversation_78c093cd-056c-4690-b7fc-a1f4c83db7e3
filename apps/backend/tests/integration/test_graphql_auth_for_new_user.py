from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch

import pytest
from fastapi.security import HTTPAuthorizationCredentials
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession


@pytest.mark.asyncio
async def test_graphql_auth_no_token(test_client: AsyncClient):
    """Test that a protected query fails when no token is provided"""
    query = """
    query {
        me {
            id
            clerkUserId
        }
    }
    """

    # Mock the clerk auth middleware to return None (no token)
    with patch(
        "a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__",
        new_callable=AsyncMock,
        return_value=None,
    ):
        # Send the query without a token
        response = await test_client.post(
            "/graphql",
            json={"query": query},
            headers={"Content-Type": "application/json"},
        )

    assert response.status_code == 200
    response_json = response.json()
    assert "errors" in response_json
    assert len(response_json["errors"]) > 0
    assert (
        response_json["errors"][0]["message"]
        == "Authentication required for this operation."
    )
    assert (
        response_json.get("data") is None
        or response_json.get("data", {}).get("me") is None
    )


@pytest.mark.asyncio
async def test_graphql_auth_invalid_token(test_client: AsyncClient):
    """Test that a protected query fails when an invalid token is provided"""
    query = """
    query {
        me {
            id
            clerkUserId
        }
    }
    """

    # Mock the clerk auth middleware to return None (invalid token)
    with patch(
        "a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__",
        new_callable=AsyncMock,
        return_value=None,
    ):
        # Send the query with an invalid token
        response = await test_client.post(
            "/graphql",
            json={"query": query},
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer invalid_token",
            },
        )

    assert response.status_code == 200
    response_json = response.json()
    assert "errors" in response_json
    assert len(response_json["errors"]) > 0
    assert (
        response_json["errors"][0]["message"]
        == "Authentication required for this operation."
    )
    assert (
        response_json.get("data") is None
        or response_json.get("data", {}).get("me") is None
    )


@pytest.fixture
async def test_auth_user(db_session_real: AsyncSession):
    """Create a test user for authentication tests"""
    import uuid

    clerk_user_id = f"clerk_user_{uuid.uuid4()}"
    email = f"test_{uuid.uuid4()}@example.com"

    # Create the user directly in the database using SQL to avoid event loop issues
    import uuid
    from datetime import UTC, datetime

    from a2a_platform.db.models.user import User

    # Create a user object
    user = User(
        id=uuid.uuid4(),
        clerk_user_id=clerk_user_id,
        email=email,
        timezone="UTC",
        preferences={},
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    # Add it to the session
    db_session_real.add(user)
    # Flush to get the ID without committing
    await db_session_real.flush()

    return user


@pytest.mark.asyncio
async def test_auth_middleware_directly():
    """Test the auth middleware directly without involving GraphQL resolvers"""
    import uuid

    from graphql import GraphQLResolveInfo
    from strawberry.types.execution import ExecutionContext

    from a2a_platform.api.graphql.middleware.auth_middleware import AuthMiddleware
    from a2a_platform.auth.clerk import ClerkAuthMiddleware

    # Create a mock user ID
    clerk_user_id = f"clerk_user_{uuid.uuid4()}"

    # Create a mock auth middleware instance
    auth_middleware = AuthMiddleware()

    # Create a proper mock request
    from fastapi import Request

    mock_request = MagicMock(spec=Request)
    mock_request.headers = {"Authorization": f"Bearer {clerk_user_id}"}

    # Create a mock context with the request
    from a2a_platform.api.graphql.middleware.auth_middleware import GraphQLContext

    mock_context = GraphQLContext()
    mock_context.request = mock_request

    # Set the execution context on the middleware
    mock_execution_context = MagicMock(spec=ExecutionContext)
    mock_execution_context.context = mock_context
    auth_middleware.execution_context = mock_execution_context

    # Mock the clerk auth middleware to return valid credentials
    mock_auth_credentials = HTTPAuthorizationCredentials(
        scheme="Bearer", credentials=clerk_user_id
    )

    # Patch the ClerkAuthMiddleware.__call__ method
    with patch.object(
        ClerkAuthMiddleware,
        "__call__",
        new_callable=AsyncMock,
        return_value=mock_auth_credentials,
    ):
        # Run the on_operation method
        on_op_gen = auth_middleware.on_operation()
        await anext(on_op_gen)

        # Verify that clerk_user_id was set on the context
        assert mock_context.clerk_user_id == clerk_user_id

        # Create a mock info object for a protected field
        mock_info = MagicMock(spec=GraphQLResolveInfo)
        mock_info.field_name = "me"  # This is a protected field
        mock_info.context = mock_context

        # Create a mock resolver that returns a value
        expected_result = {"id": "test-id"}
        mock_resolver = AsyncMock(return_value=expected_result)

        # Call the resolve method
        result = await auth_middleware.resolve(mock_resolver, None, mock_info)

        # Assert the resolver was called and the result is returned
        mock_resolver.assert_called_once()
        assert result == expected_result


# More tests can be added here, for example, to verify the actual data returned by the 'me' resolver
