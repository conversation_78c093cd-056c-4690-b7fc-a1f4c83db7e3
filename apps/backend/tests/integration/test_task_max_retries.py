"""
Implementation of missing test case TC_US5.5_031: Max Retries.

This test verifies that a task transitions to proper state when reaching max retries.
"""

import uuid

import pytest
from httpx import AsyncClient
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.task import Task
from a2a_platform.schemas.task_schemas import TaskCreate, TaskUpdate


@pytest.mark.asyncio
async def test_task_max_retries(
    test_client: AsyncClient,
    test_assistant_id: uuid.UUID,
    db_session_real: AsyncSession,
):
    """
    TC_US5.5_031: Test max retries handling.

    Setup: Create a task. Manually update retry_count to simulate max retries condition.
    Action: Update task status to failed_max_retries.
    Expected: 200 OK. Task status is 'failed_max_retries'.
    """
    # Arrange - Create a task
    metadata = {"max_retries": 3}  # Set max retries to 3
    task_data = TaskCreate(
        description="Task for max retries test", metadata=metadata
    ).model_dump()

    response = await test_client.post(
        "/api/internal/assistants/my/tasks",
        json=task_data,
        headers={"X-Assistant-ID": str(test_assistant_id)},
    )

    assert response.status_code == 201
    task = response.json()
    task_id = task["id"]

    # Manually update the retry count to simulate reaching max_retries
    # In a real system this would happen through worker logic
    query = update(Task).where(Task.id == uuid.UUID(task_id)).values(retry_count=3)
    await db_session_real.execute(query)
    await db_session_real.commit()

    # Verify retry count was updated
    query = select(Task).where(Task.id == uuid.UUID(task_id))
    result = await db_session_real.execute(query)
    db_task = result.scalar_one_or_none()
    assert db_task.retry_count == 3

    # Act - Update task status to failed_max_retries
    # In a real system, this would be done by a worker when max retries are reached
    update_data = TaskUpdate(status="failed_max_retries").model_dump()
    update_response = await test_client.put(
        f"/api/internal/tasks/{task_id}", json=update_data
    )

    # Assert
    assert update_response.status_code == 200
    updated_task = update_response.json()
    assert updated_task["status"] == "failed_max_retries"

    # Verify in database - refresh the session to see committed changes
    await db_session_real.rollback()  # Clear any pending transaction
    query = select(Task).where(Task.id == uuid.UUID(task_id))
    result = await db_session_real.execute(query)
    db_task = result.scalar_one_or_none()

    assert db_task is not None
    assert db_task.status == "failed_max_retries"
    assert db_task.retry_count == 3  # Retry count should still be at max
