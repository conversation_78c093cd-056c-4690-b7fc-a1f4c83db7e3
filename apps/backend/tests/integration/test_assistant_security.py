"""
Security and authorization tests for Personal Assistant functionality.

These tests verify authentication and authorization requirements
for PA creation and management as specified in US1.1 QA specifications.

Test IDs covered:
- API-TC-002: Authentication - Attempt PA creation without authentication
- API-TC-003: Authorization - Verify user can only create their own PA
- Additional security tests for PA management operations
"""

from unittest.mock import patch

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.user import User


class TestAssistantSecurity:
    """Security and authorization tests for Personal Assistant functionality."""

    @pytest.mark.asyncio
    async def test_create_pa_requires_authentication(self, test_client: AsyncClient):
        """
        API-TC-002: Authentication - Attempt PA creation without authentication.

        Verifies that the createPersonalAssistant mutation requires valid authentication.
        """
        # Arrange
        mutation = """
            mutation CreatePersonalAssistant($input: CreateAssistantInput!) {
                createPersonalAssistant(input: $input) {
                    success
                    message
                    assistant {
                        id
                        name
                    }
                }
            }
        """

        variables = {
            "input": {
                "name": "Unauthorized Assistant",
                "backstory": "This should fail due to lack of authentication",
            }
        }

        # Test 1: No Authorization header
        response = await test_client.post(
            "/graphql", json={"query": mutation, "variables": variables}
        )

        assert response.status_code == 200  # GraphQL returns 200 even for auth errors
        data = response.json()
        assert "errors" in data
        assert len(data["errors"]) > 0

        # Test 2: Invalid Authorization header
        response = await test_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers={"Authorization": "Bearer invalid_token"},
        )

        assert response.status_code == 200
        data = response.json()
        assert "errors" in data
        assert len(data["errors"]) > 0

        # Test 3: Malformed Authorization header
        response = await test_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers={"Authorization": "InvalidFormat"},
        )

        assert response.status_code == 200
        data = response.json()
        assert "errors" in data
        assert len(data["errors"]) > 0

    @pytest.mark.asyncio
    async def test_user_can_only_create_own_pa(
        self, test_client: AsyncClient, db_session_real: AsyncSession
    ):
        """
        API-TC-003: Authorization - Verify user can only create their own PA.

        Tests that the system correctly uses the authenticated user's ID
        and doesn't allow creating PAs for other users.
        """
        # Arrange - Create two test users
        import uuid

        from a2a_platform.schemas.user import UserCreate
        from a2a_platform.services.user_service import create_user

        # First user will be authenticated
        auth_user_data = UserCreate(
            clerk_user_id=f"test_auth_user_{uuid.uuid4()}",
            email=f"auth_user_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        auth_user = await create_user(db_session_real, auth_user_data)

        # Second user will be the attempted target
        target_user_data = UserCreate(
            clerk_user_id=f"test_target_user_{uuid.uuid4()}",
            email=f"target_user_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        target_user = await create_user(db_session_real, target_user_data)

        mutation = """
            mutation CreatePersonalAssistant($input: CreateAssistantInput!) {
                createPersonalAssistant(input: $input) {
                    success
                    message
                    assistant {
                        id
                        userId
                        name
                    }
                }
            }
        """

        variables = {
            "input": {
                "name": "Impersonation Test Assistant",
                "backstory": "Attempting to create for another user",
            }
        }

        # Mock authentication to return our first user's Clerk ID
        with patch("a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__") as mock_auth:
            from fastapi.security import HTTPAuthorizationCredentials

            mock_auth.return_value = HTTPAuthorizationCredentials(
                scheme="Bearer", credentials=auth_user.clerk_user_id
            )

            # Act - Create the personal assistant
            response = await test_client.post(
                "/graphql",
                json={"query": mutation, "variables": variables},
                headers={"Authorization": "Bearer fake_token"},
            )

        # Assert
        assert response.status_code == 200
        data = response.json()

        # Verify no GraphQL errors
        assert "errors" not in data or not data["errors"], (
            f"Got unexpected errors: {data.get('errors')}"
        )

        # Verify the PA was created for the authenticated user, NOT the target user
        payload = data["data"]["createPersonalAssistant"]
        assert payload["success"] is True

        assistant_data = payload["assistant"]
        assert assistant_data["userId"] == str(auth_user.id), (
            "Assistant should be created for authenticated user"
        )
        assert assistant_data["userId"] != str(target_user.id), (
            "Assistant should NOT be created for target user"
        )

        # Verify database state - auth_user should have an assistant, target_user should not
        from sqlalchemy import select

        from a2a_platform.db.models.assistant import Assistant

        # Check auth_user has assistant
        auth_result = await db_session_real.execute(
            select(Assistant).where(Assistant.user_id == auth_user.id)
        )
        auth_assistants = auth_result.scalars().all()
        assert len(auth_assistants) == 1, "Authenticated user should have an assistant"

        # Check target_user has no assistant
        target_result = await db_session_real.execute(
            select(Assistant).where(Assistant.user_id == target_user.id)
        )
        target_assistants = target_result.scalars().all()
        assert len(target_assistants) == 0, "Target user should NOT have an assistant"

    @pytest.mark.asyncio
    async def test_pa_operations_require_ownership(
        self, test_client: AsyncClient, db_session_real: AsyncSession
    ):
        """
        Test that PA management operations require ownership verification.

        Verifies that users can only access and modify their own assistants.
        """
        # Arrange - Create two test users, each with their own PA
        import uuid

        from a2a_platform.schemas.assistant_schemas import AssistantCreate
        from a2a_platform.schemas.user import UserCreate
        from a2a_platform.services.assistant_service import AssistantService
        from a2a_platform.services.user_service import create_user

        # Create users
        user1_data = UserCreate(
            clerk_user_id=f"test_owner_user_{uuid.uuid4()}",
            email=f"owner_user_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user1 = await create_user(db_session_real, user1_data)

        user2_data = UserCreate(
            clerk_user_id=f"test_other_user_{uuid.uuid4()}",
            email=f"other_user_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user2 = await create_user(db_session_real, user2_data)

        # Create PAs for both users
        assistant_service = AssistantService(db_session=db_session_real)

        pa1_data = AssistantCreate(
            name="User1's Assistant",
            backstory="This is user1's personal assistant",
        )
        await assistant_service.create_personal_assistant(user1.id, pa1_data)

        pa2_data = AssistantCreate(
            name="User2's Assistant",
            backstory="This is user2's personal assistant",
        )
        pa2 = await assistant_service.create_personal_assistant(user2.id, pa2_data)

        # Query to get assistant details
        query = """
            query GetAssistant {
                myAssistant {
                    id
                    name
                    userId
                }
            }
        """

        # Try to access user2's PA while authenticated as user1
        # Note: We don't need to pass an ID as the myAssistant query returns the current user's assistant

        with patch("a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__") as mock_auth:
            from fastapi.security import HTTPAuthorizationCredentials

            mock_auth.return_value = HTTPAuthorizationCredentials(
                scheme="Bearer", credentials=user1.clerk_user_id
            )

            response = await test_client.post(
                "/graphql",
                json={"query": query},
                headers={"Authorization": "Bearer fake_token"},
            )

        # Assert - Should either get an error or null data for unauthorized access
        assert response.status_code == 200
        data = response.json()

        # We should get back user1's assistant, not user2's
        assert "data" in data, "Expected data in response"
        assert "myAssistant" in data["data"], "Expected myAssistant in data"
        assistant_data = data["data"]["myAssistant"]

        # The returned assistant should be user1's, not user2's
        assert assistant_data["userId"] == str(user1.id), (
            "Should get the authenticated user's assistant"
        )
        assert assistant_data["userId"] != str(user2.id), (
            "Should not get another user's assistant"
        )

        # Now authenticate as user2 and verify they get their own assistant
        with patch("a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__") as mock_auth:
            mock_auth.return_value = HTTPAuthorizationCredentials(
                scheme="Bearer", credentials=user2.clerk_user_id
            )

            response = await test_client.post(
                "/graphql",
                json={"query": query},
                headers={"Authorization": "Bearer fake_token"},
            )

        # Assert - Should be able to access own PA
        assert response.status_code == 200
        data = response.json()

        assert "errors" not in data or not data["errors"], (
            "Accessing own PA should not produce errors"
        )
        assert data["data"]["myAssistant"] is not None, (
            "Assistant data should be returned"
        )
        assert data["data"]["myAssistant"]["userId"] == str(user2.id), (
            "User2 should get their own assistant"
        )
        assert data["data"]["myAssistant"]["id"] == str(pa2.id), (
            "Correct assistant should be returned"
        )

    @pytest.mark.asyncio
    async def test_invalid_clerk_token_handling(self, test_client: AsyncClient):
        """
        Test handling of various invalid Clerk token scenarios.

        Verifies robust error handling for authentication edge cases.
        """
        mutation = """
            mutation CreatePersonalAssistant($input: CreateAssistantInput!) {
                createPersonalAssistant(input: $input) {
                    success
                    message
                    assistant {
                        id
                        name
                    }
                }
            }
        """

        variables = {
            "input": {
                "name": "Token Test Assistant",
                "backstory": "Testing invalid token scenarios",
            }
        }

        # Test Case 1: Expired token simulation
        with patch("a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__") as mock_auth:
            mock_auth.side_effect = Exception("Token expired")

            response = await test_client.post(
                "/graphql",
                json={"query": mutation, "variables": variables},
                headers={"Authorization": "Bearer expired_token"},
            )

        assert response.status_code == 200  # GraphQL returns 200 even for errors
        data = response.json()
        assert "errors" in data, "Expired token should cause errors"

        # Test Case 2: Malformed token
        response = await test_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers={"Authorization": "Bearer malformed.token.structure"},
        )

        assert response.status_code == 200
        data = response.json()
        assert "errors" in data, "Malformed token should cause errors"

        # Test Case 3: Invalid token format (not Bearer)
        response = await test_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers={"Authorization": "Basic somebase64string"},
        )

        assert response.status_code == 200
        data = response.json()
        assert "errors" in data, "Invalid token format should cause errors"

    @pytest.mark.asyncio
    async def test_rate_limiting_simulation(
        self, test_client: AsyncClient, test_user: User
    ):
        """
        Test behavior under rapid successive requests.

        While not implementing actual rate limiting, this tests that
        the system handles rapid requests gracefully.
        """
        mutation = """
            mutation CreatePersonalAssistant($input: CreateAssistantInput!) {
                createPersonalAssistant(input: $input) {
                    success
                    message
                    assistant {
                        id
                        name
                    }
                }
            }
        """

        variables = {
            "input": {
                "name": "Rapid Request Assistant",
                "backstory": "Testing rapid requests",
            }
        }

        # Make multiple rapid requests
        responses = []

        with patch("a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__") as mock_auth:
            from fastapi.security import HTTPAuthorizationCredentials

            mock_auth.return_value = HTTPAuthorizationCredentials(
                scheme="Bearer", credentials=test_user.clerk_user_id
            )

            # Send 5 rapid requests
            for i in range(5):
                response = await test_client.post(
                    "/graphql",
                    json={"query": mutation, "variables": variables},
                    headers={"Authorization": "Bearer fake_token"},
                )
                responses.append(response)

        # Verify that only one succeeds (due to unique constraint)
        success_count = 0
        error_count = 0

        for response in responses:
            assert response.status_code == 200
            data = response.json()

            if "errors" in data and data["errors"]:
                error_count += 1
            else:
                success_count += 1

        # Should have exactly one success and multiple failures
        assert success_count == 1, f"Expected 1 success, got {success_count}"
        assert error_count == 4, f"Expected 4 errors, got {error_count}"

    @pytest.mark.asyncio
    async def test_sql_injection_protection(
        self, test_client: AsyncClient, test_user: User
    ):
        """
        Test protection against SQL injection attempts.

        Verifies that malicious input is properly sanitized.
        """
        mutation = """
            mutation CreatePersonalAssistant($input: CreateAssistantInput!) {
                createPersonalAssistant(input: $input) {
                    success
                    message
                    assistant {
                        id
                        name
                        backstory
                    }
                }
            }
        """

        # Test with various SQL injection payloads in different fields
        sql_injection_payloads = [
            "'; DROP TABLE assistants; --",
            '" OR 1=1; --',
            "Robert'); DROP TABLE assistants; --",
            "<script>alert('XSS')</script>",  # Also testing XSS protection
            "' UNION SELECT * FROM users; --",
        ]

        for payload in sql_injection_payloads:
            # Try injection in name
            variables_name = {
                "input": {"name": payload, "backstory": "Regular backstory"}
            }

            # Try injection in backstory
            variables_backstory = {
                "input": {"name": "Regular Name", "backstory": payload}
            }

            # Mock authentication
            with patch(
                "a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__"
            ) as mock_auth:
                from fastapi.security import HTTPAuthorizationCredentials

                mock_auth.return_value = HTTPAuthorizationCredentials(
                    scheme="Bearer", credentials=test_user.clerk_user_id
                )

                # Test injection in name
                response_name = await test_client.post(
                    "/graphql",
                    json={"query": mutation, "variables": variables_name},
                    headers={"Authorization": "Bearer fake_token"},
                )

                # Test injection in backstory
                response_backstory = await test_client.post(
                    "/graphql",
                    json={"query": mutation, "variables": variables_backstory},
                    headers={"Authorization": "Bearer fake_token"},
                )

            # For both requests, either they should fail with validation errors,
            # or the system should sanitize/escape the input properly

            # Check name injection response
            assert response_name.status_code == 200
            data_name = response_name.json()

            # If it succeeds, the name should be stored as is (escaped/sanitized by ORM/DB)
            if "errors" not in data_name or not data_name["errors"]:
                assistant_data = data_name["data"]["createPersonalAssistant"][
                    "assistant"
                ]
                assert assistant_data["name"] == payload, (
                    "Name should be stored as provided"
                )

                # Verify the system didn't actually execute the SQL command
                # by ensuring we can still make requests afterward
                verification_response = await test_client.post(
                    "/graphql",
                    json={"query": "query { __typename }"},
                    headers={"Authorization": "Bearer fake_token"},
                )
                assert verification_response.status_code == 200, (
                    "API should still be functional"
                )

            # Check backstory injection response
            assert response_backstory.status_code == 200
            data_backstory = response_backstory.json()

            # Same verification for backstory
            if "errors" not in data_backstory or not data_backstory["errors"]:
                assistant_data = data_backstory["data"]["createPersonalAssistant"][
                    "assistant"
                ]
                assert assistant_data["backstory"] == payload, (
                    "Backstory should be stored as provided"
                )

                verification_response = await test_client.post(
                    "/graphql",
                    json={"query": "query { __typename }"},
                    headers={"Authorization": "Bearer fake_token"},
                )
                assert verification_response.status_code == 200, (
                    "API should still be functional"
                )
