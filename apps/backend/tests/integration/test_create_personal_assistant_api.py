"""
Integration tests for the createPersonalAssistant GraphQL API.

These tests verify the GraphQL mutation for creating personal assistants
as specified in US1.1 QA specifications.

Test IDs covered:
- API-TC-001: Happy Path - Create PA with valid auth and minimal required data
- API-TC-002: Authentication - Attempt PA creation without authentication
- API-TC-003: Authorization - Verify user can only create their own PA
- API-TC-004: Input Validation - Empty name
- API-TC-005: Input Validation - Excessively long name
- API-TC-006: Input Validation - Provide valid backstory
- API-TC-007: Input Validation - Omit backstory
- API-TC-008: Conflict - Duplicate PA for same user
- API-TC-010: User Profile Update - Verify assistant relationship
"""

import uuid
from unittest.mock import patch

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.user import User


class TestCreatePersonalAssistantAPI:
    """Tests for the createPersonalAssistant GraphQL mutation."""

    @pytest.mark.asyncio
    async def test_create_personal_assistant_happy_path(
        self, test_client: AsyncClient, test_user: User, db_session_real: AsyncSession
    ):
        """
        API-TC-001: Happy Path - Create PA with valid auth and minimal required data.

        Tests successful creation of a personal assistant with valid authentication
        and minimal required data (name only).
        """
        # Arrange
        mutation = """
            mutation CreatePersonalAssistant($input: CreateAssistantInput!) {
                createPersonalAssistant(input: $input) {
                    success
                    message
                    assistant {
                        id
                        userId
                        name
                        backstory
                        createdAt
                        updatedAt
                    }
                }
            }
        """

        # Use minimal required data (name and backstory since backstory is required)
        variables = {
            "input": {
                "name": "Test Personal Assistant",
                "backstory": "A simple backstory for testing",  # Including backstory as it's required
            }
        }

        # Mock authentication
        with patch("a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__") as mock_auth:
            from fastapi.security import HTTPAuthorizationCredentials

            mock_auth.return_value = HTTPAuthorizationCredentials(
                scheme="Bearer", credentials=test_user.clerk_user_id
            )

            # Act - Create the personal assistant
            response = await test_client.post(
                "/graphql",
                json={"query": mutation, "variables": variables},
                headers={"Authorization": "Bearer fake_token"},
            )

        # Assert
        assert response.status_code == 200
        data = response.json()

        # Check for no GraphQL errors
        assert "errors" not in data or not data["errors"], (
            f"Got GraphQL errors: {data.get('errors')}"
        )

        # Verify response data
        payload = data["data"]["createPersonalAssistant"]
        assert payload["success"] is True
        assert "created successfully" in payload["message"].lower()

        assistant_data = payload["assistant"]
        assert assistant_data["id"] is not None
        assert assistant_data["userId"] == str(test_user.id)
        assert assistant_data["name"] == "Test Personal Assistant"
        assert assistant_data["backstory"] == "A simple backstory for testing"

        # Verify the PA was actually created in the database
        from sqlalchemy import select

        from a2a_platform.db.models.assistant import Assistant

        result = await db_session_real.execute(
            select(Assistant).where(Assistant.user_id == test_user.id)
        )
        assistants = result.scalars().all()

        assert len(assistants) == 1, "Should have exactly one assistant in database"
        assert assistants[0].name == "Test Personal Assistant"
        assert assistants[0].user_id == test_user.id

    @pytest.mark.asyncio
    async def test_create_personal_assistant_without_authentication(
        self, test_client: AsyncClient
    ):
        """
        API-TC-002: Authentication - Attempt PA creation without authentication.

        Tests that creating a personal assistant without authentication fails.
        """
        # Arrange
        mutation = """
            mutation CreatePersonalAssistant($input: CreateAssistantInput!) {
                createPersonalAssistant(input: $input) {
                    success
                    message
                    assistant {
                        id
                        name
                    }
                }
            }
        """

        variables = {
            "input": {"name": "Unauthorized Assistant", "backstory": "This should fail"}
        }

        # Act - No Authorization header
        response = await test_client.post(
            "/graphql", json={"query": mutation, "variables": variables}
        )

        # Assert
        assert response.status_code == 200  # GraphQL returns 200 even for errors
        data = response.json()

        # Should have GraphQL errors indicating authentication failure
        assert "errors" in data
        assert len(data["errors"]) > 0

        # Check for authentication-related error
        error_messages = [error["message"].lower() for error in data["errors"]]
        auth_error_found = any(
            "auth" in msg or "unauthorized" in msg or "token" in msg
            for msg in error_messages
        )
        assert auth_error_found, f"Expected authentication error, got: {error_messages}"

    @pytest.mark.asyncio
    async def test_create_personal_assistant_empty_name(
        self, test_client: AsyncClient, test_user: User
    ):
        """
        API-TC-004: Input Validation - Empty name.

        Tests that creating a personal assistant with an empty name fails.
        """
        # Arrange
        mutation = """
            mutation CreatePersonalAssistant($input: CreateAssistantInput!) {
                createPersonalAssistant(input: $input) {
                    success
                    message
                    assistant {
                        id
                        name
                    }
                }
            }
        """

        variables = {
            "input": {
                "name": "",  # Empty name
                "backstory": "Valid backstory",
            }
        }

        # Mock authentication
        with patch("a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__") as mock_auth:
            from fastapi.security import HTTPAuthorizationCredentials

            mock_auth.return_value = HTTPAuthorizationCredentials(
                scheme="Bearer", credentials=test_user.clerk_user_id
            )

            # Act
            response = await test_client.post(
                "/graphql",
                json={"query": mutation, "variables": variables},
                headers={"Authorization": "Bearer fake_token"},
            )

        # Assert
        assert response.status_code == 200
        data = response.json()

        # Should have validation errors
        assert "errors" in data
        assert len(data["errors"]) > 0

        # Check for validation error related to name
        error_messages = [error["message"].lower() for error in data["errors"]]
        name_error_found = any(
            "name" in msg
            and (
                "required" in msg
                or "empty" in msg
                or "invalid" in msg
                or "should have at least" in msg
                or "too short" in msg
            )
            for msg in error_messages
        )
        assert name_error_found, (
            f"Expected name validation error, got: {error_messages}"
        )

    @pytest.mark.asyncio
    async def test_create_personal_assistant_long_name(
        self, test_client: AsyncClient, test_user: User
    ):
        """
        API-TC-005: Input Validation - Excessively long name.

        Tests that creating a personal assistant with an excessively long name fails.
        """
        # Arrange
        mutation = """
            mutation CreatePersonalAssistant($input: CreateAssistantInput!) {
                createPersonalAssistant(input: $input) {
                    success
                    message
                    assistant {
                        id
                        name
                    }
                }
            }
        """

        # Create a name that exceeds the 100 character limit
        long_name = "A" * 101

        variables = {"input": {"name": long_name, "backstory": "Valid backstory"}}

        # Mock authentication
        with patch("a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__") as mock_auth:
            from fastapi.security import HTTPAuthorizationCredentials

            mock_auth.return_value = HTTPAuthorizationCredentials(
                scheme="Bearer", credentials=test_user.clerk_user_id
            )

            # Act
            response = await test_client.post(
                "/graphql",
                json={"query": mutation, "variables": variables},
                headers={"Authorization": "Bearer fake_token"},
            )

        # Assert
        assert response.status_code == 200
        data = response.json()

        # Should have validation errors
        assert "errors" in data
        assert len(data["errors"]) > 0

        # Check for validation error related to name length
        error_messages = [error["message"].lower() for error in data["errors"]]
        length_error_found = any(
            "name" in msg and ("length" in msg or "long" in msg or "max" in msg)
            for msg in error_messages
        )
        assert length_error_found, (
            f"Expected name length validation error, got: {error_messages}"
        )

    @pytest.mark.asyncio
    async def test_create_personal_assistant_with_backstory(
        self, test_client: AsyncClient, test_user: User, db_session_real: AsyncSession
    ):
        """
        API-TC-006: Input Validation - Provide valid backstory.

        Tests successful creation with a custom backstory.
        """
        # Arrange
        mutation = """
            mutation CreatePersonalAssistant($input: CreateAssistantInput!) {
                createPersonalAssistant(input: $input) {
                    success
                    message
                    assistant {
                        id
                        userId
                        name
                        backstory
                        createdAt
                        updatedAt
                    }
                }
            }
        """

        # Include a detailed backstory
        custom_backstory = (
            "This is a custom backstory for my AI assistant. "
            "It should be helpful, friendly, and knowledgeable "
            "about various topics while maintaining a professional demeanor."
        )

        variables = {
            "input": {"name": "Backstory Assistant", "backstory": custom_backstory}
        }

        # Mock authentication
        with patch("a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__") as mock_auth:
            from fastapi.security import HTTPAuthorizationCredentials

            mock_auth.return_value = HTTPAuthorizationCredentials(
                scheme="Bearer", credentials=test_user.clerk_user_id
            )

            # Act - Create the personal assistant with backstory
            response = await test_client.post(
                "/graphql",
                json={"query": mutation, "variables": variables},
                headers={"Authorization": "Bearer fake_token"},
            )

        # Assert
        assert response.status_code == 200
        data = response.json()

        # Check for no GraphQL errors
        assert "errors" not in data or not data["errors"], (
            f"Got GraphQL errors: {data.get('errors')}"
        )

        # Verify response data
        payload = data["data"]["createPersonalAssistant"]
        assert payload["success"] is True
        assert "created successfully" in payload["message"].lower()

        assistant_data = payload["assistant"]
        assert assistant_data["id"] is not None
        assert assistant_data["userId"] == str(test_user.id)
        assert assistant_data["name"] == "Backstory Assistant"
        assert assistant_data["backstory"] == custom_backstory

        # Verify the PA was actually created in the database with the backstory
        from sqlalchemy import select

        from a2a_platform.db.models.assistant import Assistant

        result = await db_session_real.execute(
            select(Assistant).where(Assistant.user_id == test_user.id)
        )
        assistants = result.scalars().all()

        assert len(assistants) == 1, "Should have exactly one assistant in database"
        assert assistants[0].name == "Backstory Assistant"
        assert assistants[0].backstory == custom_backstory
        assert assistants[0].user_id == test_user.id

    @pytest.mark.asyncio
    async def test_create_personal_assistant_duplicate_user(
        self, test_client: AsyncClient, test_assistant_id: uuid.UUID, test_user: User
    ):
        """
        API-TC-008: Conflict - Duplicate PA for same user.

        Tests that attempting to create a second personal assistant for a user
        who already has one fails with an appropriate error.
        """
        # Arrange - test_assistant_id fixture already creates an assistant for test_user
        mutation = """
            mutation CreatePersonalAssistant($input: CreateAssistantInput!) {
                createPersonalAssistant(input: $input) {
                    success
                    message
                    assistant {
                        id
                        name
                    }
                }
            }
        """

        variables = {
            "input": {"name": "Second Assistant", "backstory": "This should fail"}
        }

        # Mock authentication
        with patch("a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__") as mock_auth:
            from fastapi.security import HTTPAuthorizationCredentials

            mock_auth.return_value = HTTPAuthorizationCredentials(
                scheme="Bearer", credentials=test_user.clerk_user_id
            )

            # Act
            response = await test_client.post(
                "/graphql",
                json={"query": mutation, "variables": variables},
                headers={"Authorization": "Bearer fake_token"},
            )

        # Assert
        assert response.status_code == 200
        data = response.json()

        # Should have errors indicating duplicate assistant
        assert "errors" in data
        assert len(data["errors"]) > 0

        # Check for duplicate/conflict error
        error_messages = [error["message"].lower() for error in data["errors"]]
        conflict_error_found = any(
            "already" in msg or "exists" in msg or "duplicate" in msg
            for msg in error_messages
        )
        assert conflict_error_found, f"Expected conflict error, got: {error_messages}"
