"""
End-to-end integration tests for Personal Assistant setup flow.

These tests verify the complete user journey from registration to PA creation
as specified in US1.1 QA specifications.

Test IDs covered:
- E2E-TC-001: Full Happy Path - New user registration to PA creation
- E2E-TC-002: User Exists, No PA - User with record but no assistant
- E2E-TC-006: Concurrent PA Creation Attempt - Race condition handling
"""

import asyncio
import uuid
from unittest.mock import patch

import pytest
from httpx import AsyncClient
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.assistant import Assistant
from a2a_platform.schemas.assistant_schemas import AssistantCreate
from a2a_platform.schemas.user import UserCreate
from a2a_platform.services.user_service import create_user


class TestAssistantE2EFlow:
    """End-to-end tests for the Personal Assistant setup flow."""

    @pytest.mark.asyncio
    async def test_full_happy_path_new_user_to_pa_creation(
        self, test_client: AsyncClient, db_session_real: AsyncSession
    ):
        """
        E2E-TC-001: Full Happy Path - New user registration to PA creation.

        Tests the complete flow from new user creation through PA setup.
        This simulates the user journey described in the QA specifications.
        """
        # Step 1: Simulate new user creation (as would happen via Clerk webhook)
        clerk_user_id = f"test_e2e_user_{uuid.uuid4()}"
        user_email = f"test_e2e_{uuid.uuid4()}@example.com"

        user_data = UserCreate(
            clerk_user_id=clerk_user_id,
            email=user_email,
            timezone="America/New_York",
        )
        user = await create_user(db_session_real, user_data)

        # Step 2: Verify user has no assistant initially (setup not complete)
        from a2a_platform.services.assistant_service import AssistantService

        assistant_service = AssistantService(db_session=db_session_real)
        initial_assistant = await assistant_service.get_user_assistant(user.id)
        assert initial_assistant is None, "New user should not have an assistant"

    @pytest.mark.asyncio
    async def test_create_personal_assistant_with_invalid_inputs(
        self, test_client: AsyncClient, db_session_real: AsyncSession
    ):
        """
        E2E-TC-003: Invalid Inputs - Error handling for invalid PA creation inputs.

        Tests that the API properly validates input data and returns appropriate
        error messages for invalid inputs.
        """
        # Step 1: Create a test user
        clerk_user_id = f"test_invalid_input_{uuid.uuid4()}"
        user_data = UserCreate(
            clerk_user_id=clerk_user_id,
            email=f"invalid_input_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user = await create_user(db_session_real, user_data)

        # Step 2: Prepare GraphQL mutation
        mutation = """
            mutation CreatePersonalAssistant($input: CreateAssistantInput!) {
                createPersonalAssistant(input: $input) {
                    success
                    message
                    assistant {
                        id
                        name
                    }
                }
            }
        """

        # Step 3: Test with empty name
        variables_empty_name = {
            "input": {
                "name": "",  # Empty name should be rejected
                "backstory": "Valid backstory",
            }
        }

        with patch("a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__") as mock_auth:
            from fastapi.security import HTTPAuthorizationCredentials

            mock_auth.return_value = HTTPAuthorizationCredentials(
                scheme="Bearer", credentials=clerk_user_id
            )

            response = await test_client.post(
                "/graphql",
                json={"query": mutation, "variables": variables_empty_name},
                headers={"Authorization": "Bearer fake_token"},
            )

        # Verify validation error for empty name
        assert response.status_code == 200
        data = response.json()
        assert "errors" in data, "Expected validation errors for empty name"

        # Step 4: Test with missing backstory
        variables_missing_backstory = {
            "input": {
                "name": "Valid Name"
                # Backstory is missing
            }
        }

        with patch("a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__") as mock_auth:
            from fastapi.security import HTTPAuthorizationCredentials

            mock_auth.return_value = HTTPAuthorizationCredentials(
                scheme="Bearer", credentials=clerk_user_id
            )

            response = await test_client.post(
                "/graphql",
                json={"query": mutation, "variables": variables_missing_backstory},
                headers={"Authorization": "Bearer fake_token"},
            )

        # Verify validation error for missing backstory
        assert response.status_code == 200
        data = response.json()
        assert "errors" in data, "Expected validation errors for missing backstory"

        # Step 5: Verify no assistant was created during these invalid attempts
        result = await db_session_real.execute(
            select(Assistant).where(Assistant.user_id == user.id)
        )
        assistants = result.scalars().all()
        assert len(assistants) == 0, (
            "No assistant should be created from invalid inputs"
        )

    @pytest.mark.asyncio
    async def test_create_personal_assistant_duplicate_attempt(
        self, test_client: AsyncClient, db_session_real: AsyncSession
    ):
        """
        E2E-TC-004: Duplicate PA Creation - Idempotency handling for PA creation.

        Tests that attempting to create a PA for a user who already has one
        is handled gracefully with appropriate error responses.
        """
        # Step 1: Create a test user
        clerk_user_id = f"test_duplicate_{uuid.uuid4()}"
        user_data = UserCreate(
            clerk_user_id=clerk_user_id,
            email=f"duplicate_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user = await create_user(db_session_real, user_data)

        # Step 2: Create initial PA for the user
        from a2a_platform.services.assistant_service import AssistantService

        assistant_service = AssistantService(db_session=db_session_real)
        initial_assistant_data = AssistantCreate(
            name="Initial Assistant",
            backstory="First PA for user",
        )
        initial_assistant = await assistant_service.create_personal_assistant(
            user_id=user.id, assistant_data=initial_assistant_data
        )

        # Step 3: Attempt to create another PA via GraphQL
        mutation = """
            mutation CreatePersonalAssistant($input: CreateAssistantInput!) {
                createPersonalAssistant(input: $input) {
                    success
                    message
                    assistant {
                        id
                        name
                    }
                }
            }
        """

        variables = {
            "input": {
                "name": "Second Assistant",
                "backstory": "Should be rejected",
            }
        }

        with patch("a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__") as mock_auth:
            from fastapi.security import HTTPAuthorizationCredentials

            mock_auth.return_value = HTTPAuthorizationCredentials(
                scheme="Bearer", credentials=clerk_user_id
            )

            response = await test_client.post(
                "/graphql",
                json={"query": mutation, "variables": variables},
                headers={"Authorization": "Bearer fake_token"},
            )

        # Step 4: Verify appropriate error response
        assert response.status_code == 200
        data = response.json()

        # API might handle this in different ways, check both possibilities
        if "errors" in data and data["errors"]:
            # GraphQL-level error
            error_found = any(
                "already" in error.get("message", "").lower()
                or "exists" in error.get("message", "").lower()
                or "duplicate" in error.get("message", "").lower()
                for error in data["errors"]
            )
            assert error_found, (
                f"Expected duplicate error message, got: {data['errors']}"
            )
        else:
            # Application-level error in success=False response
            payload = data["data"]["createPersonalAssistant"]
            assert payload["success"] is False, (
                "Expected failure for duplicate PA creation"
            )
            assert (
                "already" in payload["message"].lower()
                or "exists" in payload["message"].lower()
            )

        # Step 5: Verify only one assistant exists in database
        result = await db_session_real.execute(
            select(Assistant).where(Assistant.user_id == user.id)
        )
        assistants = result.scalars().all()
        assert len(assistants) == 1, "Should still have exactly one assistant"
        assert assistants[0].id == initial_assistant.id, (
            "Original assistant should be unchanged"
        )

    @pytest.mark.asyncio
    async def test_create_personal_assistant_unauthorized(
        self, test_client: AsyncClient
    ):
        """
        E2E-TC-005: Unauthorized Access - Authentication checks for PA creation.

        Tests that the API properly rejects PA creation attempts without valid
        authentication credentials.
        """
        # Step 1: Prepare GraphQL mutation
        mutation = """
            mutation CreatePersonalAssistant($input: CreateAssistantInput!) {
                createPersonalAssistant(input: $input) {
                    success
                    message
                }
            }
        """

        variables = {
            "input": {
                "name": "Unauthorized Assistant",
                "backstory": "Should be rejected due to auth",
            }
        }

        # Step 2: Send request without authentication
        response = await test_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            # No Authorization header
        )

        # Step 3: Verify unauthorized response
        # GraphQL returns 200 with errors in the response body
        assert response.status_code == 200
        data = response.json()
        assert "errors" in data and data["errors"], (
            "Expected GraphQL errors for unauthorized request"
        )

        # Verify it's an authentication error
        auth_error = any(
            "auth" in error.get("message", "").lower()
            or "unauthorized" in error.get("message", "").lower()
            or "permission" in error.get("message", "").lower()
            for error in data["errors"]
        )
        assert auth_error, f"Expected authentication error, got: {data['errors']}"

    @pytest.mark.asyncio
    async def test_create_pa_with_all_optional_fields(
        self, test_client: AsyncClient, db_session_real: AsyncSession
    ):
        """
        E2E-TC-007: Optional Fields - Creating PA with all optional fields.

        Tests creation of a PA with all optional fields including avatar_file_id
        and configuration to ensure they're properly stored and retrieved.
        """
        # Step 1: Create a test user
        clerk_user_id = f"test_full_fields_{uuid.uuid4()}"
        user_data = UserCreate(
            clerk_user_id=clerk_user_id,
            email=f"full_fields_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user = await create_user(db_session_real, user_data)

        # Step 2: Prepare GraphQL mutation with all fields
        mutation = """
            mutation CreatePersonalAssistant($input: CreateAssistantInput!) {
                createPersonalAssistant(input: $input) {
                    success
                    message
                    assistant {
                        id
                        userId
                        name
                        backstory
                        avatarFileId
                        configuration
                        createdAt
                        updatedAt
                    }
                }
            }
        """

        avatar_file_id = str(uuid.uuid4())
        configuration = {
            "theme": "light",
            "notifications": {"enabled": True},
            "preferences": {"showTips": True},
        }

        variables = {
            "input": {
                "name": "Full Fields Assistant",
                "backstory": "Testing all optional fields",
                "avatarFileId": avatar_file_id,
                "configuration": configuration,
            }
        }

        # Step 3: Send the request
        with patch("a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__") as mock_auth:
            from fastapi.security import HTTPAuthorizationCredentials

            mock_auth.return_value = HTTPAuthorizationCredentials(
                scheme="Bearer", credentials=clerk_user_id
            )

            response = await test_client.post(
                "/graphql",
                json={"query": mutation, "variables": variables},
                headers={"Authorization": "Bearer fake_token"},
            )

        # Step 4: Verify successful response with all fields
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data or not data["errors"]

        payload = data["data"]["createPersonalAssistant"]
        assert payload["success"] is True

        assistant_data = payload["assistant"]
        assert assistant_data["name"] == "Full Fields Assistant"
        assert assistant_data["backstory"] == "Testing all optional fields"
        assert assistant_data["avatarFileId"] == avatar_file_id
        assert assistant_data["configuration"] == configuration

        # Step 5: Verify database record has all fields
        result = await db_session_real.execute(
            select(Assistant).where(Assistant.user_id == user.id)
        )
        db_assistant = result.scalar_one_or_none()

        assert db_assistant is not None
        assert db_assistant.name == "Full Fields Assistant"
        assert db_assistant.backstory == "Testing all optional fields"
        assert str(db_assistant.avatar_file_id) == avatar_file_id
        assert db_assistant.configuration == configuration

    @pytest.mark.asyncio
    async def test_full_happy_path_new_user_to_pa_creation_complete(
        self, test_client: AsyncClient, db_session_real: AsyncSession
    ):
        """
        Complete E2E test for new user to PA creation.

        This test was split from the original to include the PA creation step.
        """
        # Step 1: Simulate new user creation (as would happen via Clerk webhook)
        clerk_user_id = f"test_e2e_user_{uuid.uuid4()}"
        user_email = f"test_e2e_{uuid.uuid4()}@example.com"

        user_data = UserCreate(
            clerk_user_id=clerk_user_id,
            email=user_email,
            timezone="America/New_York",
        )
        user = await create_user(db_session_real, user_data)

        # Step 2: Verify user has no assistant initially (setup not complete)
        from a2a_platform.services.assistant_service import AssistantService

        assistant_service = AssistantService(db_session=db_session_real)
        initial_assistant = await assistant_service.get_user_assistant(user.id)
        assert initial_assistant is None, "New user should not have an assistant"

        # Step 3: Simulate PA creation via GraphQL mutation
        mutation = """
            mutation CreatePersonalAssistant($input: CreateAssistantInput!) {
                createPersonalAssistant(input: $input) {
                    success
                    message
                    assistant {
                        id
                        userId
                        name
                        backstory
                        avatarFileId
                        configuration
                        createdAt
                        updatedAt
                    }
                }
            }
        """

        variables = {
            "input": {
                "name": "My Personal Assistant",
                "backstory": "A helpful AI assistant to manage my daily tasks and goals",
            }
        }

        # Mock authentication to return our test user's Clerk ID
        with patch("a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__") as mock_auth:
            from fastapi.security import HTTPAuthorizationCredentials

            mock_auth.return_value = HTTPAuthorizationCredentials(
                scheme="Bearer", credentials=clerk_user_id
            )

            # Act - Create the personal assistant
            response = await test_client.post(
                "/graphql",
                json={"query": mutation, "variables": variables},
                headers={"Authorization": "Bearer fake_token"},
            )

        # Step 4: Verify successful PA creation
        assert response.status_code == 200
        data = response.json()

        # Verify no GraphQL errors
        assert "errors" not in data or not data["errors"]

        # Verify response data
        payload = data["data"]["createPersonalAssistant"]
        assert payload["success"] is True
        assert payload["message"] == "Assistant created successfully"

        assistant_data = payload["assistant"]
        assert assistant_data["id"] is not None
        assert assistant_data["userId"] == str(user.id)
        assert assistant_data["name"] == "My Personal Assistant"
        assert (
            assistant_data["backstory"]
            == "A helpful AI assistant to manage my daily tasks and goals"
        )

        # Step 5: Verify database state and user-assistant relationship
        final_assistant = await assistant_service.get_user_assistant(user.id)
        assert final_assistant is not None, "User should now have an assistant"
        assert final_assistant.name == "My Personal Assistant"

        # Step 6: Verify PA setup is now complete (user has assistant)
        # This simulates how the frontend would determine if setup is complete
        setup_complete = final_assistant is not None
        assert setup_complete, "PA setup should be marked as complete"

        # Step 7: Verify assistant data integrity in database
        result = await db_session_real.execute(
            select(Assistant).where(Assistant.user_id == user.id)
        )
        db_assistant = result.scalar_one_or_none()

        assert db_assistant is not None
        assert db_assistant.name == "My Personal Assistant"
        assert db_assistant.user_id == user.id
        assert db_assistant.created_at is not None
        assert db_assistant.updated_at is not None

    @pytest.mark.asyncio
    async def test_user_exists_no_pa_setup_flow(
        self, test_client: AsyncClient, db_session_real: AsyncSession
    ):
        """
        E2E-TC-002: User Exists, No PA - User with record but no assistant.

        Tests the scenario where a user exists in the database but hasn't
        completed PA setup yet.
        """
        # Step 1: Create a user with no PA
        clerk_user_id = f"test_user_no_pa_{uuid.uuid4()}"
        user_email = f"test_no_pa_{uuid.uuid4()}@example.com"

        user_data = UserCreate(
            clerk_user_id=clerk_user_id,
            email=user_email,
            timezone="America/Los_Angeles",
        )
        existing_user = await create_user(db_session_real, user_data)

        # Step 2: Verify user has no assistant initially (setup not complete)
        from a2a_platform.services.assistant_service import AssistantService

        assistant_service = AssistantService(db_session=db_session_real)
        initial_assistant = await assistant_service.get_user_assistant(existing_user.id)
        assert initial_assistant is None, "New user should not have an assistant"

        # Step 3: Simulate PA creation via GraphQL mutation
        mutation = """
            mutation CreatePersonalAssistant($input: CreateAssistantInput!) {
                createPersonalAssistant(input: $input) {
                    success
                    message
                    assistant {
                        id
                        userId
                        name
                        backstory
                    }
                }
            }
        """

        variables = {
            "input": {
                "name": "Late Setup Assistant",
                "backstory": "Created after initial user registration",
            }
        }

        with patch("a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__") as mock_auth:
            from fastapi.security import HTTPAuthorizationCredentials

            mock_auth.return_value = HTTPAuthorizationCredentials(
                scheme="Bearer", credentials=clerk_user_id
            )

            response = await test_client.post(
                "/graphql",
                json={"query": mutation, "variables": variables},
                headers={"Authorization": "Bearer fake_token"},
            )

        # Step 5: Verify successful creation
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data or not data["errors"], (
            f"Got GraphQL errors: {data.get('errors')}"
        )

        # Step 6: Verify setup is now complete - use a direct query instead of accessing the relationship
        # This avoids the SQLAlchemy greenlet issue
        from sqlalchemy import select

        from a2a_platform.db.models.assistant import Assistant

        # Query to check if an assistant was created for the user
        result = await db_session_real.execute(
            select(Assistant).where(Assistant.user_id == existing_user.id)
        )
        assistants = result.scalars().all()

        # Verify the assistant was created
        assert len(assistants) == 1, "User should now have an assistant"
        setup_complete = len(assistants) == 1
        assert setup_complete, "PA setup should now be complete"

        # Verify the created assistant data
        assistant_data = data["data"]["createPersonalAssistant"]["assistant"]
        assert assistant_data["userId"] == str(existing_user.id)
        assert assistant_data["name"] == "Late Setup Assistant"
        assert assistant_data["backstory"] == "Created after initial user registration"

        # This assertion was already done above, removed to avoid duplication

    @pytest.mark.asyncio
    async def test_concurrent_pa_creation_attempt(
        self, test_client: AsyncClient, db_session_real: AsyncSession
    ):
        """
        E2E-TC-006: Concurrent PA Creation Attempt - Race condition handling.

        Tests that attempting to create multiple assistants for the same user
        concurrently is handled gracefully with proper error responses.
        """
        # Step 1: Create a test user
        clerk_user_id = f"test_concurrent_{uuid.uuid4()}"
        user_data = UserCreate(
            clerk_user_id=clerk_user_id,
            email=f"concurrent_test_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user = await create_user(db_session_real, user_data)

        # Step 2: Prepare two concurrent PA creation requests
        mutation = """
            mutation CreatePersonalAssistant($input: CreateAssistantInput!) {
                createPersonalAssistant(input: $input) {
                    success
                    message
                    assistant {
                        id
                        name
                        userId
                    }
                }
            }
        """

        variables_1 = {
            "input": {
                "name": "First Assistant",
                "backstory": "First concurrent creation attempt",
            }
        }

        variables_2 = {
            "input": {
                "name": "Second Assistant",
                "backstory": "Second concurrent creation attempt",
            }
        }

        # Step 3: Mock authentication for both requests
        with patch("a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__") as mock_auth:
            from fastapi.security import HTTPAuthorizationCredentials

            mock_auth.return_value = HTTPAuthorizationCredentials(
                scheme="Bearer", credentials=clerk_user_id
            )

            # Step 4: Create concurrent requests
            async def create_assistant(variables):
                response = await test_client.post(
                    "/graphql",
                    json={"query": mutation, "variables": variables},
                    headers={"Authorization": "Bearer fake_token"},
                )
                return response

            # Execute both requests simultaneously
            response_1, response_2 = await asyncio.gather(
                create_assistant(variables_1),
                create_assistant(variables_2),
                return_exceptions=True,
            )

            responses = [response_1, response_2]
        success_count = 0
        error_count = 0

        for response in responses:
            if isinstance(response, Exception):
                error_count += 1
                continue

            assert response.status_code == 200
            data = response.json()

            if "errors" in data and data["errors"]:
                error_count += 1
                # Verify it's a conflict/duplicate error, SQLAlchemy session warning, or asyncpg connection error
                error_messages = [error["message"].lower() for error in data["errors"]]
                conflict_found = any(
                    "already" in msg
                    or "exists" in msg
                    or "duplicate" in msg
                    or "session.add()" in msg  # SQLAlchemy session warning
                    or "flush process" in msg  # SQLAlchemy session warning
                    or "another operation is in progress"
                    in msg  # asyncpg concurrent operation error
                    or "interfaceerror" in msg  # asyncpg interface error
                    or "this session is provisioning a new connection"
                    in msg  # SQLAlchemy concurrent session error
                    or "concurrent operations are not permitted"
                    in msg  # SQLAlchemy concurrent session error
                    for msg in error_messages
                )
                assert conflict_found, (
                    f"Expected conflict, session, or asyncpg concurrent operation error, got: {error_messages}"
                )
            else:
                success_count += 1
                # Verify successful creation
                payload = data["data"]["createPersonalAssistant"]
                assert payload["success"] is True
                assistant_data = payload["assistant"]
                assert assistant_data["userId"] == str(user.id)

        # Step 6: Verify exactly one creation succeeded and one failed
        assert success_count == 1, f"Expected exactly 1 success, got {success_count}"
        assert error_count == 1, f"Expected exactly 1 error, got {error_count}"

        # Step 7: Verify only one assistant exists in database
        result = await db_session_real.execute(
            select(Assistant).where(Assistant.user_id == user.id)
        )
        assistants = result.scalars().all()
        assert len(assistants) == 1, "Should have exactly one assistant in database"

        # Step 8: Verify user relationship is correct
        from a2a_platform.services.assistant_service import AssistantService

        assistant_service_final = AssistantService(db_session=db_session_real)
        final_user_assistant = await assistant_service_final.get_user_assistant(user.id)
        assert final_user_assistant is not None, "User should have an assistant"
        assert final_user_assistant.id == assistants[0].id

    @pytest.mark.asyncio
    async def test_pa_setup_status_check_flow(self, db_session_real: AsyncSession):
        """
        Test the flow for checking PA setup status.

        This simulates how the frontend would determine whether to show
        the setup wizard or the main PA interface.
        """
        # Step 1: Create users in different states

        # User without PA
        user_no_pa_data = UserCreate(
            clerk_user_id=f"test_no_pa_status_{uuid.uuid4()}",
            email=f"no_pa_status_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user_no_pa = await create_user(db_session_real, user_no_pa_data)

        # User with PA
        user_with_pa_data = UserCreate(
            clerk_user_id=f"test_with_pa_status_{uuid.uuid4()}",
            email=f"with_pa_status_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user_with_pa = await create_user(db_session_real, user_with_pa_data)

        # Create PA for second user
        from a2a_platform.services.assistant_service import AssistantService

        assistant_service = AssistantService(db_session=db_session_real)
        assistant_data = AssistantCreate(
            name="Status Check Assistant",
            backstory="For testing setup status",
        )
        await assistant_service.create_personal_assistant(
            user_id=user_with_pa.id, assistant_data=assistant_data
        )

        # Step 2: Check setup status for both users

        # User without PA - setup not complete
        no_pa_assistant = await assistant_service.get_user_assistant(user_no_pa.id)
        setup_complete_no_pa = no_pa_assistant is not None
        assert not setup_complete_no_pa, "User without PA should show setup incomplete"

        # User with PA - setup complete
        with_pa_assistant = await assistant_service.get_user_assistant(user_with_pa.id)
        setup_complete_with_pa = with_pa_assistant is not None
        assert setup_complete_with_pa, "User with PA should show setup complete"

        # Step 3: Verify this matches the expected frontend behavior
        # - User without PA would see the setup wizard
        # - User with PA would see the main PA interface

        # This test verifies the backend provides the necessary data
        # for the frontend to make these routing decisions
