"""
Integration tests for Assistant database operations.

These tests verify the database schema, constraints, and data integrity
for the assistants table as specified in US1.1 QA specifications.

Test IDs covered:
- DB-TC-001: Verify assistants table schema after Alembic migration
- DB-TC-002: Verify Alembic migration downgrade (manual test)
- DB-TC-003: Verify user_id FK constraint and UNIQUE constraint
- DB-TC-004: Verify name and backstory NOT NULL constraints
- DB-TC-005: Verify default values for created_at, updated_at

These tests are SQLite-compatible and focus on basic database schema validation.
They test fundamental database constraints and operations that work identically
in both SQLite and PostgreSQL.
"""

import uuid
from datetime import datetime

import pytest
from sqlalchemy import inspect, text
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.assistant import Assistant
from a2a_platform.schemas.user import UserCreate
from a2a_platform.services.user_service import create_user

# Import SQLite fixtures
pytest_plugins = ["tests.conftest_sqlite"]


@pytest.mark.fast_db
class TestAssistantDatabaseSchema:
    """Tests for Assistant database schema and constraints."""

    @pytest.mark.asyncio
    async def test_assistants_table_schema(self, db_session_real: AsyncSession):
        """
        DB-TC-001: Verify assistants table schema after Alembic migration.

        Verifies that the assistants table exists with all required columns,
        correct data types, and proper constraints.
        """

        # Get table information using SQLAlchemy inspector with async engine
        def get_table_info(connection):
            inspector = inspect(connection)
            return {
                "tables": inspector.get_table_names(),
                "columns": inspector.get_columns("assistants"),
                "indexes": inspector.get_indexes("assistants"),
                "unique_constraints": inspector.get_unique_constraints("assistants"),
                "foreign_keys": inspector.get_foreign_keys("assistants"),
            }

        # Use run_sync to execute the inspection
        table_info = await db_session_real.connection()
        table_info = await table_info.run_sync(get_table_info)

        # Verify table exists
        tables = table_info["tables"]
        assert "assistants" in tables, "assistants table should exist"

        # Get column information
        columns = table_info["columns"]
        column_names = [col["name"] for col in columns]

        # Verify all required columns exist
        expected_columns = [
            "id",
            "user_id",
            "name",
            "backstory",
            "avatar_file_id",
            "configuration",
            "created_at",
            "updated_at",
        ]
        for col in expected_columns:
            assert col in column_names, f"Column {col} should exist in assistants table"

        # Verify column types and constraints
        column_dict = {col["name"]: col for col in columns}

        # Check id column (UUID, primary key)
        id_col = column_dict["id"]
        assert not id_col["nullable"], "id column should be NOT NULL"

        # Check user_id column (UUID, foreign key, unique)
        user_id_col = column_dict["user_id"]
        assert not user_id_col["nullable"], "user_id column should be NOT NULL"

        # Check name column (TEXT, NOT NULL)
        name_col = column_dict["name"]
        assert not name_col["nullable"], "name column should be NOT NULL"

        # Check backstory column (TEXT, NOT NULL)
        backstory_col = column_dict["backstory"]
        assert not backstory_col["nullable"], "backstory column should be NOT NULL"

        # Check avatar_file_id column (UUID, nullable)
        avatar_col = column_dict["avatar_file_id"]
        assert avatar_col["nullable"], "avatar_file_id column should be nullable"

        # Check configuration column (JSONB, nullable)
        config_col = column_dict["configuration"]
        assert config_col["nullable"], "configuration column should be nullable"

        # Check created_at column (TIMESTAMP WITH TIMEZONE, NOT NULL)
        created_col = column_dict["created_at"]
        assert not created_col["nullable"], "created_at column should be NOT NULL"

        # Check updated_at column (TIMESTAMP WITH TIMEZONE, NOT NULL)
        updated_col = column_dict["updated_at"]
        assert not updated_col["nullable"], "updated_at column should be NOT NULL"

        # Verify indexes
        indexes = table_info["indexes"]

        # Should have index on user_id
        user_id_indexes = [idx for idx in indexes if "user_id" in idx["column_names"]]
        assert len(user_id_indexes) > 0, "Should have index on user_id column"

        # Verify unique constraint on user_id (implemented as unique index)
        user_id_unique_index = any(
            "user_id" in idx["column_names"] and idx.get("unique", False)
            for idx in indexes
        )

        # Also check unique constraints table (alternative implementation)
        unique_constraints = table_info["unique_constraints"]
        user_id_unique_constraint = any(
            "user_id" in constraint["column_names"] for constraint in unique_constraints
        )

        # Either unique index or unique constraint should exist
        assert user_id_unique_index or user_id_unique_constraint, (
            "Should have unique constraint on user_id (either as index or constraint)"
        )

        # Verify foreign key constraints
        foreign_keys = table_info["foreign_keys"]
        user_fk = any(
            fk["constrained_columns"] == ["user_id"] and fk["referred_table"] == "users"
            for fk in foreign_keys
        )
        assert user_fk, "Should have foreign key constraint from user_id to users.id"

    @pytest.mark.asyncio
    async def test_user_id_foreign_key_constraint(self, db_session_real: AsyncSession):
        """
        DB-TC-003 (Part 1): Verify user_id FK constraint.

        Tests that inserting an assistant with a non-existent user_id fails.
        """
        # Try to create an assistant with a non-existent user_id
        non_existent_user_id = uuid.uuid4()

        assistant = Assistant(
            user_id=non_existent_user_id,
            name="Test Assistant",
            backstory="Test backstory",
        )

        db_session_real.add(assistant)

        # Should raise IntegrityError due to foreign key violation
        with pytest.raises(IntegrityError) as exc_info:
            await db_session_real.commit()

        # Verify it's a foreign key violation
        error_msg = str(exc_info.value).lower()
        assert (
            "foreign key" in error_msg or "violates foreign key constraint" in error_msg
        )

        await db_session_real.rollback()

    @pytest.mark.asyncio
    async def test_user_id_unique_constraint(self, db_session_real: AsyncSession):
        """
        DB-TC-003 (Part 2): Verify user_id UNIQUE constraint.

        Tests that creating two assistants for the same user fails.
        """
        # Create a test user
        user_data = UserCreate(
            clerk_user_id=f"test_unique_{uuid.uuid4()}",
            email=f"test_unique_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user = await create_user(db_session_real, user_data)

        # Create first assistant
        assistant1 = Assistant(
            user_id=user.id,
            name="First Assistant",
            backstory="First backstory",
        )
        db_session_real.add(assistant1)
        await db_session_real.commit()

        # Try to create second assistant for the same user
        assistant2 = Assistant(
            user_id=user.id,
            name="Second Assistant",
            backstory="Second backstory",
        )
        db_session_real.add(assistant2)

        # Should raise IntegrityError due to unique constraint violation
        with pytest.raises(IntegrityError) as exc_info:
            await db_session_real.commit()

        # Verify it's a unique constraint violation
        error_msg = str(exc_info.value).lower()
        assert "unique" in error_msg or "duplicate" in error_msg

        await db_session_real.rollback()

    @pytest.mark.asyncio
    async def test_name_not_null_constraint(self, db_session_real: AsyncSession):
        """
        DB-TC-004 (Part 1): Verify name NOT NULL constraint.

        Tests that creating an assistant with NULL name fails.
        """
        # Create a test user
        user_data = UserCreate(
            clerk_user_id=f"test_name_null_{uuid.uuid4()}",
            email=f"test_name_null_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user = await create_user(db_session_real, user_data)

        # Try to create assistant with NULL name using raw SQL
        # (SQLAlchemy model validation would prevent this, so we use raw SQL)
        with pytest.raises(IntegrityError) as exc_info:
            await db_session_real.execute(
                text("""
                    INSERT INTO assistants (id, user_id, name, backstory)
                    VALUES (:id, :user_id, NULL, :backstory)
                """),
                {
                    "id": str(uuid.uuid4()),
                    "user_id": str(user.id),
                    "backstory": "Test backstory",
                },
            )
            await db_session_real.commit()

        # Verify it's a NOT NULL violation
        error_msg = str(exc_info.value).lower()
        assert "not null" in error_msg or "null value" in error_msg

        await db_session_real.rollback()

    @pytest.mark.asyncio
    async def test_backstory_not_null_constraint(self, db_session_real: AsyncSession):
        """
        DB-TC-004 (Part 2): Verify backstory NOT NULL constraint.

        Tests that creating an assistant with NULL backstory fails.
        """
        # Create a test user
        user_data = UserCreate(
            clerk_user_id=f"test_backstory_null_{uuid.uuid4()}",
            email=f"test_backstory_null_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user = await create_user(db_session_real, user_data)

        # Try to create assistant with NULL backstory using raw SQL
        with pytest.raises(IntegrityError) as exc_info:
            await db_session_real.execute(
                text("""
                    INSERT INTO assistants (id, user_id, name, backstory)
                    VALUES (:id, :user_id, :name, NULL)
                """),
                {
                    "id": str(uuid.uuid4()),
                    "user_id": str(user.id),
                    "name": "Test Assistant",
                },
            )
            await db_session_real.commit()

        # Verify it's a NOT NULL violation
        error_msg = str(exc_info.value).lower()
        assert "not null" in error_msg or "null value" in error_msg

        await db_session_real.rollback()

    @pytest.mark.asyncio
    async def test_default_timestamps(self, db_session_real: AsyncSession):
        """
        DB-TC-005: Verify default values for created_at, updated_at.

        Tests that timestamps are automatically populated and updated correctly.
        """
        # Create a test user
        user_data = UserCreate(
            clerk_user_id=f"test_timestamps_{uuid.uuid4()}",
            email=f"test_timestamps_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user = await create_user(db_session_real, user_data)

        # Record time before creation
        from datetime import timezone

        before_creation = datetime.now(timezone.utc)

        # Create assistant without specifying timestamps
        assistant = Assistant(
            user_id=user.id,
            name="Timestamp Test Assistant",
            backstory="Testing timestamp defaults",
        )
        db_session_real.add(assistant)
        await db_session_real.commit()
        await db_session_real.refresh(assistant)

        # Record time after creation
        after_creation = datetime.now(timezone.utc)

        # Verify created_at and updated_at are populated
        assert assistant.created_at is not None, (
            "created_at should be automatically populated"
        )
        assert assistant.updated_at is not None, (
            "updated_at should be automatically populated"
        )

        # Verify timestamps are reasonable (within our test window)
        # Convert to timezone-aware if needed for comparison
        if assistant.created_at.tzinfo is None:
            created_at_aware = assistant.created_at.replace(tzinfo=timezone.utc)
        else:
            created_at_aware = assistant.created_at

        if assistant.updated_at.tzinfo is None:
            updated_at_aware = assistant.updated_at.replace(tzinfo=timezone.utc)
        else:
            updated_at_aware = assistant.updated_at

        assert before_creation <= created_at_aware <= after_creation, (
            "created_at should be between before and after creation time"
        )
        assert before_creation <= updated_at_aware <= after_creation, (
            "updated_at should be between before and after creation time"
        )

        # Verify created_at and updated_at are very close (within 1 second)
        # They might differ by microseconds due to database precision
        time_diff = abs((assistant.created_at - assistant.updated_at).total_seconds())
        assert time_diff < 1.0, (
            "created_at and updated_at should be very close initially"
        )

        # Update the assistant and verify updated_at changes
        original_created_at = assistant.created_at
        original_updated_at = assistant.updated_at

        # Small delay to ensure timestamp difference
        import asyncio

        await asyncio.sleep(0.01)

        assistant.name = "Updated Name"
        await db_session_real.commit()
        await db_session_real.refresh(assistant)

        # Verify created_at stays the same but updated_at changes
        assert assistant.created_at == original_created_at, (
            "created_at should not change on update"
        )
        assert assistant.updated_at > original_updated_at, (
            "updated_at should be updated on modification"
        )
