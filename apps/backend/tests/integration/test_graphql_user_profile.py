"""
Tests for GraphQL user profile operations.

This module contains tests for the GraphQL user profile operations, including:
- Querying the current user's profile (me query)
- Updating the current user's profile (updateMyProfile mutation)

Note on testing approach:
- For tests that interact with the GraphQL API through HTTP, we use the test_client fixture
  and mock the auth middleware to simulate authenticated/unauthenticated requests.
- For tests that previously caused asyncio event loop issues, we use a direct testing approach
  where we call the resolvers directly with mocked contexts and dependencies.
  This avoids the event loop conflicts that occur when mixing asyncio operations across
  different components (GraphQL execution, database access, etc.).
"""

import uuid
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from fastapi.security.http import HTTPAuthorizationCredentials
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

# Imports for CLI Token testing
from a2a_platform.api.graphql.resolvers.user_profile_resolvers import (
    CLI_TOKEN_PREFIX,  # For assertion
)
from a2a_platform.api.graphql.resolvers.user_profile_resolvers import (
    resolve_create_cli_token,
)
from a2a_platform.api.graphql.schemas.user_profile_schemas import (
    C<PERSON>Token,
    CreateCliTokenInput,
    CreateCliTokenPayload,
)
from a2a_platform.db.models.cli_token import CliToken as CliTokenModel
from a2a_platform.db.models.user import User
from a2a_platform.schemas.user import UserCreate
from a2a_platform.services.user_service import create_user


@pytest.fixture
async def test_user(db_session_real: AsyncSession) -> User:
    """Create a test user in the database for use in a single test context."""
    clerk_user_id = f"test_clerk_user_{uuid.uuid4()}"
    email = f"test_{uuid.uuid4()}@example.com"
    user_data = UserCreate(
        clerk_user_id=clerk_user_id,
        email=email,
        timezone="UTC",
    )
    user = await create_user(db_session_real, user_data)
    # Do not commit here; let db_session_real manage the transaction for the test.
    # The user will be available within the test's transaction and rolled back afterwards.
    return user


@pytest.mark.asyncio
async def test_update_my_profile_authenticated_direct():
    """
    Test the updateMyProfile mutation directly by mocking the resolver and context.
    This approach avoids asyncio event loop issues by not using the actual GraphQL execution.
    """
    import uuid
    from datetime import UTC, datetime

    from a2a_platform.api.graphql.resolvers.user_profile_resolvers import (
        resolve_update_my_profile,
    )
    from a2a_platform.db.models.user import User as UserModel
    from a2a_platform.schemas.user import User as UserSchema

    # Create a mock user
    user_id = uuid.uuid4()
    clerk_user_id = f"test_clerk_user_{uuid.uuid4()}"
    email = f"test_{uuid.uuid4()}@example.com"

    # We don't need to create a mock database user since we're mocking the service

    # Create a mock updated user to be returned by the service
    mock_updated_user = UserModel(
        id=user_id,
        clerk_user_id=clerk_user_id,
        email=email,
        timezone="America/New_York",  # Updated timezone
        preferences={},
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    # Create a mock GraphQL info object with context
    mock_info = MagicMock()
    mock_context = MagicMock()
    mock_context.clerk_user_id = clerk_user_id
    mock_context.db_session = MagicMock()
    mock_info.context = mock_context

    # Mock the user service functions
    with patch(
        "a2a_platform.api.graphql.resolvers.user_profile_resolvers.update_user_profile",
        new_callable=AsyncMock,
        return_value=mock_updated_user,
    ):
        # Call the resolver directly
        result = await resolve_update_my_profile(mock_info, "America/New_York")

        # Verify the result
        assert isinstance(result, UserSchema)
        assert result.clerk_user_id == clerk_user_id
        assert result.timezone == "America/New_York"


@pytest.mark.asyncio
async def test_update_user_profile_preferences_authenticated_direct():
    """
    Test the updateUserProfilePreferences mutation directly by mocking the resolver and context.
    This approach avoids asyncio event loop issues by not using the actual GraphQL execution.
    """
    import uuid
    from datetime import UTC, datetime

    from a2a_platform.api.graphql.resolvers.user_profile_resolvers import (
        resolve_update_user_profile_preferences,
    )
    from a2a_platform.api.graphql.schemas.user_profile_schemas import (
        UserProfilePreferencesInput,
    )
    from a2a_platform.db.models.user import User as UserModel
    from a2a_platform.schemas.user import User as UserSchema

    # Create a mock user
    user_id = uuid.uuid4()
    clerk_user_id = f"test_clerk_user_{uuid.uuid4()}"
    email = f"test_{uuid.uuid4()}@example.com"

    # Create preferences to update
    preferences_update = {"theme": "dark", "notifications": {"email": True}}
    timezone_update = "Europe/London"

    # Create a mock updated user to be returned by the service
    mock_updated_user = UserModel(
        id=user_id,
        clerk_user_id=clerk_user_id,
        email=email,
        timezone=timezone_update,
        preferences=preferences_update,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    # Create a mock GraphQL info object with context
    mock_info = MagicMock()
    mock_context = MagicMock()
    mock_context.clerk_user_id = clerk_user_id
    mock_context.db_session = MagicMock()
    mock_info.context = mock_context

    # Create input object
    input_obj = UserProfilePreferencesInput(
        preferences=preferences_update, timezone=timezone_update
    )

    # Mock the user service functions
    with patch(
        "a2a_platform.api.graphql.resolvers.user_profile_resolvers.update_user_preferences",
        new_callable=AsyncMock,
        return_value=mock_updated_user,
    ):
        # Call the resolver directly
        result = await resolve_update_user_profile_preferences(mock_info, input_obj)

        # Verify the result
        assert isinstance(result, UserSchema)
        assert result.clerk_user_id == clerk_user_id
        assert result.timezone == timezone_update

        # Check preferences
        # The preferences() method should always return a dict
        preferences_dict = result.preferences()
        assert preferences_dict["theme"] == "dark"
        assert preferences_dict["notifications"]["email"] is True


@pytest.mark.asyncio
async def test_update_my_profile_unauthenticated(test_client: AsyncClient):
    """Test that an unauthenticated user cannot update a profile"""
    # Define the GraphQL mutation
    mutation = """
    mutation UpdateMyProfile($timezone: String!) {
        updateMyProfile(timezone: $timezone) {
            id
            clerkUserId
            email
            timezone
        }
    }
    """

    # Variables for the mutation
    variables = {"timezone": "America/New_York"}

    # Create a mock for the ClerkAuthMiddleware.__call__ method that returns None
    mock_auth = AsyncMock(return_value=None)

    # Patch the method
    with patch("a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__", mock_auth):
        # Send the mutation without authentication
        response = await test_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers={"Content-Type": "application/json"},
        )

    # Verify the response
    assert response.status_code == 200
    response_json = response.json()

    # Verify there are errors
    assert "errors" in response_json
    assert len(response_json["errors"]) > 0
    assert "Authentication required" in response_json["errors"][0]["message"]

    # Verify no data was returned
    assert (
        response_json.get("data") is None
        or response_json.get("data", {}).get("updateMyProfile") is None
    )


@pytest.mark.asyncio
async def test_me_query_authenticated_direct():
    """
    Test the me query resolver directly by mocking the context and database.
    This approach avoids asyncio event loop issues by not using the actual GraphQL execution.
    """
    import uuid
    from datetime import UTC, datetime

    from a2a_platform.api.graphql.resolvers.user_profile_resolvers import resolve_me
    from a2a_platform.db.models.user import User as UserModel
    from a2a_platform.schemas.user import User as UserSchema

    # Create a mock user
    user_id = uuid.uuid4()
    clerk_user_id = f"test_clerk_user_{uuid.uuid4()}"
    email = f"test_{uuid.uuid4()}@example.com"

    # Create a mock user to be returned by the service
    mock_db_user = UserModel(
        id=user_id,
        clerk_user_id=clerk_user_id,
        email=email,
        timezone="UTC",
        preferences={},
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    # Create a mock GraphQL info object with context
    mock_info = MagicMock()
    mock_context = MagicMock()
    mock_context.clerk_user_id = clerk_user_id
    mock_context.db_session = MagicMock()
    mock_info.context = mock_context

    # Mock the user service function
    with patch(
        "a2a_platform.api.graphql.resolvers.user_profile_resolvers.get_user_by_clerk_id",
        new_callable=AsyncMock,
        return_value=mock_db_user,
    ):
        # Call the resolver directly
        result = await resolve_me(mock_info)

        # Verify the result
        assert isinstance(result, UserSchema)
        assert result.id == user_id
        assert result.clerk_user_id == clerk_user_id
        assert result.email == email
        assert result.timezone == "UTC"


@pytest.mark.asyncio
async def test_me_query_unauthenticated(test_client: AsyncClient):
    """Test that an unauthenticated user cannot retrieve a profile with the me query"""
    # Define the GraphQL query
    query = """
    query GetMyProfile {
        me {
            id
            clerkUserId
            email
            timezone
            preferences
            createdAt
            updatedAt
        }
    }
    """

    # Create a mock for the ClerkAuthMiddleware.__call__ method that returns None
    mock_auth = AsyncMock(return_value=None)

    # Patch the method
    with patch("a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__", mock_auth):
        # Send the query without authentication
        response = await test_client.post(
            "/graphql",
            json={"query": query},
            headers={"Content-Type": "application/json"},
        )

    # Verify the response
    assert response.status_code == 200
    response_json = response.json()

    # Verify there are errors
    assert "errors" in response_json
    assert len(response_json["errors"]) > 0
    assert "Authentication required" in response_json["errors"][0]["message"]

    # Verify no data was returned
    assert (
        response_json.get("data") is None
        or response_json.get("data", {}).get("me") is None
    )


@pytest.mark.asyncio
async def test_create_cli_token_authenticated_direct():
    """
    Test the create_cli_token resolver directly by mocking context and dependencies.
    """
    from datetime import UTC, datetime

    from a2a_platform.db.models.user import User as UserModel

    # Mock user details
    user_id = uuid.uuid4()
    clerk_user_id = f"test_clerk_user_{uuid.uuid4()}"
    email = f"test_{uuid.uuid4()}@example.com"

    mock_db_user = UserModel(
        id=user_id,
        clerk_user_id=clerk_user_id,
        email=email,
        timezone="UTC",
        preferences={},
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    # Create a mock GraphQL info object with context
    mock_info = MagicMock()
    mock_context = MagicMock()
    mock_context.clerk_user_id = clerk_user_id
    mock_context.db_session = AsyncMock(
        spec=AsyncSession
    )  # Use AsyncMock for db_session
    mock_info.context = mock_context

    # Input for the mutation
    token_description = "Test CLI Token"
    mutation_input = CreateCliTokenInput(description=token_description)

    # Mock the user service function and CliTokenModel to capture arguments
    with (
        patch(
            "a2a_platform.api.graphql.resolvers.user_profile_resolvers.get_user_by_clerk_id",
            new_callable=AsyncMock,
            return_value=mock_db_user,
        ) as mock_get_user,
        patch(
            "a2a_platform.api.graphql.resolvers.user_profile_resolvers.CliTokenModel",
            autospec=True,  # Use autospec to ensure instance methods are MagicMock
        ) as MockCliTokenDbModel,
    ):
        # Configure the mock instance that CliTokenModel() will return
        mock_db_token_instance = MockCliTokenDbModel.return_value
        mock_db_token_instance.id = uuid.uuid4()
        mock_db_token_instance.token_prefix = CLI_TOKEN_PREFIX
        mock_db_token_instance.description = token_description
        mock_db_token_instance.user_id = mock_db_user.id
        mock_db_token_instance.created_at = datetime.now(UTC)
        mock_db_token_instance.expires_at = None
        mock_db_token_instance.last_used_at = None

        # Call the resolver directly
        result = await resolve_create_cli_token(mock_info, mutation_input)

        # --- Assertions ---
        assert isinstance(result, CreateCliTokenPayload)

        # 1. Assert token format and content
        assert result.token.startswith(CLI_TOKEN_PREFIX)
        assert len(result.token) > len(CLI_TOKEN_PREFIX)

        # 2. Verify the token format: prefix_userId_secret
        token_parts = result.token.split("_")
        assert len(token_parts) >= 3  # At least 3 parts (prefix, userId, secret)

        # Extract user ID from token (simplified approach)
        user_id_str = str(user_id).replace("-", "")[
            :8
        ]  # Same format as used in resolver
        assert user_id_str in result.token

        # 3. Assert CliToken content
        assert isinstance(result.cliToken, CliToken)
        assert result.cliToken.tokenPrefix == CLI_TOKEN_PREFIX
        assert result.cliToken.description == token_description
        assert str(result.cliToken.userId) == str(user_id)  # Ensure it's string ID

        # 4. Assert service calls
        mock_get_user.assert_awaited_once_with(mock_context.db_session, clerk_user_id)

        # 5. Assert database interactions
        MockCliTokenDbModel.assert_called_once()
        db_token_args = MockCliTokenDbModel.call_args[1]  # Get kwargs

        assert db_token_args["user_id"] == user_id
        assert db_token_args["token_prefix"] == CLI_TOKEN_PREFIX
        assert db_token_args["description"] == token_description
        assert "hashed_token" in db_token_args
        assert "salt_hex" in db_token_args

        # 6. Assert the new simplified approach
        assert db_token_args["salt_hex"] == user_id_str  # User ID used as salt
        # Note: We don't need to verify hashing since we store the token directly now

        mock_context.db_session.add.assert_called_once()
        mock_context.db_session.commit.assert_awaited_once()
        # refresh is called with the instance created by MockCliTokenDbModel
        mock_context.db_session.refresh.assert_awaited_once_with(
            MockCliTokenDbModel.return_value
        )


@pytest.mark.asyncio
async def test_create_cli_token_unauthenticated_http(test_client: AsyncClient):
    """Test that an unauthenticated user cannot create a CLI token via HTTP."""
    mutation = """
        mutation CreateCliToken($description: String) {
            createCliToken(input: { description: $description }) {
                token
                cliToken {
                    id
                    tokenPrefix
                    description
                }
            }
        }
    """
    variables = {"description": "Unauthenticated Test Token"}

    # Mock the auth middleware to simulate no user being authenticated
    with patch(
        "a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__",
        new_callable=AsyncMock,
        return_value=None,
    ):
        response = await test_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers={"Content-Type": "application/json"},
        )

    assert response.status_code == 200
    response_json = response.json()

    assert "errors" in response_json
    assert len(response_json["errors"]) > 0
    assert "Authentication required" in response_json["errors"][0]["message"]
    assert (
        response_json.get("data") is None
        or response_json.get("data", {}).get("createCliToken") is None
    )


@pytest.mark.asyncio
async def test_create_cli_token_authenticated_http(
    test_client: AsyncClient,
    test_user: User,
    db_session_real: AsyncSession,
):
    """Test creating a CLI token for an authenticated user via HTTP and verify DB state."""
    mutation = """
        mutation CreateCliToken($description: String) {
            createCliToken(input: { description: $description }) {
                token # Raw token to be returned once
                cliToken {
                    id
                    tokenPrefix
                    description
                    createdAt
                    userId
                }
            }
        }
    """
    token_description = "Authenticated HTTP Test Token"
    variables = {"description": token_description}

    # To mock authentication for this HTTP request, we need to patch the
    # ClerkAuthMiddleware's __call__ method, which is used by the AuthMiddleware.
    # This method is expected to return HTTPAuthorizationCredentials if auth is successful.
    mock_auth_credentials = HTTPAuthorizationCredentials(
        scheme="Bearer", credentials=test_user.clerk_user_id
    )

    with patch(
        "a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__",
        new_callable=AsyncMock,
        return_value=mock_auth_credentials,
    ):
        response = await test_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers={
                "Content-Type": "application/json"
            },  # No auth header needed as we mock context
        )

    assert response.status_code == 200
    response_json = response.json()

    assert "errors" not in response_json, (
        f"GraphQL errors: {response_json.get('errors')}"
    )
    data = response_json.get("data", {}).get("createCliToken")
    assert data is not None, "createCliToken data is missing"

    # 1. Assert returned token and CliToken details
    assert data["token"].startswith(CLI_TOKEN_PREFIX)

    # Verify token format: should have user ID embedded
    user_id_str = str(test_user.id).replace("-", "")[:8]
    assert user_id_str in data["token"]

    # Split token to get components
    token_parts = data["token"].split("_")
    assert len(token_parts) >= 3  # Should have at least prefix, user ID, and secret

    returned_cli_token = data["cliToken"]
    assert returned_cli_token["id"] is not None
    assert returned_cli_token["tokenPrefix"] == CLI_TOKEN_PREFIX
    assert returned_cli_token["description"] == token_description
    assert returned_cli_token["userId"] == str(test_user.id)

    # 2. Verify database state
    # Fetch the token from the DB using the ID returned by the mutation
    from sqlalchemy import select

    stmt = select(CliTokenModel).where(
        CliTokenModel.id == uuid.UUID(returned_cli_token["id"])
    )
    result = await db_session_real.execute(stmt)
    db_cli_token = result.scalar_one_or_none()

    assert db_cli_token is not None, "CLI token not found in database"
    assert db_cli_token.user_id == test_user.id
    assert db_cli_token.token_prefix == CLI_TOKEN_PREFIX
    assert db_cli_token.description == token_description
    assert db_cli_token.hashed_token is not None
    assert db_cli_token.salt_hex is not None

    # 3. Verify new simplified approach
    # Extract token secret from full token
    token_secret = token_parts[-1]  # The last part is the secret

    # With our simplified approach:
    # - salt_hex should be the user ID string
    assert db_cli_token.salt_hex == user_id_str

    # - hashed_token should be the token secret directly
    assert db_cli_token.hashed_token == token_secret
