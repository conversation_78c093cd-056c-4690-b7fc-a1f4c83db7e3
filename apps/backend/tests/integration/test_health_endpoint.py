"""
Integration tests for the health endpoint.

This module tests the health check endpoints to ensure they work correctly
for Cloud Run startup probes and load balancer health checks.
"""

import pytest
from fastapi.testclient import TestClient

from a2a_platform.main import app


@pytest.fixture
def client():
    """Create a test client for the FastAPI app."""
    return TestClient(app)


def test_health_endpoint(client):
    """Test the main health endpoint."""
    response = client.get("/api/health")

    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "OK"
    assert data["service"] == "API"


def test_readiness_endpoint(client):
    """Test the readiness check endpoint."""
    response = client.get("/api/health/ready")

    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "OK"
    assert data["service"] == "API"


def test_liveness_endpoint(client):
    """Test the liveness check endpoint."""
    response = client.get("/api/health/live")

    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "OK"
    assert data["service"] == "API"


def test_health_endpoint_content_type(client):
    """Test that health endpoint returns correct content type."""
    response = client.get("/api/health")

    assert response.status_code == 200
    assert response.headers["content-type"].startswith("application/json")


def test_api_structure(client):
    """Test that the API structure is correctly set up."""
    # Test that the API prefix is working
    response = client.get("/api/health")
    assert response.status_code == 200

    # Test that health endpoints are properly mounted
    response = client.get("/api/health/ready")
    assert response.status_code == 200

    response = client.get("/api/health/live")
    assert response.status_code == 200
