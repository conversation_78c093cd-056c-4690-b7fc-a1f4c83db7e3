"""
Implementation of missing test case TC_US5.5_034: Escalation.

This test verifies that a task can be escalated when it has critical failures.
"""

import uuid

import pytest
from httpx import AsyncClient
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.task import Task
from a2a_platform.schemas.task_schemas import TaskCreate, TaskUpdate


@pytest.mark.asyncio
async def test_task_escalation(
    test_client: AsyncClient,
    test_assistant_id: uuid.UUID,
    db_session_real: AsyncSession,
):
    """
    TC_US5.5_034: Test task escalation.

    Setup: Create a task. Simulate a condition for escalation (e.g., critical failure).
    Action: PUT /internal/tasks/{task_id} with status: 'escalated'.
    Expected: 200 OK. Task status is 'escalated'. (AC14)
    """
    # Arrange - Create a task
    task_data = TaskCreate(description="Task that will be escalated").model_dump()
    response = await test_client.post(
        "/api/internal/assistants/my/tasks",
        json=task_data,
        headers={"X-Assistant-ID": str(test_assistant_id)},
    )

    assert response.status_code == 201
    task = response.json()
    task_id = task["id"]
    initial_updated_at = task["updated_at"]

    # Simulate a critical failure by adding metadata about the failure
    failure_metadata = {
        "critical_failure": True,
        "failure_reason": "Complex error requiring human intervention",
        "failed_attempts": 3,
        "error_logs": "Detailed error information for review",
    }

    # Act - Set task status to escalated
    update_data = TaskUpdate(status="escalated", metadata=failure_metadata).model_dump()

    update_response = await test_client.put(
        f"/api/internal/tasks/{task_id}", json=update_data
    )

    # Assert
    assert update_response.status_code == 200
    updated_task = update_response.json()
    assert updated_task["status"] == "escalated"
    assert (
        updated_task["updated_at"] != initial_updated_at
    )  # updated_at should be changed
    assert "critical_failure" in updated_task["metadata"]
    assert (
        updated_task["metadata"]["failure_reason"]
        == "Complex error requiring human intervention"
    )

    # Verify in database
    query = select(Task).where(Task.id == uuid.UUID(task_id))
    result = await db_session_real.execute(query)
    db_task = result.scalar_one_or_none()

    assert db_task is not None
    assert db_task.status == "escalated"
    assert "critical_failure" in db_task.metadata_json
    assert (
        db_task.metadata_json["failure_reason"]
        == "Complex error requiring human intervention"
    )
