"""
Integration tests for conversation GraphQL queries.

These tests verify the GraphQL query for getting or creating conversations
between users and their assistants.
"""

import uuid
from unittest.mock import patch

import pytest
from fastapi.security.http import HTTPAuthorizationCredentials
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.user import User
from a2a_platform.db.models.conversation import Conversation


@pytest.fixture
def mock_clerk_auth(test_user: User):
    """
    Mock the Clerk authentication middleware to accept test user.
    This fixture patches the ClerkAuthMiddleware.__call__ method to return
    the test user's clerk_user_id from the Authorization header.
    """

    async def mock_clerk_auth_call(_, request):
        # Extract the token from the Authorization header
        auth_header = request.headers.get("Authorization", "")
        if auth_header.startswith("Bearer "):
            # Return the test user's clerk_user_id
            return HTTPAuthorizationCredentials(
                scheme="Bearer", credentials=test_user.clerk_user_id
            )
        return None

    with patch(
        "a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__",
        new=mock_clerk_auth_call,
    ) as mock:
        yield mock


@pytest.fixture
async def test_user_no_assistant(
    db_session_real: AsyncSession,
) -> User:
    """Create a test user without an assistant for testing error scenarios."""
    from a2a_platform.schemas.user import UserCreate
    from a2a_platform.services.user_service import create_user

    unique_id = uuid.uuid4()
    user_data = UserCreate(
        clerk_user_id=f"clerk_user_no_assistant_{unique_id}",
        email=f"no-assistant-{unique_id}@example.com",
        timezone="UTC",
    )
    user = await create_user(db_session_real, user_data)
    return user


@pytest.fixture
def mock_clerk_jwt_valid_no_assistant() -> str:
    """Provide a mock valid JWT token for user without assistant."""
    return "mock_jwt_token_valid_no_assistant"


@pytest.fixture
def mock_clerk_auth_no_assistant(test_user_no_assistant: User):
    """
    Mock the Clerk authentication middleware for user without assistant.
    """

    async def mock_clerk_auth_call(_, request):
        # Extract the token from the Authorization header
        auth_header = request.headers.get("Authorization", "")
        if auth_header.startswith("Bearer "):
            # Return the test user's clerk_user_id
            return HTTPAuthorizationCredentials(
                scheme="Bearer",
                credentials=test_user_no_assistant.clerk_user_id,
            )
        return None

    with patch(
        "a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__",
        new=mock_clerk_auth_call,
    ) as mock:
        yield mock


class TestGetOrCreateConversationQuery:
    """Test the getOrCreateConversation GraphQL query."""

    @pytest.mark.asyncio
    async def test_get_or_create_conversation_success(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """Test successful conversation creation with valid user and assistant."""
        query = """
        query GetOrCreateConversation {
            getOrCreateConversation {
                id
                userId
                assistantId
                createdAt
                lastMessageAt
            }
        }
        """

        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": query},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        data = response.json()

        # Should not have GraphQL errors
        assert "errors" not in data or not data["errors"]

        # Verify the conversation data
        conversation_data = data["data"]["getOrCreateConversation"]
        assert conversation_data is not None
        assert "id" in conversation_data
        assert conversation_data["userId"] == str(test_user.id)
        assert conversation_data["assistantId"] == str(test_assistant_id)
        assert "createdAt" in conversation_data

    @pytest.mark.asyncio
    async def test_get_or_create_conversation_existing(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """Test that existing conversation is returned instead of new one."""
        # Arrange - Create an existing conversation
        existing_conversation = Conversation(
            user_id=test_user.id,
            assistant_id=test_assistant_id,
        )
        db_session_real.add(existing_conversation)
        await db_session_real.commit()
        await db_session_real.refresh(existing_conversation)

        query = """
        query GetOrCreateConversation {
            getOrCreateConversation {
                id
                userId
                assistantId
                createdAt
                lastMessageAt
            }
        }
        """

        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": query},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        data = response.json()

        # Should not have GraphQL errors
        assert "errors" not in data or not data["errors"]

        # Verify the same conversation is returned
        conversation_data = data["data"]["getOrCreateConversation"]
        assert conversation_data["id"] == str(existing_conversation.id)
        assert conversation_data["userId"] == str(test_user.id)
        assert conversation_data["assistantId"] == str(test_assistant_id)

    @pytest.mark.asyncio
    async def test_get_or_create_conversation_unauthenticated(
        self,
        async_client: AsyncClient,
    ):
        """Test that unauthenticated request is rejected."""
        query = """
        query GetOrCreateConversation {
            getOrCreateConversation {
                id
                userId
                assistantId
                createdAt
                lastMessageAt
            }
        }
        """

        # Act - No authorization header
        response = await async_client.post(
            "/graphql",
            json={"query": query},
        )

        # Assert
        assert response.status_code == 200
        data = response.json()

        # Should have GraphQL errors for authentication
        assert "errors" in data
        assert len(data["errors"]) > 0

    @pytest.mark.asyncio
    async def test_get_or_create_conversation_no_assistant(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user_no_assistant: User,
        mock_clerk_jwt_valid_no_assistant: str,
        mock_clerk_auth_no_assistant,
    ):
        """Test that request fails when user has no assistant."""
        query = """
        query GetOrCreateConversation {
            getOrCreateConversation {
                id
                userId
                assistantId
                createdAt
                lastMessageAt
            }
        }
        """

        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid_no_assistant}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": query},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        data = response.json()

        # Should have GraphQL errors for missing assistant
        assert "errors" in data
        assert len(data["errors"]) > 0
        error_message = data["errors"][0]["message"]
        # Should get a specific error about missing assistant
        assert (
            "personal assistant" in error_message.lower()
            or "unable to access conversation" in error_message.lower()
        )
