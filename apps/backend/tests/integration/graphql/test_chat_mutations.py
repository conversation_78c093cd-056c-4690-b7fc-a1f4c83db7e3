"""
Integration tests for sendMessage GraphQL mutation.

Tests the complete sendMessage mutation flow including:
- Authentication and authorization
- Input validation
- Message persistence
- Error handling
- GraphQL schema compliance
- Performance benchmarks
- Security validations

AI Response Service Mocking:
This module provides several fixtures for mocking the PydanticAIResponseService.generate_response method:

1. mock_ai_response_service: Default mock returning successful AI response
2. mock_ai_response_service_fallback: Mock returning fallback responses (AI disabled/failed)
3. mock_ai_response_service_error: Mock that raises exceptions for error testing

Usage Examples:

# Basic usage with default mock
def test_example(mock_ai_response_service):
    # Mock returns ("This is a test AI response.", True) by default
    pass

# Custom response configuration
def test_custom_response(mock_ai_response_service):
    mock_ai_response_service.return_value = ("Custom response", True)
    # Your test code here

# Inline mock with specific configuration
def test_inline_mock():
    service_path = "a2a_platform.services.pydantic_ai_response_service.PydanticAIResponseService.generate_response"
    with patch(service_path) as mock:
        mock.return_value = ("Specific response", False)
        # Your test code here

        # Verify mock was called
        assert mock.called

        # Verify call arguments
        call_args = mock.call_args
        if call_args:
            args, kwargs = call_args
            assistant = args[0]  # Assistant object
            user_message = args[1]  # str
            conversation_history = args[2]  # List[ChatMessage]
            # force_fallback might be in kwargs
"""

import uuid
from unittest.mock import patch

import pytest
from fastapi.security.http import HTTPAuthorizationCredentials
from httpx import AsyncClient
from sqlalchemy import delete, select
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.chat_message import ChatMessage
from a2a_platform.db.models.conversation import Conversation
from a2a_platform.db.models.user import User


async def get_or_create_conversation(
    db_session: AsyncSession, user_id: uuid.UUID, assistant_id: uuid.UUID
) -> Conversation:
    """
    Get an existing conversation or create a new one if none exists.

    This handles the unique constraint on (user_id, assistant_id) by checking
    for existing conversations before creating new ones.
    """
    result = await db_session.execute(
        select(Conversation).where(
            Conversation.user_id == user_id, Conversation.assistant_id == assistant_id
        )
    )
    conversation = result.scalar_one_or_none()

    if conversation is None:
        conversation = Conversation(
            user_id=user_id,
            assistant_id=assistant_id,
        )
        db_session.add(conversation)
        await db_session.commit()
        await db_session.refresh(conversation)

    return conversation


async def clear_conversation_messages(
    db_session: AsyncSession, conversation_id: uuid.UUID
) -> None:
    """
    Clear all messages from a conversation for test isolation.

    This is useful for tests that expect a clean conversation state.
    """
    await db_session.execute(
        delete(ChatMessage).where(ChatMessage.conversation_id == conversation_id)
    )
    await db_session.commit()


@pytest.fixture
def mock_clerk_auth(test_user: User):
    """
    Mock the Clerk authentication middleware to accept test user.
    This fixture patches the ClerkAuthMiddleware.__call__ method to return
    the test user's clerk_user_id from the Authorization header.
    """

    async def mock_clerk_auth_call(_, request):
        # Extract the token from the Authorization header
        auth_header = request.headers.get("Authorization", "")
        if auth_header.startswith("Bearer "):
            # Return the test user's clerk_user_id
            return HTTPAuthorizationCredentials(
                scheme="Bearer", credentials=test_user.clerk_user_id
            )
        return None

    with patch(
        "a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__",
        new=mock_clerk_auth_call,
    ) as mock:
        yield mock


# AI Response Service Mock Fixtures
# These fixtures provide comprehensive mocking for the PydanticAIResponseService.generate_response method
# to enable thorough testing of AI integration without external API calls.


@pytest.fixture
def mock_ai_response_service():
    """
    Default AI response mock fixture returning successful AI response.

    Returns:
        Mock: Configured mock that returns ("This is a test AI response.", True)

    Usage:
        def test_example(mock_ai_response_service):
            # Mock automatically returns default response
            # Customize with: mock_ai_response_service.return_value = ("Custom", True)
    """
    service_path = (
        "a2a_platform.services.pydantic_ai_response_service."
        "PydanticAIResponseService.generate_response"
    )
    with patch(service_path) as mock:
        mock.return_value = ("This is a test AI response.", True)
        yield mock


@pytest.fixture
def mock_ai_response_service_fallback():
    """
    AI response mock fixture returning fallback response (AI disabled/failed).

    Returns:
        Mock: Configured mock that returns fallback response with is_ai_generated=False

    Usage:
        def test_fallback_scenario(mock_ai_response_service_fallback):
            # Mock simulates AI service failure or disabled state
            # Tests should verify fallback message handling
    """
    service_path = (
        "a2a_platform.services.pydantic_ai_response_service."
        "PydanticAIResponseService.generate_response"
    )
    with patch(service_path) as mock:
        mock.return_value = (
            "Sorry, I'm currently unable to provide an AI response.",
            False,
        )
        yield mock


@pytest.fixture
def mock_ai_response_service_error():
    """
    AI response mock fixture that raises exceptions for error testing.

    Returns:
        Mock: Configured mock that raises Exception("AI service unavailable")

    Usage:
        def test_error_handling(mock_ai_response_service_error):
            # Mock simulates complete AI service failure
            # Tests should verify proper error handling and user feedback
    """
    service_path = (
        "a2a_platform.services.pydantic_ai_response_service."
        "PydanticAIResponseService.generate_response"
    )
    with patch(service_path) as mock:
        mock.side_effect = Exception("AI service unavailable")
        yield mock


@pytest.fixture
def mock_ai_response_service_contextual():
    """
    AI response mock fixture that returns contextual responses based on input.

    Returns:
        Mock: Configured mock that analyzes user_message and returns appropriate responses

    Usage:
        def test_contextual_responses(mock_ai_response_service_contextual):
            # Mock returns different responses based on message content
            # Useful for testing conversation flow and context handling
    """
    service_path = (
        "a2a_platform.services.pydantic_ai_response_service."
        "PydanticAIResponseService.generate_response"
    )

    def contextual_response(
        assistant, user_message, conversation_history, force_fallback=False
    ):
        """Generate contextual responses based on user message content."""
        if force_fallback:
            return ("Fallback response due to force_fallback=True", False)

        message_lower = user_message.lower()
        if "hello" in message_lower or "hi" in message_lower:
            return (f"Hello! I'm {assistant.name}. How can I help you today?", True)
        elif "help" in message_lower:
            return ("I'm here to assist you. What do you need help with?", True)
        elif "goodbye" in message_lower or "bye" in message_lower:
            return ("Goodbye! Feel free to reach out if you need anything else.", True)
        else:
            return (
                f"I understand your message: '{user_message}'. How can I assist you further?",
                True,
            )

    with patch(service_path, side_effect=contextual_response) as mock:
        yield mock


class TestSendMessageMutation:
    """Integration tests for sendMessage GraphQL mutation."""

    @pytest.mark.asyncio
    async def test_send_message_with_ai_response_mock(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
        mock_ai_response_service,
    ):
        """
        Test sendMessage with mocked AI response service.

        This test verifies:
        - User message is stored correctly
        - AI service is called with proper parameters
        - AI response is generated and stored
        - GraphQL response includes both user and AI messages
        """
        # Arrange - Get or create a conversation
        conversation = await get_or_create_conversation(
            db_session_real, test_user.id, test_assistant_id
        )

        # Configure AI mock with custom response for this test
        mock_ai_response_service.return_value = (
            "Hello! This is a test AI response to your message.",
            True,
        )
        mutation = """
        mutation SendMessage($conversationId: ID!, $content: String!) {
            sendMessage(conversationId: $conversationId, content: $content) {
                message {
                    id
                    conversationId
                    senderRole
                    content
                    timestamp
                    metadata
                }
                success
                errorMessage
            }
        }
        """

        variables = {
            "conversationId": str(conversation.id),
            "content": "Hello, this is a test message!",
        }

        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        send_message_data = data["data"]["sendMessage"]
        assert send_message_data["success"] is True
        assert send_message_data["errorMessage"] is None

        message_data = send_message_data["message"]
        assert message_data["conversationId"] == str(conversation.id)
        assert message_data["senderRole"] == "USER"
        assert message_data["content"]["text"] == "Hello, this is a test message!"

        # Wait for background task to complete
        import asyncio

        await asyncio.sleep(0.1)  # Give background task time to execute

        # Verify the AI service was called with correct parameters
        assert mock_ai_response_service.called, "AI service should have been called"

        # Validate AI service call arguments
        call_args = mock_ai_response_service.call_args
        assert call_args is not None, (
            "AI service should have been called with arguments"
        )

        args, kwargs = call_args

        # Arguments can be passed as positional or keyword arguments
        if len(args) >= 3:
            # Positional arguments
            assistant = args[0]
            user_message = args[1]
            conversation_history = args[2]
        else:
            # Keyword arguments
            assert "assistant" in kwargs, "Should have assistant argument"
            assert "user_message" in kwargs, "Should have user_message argument"
            assert "conversation_history" in kwargs, (
                "Should have conversation_history argument"
            )
            assistant = kwargs["assistant"]
            user_message = kwargs["user_message"]
            conversation_history = kwargs["conversation_history"]

        # Verify argument types and content
        assert assistant.id == test_assistant_id, (
            "Should be called with correct assistant"
        )
        assert user_message == "Hello, this is a test message!", (
            "Should pass user message correctly"
        )
        assert isinstance(conversation_history, list), (
            "Conversation history should be a list"
        )

    @pytest.mark.asyncio
    async def test_send_message_with_ai_fallback_mock(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
        mock_ai_response_service_fallback,
    ):
        """
        Test sendMessage with AI service returning fallback response.

        This test verifies:
        - AI service fallback behavior is handled correctly
        - Fallback responses have is_ai_generated=False
        - System gracefully handles AI service limitations
        """
        # Arrange - Get or create a conversation
        conversation = await get_or_create_conversation(
            db_session_real, test_user.id, test_assistant_id
        )
        mutation = """
        mutation SendMessage($conversationId: ID!, $content: String!) {
            sendMessage(conversationId: $conversationId, content: $content) {
                message {
                    id
                    content
                }
                success
                errorMessage
            }
        }
        """

        variables = {
            "conversationId": str(conversation.id),
            "content": "Test message for fallback scenario",
        }

        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        send_message_data = data["data"]["sendMessage"]
        assert send_message_data["success"] is True

        # Wait for background task to complete
        import asyncio

        await asyncio.sleep(0.1)  # Give background task time to execute

        # Verify the AI service was called and returned fallback response
        assert mock_ai_response_service_fallback.called, (
            "AI service should have been called"
        )

        # Verify the mock returned expected fallback response
        call_result = mock_ai_response_service_fallback.return_value
        response_text, is_ai_generated = call_result
        assert is_ai_generated is False, (
            "Fallback responses should have is_ai_generated=False"
        )
        assert "unable to provide an AI response" in response_text, (
            "Should contain fallback message"
        )

    @pytest.mark.asyncio
    async def test_send_message_with_custom_ai_mock_configuration(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """Test sendMessage with custom mock configuration for specific test scenarios."""
        # Arrange - Get or create a conversation
        conversation = await get_or_create_conversation(
            db_session_real, test_user.id, test_assistant_id
        )

        # Mock the PA response service scheduling to prevent background tasks
        # and verify the flow is triggered correctly
        with patch(
            "a2a_platform.services.pa_response_service.PAResponseService.schedule_response_generation"
        ) as mock_schedule:
            mutation = """
            mutation SendMessage($conversationId: ID!, $content: String!) {
                sendMessage(conversationId: $conversationId, content: $content) {
                    message {
                        id
                        content
                    }
                    success
                    errorMessage
                }
            }
            """

            variables = {
                "conversationId": str(conversation.id),
                "content": "Test message with custom mock",
            }

            headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

            # Act
            response = await async_client.post(
                "/graphql",
                json={"query": mutation, "variables": variables},
                headers=headers,
            )

            # Assert
            assert response.status_code == 200
            data = response.json()
            assert "errors" not in data

            send_message_data = data["data"]["sendMessage"]
            assert send_message_data["success"] is True

            # Verify the PA response scheduling was called with expected parameters
            assert mock_schedule.called, (
                "PA response scheduling should have been called"
            )

            # Verify the call arguments (called with keyword arguments)
            call_args = mock_schedule.call_args
            if call_args:
                _args, kwargs = call_args
                # The method is called with keyword arguments
                assert "conversation_id" in kwargs
                assert "user_message" in kwargs
                assert "user_id" in kwargs
                assert kwargs["conversation_id"] == conversation.id
                assert kwargs["user_id"] == test_user.id

    @pytest.mark.asyncio
    async def test_send_message_success(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """Test successful message sending with valid input."""
        # Arrange - Get or create a conversation
        conversation = await get_or_create_conversation(
            db_session_real, test_user.id, test_assistant_id
        )

        mutation = """
        mutation SendMessage($conversationId: ID!, $content: String!) {
            sendMessage(conversationId: $conversationId, content: $content) {
                message {
                    id
                    conversationId
                    senderRole
                    content
                    timestamp
                    metadata
                }
                success
                errorMessage
            }
        }
        """

        test_messages = [
            "Hello, this is a test message!",  # Basic message
            "Special chars: @#$%^&*()_+-=[]{}|;':\",./<>?",  # Special characters
            "Emoji test: 🚀👍😊",  # Emojis
            "Unicode: 测试 русский العربية",  # Unicode characters
            "a" * 4000,  # Max length message
        ]

        for message_content in test_messages:
            variables = {
                "conversationId": str(conversation.id),
                "content": message_content,
            }

            headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

            # Act - Time the request for performance testing
            import time

            start_time = time.time()
            response = await async_client.post(
                "/graphql",
                json={"query": mutation, "variables": variables},
                headers=headers,
            )
            end_time = time.time()
            response_time_ms = (end_time - start_time) * 1000

            # Assert response time is within acceptable limits (relaxed in CI)
            import os

            time_limit = 2000 if os.getenv("CI") == "true" else 1000
            assert response_time_ms < time_limit, (
                f"Response time {response_time_ms:.2f}ms exceeds {time_limit}ms"
            )

            # Assert
            assert response.status_code == 200, (
                f"Failed for message: {message_content[:50]}..."
            )
            data = response.json()
            assert "errors" not in data, f"GraphQL errors: {data.get('errors')}"

            send_message_data = data["data"]["sendMessage"]
            assert send_message_data["success"] is True, (
                f"Message failed: {send_message_data}"
            )
            assert send_message_data["errorMessage"] is None

            message_data = send_message_data["message"]
            assert message_data["conversationId"] == str(conversation.id)
            assert message_data["senderRole"] == "USER"
            assert message_data["content"]["text"] == message_content
            assert message_data["timestamp"] is not None
            assert message_data["metadata"] == {}

            # Verify message was persisted in database
            # Query specifically for user messages to avoid race condition with AI responses
            result = await db_session_real.execute(
                select(ChatMessage)
                .where(
                    ChatMessage.conversation_id == conversation.id,
                    ChatMessage.sender_role == "user",
                )
                .order_by(ChatMessage.timestamp.desc())
                .limit(1)
            )
            persisted_message = result.scalar_one()
            assert persisted_message.content["parts"][0]["content"] == message_content
            assert persisted_message.sender_role == "user"

    @pytest.mark.asyncio
    async def test_send_message_empty_content_validation(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """Test that empty message content is rejected."""
        # Arrange - Get or create a conversation
        conversation = await get_or_create_conversation(
            db_session_real, test_user.id, test_assistant_id
        )

        mutation = """
        mutation SendMessage($conversationId: ID!, $content: String!) {
            sendMessage(conversationId: $conversationId, content: $content) {
                message {
                    id
                    content
                }
                success
                errorMessage
            }
        }
        """

        test_cases = [
            ("", "empty string"),
            ("   ", "whitespace only"),
            ("\t\n\r", "whitespace characters only"),
            ("<script>alert('xss')</script>", "XSS attempt"),
        ]

        for content, description in test_cases:
            variables = {
                "conversationId": str(conversation.id),
                "content": content,
            }

            headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

            # Act
            response = await async_client.post(
                "/graphql",
                json={"query": mutation, "variables": variables},
                headers=headers,
            )

            # Assert
            assert response.status_code == 200, f"Failed for {description}"
            data = response.json()

            if not content.strip():
                # Empty or whitespace-only content should be rejected
                assert "errors" not in data, (
                    f"Unexpected GraphQL errors: {data.get('errors')}"
                )
                send_message_data = data["data"]["sendMessage"]
                assert send_message_data["success"] is False, (
                    f"Expected failure for {description}"
                )
                assert "empty" in send_message_data["errorMessage"].lower()
            else:
                # XSS attempt should be sanitized but not necessarily rejected
                assert "errors" not in data, (
                    f"Unexpected GraphQL errors: {data.get('errors')}"
                )
                send_message_data = data["data"]["sendMessage"]
                assert send_message_data["success"] is True, (
                    f"Expected success for {description}"
                )
                assert (
                    "<script>" not in send_message_data["message"]["content"]["text"]
                ), f"XSS not sanitized: {send_message_data}"

            # Verify no message was persisted for empty/whitespace content
            if not content.strip():
                result = await db_session_real.execute(
                    select(ChatMessage).where(
                        ChatMessage.conversation_id == conversation.id
                    )
                )
                messages = result.scalars().all()
                assert len(messages) == 0, (
                    f"Unexpected message persisted for {description}"
                )

    @pytest.mark.asyncio
    async def test_send_message_max_length_allowed(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """Test message with exactly 4000 characters succeeds."""
        # Arrange - Get or create a conversation
        conversation = await get_or_create_conversation(
            db_session_real, test_user.id, test_assistant_id
        )

        mutation = """
        mutation SendMessage($conversationId: ID!, $content: String!) {
            sendMessage(conversationId: $conversationId, content: $content) {
                message {
                    id
                    content
                }
                success
                errorMessage
            }
        }
        """

        # Max allowed length (4000 characters)
        max_content = "a" * 4000
        variables = {
            "conversationId": str(conversation.id),
            "content": max_content,
        }

        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        send_message_data = data["data"]["sendMessage"]
        assert send_message_data["success"] is True
        assert send_message_data["message"]["content"]["text"] == max_content

        # Verify message was persisted correctly
        result = await db_session_real.execute(
            select(ChatMessage)
            .where(ChatMessage.conversation_id == conversation.id)
            .order_by(ChatMessage.timestamp.desc())
            .limit(1)
        )
        persisted_message = result.scalar_one()
        assert persisted_message.content["parts"][0]["content"] == max_content

    @pytest.mark.asyncio
    async def test_send_message_exceeds_length_limit(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """Test message over 4000 characters fails."""
        # Arrange - Get or create a conversation
        conversation = await get_or_create_conversation(
            db_session_real, test_user.id, test_assistant_id
        )

        mutation = """
        mutation SendMessage($conversationId: ID!, $content: String!) {
            sendMessage(conversationId: $conversationId, content: $content) {
                message {
                    id
                }
                success
                errorMessage
            }
        }
        """

        # Count existing messages before test
        result = await db_session_real.execute(
            select(ChatMessage).where(ChatMessage.conversation_id == conversation.id)
        )
        initial_message_count = len(result.scalars().all())

        # Content exceeding limit
        long_content = "a" * 4001
        variables = {"conversationId": str(conversation.id), "content": long_content}

        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        send_message_data = data["data"]["sendMessage"]
        assert send_message_data["success"] is False
        assert (
            "4000" in send_message_data["errorMessage"]
            or "exceed" in send_message_data["errorMessage"].lower()
        )

        # Verify no new message was persisted
        result = await db_session_real.execute(
            select(ChatMessage).where(ChatMessage.conversation_id == conversation.id)
        )
        final_message_count = len(result.scalars().all())
        assert final_message_count == initial_message_count

    @pytest.mark.asyncio
    async def test_send_message_way_over_length_limit(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """Test message way over 4000 characters fails."""
        # Arrange - Get or create a conversation
        conversation = await get_or_create_conversation(
            db_session_real, test_user.id, test_assistant_id
        )

        mutation = """
        mutation SendMessage($conversationId: ID!, $content: String!) {
            sendMessage(conversationId: $conversationId, content: $content) {
                message {
                    id
                }
                success
                errorMessage
            }
        }
        """

        # Count existing messages before test
        result = await db_session_real.execute(
            select(ChatMessage).where(ChatMessage.conversation_id == conversation.id)
        )
        initial_message_count = len(result.scalars().all())

        # Content way over limit
        very_long_content = "a" * 5000
        variables = {
            "conversationId": str(conversation.id),
            "content": very_long_content,
        }

        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        send_message_data = data["data"]["sendMessage"]
        assert send_message_data["success"] is False
        assert "exceed" in send_message_data["errorMessage"].lower()

        # Verify no new message was persisted
        result = await db_session_real.execute(
            select(ChatMessage).where(ChatMessage.conversation_id == conversation.id)
        )
        final_message_count = len(result.scalars().all())
        assert final_message_count == initial_message_count

    @pytest.mark.asyncio
    async def test_send_message_xss_sanitization(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """Test XSS content is properly sanitized."""
        # Arrange - Get or create a conversation
        conversation = await get_or_create_conversation(
            db_session_real, test_user.id, test_assistant_id
        )

        mutation = """
        mutation SendMessage($conversationId: ID!, $content: String!) {
            sendMessage(conversationId: $conversationId, content: $content) {
                message {
                    id
                    content
                }
                success
                errorMessage
            }
        }
        """

        # XSS attempt
        xss_content = "<script>alert('xss')</script>"
        variables = {
            "conversationId": str(conversation.id),
            "content": xss_content,
        }

        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        send_message_data = data["data"]["sendMessage"]
        assert send_message_data["success"] is True

        # XSS content should be sanitized (removed) for security
        message_content = send_message_data["message"]["content"]["text"]
        assert "<script>" not in message_content, "XSS not sanitized"
        assert message_content == "", "XSS content should be completely removed"

        # Verify message was persisted with sanitized content
        result = await db_session_real.execute(
            select(ChatMessage)
            .where(ChatMessage.conversation_id == conversation.id)
            .order_by(ChatMessage.timestamp.desc())
            .limit(1)
        )
        persisted_message = result.scalar_one()
        sanitized_content = persisted_message.content["parts"][0]["content"]
        assert sanitized_content == "", "Sanitized content stored"
        assert "<script>" not in sanitized_content

    @pytest.mark.asyncio
    async def test_send_message_sql_injection_prevention(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """Test SQL injection attempts are handled."""
        # Arrange - Get or create a conversation
        conversation = await get_or_create_conversation(
            db_session_real, test_user.id, test_assistant_id
        )

        mutation = """
        mutation SendMessage($conversationId: ID!, $content: String!) {
            sendMessage(conversationId: $conversationId, content: $content) {
                message {
                    id
                    content
                }
                success
                errorMessage
            }
        }
        """

        # SQL injection attempt
        sql_injection_content = "' OR '1'='1"
        variables = {
            "conversationId": str(conversation.id),
            "content": sql_injection_content,
        }

        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        send_message_data = data["data"]["sendMessage"]
        assert send_message_data["success"] is True

        # SQL injection content should be sanitized
        message_content = send_message_data["message"]["content"]["text"]
        assert "' OR '1'='1" not in message_content, "SQL injection not prevented"
        assert message_content == "", (
            "SQL injection content should be completely removed"
        )

        # Verify message was persisted with sanitized content
        result = await db_session_real.execute(
            select(ChatMessage)
            .where(ChatMessage.conversation_id == conversation.id)
            .order_by(ChatMessage.timestamp.desc())
            .limit(1)
        )
        persisted_message = result.scalar_one()
        sanitized_content = persisted_message.content["parts"][0]["content"]
        assert sanitized_content == "", "Sanitized content stored"
        assert "' OR '1'='1" not in sanitized_content

    @pytest.mark.asyncio
    async def test_send_message_null_byte_sanitization(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """Test null byte injection is sanitized."""
        # Arrange - Get or create a conversation
        conversation = await get_or_create_conversation(
            db_session_real, test_user.id, test_assistant_id
        )

        mutation = """
        mutation SendMessage($conversationId: ID!, $content: String!) {
            sendMessage(conversationId: $conversationId, content: $content) {
                message {
                    id
                    content
                }
                success
                errorMessage
            }
        }
        """

        # Null byte injection
        null_byte_content = "\u0000null-byte"
        variables = {
            "conversationId": str(conversation.id),
            "content": null_byte_content,
        }

        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        send_message_data = data["data"]["sendMessage"]
        assert send_message_data["success"] is True

        # Null byte should be sanitized
        message_content = send_message_data["message"]["content"]["text"]
        assert "\u0000" not in message_content, "Null byte not sanitized"

        # Verify message was persisted with sanitized content
        result = await db_session_real.execute(
            select(ChatMessage)
            .where(ChatMessage.conversation_id == conversation.id)
            .order_by(ChatMessage.timestamp.desc())
            .limit(1)
        )
        persisted_message = result.scalar_one()
        sanitized_content = persisted_message.content["parts"][0]["content"]
        assert "\u0000" not in sanitized_content, "Null byte not sanitized in storage"

    @pytest.mark.asyncio
    async def test_send_message_invalid_conversation_id(
        self,
        async_client: AsyncClient,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """Test that invalid conversation ID is rejected."""
        mutation = """
        mutation SendMessage($conversationId: ID!, $content: String!) {
            sendMessage(conversationId: $conversationId, content: $content) {
                message {
                    id
                }
                success
                errorMessage
            }
        }
        """

        variables = {"conversationId": "invalid-uuid", "content": "Hello world"}

        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        send_message_data = data["data"]["sendMessage"]
        assert send_message_data["success"] is False
        assert "invalid" in send_message_data["errorMessage"].lower()

    @pytest.mark.asyncio
    async def test_send_message_nonexistent_conversation(
        self,
        async_client: AsyncClient,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """Test that sending to non-existent conversation is rejected."""
        mutation = """
        mutation SendMessage($conversationId: ID!, $content: String!) {
            sendMessage(conversationId: $conversationId, content: $content) {
                message {
                    id
                }
                success
                errorMessage
            }
        }
        """

        # Use a valid UUID that doesn't exist in database
        nonexistent_id = str(uuid.uuid4())

        variables = {"conversationId": nonexistent_id, "content": "Hello world"}

        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        send_message_data = data["data"]["sendMessage"]
        assert send_message_data["success"] is False
        assert "not found" in send_message_data["errorMessage"].lower()

    @pytest.mark.asyncio
    async def test_send_message_unauthenticated(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
    ):
        """Test that unauthenticated requests are rejected."""
        # Arrange - Get or create a conversation
        conversation = await get_or_create_conversation(
            db_session_real, test_user.id, test_assistant_id
        )

        mutation = """
        mutation SendMessage($conversationId: ID!, $content: String!) {
            sendMessage(conversationId: $conversationId, content: $content) {
                message {
                    id
                }
                success
                errorMessage
            }
        }
        """

        variables = {"conversationId": str(conversation.id), "content": "Hello world"}

        # Act - No Authorization header
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" in data
        assert "authentication" in data["errors"][0]["message"].lower()

    @pytest.mark.asyncio
    async def test_send_message_unauthorized_conversation(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """Test users cannot send messages to conversations they don't own."""
        # Arrange - Create another user in the database
        from a2a_platform.schemas.user import UserCreate
        from a2a_platform.services.user_service import create_user

        other_user_data = UserCreate(
            clerk_user_id=f"other_test_clerk_user_{uuid.uuid4()}",
            email=f"other_test_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        other_user = await create_user(db_session_real, other_user_data)

        # Create a conversation owned by the other user
        conversation = await get_or_create_conversation(
            db_session_real, other_user.id, test_assistant_id
        )

        mutation = """
        mutation SendMessage($conversationId: ID!, $content: String!) {
            sendMessage(conversationId: $conversationId, content: $content) {
                message {
                    id
                }
                success
                errorMessage
            }
        }
        """

        variables = {"conversationId": str(conversation.id), "content": "Hello world"}

        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        send_message_data = data["data"]["sendMessage"]
        assert send_message_data["success"] is False
        assert "permission" in send_message_data["errorMessage"].lower()

        # Verify no message was persisted
        result = await db_session_real.execute(
            select(ChatMessage).where(ChatMessage.conversation_id == conversation.id)
        )
        messages = result.scalars().all()
        assert len(messages) == 0

    @pytest.mark.asyncio
    async def test_send_message_updates_conversation_timestamp(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """Test sending message updates conversation's last_message_at."""
        # Arrange - Get or create a conversation
        conversation = await get_or_create_conversation(
            db_session_real, test_user.id, test_assistant_id
        )

        original_last_message_at = conversation.last_message_at

        mutation = """
        mutation SendMessage($conversationId: ID!, $content: String!) {
            sendMessage(conversationId: $conversationId, content: $content) {
                message {
                    id
                }
                success
                errorMessage
            }
        }
        """

        variables = {"conversationId": str(conversation.id), "content": "Hello world"}

        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        send_message_data = data["data"]["sendMessage"]
        assert send_message_data["success"] is True

        # Verify conversation timestamp was updated
        await db_session_real.refresh(conversation)
        assert conversation.last_message_at != original_last_message_at
        assert conversation.last_message_at is not None

    @pytest.mark.asyncio
    async def test_send_message_with_special_characters(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """Test that messages with special characters are handled correctly."""
        # Arrange - Get or create a conversation
        conversation = await get_or_create_conversation(
            db_session_real, test_user.id, test_assistant_id
        )

        mutation = """
        mutation SendMessage($conversationId: ID!, $content: String!) {
            sendMessage(conversationId: $conversationId, content: $content) {
                message {
                    id
                    content
                }
                success
                errorMessage
            }
        }
        """

        special_content = "Hello! @#$%^&*()_+-=[]{}|;':\",./<>?`~"
        # Only dangerous characters like backticks are removed for security
        expected_content = "Hello! @#$%^&*()_+-=[]{}|;':\",./<>?~"

        variables = {"conversationId": str(conversation.id), "content": special_content}

        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        send_message_data = data["data"]["sendMessage"]
        assert send_message_data["success"] is True
        message_content = send_message_data["message"]["content"]["text"]
        assert message_content == expected_content

        # Verify message was persisted correctly
        result = await db_session_real.execute(
            select(ChatMessage).where(ChatMessage.conversation_id == conversation.id)
        )
        persisted_message = result.scalar_one()
        assert persisted_message.content["parts"][0]["content"] == expected_content

    @pytest.mark.asyncio
    async def test_send_message_graphql_schema_compliance(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """Test that sendMessage mutation complies with GraphQL schema."""
        # Arrange - Get or create a conversation
        await get_or_create_conversation(
            db_session_real, test_user.id, test_assistant_id
        )

        # Test schema introspection for sendMessage mutation
        introspection_query = """
        query {
            __schema {
                mutationType {
                    fields {
                        name
                        args {
                            name
                            type {
                                name
                                kind
                                ofType {
                                    name
                                    kind
                                }
                            }
                        }
                        type {
                            name
                            kind
                            ofType {
                                name
                                kind
                            }
                        }
                    }
                }
            }
        }
        """

        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": introspection_query},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        mutation_fields = data["data"]["__schema"]["mutationType"]["fields"]
        send_message_field = next(
            (field for field in mutation_fields if field["name"] == "sendMessage"),
            None,
        )

        assert send_message_field is not None

        # The type might be wrapped in NonNull or other wrappers
        field_type = send_message_field["type"]
        if field_type.get("kind") == "NON_NULL":
            # Unwrap NonNull wrapper
            field_type = field_type.get("ofType", field_type)

        assert field_type["name"] == "SendMessagePayload"

        # Check arguments
        args = send_message_field["args"]
        arg_names = [arg["name"] for arg in args]
        assert "conversationId" in arg_names
        assert "content" in arg_names

    @pytest.mark.asyncio
    async def test_send_message_whitespace_only_content(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """Test that whitespace-only content is rejected."""
        # Arrange - Get or create a conversation
        conversation = await get_or_create_conversation(
            db_session_real, test_user.id, test_assistant_id
        )

        mutation = """
        mutation SendMessage($conversationId: ID!, $content: String!) {
            sendMessage(conversationId: $conversationId, content: $content) {
                message {
                    id
                }
                success
                errorMessage
            }
        }
        """

        variables = {
            "conversationId": str(conversation.id),
            "content": "   \t\n   ",  # Only whitespace
        }

        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        send_message_data = data["data"]["sendMessage"]
        assert send_message_data["success"] is False
        assert "empty" in send_message_data["errorMessage"].lower()

    @pytest.mark.asyncio
    async def test_send_message_unicode_content(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """Test that unicode content is handled correctly."""
        # Arrange - Get or create a conversation
        conversation = await get_or_create_conversation(
            db_session_real, test_user.id, test_assistant_id
        )

        mutation = """
        mutation SendMessage($conversationId: ID!, $content: String!) {
            sendMessage(conversationId: $conversationId, content: $content) {
                message {
                    id
                    content
                }
                success
                errorMessage
            }
        }
        """

        unicode_content = "Hello 🚀 测试 العربية русский 🎉"

        variables = {"conversationId": str(conversation.id), "content": unicode_content}

        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        send_message_data = data["data"]["sendMessage"]
        assert send_message_data["success"] is True
        message_content = send_message_data["message"]["content"]["text"]
        assert message_content == unicode_content

    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_send_message_response_time_performance(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """Test that sendMessage responds within acceptable time limits."""
        import time

        # Arrange - Get or create a conversation
        conversation = await get_or_create_conversation(
            db_session_real, test_user.id, test_assistant_id
        )

        mutation = """
        mutation SendMessage($conversationId: ID!, $content: String!) {
            sendMessage(conversationId: $conversationId, content: $content) {
                message {
                    id
                }
                success
                errorMessage
            }
        }
        """

        variables = {
            "conversationId": str(conversation.id),
            "content": "Performance test message",
        }

        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act - Measure response time
        start_time = time.time()
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=headers,
        )
        end_time = time.time()
        # Convert to milliseconds
        response_time = (end_time - start_time) * 1000

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        send_message_data = data["data"]["sendMessage"]
        assert send_message_data["success"] is True

        # Response time should be under 200ms as per QA spec (relaxed in CI)
        import os

        time_limit = 1000 if os.getenv("CI") == "true" else 200
        error_msg = f"Response time {response_time}ms exceeds {time_limit}ms limit"
        assert response_time < time_limit, error_msg

    @pytest.mark.asyncio
    async def test_send_message_concurrent_requests(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """
        Test that multiple message sending requests are handled correctly.

        Note: This test uses sequential requests with small delays to simulate
        concurrent-like behavior while avoiding database connection concurrency
        issues in the test infrastructure. The test verifies that multiple
        rapid requests are processed correctly without data corruption.
        """
        import asyncio
        from unittest.mock import patch

        # Mock PA response generation to prevent automatic agent responses
        # This ensures the test only validates user message handling without
        # interference from background AI response generation
        with patch(
            "a2a_platform.services.pa_response_service.PAResponseService."
            "schedule_response_generation"
        ):
            # Arrange - Get or create a conversation
            conversation = await get_or_create_conversation(
                db_session_real, test_user.id, test_assistant_id
            )

            mutation = """
            mutation SendMessage($conversationId: ID!, $content: String!) {
                sendMessage(conversationId: $conversationId, content: $content) {
                    message {
                        id
                        content
                    }
                    success
                    errorMessage
                }
            }
            """

            headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

            # Send multiple rapid sequential requests to test quick succession
            async def send_message(message_num: int):
                # Small delay to simulate rapid but not truly concurrent requests
                await asyncio.sleep(message_num * 0.05)

                variables = {
                    "conversationId": str(conversation.id),
                    "content": f"Sequential message {message_num}",
                }

                response = await async_client.post(
                    "/graphql",
                    json={"query": mutation, "variables": variables},
                    headers=headers,
                )
                return response, message_num

            # Act - Send 3 rapid sequential messages
            successful_responses = []
            for i in range(3):
                response, message_num = await send_message(i)

                # Assert each response is successful
                assert response.status_code == 200
                data = response.json()
                errors = data.get("errors")
                assert "errors" not in data, f"GraphQL errors: {errors}"

                send_message_data = data["data"]["sendMessage"]
                error_msg = (
                    f"Message {message_num} failed: "
                    f"{send_message_data.get('errorMessage', 'Unknown error')}"
                )
                assert send_message_data["success"] is True, error_msg

                successful_responses.append((response, message_num))

            # Assert that all messages were sent successfully
            success_count = len(successful_responses)
            assert success_count == 3, "All 3 messages should succeed"

            # Verify that all messages were persisted correctly
            result = await db_session_real.execute(
                select(ChatMessage)
                .where(ChatMessage.conversation_id == conversation.id)
                .order_by(ChatMessage.timestamp)
            )
            messages = result.scalars().all()
            assert len(messages) == 3, "Exactly 3 messages should be persisted"

            # Verify message content and order
            for i, message in enumerate(messages):
                expected_content = f"Sequential message {i}"
                actual_content = message.content["parts"][0]["content"]
                assert actual_content == expected_content
                assert message.sender_role == "user"

    @pytest.mark.asyncio
    async def test_send_message_xss_prevention(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """Test that potential XSS content is stored safely."""
        # Arrange - Get or create a conversation
        conversation = await get_or_create_conversation(
            db_session_real, test_user.id, test_assistant_id
        )

        mutation = """
        mutation SendMessage($conversationId: ID!, $content: String!) {
            sendMessage(conversationId: $conversationId, content: $content) {
                message {
                    id
                    content
                }
                success
                errorMessage
            }
        }
        """

        # Potential XSS content
        xss_content = "<script>alert('xss')</script><img src=x onerror=alert('xss')>"

        variables = {"conversationId": str(conversation.id), "content": xss_content}

        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        send_message_data = data["data"]["sendMessage"]
        assert send_message_data["success"] is True

        # XSS content should be sanitized (removed) for security
        message_content = send_message_data["message"]["content"]["text"]
        assert message_content == ""  # XSS content completely removed
        assert "<script>" not in message_content  # No script tags
        assert "onerror" not in message_content  # No event handlers

        # Verify message was persisted correctly with sanitized content
        result = await db_session_real.execute(
            select(ChatMessage).where(ChatMessage.conversation_id == conversation.id)
        )
        persisted_message = result.scalar_one()
        sanitized_content = persisted_message.content["parts"][0]["content"]
        assert sanitized_content == ""  # Sanitized content stored
        assert "<script>" not in sanitized_content
        assert "onerror" not in sanitized_content

    @pytest.mark.asyncio
    async def test_ai_service_error_handling(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
        mock_ai_response_service_error,
    ):
        """
        Test sendMessage when AI service raises exceptions.

        This test verifies:
        - System handles AI service exceptions gracefully
        - User receives appropriate error handling
        - No system crashes occur when AI service fails
        """
        # Arrange - Get or create a conversation
        conversation = await get_or_create_conversation(
            db_session_real, test_user.id, test_assistant_id
        )

        mutation = """
        mutation SendMessage($conversationId: ID!, $content: String!) {
            sendMessage(conversationId: $conversationId, content: $content) {
                message {
                    id
                    content
                }
                success
                errorMessage
            }
        }
        """

        variables = {
            "conversationId": str(conversation.id),
            "content": "Test message that will trigger AI error",
        }

        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        response_data = response.json()
        assert "errors" not in response_data

        # Wait for background task to complete
        import asyncio

        await asyncio.sleep(0.1)  # Give background task time to execute

        # Verify AI service was called and exception was raised
        assert mock_ai_response_service_error.called, (
            "AI service should have been called"
        )

        # The system should handle the error gracefully
        # (specific error handling behavior depends on implementation)

    @pytest.mark.asyncio
    async def test_ai_contextual_responses(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
        mock_ai_response_service_contextual,
    ):
        """
        Test AI service with contextual responses based on user input.

        This test verifies:
        - AI mock responds appropriately to different message types
        - Context-aware response generation works correctly
        - Assistant personality is reflected in responses
        """
        # Arrange - Get or create a conversation
        conversation = await get_or_create_conversation(
            db_session_real, test_user.id, test_assistant_id
        )

        # Test different message types that trigger different responses
        test_cases = [
            ("Hello there!", "hello"),  # Greeting
            ("I need help with something", "help"),  # Help request
            ("Goodbye for now", "goodbye"),  # Farewell
            ("What is the weather like?", "understand"),  # General query
        ]

        mutation = """
        mutation SendMessage($conversationId: ID!, $content: String!) {
            sendMessage(conversationId: $conversationId, content: $content) {
                message {
                    id
                    content
                }
                success
                errorMessage
            }
        }
        """

        for message_content, expected_response_type in test_cases:
            variables = {
                "conversationId": str(conversation.id),
                "content": message_content,
            }

            headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

            # Act
            response = await async_client.post(
                "/graphql",
                json={"query": mutation, "variables": variables},
                headers=headers,
            )

            # Assert
            assert response.status_code == 200
            data = response.json()
            assert "errors" not in data

            send_message_data = data["data"]["sendMessage"]
            assert send_message_data["success"] is True

        # Wait for background tasks to complete
        import asyncio

        await asyncio.sleep(0.1)  # Give background tasks time to execute

        # Verify AI service was called for each message
        assert mock_ai_response_service_contextual.call_count == len(test_cases)

    @pytest.mark.asyncio
    async def test_ai_service_input_validation(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """
        Test comprehensive validation of AI service inputs.

        This test verifies:
        - AI service receives correct Assistant object
        - User message is passed accurately
        - Conversation history is properly formatted
        - Optional parameters (force_fallback) work correctly
        """
        # Arrange - Get or create a conversation with some history
        conversation = await get_or_create_conversation(
            db_session_real, test_user.id, test_assistant_id
        )

        # Add some conversation history
        previous_message = ChatMessage(
            conversation_id=conversation.id,
            sender_role="user",
            content={"text": "Previous message"},
        )
        db_session_real.add(previous_message)
        await db_session_real.commit()

        # Use inline mock for detailed parameter validation
        service_path = (
            "a2a_platform.services.pydantic_ai_response_service."
            "PydanticAIResponseService.generate_response"
        )

        def validate_ai_inputs(
            assistant, user_message, conversation_history, force_fallback=False
        ):
            """Detailed validation of AI service parameters."""
            # Validate Assistant object
            assert hasattr(assistant, "id"), "Assistant should have id attribute"
            assert hasattr(assistant, "name"), "Assistant should have name attribute"
            assert hasattr(assistant, "backstory"), (
                "Assistant should have backstory attribute"
            )
            assert assistant.id == test_assistant_id, "Should pass correct assistant"

            # Validate user message
            assert isinstance(user_message, str), "User message should be string"
            assert len(user_message) > 0, "User message should not be empty"
            assert user_message == "Test message for input validation", (
                "Should pass exact message"
            )

            # Validate conversation history
            assert isinstance(conversation_history, list), (
                "Conversation history should be list"
            )
            # Should include the previous message we added
            assert len(conversation_history) >= 1, "Should include previous messages"

            # Validate optional parameter
            assert isinstance(force_fallback, bool), "force_fallback should be boolean"

            return ("Input validation successful!", True)

        with patch(service_path, side_effect=validate_ai_inputs) as mock_ai:
            mutation = """
            mutation SendMessage($conversationId: ID!, $content: String!) {
                sendMessage(conversationId: $conversationId, content: $content) {
                    message {
                        id
                        content
                    }
                    success
                    errorMessage
                }
            }
            """

            variables = {
                "conversationId": str(conversation.id),
                "content": "Test message for input validation",
            }

            headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

            # Act
            response = await async_client.post(
                "/graphql",
                json={"query": mutation, "variables": variables},
                headers=headers,
            )

            # Assert
            assert response.status_code == 200
            data = response.json()
            assert "errors" not in data

            send_message_data = data["data"]["sendMessage"]
            assert send_message_data["success"] is True

            # Wait for background task to complete
            import asyncio

            await asyncio.sleep(0.1)  # Give background task time to execute

            # Verify our validation function was called
            assert mock_ai.called, "AI service should have been called"

            # All parameter validation happens in the side_effect function
            # If we reach this point, all validations passed

    @pytest.mark.asyncio
    async def test_ai_service_custom_response_configuration(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
        mock_ai_response_service,
    ):
        """
        Test customizing AI response mock for specific test scenarios.

        This test demonstrates:
        - How to override default fixture responses
        - Testing specific AI response content
        - Verifying AI-generated vs fallback responses
        """
        # Arrange - Get or create a conversation
        conversation = await get_or_create_conversation(
            db_session_real, test_user.id, test_assistant_id
        )

        # Configure custom AI response for this specific test
        custom_response = (
            "This is a custom AI response configured for this specific test scenario."
        )
        mock_ai_response_service.return_value = (custom_response, True)

        mutation = """
        mutation SendMessage($conversationId: ID!, $content: String!) {
            sendMessage(conversationId: $conversationId, content: $content) {
                message {
                    id
                    content
                }
                success
                errorMessage
            }
        }
        """

        variables = {
            "conversationId": str(conversation.id),
            "content": "Test message with custom AI response",
        }

        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        send_message_data = data["data"]["sendMessage"]
        assert send_message_data["success"] is True

        # Wait for background task to complete
        import asyncio

        await asyncio.sleep(0.1)  # Give background task time to execute

        # Verify our custom response configuration was used
        assert mock_ai_response_service.called, "AI service should have been called"
        returned_response, is_ai_generated = mock_ai_response_service.return_value
        assert returned_response == custom_response, "Should use custom response"
        assert is_ai_generated is True, "Should be marked as AI-generated"
