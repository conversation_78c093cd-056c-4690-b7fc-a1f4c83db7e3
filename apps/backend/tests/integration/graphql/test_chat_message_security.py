"""
Security tests for ChatMessage GraphQL schema.

Tests to ensure that private fields like _content are not exposed to GraphQL
clients and that only the transformed content field is accessible.
"""

import uuid
import pytest
from unittest.mock import patch
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi.security.http import HTTPAuthorizationCredentials

from a2a_platform.db.models.chat_message import ChatMessage
from a2a_platform.db.models.conversation import Conversation
from a2a_platform.db.models.user import User


@pytest.fixture
def mock_clerk_auth(test_user: User):
    """
    Mock the Clerk authentication middleware to accept test user.
    This fixture patches the ClerkAuthMiddleware.__call__ method to return
    the test user's clerk_user_id from the Authorization header.
    """

    async def mock_clerk_auth_call(_, request):
        # Extract the token from the Authorization header
        auth_header = request.headers.get("Authorization", "")
        if auth_header.startswith("Bearer "):
            # Return the test user's clerk_user_id
            return HTTPAuthorizationCredentials(
                scheme="Bearer", credentials=test_user.clerk_user_id
            )
        return None

    with patch(
        "a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__",
        new=mock_clerk_auth_call,
    ) as mock:
        yield mock


class TestChatMessageSecurity:
    """Test security aspects of ChatMessage GraphQL schema."""

    @pytest.mark.asyncio
    async def test_private_content_field_not_exposed_in_schema(
        self,
        async_client: AsyncClient,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """Test that _content field is not exposed in GraphQL schema."""
        # GraphQL introspection query to get ChatMessage type fields
        introspection_query = """
        query {
            __schema {
                types {
                    name
                    fields {
                        name
                        type {
                            name
                            kind
                        }
                    }
                }
            }
        }
        """

        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": introspection_query},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        # Find ChatMessage type in schema
        types = data["data"]["__schema"]["types"]
        chat_message_type = next((t for t in types if t["name"] == "ChatMessage"), None)

        assert chat_message_type is not None, "ChatMessage type should exist"

        # Get all field names
        field_names = [field["name"] for field in chat_message_type["fields"]]

        # Assert that _content is NOT exposed
        assert "_content" not in field_names, (
            "_content field should not be exposed in GraphQL schema"
        )

        # Assert that content IS exposed (the public transformed field)
        assert "content" in field_names, (
            "content field should be exposed in GraphQL schema"
        )

        # Assert other expected fields are present
        expected_fields = [
            "id",
            "conversationId",
            "senderRole",
            "timestamp",
            "metadata",
        ]
        for field in expected_fields:
            assert field in field_names, f"{field} should be exposed in schema"

    @pytest.mark.asyncio
    async def test_direct_query_private_content_field_fails(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """Test that directly querying _content field results in error."""
        # Arrange - Create a conversation and message
        conversation = Conversation(
            user_id=test_user.id,
            assistant_id=test_assistant_id,
        )
        db_session_real.add(conversation)
        await db_session_real.commit()
        await db_session_real.refresh(conversation)

        # Create a test message
        message = ChatMessage(
            conversation_id=conversation.id,
            sender_role="user",
            content={"parts": [{"type": "text", "content": "Test message"}]},
        )
        db_session_real.add(message)
        await db_session_real.commit()
        await db_session_real.refresh(message)

        # Query that tries to access _content field directly
        query_with_private_field = """
        query GetConversationMessages($conversationId: ID!) {
            getConversationMessages(conversationId: $conversationId) {
                edges {
                    node {
                        id
                        _content
                        content
                    }
                }
            }
        }
        """

        variables = {"conversationId": str(conversation.id)}
        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": query_with_private_field, "variables": variables},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        data = response.json()

        # Should have GraphQL errors because _content field doesn't exist
        assert "errors" in data, "Query should fail with GraphQL errors"

        # Check that the error mentions the unknown field
        error_message = str(data["errors"])
        assert "_content" in error_message, "Error should mention _content"

    @pytest.mark.asyncio
    async def test_public_content_field_works_correctly(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
        mock_clerk_jwt_valid: str,
        mock_clerk_auth,
    ):
        """Test that the public content field works correctly."""
        # Arrange - Create a conversation and message
        conversation = Conversation(
            user_id=test_user.id,
            assistant_id=test_assistant_id,
        )
        db_session_real.add(conversation)
        await db_session_real.commit()
        await db_session_real.refresh(conversation)

        # Create a test message with parts format (internal format)
        test_content = "Hello, this is a test message!"
        message = ChatMessage(
            conversation_id=conversation.id,
            sender_role="user",
            content={"parts": [{"type": "text", "content": test_content}]},
        )
        db_session_real.add(message)
        await db_session_real.commit()
        await db_session_real.refresh(message)

        # Query that accesses only the public content field
        query = """
        query GetConversationMessages($conversationId: ID!) {
            getConversationMessages(conversationId: $conversationId) {
                edges {
                    node {
                        id
                        content
                        senderRole
                    }
                }
            }
        }
        """

        variables = {"conversationId": str(conversation.id)}
        headers = {"Authorization": f"Bearer {mock_clerk_jwt_valid}"}

        # Act
        response = await async_client.post(
            "/graphql",
            json={"query": query, "variables": variables},
            headers=headers,
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        connection = data["data"]["getConversationMessages"]
        assert len(connection["edges"]) == 1

        message_data = connection["edges"][0]["node"]
        assert message_data["id"] == str(message.id)
        assert message_data["senderRole"] == "USER"

        # Verify content is transformed to expected format
        assert "content" in message_data
        assert message_data["content"]["text"] == test_content

        # Verify the internal format is not exposed
        assert "parts" not in message_data["content"]
