"""
Integration tests for PydanticAI multi-provider support.

Tests the complete flow with different LLM providers (OpenAI, Anthropic, Google Gemini)
and ensures consistent behavior across all providers.
"""

import uuid
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.assistant import Assistant
from a2a_platform.db.models.chat_message import ChatMessage
from a2a_platform.db.models.conversation import Conversation
from a2a_platform.db.models.user import User
from a2a_platform.services.pa_response_service import PAResponseService
from a2a_platform.services.pydantic_ai_response_service import PydanticAIResponseService


@pytest.mark.asyncio
class TestPydanticAIMultiProvider:
    """Test PydanticAI multi-provider integration."""

    async def test_openai_provider_integration(self, db_session_real: AsyncSession):
        """Test OpenAI provider integration."""
        # Create test user with unique identifier
        unique_id = str(uuid.uuid4())[:8]
        user = User(
            clerk_user_id=f"test_user_openai_{unique_id}",
            email=f"openai_{unique_id}@example.com",
            is_pa_setup_complete=True,
        )
        db_session_real.add(user)
        await db_session_real.flush()

        # Create test assistant with OpenAI configuration
        assistant = Assistant(
            user_id=user.id,
            name="OpenAI Bot",
            backstory="I am an OpenAI-powered assistant.",
            configuration={
                "ai": {
                    "enabled": True,
                    "model": "openai:gpt-3.5-turbo",
                    "temperature": 0.7,
                    "max_tokens": 150,
                }
            },
        )
        db_session_real.add(assistant)
        await db_session_real.flush()

        # Create test conversation
        conversation = Conversation(
            user_id=user.id,
            assistant_id=assistant.id,
        )
        db_session_real.add(conversation)
        await db_session_real.flush()

        # Create user message
        user_message = ChatMessage(
            conversation_id=conversation.id,
            sender_role="user",
            content={"parts": [{"type": "text", "content": "Hello from OpenAI test!"}]},
        )
        db_session_real.add(user_message)
        await db_session_real.commit()

        # Mock PydanticAI Agent response
        with patch(
            "a2a_platform.services.pydantic_ai_response_service.Agent"
        ) as mock_agent_class:
            mock_result = MagicMock()
            mock_result.output = (
                "Hello! I'm an OpenAI-powered assistant. How can I help you?"
            )

            mock_agent = AsyncMock()
            mock_agent.run.return_value = mock_result
            mock_agent_class.return_value = mock_agent

            # Test PA response generation
            pa_service = PAResponseService(db_session_real)
            pa_response = await pa_service.generate_and_send_response(
                conversation_id=conversation.id,
                user_message=user_message,
                user_id=user.id,
            )

            # Verify response was created
            assert pa_response is not None
            assert pa_response.sender_role == "agent"
            assert pa_response.conversation_id == conversation.id

            # Verify response content
            content = pa_response.content
            assert isinstance(content, dict)
            assert "parts" in content
            assert len(content["parts"]) > 0
            assert content["parts"][0]["type"] == "text"
            assert "OpenAI-powered assistant" in content["parts"][0]["content"]

            # Verify metadata
            metadata = pa_response.message_metadata
            assert metadata["ai_generated"] is True
            assert metadata["response_to_message_id"] == str(user_message.id)
            assert metadata["assistant_id"] == str(assistant.id)

    async def test_anthropic_provider_integration(self, db_session_real: AsyncSession):
        """Test Anthropic Claude provider integration."""
        # Create test user with unique identifier
        unique_id = str(uuid.uuid4())[:8]
        user = User(
            clerk_user_id=f"test_user_anthropic_{unique_id}",
            email=f"anthropic_{unique_id}@example.com",
            is_pa_setup_complete=True,
        )
        db_session_real.add(user)
        await db_session_real.flush()

        # Create test assistant with Anthropic configuration
        assistant = Assistant(
            user_id=user.id,
            name="Claude Bot",
            backstory="I am a Claude-powered assistant.",
            configuration={
                "ai": {
                    "enabled": True,
                    "model": "anthropic:claude-3-5-sonnet-latest",
                    "temperature": 0.5,
                    "max_tokens": 200,
                }
            },
        )
        db_session_real.add(assistant)
        await db_session_real.flush()

        # Create test conversation
        conversation = Conversation(
            user_id=user.id,
            assistant_id=assistant.id,
        )
        db_session_real.add(conversation)
        await db_session_real.flush()

        # Create user message
        user_message = ChatMessage(
            conversation_id=conversation.id,
            sender_role="user",
            content={
                "parts": [{"type": "text", "content": "Hello from Anthropic test!"}]
            },
        )
        db_session_real.add(user_message)
        await db_session_real.commit()

        # Mock PydanticAI Agent response
        with patch(
            "a2a_platform.services.pydantic_ai_response_service.Agent"
        ) as mock_agent_class:
            mock_result = MagicMock()
            mock_result.output = "Hello! I'm Claude, an AI assistant created by Anthropic. How may I assist you today?"

            mock_agent = AsyncMock()
            mock_agent.run.return_value = mock_result
            mock_agent_class.return_value = mock_agent

            # Test PA response generation
            pa_service = PAResponseService(db_session_real)
            pa_response = await pa_service.generate_and_send_response(
                conversation_id=conversation.id,
                user_message=user_message,
                user_id=user.id,
            )

            # Verify response was created
            assert pa_response is not None
            assert pa_response.sender_role == "agent"

            # Verify response content
            content = pa_response.content
            assert "Claude" in content["parts"][0]["content"]
            assert "Anthropic" in content["parts"][0]["content"]

            # Verify metadata
            metadata = pa_response.message_metadata
            assert metadata["ai_generated"] is True

    async def test_google_gemini_provider_integration(
        self, db_session_real: AsyncSession
    ):
        """Test Google Gemini provider integration."""
        # Create test user with unique identifier
        unique_id = str(uuid.uuid4())[:8]
        user = User(
            clerk_user_id=f"test_user_gemini_{unique_id}",
            email=f"gemini_{unique_id}@example.com",
            is_pa_setup_complete=True,
        )
        db_session_real.add(user)
        await db_session_real.flush()

        # Create test assistant with Gemini configuration
        assistant = Assistant(
            user_id=user.id,
            name="Gemini Bot",
            backstory="I am a Gemini-powered assistant.",
            configuration={
                "ai": {
                    "enabled": True,
                    "model": "google-gla:gemini-2.0-flash",
                    "temperature": 0.8,
                    "max_tokens": 300,
                }
            },
        )
        db_session_real.add(assistant)
        await db_session_real.flush()

        # Create test conversation
        conversation = Conversation(
            user_id=user.id,
            assistant_id=assistant.id,
        )
        db_session_real.add(conversation)
        await db_session_real.flush()

        # Create user message
        user_message = ChatMessage(
            conversation_id=conversation.id,
            sender_role="user",
            content={"parts": [{"type": "text", "content": "Hello from Gemini test!"}]},
        )
        db_session_real.add(user_message)
        await db_session_real.commit()

        # Mock PydanticAI Agent response
        with patch(
            "a2a_platform.services.pydantic_ai_response_service.Agent"
        ) as mock_agent_class:
            mock_result = MagicMock()
            mock_result.output = "Hello! I'm Gemini, Google's AI assistant. I'm here to help you with any questions or tasks you have!"

            mock_agent = AsyncMock()
            mock_agent.run.return_value = mock_result
            mock_agent_class.return_value = mock_agent

            # Test PA response generation
            pa_service = PAResponseService(db_session_real)
            pa_response = await pa_service.generate_and_send_response(
                conversation_id=conversation.id,
                user_message=user_message,
                user_id=user.id,
            )

            # Verify response was created
            assert pa_response is not None
            assert pa_response.sender_role == "agent"

            # Verify response content
            content = pa_response.content
            assert "Gemini" in content["parts"][0]["content"]
            assert "Google" in content["parts"][0]["content"]

            # Verify metadata
            metadata = pa_response.message_metadata
            assert metadata["ai_generated"] is True

    async def test_provider_fallback_behavior(self, db_session_real: AsyncSession):
        """Test fallback behavior when AI providers fail."""
        # Create test data with unique identifier to avoid conflicts
        unique_id = str(uuid.uuid4())[:8]
        user = User(
            clerk_user_id=f"test_user_fallback_{unique_id}",
            email=f"fallback_{unique_id}@example.com",
            is_pa_setup_complete=True,
        )
        db_session_real.add(user)
        await db_session_real.flush()

        assistant = Assistant(
            user_id=user.id,
            name="Fallback Bot",
            backstory="I am a test assistant for fallback scenarios.",
            configuration={"ai": {"enabled": True, "model": "openai:gpt-3.5-turbo"}},
        )
        db_session_real.add(assistant)
        await db_session_real.flush()

        conversation = Conversation(
            user_id=user.id,
            assistant_id=assistant.id,
        )
        db_session_real.add(conversation)
        await db_session_real.flush()

        user_message = ChatMessage(
            conversation_id=conversation.id,
            sender_role="user",
            content={"parts": [{"type": "text", "content": "Test fallback"}]},
        )
        db_session_real.add(user_message)
        await db_session_real.commit()

        # Mock PydanticAI Agent to raise an exception
        with patch(
            "a2a_platform.services.pydantic_ai_response_service.Agent"
        ) as mock_agent_class:
            mock_agent = AsyncMock()
            mock_agent.run.side_effect = Exception("Provider unavailable")
            mock_agent_class.return_value = mock_agent

            # Test PA response generation with fallback
            pa_service = PAResponseService(db_session_real)
            pa_response = await pa_service.generate_and_send_response(
                conversation_id=conversation.id,
                user_message=user_message,
                user_id=user.id,
            )

            # Should still create a response (fallback)
            assert pa_response is not None
            assert pa_response.sender_role == "agent"

            # Verify it's a fallback response
            metadata = pa_response.message_metadata
            assert metadata["ai_generated"] is False

    async def test_model_configuration_consistency(self):
        """Test that model configurations are handled consistently across providers."""
        service = PydanticAIResponseService()

        # Test different model configurations
        test_configs = [
            {"model": "openai:gpt-4", "temperature": 0.5, "max_tokens": 100},
            {
                "model": "anthropic:claude-3-5-sonnet-latest",
                "temperature": 0.7,
                "max_tokens": 200,
            },
            {
                "model": "google-gla:gemini-2.0-flash",
                "temperature": 0.9,
                "max_tokens": 300,
            },
        ]

        for config_dict in test_configs:
            assistant = Assistant(
                id=f"test-{config_dict['model'].replace(':', '-')}",
                name="Test Assistant",
                backstory="Test backstory",
                configuration={"ai": config_dict},
            )

            config = service._get_ai_config(assistant)
            assert config.model == config_dict["model"]
            assert config.temperature == config_dict["temperature"]
            assert config.max_tokens == config_dict["max_tokens"]

            # Test model instance creation
            model_instance = service._create_model_instance(config)
            assert model_instance is not None

    async def test_conversation_context_across_providers(
        self, db_session_real: AsyncSession
    ):
        """Test that conversation context is handled consistently across providers."""
        # Test with different providers - create separate users for each provider
        # since Assistant model has unique constraint on user_id (one assistant per user)
        providers = [
            "openai:gpt-3.5-turbo",
            "anthropic:claude-3-5-sonnet-latest",
            "google-gla:gemini-2.0-flash",
        ]

        assistants = []
        users = []

        for provider in providers:
            # Create unique user for each provider to avoid unique constraint violation
            unique_id = str(uuid.uuid4())[:8]
            user = User(
                clerk_user_id=f"test_user_context_{provider.split(':')[0]}_{unique_id}",
                email=f"context_{provider.split(':')[0]}_{unique_id}@example.com",
                is_pa_setup_complete=True,
            )
            db_session_real.add(user)
            await db_session_real.flush()
            users.append(user)

            assistant = Assistant(
                user_id=user.id,
                name=f"{provider.split(':')[0].title()} Bot",
                backstory=f"I am a {provider} powered assistant.",
                configuration={
                    "ai": {
                        "enabled": True,
                        "model": provider,
                        "temperature": 0.7,
                        "max_tokens": 150,
                    }
                },
            )
            db_session_real.add(assistant)
            await db_session_real.flush()
            assistants.append(assistant)

            conversation = Conversation(
                user_id=user.id,
                assistant_id=assistant.id,
            )
            db_session_real.add(conversation)
            await db_session_real.flush()

            # Create conversation history
            history_messages = [
                ChatMessage(
                    conversation_id=conversation.id,
                    sender_role="user",
                    content={
                        "parts": [{"type": "text", "content": "My name is Alice"}]
                    },
                ),
                ChatMessage(
                    conversation_id=conversation.id,
                    sender_role="agent",
                    content={
                        "parts": [
                            {
                                "type": "text",
                                "content": f"Nice to meet you, Alice! I'm {assistant.name}.",
                            }
                        ]
                    },
                ),
            ]

            for msg in history_messages:
                db_session_real.add(msg)
            await db_session_real.flush()

            # Create new user message
            user_message = ChatMessage(
                conversation_id=conversation.id,
                sender_role="user",
                content={"parts": [{"type": "text", "content": "What's my name?"}]},
            )
            db_session_real.add(user_message)
            await db_session_real.commit()

            # Mock PydanticAI Agent and capture the call
            with patch(
                "a2a_platform.services.pydantic_ai_response_service.Agent"
            ) as mock_agent_class:
                mock_result = MagicMock()
                mock_result.output = f"Your name is Alice! (from {provider})"

                mock_agent = AsyncMock()
                mock_agent.run.return_value = mock_result
                mock_agent_class.return_value = mock_agent

                # Test PA response generation
                pa_service = PAResponseService(db_session_real)
                pa_response = await pa_service.generate_and_send_response(
                    conversation_id=conversation.id,
                    user_message=user_message,
                    user_id=user.id,
                )

                # Verify response was generated
                assert pa_response is not None
                assert pa_response.sender_role == "agent"

                # Verify the agent was called with context
                mock_agent.run.assert_called_once()
                call_args = mock_agent.run.call_args[0]

                # The prompt should include conversation context
                prompt = call_args[0]
                assert "Alice" in prompt or "What's my name?" in prompt
