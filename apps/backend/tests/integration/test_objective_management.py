"""
Integration tests for objective management.

These tests verify the functionality of the objective management system,
including storing objectives defined by users via chat and retrieving them.
"""

import uuid
from datetime import datetime

import pytest
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.assistant_objective import AssistantObjective
from a2a_platform.schemas.objective_schemas import ObjectiveCreate
from a2a_platform.services.chat_processor_service import ChatProcessorService
from a2a_platform.services.objective_service import ObjectiveService


@pytest.fixture
async def objective_service(db_session_real: AsyncSession) -> ObjectiveService:
    """Create an ObjectiveService instance with a real database session.

    This fixture is now async to ensure it's created in the same event loop
    as the test function.
    """
    # Create a new service instance with the function-scoped session
    # This ensures the service uses the same event loop as the test
    return ObjectiveService(db_session=db_session_real)


@pytest.fixture
async def chat_processor_service(db_session_real: AsyncSession) -> ChatProcessorService:
    """Create a ChatProcessorService instance with a real database session."""
    return ChatProcessorService(db_session=db_session_real)


@pytest.mark.fast_db
class TestObjectiveManagement:
    """Integration tests for objective management."""

    @pytest.mark.asyncio
    async def test_add_and_retrieve_objective(
        self,
        db_session_real: AsyncSession,
        test_assistant_id: uuid.UUID,
        objective_service: ObjectiveService,
    ):
        """Test adding an objective and then retrieving it."""
        # Use consistent PostgreSQL session for both assistant and objective

        # Arrange
        objective_text = "Learn Spanish fluently"
        objective_data = ObjectiveCreate(
            assistant_id=test_assistant_id,
            objective_text=objective_text,
            status="active",
            metadata={"priority": "high"},
        )

        # Act - Add the objective
        created_objective = await objective_service.add_assistant_objective(
            objective_data
        )

        # Assert - Check the returned object
        assert created_objective is not None
        assert created_objective.assistant_id == test_assistant_id
        assert created_objective.objective_text == objective_text
        assert created_objective.status == "active"
        assert created_objective.metadata_json == {"priority": "high"}
        assert isinstance(created_objective.id, uuid.UUID)
        assert isinstance(created_objective.created_at, datetime)
        assert isinstance(created_objective.updated_at, datetime)

        # Act - Retrieve the objective directly from the database
        stmt = select(AssistantObjective).where(
            AssistantObjective.id == created_objective.id
        )
        result = await db_session_real.execute(stmt)
        retrieved_objective = result.scalar_one_or_none()

        # Assert - Check the retrieved object
        assert retrieved_objective is not None
        assert retrieved_objective.id == created_objective.id
        assert retrieved_objective.assistant_id == test_assistant_id
        assert retrieved_objective.objective_text == objective_text
        assert retrieved_objective.status == "active"
        assert retrieved_objective.metadata_json == {"priority": "high"}

    @pytest.mark.asyncio
    async def test_get_objectives_by_assistant_id(
        self,
        db_session_real: AsyncSession,
        test_assistant_id: uuid.UUID,
        objective_service: ObjectiveService,
    ):
        """Test retrieving objectives for a specific assistant."""
        # The db_session_real fixture now uses the function-scoped engine bound to the test's event loop

        # Arrange - Create multiple objectives
        objective_texts = ["Learn Spanish", "Exercise daily", "Read more books"]
        created_objectives = []

        for text in objective_texts:
            objective_data = ObjectiveCreate(
                assistant_id=test_assistant_id,
                objective_text=text,
                status="active",
            )
            obj = await objective_service.add_assistant_objective(objective_data)
            created_objectives.append(obj)

        # Act - Retrieve objectives for the assistant
        retrieved_objectives = await objective_service.get_objectives_by_assistant_id(
            test_assistant_id
        )

        # Assert
        assert len(retrieved_objectives) == len(objective_texts)
        retrieved_texts = [obj.objective_text for obj in retrieved_objectives]
        for text in objective_texts:
            assert text in retrieved_texts

    @pytest.mark.asyncio
    async def test_get_objectives_by_status(
        self,
        db_session_real: AsyncSession,
        test_assistant_id: uuid.UUID,
        objective_service: ObjectiveService,
    ):
        """Test retrieving objectives filtered by status."""
        # The db_session_real fixture now uses the function-scoped engine bound to the test's event loop

        # Arrange - Create objectives with different statuses
        objectives_data = [
            {"text": "Active objective 1", "status": "active"},
            {"text": "Active objective 2", "status": "active"},
            {"text": "Completed objective", "status": "completed"},
            {"text": "Cancelled objective", "status": "cancelled"},
        ]

        for data in objectives_data:
            objective_data = ObjectiveCreate(
                assistant_id=test_assistant_id,
                objective_text=data["text"],
                status=data["status"],
            )
            await objective_service.add_assistant_objective(objective_data)

        # Act - Retrieve active objectives
        active_objectives = await objective_service.get_objectives_by_assistant_id(
            test_assistant_id, status="active"
        )

        # Assert
        assert len(active_objectives) == 2
        for obj in active_objectives:
            assert obj.status == "active"
            assert "Active objective" in obj.objective_text

        # Act - Retrieve completed objectives
        completed_objectives = await objective_service.get_objectives_by_assistant_id(
            test_assistant_id, status="completed"
        )

        # Assert
        assert len(completed_objectives) == 1
        assert completed_objectives[0].status == "completed"
        assert "Completed objective" in completed_objectives[0].objective_text

    @pytest.mark.asyncio
    async def test_chat_processor_objective_identification_and_storage(
        self,
        db_session_real: AsyncSession,
        test_assistant_id: uuid.UUID,
        chat_processor_service: ChatProcessorService,
    ):
        """
        Test the complete flow of identifying an objective in a chat message
        and storing it in the database.

        This test verifies the integration between the chat processor service
        and the objective service.
        """
        # Arrange
        message_text = "My goal is to learn Spanish fluently"

        # Act - Process the message
        (
            objective_identified,
            stored_objective,
        ) = await chat_processor_service.process_message(
            message_text, test_assistant_id
        )

        # Assert - Verify that an objective was identified and stored
        assert objective_identified is True
        assert stored_objective is not None
        assert stored_objective.assistant_id == test_assistant_id
        assert "learn Spanish fluently" in stored_objective.objective_text
        assert stored_objective.status == "active"

        # Verify the objective was actually stored in the database
        stmt = select(AssistantObjective).where(
            AssistantObjective.id == stored_objective.id
        )
        result = await db_session_real.execute(stmt)
        db_objective = result.scalar_one_or_none()

        assert db_objective is not None
        assert db_objective.id == stored_objective.id
        assert db_objective.assistant_id == test_assistant_id
        assert db_objective.objective_text == stored_objective.objective_text

    @pytest.mark.asyncio
    async def test_chat_processor_multiple_users(
        self,
        db_session_real: AsyncSession,
        chat_processor_service: ChatProcessorService,
    ):
        """
        Test processing chat messages from multiple users (assistants).

        This test verifies that objectives from different assistants are
        correctly stored and linked to the appropriate assistant.
        """
        # Arrange - Create two different users and their assistants
        from a2a_platform.schemas.assistant_schemas import AssistantCreate
        from a2a_platform.schemas.user import UserCreate
        from a2a_platform.services.assistant_service import AssistantService
        from a2a_platform.services.user_service import create_user

        # Create first user and assistant
        user_1_data = UserCreate(
            clerk_user_id=f"test_clerk_user_1_{uuid.uuid4()}",
            email=f"test_1_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user_1 = await create_user(db_session_real, user_1_data)

        assistant_service = AssistantService(db_session=db_session_real)
        assistant_1_data = AssistantCreate(
            name="Test Assistant 1",
            backstory="A test assistant for user 1",
            avatar_file_id=None,
            configuration={},
        )
        assistant_1 = await assistant_service.create_personal_assistant(
            user_id=user_1.id, assistant_data=assistant_1_data
        )

        # Create second user and assistant
        user_2_data = UserCreate(
            clerk_user_id=f"test_clerk_user_2_{uuid.uuid4()}",
            email=f"test_2_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user_2 = await create_user(db_session_real, user_2_data)

        assistant_2_data = AssistantCreate(
            name="Test Assistant 2",
            backstory="A test assistant for user 2",
            avatar_file_id=None,
            configuration={},
        )
        assistant_2 = await assistant_service.create_personal_assistant(
            user_id=user_2.id, assistant_data=assistant_2_data
        )

        # Act - Process messages from both assistants
        message_1 = "My goal is to learn Spanish"
        message_2 = "I want to save money"

        # Process the first message
        result_1, objective_1 = await chat_processor_service.process_message(
            message_1, assistant_1.id
        )

        # Process the second message
        result_2, objective_2 = await chat_processor_service.process_message(
            message_2, assistant_2.id
        )

        # Assert - Verify that objectives were identified and stored for both assistants
        assert result_1 is True
        assert result_2 is True

        assert objective_1 is not None
        assert objective_2 is not None

        # Verify the objectives are linked to the correct assistants
        assert objective_1.assistant_id == assistant_1.id
        assert objective_2.assistant_id == assistant_2.id

        # Verify the objective texts are correct
        assert "learn Spanish" in objective_1.objective_text
        assert "save money" in objective_2.objective_text

        # Verify the objectives were actually stored in the database
        # First objective
        stmt_1 = select(AssistantObjective).where(
            AssistantObjective.id == objective_1.id
        )
        result_1 = await db_session_real.execute(stmt_1)
        db_objective_1 = result_1.scalar_one_or_none()

        assert db_objective_1 is not None
        assert db_objective_1.id == objective_1.id
        assert db_objective_1.assistant_id == assistant_1.id

        # Second objective
        stmt_2 = select(AssistantObjective).where(
            AssistantObjective.id == objective_2.id
        )
        result_2 = await db_session_real.execute(stmt_2)
        db_objective_2 = result_2.scalar_one_or_none()

        assert db_objective_2 is not None
        assert db_objective_2.id == objective_2.id
        assert db_objective_2.assistant_id == assistant_2.id
