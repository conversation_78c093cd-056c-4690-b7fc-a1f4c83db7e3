"""
Implementation of missing test cases for human input workflow:
- TC_US5.5_040: Request Human Input
- TC_US5.5_041: Provide Human Input

These tests verify the human input workflow for tasks.
"""

import uuid

import pytest
from httpx import AsyncClient
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.task import Task
from a2a_platform.schemas.task_schemas import TaskCreate, TaskUpdate


@pytest.mark.asyncio
async def test_request_human_input(
    test_client: AsyncClient,
    test_assistant_id: uuid.UUID,
    db_session_real: AsyncSession,
):
    """
    TC_US5.5_040: Test requesting human input for a task.

    Setup: Create a task.
    Action: PUT /internal/tasks/{task_id} with status: 'pending_human_input' and
           metadata: {"human_input_request": {"query": "Need clarification on X"}}
    Expected: 200 OK. Status is 'pending_human_input'. metadata stores the request details. (AC8)
    """
    # Arrange - Create a task
    task_data = TaskCreate(description="Task requiring human input").model_dump()
    response = await test_client.post(
        "/api/internal/assistants/my/tasks",
        json=task_data,
        headers={"X-Assistant-ID": str(test_assistant_id)},
    )

    assert response.status_code == 201
    task = response.json()
    task_id = task["id"]

    # Act - Set task to pending_human_input with request details
    human_input_request = {
        "query": "Need clarification on X",
        "context": "Additional context for the human",
    }
    update_data = TaskUpdate(
        status="pending_human_input",
        metadata={"human_input_request": human_input_request},
    ).model_dump()

    update_response = await test_client.put(
        f"/api/internal/tasks/{task_id}", json=update_data
    )

    # Assert
    assert update_response.status_code == 200
    updated_task = update_response.json()
    assert updated_task["status"] == "pending_human_input"
    assert "human_input_request" in updated_task["metadata"]
    assert (
        updated_task["metadata"]["human_input_request"]["query"]
        == "Need clarification on X"
    )

    # Verify in database
    query = select(Task).where(Task.id == uuid.UUID(task_id))
    result = await db_session_real.execute(query)
    db_task = result.scalar_one_or_none()

    assert db_task is not None
    assert db_task.status == "pending_human_input"
    assert "human_input_request" in db_task.metadata_json
    assert (
        db_task.metadata_json["human_input_request"]["query"]
        == "Need clarification on X"
    )


@pytest.mark.asyncio
async def test_provide_human_input(
    test_client: AsyncClient,
    test_assistant_id: uuid.UUID,
    db_session_real: AsyncSession,
):
    """
    TC_US5.5_041: Test providing human input for a task.

    Setup: TC_US5.5_040 executed. Task is in 'pending_human_input' state with request.
    Action: PUT /internal/tasks/{task_id} with status: 'todo' and
           metadata that includes human_input_response.
    Expected: 200 OK. Status updated (to 'todo' to be re-picked). metadata stores the response. (AC9)
    """
    # Arrange - Create a task and set it to pending_human_input with request
    task_data = TaskCreate(description="Task awaiting human input").model_dump()
    response = await test_client.post(
        "/api/internal/assistants/my/tasks",
        json=task_data,
        headers={"X-Assistant-ID": str(test_assistant_id)},
    )

    assert response.status_code == 201
    task = response.json()
    task_id = task["id"]

    # Set to pending_human_input with request
    human_input_request = {"query": "Need clarification on X"}
    set_pending_data = TaskUpdate(
        status="pending_human_input",
        metadata={"human_input_request": human_input_request},
    ).model_dump()

    await test_client.put(f"/api/internal/tasks/{task_id}", json=set_pending_data)

    # Act - Provide human input and change status back to todo
    human_input_response = {"answer": "Clarification Y"}
    update_with_response = TaskUpdate(
        status="todo",
        metadata={
            "human_input_request": human_input_request,
            "human_input_response": human_input_response,
        },
    ).model_dump()

    update_response = await test_client.put(
        f"/api/internal/tasks/{task_id}", json=update_with_response
    )

    # Assert
    assert update_response.status_code == 200
    updated_task = update_response.json()
    assert updated_task["status"] == "todo"
    assert "human_input_request" in updated_task["metadata"]
    assert "human_input_response" in updated_task["metadata"]
    assert (
        updated_task["metadata"]["human_input_response"]["answer"] == "Clarification Y"
    )

    # Verify in database
    query = select(Task).where(Task.id == uuid.UUID(task_id))
    result = await db_session_real.execute(query)
    db_task = result.scalar_one_or_none()

    assert db_task is not None
    assert db_task.status == "todo"
    assert "human_input_request" in db_task.metadata_json
    assert "human_input_response" in db_task.metadata_json
    assert db_task.metadata_json["human_input_response"]["answer"] == "Clarification Y"
