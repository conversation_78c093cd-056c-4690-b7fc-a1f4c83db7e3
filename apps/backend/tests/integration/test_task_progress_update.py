"""
Implementation of missing test case TC_US5.5_032: Heartbeat/Progress Update.

This test verifies that a task's last_progress_at timestamp can be updated.
"""

import uuid
from datetime import UTC, datetime

import pytest
from httpx import AsyncClient
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.task import Task
from a2a_platform.schemas.task_schemas import TaskCreate, TaskUpdate


@pytest.mark.asyncio
async def test_task_progress_update(
    test_client: AsyncClient,
    test_assistant_id: uuid.UUID,
    db_session_real: AsyncSession,
):
    """
    TC_US5.5_032: Test task progress update (heartbeat).

    Setup: Create a task, set status to 'in_progress'.
    Action: PUT /internal/tasks/{task_id} with last_progress_at:
    (current_timestamp).
    Expected: 200 OK. last_progress_at and updated_at are updated.
    """
    # Arrange - Create a task and set it to in_progress
    task_data = TaskCreate(description="Task for progress update test").model_dump()
    response = await test_client.post(
        "/api/internal/assistants/my/tasks",
        json=task_data,
        headers={"X-Assistant-ID": str(test_assistant_id)},
    )

    assert response.status_code == 201
    task = response.json()
    task_id = task["id"]

    # Set task to in_progress
    update_data = TaskUpdate(status="in_progress").model_dump()
    update_response = await test_client.put(
        f"/api/internal/tasks/{task_id}", json=update_data
    )
    assert update_response.status_code == 200

    # Act - Update last_progress_at with current timestamp
    current_time = datetime.now(UTC)
    # Convert datetime to ISO format string for JSON serialization
    progress_update = {"last_progress_at": current_time.isoformat()}
    progress_response = await test_client.put(
        f"/api/internal/tasks/{task_id}", json=progress_update
    )

    # Assert
    assert progress_response.status_code == 200
    updated_task = progress_response.json()
    assert updated_task["last_progress_at"] is not None

    # Verify in database
    query = select(Task).where(Task.id == uuid.UUID(task_id))
    result = await db_session_real.execute(query)
    db_task = result.scalar_one_or_none()

    assert db_task is not None
    assert db_task.last_progress_at is not None
    assert db_task.status == "in_progress"
