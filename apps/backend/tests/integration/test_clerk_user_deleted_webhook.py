import base64
import time
import uuid
from unittest.mock import MagicMock, patch

import pytest

from a2a_platform.config import settings

settings.CLERK_WEBHOOK_SECRET = base64.b64encode(b"test_secret").decode()


@pytest.fixture
def clerk_user_deleted_payload():
    """
    Fixture providing a realistic Clerk user.deleted webhook payload.

    This represents the contract with Clerk's webhook service for user.deleted events.
    Reference: https://clerk.com/docs/webhooks/overview
    """
    return {
        "data": {
            "id": "user_29w83sxmDNGwOuEthce5gg56FcC",
            "object": "user",
            "deleted": True,
        },
        "object": "event",
        "timestamp": int(time.time() * 1000),
        "type": "user.deleted",
    }


@pytest.mark.asyncio
async def test_clerk_webhook_handler_user_deleted(test_client):
    """
    Test that the Clerk webhook handler correctly processes a user.deleted event.

    This test verifies the consumer-side contract between our application and Clerk's webhook service.
    It ensures that our application can correctly process a webhook payload that adheres to
    Clerk's documented structure for the user.deleted event.

    Reference: https://clerk.com/docs/webhooks/overview
    """
    # Mock the verify_webhook_signature function to always return True for testing
    clerk_user_id = (
        f"user_{uuid.uuid4()}"  # Clerk user IDs typically start with "user_"
    )

    # Mock the Webhook class to avoid actual signature verification
    with patch("a2a_platform.auth.clerk.Webhook") as mock_webhook_cls:
        # Configure the mock webhook instance
        mock_webhook = MagicMock()
        mock_webhook.verify.return_value = None  # No error means verification passed
        mock_webhook_cls.return_value = mock_webhook

        # Mock the process_user_deleted_task function
        with patch(
            "a2a_platform.api.rest.routes.webhooks.process_user_deleted_task"
        ) as mock_delete_user:
            # Prepare the webhook payload that represents the contract with Clerk
            webhook_payload = {
                "type": "user.deleted",
                "data": {"id": clerk_user_id, "deleted": True, "object": "user"},
                "object": "event",
                "timestamp": int(time.time() * 1000),
            }

            # Configure the mock to return a coroutine
            async def mock_coro(*_args, **_kwargs):
                return True

            mock_delete_user.side_effect = mock_coro

            # Make the request with minimal required headers
            response = await test_client.post(
                "/api/v1/webhooks/clerk",
                json=webhook_payload,
                headers={
                    "svix-signature": "v1,test_signature",
                    "svix-timestamp": str(int(time.time())),
                    "svix-id": f"msg_{uuid.uuid4().hex}",
                },
            )

            # Verify the mock was called with the correct arguments
            mock_webhook.verify.assert_called_once()
            mock_delete_user.assert_called_once()

            # Verify the response was successful
            assert response.status_code == 200
            response_json = response.json()
            assert response_json["success"] is True
            assert "user.deleted event" in response_json["message"]


@pytest.mark.asyncio
async def test_clerk_webhook_handler_with_real_deleted_payload(
    test_client, clerk_user_deleted_payload
):
    """
    Test that the Clerk webhook handler correctly processes a real user.deleted event payload.

    This test uses a realistic payload structure from Clerk's documentation to ensure
    our webhook handler can process actual production payloads.
    """
    # Extract payload data for logging/debugging if needed
    _ = clerk_user_deleted_payload["data"]["id"]

    # Mock the Webhook class to avoid actual signature verification
    with patch("a2a_platform.auth.clerk.Webhook") as mock_webhook_cls:
        # Configure the mock webhook instance
        mock_webhook = MagicMock()
        mock_webhook.verify.return_value = None  # No error means verification passed
        mock_webhook_cls.return_value = mock_webhook

        # Mock the process_user_deleted_task function
        with patch(
            "a2a_platform.api.rest.routes.webhooks.process_user_deleted_task"
        ) as mock_delete_user:
            # Configure the mock to return a coroutine
            async def mock_coro(*_args, **_kwargs):
                return True

            mock_delete_user.side_effect = mock_coro

            # Make the request with minimal required headers
            response = await test_client.post(
                "/api/v1/webhooks/clerk",
                json=clerk_user_deleted_payload,
                headers={
                    "svix-signature": "v1,test_signature",
                    "svix-timestamp": str(int(time.time())),
                    "svix-id": f"msg_{uuid.uuid4().hex}",
                },
            )

            # Verify the mock was called with the correct arguments
            mock_webhook.verify.assert_called_once()
            mock_delete_user.assert_called_once()

            # Verify the response was successful
            assert response.status_code == 200
            response_json = response.json()
            assert response_json["success"] is True
            assert "user.deleted event" in response_json["message"]


@pytest.mark.asyncio
async def test_clerk_webhook_invalid_user_deleted_data(test_client):
    """
    Test that the Clerk webhook handler correctly rejects invalid user.deleted event data.

    This test verifies that our application properly validates the webhook payload
    and rejects requests with invalid data.
    """
    # Mock the Webhook class to avoid actual signature verification
    with patch("a2a_platform.auth.clerk.Webhook") as mock_webhook_cls:
        # Configure the mock webhook instance
        mock_webhook = MagicMock()
        mock_webhook.verify.return_value = None  # No error means verification passed
        mock_webhook_cls.return_value = mock_webhook

        # Prepare an invalid webhook payload (missing required 'id' field)
        webhook_payload = {
            "type": "user.deleted",
            "data": {
                # Missing 'id' field
                "deleted": True
            },
            "object": "event",
            "timestamp": int(time.time() * 1000),
        }

        # Make the request with minimal required headers
        response = await test_client.post(
            "/api/v1/webhooks/clerk",
            json=webhook_payload,
            headers={
                "svix-signature": "v1,test_signature",
                "svix-timestamp": str(int(time.time())),
                "svix-id": f"msg_{uuid.uuid4().hex}",
            },
        )

        # Verify the response indicates an error
        assert response.status_code == 400
        response_json = response.json()
        # With our optimized webhook handler, the error message format has changed
        # from "Invalid user.deleted event data" to "Missing required 'id' field in user.deleted event data"
        assert "Missing required 'id' field" in response_json["detail"]
