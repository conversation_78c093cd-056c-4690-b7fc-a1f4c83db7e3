"""
Integration tests for Assistant-User relationship and setup status.

These tests verify the relationship between users and their personal assistants,
and how the system determines PA setup completion status.

Test IDs covered:
- API-TC-010: User Profile Update - Verify assistant relationship
- Additional tests for setup status determination

These tests are SQLite-compatible and focus on basic user-assistant relationships,
CRUD operations, and foreign key constraints that work identically in both
SQLite and PostgreSQL.
"""

import uuid

import pytest
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.assistant import Assistant
from a2a_platform.schemas.assistant_schemas import AssistantCreate
from a2a_platform.schemas.user import UserCreate
from a2a_platform.services.assistant_service import AssistantService
from a2a_platform.services.user_service import create_user

# Import SQLite fixtures
pytest_plugins = ["tests.conftest_sqlite"]


@pytest.mark.fast_db
class TestAssistantUserRelationship:
    """Tests for the relationship between users and their personal assistants."""

    @pytest.mark.asyncio
    async def test_user_assistant_relationship_creation(
        self, db_session_real: AsyncSession
    ):
        """
        API-TC-010: User Profile Update - Verify assistant relationship.

        Tests that creating a personal assistant properly establishes the
        relationship with the user and can be accessed via the user model.
        """
        # Arrange - Create a test user
        user_data = UserCreate(
            clerk_user_id=f"test_relationship_{uuid.uuid4()}",
            email=f"test_relationship_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user = await create_user(db_session_real, user_data)

        # Verify user initially has no assistant
        assistant_service = AssistantService(db_session=db_session_real)
        initial_assistant = await assistant_service.get_user_assistant(user.id)
        assert initial_assistant is None, (
            "New user should not have an assistant initially"
        )

        # Act - Create an assistant for the user
        assistant_service = AssistantService(db_session=db_session_real)
        assistant_data = AssistantCreate(
            name="Relationship Test Assistant",
            backstory="Testing user-assistant relationship",
            avatar_file_id=None,
            configuration={"test": True},
        )

        assistant = await assistant_service.create_personal_assistant(
            user_id=user.id, assistant_data=assistant_data
        )

        # Assert - Verify the relationship is established

        # 1. Assistant should reference the user
        assert assistant.user_id == user.id
        assert assistant.user is not None

        # 2. User should reference the assistant (verify via service)
        user_assistant = await assistant_service.get_user_assistant(user.id)
        assert user_assistant is not None
        assert user_assistant.id == assistant.id
        assert user_assistant.name == "Relationship Test Assistant"

        # 3. Verify the relationship works both ways
        assert assistant.user_id == user.id
        # Note: assistant.user relationship requires explicit loading in async context

    @pytest.mark.asyncio
    async def test_pa_setup_status_determination(self, db_session_real: AsyncSession):
        """
        Test determining PA setup completion status based on assistant existence.

        Since there's no explicit is_pa_setup_complete field, the system should
        determine setup status by checking if the user has an assistant.
        """
        # Arrange - Create two test users
        user_without_pa_data = UserCreate(
            clerk_user_id=f"test_no_pa_{uuid.uuid4()}",
            email=f"test_no_pa_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user_without_pa = await create_user(db_session_real, user_without_pa_data)

        user_with_pa_data = UserCreate(
            clerk_user_id=f"test_with_pa_{uuid.uuid4()}",
            email=f"test_with_pa_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user_with_pa = await create_user(db_session_real, user_with_pa_data)

        # Act - Create assistant for one user only
        assistant_service = AssistantService(db_session=db_session_real)
        assistant_data = AssistantCreate(
            name="Setup Status Test Assistant",
            backstory="Testing setup status determination",
            avatar_file_id=None,
        )

        await assistant_service.create_personal_assistant(
            user_id=user_with_pa.id, assistant_data=assistant_data
        )

        # Assert - Verify setup status can be determined

        # User without PA should show setup incomplete
        assistant_service_check = AssistantService(db_session=db_session_real)
        no_pa_assistant = await assistant_service_check.get_user_assistant(
            user_without_pa.id
        )
        assert no_pa_assistant is None
        # This indicates PA setup is NOT complete

        # User with PA should show setup complete
        with_pa_assistant = await assistant_service_check.get_user_assistant(
            user_with_pa.id
        )
        assert with_pa_assistant is not None
        # This indicates PA setup IS complete

    @pytest.mark.asyncio
    async def test_assistant_deletion_updates_user_relationship(
        self, db_session_real: AsyncSession
    ):
        """
        Test that deleting an assistant properly updates the user relationship.

        Verifies that when an assistant is deleted, the user's assistant
        relationship becomes None, indicating setup is no longer complete.
        """
        # Arrange - Create user with assistant
        user_data = UserCreate(
            clerk_user_id=f"test_deletion_{uuid.uuid4()}",
            email=f"test_deletion_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user = await create_user(db_session_real, user_data)

        assistant_service = AssistantService(db_session=db_session_real)
        assistant_data = AssistantCreate(
            name="Deletion Test Assistant",
            backstory="Testing assistant deletion",
            avatar_file_id=None,
        )

        assistant = await assistant_service.create_personal_assistant(
            user_id=user.id, assistant_data=assistant_data
        )

        # Verify assistant exists
        existing_assistant = await assistant_service.get_user_assistant(user.id)
        assert existing_assistant is not None

        # Act - Delete the assistant
        success = await assistant_service.delete_assistant(
            assistant_id=assistant.id, user_id=user.id
        )
        assert success, "Assistant deletion should succeed"

        # Assert - Verify relationship is updated
        deleted_check = await assistant_service.get_user_assistant(user.id)
        assert deleted_check is None, "User should have no assistant after deletion"

        # Verify assistant is actually deleted from database
        result = await db_session_real.execute(
            select(Assistant).where(Assistant.id == assistant.id)
        )
        deleted_assistant = result.scalar_one_or_none()
        assert deleted_assistant is None, "Assistant should be deleted from database"

    @pytest.mark.asyncio
    async def test_user_deletion_cascades_to_assistant(
        self, db_session_real: AsyncSession
    ):
        """
        Test that deleting a user cascades to delete their assistant.

        Verifies the CASCADE delete constraint works properly.
        """
        # Arrange - Create user with assistant
        user_data = UserCreate(
            clerk_user_id=f"test_cascade_{uuid.uuid4()}",
            email=f"test_cascade_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user = await create_user(db_session_real, user_data)

        assistant_service = AssistantService(db_session=db_session_real)
        assistant_data = AssistantCreate(
            name="Cascade Test Assistant",
            backstory="Testing cascade deletion",
            avatar_file_id=None,
        )

        assistant = await assistant_service.create_personal_assistant(
            user_id=user.id, assistant_data=assistant_data
        )
        assistant_id = assistant.id

        # Act - Delete the user
        await db_session_real.delete(user)
        await db_session_real.commit()

        # Assert - Verify assistant is also deleted (cascade)
        result = await db_session_real.execute(
            select(Assistant).where(Assistant.id == assistant_id)
        )
        cascaded_assistant = result.scalar_one_or_none()
        assert cascaded_assistant is None, (
            "Assistant should be deleted when user is deleted (CASCADE)"
        )

    @pytest.mark.asyncio
    async def test_multiple_users_separate_assistants(
        self, db_session_real: AsyncSession
    ):
        """
        Test that multiple users can each have their own assistant.

        Verifies the one-to-one relationship works correctly across multiple users.
        """
        # Arrange - Create multiple users
        users = []
        assistants = []

        for i in range(3):
            user_data = UserCreate(
                clerk_user_id=f"test_multi_user_{i}_{uuid.uuid4()}",
                email=f"test_multi_user_{i}_{uuid.uuid4()}@example.com",
                timezone="UTC",
            )
            user = await create_user(db_session_real, user_data)
            users.append(user)

            # Create assistant for each user
            assistant_service = AssistantService(db_session=db_session_real)
            assistant_data = AssistantCreate(
                name=f"Assistant {i}",
                backstory=f"Assistant for user {i}",
                avatar_file_id=None,
            )

            assistant = await assistant_service.create_personal_assistant(
                user_id=user.id, assistant_data=assistant_data
            )
            assistants.append(assistant)

        # Assert - Verify each user has their own assistant
        for i, user in enumerate(users):
            user_assistant = await assistant_service.get_user_assistant(user.id)
            assert user_assistant is not None
            assert user_assistant.id == assistants[i].id
            assert user_assistant.name == f"Assistant {i}"
            assert user_assistant.user_id == user.id

        # Verify assistants are distinct
        assistant_ids = [assistant.id for assistant in assistants]
        assert len(set(assistant_ids)) == 3, "All assistants should have unique IDs"

        # Verify each assistant belongs to the correct user
        for i, assistant in enumerate(assistants):
            assert assistant.user_id == users[i].id
            assert assistant.user.id == users[i].id

    @pytest.mark.asyncio
    async def test_assistant_service_get_user_assistant(
        self, db_session_real: AsyncSession
    ):
        """
        Test the AssistantService.get_user_assistant method.

        Verifies that the service method correctly retrieves a user's assistant.
        """
        # Arrange - Create user with assistant
        user_data = UserCreate(
            clerk_user_id=f"test_get_assistant_{uuid.uuid4()}",
            email=f"test_get_assistant_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user = await create_user(db_session_real, user_data)

        assistant_service = AssistantService(db_session=db_session_real)
        assistant_data = AssistantCreate(
            name="Get Assistant Test",
            backstory="Testing get_user_assistant method",
            avatar_file_id=None,
        )

        created_assistant = await assistant_service.create_personal_assistant(
            user_id=user.id, assistant_data=assistant_data
        )

        # Act - Get the assistant using the service method
        retrieved_assistant = await assistant_service.get_user_assistant(user.id)

        # Assert - Verify correct assistant is retrieved
        assert retrieved_assistant is not None
        assert retrieved_assistant.id == created_assistant.id
        assert retrieved_assistant.name == "Get Assistant Test"
        assert retrieved_assistant.user_id == user.id

        # Test with user who has no assistant
        user_no_assistant_data = UserCreate(
            clerk_user_id=f"test_no_assistant_{uuid.uuid4()}",
            email=f"test_no_assistant_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user_no_assistant = await create_user(db_session_real, user_no_assistant_data)

        no_assistant = await assistant_service.get_user_assistant(user_no_assistant.id)
        assert no_assistant is None, "Should return None for user with no assistant"
