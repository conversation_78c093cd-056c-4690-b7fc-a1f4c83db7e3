"""
Integration tests for the chat processor service and objective storage.

These tests verify that objectives identified in chat messages are correctly
stored in the database.
"""

import uuid
from datetime import datetime

import pytest
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.assistant_objective import AssistantObjective
from a2a_platform.services.chat_processor_service import ChatProcessorService


@pytest.fixture
async def chat_processor_service(db_session_real: AsyncSession) -> ChatProcessorService:
    """Create a ChatProcessorService instance with a real database session."""
    return ChatProcessorService(db_session=db_session_real)


class TestChatObjectiveIntegration:
    """Integration tests for chat objective identification and storage."""

    @pytest.mark.asyncio
    async def test_objective_identification_and_storage(
        self,
        db_session_real: AsyncSession,
        test_assistant_id: uuid.UUID,
        chat_processor_service: ChatProcessorService,
    ):
        """Test that an objective identified in a chat message is stored in the database."""
        # Arrange
        message = "My goal is to read one book this month"

        # Act
        result, stored_objective = await chat_processor_service.process_message(
            message, test_assistant_id
        )

        # Assert - Check the result
        assert result is True
        assert stored_objective is not None
        assert stored_objective.assistant_id == test_assistant_id
        assert stored_objective.objective_text == "read one book this month"
        assert stored_objective.status == "active"
        assert isinstance(stored_objective.id, uuid.UUID)
        assert isinstance(stored_objective.created_at, datetime)
        assert isinstance(stored_objective.updated_at, datetime)

        # Act - Retrieve the objective directly from the database to verify it was stored
        stmt = select(AssistantObjective).where(
            AssistantObjective.id == stored_objective.id
        )
        result = await db_session_real.execute(stmt)
        retrieved_objective = result.scalar_one_or_none()

        # Assert - Check the retrieved object
        assert retrieved_objective is not None
        assert retrieved_objective.id == stored_objective.id
        assert retrieved_objective.assistant_id == test_assistant_id
        assert retrieved_objective.objective_text == "read one book this month"
        assert retrieved_objective.status == "active"

    @pytest.mark.asyncio
    async def test_multiple_users_objectives(
        self,
        db_session_real: AsyncSession,
        chat_processor_service: ChatProcessorService,
    ):
        """Test that objectives from different users (assistants) are stored correctly."""
        # Arrange - Create two different users and their assistants
        from a2a_platform.schemas.assistant_schemas import AssistantCreate
        from a2a_platform.schemas.user import UserCreate
        from a2a_platform.services.assistant_service import AssistantService
        from a2a_platform.services.user_service import create_user

        # Create first user and assistant
        user_a_data = UserCreate(
            clerk_user_id=f"test_clerk_user_a_{uuid.uuid4()}",
            email=f"test_a_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user_a = await create_user(db_session_real, user_a_data)

        assistant_service = AssistantService(db_session=db_session_real)
        assistant_a_data = AssistantCreate(
            name="Test Assistant A",
            backstory="A test assistant for user A",
            avatar_file_id=None,
            configuration={},
        )
        assistant_a = await assistant_service.create_personal_assistant(
            user_id=user_a.id, assistant_data=assistant_a_data
        )

        # Create second user and assistant
        user_b_data = UserCreate(
            clerk_user_id=f"test_clerk_user_b_{uuid.uuid4()}",
            email=f"test_b_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user_b = await create_user(db_session_real, user_b_data)

        assistant_b_data = AssistantCreate(
            name="Test Assistant B",
            backstory="A test assistant for user B",
            avatar_file_id=None,
            configuration={},
        )
        assistant_b = await assistant_service.create_personal_assistant(
            user_id=user_b.id, assistant_data=assistant_b_data
        )

        # Act - Process messages from both assistants
        message_a = "I want to save $500"
        result_a, objective_a = await chat_processor_service.process_message(
            message_a, assistant_a.id
        )

        message_b = "Help me learn to play guitar"
        result_b, objective_b = await chat_processor_service.process_message(
            message_b, assistant_b.id
        )

        # Assert - Check both objectives were stored
        assert result_a is True
        assert objective_a is not None
        assert objective_a.assistant_id == assistant_a.id
        assert objective_a.objective_text == "save $500"

        assert result_b is True
        assert objective_b is not None
        assert objective_b.assistant_id == assistant_b.id
        assert objective_b.objective_text == "learn to play guitar"

        # Act - Retrieve objectives for assistant A
        stmt_a = select(AssistantObjective).where(
            AssistantObjective.assistant_id == assistant_a.id
        )
        result_a = await db_session_real.execute(stmt_a)
        objectives_a = result_a.scalars().all()

        # Act - Retrieve objectives for assistant B
        stmt_b = select(AssistantObjective).where(
            AssistantObjective.assistant_id == assistant_b.id
        )
        result_b = await db_session_real.execute(stmt_b)
        objectives_b = result_b.scalars().all()

        # Assert - Check each assistant has their own objectives
        assert len(list(objectives_a)) == 1
        assert len(list(objectives_b)) == 1
        assert objectives_a[0].objective_text == "save $500"
        assert objectives_b[0].objective_text == "learn to play guitar"

    @pytest.mark.asyncio
    async def test_non_objective_message(
        self,
        db_session_real: AsyncSession,
        test_assistant_id: uuid.UUID,
        chat_processor_service: ChatProcessorService,
    ):
        """Test that a message without an objective doesn't create a database record."""
        # Arrange
        message = "Hello, how are you today?"

        # Act
        result, stored_objective = await chat_processor_service.process_message(
            message, test_assistant_id
        )

        # Assert
        assert result is False
        assert stored_objective is None

        # Act - Check if any objectives were stored for this assistant
        stmt = select(AssistantObjective).where(
            AssistantObjective.assistant_id == test_assistant_id
        )
        result = await db_session_real.execute(stmt)
        objectives = result.scalars().all()

        # Assert - No objectives should have been stored
        assert len(list(objectives)) == 0
