"""
Integration tests for the internal task management feature.

These tests verify the functionality of the internal task management API endpoints
and the underlying database operations as specified in US5.5.
"""

import uuid
from datetime import UTC, datetime, timedelta

import pytest
from httpx import AsyncClient
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.assistant_objective import AssistantObjective
from a2a_platform.db.models.task import Task
from a2a_platform.schemas.objective_schemas import ObjectiveCreate
from a2a_platform.schemas.task_schemas import TaskCreate
from a2a_platform.services.objective_service import ObjectiveService
from a2a_platform.services.task_service import TaskService


@pytest.fixture
async def task_service(db_session_real: AsyncSession) -> TaskService:
    """Create a TaskService instance with a real database session."""
    return TaskService(session=db_session_real)


@pytest.fixture
async def objective_service(db_session_real: AsyncSession) -> ObjectiveService:
    """Create an ObjectiveService instance with a real database session."""
    return ObjectiveService(db_session=db_session_real)


@pytest.fixture
async def test_objective(
    test_assistant_id: uuid.UUID, objective_service: ObjectiveService
) -> AssistantObjective:
    """Create a test objective for testing."""
    objective_data = ObjectiveCreate(
        assistant_id=test_assistant_id,
        objective_text="Test objective for task management",
        status="active",
    )
    return await objective_service.add_assistant_objective(objective_data)


# Use the test_client fixture from conftest.py


@pytest.mark.fast_db
class TestInternalTaskManagement:
    """Integration tests for the internal task management feature."""

    @pytest.mark.asyncio
    async def test_task_service_direct(
        self,
        task_service: TaskService,
        test_assistant_id: uuid.UUID,
        db_session_real: AsyncSession,
    ):
        """Test creating a task directly with TaskService."""
        # Create a task using the service directly
        task_data = TaskCreate(description="Direct TaskService test")

        task = await task_service.create_task(
            assistant_id=test_assistant_id, task_data=task_data
        )

        # Verify the task was created
        assert task is not None
        assert task.description == "Direct TaskService test"
        assert task.assistant_id == test_assistant_id
        assert task.status == "todo"

    @pytest.mark.asyncio
    async def test_simple_task_creation(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
        db_session_real: AsyncSession,
    ):
        """
        TC_US5.5_001: Test creating a simple task.

        Action: POST /internal/assistants/my/tasks with valid description.
        Expected: 201 Created. New task record in tasks with status='todo', correct assistant_id,
                 description, created_at, updated_at. idempotency_key is NULL.
        """
        # Arrange
        task_data = TaskCreate(description="Test simple task creation").model_dump()

        # Act
        response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        # Assert
        print(f"Response: {response.status_code} - {response.text}")
        assert response.status_code == 201
        task_response = response.json()
        assert task_response["description"] == task_data["description"]
        assert task_response["status"] == "todo"
        assert task_response["assistant_id"] == str(test_assistant_id)
        assert "created_at" in task_response
        assert "updated_at" in task_response

        # Verify in database
        task_id = uuid.UUID(task_response["id"])
        query = select(Task).where(Task.id == task_id)
        result = await db_session_real.execute(query)
        task = result.scalar_one_or_none()

        assert task is not None
        assert task.description == task_data["description"]
        assert task.status == "todo"
        assert task.assistant_id == test_assistant_id
        assert task.idempotency_key is None

    @pytest.mark.asyncio
    async def test_create_task_with_parent(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
        db_session_real: AsyncSession,
    ):
        """
        TC_US5.5_002: Test creating a task with a parent task.

        Setup: Create a parent task.
        Action: POST /internal/assistants/my/tasks with description and valid parent_task_id.
        Expected: 201 Created. New task linked to parent_task_id.
        """
        # Arrange - Create parent task
        parent_task_data = TaskCreate(description="Parent task").model_dump()

        parent_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=parent_task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        assert parent_response.status_code == 201
        parent_task_id = parent_response.json()["id"]

        # Act - Create child task
        child_task_data = TaskCreate(
            description="Child task", parent_task_id=uuid.UUID(parent_task_id)
        ).model_dump()

        # Convert UUID to string for JSON serialization
        if (
            "parent_task_id" in child_task_data
            and child_task_data["parent_task_id"] is not None
        ):
            child_task_data["parent_task_id"] = str(child_task_data["parent_task_id"])

        child_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=child_task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        # Assert
        assert child_response.status_code == 201
        child_task = child_response.json()
        assert child_task["description"] == child_task_data["description"]
        assert child_task["parent_task_id"] == parent_task_id

        # Verify in database
        child_task_id = uuid.UUID(child_task["id"])
        query = select(Task).where(Task.id == child_task_id)
        result = await db_session_real.execute(query)
        db_child_task = result.scalar_one_or_none()

        assert db_child_task is not None
        assert db_child_task.parent_task_id == uuid.UUID(parent_task_id)

    @pytest.mark.asyncio
    async def test_create_task_with_objective(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
        test_objective: AssistantObjective,
        db_session_real: AsyncSession,
    ):
        """
        TC_US5.5_003: Test creating a task with an objective.

        Setup: Create an objective.
        Action: POST /internal/assistants/my/tasks with description and valid objective_id.
        Expected: 201 Created. New task linked to objective_id.
        """
        # Arrange
        task_data = TaskCreate(
            description="Task with objective", objective_id=test_objective.id
        ).model_dump()

        # Convert UUID to string for JSON serialization
        if "objective_id" in task_data and task_data["objective_id"] is not None:
            task_data["objective_id"] = str(task_data["objective_id"])

        # Act
        response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        # Assert
        assert response.status_code == 201
        task = response.json()
        assert task["description"] == task_data["description"]
        assert task["objective_id"] == str(test_objective.id)

        # Verify in database
        task_id = uuid.UUID(task["id"])
        query = select(Task).where(Task.id == task_id)
        result = await db_session_real.execute(query)
        db_task = result.scalar_one_or_none()

        assert db_task is not None
        assert db_task.objective_id == test_objective.id

    @pytest.mark.asyncio
    async def test_idempotent_task_creation_first_call(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
        db_session_real: AsyncSession,
    ):
        """
        TC_US5.5_004: Test idempotent task creation - first call.

        Action: POST /internal/assistants/my/tasks with description and a new X-Idempotency-Key.
        Expected: 201 Created. New task record, idempotency_key field populated.
        """
        # Arrange
        idempotency_key = str(uuid.uuid4())
        task_data = TaskCreate(description="Idempotent task creation").model_dump()

        # Act
        response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data,
            headers={
                "X-Assistant-ID": str(test_assistant_id),
                "X-Idempotency-Key": idempotency_key,
            },
        )

        # Assert
        assert response.status_code == 201
        task = response.json()
        assert task["description"] == task_data["description"]

        # Verify in database
        task_id = uuid.UUID(task["id"])
        query = select(Task).where(Task.id == task_id)
        result = await db_session_real.execute(query)
        db_task = result.scalar_one_or_none()

        assert db_task is not None
        assert db_task.idempotency_key == idempotency_key

    @pytest.mark.asyncio
    async def test_idempotent_task_creation_second_call(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
        db_session_real: AsyncSession,
    ):
        """
        TC_US5.5_005: Test idempotent task creation - second call with same key and active task.

        Setup: TC_US5.5_004 executed.
        Action: Repeat POST with the exact same payload and X-Idempotency-Key while the task is in a non-terminal state.
        Expected: 200 OK. Returns details of the existing task created in TC_US5.5_004. No new task created.
        """
        # Arrange - Create first task with idempotency key
        idempotency_key = str(uuid.uuid4())
        task_data = TaskCreate(description="Idempotent task - second call").model_dump()

        first_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data,
            headers={
                "X-Assistant-ID": str(test_assistant_id),
                "X-Idempotency-Key": idempotency_key,
            },
        )

        assert first_response.status_code == 201
        first_task = first_response.json()
        first_task_id = first_task["id"]

        # Get count of tasks before second call
        query = select(Task).where(Task.assistant_id == test_assistant_id)
        result = await db_session_real.execute(query)
        tasks_before = result.scalars().all()
        count_before = len(tasks_before)

        # Wait for 2 seconds to ensure the created_at timestamp is different enough
        import asyncio

        await asyncio.sleep(2)

        # Act - Make second call with same idempotency key
        second_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data,
            headers={
                "X-Assistant-ID": str(test_assistant_id),
                "X-Idempotency-Key": idempotency_key,
            },
        )

        # Assert
        assert (
            second_response.status_code == 200
        )  # Not 201, as it's returning existing task
        second_task = second_response.json()
        assert second_task["id"] == first_task_id  # Same task ID

        # Verify no new task was created
        query = select(Task).where(Task.assistant_id == test_assistant_id)
        result = await db_session_real.execute(query)
        tasks_after = result.scalars().all()
        count_after = len(tasks_after)

        assert count_after == count_before  # No new task created

    @pytest.mark.asyncio
    async def test_idempotent_task_creation_different_payload(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
        db_session_real: AsyncSession,
    ):
        """
        TC_US5.5_006: Test idempotent task creation - second call with same key but different payload.

        Setup: TC_US5.5_004 executed.
        Action: Repeat POST with the same X-Idempotency-Key but different description while the task is active.
        Expected: 200 OK. Returns details of the existing task created in TC_US5.5_004.
        """
        # Arrange - Create first task with idempotency key
        idempotency_key = str(uuid.uuid4())
        first_task_data = TaskCreate(
            description="Original task description"
        ).model_dump()

        first_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=first_task_data,
            headers={
                "X-Assistant-ID": str(test_assistant_id),
                "X-Idempotency-Key": idempotency_key,
            },
        )

        assert first_response.status_code == 201
        first_task = first_response.json()
        first_task_id = first_task["id"]

        # Act - Make second call with same idempotency key but different payload
        second_task_data = TaskCreate(
            description="Different task description"
        ).model_dump()

        second_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=second_task_data,
            headers={
                "X-Assistant-ID": str(test_assistant_id),
                "X-Idempotency-Key": idempotency_key,
            },
        )

        # Assert
        assert second_response.status_code == 200
        second_task = second_response.json()
        assert second_task["id"] == first_task_id  # Same task ID
        assert (
            second_task["description"] == first_task_data["description"]
        )  # Original description

        # Verify in database that the task wasn't updated
        task_id = uuid.UUID(first_task_id)
        query = select(Task).where(Task.id == task_id)
        result = await db_session_real.execute(query)
        db_task = result.scalar_one_or_none()

        assert db_task is not None
        assert (
            db_task.description == first_task_data["description"]
        )  # Original description

    @pytest.mark.asyncio
    async def test_idempotent_task_creation_terminal_task(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
        db_session_real: AsyncSession,
    ):
        """
        TC_US5.5_007: Test idempotent task creation - key for terminal task.

        Setup: Create a task with an idempotency key, then update its status to 'done'.
        Action: POST using the same X-Idempotency-Key again.
        Expected: 201 Created. New task created.
        """
        # Arrange - Create first task with idempotency key
        idempotency_key = str(uuid.uuid4())
        task_data = TaskCreate(description="Task to be completed").model_dump()

        first_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data,
            headers={
                "X-Assistant-ID": str(test_assistant_id),
                "X-Idempotency-Key": idempotency_key,
            },
        )

        assert first_response.status_code == 201
        first_task = first_response.json()
        first_task_id = first_task["id"]

        # Update task to 'done' status
        update_response = await test_client.put(
            f"/api/internal/tasks/{first_task_id}", json={"status": "done"}
        )

        assert update_response.status_code == 200

        # Act - Make second call with same idempotency key after task is in terminal state
        second_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data,
            headers={
                "X-Assistant-ID": str(test_assistant_id),
                "X-Idempotency-Key": idempotency_key,
            },
        )

        # Assert
        assert second_response.status_code == 201  # New task created
        second_task = second_response.json()
        assert second_task["id"] != first_task_id  # Different task ID

    @pytest.mark.asyncio
    async def test_create_task_missing_description(
        self, test_client: AsyncClient, test_assistant_id: uuid.UUID
    ):
        """
        TC_US5.5_008: Test create task with missing description.

        Action: POST /internal/assistants/my/tasks without description.
        Expected: 422 Unprocessable Entity.
        """
        # Arrange
        task_data = {}  # Missing required description

        # Act
        response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        # Assert
        assert response.status_code == 422  # Validation error

    @pytest.mark.asyncio
    async def test_create_task_invalid_parent_id(
        self, test_client: AsyncClient, test_assistant_id: uuid.UUID
    ):
        """
        TC_US5.5_009: Test create task with invalid parent_task_id.

        Action: POST /internal/assistants/my/tasks with a non-existent UUID for parent_task_id.
        Expected: 4xx error.
        """
        # Arrange
        non_existent_id = str(uuid.uuid4())
        task_data = TaskCreate(
            description="Task with invalid parent",
            parent_task_id=uuid.UUID(non_existent_id),
        ).model_dump()

        # Convert UUID to string for JSON serialization
        if "parent_task_id" in task_data and task_data["parent_task_id"] is not None:
            task_data["parent_task_id"] = str(task_data["parent_task_id"])

        # Act
        response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        # Assert
        # Could be 404 Not Found or 422 Unprocessable Entity depending on implementation
        assert response.status_code >= 400 and response.status_code < 500

    @pytest.mark.asyncio
    async def test_create_task_with_initial_status(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
        db_session_real: AsyncSession,
    ):
        """
        TC_US5.5_010: Test create task with initial status.

        Action: POST /internal/assistants/my/tasks with description and initial_status: 'pending_dependency'.
        Expected: 201 Created. Task status is 'todo' if no dependency, 'pending_dependency' if depends_on_task_id is set.
        """
        # Arrange
        task_data = TaskCreate(
            description="Task with initial status", initial_status="pending_dependency"
        ).model_dump()

        # Act
        response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        # Assert
        assert response.status_code == 201
        task = response.json()
        assert task["description"] == task_data["description"]
        # Should be 'todo' since no depends_on_task_id
        assert task["status"] == "todo"

    @pytest.mark.asyncio
    async def test_dependent_task_creation(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
        db_session_real: AsyncSession,
    ):
        """
        TC_US5.5_050: Test dependent task creation.

        Setup: Create prerequisite_task_1.
        Action: POST /internal/assistants/my/tasks for dependent_task_2 with
               depends_on_task_id: prerequisite_task_1.id and initial_status: 'pending_dependency'.
        Expected: 201 Created. dependent_task_2 status is 'pending_dependency'.
        """
        # Arrange - Create prerequisite task
        prerequisite_task_data = TaskCreate(
            description="Prerequisite task"
        ).model_dump()

        prerequisite_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=prerequisite_task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        assert prerequisite_response.status_code == 201
        prerequisite_task_id = prerequisite_response.json()["id"]

        # Act - Create dependent task
        dependent_task_data = TaskCreate(
            description="Dependent task",
            depends_on_task_id=uuid.UUID(prerequisite_task_id),
            initial_status="pending_dependency",
        ).model_dump()
        # Ensure UUID is string
        dependent_task_data["depends_on_task_id"] = str(
            dependent_task_data["depends_on_task_id"]
        )

        dependent_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=dependent_task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        # Assert
        assert dependent_response.status_code == 201
        dependent_task = dependent_response.json()
        assert dependent_task["description"] == dependent_task_data["description"]
        assert dependent_task["depends_on_task_id"] == prerequisite_task_id
        assert dependent_task["status"] == "pending_dependency"

        # Verify in database
        dependent_task_id = uuid.UUID(dependent_task["id"])
        query = select(Task).where(Task.id == dependent_task_id)
        result = await db_session_real.execute(query)
        db_dependent_task = result.scalar_one_or_none()

        assert db_dependent_task is not None
        assert db_dependent_task.depends_on_task_id == uuid.UUID(prerequisite_task_id)
        assert db_dependent_task.status == "pending_dependency"

    @pytest.mark.asyncio
    async def test_prerequisite_completes_dependent_unblocks(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
        db_session_real: AsyncSession,
    ):
        """
        TC_US5.5_052: Test prerequisite completes, dependent unblocks.

        Setup: Create prerequisite and dependent tasks.
        Action: Update prerequisite_task_1 status to 'done'.
        Expected: dependent_task_2 status transitions from 'pending_dependency' to 'todo'.
        """
        # Arrange - Create prerequisite task
        prerequisite_task_data = TaskCreate(
            description="Prerequisite task to complete"
        ).model_dump()

        prerequisite_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=prerequisite_task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        assert prerequisite_response.status_code == 201
        prerequisite_task_id = prerequisite_response.json()["id"]

        # Create dependent task
        dependent_task_data = TaskCreate(
            description="Dependent task to unblock",
            depends_on_task_id=uuid.UUID(prerequisite_task_id),
            initial_status="pending_dependency",
        ).model_dump()
        dependent_task_data["depends_on_task_id"] = str(
            dependent_task_data["depends_on_task_id"]
        )

        dependent_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=dependent_task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        assert dependent_response.status_code == 201
        dependent_task_id = dependent_response.json()["id"]

        # Verify dependent task is in pending_dependency status
        query = select(Task).where(Task.id == uuid.UUID(dependent_task_id))
        result = await db_session_real.execute(query)
        dependent_task_before = result.scalar_one_or_none()
        assert dependent_task_before.status == "pending_dependency"

        # Act - Complete the prerequisite task
        update_data = {"status": "done"}

        update_response = await test_client.put(
            f"/api/internal/tasks/{prerequisite_task_id}", json=update_data
        )

        assert update_response.status_code == 200

        # Wait a moment for the dependency update to complete
        import asyncio

        await asyncio.sleep(0.1)

        # Verify dependent task is now 'todo' - refresh the session to see committed changes
        await db_session_real.rollback()  # Clear any pending transaction
        query = select(Task).where(Task.id == uuid.UUID(dependent_task_id))
        result = await db_session_real.execute(query)
        dependent_task_after = result.scalar_one_or_none()
        assert dependent_task_after.status == "todo"

    @pytest.mark.asyncio
    async def test_dependent_task_creation_with_terminal_prerequisite(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
        db_session_real: AsyncSession,
    ):
        """
        TC_US5.5_054: Test dependent task creation with terminal prerequisite.

        Setup: Create prerequisite_task_1 and set its status to 'done'.
        Action: POST /internal/assistants/my/tasks for dependent_task_2 with
               depends_on_task_id: prerequisite_task_1.id and initial_status: 'pending_dependency'.
        Expected: 201 Created. dependent_task_2 status is 'todo' (since prerequisite is already done).
        """
        # Arrange - Create prerequisite task
        prerequisite_task_data = TaskCreate(
            description="Terminal prerequisite task"
        ).model_dump()

        prerequisite_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=prerequisite_task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        assert prerequisite_response.status_code == 201
        prerequisite_task_id = prerequisite_response.json()["id"]

        # Set prerequisite to 'done'
        update_response = await test_client.put(
            f"/api/internal/tasks/{prerequisite_task_id}", json={"status": "done"}
        )
        assert update_response.status_code == 200

        # Act - Create dependent task
        dependent_task_data = TaskCreate(
            description="Dependent on terminal prerequisite",
            depends_on_task_id=uuid.UUID(prerequisite_task_id),
            initial_status="pending_dependency",
        ).model_dump()
        dependent_task_data["depends_on_task_id"] = str(
            dependent_task_data["depends_on_task_id"]
        )

        dependent_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=dependent_task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        # Assert
        assert dependent_response.status_code == 201
        dependent_task = dependent_response.json()
        assert dependent_task["description"] == dependent_task_data["description"]
        assert dependent_task["depends_on_task_id"] == prerequisite_task_id
        # Should be 'todo' since prerequisite is already done
        assert dependent_task["status"] == "todo"

        # Verify in database
        dependent_task_id = uuid.UUID(dependent_task["id"])
        query = select(Task).where(Task.id == dependent_task_id)
        result = await db_session_real.execute(query)
        db_dependent_task = result.scalar_one_or_none()

        assert db_dependent_task is not None
        assert db_dependent_task.depends_on_task_id == uuid.UUID(prerequisite_task_id)
        assert db_dependent_task.status == "todo"

    @pytest.mark.asyncio
    async def test_create_task_invalid_initial_status(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
    ):
        """
        TC_US5.5_011: Test create task with invalid initial status.

        Action: POST /internal/assistants/my/tasks with initial_status: 'invalid_status_value'.
        Expected: 422 Unprocessable Entity.
        """
        # Arrange - Use raw JSON to bypass Pydantic validation
        task_data = {
            "description": "Task with invalid initial status",
            "initial_status": "invalid_status_value",
        }

        # Act
        response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        # Assert
        assert response.status_code == 422  # Validation error

    @pytest.mark.asyncio
    async def test_update_task_status(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
        db_session_real: AsyncSession,
    ):
        """
        TC_US5.5_020: Test updating a task's status.

        Setup: Create a task.
        Action: PUT /internal/tasks/{task_id} with status: 'in_progress'.
        Expected: 200 OK. Task status and updated_at updated in DB.
        """
        # Arrange - Create a task
        task_data = TaskCreate(description="Task to update status").model_dump()

        create_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        assert create_response.status_code == 201
        task_id = create_response.json()["id"]

        # Get the task's current updated_at timestamp
        query = select(Task).where(Task.id == uuid.UUID(task_id))
        result = await db_session_real.execute(query)
        task_before = result.scalar_one_or_none()
        updated_at_before = task_before.updated_at

        # Wait a moment to ensure the timestamp will be different
        import asyncio

        await asyncio.sleep(1)

        # Act - Update the task status
        update_data = {"status": "in_progress"}
        update_response = await test_client.put(
            f"/api/internal/tasks/{task_id}", json=update_data
        )

        # Assert
        assert update_response.status_code == 200
        updated_task = update_response.json()
        assert updated_task["status"] == "in_progress"

        # Verify in database - refresh the session to see committed changes
        await db_session_real.rollback()  # Clear any pending transaction
        query = select(Task).where(Task.id == uuid.UUID(task_id))
        result = await db_session_real.execute(query)
        task_after = result.scalar_one_or_none()

        assert task_after is not None
        assert task_after.status == "in_progress"

        # Handle timezone comparison for SQLite vs PostgreSQL compatibility
        # SQLite returns offset-naive datetimes, PostgreSQL returns offset-aware
        from datetime import timezone

        if (
            updated_at_before.tzinfo is None
            and task_after.updated_at.tzinfo is not None
        ):
            # Convert SQLite naive datetime to UTC for comparison
            updated_at_before = updated_at_before.replace(tzinfo=timezone.utc)
        elif (
            updated_at_before.tzinfo is not None
            and task_after.updated_at.tzinfo is None
        ):
            # Convert SQLite naive datetime to UTC for comparison
            task_after_updated_at = task_after.updated_at.replace(tzinfo=timezone.utc)
        else:
            task_after_updated_at = task_after.updated_at

        # Use the properly timezone-handled timestamp for comparison
        if "task_after_updated_at" in locals():
            assert task_after_updated_at > updated_at_before
        else:
            assert task_after.updated_at > updated_at_before

    @pytest.mark.asyncio
    async def test_update_task_metadata(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
        db_session_real: AsyncSession,
    ):
        """
        TC_US5.5_022: Test updating a task's metadata.

        Setup: Create a task.
        Action: PUT /internal/tasks/{task_id} with updated metadata.
        Expected: 200 OK. Task metadata updated in DB.
        """
        # Arrange - Create a task with initial metadata
        initial_metadata = {"key1": "value1"}
        task_data = TaskCreate(
            description="Task to update metadata", metadata=initial_metadata
        ).model_dump()

        create_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        assert create_response.status_code == 201
        task_id = create_response.json()["id"]

        # Act - Update the task metadata
        updated_metadata = {"key1": "value1", "key2": "value2"}
        update_data = {"metadata": updated_metadata}
        update_response = await test_client.put(
            f"/api/internal/tasks/{task_id}", json=update_data
        )

        # Assert
        assert update_response.status_code == 200
        updated_task = update_response.json()
        assert updated_task["metadata"] == updated_metadata

        # Verify in database
        query = select(Task).where(Task.id == uuid.UUID(task_id))
        result = await db_session_real.execute(query)
        task_after = result.scalar_one_or_none()

        assert task_after is not None
        assert task_after.metadata_json == updated_metadata

    @pytest.mark.asyncio
    async def test_update_task_invalid_status(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
    ):
        """
        TC_US5.5_023: Test updating a task with an invalid status.

        Setup: Create a task.
        Action: PUT /internal/tasks/{task_id} with status: 'invalid_status_here'.
        Expected: 422 Unprocessable Entity.
        """
        # Arrange - Create a task
        task_data = TaskCreate(
            description="Task for invalid status update"
        ).model_dump()

        create_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        assert create_response.status_code == 201
        task_id = create_response.json()["id"]

        # Act - Update with invalid status
        update_data = {"status": "invalid_status_here"}
        update_response = await test_client.put(
            f"/api/internal/tasks/{task_id}", json=update_data
        )

        # Assert
        assert update_response.status_code == 422  # Validation error

    @pytest.mark.asyncio
    async def test_update_nonexistent_task(
        self,
        test_client: AsyncClient,
    ):
        """
        TC_US5.5_024: Test updating a non-existent task.

        Action: PUT /internal/tasks/{non_existent_uuid} with valid payload.
        Expected: 404 Not Found.
        """
        # Arrange
        non_existent_task_id = str(uuid.uuid4())
        update_data = {"status": "in_progress"}

        # Act
        update_response = await test_client.put(
            f"/api/internal/tasks/{non_existent_task_id}", json=update_data
        )

        # Assert
        assert update_response.status_code == 404

    @pytest.mark.asyncio
    async def test_cancel_task(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
        db_session_real: AsyncSession,
    ):
        """
        TC_US5.5_025: Test cancelling a task via DELETE endpoint.

        Setup: Create a task.
        Action: DELETE /internal/tasks/{task_id}.
        Expected: 200 OK or 204 No Content. Task status in DB is 'cancelled'.
        """
        # Arrange - Create a task
        task_data = TaskCreate(description="Task to cancel").model_dump()

        create_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        assert create_response.status_code == 201
        task_id = create_response.json()["id"]

        # Act - Cancel the task
        cancel_response = await test_client.delete(f"/api/internal/tasks/{task_id}")

        # Assert
        assert cancel_response.status_code in [200, 204]

        if cancel_response.status_code == 200:
            cancelled_task = cancel_response.json()
            assert cancelled_task["status"] == "cancelled"

        # Verify in database
        query = select(Task).where(Task.id == uuid.UUID(task_id))
        result = await db_session_real.execute(query)
        task_after = result.scalar_one_or_none()

        assert task_after is not None
        assert task_after.status == "cancelled"

    @pytest.mark.asyncio
    async def test_timeout_and_watchdog_status_change(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
        db_session_real: AsyncSession,
    ):
        """
        TC_US5.5_033: Test timeout and watchdog-driven status change.

        Setup: Create a task with metadata.expected_duration_seconds = 5.
               Set status to in_progress. Do NOT update last_progress_at.
        Action: Simulate a watchdog process detecting the stale task.
        Expected: Task status changes to 'failed_timeout'.
        """
        # Arrange - Create a task with expected duration
        task_data = TaskCreate(
            description="Task with timeout", metadata={"expected_duration_seconds": 5}
        ).model_dump()

        create_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        assert create_response.status_code == 201
        task_id = create_response.json()["id"]

        # Set task to in_progress
        await test_client.put(
            f"/api/internal/tasks/{task_id}", json={"status": "in_progress"}
        )

        # Simulate time passing (we can't actually wait in a test)
        # In a real system, a watchdog process would detect this
        # For testing, we'll directly simulate what the watchdog would do

        # Act - Simulate watchdog detecting timeout
        update_data = {"status": "failed_timeout"}
        update_response = await test_client.put(
            f"/api/internal/tasks/{task_id}", json=update_data
        )

        # Assert
        assert update_response.status_code == 200
        updated_task = update_response.json()
        assert updated_task["status"] == "failed_timeout"

        # Verify in database
        query = select(Task).where(Task.id == uuid.UUID(task_id))
        result = await db_session_real.execute(query)
        task_after = result.scalar_one_or_none()

        assert task_after is not None
        assert task_after.status == "failed_timeout"

    @pytest.mark.asyncio
    async def test_human_input_timeout_escalation(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
        db_session_real: AsyncSession,
    ):
        """
        TC_US5.5_042: Test human input timeout/escalation.

        Setup: Create a task and set to pending_human_input with metadata.
        Action: Simulate a watchdog process detecting timeout for human input.
        Expected: Task status changes to 'failed_human_timeout' or 'escalated'.
        """
        # Arrange - Create a task
        task_data = TaskCreate(
            description="Task needing human input",
            metadata={"human_input_request": {"query": "Need help with X"}},
        ).model_dump()

        create_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        assert create_response.status_code == 201
        task_id = create_response.json()["id"]

        # Set task to pending_human_input
        await test_client.put(
            f"/api/internal/tasks/{task_id}", json={"status": "pending_human_input"}
        )

        # Simulate time passing without human input
        # In a real system, a watchdog would detect this
        # For testing, we'll directly simulate what the watchdog would do

        # Act - Simulate watchdog detecting human input timeout
        update_data = {"status": "failed_human_timeout"}
        update_response = await test_client.put(
            f"/api/internal/tasks/{task_id}", json=update_data
        )

        # Assert
        assert update_response.status_code == 200
        updated_task = update_response.json()
        assert updated_task["status"] == "failed_human_timeout"

        # Verify in database
        query = select(Task).where(Task.id == uuid.UUID(task_id))
        result = await db_session_real.execute(query)
        task_after = result.scalar_one_or_none()

        assert task_after is not None
        assert task_after.status == "failed_human_timeout"

    @pytest.mark.asyncio
    async def test_human_input_metadata_edge_cases(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
        db_session_real: AsyncSession,
    ):
        """
        TC_US5.5_043: Test edge cases in human_input metadata structure.

        Setup: Create a task.
        Action: Update task with various human_input metadata structures.
        Expected: Valid structures are accepted, invalid ones rejected.
        """
        # Arrange - Create a task
        task_data = TaskCreate(
            description="Task for human input metadata testing"
        ).model_dump()

        create_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        assert create_response.status_code == 201
        task_id = create_response.json()["id"]

        # Test Case 1: Valid human_input_request structure
        valid_request = {
            "metadata": {
                "human_input_request": {
                    "query": "Please provide information about X",
                    "context": "Additional context",
                    "options": ["Option A", "Option B"],
                }
            },
            "status": "pending_human_input",
        }

        response1 = await test_client.put(
            f"/api/internal/tasks/{task_id}", json=valid_request
        )
        assert response1.status_code == 200

        # Test Case 2: Valid human_input_response structure
        valid_response = {
            "metadata": {
                "human_input_request": {
                    "query": "Please provide information about X",
                },
                "human_input_response": {
                    "answer": "Here is the information",
                    "timestamp": "2023-01-01T12:00:00Z",
                },
            },
            "status": "in_progress",
        }

        response2 = await test_client.put(
            f"/api/internal/tasks/{task_id}", json=valid_response
        )
        assert response2.status_code == 200

        # Verify the metadata was stored correctly
        query = select(Task).where(Task.id == uuid.UUID(task_id))
        result = await db_session_real.execute(query)
        task = result.scalar_one_or_none()

        assert task is not None
        assert "human_input_request" in task.metadata_json
        assert "human_input_response" in task.metadata_json

    @pytest.mark.asyncio
    async def test_dependent_task_remains_pending(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
        db_session_real: AsyncSession,
    ):
        """
        TC_US5.5_051: Test dependent task remains pending until prerequisite is done.

        Setup: Create prerequisite_task_1 and dependent_task_2.
        Action: Observe dependent_task_2 while prerequisite_task_1 is in progress.
        Expected: dependent_task_2 remains in 'pending_dependency' state.
        """
        # Arrange - Create prerequisite task
        prerequisite_task_data = TaskCreate(
            description="Prerequisite task that will stay in progress"
        ).model_dump()

        prerequisite_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=prerequisite_task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        assert prerequisite_response.status_code == 201
        prerequisite_task_id = prerequisite_response.json()["id"]

        # Set prerequisite to in_progress
        await test_client.put(
            f"/api/internal/tasks/{prerequisite_task_id}",
            json={"status": "in_progress"},
        )

        # Create dependent task
        dependent_task_data = TaskCreate(
            description="Dependent task that should remain pending",
            depends_on_task_id=uuid.UUID(prerequisite_task_id),
        ).model_dump()
        dependent_task_data["depends_on_task_id"] = str(
            dependent_task_data["depends_on_task_id"]
        )

        dependent_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=dependent_task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        assert dependent_response.status_code == 201
        dependent_task_id = dependent_response.json()["id"]

        # Verify dependent task is in pending_dependency status
        query = select(Task).where(Task.id == uuid.UUID(dependent_task_id))
        result = await db_session_real.execute(query)
        dependent_task = result.scalar_one_or_none()

        assert dependent_task is not None
        assert dependent_task.status == "pending_dependency"

        # Try to update dependent task to in_progress
        await test_client.put(
            f"/api/internal/tasks/{dependent_task_id}", json={"status": "in_progress"}
        )

        # The API might allow this update, but the task should remain pending
        # due to the dependency (this depends on implementation)

        # Verify dependent task is still in pending_dependency status
        query = select(Task).where(Task.id == uuid.UUID(dependent_task_id))
        result = await db_session_real.execute(query)
        dependent_task_after = result.scalar_one_or_none()

        # This assertion might need adjustment based on implementation
        assert dependent_task_after is not None
        assert dependent_task_after.status in ["pending_dependency", "in_progress"]

    @pytest.mark.asyncio
    async def test_prerequisite_fails_or_cancelled(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
        db_session_real: AsyncSession,
    ):
        """
        TC_US5.5_053: Test behavior when prerequisite fails or is cancelled.

        Setup: Create prerequisite_task_1 and dependent_task_2.
        Action: Update prerequisite_task_1 status to 'cancelled'.
        Expected: Behavior of dependent_task_2 should be defined.
        """
        # Arrange - Create prerequisite task
        prerequisite_task_data = TaskCreate(
            description="Prerequisite task that will be cancelled"
        ).model_dump()

        prerequisite_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=prerequisite_task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        assert prerequisite_response.status_code == 201
        prerequisite_task_id = prerequisite_response.json()["id"]

        # Create dependent task
        dependent_task_data = TaskCreate(
            description="Dependent task with prerequisite that will fail",
            depends_on_task_id=uuid.UUID(prerequisite_task_id),
        ).model_dump()
        dependent_task_data["depends_on_task_id"] = str(
            dependent_task_data["depends_on_task_id"]
        )

        dependent_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=dependent_task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        assert dependent_response.status_code == 201
        dependent_task_id = dependent_response.json()["id"]

        # Verify dependent task is in pending_dependency status
        query = select(Task).where(Task.id == uuid.UUID(dependent_task_id))
        result = await db_session_real.execute(query)
        dependent_task_before = result.scalar_one_or_none()
        assert dependent_task_before.status == "pending_dependency"

        # Act - Cancel the prerequisite task
        await test_client.delete(f"/api/internal/tasks/{prerequisite_task_id}")

        # Verify prerequisite task is cancelled
        query = select(Task).where(Task.id == uuid.UUID(prerequisite_task_id))
        result = await db_session_real.execute(query)
        prerequisite_task_after = result.scalar_one_or_none()
        assert prerequisite_task_after.status == "cancelled"

        # Verify dependent task status
        query = select(Task).where(Task.id == uuid.UUID(dependent_task_id))
        result = await db_session_real.execute(query)
        dependent_task_after = result.scalar_one_or_none()

        # The behavior here depends on implementation
        assert dependent_task_after is not None

    @pytest.mark.asyncio
    async def test_lease_expiry_and_recovery(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
        db_session_real: AsyncSession,
    ):
        """
        TC_US5.5_061: Test lease expiry and recovery.

        Setup: Create a task and acquire a lease.
        Action: Simulate lease expiry and recovery.
        Expected: Task lease becomes available for another worker.
        """
        # Arrange - Create a task
        task_data = TaskCreate(description="Task for lease testing").model_dump()

        create_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        assert create_response.status_code == 201
        task_id = create_response.json()["id"]

        # Simulate acquiring a lease (worker 1)
        lease_data = {
            "status": "leased",
            "metadata": {
                "lease_owner_id": "worker_1",
                "lease_expires_at": (
                    datetime.now(UTC) + timedelta(seconds=5)
                ).isoformat(),
            },
        }

        lease_response = await test_client.put(
            f"/api/internal/tasks/{task_id}", json=lease_data
        )

        assert lease_response.status_code == 200

        # Verify lease is acquired
        query = select(Task).where(Task.id == uuid.UUID(task_id))
        result = await db_session_real.execute(query)
        task_leased = result.scalar_one_or_none()

        assert task_leased is not None
        assert task_leased.status == "leased"
        assert task_leased.metadata_json.get("lease_owner_id") == "worker_1"

        # Simulate lease expiry (we can't actually wait in a test)
        # In a real system, time would pass and the lease would expire

        # Act - Simulate another worker (worker 2) acquiring the lease
        new_lease_data = {
            "status": "leased",
            "metadata": {
                "lease_owner_id": "worker_2",
                "lease_expires_at": (
                    datetime.now(UTC) + timedelta(seconds=5)
                ).isoformat(),
            },
        }

        new_lease_response = await test_client.put(
            f"/api/internal/tasks/{task_id}", json=new_lease_data
        )

        assert new_lease_response.status_code == 200

        # Verify new lease is acquired - refresh the session to see committed changes
        await db_session_real.rollback()  # Clear any pending transaction
        query = select(Task).where(Task.id == uuid.UUID(task_id))
        result = await db_session_real.execute(query)
        task_new_lease = result.scalar_one_or_none()

        assert task_new_lease is not None
        assert task_new_lease.status == "leased"
        assert task_new_lease.metadata_json.get("lease_owner_id") == "worker_2"

    @pytest.mark.asyncio
    async def test_parent_task_event_on_subtask_completion(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
        db_session_real: AsyncSession,
    ):
        """
        TC_US5.5_070: Test parent-task event on sub-task completion.

        Setup: Create parent_task and child_task.
        Action: Update child_task status to 'done'.
        Expected: Event is available for the parent task's logic.
        """
        # Arrange - Create parent task
        parent_task_data = TaskCreate(description="Parent task").model_dump()

        parent_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=parent_task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        assert parent_response.status_code == 201
        parent_task_id = parent_response.json()["id"]

        # Create child task
        child_task_data = TaskCreate(
            description="Child task", parent_task_id=uuid.UUID(parent_task_id)
        ).model_dump()
        # Convert UUID to string for JSON
        child_task_data["parent_task_id"] = str(child_task_data["parent_task_id"])

        child_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=child_task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        assert child_response.status_code == 201
        child_task_id = child_response.json()["id"]

        # Act - Complete the child task
        update_data = {"status": "done"}
        update_response = await test_client.put(
            f"/api/internal/tasks/{child_task_id}", json=update_data
        )

        assert update_response.status_code == 200

        # Verify child task is completed
        query = select(Task).where(Task.id == uuid.UUID(child_task_id))
        result = await db_session_real.execute(query)
        child_task_after = result.scalar_one_or_none()
        assert child_task_after.status == "done"

        # Verify parent task is updated (this depends on implementation)
        query = select(Task).where(Task.id == uuid.UUID(parent_task_id))
        result = await db_session_real.execute(query)
        parent_task_after = result.scalar_one_or_none()

        # This assertion might need adjustment based on implementation
        assert parent_task_after is not None

    @pytest.mark.asyncio
    async def test_invalid_objective_id_format(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
    ):
        """
        TC_US5.5_080: Test invalid objective_id format.

        Action: POST /internal/assistants/my/tasks with objective_id: 'not-a-uuid'.
        Expected: 422 Unprocessable Entity.
        """
        # Arrange
        task_data = {
            "description": "Task with invalid objective ID",
            "objective_id": "not-a-uuid",
        }

        # Act
        response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        # Assert
        assert response.status_code == 422  # Validation error

    @pytest.mark.asyncio
    async def test_invalid_metadata_structure(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
    ):
        """
        TC_US5.5_081: Test invalid metadata structure on update.

        Setup: Create a task.
        Action: PUT /internal/tasks/{task_id} with metadata: "not-an-object".
        Expected: 422 Unprocessable Entity.
        """
        # Arrange - Create a task
        task_data = TaskCreate(description="Task for metadata test").model_dump()

        create_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        assert create_response.status_code == 201
        task_id = create_response.json()["id"]

        # Act - Update with invalid metadata
        update_data = {"metadata": "not-an-object"}
        update_response = await test_client.put(
            f"/api/internal/tasks/{task_id}", json=update_data
        )

        # Assert
        assert update_response.status_code == 422  # Validation error

    @pytest.mark.asyncio
    async def test_invalid_status_value_on_update(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
    ):
        """
        TC_US5.5_082: Test invalid status value on update.

        Setup: Create a task.
        Action: PUT /internal/tasks/{task_id} with status: 'super_done_custom_status'.
        Expected: 422 Unprocessable Entity.
        """
        # Arrange - Create a task
        task_data = TaskCreate(
            description="Task for invalid status update"
        ).model_dump()

        create_response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        assert create_response.status_code == 201
        task_id = create_response.json()["id"]

        # Act - Update with invalid status
        update_data = {"status": "super_done_custom_status"}
        update_response = await test_client.put(
            f"/api/internal/tasks/{task_id}", json=update_data
        )

        # Assert
        assert update_response.status_code == 422  # Validation error

    @pytest.mark.asyncio
    async def test_same_idempotency_key_different_assistants(
        self,
        test_client: AsyncClient,
        db_session_real: AsyncSession,
    ):
        """
        TC_US5.5_084: Test same idempotency_key across different assistants.

        Setup: Create task_A by assistant_1 with idempotency_key: 'key123'.
        Action: Create task_C by different assistant_2 with same key.
        Expected: 201 Created. New task_C created for assistant_2.
        """
        # Arrange - Create two different users and their assistants
        from a2a_platform.schemas.assistant_schemas import AssistantCreate
        from a2a_platform.schemas.user import UserCreate
        from a2a_platform.services.assistant_service import AssistantService
        from a2a_platform.services.user_service import create_user

        # Create first user and assistant
        user_1_data = UserCreate(
            clerk_user_id=f"test_clerk_user_1_{uuid.uuid4()}",
            email=f"test_1_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user_1 = await create_user(db_session_real, user_1_data)

        assistant_service = AssistantService(db_session=db_session_real)
        assistant_1_data = AssistantCreate(
            name="Test Assistant 1",
            backstory="A test assistant for user 1",
            avatar_file_id=None,
            configuration={},
        )
        assistant_1 = await assistant_service.create_personal_assistant(
            user_id=user_1.id, assistant_data=assistant_1_data
        )

        # Create second user and assistant
        user_2_data = UserCreate(
            clerk_user_id=f"test_clerk_user_2_{uuid.uuid4()}",
            email=f"test_2_{uuid.uuid4()}@example.com",
            timezone="UTC",
        )
        user_2 = await create_user(db_session_real, user_2_data)

        assistant_2_data = AssistantCreate(
            name="Test Assistant 2",
            backstory="A test assistant for user 2",
            avatar_file_id=None,
            configuration={},
        )
        assistant_2 = await assistant_service.create_personal_assistant(
            user_id=user_2.id, assistant_data=assistant_2_data
        )

        # Create a shared idempotency key
        idempotency_key = "shared-key-123"

        # Create task for first assistant
        task_data_1 = TaskCreate(description="Task for first assistant").model_dump()

        response_1 = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data_1,
            headers={
                "X-Assistant-ID": str(assistant_1.id),
                "X-Idempotency-Key": idempotency_key,
            },
        )

        assert response_1.status_code == 201
        task_1_id = response_1.json()["id"]

        # Act - Create task for second assistant with same idempotency key
        task_data_2 = TaskCreate(description="Task for second assistant").model_dump()

        response_2 = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data_2,
            headers={
                "X-Assistant-ID": str(assistant_2.id),
                "X-Idempotency-Key": idempotency_key,
            },
        )

        # Assert
        assert response_2.status_code == 201  # New task created
        task_2_id = response_2.json()["id"]

        # Verify tasks are different
        assert task_1_id != task_2_id

        # Verify in database
        query = select(Task).where(Task.id == uuid.UUID(task_1_id))
        result = await db_session_real.execute(query)
        task_1 = result.scalar_one_or_none()

        query = select(Task).where(Task.id == uuid.UUID(task_2_id))
        result = await db_session_real.execute(query)
        task_2 = result.scalar_one_or_none()

        assert task_1 is not None
        assert task_2 is not None
        assert task_1.assistant_id == assistant_1.id
        assert task_2.assistant_id == assistant_2.id
        assert task_1.idempotency_key == idempotency_key
        assert task_2.idempotency_key == idempotency_key
