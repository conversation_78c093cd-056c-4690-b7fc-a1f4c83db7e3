"""
Integration tests for PA message functionality.

Tests the complete PA message flow including:
- ChatService PA message creation
- GraphQL mutation operations
- Database persistence
- Error handling scenarios
"""

import uuid

import pytest
from httpx import AsyncClient
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.chat_message import ChatMessage
from a2a_platform.db.models.conversation import Conversation
from a2a_platform.db.models.user import User
from tests.utils.test_auth import get_test_auth_headers


# We import the fixture from our utils module
# from tests.utils.test_auth import mock_auth_middleware


class TestPAMessageIntegration:
    """Integration tests for PA message functionality."""

    @pytest.mark.asyncio
    async def test_send_pa_message_graphql_mutation_success(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
    ):
        """Test successful PA message creation via GraphQL mutation."""
        # Arrange - Create a conversation
        conversation = Conversation(
            user_id=test_user.id,
            assistant_id=test_assistant_id,
        )
        db_session_real.add(conversation)
        await db_session_real.commit()
        await db_session_real.refresh(conversation)

        mutation = """
        mutation SendMessageFromPA($input: SendMessageFromPAInput!) {
            sendMessageFromPA(input: $input) {
                message {
                    id
                    conversationId
                    senderRole
                    content
                    timestamp
                    metadata
                }
                success
                errorMessage
            }
        }
        """

        variables = {
            "input": {
                "conversationId": str(conversation.id),
                "content": "Hello! I'm your Personal Assistant. How can I help you today?",
                "metadata": {"source": "ai_service", "model": "gpt-4"},
            }
        }

        # Act - Include authentication headers
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=get_test_auth_headers(test_user),
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        result = data["data"]["sendMessageFromPA"]
        assert result["success"] is True
        assert result["errorMessage"] is None

        message = result["message"]
        assert message["conversationId"] == str(conversation.id)
        assert message["senderRole"] == "AGENT"
        assert "text" in message["content"]
        assert (
            message["content"]["text"]
            == "Hello! I'm your Personal Assistant. How can I help you today?"
        )
        assert message["metadata"]["source"] == "ai_service"

        # Verify message was persisted in database
        db_result = await db_session_real.execute(
            select(ChatMessage).where(ChatMessage.id == uuid.UUID(message["id"]))
        )
        db_message = db_result.scalar_one()
        assert db_message.sender_role == "agent"
        assert db_message.conversation_id == conversation.id

    @pytest.mark.asyncio
    async def test_send_pa_message_empty_content_error(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
    ):
        """Test PA message creation with empty content returns error."""
        # Arrange - Create a conversation
        conversation = Conversation(
            user_id=test_user.id,
            assistant_id=test_assistant_id,
        )
        db_session_real.add(conversation)
        await db_session_real.commit()
        await db_session_real.refresh(conversation)

        mutation = """
        mutation SendMessageFromPA($input: SendMessageFromPAInput!) {
            sendMessageFromPA(input: $input) {
                message {
                    id
                }
                success
                errorMessage
            }
        }
        """

        variables = {
            "input": {
                "conversationId": str(conversation.id),
                "content": "",
            }
        }

        # Act - Include authentication headers
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=get_test_auth_headers(test_user),
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        result = data["data"]["sendMessageFromPA"]
        assert result["success"] is False
        assert "PA message content cannot be empty" in result["errorMessage"]

    @pytest.mark.asyncio
    async def test_send_pa_message_invalid_conversation_id(
        self,
        async_client: AsyncClient,
        test_user: User,  # Add test_user parameter to get auth headers
    ):
        """Test PA message creation with invalid conversation ID."""
        mutation = """
        mutation SendMessageFromPA($input: SendMessageFromPAInput!) {
            sendMessageFromPA(input: $input) {
                message {
                    id
                }
                success
                errorMessage
            }
        }
        """

        variables = {
            "input": {
                "conversationId": "invalid-uuid",
                "content": "Hello from PA",
            }
        }

        # Act - Include authentication headers
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=get_test_auth_headers(test_user),
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        result = data["data"]["sendMessageFromPA"]
        assert result["success"] is False
        assert "Invalid conversation ID format" in result["errorMessage"]

    @pytest.mark.asyncio
    async def test_send_pa_message_nonexistent_conversation(
        self,
        async_client: AsyncClient,
        test_user: User,  # Add test_user parameter to get auth headers
    ):
        """Test PA message creation with non-existent conversation."""
        mutation = """
        mutation SendMessageFromPA($input: SendMessageFromPAInput!) {
            sendMessageFromPA(input: $input) {
                message {
                    id
                }
                success
                errorMessage
            }
        }
        """

        nonexistent_id = str(uuid.uuid4())
        variables = {
            "input": {
                "conversationId": nonexistent_id,
                "content": "Hello from PA",
            }
        }

        # Act - Include authentication headers
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=get_test_auth_headers(test_user),
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert "errors" not in data

        result = data["data"]["sendMessageFromPA"]
        assert result["success"] is False
        assert (
            "Conversation not found or access denied" in result["errorMessage"]
            or f"Conversation with ID {nonexistent_id} not found"
            in result["errorMessage"]
        )

    @pytest.mark.asyncio
    async def test_send_pa_message_content_structure_persistence(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
    ):
        """Test that PA messages are persisted with correct structured content format."""
        # Arrange - Create a conversation
        conversation = Conversation(
            user_id=test_user.id,
            assistant_id=test_assistant_id,
        )
        db_session_real.add(conversation)
        await db_session_real.commit()
        await db_session_real.refresh(conversation)

        mutation = """
        mutation SendMessageFromPA($input: SendMessageFromPAInput!) {
            sendMessageFromPA(input: $input) {
                message {
                    id
                    content
                }
                success
            }
        }
        """

        content_text = "This is a structured PA message"
        variables = {
            "input": {
                "conversationId": str(conversation.id),
                "content": content_text,
            }
        }

        # Act - Include authentication headers
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=get_test_auth_headers(test_user),
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        result = data["data"]["sendMessageFromPA"]
        assert result["success"] is True

        message_id = result["message"]["id"]

        # Verify database content structure
        db_result = await db_session_real.execute(
            select(ChatMessage).where(ChatMessage.id == uuid.UUID(message_id))
        )
        db_message = db_result.scalar_one()

        expected_content = {"parts": [{"type": "text", "content": content_text}]}
        assert db_message.content == expected_content
        assert db_message.sender_role == "agent"

    @pytest.mark.asyncio
    async def test_send_pa_message_updates_conversation_timestamp(
        self,
        async_client: AsyncClient,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
    ):
        """Test that sending PA message updates conversation's last_message_at."""
        # Arrange - Create a conversation
        conversation = Conversation(
            user_id=test_user.id,
            assistant_id=test_assistant_id,
        )
        db_session_real.add(conversation)
        await db_session_real.commit()
        await db_session_real.refresh(conversation)

        original_timestamp = conversation.last_message_at

        mutation = """
        mutation SendMessageFromPA($input: SendMessageFromPAInput!) {
            sendMessageFromPA(input: $input) {
                success
            }
        }
        """

        variables = {
            "input": {
                "conversationId": str(conversation.id),
                "content": "Hello from PA",
            }
        }

        # Act - Include authentication headers
        response = await async_client.post(
            "/graphql",
            json={"query": mutation, "variables": variables},
            headers=get_test_auth_headers(test_user),
        )

        # Assert
        assert response.status_code == 200
        data = response.json()
        assert data["data"]["sendMessageFromPA"]["success"] is True

        # Refresh conversation and check timestamp was updated
        await db_session_real.refresh(conversation)
        assert conversation.last_message_at != original_timestamp
