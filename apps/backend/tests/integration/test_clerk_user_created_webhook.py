import base64
import time
import uuid
from unittest.mock import MagicMock, patch

import pytest

from a2a_platform.config.settings import get_settings

# Set the webhook secret for testing
get_settings().CLERK_WEBHOOK_SECRET = base64.b64encode(b"test_secret").decode()


# The test_client fixture is now provided by conftest.py


@pytest.fixture
def clerk_user_created_payload():
    """
    Fixture providing a realistic Clerk user.created webhook payload.

    This represents the contract with <PERSON>'s webhook service for user.created events.
    Reference: https://clerk.com/docs/webhooks/overview
    """
    return {
        "data": {
            "birthday": "",
            "created_at": "2022-05-31T10:56:31Z",
            "email_addresses": [
                {
                    "email_address": "<EMAIL>",
                    "id": "idn_29w83yL7CwVlJXylYLxcslromF1",
                    "linked_to": [],
                    "object": "email_address",
                    "verification": {"status": "verified", "strategy": "ticket"},
                }
            ],
            "external_accounts": [],
            "external_id": "567772",
            "first_name": "Example",
            "gender": "",
            "id": "user_29w83sxmDNGwOuEthce5gg56FcC",
            "image_url": "https://img.clerk.com/xxxxxx",
            "last_name": "Example",
            "last_sign_in_at": *************,
            "object": "user",
            "password_enabled": True,
            "phone_numbers": [],
            "primary_email_address_id": "idn_29w83yL7CwVlJXylYLxcslromF1",
            "primary_phone_number_id": None,
            "primary_web3_wallet_id": None,
            "private_metadata": {},
            "profile_image_url": "https://www.gravatar.com/avatar?d=mp",
            "public_metadata": {},
            "two_factor_enabled": False,
            "unsafe_metadata": {},
            "updated_at": "2022-05-31T10:56:31Z",
            "username": None,
            "web3_wallets": [],
        },
        "event_attributes": {
            "http_request": {
                "client_ip": "0.0.0.0",
                "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            }
        },
        "object": "event",
        "timestamp": 1654012591835,
        "type": "user.created",
    }


@pytest.mark.asyncio
async def test_clerk_webhook_handler_user_created(test_client):
    """
    Test that the Clerk webhook handler correctly processes a user.created event.

    This test verifies the consumer-side contract between our application and Clerk's webhook service.
    It ensures that our application can correctly process a webhook payload that adheres to
    Clerk's documented structure for the user.created event.

    Reference: https://clerk.com/docs/webhooks/overview
    """
    # Our conftest.py now handles the database connection properly
    # by automatically using 'localhost' instead of 'db' when not in Docker

    # Mock the verify_webhook_signature function to always return True for testing
    clerk_user_id = (
        f"user_{uuid.uuid4()}"  # Clerk user IDs typically start with "user_"
    )
    mock_email = f"test-{uuid.uuid4()}@example.com"

    # Mock the Webhook class to avoid actual signature verification
    with patch("a2a_platform.auth.clerk.Webhook") as mock_webhook_cls:
        # Configure the mock webhook instance
        mock_webhook = MagicMock()
        mock_webhook.verify.return_value = None  # No error means verification passed
        mock_webhook_cls.return_value = mock_webhook

        # Mock the process_user_created_task function
        with patch(
            "a2a_platform.api.rest.routes.webhooks.process_user_created_task"
        ) as mock_create_user:
            # Prepare the webhook payload that represents the contract with Clerk
            # This is a more comprehensive representation of Clerk's user.created event payload
            # based on their documentation and typical structure
            webhook_payload = {
                "type": "user.created",
                "data": {
                    "id": clerk_user_id,
                    "first_name": "Test",
                    "last_name": "User",
                    "email_addresses": [
                        {
                            "id": f"idn_{uuid.uuid4()}",
                            "email_address": mock_email,
                            "verification": {
                                "status": "verified",
                                "strategy": "email_link",
                            },
                        }
                    ],
                    "primary_email_address_id": f"idn_{uuid.uuid4()}",
                    "username": None,
                    "profile_image_url": "https://www.gravatar.com/avatar?d=mp",
                    "public_metadata": {},
                    "private_metadata": {},
                    "unsafe_metadata": {},
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z",
                },
                "object": "event",
                "timestamp": "2024-01-01T00:00:00Z",
            }

            # Configure the mock to return a coroutine
            async def mock_coro(*_args, **_kwargs):
                return True

            mock_create_user.side_effect = mock_coro

            # Make the request with minimal required headers
            response = await test_client.post(
                "/api/v1/webhooks/clerk",
                json=webhook_payload,
                headers={
                    "svix-signature": "v1,test_signature",
                    "svix-timestamp": str(int(time.time())),
                    "svix-id": f"msg_{uuid.uuid4().hex}",
                },
            )

            # Verify the mock was called with the correct arguments
            mock_webhook.verify.assert_called_once()
            mock_create_user.assert_called_once()

            # Verify the response was successful
            assert response.status_code == 200
            assert response.json()["success"] is True
            assert "user.created event" in response.json()["message"]


@pytest.mark.asyncio
async def test_clerk_webhook_handler_with_real_payload(
    test_client, clerk_user_created_payload
):
    """
    Test that the Clerk webhook handler correctly processes a real user.created event payload.

    This test uses a realistic payload structure from Clerk's documentation to ensure
    our webhook handler can process actual production payloads.
    """
    # Extract payload data for logging/debugging if needed
    _ = clerk_user_created_payload["data"]["id"]

    # Mock the Webhook class to avoid actual signature verification
    with patch("a2a_platform.auth.clerk.Webhook") as mock_webhook_cls:
        # Configure the mock webhook instance
        mock_webhook = MagicMock()
        mock_webhook.verify.return_value = None  # No error means verification passed
        mock_webhook_cls.return_value = mock_webhook

        # Mock the process_user_created_task function
        with patch(
            "a2a_platform.api.rest.routes.webhooks.process_user_created_task"
        ) as mock_create_user:
            # Configure the mock to return a coroutine
            async def mock_coro(*_args, **_kwargs):
                return True

            mock_create_user.side_effect = mock_coro

            # Make the request with minimal required headers
            response = await test_client.post(
                "/api/v1/webhooks/clerk",
                json=clerk_user_created_payload,
                headers={
                    "svix-signature": "v1,test_signature",
                    "svix-timestamp": str(int(time.time())),
                    "svix-id": f"msg_{uuid.uuid4().hex}",
                },
            )

            # Verify the mock was called with the correct arguments
            mock_webhook.verify.assert_called_once()
            mock_create_user.assert_called_once()

            # Verify the response was successful
            assert response.status_code == 200
            assert response.json()["success"] is True
            assert "user.created event" in response.json()["message"]


@pytest.mark.asyncio
async def test_clerk_webhook_invalid_signature(test_client):
    """
    Test that the Clerk webhook handler correctly rejects requests with invalid signatures.

    This test verifies that our application properly validates the webhook signature
    and rejects requests that don't have a valid signature.
    """
    # Mock the Svix Webhook class to raise an exception when verify is called
    mock_webhook = MagicMock()
    mock_webhook.verify.side_effect = Exception("Invalid signature")
    with patch(
        "svix.webhooks.Webhook",
        return_value=mock_webhook,
    ):
        # Prepare the webhook payload
        webhook_payload = {
            "type": "user.created",
            "data": {
                "id": "mock_user_id",
                "email_addresses": [{"email_address": "<EMAIL>"}],
            },
        }
        # For this test, we're mocking verify_webhook_signature to return False,
        # so the signature format doesn't matter
        mock_timestamp = str(int(time.time()))
        invalid_signature = "invalid_signature"

        response = await test_client.post(
            "/api/v1/webhooks/clerk",
            json=webhook_payload,
            headers={
                "svix-signature": invalid_signature,
                "svix-timestamp": mock_timestamp,
                "svix-id": f"msg_{uuid.uuid4().hex}",
            },
        )
        assert response.status_code == 401
        response_json = response.json()
        assert response_json["detail"] == "Invalid webhook signature"
