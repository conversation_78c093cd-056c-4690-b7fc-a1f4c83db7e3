"""
Implementation of missing test case TC_US5.5_030: Task Retry.

This test verifies that a task's retry mechanism works correctly.
"""

import uuid

import pytest
from httpx import AsyncClient
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.task import Task
from a2a_platform.schemas.task_schemas import TaskCreate, TaskUpdate


@pytest.mark.asyncio
async def test_task_retry(
    test_client: AsyncClient,
    test_assistant_id: uuid.UUID,
    db_session_real: AsyncSession,
):
    """
    TC_US5.5_030: Test task retry mechanism.

    Setup: Create a task. Simulate a scenario where the PA logic decides to retry.
    Action: PUT /internal/tasks/{task_id} with status: 'retrying'.
    Expected: 200 OK. Task status is 'retrying'. Verify updated_at.
    """
    # Arrange - Create a task to retry
    task_data = TaskCreate(description="Task for retry test").model_dump()
    response = await test_client.post(
        "/api/internal/assistants/my/tasks",
        json=task_data,
        headers={"X-Assistant-ID": str(test_assistant_id)},
    )

    assert response.status_code == 201
    task = response.json()
    task_id = task["id"]
    initial_retry_count = task["retry_count"]
    initial_updated_at = task["updated_at"]

    # Act - Set task status to retrying
    update_data = TaskUpdate(status="retrying").model_dump()
    update_response = await test_client.put(
        f"/api/internal/tasks/{task_id}", json=update_data
    )

    # Assert
    assert update_response.status_code == 200
    updated_task = update_response.json()
    assert updated_task["status"] == "retrying"
    assert (
        updated_task["updated_at"] != initial_updated_at
    )  # updated_at should be changed

    # Verify in database - In a real system, the worker would increment the retry_count
    # Here we need to manually update the retry_count to simulate worker behavior
    query = select(Task).where(Task.id == uuid.UUID(task_id))
    result = await db_session_real.execute(query)
    db_task = result.scalar_one_or_none()

    assert db_task is not None
    assert db_task.status == "retrying"

    # Manually increment retry count to simulate worker behavior
    db_task.retry_count += 1
    await db_session_real.flush()
    await db_session_real.commit()

    # Verify retry count was incremented
    query = select(Task).where(Task.id == uuid.UUID(task_id))
    result = await db_session_real.execute(query)
    db_task = result.scalar_one_or_none()

    assert db_task.retry_count == initial_retry_count + 1
