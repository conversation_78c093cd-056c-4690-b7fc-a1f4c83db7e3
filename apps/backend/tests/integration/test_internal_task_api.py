"""
Integration tests for the internal task management API endpoints.

These tests verify the functionality of the internal task management API endpoints
as specified in US5.5.
"""

import logging
import uuid
from datetime import UTC, datetime

import pytest
from httpx import AsyncClient
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.assistant_objective import AssistantObjective
from a2a_platform.db.models.task import Task


@pytest.fixture
async def test_objective(
    test_assistant_id: uuid.UUID, db_session_real: AsyncSession
) -> AssistantObjective:
    """Create a test objective for testing."""
    objective = AssistantObjective(
        assistant_id=test_assistant_id,
        objective_text="Test objective",
        status="active",
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )
    db_session_real.add(objective)
    await db_session_real.commit()
    return objective


class TestInternalTaskAPI:
    """Integration tests for the internal task management API endpoints."""

    @pytest.mark.asyncio
    async def test_simple_task_creation(
        self,
        test_client: AsyncClient,
        test_assistant_id: uuid.UUID,
        db_session_real: AsyncSession,
    ):
        """
        TC_US5.5_001: Test creating a simple task.

        Action: POST /internal/assistants/my/tasks with valid description.
        Expected: 201 Created. New task record in tasks with status='todo', correct assistant_id,
                 description, created_at, updated_at. idempotency_key is NULL.
        """
        # Arrange
        task_data = {"description": "Test simple task creation"}

        # Act
        response = await test_client.post(
            "/api/internal/assistants/my/tasks",
            json=task_data,
            headers={"X-Assistant-ID": str(test_assistant_id)},
        )

        # Assert
        logging.debug(f"Response: {response.status_code} - {response.text}")
        assert response.status_code == 201
        task = response.json()
        assert task["description"] == task_data["description"]
        assert task["status"] == "todo"
        assert task["assistant_id"] == str(test_assistant_id)
        assert "created_at" in task
        assert "updated_at" in task
        assert task["idempotency_key"] is None

        # Verify in database
        task_id = uuid.UUID(task["id"])
        query = select(Task).where(Task.id == task_id)
        result = await db_session_real.execute(query)
        db_task = result.scalar_one_or_none()

        assert db_task is not None
        assert db_task.description == task_data["description"]
        assert db_task.assistant_id == test_assistant_id
        assert db_task.status == "todo"
        assert db_task.idempotency_key is None
