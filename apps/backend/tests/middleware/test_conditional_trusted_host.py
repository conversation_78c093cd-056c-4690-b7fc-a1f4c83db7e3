"""
Tests for ConditionalTrustedHost middleware.

Tests that the middleware properly validates trusted hosts while allowing
health check routes to bypass validation for Cloud Run startup probes.
"""

import pytest
from fastapi import Fast<PERSON><PERSON>
from fastapi.testclient import TestClient

from a2a_platform.middleware.conditional_trusted_host import (
    ConditionalTrustedHostMiddleware,
)


# Mark all tests in this module to not use database fixtures
pytestmark = pytest.mark.no_db


@pytest.fixture
def app_with_middleware():
    """Create a FastAPI app with ConditionalTrustedHost middleware for testing."""
    app = FastAPI()

    # Add the middleware with test configuration
    app.add_middleware(
        ConditionalTrustedHostMiddleware,
        allowed_hosts=["example.com", "*.trusted.com", "api-staging.vedavivi.app"],
    )

    # Add test routes
    @app.get("/api/health")
    async def health():
        return {"status": "OK"}

    @app.get("/health")
    async def health_alt():
        return {"status": "OK"}

    @app.get("/api/protected")
    async def protected():
        return {"message": "protected endpoint"}

    @app.get("/")
    async def root():
        return {"message": "root endpoint"}

    return app


@pytest.fixture
def client(app_with_middleware):
    """Create a test client for the app."""
    return TestClient(app_with_middleware)


class TestConditionalTrustedHostMiddleware:
    """Test cases for ConditionalTrustedHost middleware."""

    def test_health_check_routes_bypass_host_validation(self, client):
        """Test that health check routes work without valid host headers."""
        # Test /api/health with invalid host
        response = client.get("/api/health", headers={"host": "invalid.com"})
        assert response.status_code == 200
        assert response.json() == {"status": "OK"}

        # Test /health with invalid host
        response = client.get("/health", headers={"host": "malicious.com"})
        assert response.status_code == 200
        assert response.json() == {"status": "OK"}

        # Test /api/health with no host header
        response = client.get("/api/health")
        assert response.status_code == 200

    def test_protected_routes_require_valid_host(self, client):
        """Test that non-health routes require valid host headers."""
        # Test with invalid host
        response = client.get("/api/protected", headers={"host": "invalid.com"})
        assert response.status_code == 400
        assert response.text == "Invalid host header"

        # Test with no host header
        response = client.get("/api/protected")
        assert response.status_code == 400
        assert response.text == "Invalid host header"

        # Test root endpoint with invalid host
        response = client.get("/", headers={"host": "malicious.com"})
        assert response.status_code == 400

    def test_exact_host_match_allowed(self, client):
        """Test that exact host matches are allowed."""
        response = client.get("/api/protected", headers={"host": "example.com"})
        assert response.status_code == 200
        assert response.json() == {"message": "protected endpoint"}

        response = client.get(
            "/api/protected", headers={"host": "api-staging.vedavivi.app"}
        )
        assert response.status_code == 200

    def test_wildcard_subdomain_match_allowed(self, client):
        """Test that wildcard subdomain matches are allowed."""
        # Test various subdomains of *.trusted.com
        response = client.get("/api/protected", headers={"host": "api.trusted.com"})
        assert response.status_code == 200

        response = client.get("/api/protected", headers={"host": "staging.trusted.com"})
        assert response.status_code == 200

        response = client.get("/api/protected", headers={"host": "www.trusted.com"})
        assert response.status_code == 200

    def test_wildcard_subdomain_match_rejected_for_different_domain(self, client):
        """Test that wildcard doesn't match different domains."""
        # *.trusted.com should not match other domains
        response = client.get(
            "/api/protected", headers={"host": "trusted.com.evil.com"}
        )
        assert response.status_code == 400

        response = client.get("/api/protected", headers={"host": "nottrusted.com"})
        assert response.status_code == 400

    def test_health_check_paths_configuration(self):
        """Test that custom health check paths work."""
        app = FastAPI()

        # Configure with custom health check paths
        app.add_middleware(
            ConditionalTrustedHostMiddleware,
            allowed_hosts=["example.com"],
            health_check_paths=["/custom/health", "/readiness"],
        )

        @app.get("/custom/health")
        async def custom_health():
            return {"status": "OK"}

        @app.get("/readiness")
        async def readiness():
            return {"ready": True}

        @app.get("/api/protected")
        async def protected():
            return {"message": "protected"}

        client = TestClient(app)

        # Custom health paths should bypass validation
        response = client.get("/custom/health", headers={"host": "invalid.com"})
        assert response.status_code == 200

        response = client.get("/readiness", headers={"host": "invalid.com"})
        assert response.status_code == 200

        # Protected route should still require valid host
        response = client.get("/api/protected", headers={"host": "invalid.com"})
        assert response.status_code == 400

    def test_allow_all_hosts_wildcard(self):
        """Test that * in allowed_hosts allows all hosts."""
        app = FastAPI()

        app.add_middleware(
            ConditionalTrustedHostMiddleware,
            allowed_hosts=["*"],
        )

        @app.get("/api/test")
        async def test_endpoint():
            return {"message": "test"}

        client = TestClient(app)

        # Any host should be allowed
        response = client.get("/api/test", headers={"host": "any.domain.com"})
        assert response.status_code == 200

        response = client.get("/api/test", headers={"host": "malicious.com"})
        assert response.status_code == 200

    def test_cloud_run_startup_probe_scenario(self, client):
        """Test the specific Cloud Run startup probe scenario."""
        # Simulate Cloud Run startup probe with internal host
        response = client.get(
            "/api/health",
            headers={
                "host": "api-staging-59178042229.us-central1.run.app",
                "user-agent": "GoogleHC/1.0",
            },
        )
        # Should work even though host is not in allowed list
        assert response.status_code == 200
        assert response.json() == {"status": "OK"}

    def test_case_sensitivity(self, client):
        """Test that host matching is case-insensitive (as per HTTP spec)."""
        # HTTP host headers should be case-insensitive per RFC 3986
        # Our implementation now correctly handles case-insensitive matching
        response = client.get("/api/protected", headers={"host": "EXAMPLE.COM"})
        # Should work because implementation is now case-insensitive
        assert response.status_code == 200
        assert response.json() == {"message": "protected endpoint"}

        # Exact case match should also work
        response = client.get("/api/protected", headers={"host": "example.com"})
        assert response.status_code == 200
        assert response.json() == {"message": "protected endpoint"}

        # Mixed case should also work
        response = client.get("/api/protected", headers={"host": "Example.Com"})
        assert response.status_code == 200
        assert response.json() == {"message": "protected endpoint"}

    def test_health_check_path_exact_matching_security(self, client):
        """Test that health check paths use exact matching for security."""
        # /api/health should NOT match /api/health/detailed (security fix)
        response = client.get("/api/health/detailed", headers={"host": "invalid.com"})
        # This should be blocked by middleware because exact matching is now used
        assert response.status_code == 400
        assert response.text == "Invalid host header"

        # But exact health check paths should still work
        response = client.get("/api/health", headers={"host": "invalid.com"})
        assert response.status_code == 200


class TestSecurityScenarios:
    """Test security scenarios and edge cases."""

    def test_malicious_host_header_injection(self):
        """Test injection attempts in host headers."""
        app = FastAPI()
        app.add_middleware(
            ConditionalTrustedHostMiddleware,
            allowed_hosts=["trusted.com"],
        )

        @app.get("/api/test")
        async def test_endpoint():
            return {"message": "test"}

        client = TestClient(app)

        malicious_hosts = [
            "trusted.com\r\nX-Injected: true",  # HTTP header injection
            "trusted.com\x00evil.com",  # Null byte injection
            "<EMAIL>",  # Email-like injection
            "trusted.com:evil.com",  # Colon injection
            "trusted.com/../../etc/passwd",  # Path traversal attempt
            "trusted.com<script>alert(1)</script>",  # XSS attempt
            "trusted.com'; DROP TABLE users; --",  # SQL injection attempt
        ]

        for malicious_host in malicious_hosts:
            response = client.get("/api/test", headers={"host": malicious_host})
            assert response.status_code == 400, (
                f"Should block malicious host: {malicious_host}"
            )
            assert response.text == "Invalid host header"

    def test_unicode_host_normalization(self):
        """Test unicode and punycode handling."""
        app = FastAPI()
        app.add_middleware(
            ConditionalTrustedHostMiddleware,
            allowed_hosts=["example.com"],
        )

        @app.get("/api/test")
        async def test_endpoint():
            return {"message": "test"}

        client = TestClient(app)

        # Test punycode and ASCII-safe lookalike domains
        suspicious_hosts = [
            "examp1e.com",  # Digit substitution
            "exarnple.com",  # Character substitution (rn -> m)
            "example.co.m",  # Extra dot
            "example..com",  # Double dot
            "xn--e1afmkfd.xn--p1ai",  # Punycode example (safe ASCII)
            "example.c0m",  # Zero instead of 'o'
        ]

        for suspicious_host in suspicious_hosts:
            response = client.get("/api/test", headers={"host": suspicious_host})
            assert response.status_code == 400, (
                f"Should block suspicious host: {suspicious_host}"
            )

    def test_case_sensitivity_edge_cases(self):
        """Test various case combinations and edge cases."""
        app = FastAPI()
        app.add_middleware(
            ConditionalTrustedHostMiddleware,
            allowed_hosts=["Example.COM", "*.TRUSTED.org"],
        )

        @app.get("/api/test")
        async def test_endpoint():
            return {"message": "test"}

        client = TestClient(app)

        # All these should work due to case-insensitive matching
        valid_cases = [
            "example.com",
            "EXAMPLE.COM",
            "Example.Com",
            "api.trusted.org",
            "API.TRUSTED.ORG",
            "Api.Trusted.Org",
        ]

        for host in valid_cases:
            response = client.get("/api/test", headers={"host": host})
            assert response.status_code == 200, f"Should allow case variant: {host}"

    def test_wildcard_pattern_edge_cases(self):
        """Test edge cases in wildcard pattern matching."""
        app = FastAPI()
        app.add_middleware(
            ConditionalTrustedHostMiddleware,
            allowed_hosts=["*.example.com"],
        )

        @app.get("/api/test")
        async def test_endpoint():
            return {"message": "test"}

        client = TestClient(app)

        # Valid wildcard matches
        valid_hosts = [
            "api.example.com",
            "staging.example.com",
            "www.example.com",
            "a.example.com",
        ]

        for host in valid_hosts:
            response = client.get("/api/test", headers={"host": host})
            assert response.status_code == 200, f"Should allow valid subdomain: {host}"

        # Invalid attempts to bypass wildcard
        invalid_hosts = [
            "example.com",  # Missing subdomain
            "example.com.evil.com",  # Domain suffix attack
            "malicious.example.com.evil.com",  # Subdomain suffix attack
            ".example.com",  # Empty subdomain
            "api.example.com.evil.com",  # Double domain attack
        ]

        for host in invalid_hosts:
            response = client.get("/api/test", headers={"host": host})
            assert response.status_code == 400, (
                f"Should block invalid wildcard attempt: {host}"
            )

    def test_empty_and_malformed_hosts(self):
        """Test empty and malformed host headers."""
        app = FastAPI()
        app.add_middleware(
            ConditionalTrustedHostMiddleware,
            allowed_hosts=["example.com"],
        )

        @app.get("/api/test")
        async def test_endpoint():
            return {"message": "test"}

        client = TestClient(app)

        # Test malformed hosts
        malformed_hosts = [
            "",  # Empty host
            " ",  # Whitespace only
            "\t",  # Tab character
            "\n",  # Newline
            ".",  # Just a dot
            "..",  # Double dots
            "...",  # Triple dots
            "host with spaces",  # Spaces in hostname
            "host\twith\ttabs",  # Tabs in hostname
        ]

        for host in malformed_hosts:
            response = client.get("/api/test", headers={"host": host})
            assert response.status_code == 400, f"Should block malformed host: '{host}'"

    def test_health_check_path_bypass_attempts(self):
        """Test attempts to bypass security via health check paths."""
        app = FastAPI()
        app.add_middleware(
            ConditionalTrustedHostMiddleware,
            allowed_hosts=["trusted.com"],
        )

        @app.get("/api/health")
        async def health():
            return {"status": "OK"}

        @app.get("/api/protected")
        async def protected():
            return {"message": "protected"}

        client = TestClient(app)

        # Attempts to bypass using health path variations
        bypass_attempts = [
            "/api/health/../protected",  # Path traversal
            "/api/health/../../protected",  # More path traversal
            "/api/health%2F..%2Fprotected",  # URL encoded traversal
            "/api/health/extra/path",  # Extra path segments
            "/api/healthcheck",  # Similar but different path
            "/api/health.backup",  # File extension attempt
        ]

        for path in bypass_attempts:
            response = client.get(path, headers={"host": "malicious.com"})
            # Should either be blocked (400) or not found (404), but not successful (200)
            assert response.status_code in [400, 404], (
                f"Should not allow bypass via: {path}"
            )
            if response.status_code == 400:
                assert response.text == "Invalid host header"
