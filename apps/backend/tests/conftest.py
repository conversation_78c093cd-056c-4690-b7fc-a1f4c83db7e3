import os
import subprocess
import uuid
import logging
from pathlib import Path
from typing import As<PERSON><PERSON>enerator
from unittest.mock import <PERSON><PERSON><PERSON><PERSON>, <PERSON>Mock, patch

import pytest
from httpx import ASGITransport, AsyncClient
from sqlalchemy.ext.asyncio import (
    AsyncConnection,
    AsyncEngine,
    AsyncSession,
    create_async_engine,
    async_sessionmaker,
)

# Set up essential environment variables first, before any imports that might load settings
os.environ.setdefault("DATABASE_URL", "******************************/test")
os.environ.setdefault(
    "DATABASE_ASYNC_URL", "postgresql+asyncpg://test:test@db:5432/test"
)
os.environ.setdefault("REDIS_URL", "redis://redis:6379/1")
os.environ.setdefault("CLERK_API_KEY", "test_clerk_api_key")
os.environ.setdefault("CLERK_JWT_PUBLIC_KEY", "test_clerk_jwt_public_key")
os.environ.setdefault("CLERK_WEBHOOK_SECRET", "test_clerk_webhook_secret")
os.environ.setdefault("STORAGE_BUCKET", "test_storage_bucket")
os.environ.setdefault("CDN_URL", "https://test-cdn.example.com")
os.environ.setdefault("PUBSUB_PROJECT_ID", "test_project_id")

from a2a_platform.config.settings import Settings, get_settings
from a2a_platform.db import get_db_session  # Removed Base
from a2a_platform.db.models.user import User
from a2a_platform.main import app  # Import your FastAPI app


@pytest.fixture(scope="session", autouse=True)
def mock_database_for_no_db_tests():
    """
    Session-scoped fixture to mock database connections for database-free tests.
    This runs before any other fixtures and prevents database connections at import time.
    """
    test_db_mode = os.environ.get("TEST_DATABASE_MODE", "postgresql")

    if test_db_mode == "no_db":
        # Mock the database engine creation at the module level
        with (
            patch("a2a_platform.db.create_async_engine") as mock_create_engine,
            patch(
                "a2a_platform.db.session.create_async_engine"
            ) as mock_session_create_async_engine,
            patch(
                "a2a_platform.db.session.create_engine"
            ) as mock_session_create_engine,
        ):
            # Return a mock engine that doesn't actually connect
            mock_engine = AsyncMock()
            mock_create_engine.return_value = mock_engine
            mock_session_create_async_engine.return_value = mock_engine
            mock_session_create_engine.return_value = mock_engine

            # Mock the session factory
            with (
                patch("a2a_platform.db.async_sessionmaker") as mock_sessionmaker,
                patch(
                    "a2a_platform.db.session.sessionmaker"
                ) as mock_session_sessionmaker,
            ):
                mock_session = AsyncMock()
                mock_sessionmaker.return_value = lambda: mock_session
                mock_session_sessionmaker.return_value = lambda: mock_session

                # Mock get_settings to return a mock DATABASE_URL
                with patch(
                    "a2a_platform.config.settings.get_settings"
                ) as mock_get_settings:
                    mock_settings = MagicMock()
                    mock_settings.ASYNC_DATABASE_URL = "mock://localhost/testdb"
                    mock_settings.DATABASE_URL = "mock://localhost/testdb"
                    mock_settings.SQL_ECHO = False
                    mock_get_settings.return_value = mock_settings

                    yield
    else:
        yield


@pytest.fixture(scope="session", autouse=True)
def setup_test_database_once():
    """
    Session-scoped fixture to ensure the test database exists and has been migrated.
    This runs once per test session and is separate from engine/session creation.
    """
    import os

    # Skip database setup for database-free tests and SQLite tests
    test_db_mode = os.environ.get("TEST_DATABASE_MODE", "postgresql")
    if test_db_mode == "no_db":
        print("Skipping database setup for database-free tests")
        yield
        return
    elif test_db_mode == "sqlite":
        print(
            "Skipping database setup script for SQLite tests (using in-memory database)"
        )
        yield
        return

    # Skip database setup if we're running in Docker mode (DOCKER_ENV=true)
    # The Docker test runner handles database initialization itself
    if os.environ.get("DOCKER_ENV") == "true":
        print(
            "Skipping database setup - running in Docker mode with pre-initialized database"
        )
        yield
        return

    # Only run database setup for local non-Docker testing
    script_path = (
        Path(__file__).parent.parent.parent.parent / "scripts" / "setup-test-db.sh"
    )
    if script_path.exists():
        print(f"Setting up test database using {script_path}")
        try:
            # Use the appropriate flag based on environment
            if os.environ.get("CI"):
                # In CI, use local PostgreSQL with --ci flag
                subprocess.run(
                    [str(script_path), "--ci"],
                    check=True,
                    capture_output=True,
                    text=True,
                )
            else:
                # Locally, use Docker PostgreSQL (default)
                subprocess.run(
                    [str(script_path)], check=True, capture_output=True, text=True
                )
        except subprocess.CalledProcessError as e:
            print(f"Warning: Failed to run setup-test-db.sh: {e}")
            print(f"Stdout: {e.stdout}")
            print(f"Stderr: {e.stderr}")

    else:
        print(f"Warning: setup-test-db.sh not found at {script_path}")

    # Clear cache before getting settings
    try:
        get_settings.cache_clear()
    except AttributeError:
        # If cache_clear is not available, just continue
        pass

    # Get settings to ensure they're loaded with environment variables
    settings = get_settings()  # Now settings should reflect environment variables
    print(f"Test database setup completed. Using DATABASE_URL: {settings.DATABASE_URL}")

    yield


@pytest.fixture(scope="function")
async def app_async_engine(
    setup_test_database_once, request
) -> AsyncGenerator[AsyncEngine | None, None]:
    """
    Function-scoped fixture that creates a new engine for each test.
    This ensures the engine is bound to the test function's event loop.
    For database-free tests (marked with @pytest.mark.no_db), returns None.
    """
    import os

    # Check if this is a database-free test
    test_markers = [mark.name for mark in request.node.iter_markers()]
    test_db_mode = os.environ.get("TEST_DATABASE_MODE", "postgresql")

    if "no_db" in test_markers or test_db_mode == "no_db":
        # For database-free tests, don't create an engine at all
        yield None
        return

    # Get settings with the correct database URL
    settings = get_settings()

    # Create a new engine for this test function
    # This ensures the engine is bound to the test function's event loop
    if test_db_mode == "sqlite":
        # For SQLite, use an in-memory database for each test
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:", echo=settings.SQL_ECHO
        )
    else:
        # For PostgreSQL, use the configured async database URL
        engine = create_async_engine(
            settings.ASYNC_DATABASE_URL, echo=settings.SQL_ECHO
        )

    # Create tables for this test to ensure isolation
    # async with engine.begin() as conn:
    #     # Create all tables for this test
    #     await conn.run_sync(Base.metadata.create_all)

    yield engine

    # Clean up after the test
    await engine.dispose()


@pytest.fixture(scope="function")
async def connection_for_test(
    app_async_engine: AsyncEngine | None, request
) -> AsyncGenerator[AsyncConnection | None, None]:
    """
    Provides a single database connection with a top-level transaction for a test.
    The transaction is rolled back at the end of the test.
    Returns None for database-free tests.
    """
    if app_async_engine is None:
        test_markers = [mark.name for mark in request.node.iter_markers()]
        test_db_mode = os.environ.get("TEST_DATABASE_MODE", "postgresql")
        if "no_db" in test_markers or test_db_mode == "no_db":
            yield None
            return
        else:
            raise RuntimeError(
                "app_async_engine is None but test is not marked as no_db"
            )

    async with app_async_engine.connect() as connection:
        # Start a top-level transaction for the test.
        transaction = await connection.begin()
        try:
            yield connection
        finally:
            # Always rollback the test transaction to ensure isolation.
            # Check if transaction is still active before rolling back,
            # as it might have been committed by a session already.
            try:
                if transaction.is_active:
                    await transaction.rollback()
            except Exception:
                # Transaction might already be committed/rolled back
                # This is expected behavior when sessions commit within tests
                pass
            # Connection is closed by the async with block


@pytest.fixture(scope="function")
def app_testing_session_factory(  # Renamed from app_testing_session_local
    app_async_engine: AsyncEngine | None,
) -> async_sessionmaker | None:
    """
    Function-scoped fixture that creates a sessionmaker.
    This sessionmaker can be used to create sessions bound to a specific connection.
    Returns None for database-free tests.
    """
    if app_async_engine is None:
        # For database-free tests, return None
        return None

    return async_sessionmaker(
        bind=app_async_engine,  # Default bind, can be overridden by session(bind=connection)
        class_=AsyncSession,
        expire_on_commit=False,
    )


def override_get_db_session_for_tests(
    session: AsyncSession,
) -> AsyncSession:
    """Helper function to override the get_db_session dependency."""
    return session


# Apply the override globally for the test session
@pytest.fixture(scope="session", autouse=True)
def apply_db_session_override():
    """
    Session-scoped fixture to set up the initial dependency override.
    This will be overridden by the function-scoped fixture for each test.
    """
    # We'll keep this for backward compatibility, but individual tests will override it
    yield
    app.dependency_overrides.clear()


@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """
    Session-scoped fixture to set up comprehensive test environment.

    Sets up AI service mocking, mock API keys, and other test configuration.
    """
    import os

    # Set up AI service environment variables if not already set
    os.environ.setdefault("OPENAI_API_KEY", "sk-test-mock-key-for-testing-only")
    os.environ.setdefault("ANTHROPIC_API_KEY", "sk-ant-test-mock-key-for-testing-only")
    os.environ.setdefault("GEMINI_API_KEY", "AIza-test-mock-key-for-testing-only")
    os.environ.setdefault("GOOGLE_API_KEY", "AIza-test-mock-key-for-testing-only")
    os.environ.setdefault("AI_SERVICE_MOCK_MODE", "true")
    os.environ.setdefault("AI_RESPONSE_ENABLED", "true")
    os.environ.setdefault("ENABLE_TEST_AUTH_BYPASS", "true")

    yield


@pytest.fixture(scope="function")
def mock_ai_services_for_isolation():
    """
    Optional AI service mock for tests that need complete isolation.

    This fixture provides AI service mocking without interfering with
    tests that have their own specific mocking requirements.
    Use this explicitly in tests that need it.
    """
    from unittest.mock import patch

    # Mock PydanticAI response service
    pydantic_ai_path = "a2a_platform.services.pydantic_ai_response_service.PydanticAIResponseService.generate_response"

    with patch(pydantic_ai_path) as mock_pydantic:
        # Default response for PydanticAI service
        mock_pydantic.return_value = (
            "This is a test AI response from mock service.",
            True,
        )

        yield {"pydantic_ai": mock_pydantic}


@pytest.fixture(scope="function", autouse=True)
async def cleanup_db_connections():
    """
    Fixture to ensure database connections are properly cleaned up after each test.
    This prevents connection leaks and SQLAlchemy pool warnings.
    """
    # Track connections before test
    try:
        from tests.utils.test_metrics import connection_tracker

        start_connections = connection_tracker.current_connections
    except ImportError:
        start_connections = 0

    yield

    # Clean up any remaining database connections
    try:
        from a2a_platform.db import _engine as main_engine
        from a2a_platform.db.session import _async_engine as session_engine

        # Dispose of engines if they exist
        if main_engine is not None:
            await main_engine.dispose()
        if session_engine is not None:
            await session_engine.dispose()
    except Exception:
        # If cleanup fails, we don't want to fail the test
        pass

    # Check for connection leaks
    try:
        from tests.utils.test_metrics import connection_tracker

        if connection_tracker.current_connections > start_connections:
            leaked = connection_tracker.current_connections - start_connections
            logger = logging.getLogger(__name__)
            logger.warning(f"Test leaked {leaked} database connections")
    except ImportError:
        pass


@pytest.fixture(scope="function", autouse=True)
async def smart_db_session_override(
    request,
    app_testing_session_factory: async_sessionmaker
    | None,  # Keep this name for clarity if factory is still used
    connection_for_test: AsyncConnection | None,
):
    """
    Smart database session override with proper connection management.
    For DB tests, provides new sessions for each get_db_session call,
    all bound to the same connection_for_test and its transaction.
    For no_db tests, provides a mock session.

    Ensures proper session cleanup to prevent connection leaks.
    """
    import os
    from unittest.mock import AsyncMock

    # Store the original override if it exists
    original_override = app.dependency_overrides.get(get_db_session)

    # Get test markers
    test_markers = [mark.name for mark in request.node.iter_markers()]
    test_db_mode = os.environ.get("TEST_DATABASE_MODE", "postgresql")

    # Determine which session to use
    if (
        "no_db" in test_markers
        or test_db_mode == "no_db"
        or connection_for_test is None
    ):
        mock_session = AsyncMock(spec=AsyncSession)
        mock_session.commit = AsyncMock()
        mock_session.rollback = AsyncMock()
        mock_session.close = AsyncMock()
        mock_session.refresh = AsyncMock()
        mock_session.flush = AsyncMock()
        mock_session.execute = AsyncMock()
        mock_session.scalar = AsyncMock()
        mock_session.scalars = AsyncMock()
        mock_session.add = MagicMock()
        mock_session.__aenter__ = AsyncMock(return_value=mock_session)
        mock_session.__aexit__ = AsyncMock(return_value=None)
        app.dependency_overrides[get_db_session] = lambda: mock_session
    elif connection_for_test is not None:  # Simplified condition

        async def get_session_for_request() -> AsyncGenerator[AsyncSession, None]:
            # Create a new session using the same engine as connection_for_test
            # with proper async context management to prevent connection leaks
            async with AsyncSession(
                bind=connection_for_test.engine,
                expire_on_commit=False,
            ) as session:
                try:
                    yield session
                except Exception:
                    await session.rollback()
                    raise
                # Session cleanup is handled by async with context manager

        app.dependency_overrides[get_db_session] = get_session_for_request

    yield

    if original_override:
        app.dependency_overrides[get_db_session] = original_override
    else:
        app.dependency_overrides.pop(get_db_session, None)


@pytest.fixture(scope="function")
async def db_session_real(
    connection_for_test: AsyncConnection | None,
    request,
) -> AsyncGenerator[AsyncSession, None]:
    """
    Provide a real database session for test setup, bound to connection_for_test.
    This session participates in the transaction managed by connection_for_test.

    Uses proper async context management to ensure connections are returned to the pool.
    """
    test_markers = [mark.name for mark in request.node.iter_markers()]

    if "no_db" in test_markers:
        mock_session = AsyncMock(spec=AsyncSession)
        mock_session.__aenter__ = AsyncMock(return_value=mock_session)
        mock_session.__aexit__ = AsyncMock(return_value=None)
        mock_session.add = MagicMock()
        mock_session.commit = AsyncMock()  # For await db_session_real.commit()
        mock_session.rollback = AsyncMock()
        mock_session.refresh = AsyncMock()
        mock_session.close = AsyncMock()
        yield mock_session
        return

    if connection_for_test is None:
        raise RuntimeError(
            "db_session_real requested for a DB test, but no database connection (connection_for_test) is available."
        )

    async with AsyncSession(
        bind=connection_for_test.engine,
        expire_on_commit=False,
    ) as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        # Session cleanup is handled by async with context manager


@pytest.fixture(scope="function")
async def test_client(
    # db_session_real is implicitly handled by smart_db_session_override for app calls
    # If test_client itself needs to do setup using a session, it should use db_session_real
    # For now, assume smart_db_session_override covers app's DB needs.
    # If test_client needs db_session_real for direct setup, add it back as a direct param.
    # Let's ensure smart_db_session_override runs because it's autouse=True
    # and connection_for_test is also available.
    connection_for_test: AsyncConnection | None,  # Make sure DB setup is ready
    request,
) -> AsyncGenerator[AsyncClient, None]:
    """
    Provide a test client for API testing.
    Relies on smart_db_session_override (autouse) for app's DB session needs.
    """
    # Check if this is a no_db test. If so, connection_for_test might be None.
    # The app's get_db_session will be mocked by smart_db_session_override.
    async with AsyncClient(
        transport=ASGITransport(app=app, raise_app_exceptions=False),
        base_url="http://test",
    ) as client:
        yield client


@pytest.fixture(scope="function")
async def async_client(
    test_client: AsyncClient,  # Just alias test_client
) -> AsyncGenerator[AsyncClient, None]:
    """
    Provide an async client for GraphQL API testing. Alias for test_client.
    """
    yield test_client


# Mock the settings
@pytest.fixture(autouse=True)
def mock_settings():
    """
    Mock the settings for testing.
    This fixture runs automatically for all tests.
    """

    # Check if we're in a CI environment
    is_ci = (
        os.environ.get("CI", "").lower() == "true"
        or os.environ.get("GITHUB_ACTIONS", "").lower() == "true"
    )

    # Check if we can resolve the 'db' hostname to determine if we're in Docker
    is_docker = False
    try:
        import socket

        socket.gethostbyname("db")
        is_docker = True
    except socket.gaierror:
        is_docker = False

    # Use 'localhost' as hostname for CI or non-Docker environments, 'db' for Docker
    db_host = "localhost" if (is_ci or not is_docker) else "db"
    redis_host = "localhost" if (is_ci or not is_docker) else "redis"

    # Handle CORS_ORIGINS from environment
    # If it's a string that looks like a JSON array, parse it
    from a2a_platform.config.settings import parse_cors_origins

    cors_origins = parse_cors_origins(
        os.environ.get("CORS_ORIGINS", "https://localhost:5173")
    )

    # Determine database URL based on test mode
    test_db_mode = os.environ.get("TEST_DATABASE_MODE", "postgresql")
    if test_db_mode == "sqlite":
        database_url = "sqlite+aiosqlite:///./test.db"
    elif test_db_mode == "no_db":
        database_url = "mock://localhost/testdb"
    else:
        # Default to PostgreSQL
        database_url = (
            f"postgresql+asyncpg://postgres:postgres@{db_host}:5432/a2a_platform_test"
        )

    mock_settings_dict = {
        "DEBUG": True,
        "APP_TITLE": "A2A Platform API Test",
        "APP_VERSION": "0.1.0",
        "DATABASE_URL": database_url,
        "ASYNC_DATABASE_URL": database_url,
        "REDIS_URL": f"redis://{redis_host}:6379/1",
        "CLERK_API_KEY": "test_clerk_api_key",
        "CLERK_JWT_PUBLIC_KEY": "test_clerk_jwt_public_key",
        "CLERK_WEBHOOK_SECRET": "test_clerk_webhook_signing_secret",
        "CORS_ORIGINS": cors_origins,
        "STORAGE_BUCKET": "test_storage_bucket",
        "CDN_URL": "https://test-cdn.example.com",
        "PUBSUB_PROJECT_ID": "test_project_id",
    }

    # Create a mock Settings object
    mocked_settings = Settings(**mock_settings_dict)

    with patch(
        "a2a_platform.config.settings.get_settings", return_value=mocked_settings
    ) as mock_get:
        yield mock_get


# Mock the database session (this is the general mock, db_session_real is for "real" operations)
@pytest.fixture
async def db_session():  # This is likely for unit tests not hitting real DB via app
    """
    Create a mock database session for testing.
    """
    mock_session = AsyncMock(spec=AsyncSession)
    mock_session.commit = AsyncMock()
    mock_session.rollback = AsyncMock()
    mock_session.close = AsyncMock()

    # Configure execute to return a mock result that can be used in tests
    mock_result = MagicMock()
    mock_result.scalar_one_or_none = MagicMock()
    mock_session.execute = AsyncMock(return_value=mock_result)

    mock_session.refresh = AsyncMock()
    mock_session.add = MagicMock()

    yield mock_session


# Test fixtures for integration tests
@pytest.fixture
async def test_user(
    db_session_real: AsyncSession,
) -> User:  # Uses the new db_session_real
    """Create a test user in the database for integration tests."""
    from a2a_platform.schemas.user import UserCreate
    from a2a_platform.services.user_service import create_user

    clerk_user_id = f"test_clerk_user_{uuid.uuid4()}"
    email = f"test_{uuid.uuid4()}@example.com"
    user_data = UserCreate(
        clerk_user_id=clerk_user_id,
        email=email,
        timezone="UTC",
    )
    user = await create_user(db_session_real, user_data)
    return user


@pytest.fixture
async def test_assistant_id(
    test_user: User,
    db_session_real: AsyncSession,  # Uses the new db_session_real
) -> uuid.UUID:
    """Create a test assistant in the database and return its ID."""
    from a2a_platform.schemas.assistant_schemas import AssistantCreate
    from a2a_platform.services.assistant_service import AssistantService

    assistant_service = AssistantService(db_session=db_session_real)
    assistant_data = AssistantCreate(
        name="Test Assistant",
        backstory="A test assistant for integration testing",
        avatar_file_id=None,
        configuration={},
    )

    assistant = await assistant_service.create_personal_assistant(
        user_id=test_user.id, assistant_data=assistant_data
    )
    return assistant.id


# Mock the Clerk client
@pytest.fixture
def mock_clerk_client():
    """
    Create a mock Clerk client for testing.
    """
    with patch("a2a_platform.auth.clerk.Clerk") as MockClerk:
        mock_clerk = MagicMock()
        mock_clerk.users.get = AsyncMock()
        mock_clerk.users.create = AsyncMock()
        mock_clerk.users.update = AsyncMock()
        mock_clerk.users.delete = AsyncMock()
        mock_clerk.sessions.verify_session = AsyncMock(
            return_value={"sub": "test_user_id"}
        )
        mock_clerk.webhooks.verify_signature = MagicMock(
            return_value=True
        )  # Changed from verify_signature
        MockClerk.return_value = mock_clerk

        # Also patch the clerk instance
        with patch("a2a_platform.auth.clerk.clerk", mock_clerk):
            yield mock_clerk


# Fixture for providing a mocked AsyncSession for unit tests
@pytest.fixture
async def mock_db_session() -> AsyncMock:
    session = AsyncMock(spec=AsyncSession)
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    session.close = AsyncMock()

    mock_execution_result = MagicMock()
    mock_execution_result.scalar_one_or_none = MagicMock(return_value=None)
    mock_execution_result.scalar = MagicMock(return_value=None)

    mock_scalars_proxy = MagicMock()
    mock_scalars_proxy.all = MagicMock(return_value=[])
    mock_execution_result.scalars = MagicMock(return_value=mock_scalars_proxy)

    mock_execution_result.first = MagicMock(return_value=None)

    session.execute = AsyncMock(return_value=mock_execution_result)
    session.refresh = AsyncMock()
    session.add = MagicMock()
    session.flush = AsyncMock()
    return session


@pytest.fixture(
    scope="session", autouse=True
)  # Keep as session autouse for broad effect
def clear_initial_settings_cache_once():
    try:
        get_settings.cache_clear()
    except AttributeError:
        # If cache_clear is not available, just continue
        pass


# Additional fixtures for GraphQL testing
@pytest.fixture
def test_user_id(test_user: User) -> uuid.UUID:
    """Extract the user ID from the test_user fixture."""
    return test_user.id


@pytest.fixture
def mock_clerk_jwt_valid() -> str:
    """Provide a mock valid JWT token for authentication testing."""
    return "mock_jwt_token_valid"


# Performance and bulk operation fixtures
@pytest.fixture
async def bulk_data_factory(db_session_real: AsyncSession):
    """Provide a bulk data factory for efficient test data creation."""
    try:
        from tests.utils.bulk_operations import BulkTestDataFactory

        return BulkTestDataFactory(db_session_real)
    except ImportError:
        # Bulk operations not available, provide mock
        from unittest.mock import AsyncMock

        return AsyncMock()


@pytest.fixture
async def simple_chat_scenario(db_session_real: AsyncSession):
    """Create a simple chat scenario with one user, assistant, and conversation."""
    try:
        from tests.utils.bulk_operations import TestScenarios

        return await TestScenarios.simple_chat_scenario(db_session_real)
    except ImportError:
        # Return empty scenario if bulk operations not available
        return {"users": [], "assistants": [], "conversations": [], "messages": []}


@pytest.fixture
async def multi_user_scenario(db_session_real: AsyncSession):
    """Create a multi-user scenario for testing user isolation."""
    try:
        from tests.utils.bulk_operations import TestScenarios

        return await TestScenarios.multi_user_scenario(db_session_real)
    except ImportError:
        # Return empty scenario if bulk operations not available
        return {"users": [], "assistants": [], "conversations": [], "messages": []}


@pytest.fixture
def sqlite_mode():
    """
    Force SQLite mode for tests marked with @pytest.mark.fast_db.

    This fixture automatically configures the test to use SQLite for faster execution
    when the test is compatible with SQLite operations.
    """
    import os

    original_mode = os.environ.get("TEST_DATABASE_MODE")
    os.environ["TEST_DATABASE_MODE"] = "sqlite"
    yield
    if original_mode is not None:
        os.environ["TEST_DATABASE_MODE"] = original_mode
    else:
        os.environ.pop("TEST_DATABASE_MODE", None)
