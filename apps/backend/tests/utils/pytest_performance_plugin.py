"""
Pytest Plugin for Automatic Test Performance Monitoring

This plugin automatically tracks test execution times and flags slow tests.
"""

import pytest
import psutil
import os

from .test_metrics import (
    performance_monitor,
    connection_tracker,
    log_test_performance_summary,
)


class PerformancePlugin:
    """Pytest plugin for automatic performance monitoring."""

    def __init__(self):
        self.test_start_times = {}
        self.test_memory_start = {}

    @pytest.hookimpl(hookwrapper=True)
    def pytest_runtest_call(self, item):
        """Hook that runs around each test execution."""
        test_name = item.nodeid

        # Record start metrics
        performance_monitor.start_test(test_name)

        # Record memory usage at start
        try:
            process = psutil.Process(os.getpid())
            memory_start = process.memory_info().rss / 1024 / 1024  # MB
            self.test_memory_start[test_name] = memory_start
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            self.test_memory_start[test_name] = None

        # Execute the test
        outcome = yield

        # Record end metrics
        status = "passed"
        if outcome.excinfo is not None:
            if outcome.excinfo[0] == pytest.skip.Exception:
                status = "skipped"
            else:
                status = "failed"

        # Calculate memory peak
        memory_peak = None
        try:
            process = psutil.Process(os.getpid())
            memory_end = process.memory_info().rss / 1024 / 1024  # MB
            memory_start = self.test_memory_start.get(test_name)
            if memory_start is not None:
                memory_peak = max(memory_start, memory_end)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass

        # Record the test completion
        performance_monitor.end_test(
            test_name=test_name, status=status, memory_peak_mb=memory_peak
        )

    def pytest_sessionfinish(self, session, exitstatus):
        """Hook that runs at the end of the test session."""
        log_test_performance_summary()

        # Save metrics to file if environment variable is set
        metrics_file = os.environ.get("TEST_METRICS_FILE")
        if metrics_file:
            performance_monitor.save_metrics(metrics_file)


def pytest_configure(config):
    """Register the performance plugin."""
    if not hasattr(config, "_performance_plugin_registered"):
        config.pluginmanager.register(PerformancePlugin(), "performance_monitor")
        config._performance_plugin_registered = True


@pytest.fixture(autouse=True)
def track_database_connections():
    """Automatically track database connections for each test."""
    # Reset connection tracker for this test
    start_connections = connection_tracker.current_connections

    yield

    # Check for connection leaks after test
    end_connections = connection_tracker.current_connections
    if end_connections > start_connections:
        leaked = end_connections - start_connections
        pytest.current_test_name = getattr(pytest.current_item, "nodeid", "unknown")
        import logging

        logging.getLogger(__name__).warning(
            f"Test {pytest.current_test_name} leaked {leaked} database connections"
        )


@pytest.fixture
def performance_metrics():
    """Provide access to the performance monitor for tests that need it."""
    return performance_monitor


@pytest.fixture
def connection_metrics():
    """Provide access to the connection tracker for tests that need it."""
    return connection_tracker
