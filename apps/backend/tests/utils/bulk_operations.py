"""
Bulk Database Operations for Test Performance

This module provides utilities for creating test data efficiently using bulk operations
to reduce test execution time and database overhead.
"""

import uuid
from typing import List, Dict, Any, Type, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import DeclarativeBase

from a2a_platform.db.models.user import User
from a2a_platform.db.models.assistant import Assistant
from a2a_platform.db.models.conversation import Conversation
from a2a_platform.db.models.chat_message import ChatMessage
from a2a_platform.db.models.registered_agent import RegisteredAgent


class BulkTestDataFactory:
    """Factory for creating test data efficiently using bulk operations."""

    def __init__(self, db_session: AsyncSession):
        """Initialize with a database session."""
        self.db = db_session

    async def create_users_bulk(self, count: int, **kwargs) -> List[User]:
        """
        Create multiple users efficiently using bulk operations.

        Args:
            count: Number of users to create
            **kwargs: Additional user attributes to override defaults

        Returns:
            List of created User objects
        """
        users = []
        for i in range(count):
            unique_id = str(uuid.uuid4())[:8]
            user_data = {
                "clerk_user_id": f"test_user_{unique_id}_{i}",
                "email": f"test_{unique_id}_{i}@example.com",
                "timezone": "UTC",
                "is_pa_setup_complete": False,
                **kwargs,  # Allow overriding defaults
            }
            users.append(User(**user_data))

        # Bulk insert
        self.db.add_all(users)
        await self.db.flush()  # Assign IDs without committing

        return users

    async def create_assistants_bulk(
        self, users: List[User], **kwargs
    ) -> List[Assistant]:
        """
        Create assistants for multiple users efficiently.

        Args:
            users: List of User objects to create assistants for
            **kwargs: Additional assistant attributes

        Returns:
            List of created Assistant objects
        """
        assistants = []
        for i, user in enumerate(users):
            assistant_data = {
                "user_id": user.id,
                "name": f"Test Assistant {i}",
                "backstory": f"Test assistant {i} for user {user.email}",
                "configuration": {"ai": {"enabled": True}},
                **kwargs,
            }
            assistants.append(Assistant(**assistant_data))

        self.db.add_all(assistants)
        await self.db.flush()

        return assistants

    async def create_conversations_bulk(
        self, users: List[User], assistants: Optional[List[Assistant]] = None, **kwargs
    ) -> List[Conversation]:
        """
        Create conversations efficiently.

        Args:
            users: List of User objects
            assistants: Optional list of Assistant objects (if None, creates without assistant)
            **kwargs: Additional conversation attributes

        Returns:
            List of created Conversation objects
        """
        conversations = []
        for i, user in enumerate(users):
            assistant_id = None
            if assistants and i < len(assistants):
                assistant_id = assistants[i].id

            conversation_data = {
                "user_id": user.id,
                "assistant_id": assistant_id,
                **kwargs,
            }
            conversations.append(Conversation(**conversation_data))

        self.db.add_all(conversations)
        await self.db.flush()

        return conversations

    async def create_messages_bulk(
        self,
        conversations: List[Conversation],
        messages_per_conversation: int = 5,
        **kwargs,
    ) -> List[ChatMessage]:
        """
        Create chat messages efficiently.

        Args:
            conversations: List of Conversation objects
            messages_per_conversation: Number of messages to create per conversation
            **kwargs: Additional message attributes

        Returns:
            List of created ChatMessage objects
        """
        messages = []
        for conv_idx, conversation in enumerate(conversations):
            for msg_idx in range(messages_per_conversation):
                # Alternate between user and agent messages
                sender_role = "user" if msg_idx % 2 == 0 else "agent"

                message_data = {
                    "conversation_id": conversation.id,
                    "sender_role": sender_role,
                    "content": {
                        "parts": [
                            {
                                "type": "text",
                                "content": f"Test message {msg_idx} in conversation {conv_idx}",
                            }
                        ]
                    },
                    **kwargs,
                }
                messages.append(ChatMessage(**message_data))

        self.db.add_all(messages)
        await self.db.flush()

        return messages

    async def create_registered_agents_bulk(
        self, count: int, **kwargs
    ) -> List[RegisteredAgent]:
        """
        Create registered agents efficiently.

        Args:
            count: Number of agents to create
            **kwargs: Additional agent attributes

        Returns:
            List of created RegisteredAgent objects
        """
        agents = []
        for i in range(count):
            unique_id = str(uuid.uuid4())[:8]
            agent_data = {
                "agent_definition_id": f"test_agent_{unique_id}_{i}",
                "name": f"Test Agent {i}",
                "version": "1.0.0",
                "description": f"Test agent {i} for bulk testing",
                "endpoint_url": f"http://localhost:8001/agent_{i}",
                "async_queue_name": f"test_queue_{i}",
                "capabilities": {"test": True, "index": i},
                "skills": [{"name": f"skill_{i}", "description": f"Test skill {i}"}],
                "status": "active",
                "developer_id": f"dev_{unique_id}",
                "pricing_info": {"type": "free"},
                "review_status": "approved",
                **kwargs,
            }
            agents.append(RegisteredAgent(**agent_data))

        self.db.add_all(agents)
        await self.db.flush()

        return agents

    async def create_complete_test_scenario(
        self, user_count: int = 5, messages_per_conversation: int = 3
    ) -> Dict[str, List[Any]]:
        """
        Create a complete test scenario with users, assistants, conversations, and messages.

        Args:
            user_count: Number of users to create
            messages_per_conversation: Number of messages per conversation

        Returns:
            Dictionary containing all created objects
        """
        # Create all entities in dependency order
        users = await self.create_users_bulk(user_count)
        assistants = await self.create_assistants_bulk(users)
        conversations = await self.create_conversations_bulk(users, assistants)
        messages = await self.create_messages_bulk(
            conversations, messages_per_conversation
        )

        return {
            "users": users,
            "assistants": assistants,
            "conversations": conversations,
            "messages": messages,
        }


async def cleanup_test_data(
    db_session: AsyncSession, *model_lists: List[DeclarativeBase]
) -> None:
    """
    Efficiently clean up test data by deleting in reverse dependency order.

    Args:
        db_session: Database session
        *model_lists: Lists of model instances to delete
    """
    # Flatten all model instances
    all_models = []
    for model_list in model_lists:
        if isinstance(model_list, list):
            all_models.extend(model_list)
        else:
            all_models.append(model_list)

    # Group by model type for efficient deletion
    model_groups: Dict[Type, List[Any]] = {}
    for model in all_models:
        model_type = type(model)
        if model_type not in model_groups:
            model_groups[model_type] = []
        model_groups[model_type].append(model)

    # Delete in reverse dependency order (messages -> conversations -> assistants -> users)
    deletion_order = [ChatMessage, Conversation, Assistant, User, RegisteredAgent]

    for model_type in deletion_order:
        if model_type in model_groups:
            for model in model_groups[model_type]:
                await db_session.delete(model)

    # Delete any remaining model types not in the order
    for model_type, models in model_groups.items():
        if model_type not in deletion_order:
            for model in models:
                await db_session.delete(model)


# Convenience fixtures for common test scenarios
class TestScenarios:
    """Pre-defined test scenarios for common testing needs."""

    @staticmethod
    async def simple_chat_scenario(db_session: AsyncSession) -> Dict[str, Any]:
        """Create a simple scenario with one user, assistant, conversation, and a few messages."""
        factory = BulkTestDataFactory(db_session)
        return await factory.create_complete_test_scenario(
            user_count=1, messages_per_conversation=3
        )

    @staticmethod
    async def multi_user_scenario(db_session: AsyncSession) -> Dict[str, Any]:
        """Create a scenario with multiple users for testing user isolation."""
        factory = BulkTestDataFactory(db_session)
        return await factory.create_complete_test_scenario(
            user_count=5, messages_per_conversation=2
        )

    @staticmethod
    async def agent_marketplace_scenario(db_session: AsyncSession) -> Dict[str, Any]:
        """Create a scenario with multiple agents for marketplace testing."""
        factory = BulkTestDataFactory(db_session)
        agents = await factory.create_registered_agents_bulk(10)
        return {"agents": agents}
