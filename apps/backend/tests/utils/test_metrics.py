"""
Test Performance Metrics and Monitoring

This module provides utilities for tracking test performance, identifying slow tests,
and monitoring database connection usage to ensure optimal test execution.
"""

import time
import logging
from typing import Dict, List, Optional
from dataclasses import dataclass, field
from pathlib import Path
import json

logger = logging.getLogger(__name__)


@dataclass
class TestMetrics:
    """Container for test execution metrics."""

    test_name: str
    duration: float
    status: str  # "passed", "failed", "skipped"
    db_connections_used: int = 0
    memory_peak_mb: Optional[float] = None
    timestamp: float = field(default_factory=time.time)


class TestPerformanceMonitor:
    """Monitor test performance and collect metrics."""

    def __init__(self, slow_test_threshold: float = 5.0):
        """
        Initialize the performance monitor.

        Args:
            slow_test_threshold: Tests taking longer than this (seconds) are flagged as slow
        """
        self.slow_test_threshold = slow_test_threshold
        self.metrics: List[TestMetrics] = []
        self._start_times: Dict[str, float] = {}

    def start_test(self, test_name: str) -> None:
        """Record the start time for a test."""
        self._start_times[test_name] = time.time()

    def end_test(self, test_name: str, status: str, **kwargs) -> TestMetrics:
        """
        Record the end time for a test and calculate metrics.

        Args:
            test_name: Name of the test
            status: Test result status ("passed", "failed", "skipped")
            **kwargs: Additional metrics (db_connections_used, memory_peak_mb, etc.)

        Returns:
            TestMetrics object with recorded data
        """
        start_time = self._start_times.pop(test_name, time.time())
        duration = time.time() - start_time

        metrics = TestMetrics(
            test_name=test_name, duration=duration, status=status, **kwargs
        )

        self.metrics.append(metrics)

        # Log slow tests
        if duration > self.slow_test_threshold:
            logger.warning(
                f"Slow test detected: {test_name} took {duration:.2f}s "
                f"(threshold: {self.slow_test_threshold}s)"
            )

        return metrics

    def get_slow_tests(self, threshold: Optional[float] = None) -> List[TestMetrics]:
        """Get all tests that exceeded the slow test threshold."""
        if threshold is None:
            threshold = self.slow_test_threshold

        return [m for m in self.metrics if m.duration > threshold]

    def get_summary(self) -> Dict:
        """Get a summary of test performance metrics."""
        if not self.metrics:
            return {"total_tests": 0}

        durations = [m.duration for m in self.metrics]
        slow_tests = self.get_slow_tests()

        return {
            "total_tests": len(self.metrics),
            "total_duration": sum(durations),
            "average_duration": sum(durations) / len(durations),
            "median_duration": sorted(durations)[len(durations) // 2],
            "max_duration": max(durations),
            "min_duration": min(durations),
            "slow_tests_count": len(slow_tests),
            "slow_tests": [
                {"name": t.test_name, "duration": t.duration} for t in slow_tests
            ],
            "status_breakdown": {
                "passed": len([m for m in self.metrics if m.status == "passed"]),
                "failed": len([m for m in self.metrics if m.status == "failed"]),
                "skipped": len([m for m in self.metrics if m.status == "skipped"]),
            },
        }

    def save_metrics(self, filepath: str) -> None:
        """Save metrics to a JSON file."""
        data = {
            "summary": self.get_summary(),
            "metrics": [
                {
                    "test_name": m.test_name,
                    "duration": m.duration,
                    "status": m.status,
                    "db_connections_used": m.db_connections_used,
                    "memory_peak_mb": m.memory_peak_mb,
                    "timestamp": m.timestamp,
                }
                for m in self.metrics
            ],
        }

        Path(filepath).parent.mkdir(parents=True, exist_ok=True)
        with open(filepath, "w") as f:
            json.dump(data, f, indent=2)

        logger.info(f"Test metrics saved to {filepath}")


# Global performance monitor instance
performance_monitor = TestPerformanceMonitor()


class DatabaseConnectionTracker:
    """Track database connection usage during tests."""

    def __init__(self):
        self.connections_created = 0
        self.connections_closed = 0
        self.peak_connections = 0
        self.current_connections = 0

    def connection_created(self) -> None:
        """Record a database connection creation."""
        self.connections_created += 1
        self.current_connections += 1
        self.peak_connections = max(self.peak_connections, self.current_connections)

    def connection_closed(self) -> None:
        """Record a database connection closure."""
        self.connections_closed += 1
        self.current_connections = max(0, self.current_connections - 1)

    def get_stats(self) -> Dict:
        """Get connection usage statistics."""
        return {
            "connections_created": self.connections_created,
            "connections_closed": self.connections_closed,
            "peak_connections": self.peak_connections,
            "current_connections": self.current_connections,
            "leaked_connections": self.connections_created - self.connections_closed,
        }

    def reset(self) -> None:
        """Reset all connection statistics."""
        self.connections_created = 0
        self.connections_closed = 0
        self.peak_connections = 0
        self.current_connections = 0


# Global connection tracker instance
connection_tracker = DatabaseConnectionTracker()


def log_test_performance_summary() -> None:
    """Log a summary of test performance at the end of a test session."""
    summary = performance_monitor.get_summary()

    if summary["total_tests"] == 0:
        logger.info("No test metrics collected")
        return

    logger.info(
        f"Test Performance Summary:\n"
        f"  Total tests: {summary['total_tests']}\n"
        f"  Total duration: {summary['total_duration']:.2f}s\n"
        f"  Average duration: {summary['average_duration']:.2f}s\n"
        f"  Median duration: {summary['median_duration']:.2f}s\n"
        f"  Slow tests: {summary['slow_tests_count']}\n"
        f"  Status: {summary['status_breakdown']['passed']} passed, "
        f"{summary['status_breakdown']['failed']} failed, "
        f"{summary['status_breakdown']['skipped']} skipped"
    )

    # Log slow tests
    if summary["slow_tests"]:
        logger.warning("Slow tests detected:")
        for test in summary["slow_tests"]:
            logger.warning(f"  {test['name']}: {test['duration']:.2f}s")

    # Log connection stats
    conn_stats = connection_tracker.get_stats()
    if conn_stats["leaked_connections"] > 0:
        logger.warning(
            f"Database connection leaks detected: "
            f"{conn_stats['leaked_connections']} connections not properly closed"
        )
