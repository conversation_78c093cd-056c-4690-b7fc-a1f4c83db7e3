"""
Test authentication utilities for GraphQL and API integration tests.
"""

from a2a_platform.db.models.user import User


def get_test_auth_headers(test_user: User) -> dict:
    """
    Create authentication headers for testing GraphQL endpoints.

    This function creates special test headers that are recognized
    by the auth_middleware's get_current_user_id function to allow
    bypassing actual Clerk authentication during tests.

    Args:
        test_user: The user to create headers for

    Returns:
        Dictionary of headers to include in requests
    """
    return {
        "Content-Type": "application/json",
        "Authorization": f"Bearer test.{test_user.clerk_user_id}.valid",
        "X-Test-Auth": "true",
        "X-Test-Clerk-User-Id": test_user.clerk_user_id,
    }
