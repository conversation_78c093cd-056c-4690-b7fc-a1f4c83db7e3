"""
Unit tests for RQ client.
"""

from unittest.mock import AsyncMock, MagicMock, patch

import pytest
import redis
from rq import Queue
from rq.job import Job

from a2a_platform.messaging.rq_client import RQClient


@pytest.mark.no_db
class TestRQClient:
    @pytest.fixture
    def mock_redis_conn(self):
        """Mock Redis connection."""
        with patch("redis.from_url") as mock_redis:
            mock_conn = MagicMock()
            mock_redis.return_value = mock_conn
            yield mock_conn

    @pytest.fixture
    def mock_queue(self):
        """Mock RQ Queue."""
        with patch("a2a_platform.messaging.rq_client.Queue") as mock_queue_class:
            mock_queue = MagicMock(spec=Queue)
            mock_queue_class.return_value = mock_queue
            yield mock_queue

    @pytest.fixture
    def mock_job(self):
        """Mock RQ Job."""
        mock_job = MagicMock(spec=Job)
        mock_job.id = "test-job-id"
        return mock_job

    @pytest.mark.asyncio
    async def test_init(self, mock_redis_conn):
        """Test initializing the RQ client."""
        client = RQClient(redis_url="redis://localhost:6379/0")
        assert client.redis_url == "redis://localhost:6379/0"
        assert client.default_job_timeout == 180
        assert client.default_ttl == 86400
        assert client._redis_conn is not None

    @pytest.mark.asyncio
    async def test_init_with_custom_values(self, mock_redis_conn):
        """Test initializing the RQ client with custom values."""
        client = RQClient(
            redis_url="redis://localhost:6379/0",
            default_job_timeout=300,
            default_ttl=43200,
        )
        assert client.redis_url == "redis://localhost:6379/0"
        assert client.default_job_timeout == 300
        assert client.default_ttl == 43200

    @pytest.mark.asyncio
    async def test_init_redis_error(self):
        """Test initializing the RQ client when Redis connection fails."""
        with patch("redis.from_url", side_effect=redis.RedisError("Connection error")):
            with pytest.raises(redis.RedisError):
                RQClient(redis_url="redis://localhost:6379/0")

    @pytest.mark.asyncio
    async def test_get_queue(self, mock_redis_conn, mock_queue):
        """Test getting a queue."""
        client = RQClient(redis_url="redis://localhost:6379/0")
        queue = client._get_queue("test-queue")
        assert queue == mock_queue
        assert "test-queue" in client._queues

    @pytest.mark.asyncio
    async def test_send_message(self, mock_redis_conn, mock_queue, mock_job):
        """Test sending a message."""
        # Setup
        mock_queue.enqueue.return_value = mock_job

        # Create client and send message
        client = RQClient(redis_url="redis://localhost:6379/0")
        message_id = await client.send_message(
            queue_name="test-queue",
            message_body="test message",
            message_attributes={"attr1": "value1"},
        )

        # Verify
        assert message_id == "test-job-id"
        mock_queue.enqueue.assert_called_once()
        args, kwargs = mock_queue.enqueue.call_args
        assert args[0] == "a2a_platform.workers.rq_worker.process_message"
        assert "body" in args[1]
        assert args[1]["body"] == "test message"
        assert args[1]["attributes"] == {"attr1": "value1"}
        assert "message_id" in args[1]
        assert kwargs["job_id"] == args[1]["message_id"]
        assert kwargs["result_ttl"] == 86400

    @pytest.mark.asyncio
    async def test_send_message_no_redis(self):
        """Test sending a message when Redis is not initialized."""
        client = RQClient(redis_url="redis://localhost:6379/0")
        client._redis_conn = None
        with pytest.raises(RuntimeError):
            await client.send_message(
                queue_name="test-queue",
                message_body="test message",
            )

    @pytest.mark.asyncio
    async def test_consume_messages(self, mock_redis_conn, mock_queue):
        """Test consuming messages."""
        # Setup
        handler = AsyncMock()

        # Create client and start consumer
        client = RQClient(redis_url="redis://localhost:6379/0")

        # Mock the _poll_queue method to avoid actually starting a consumer
        with patch.object(client, "_poll_queue", AsyncMock()) as mock_poll:
            await client.consume_messages(queue_name="test-queue", handler=handler)

            # Verify
            mock_poll.assert_called_once_with("test-queue", handler)
            assert len(client._running_consumers) == 1

    @pytest.mark.asyncio
    async def test_consume_messages_no_redis(self):
        """Test consuming messages when Redis is not initialized."""
        client = RQClient(redis_url="redis://localhost:6379/0")
        client._redis_conn = None
        with pytest.raises(RuntimeError):
            await client.consume_messages(
                queue_name="test-queue",
                handler=AsyncMock(),
            )

    @pytest.mark.asyncio
    async def test_poll_queue(self, mock_redis_conn, mock_queue):
        """Test polling the queue for jobs."""
        # Setup
        handler = AsyncMock(return_value=True)
        mock_job = MagicMock()
        mock_job.is_queued = True
        mock_job.args = {
            "message_id": "test-job-id",
            "body": "test message",
            "attributes": {},
        }
        mock_queue.fetch_job.return_value = mock_job

        # Create client
        client = RQClient(redis_url="redis://localhost:6379/0")
        client._queues["test-queue"] = mock_queue

        # Mock asyncio.sleep to avoid waiting
        with patch("asyncio.sleep", AsyncMock()) as mock_sleep:
            # Call _poll_queue
            await client._poll_queue("test-queue", handler)

            # Verify
            handler.assert_called_once()
            mock_job.set_status.assert_called_once_with("finished")
            mock_sleep.assert_called_once()

    @pytest.mark.asyncio
    async def test_poll_queue_handler_failure(self, mock_redis_conn, mock_queue):
        """Test polling the queue when handler fails."""
        # Setup
        handler = AsyncMock(return_value=False)
        mock_job = MagicMock()
        mock_job.is_queued = True
        mock_job.args = {
            "message_id": "test-job-id",
            "body": "test message",
            "attributes": {},
        }
        mock_queue.fetch_job.return_value = mock_job

        # Create client
        client = RQClient(redis_url="redis://localhost:6379/0")
        client._queues["test-queue"] = mock_queue

        # Mock asyncio.sleep to avoid waiting
        with patch("asyncio.sleep", AsyncMock()) as mock_sleep:
            # Call _poll_queue
            await client._poll_queue("test-queue", handler)

            # Verify
            handler.assert_called_once()
            mock_job.set_status.assert_called_once_with("failed")
            mock_sleep.assert_called_once()
