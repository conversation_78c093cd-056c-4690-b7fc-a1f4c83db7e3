"""
Unit tests for A2A message producer.
"""

import json
from unittest.mock import AsyncMock, MagicMock

import pytest

from a2a_platform.messaging.a2a_producer import A2AMessageProducer
from a2a_platform.messaging.queue_client import QueueClient


class TestA2AMessageProducer:
    @pytest.mark.asyncio
    async def test_send_message(self):
        """Test sending a message with user context."""
        # Setup mock queue client
        mock_queue_client = MagicMock(spec=QueueClient)
        mock_queue_client.send_message = AsyncMock(return_value="message123")

        # Create producer and call method
        producer = A2AMessageProducer(queue_client=mock_queue_client)
        message_id = await producer.send_message(
            queue_name="test-queue",
            payload={"action": "test"},
            user_id="user123",
            initiating_agent_id="agent456",
        )

        # Verify the result
        assert message_id == "message123"

        # Verify the message
        mock_queue_client.send_message.assert_called_once()
        args, kwargs = mock_queue_client.send_message.call_args
        assert kwargs["queue_name"] == "test-queue"
        assert "message_body" in kwargs
        assert "message_attributes" in kwargs

        # Parse the message body to verify its contents
        message_body = json.loads(kwargs["message_body"])
        assert message_body["user_context"]["user_id"] == "user123"
        assert message_body["user_context"]["initiating_agent_id"] == "agent456"
        assert message_body["payload"]["action"] == "test"

    @pytest.mark.asyncio
    async def test_send_message_with_attributes(self):
        """Test sending a message with custom attributes."""
        # Setup mock queue client
        mock_queue_client = MagicMock(spec=QueueClient)
        mock_queue_client.send_message = AsyncMock(return_value="message123")

        # Create producer and call method with custom attributes
        producer = A2AMessageProducer(queue_client=mock_queue_client)
        message_id = await producer.send_message(
            queue_name="test-queue",
            payload={"action": "test"},
            user_id="user123",
            initiating_agent_id="agent456",
            message_attributes={"custom_attr": "value"},
        )

        # Verify the result
        assert message_id == "message123"

        # Verify the message attributes
        mock_queue_client.send_message.assert_called_once()
        args, kwargs = mock_queue_client.send_message.call_args
        assert kwargs["message_attributes"]["custom_attr"] == "value"
        assert kwargs["message_attributes"]["user_id"] == "user123"
        assert kwargs["message_attributes"]["initiating_agent_id"] == "agent456"
        assert "timestamp" in kwargs["message_attributes"]

    @pytest.mark.asyncio
    async def test_send_message_error(self):
        """Test sending a message when an error occurs."""
        # Setup mock queue client to raise an error
        mock_queue_client = MagicMock(spec=QueueClient)
        mock_queue_client.send_message = AsyncMock(side_effect=Exception("Error"))

        # Create producer and call method
        producer = A2AMessageProducer(queue_client=mock_queue_client)

        # Verify that the error is propagated
        with pytest.raises(Exception):
            await producer.send_message(
                queue_name="test-queue",
                payload={"action": "test"},
                user_id="user123",
                initiating_agent_id="agent456",
            )
