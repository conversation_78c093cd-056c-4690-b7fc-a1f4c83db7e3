"""
Unit tests for queue factory.
"""

from unittest.mock import MagicMock, patch

import pytest

from a2a_platform.messaging.queue_client import PubSubClient, QueueClient
from a2a_platform.messaging.queue_factory import create_queue_client


@pytest.mark.no_db
class TestQueueFactory:
    @pytest.mark.asyncio
    @patch("a2a_platform.messaging.queue_factory.PubSubClient")
    async def test_create_pubsub_client(self, mock_pubsub_client):
        """Test creating a PubSub client."""
        # Setup
        mock_client = MagicMock(spec=PubSubClient)
        mock_pubsub_client.return_value = mock_client

        # Create client
        client = await create_queue_client(
            queue_type="pubsub", project_id="test-project"
        )

        # Verify
        assert client == mock_client
        mock_pubsub_client.assert_called_once_with(project_id="test-project")

    @pytest.mark.asyncio
    @patch("a2a_platform.messaging.rq_client.RQClient")
    async def test_create_rq_client(self, mock_rq_client):
        """Test creating an RQ client."""
        # Setup
        mock_client = MagicMock(spec=QueueClient)
        mock_rq_client.return_value = mock_client

        # Create client
        client = await create_queue_client(
            queue_type="rq", redis_url="redis://localhost:6379/0"
        )

        # Verify
        assert client == mock_client
        mock_rq_client.assert_called_once()
        args, kwargs = mock_rq_client.call_args
        assert kwargs["redis_url"] == "redis://localhost:6379/0"

    @pytest.mark.asyncio
    async def test_create_invalid_client(self):
        """Test creating an invalid client type."""
        with pytest.raises(ValueError):
            await create_queue_client(queue_type="invalid")  # type: ignore
