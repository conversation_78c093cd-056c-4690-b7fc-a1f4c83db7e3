"""
Unit tests for SubscriptionManager.

These tests verify that the SubscriptionManager correctly handles message
publishing, subscription management, and error scenarios including JSON
serialization errors.
"""

import json
import uuid
from datetime import UTC, datetime
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
import redis.asyncio as redis

from a2a_platform.messaging.subscription_manager import SubscriptionManager


class TestSubscriptionManagerPublishMessage:
    """Tests for SubscriptionManager.publish_message method."""

    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test fixtures."""
        # Create a subscription manager instance with mocked Redis
        self.subscription_manager = SubscriptionManager(
            redis_url="redis://localhost:6379/1"
        )

        # Mock Redis client
        self.mock_redis_client = AsyncMock(spec=redis.Redis)
        self.subscription_manager._redis_client = self.mock_redis_client

        # Mock database message model
        self.mock_db_message = MagicMock()
        self.mock_db_message.id = uuid.uuid4()
        self.mock_db_message.conversation_id = uuid.uuid4()
        self.mock_db_message.sender_role = "user"
        self.mock_db_message.content = {
            "parts": [{"type": "text", "content": "Hello world"}]
        }
        self.mock_db_message.timestamp = datetime.now(UTC)
        self.mock_db_message.message_metadata = {"edited": False}

        # Test conversation ID
        self.conversation_id = uuid.uuid4()

    @pytest.mark.asyncio
    @pytest.mark.no_db
    async def test_publish_message_success(self):
        """Test successful message publishing."""
        # Mock ChatMessage.from_db_model
        with patch(
            "a2a_platform.messaging.subscription_manager.ChatMessage"
        ) as MockChatMessage:
            mock_message_gql = MagicMock()
            mock_message_gql.id = self.mock_db_message.id
            mock_message_gql.conversation_id = self.mock_db_message.conversation_id
            mock_message_gql.sender_role = self.mock_db_message.sender_role
            mock_message_gql._sender_role = self.mock_db_message.sender_role
            mock_message_gql.content = {"text": "Hello world"}
            mock_message_gql.timestamp = self.mock_db_message.timestamp
            mock_message_gql.metadata = self.mock_db_message.message_metadata

            MockChatMessage.from_db_model.return_value = mock_message_gql

            # Act
            await self.subscription_manager.publish_message(
                conversation_id=self.conversation_id,
                message=self.mock_db_message,
                event_type="new_message",
            )

            # Assert
            MockChatMessage.from_db_model.assert_called_once_with(self.mock_db_message)
            self.mock_redis_client.publish.assert_called_once()

            # Verify the published data structure
            call_args = self.mock_redis_client.publish.call_args
            channel_name, payload_json = call_args[0]

            assert channel_name == f"chat:conversation:{self.conversation_id}"

            payload_data = json.loads(payload_json)
            assert payload_data["conversationId"] == str(self.conversation_id)
            assert payload_data["eventType"] == "new_message"
            assert payload_data["message"]["id"] == str(mock_message_gql.id)
            assert payload_data["message"]["senderRole"] == mock_message_gql.sender_role

    @pytest.mark.asyncio
    @pytest.mark.no_db
    async def test_publish_message_no_redis_client(self):
        """Test publishing when Redis client is not initialized."""
        # Arrange
        self.subscription_manager._redis_client = None

        with patch("a2a_platform.messaging.subscription_manager.logger") as mock_logger:
            # Act
            await self.subscription_manager.publish_message(
                conversation_id=self.conversation_id, message=self.mock_db_message
            )

            # Assert
            mock_logger.warning.assert_called_once_with(
                "Redis client not initialized, cannot publish message"
            )

    @pytest.mark.asyncio
    @pytest.mark.no_db
    async def test_publish_message_json_serialization_error(self):
        """Test handling of JSON serialization errors (the specific error mentioned)."""
        # Mock ChatMessage.from_db_model to return an object with non-serializable content
        with patch(
            "a2a_platform.messaging.subscription_manager.ChatMessage"
        ) as MockChatMessage:
            mock_message_gql = MagicMock()
            mock_message_gql.id = self.mock_db_message.id
            mock_message_gql.conversation_id = self.mock_db_message.conversation_id
            mock_message_gql.sender_role = self.mock_db_message.sender_role
            mock_message_gql._sender_role = self.mock_db_message.sender_role
            mock_message_gql.timestamp = self.mock_db_message.timestamp

            # Make content property return a valid value
            mock_message_gql.content = {"text": "Hello world"}

            # Set metadata with a non-serializable method to cause JSON error
            mock_message_gql.metadata = {
                "method": lambda x: x  # Non-serializable method
            }

            MockChatMessage.from_db_model.return_value = mock_message_gql

            # Mock json.dumps to raise TypeError for metadata test
            with patch(
                "a2a_platform.messaging.subscription_manager.json.dumps"
            ) as mock_json_dumps:
                mock_json_dumps.side_effect = [
                    None,  # First call for metadata test passes
                    TypeError(
                        "Object of type function is not JSON serializable"
                    ),  # Second call for final payload fails
                ]

                with patch(
                    "a2a_platform.messaging.subscription_manager.logger"
                ) as mock_logger:
                    # Act
                    await self.subscription_manager.publish_message(
                        conversation_id=self.conversation_id,
                        message=self.mock_db_message,
                    )

                    # Assert
                    mock_logger.error.assert_called_once()
                    error_call = mock_logger.error.call_args[0][0]
                    assert "Failed to publish message:" in error_call
                    assert "not JSON serializable" in error_call

                # Verify Redis publish was not called
                self.mock_redis_client.publish.assert_not_called()

    @pytest.mark.asyncio
    @pytest.mark.no_db
    async def test_publish_message_from_db_model_error(self):
        """Test handling of errors in ChatMessage.from_db_model conversion."""
        # Mock ChatMessage.from_db_model to raise an exception
        with patch(
            "a2a_platform.messaging.subscription_manager.ChatMessage"
        ) as MockChatMessage:
            MockChatMessage.from_db_model.side_effect = AttributeError(
                "Missing required attribute"
            )

            with patch(
                "a2a_platform.messaging.subscription_manager.logger"
            ) as mock_logger:
                # Act
                await self.subscription_manager.publish_message(
                    conversation_id=self.conversation_id, message=self.mock_db_message
                )

                # Assert
                mock_logger.error.assert_called_once()
                error_call = mock_logger.error.call_args[0][0]
                assert "Failed to publish message:" in error_call
                assert "Missing required attribute" in error_call

                # Verify Redis publish was not called
                self.mock_redis_client.publish.assert_not_called()

    @pytest.mark.asyncio
    @pytest.mark.no_db
    async def test_publish_message_redis_publish_error(self):
        """Test handling of Redis publish errors."""
        # Mock ChatMessage.from_db_model
        with patch(
            "a2a_platform.messaging.subscription_manager.ChatMessage"
        ) as MockChatMessage:
            mock_message_gql = MagicMock()
            mock_message_gql.id = self.mock_db_message.id
            mock_message_gql.conversation_id = self.mock_db_message.conversation_id
            mock_message_gql.sender_role = self.mock_db_message.sender_role
            mock_message_gql._sender_role = self.mock_db_message.sender_role
            mock_message_gql.content = {"text": "Hello world"}
            mock_message_gql.timestamp = self.mock_db_message.timestamp
            mock_message_gql.metadata = {}

            MockChatMessage.from_db_model.return_value = mock_message_gql

            # Mock Redis publish to raise an exception
            self.mock_redis_client.publish.side_effect = redis.ConnectionError(
                "Redis connection failed"
            )

            with patch(
                "a2a_platform.messaging.subscription_manager.logger"
            ) as mock_logger:
                # Act
                await self.subscription_manager.publish_message(
                    conversation_id=self.conversation_id, message=self.mock_db_message
                )

                # Assert
                mock_logger.error.assert_called_once()
                error_call = mock_logger.error.call_args[0][0]
                assert "Failed to publish message:" in error_call
                assert "Redis connection failed" in error_call

    @pytest.mark.asyncio
    @pytest.mark.no_db
    async def test_publish_message_with_custom_event_type(self):
        """Test publishing with custom event type."""
        # Mock ChatMessage.from_db_model
        with patch(
            "a2a_platform.messaging.subscription_manager.ChatMessage"
        ) as MockChatMessage:
            mock_message_gql = MagicMock()
            mock_message_gql.id = self.mock_db_message.id
            mock_message_gql.conversation_id = self.mock_db_message.conversation_id
            mock_message_gql.sender_role = self.mock_db_message.sender_role
            mock_message_gql._sender_role = self.mock_db_message.sender_role
            mock_message_gql.content = {"text": "Hello world"}
            mock_message_gql.timestamp = self.mock_db_message.timestamp
            mock_message_gql.metadata = {}

            MockChatMessage.from_db_model.return_value = mock_message_gql

            # Act
            await self.subscription_manager.publish_message(
                conversation_id=self.conversation_id,
                message=self.mock_db_message,
                event_type="message_edited",
            )

            # Assert
            call_args = self.mock_redis_client.publish.call_args
            _, payload_json = call_args[0]

            payload_data = json.loads(payload_json)
            assert payload_data["eventType"] == "message_edited"

    @pytest.mark.asyncio
    @pytest.mark.no_db
    async def test_publish_message_with_complex_metadata(self):
        """Test publishing with complex metadata that serializes correctly."""
        # Mock database message with complex metadata
        complex_metadata = {
            "edited": True,
            "edit_count": 3,
            "mentions": ["@user1", "@user2"],
            "attachments": [
                {"type": "image", "url": "https://example.com/image.jpg"},
                {"type": "file", "name": "document.pdf", "size": 1024},
            ],
            "reactions": {"thumbs_up": ["user1", "user2"], "heart": ["user3"]},
        }

        # Mock ChatMessage.from_db_model
        with patch(
            "a2a_platform.messaging.subscription_manager.ChatMessage"
        ) as MockChatMessage:
            mock_message_gql = MagicMock()
            mock_message_gql.id = self.mock_db_message.id
            mock_message_gql.conversation_id = self.mock_db_message.conversation_id
            mock_message_gql.sender_role = self.mock_db_message.sender_role
            mock_message_gql._sender_role = self.mock_db_message.sender_role
            mock_message_gql.content = {"text": "Hello world"}
            mock_message_gql.timestamp = self.mock_db_message.timestamp
            mock_message_gql.metadata = complex_metadata

            MockChatMessage.from_db_model.return_value = mock_message_gql

            # Act
            await self.subscription_manager.publish_message(
                conversation_id=self.conversation_id, message=self.mock_db_message
            )

            # Assert
            call_args = self.mock_redis_client.publish.call_args
            _, payload_json = call_args[0]

            payload_data = json.loads(payload_json)
            assert payload_data["message"]["metadata"] == complex_metadata

    @pytest.mark.asyncio
    @pytest.mark.no_db
    async def test_publish_message_timestamp_serialization(self):
        """Test that datetime timestamps are serialized to ISO format."""
        test_timestamp = datetime(2024, 1, 15, 14, 30, 45, 123456, tzinfo=UTC)

        # Mock ChatMessage.from_db_model
        with patch(
            "a2a_platform.messaging.subscription_manager.ChatMessage"
        ) as MockChatMessage:
            mock_message_gql = MagicMock()
            mock_message_gql.id = self.mock_db_message.id
            mock_message_gql.conversation_id = self.mock_db_message.conversation_id
            mock_message_gql.sender_role = self.mock_db_message.sender_role
            mock_message_gql._sender_role = self.mock_db_message.sender_role
            mock_message_gql.content = {"text": "Hello world"}
            mock_message_gql.timestamp = test_timestamp
            mock_message_gql.metadata = {}

            MockChatMessage.from_db_model.return_value = mock_message_gql

            # Act
            await self.subscription_manager.publish_message(
                conversation_id=self.conversation_id, message=self.mock_db_message
            )

            # Assert
            call_args = self.mock_redis_client.publish.call_args
            _, payload_json = call_args[0]

            payload_data = json.loads(payload_json)
            expected_timestamp = test_timestamp.isoformat()
            assert payload_data["message"]["timestamp"] == expected_timestamp

    @pytest.mark.asyncio
    @pytest.mark.no_db
    async def test_publish_message_channel_name_generation(self):
        """Test that the correct channel name is generated."""
        # Mock ChatMessage.from_db_model
        with patch(
            "a2a_platform.messaging.subscription_manager.ChatMessage"
        ) as MockChatMessage:
            mock_message_gql = MagicMock()
            mock_message_gql.id = self.mock_db_message.id
            mock_message_gql.conversation_id = self.mock_db_message.conversation_id
            mock_message_gql.sender_role = self.mock_db_message.sender_role
            mock_message_gql._sender_role = self.mock_db_message.sender_role
            mock_message_gql.content = {"text": "Hello world"}
            mock_message_gql.timestamp = self.mock_db_message.timestamp
            mock_message_gql.metadata = {}

            MockChatMessage.from_db_model.return_value = mock_message_gql

            # Act
            await self.subscription_manager.publish_message(
                conversation_id=self.conversation_id, message=self.mock_db_message
            )

            # Assert
            call_args = self.mock_redis_client.publish.call_args
            channel_name, _ = call_args[0]

            expected_channel = f"chat:conversation:{self.conversation_id}"
            assert channel_name == expected_channel

    @pytest.mark.asyncio
    @pytest.mark.no_db
    async def test_publish_message_method_object_serialization_error(self):
        """Test the specific 'Object of type method is not JSON serializable' error."""
        # Mock ChatMessage.from_db_model to return an object with a method
        with patch(
            "a2a_platform.messaging.subscription_manager.ChatMessage"
        ) as MockChatMessage:
            mock_message_gql = MagicMock()
            mock_message_gql.id = self.mock_db_message.id
            mock_message_gql.conversation_id = self.mock_db_message.conversation_id
            mock_message_gql.sender_role = self.mock_db_message.sender_role
            mock_message_gql._sender_role = self.mock_db_message.sender_role
            mock_message_gql.timestamp = self.mock_db_message.timestamp

            # Create a method that would cause JSON serialization to fail
            def non_serializable_method():
                return "This is a method"

            # Set up the content property to return a valid value
            mock_message_gql.content = {"text": "Hello world"}

            # Set metadata with a non-serializable method to cause JSON error
            mock_message_gql.metadata = {
                "method": non_serializable_method,  # This will cause JSON error
            }

            MockChatMessage.from_db_model.return_value = mock_message_gql

            # Ensure Redis client publish method is properly mocked as async
            self.mock_redis_client.publish = AsyncMock()

            # Mock json.dumps to properly simulate the metadata serialization test and final payload success
            with patch(
                "a2a_platform.messaging.subscription_manager.json.dumps"
            ) as mock_json_dumps:
                # First call (metadata test) should fail with the function error
                # Second call (final payload) should succeed
                mock_json_dumps.side_effect = [
                    TypeError(
                        "Object of type function is not JSON serializable"
                    ),  # Metadata test fails
                    '{"message": {"id": "test"}, "conversationId": "test", "eventType": "new_message"}',  # Final payload succeeds
                ]

                with patch(
                    "a2a_platform.messaging.subscription_manager.logger"
                ) as mock_logger:
                    # Act
                    await self.subscription_manager.publish_message(
                        conversation_id=self.conversation_id,
                        message=self.mock_db_message,
                    )

                    # Assert - should log warning about metadata, not error
                    mock_logger.warning.assert_called_once()
                    warning_call = mock_logger.warning.call_args[0][0]
                    assert "Metadata not JSON serializable:" in warning_call

                    # Should still publish with empty metadata
                    self.mock_redis_client.publish.assert_called_once()

    @pytest.mark.asyncio
    @pytest.mark.no_db
    async def test_publish_message_string_timestamp_handling(self):
        """Test that string timestamps are handled correctly without calling isoformat()."""
        # Mock ChatMessage.from_db_model
        with patch(
            "a2a_platform.messaging.subscription_manager.ChatMessage"
        ) as MockChatMessage:
            mock_message_gql = MagicMock()
            mock_message_gql.id = self.mock_db_message.id
            mock_message_gql.conversation_id = self.mock_db_message.conversation_id
            mock_message_gql.sender_role = self.mock_db_message.sender_role
            mock_message_gql._sender_role = self.mock_db_message.sender_role

            # Set up the content property to return a valid value
            mock_message_gql.content = {"text": "Hello world"}

            # Set timestamp as a string (simulating the issue scenario)
            mock_message_gql.timestamp = "2023-01-01T12:00:00+00:00"
            mock_message_gql.metadata = {}

            MockChatMessage.from_db_model.return_value = mock_message_gql

            # Act
            await self.subscription_manager.publish_message(
                conversation_id=self.conversation_id, message=self.mock_db_message
            )

            # Assert
            call_args = self.mock_redis_client.publish.call_args
            _, payload_json = call_args[0]

            payload_data = json.loads(payload_json)
            # Should use the string timestamp as-is, not call isoformat()
            assert payload_data["message"]["timestamp"] == "2023-01-01T12:00:00+00:00"
