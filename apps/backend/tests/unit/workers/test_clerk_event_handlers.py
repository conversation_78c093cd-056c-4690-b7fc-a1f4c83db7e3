from unittest.mock import AsyncMock

import pytest

from a2a_platform.workers import clerk_event_handlers


@pytest.mark.no_db
@pytest.mark.asyncio
async def test_process_user_deleted_task_idempotent(monkeypatch):
    # Simulate event already processed
    monkeypatch.setattr(
        clerk_event_handlers, "check_idempotency", AsyncMock(return_value=True)
    )
    logger = clerk_event_handlers.logger
    monkeypatch.setattr(logger, "info", lambda msg, *a, **k: None)
    result = await clerk_event_handlers.process_user_deleted_task_func(
        None, "evt1", {"id": "clerkid"}
    )
    assert result is True


@pytest.mark.no_db
@pytest.mark.asyncio
async def test_process_user_deleted_task_success(monkeypatch):
    monkeypatch.setattr(
        clerk_event_handlers, "check_idempotency", AsyncMock(return_value=False)
    )
    monkeypatch.setattr(clerk_event_handlers, "mark_as_processed", AsyncMock())
    mock_delete = AsyncMock(return_value=True)
    monkeypatch.setattr(clerk_event_handlers, "delete_all_user_data", mock_delete)

    # Patch get_session_factory to return a mock session factory
    class DummyAsyncContext:
        async def __aenter__(self):
            return AsyncMock()

        async def __aexit__(self, exc_type, exc, tb):
            pass

    monkeypatch.setattr(
        clerk_event_handlers, "get_session_factory", lambda: lambda: DummyAsyncContext()
    )
    logger = clerk_event_handlers.logger
    monkeypatch.setattr(logger, "info", lambda msg, *a, **k: None)
    data = {"id": "clerkid"}
    result = await clerk_event_handlers.process_user_deleted_task_func(
        None, "evt1", data
    )
    assert result is True
    mock_delete.assert_awaited()


@pytest.mark.no_db
@pytest.mark.asyncio
async def test_process_user_deleted_task_validation_error(monkeypatch):
    monkeypatch.setattr(
        clerk_event_handlers, "check_idempotency", AsyncMock(return_value=False)
    )
    monkeypatch.setattr(clerk_event_handlers, "mark_as_processed", AsyncMock())
    logger = clerk_event_handlers.logger
    monkeypatch.setattr(logger, "error", lambda msg, *a, **k: None)
    # Missing required 'id' field
    data = {"bad": "data"}
    result = await clerk_event_handlers.process_user_deleted_task_func(
        None, "evt1", data
    )
    assert result is False


@pytest.mark.no_db
@pytest.mark.asyncio
async def test_process_user_deleted_task_generic_error(monkeypatch):
    monkeypatch.setattr(
        clerk_event_handlers, "check_idempotency", AsyncMock(return_value=False)
    )
    monkeypatch.setattr(clerk_event_handlers, "mark_as_processed", AsyncMock())
    logger = clerk_event_handlers.logger
    monkeypatch.setattr(logger, "error", lambda msg, *a, **k: None)
    # Patch delete_all_user_data to raise Exception
    monkeypatch.setattr(
        clerk_event_handlers,
        "delete_all_user_data",
        AsyncMock(side_effect=Exception("fail")),
    )

    # Patch get_session_factory to return a mock session factory
    class DummyAsyncContext:
        async def __aenter__(self):
            return AsyncMock()

        async def __aexit__(self, exc_type, exc, tb):
            pass

    monkeypatch.setattr(
        clerk_event_handlers, "get_session_factory", lambda: lambda: DummyAsyncContext()
    )
    with pytest.raises(clerk_event_handlers.ProcessingError):
        await clerk_event_handlers.process_user_deleted_task_func(
            None, "evt1", {"id": "clerkid"}
        )
