"""
Unit tests for ChatMessage model.

Tests the SQLAlchemy ChatMessage model functionality including:
- Model creation and validation
- JSONB content handling
- Sender role constraints
- Relationships to Conversation model
- Timestamp handling
"""

import uuid
from datetime import datetime, UTC

import pytest
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.chat_message import ChatMessage
from a2a_platform.db.models.conversation import Conversation
from a2a_platform.db.models.user import User


class TestChatMessageModel:
    """Unit tests for ChatMessage SQLAlchemy model."""

    @pytest.mark.asyncio
    async def test_create_chat_message_with_valid_data(self):
        """Test creating a ChatMessage with valid data."""
        # Arrange
        conversation_id = uuid.uuid4()
        content = {"text": "Hello, this is a test message"}
        sender_role = "user"
        metadata = {"source": "test"}
        message_id = uuid.uuid4()
        timestamp = datetime.now(UTC)

        # Act
        message = ChatMessage(
            id=message_id,
            conversation_id=conversation_id,
            sender_role=sender_role,
            content=content,
            message_metadata=metadata,
            timestamp=timestamp,
        )

        # Assert
        assert message.conversation_id == conversation_id
        assert message.sender_role == sender_role
        assert message.content == content
        assert message.message_metadata == metadata
        assert message.id == message_id
        assert message.timestamp == timestamp
        assert message.timestamp.tzinfo == UTC

    @pytest.mark.asyncio
    async def test_chat_message_jsonb_content_serialization(self):
        """Test that JSONB content is properly serialized and deserialized."""
        # Arrange
        conversation_id = uuid.uuid4()
        complex_content = {
            "text": "Hello world",
            "attachments": [
                {"type": "image", "url": "https://example.com/image.jpg"},
                {"type": "file", "url": "https://example.com/document.pdf"},
            ],
            "metadata": {
                "edited": True,
                "edit_count": 2,
                "mentions": ["@user1", "@user2"],
            },
        }

        # Act
        message = ChatMessage(
            conversation_id=conversation_id,
            sender_role="user",
            content=complex_content,
        )

        # Assert
        assert message.content == complex_content
        assert message.content["text"] == "Hello world"
        assert len(message.content["attachments"]) == 2
        assert message.content["metadata"]["edit_count"] == 2

    @pytest.mark.asyncio
    async def test_sender_role_constraint_valid_values(self):
        """Test that sender_role accepts valid values."""
        conversation_id = uuid.uuid4()
        content = {"text": "Test message"}

        # Test 'user' role
        user_message = ChatMessage(
            conversation_id=conversation_id,
            sender_role="user",
            content=content,
        )
        assert user_message.sender_role == "user"

        # Test 'agent' role
        agent_message = ChatMessage(
            conversation_id=conversation_id,
            sender_role="agent",
            content=content,
        )
        assert agent_message.sender_role == "agent"

    @pytest.mark.asyncio
    async def test_default_timestamp_is_utc(self):
        """Test that timestamp can be set to UTC."""
        # Arrange
        conversation_id = uuid.uuid4()
        content = {"text": "Test message"}
        timestamp = datetime.now(UTC)

        # Act
        message = ChatMessage(
            conversation_id=conversation_id,
            sender_role="user",
            content=content,
            timestamp=timestamp,
        )

        # Assert
        assert message.timestamp.tzinfo == UTC

    @pytest.mark.asyncio
    async def test_default_metadata_is_empty_dict(self):
        """Test that message_metadata can be set to empty dictionary."""
        # Arrange
        conversation_id = uuid.uuid4()
        content = {"text": "Test message"}

        # Act
        message = ChatMessage(
            conversation_id=conversation_id,
            sender_role="user",
            content=content,
            message_metadata={},
        )

        # Assert
        assert message.message_metadata == {}

    @pytest.mark.asyncio
    async def test_chat_message_repr(self):
        """Test the string representation of ChatMessage."""
        # Arrange
        conversation_id = uuid.uuid4()
        content = {"text": "Test message"}

        # Act
        message = ChatMessage(
            conversation_id=conversation_id,
            sender_role="user",
            content=content,
        )

        # Assert
        repr_str = repr(message)
        assert "ChatMessage" in repr_str
        assert str(message.id) in repr_str
        assert str(conversation_id) in repr_str
        assert "user" in repr_str

    @pytest.mark.asyncio
    async def test_chat_message_required_fields(self):
        """Test that required fields are properly set."""
        conversation_id = uuid.uuid4()

        # Test that all required fields can be set
        message = ChatMessage(
            conversation_id=conversation_id,
            sender_role="user",
            content={"text": "Test"},
        )

        assert message.conversation_id == conversation_id
        assert message.sender_role == "user"
        assert message.content == {"text": "Test"}

        # Test that None values are handled appropriately
        # (SQLAlchemy will handle validation at the database level)

    @pytest.mark.asyncio
    async def test_chat_message_persistence_with_database(
        self,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
    ):
        """Test ChatMessage persistence to and retrieval from database."""
        # Arrange - Create a conversation first
        conversation = Conversation(
            user_id=test_user.id,
            assistant_id=test_assistant_id,
        )
        db_session_real.add(conversation)
        await db_session_real.commit()
        await db_session_real.refresh(conversation)

        content = {"text": "Hello from database test"}
        message = ChatMessage(
            conversation_id=conversation.id,
            sender_role="user",
            content=content,
            message_metadata={"test": True},
        )

        # Act - Persist to database
        db_session_real.add(message)
        await db_session_real.commit()
        await db_session_real.refresh(message)

        # Retrieve from database
        result = await db_session_real.execute(
            select(ChatMessage).where(ChatMessage.id == message.id)
        )
        retrieved_message = result.scalar_one()

        # Assert
        assert retrieved_message.id == message.id
        assert retrieved_message.conversation_id == conversation.id
        assert retrieved_message.sender_role == "user"
        assert retrieved_message.content == content
        assert retrieved_message.message_metadata == {"test": True}
        assert retrieved_message.timestamp is not None

    @pytest.mark.asyncio
    async def test_chat_message_relationship_to_conversation(
        self,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
    ):
        """Test the relationship between ChatMessage and Conversation."""
        # Arrange - Create a conversation
        conversation = Conversation(
            user_id=test_user.id,
            assistant_id=test_assistant_id,
        )
        db_session_real.add(conversation)
        await db_session_real.commit()
        await db_session_real.refresh(conversation)

        # Create messages
        message1 = ChatMessage(
            conversation_id=conversation.id,
            sender_role="user",
            content={"text": "First message"},
        )
        message2 = ChatMessage(
            conversation_id=conversation.id,
            sender_role="agent",
            content={"text": "Second message"},
        )

        db_session_real.add_all([message1, message2])
        await db_session_real.commit()

        # Act - Retrieve messages for the conversation
        result = await db_session_real.execute(
            select(ChatMessage)
            .where(ChatMessage.conversation_id == conversation.id)
            .order_by(ChatMessage.timestamp)
        )
        messages = result.scalars().all()

        # Assert
        assert len(messages) == 2
        assert messages[0].sender_role in ["user", "agent"]
        assert messages[1].sender_role in ["user", "agent"]

        # Test that messages are linked to the conversation
        assert messages[0].conversation_id == conversation.id
        assert messages[1].conversation_id == conversation.id

    @pytest.mark.asyncio
    async def test_chat_message_cascade_delete_with_conversation(
        self,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
    ):
        """Test messages deleted when conversation deleted (CASCADE)."""
        # Arrange - Create conversation and messages
        conversation = Conversation(
            user_id=test_user.id,
            assistant_id=test_assistant_id,
        )
        db_session_real.add(conversation)
        await db_session_real.commit()
        await db_session_real.refresh(conversation)

        message = ChatMessage(
            conversation_id=conversation.id,
            sender_role="user",
            content={"text": "This message should be deleted"},
        )
        db_session_real.add(message)
        await db_session_real.commit()
        message_id = message.id

        # Act - Delete the conversation
        await db_session_real.delete(conversation)
        await db_session_real.commit()

        # Assert - Message should be deleted due to CASCADE
        result = await db_session_real.execute(
            select(ChatMessage).where(ChatMessage.id == message_id)
        )
        deleted_message = result.scalar_one_or_none()
        assert deleted_message is None

    @pytest.mark.asyncio
    async def test_chat_message_ordering_by_timestamp(
        self,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
    ):
        """Test messages ordered by timestamp in conversation relationship."""
        # Arrange - Create conversation
        conversation = Conversation(
            user_id=test_user.id,
            assistant_id=test_assistant_id,
        )
        db_session_real.add(conversation)
        await db_session_real.commit()
        await db_session_real.refresh(conversation)

        # Create messages with different timestamps
        import time

        message1 = ChatMessage(
            conversation_id=conversation.id,
            sender_role="user",
            content={"text": "First message"},
        )
        db_session_real.add(message1)
        await db_session_real.commit()

        # Small delay to ensure different timestamps
        time.sleep(0.01)

        message2 = ChatMessage(
            conversation_id=conversation.id,
            sender_role="agent",
            content={"text": "Second message"},
        )
        db_session_real.add(message2)
        await db_session_real.commit()

        # Act - Retrieve messages ordered by timestamp
        result = await db_session_real.execute(
            select(ChatMessage)
            .where(ChatMessage.conversation_id == conversation.id)
            .order_by(ChatMessage.timestamp)
        )
        messages = result.scalars().all()

        # Assert - Messages should be ordered by timestamp
        assert len(messages) == 2
        assert messages[0].timestamp <= messages[1].timestamp
        assert messages[0].content["text"] == "First message"
        assert messages[1].content["text"] == "Second message"

    @pytest.mark.asyncio
    async def test_chat_message_empty_content_validation(self):
        """Test that empty content is handled appropriately."""
        conversation_id = uuid.uuid4()

        # Test with empty dict
        message = ChatMessage(
            conversation_id=conversation_id,
            sender_role="user",
            content={},
        )
        assert message.content == {}

        # Test with minimal content structure
        message_with_text = ChatMessage(
            conversation_id=conversation_id,
            sender_role="user",
            content={"text": ""},
        )
        assert message_with_text.content == {"text": ""}

    @pytest.mark.asyncio
    async def test_chat_message_large_content_handling(self):
        """Test that large content is handled properly."""
        conversation_id = uuid.uuid4()

        # Create large content (simulating a long message)
        large_text = "A" * 4000  # 4000 characters
        large_content = {
            "text": large_text,
            "metadata": {"length": len(large_text), "type": "long_message"},
        }

        message = ChatMessage(
            conversation_id=conversation_id,
            sender_role="user",
            content=large_content,
        )

        assert len(message.content["text"]) == 4000
        assert message.content["metadata"]["length"] == 4000

    @pytest.mark.asyncio
    async def test_chat_message_special_characters_in_content(self):
        """Test that special characters in content are handled properly."""
        conversation_id = uuid.uuid4()

        # Test with various special characters and unicode
        special_content = {
            "text": "Hello! @#$%^&*()_+-=[]{}|;':\",./<>? 🚀 🎉 测试 العربية",
            "emojis": ["🚀", "🎉", "💻"],
            "unicode_text": "测试 العربية русский",
        }

        message = ChatMessage(
            conversation_id=conversation_id,
            sender_role="user",
            content=special_content,
        )

        expected_text = "Hello! @#$%^&*()_+-=[]{}|;':\",./<>? 🚀 🎉 测试 العربية"
        assert message.content["text"] == expected_text
        assert message.content["emojis"] == ["🚀", "🎉", "💻"]
        assert message.content["unicode_text"] == "测试 العربية русский"

    @pytest.mark.asyncio
    async def test_chat_message_metadata_structure_validation(self):
        """Test that metadata can contain various data structures."""
        conversation_id = uuid.uuid4()

        complex_metadata = {
            "user_agent": "Mozilla/5.1...",
            "ip_address": "***********",
            "session_id": str(uuid.uuid4()),
            "features": {
                "mentions": ["@user1", "@user2"],
                "hashtags": ["#important", "#work"],
                "links": ["https://example.com"],
            },
            "analytics": {"read_time": 15.5, "word_count": 42, "sentiment": "positive"},
            "flags": {"is_edited": False, "is_pinned": True, "is_urgent": False},
        }

        message = ChatMessage(
            conversation_id=conversation_id,
            sender_role="user",
            content={"text": "Test message"},
            message_metadata=complex_metadata,
        )

        assert message.message_metadata["user_agent"] == "Mozilla/5.1..."
        assert len(message.message_metadata["features"]["mentions"]) == 2
        assert message.message_metadata["analytics"]["word_count"] == 42
        assert message.message_metadata["flags"]["is_pinned"] is True

    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_chat_message_index_performance(
        self,
        db_session_real: AsyncSession,
        test_user: User,
        test_assistant_id: uuid.UUID,
    ):
        """Test database indexes work correctly for conversation queries."""
        # Arrange - Create conversation
        conversation = Conversation(
            user_id=test_user.id,
            assistant_id=test_assistant_id,
        )
        db_session_real.add(conversation)
        await db_session_real.commit()
        await db_session_real.refresh(conversation)

        # Create multiple messages to test index usage
        messages = []
        for i in range(10):
            message = ChatMessage(
                conversation_id=conversation.id,
                sender_role="user" if i % 2 == 0 else "agent",
                content={"text": f"Message {i}"},
            )
            messages.append(message)

        db_session_real.add_all(messages)
        await db_session_real.commit()

        # Act - Query using the composite index (conversation_id, timestamp)
        result = await db_session_real.execute(
            select(ChatMessage)
            .where(ChatMessage.conversation_id == conversation.id)
            .order_by(ChatMessage.timestamp.desc())
            .limit(5)
        )
        recent_messages = result.scalars().all()

        # Assert - Should get the 5 most recent messages
        assert len(recent_messages) == 5
        # Verify they are in descending timestamp order
        for i in range(len(recent_messages) - 1):
            current_msg = recent_messages[i]
            next_msg = recent_messages[i + 1]
            assert current_msg.timestamp >= next_msg.timestamp
