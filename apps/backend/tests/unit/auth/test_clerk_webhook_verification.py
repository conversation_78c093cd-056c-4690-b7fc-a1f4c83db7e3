import base64
import hmac
import json
import time
import uuid
from hashlib import sha256
from unittest.mock import MagicMock, patch

import pytest

from a2a_platform.auth.clerk import verify_webhook_signature


@pytest.mark.no_db
class TestWebhookSignatureVerification:
    """Tests for the webhook signature verification function."""

    # Test data
    test_secret = "whsec_test_secret_key"
    test_payload = json.dumps(
        {"type": "user.created", "data": {"id": "test_id"}}
    ).encode()

    @pytest.fixture(autouse=True)
    def setup_mocks(self):
        """Set up mocks for all tests in this class."""
        # Create a mock settings object
        mock_settings = MagicMock()
        mock_settings.CLERK_WEBHOOK_SECRET = self.test_secret

        # Patch the get_settings function to return our mock settings
        with patch("a2a_platform.auth.clerk.get_settings", return_value=mock_settings):
            yield

    @pytest.fixture
    def mock_webhook_verify_success(self):
        """Mock the Webhook.verify method to always return success."""
        with patch("a2a_platform.auth.clerk.Webhook") as mock_webhook:
            # Set up the mock webhook
            mock_webhook_instance = MagicMock()
            mock_webhook.return_value = mock_webhook_instance

            # Make verify return True for positive tests
            mock_webhook_instance.verify.return_value = True

            yield mock_webhook_instance

    @pytest.fixture
    def mock_webhook_verify_failure(self):
        """Mock the Webhook.verify method to always raise WebhookVerificationError."""
        with patch("a2a_platform.auth.clerk.Webhook") as mock_webhook:
            # Set up the mock webhook
            mock_webhook_instance = MagicMock()
            mock_webhook.return_value = mock_webhook_instance

            # Make verify raise WebhookVerificationError for negative tests
            from svix.webhooks import WebhookVerificationError

            mock_webhook_instance.verify.side_effect = WebhookVerificationError(
                "Invalid signature"
            )

            yield mock_webhook_instance

    def test_valid_signature_svix_format(self, mock_webhook_verify_success):
        """
        Test that a valid signature in Svix format is correctly verified.

        This test creates a signature using the same algorithm and format that Svix uses,
        which is what Clerk implements for webhook signatures.
        """
        # Create a valid signature using Svix's format
        timestamp = str(int(time.time()))
        signature_payload = f"{timestamp}.{self.test_payload.decode()}"

        # Create HMAC-SHA256 signature and base64 encode it (as Svix expects)
        hmac_signature = hmac.new(
            self.test_secret.encode(), signature_payload.encode(), sha256
        ).digest()
        signature = base64.b64encode(hmac_signature).decode()

        # Format the signature as Svix would
        svix_signature = f"v1,{timestamp},{signature}"

        # Verify the signature
        result = verify_webhook_signature(svix_signature, self.test_payload)
        assert result is True

        # Verify that the mock was called with the correct arguments
        mock_webhook_verify_success.verify.assert_called_once()

    def test_multiple_signatures_svix_format(self, mock_webhook_verify_success):
        """
        Test that multiple signatures in Svix format are correctly verified.

        Svix allows multiple signatures to be provided, separated by spaces.
        At least one must be valid for verification to succeed.
        """
        # Create a valid signature using Svix's format
        timestamp = str(int(time.time()))
        signature_payload = f"{timestamp}.{self.test_payload.decode()}"

        # Create HMAC-SHA256 signature and base64 encode it (as Svix expects)
        hmac_signature = hmac.new(
            self.test_secret.encode(), signature_payload.encode(), sha256
        ).digest()
        signature = base64.b64encode(hmac_signature).decode()

        # Create an invalid signature (properly base64 encoded for format validity)
        invalid_signature = base64.b64encode(b"invalid_signature").decode()

        # Format with multiple signatures (one valid, one invalid)
        svix_signature = (
            f"v1,{timestamp},{signature} v1,{timestamp},{invalid_signature}"
        )

        # Verify the signature
        result = verify_webhook_signature(svix_signature, self.test_payload)
        assert result is True

        # Verify that the mock was called with the correct arguments
        mock_webhook_verify_success.verify.assert_called_once()

    def test_invalid_signature(self, mock_webhook_verify_failure):
        """Test that an invalid signature is correctly rejected."""
        # Create an invalid signature
        timestamp = str(int(time.time()))

        # Create a properly base64-encoded invalid signature
        invalid_signature = base64.b64encode(b"invalid_signature").decode()

        # Format the signature as Svix would
        svix_signature = f"v1,{timestamp},{invalid_signature}"

        # Verify the signature
        result = verify_webhook_signature(svix_signature, self.test_payload)
        assert result is False

        # Verify that the mock was called with the correct arguments
        mock_webhook_verify_failure.verify.assert_called_once()

    def test_missing_signature(self, mock_webhook_verify_failure):
        """Test that a missing signature is correctly rejected."""
        # Verify with empty signature
        result = verify_webhook_signature("", self.test_payload)
        assert result is False

        # Based on the implementation, even empty signatures are passed to the Webhook.verify method
        # which will then raise a WebhookVerificationError
        mock_webhook_verify_failure.verify.assert_called_once()

    def test_malformed_signature(self, mock_webhook_verify_failure):
        """Test that malformed signatures are correctly rejected."""
        # Create malformed signatures
        malformed_signatures = [
            "not_a_valid_format",
            "v1",
            "v1,",
            "v1,timestamp",
            "v1,timestamp,",
            "v2,timestamp,signature",  # Wrong version
        ]

        # Verify each malformed signature
        for sig in malformed_signatures:
            # Reset the mock for each iteration
            mock_webhook_verify_failure.verify.reset_mock()

            result = verify_webhook_signature(sig, self.test_payload)
            assert result is False, (
                f"Malformed signature '{sig}' was incorrectly accepted"
            )

            # For malformed signatures, the function might not call Webhook.verify
            # depending on the implementation, so we don't assert on the mock

    def test_tampered_payload(self, mock_webhook_verify_failure):
        """Test that a tampered payload is correctly rejected."""
        # Create a valid signature for the original payload
        timestamp = str(int(time.time()))
        signature_payload = f"{timestamp}.{self.test_payload.decode()}"

        # Create HMAC-SHA256 signature and base64 encode it (as Svix expects)
        hmac_signature = hmac.new(
            self.test_secret.encode(), signature_payload.encode(), sha256
        ).digest()
        signature = base64.b64encode(hmac_signature).decode()

        # Format the signature as Svix would
        svix_signature = f"v1,{timestamp},{signature}"

        # Create a tampered payload
        tampered_payload = json.dumps(
            {"type": "user.created", "data": {"id": "tampered_id"}}
        ).encode()

        # Verify the signature with the tampered payload
        result = verify_webhook_signature(svix_signature, tampered_payload)
        assert result is False

        # Verify that the mock was called with the correct arguments
        mock_webhook_verify_failure.verify.assert_called_once()

    def test_expired_timestamp(self, mock_webhook_verify_success):
        """
        Test that a signature with an expired timestamp is rejected.

        Svix typically enforces a 5-minute window for signature validity.
        """
        # Create a timestamp from 10 minutes ago (expired)
        expired_time = int(time.time()) - 600  # 10 minutes ago
        timestamp = str(expired_time)

        signature_payload = f"{timestamp}.{self.test_payload.decode()}"

        # Create HMAC-SHA256 signature and base64 encode it (as Svix expects)
        hmac_signature = hmac.new(
            self.test_secret.encode(), signature_payload.encode(), sha256
        ).digest()
        signature = base64.b64encode(hmac_signature).decode()

        # Format the signature as Svix would
        svix_signature = f"v1,{timestamp},{signature}"

        # Verify the signature
        result = verify_webhook_signature(svix_signature, self.test_payload)

        # In our mocked environment, this should pass because we're mocking the verification
        assert result is True

        # Verify that the mock was called with the correct arguments
        mock_webhook_verify_success.verify.assert_called_once()

        # Note: In a real environment, Svix might reject this due to the expired timestamp
        # This test is primarily to ensure our code correctly passes the signature to Svix

    def test_clerk_svix_headers_format(self, mock_webhook_verify_success):
        """
        Test with the complete set of Svix headers that Clerk sends.

        Clerk sends three headers:
        - svix-id: A unique ID for the webhook event
        - svix-timestamp: The timestamp when the webhook was sent
        - svix-signature: The signature for verification

        Our webhook handler extracts these headers and passes the signature to verify_webhook_signature.
        """
        # Create a valid signature
        timestamp = str(int(time.time()))
        svix_id = f"msg_{uuid.uuid4().hex}"

        signature_payload = f"{timestamp}.{self.test_payload.decode()}"

        # Create HMAC-SHA256 signature and base64 encode it (as Svix expects)
        hmac_signature = hmac.new(
            self.test_secret.encode(), signature_payload.encode(), sha256
        ).digest()
        signature = base64.b64encode(hmac_signature).decode()

        # Format the signature as Svix would
        svix_signature = f"v1,{timestamp},{signature}"

        # Create the complete set of Svix headers
        svix_headers = {
            "svix-id": svix_id,
            "svix-timestamp": timestamp,
            "svix-signature": svix_signature,
        }

        # Verify the signature using just the signature header
        result = verify_webhook_signature(
            svix_headers["svix-signature"], self.test_payload
        )
        assert result is True

        # Verify that the mock was called with the correct arguments
        mock_webhook_verify_success.verify.assert_called_once()

    def test_real_world_example(self, mock_webhook_verify_success):
        """
        Test with a real-world example payload and signature format.

        This test uses a sample payload similar to what Clerk would send,
        with a signature generated in the same format Clerk would use.
        """
        # Sample payload based on Clerk's documentation and the user's example
        real_payload = json.dumps(
            {
                "data": {
                    "birthday": "",
                    "created_at": *************,
                    "email_addresses": [
                        {
                            "email_address": "<EMAIL>",
                            "id": "idn_29w83yL7CwVlJXylYLxcslromF1",
                            "linked_to": [],
                            "object": "email_address",
                            "verification": {
                                "status": "verified",
                                "strategy": "ticket",
                            },
                        }
                    ],
                    "external_accounts": [],
                    "external_id": "567772",
                    "first_name": "Example",
                    "gender": "",
                    "id": "user_29w83sxmDNGwOuEthce5gg56FcC",
                    "image_url": "https://img.clerk.com/xxxxxx",
                    "last_name": "Example",
                    "last_sign_in_at": *************,
                    "object": "user",
                    "password_enabled": True,
                    "phone_numbers": [],
                    "primary_email_address_id": "idn_29w83yL7CwVlJXylYLxcslromF1",
                    "primary_phone_number_id": None,
                    "primary_web3_wallet_id": None,
                    "private_metadata": {},
                    "profile_image_url": "https://www.gravatar.com/avatar?d=mp",
                    "public_metadata": {},
                    "two_factor_enabled": False,
                    "unsafe_metadata": {},
                    "updated_at": *************,
                    "username": None,
                    "web3_wallets": [],
                },
                "event_attributes": {
                    "http_request": {
                        "client_ip": "0.0.0.0",
                        "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                    }
                },
                "object": "event",
                "timestamp": *************,
                "type": "user.created",
            }
        ).encode()

        # Create a valid signature
        timestamp = str(int(time.time()))
        signature_payload = f"{timestamp}.{real_payload.decode()}"

        # Create HMAC-SHA256 signature and base64 encode it (as Svix expects)
        hmac_signature = hmac.new(
            self.test_secret.encode(), signature_payload.encode(), sha256
        ).digest()
        signature = base64.b64encode(hmac_signature).decode()

        # Format the signature as Svix would
        svix_signature = f"v1,{timestamp},{signature}"

        # Verify the signature
        result = verify_webhook_signature(svix_signature, real_payload)
        assert result is True

        # Verify that the mock was called with the correct arguments
        mock_webhook_verify_success.verify.assert_called_once()
