"""
Unit tests for PydanticAI Response Service.

Tests the PydanticAI-based AI response generation logic, multi-provider support,
rate limiting, and error handling.
"""

import time
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from pydantic_ai.exceptions import UnexpectedModelBehavior

from a2a_platform.db.models.assistant import Assistant
from a2a_platform.db.models.chat_message import ChatMessage
from a2a_platform.services.pydantic_ai_response_service import (
    AIResponseConfig,
    PydanticAIResponseService,
    RateLimiter,
)


class TestRateLimiter:
    """Test rate limiter functionality."""

    def test_rate_limiter_allows_calls_within_limit(self):
        """Test that rate limiter allows calls within the limit."""
        limiter = RateLimiter(max_calls=3, window_seconds=60)

        # Should allow first 3 calls
        assert limiter.acquire() is True
        assert limiter.acquire() is True
        assert limiter.acquire() is True

        # Should reject 4th call
        assert limiter.acquire() is False

    def test_rate_limiter_resets_after_window(self):
        """Test that rate limiter resets after the time window."""
        limiter = RateLimiter(max_calls=2, window_seconds=1)

        # Use up the limit
        assert limiter.acquire() is True
        assert limiter.acquire() is True
        assert limiter.acquire() is False

        # Wait for window to pass
        time.sleep(1.1)

        # Should allow calls again
        assert limiter.acquire() is True

    def test_time_until_next_call(self):
        """Test calculation of time until next call is allowed."""
        limiter = RateLimiter(max_calls=1, window_seconds=60)

        # No calls made yet
        assert limiter.time_until_next_call() == 0.0

        # Make a call
        limiter.acquire()

        # Should have time remaining
        time_remaining = limiter.time_until_next_call()
        assert 0 < time_remaining <= 60


class TestAIResponseConfig:
    """Test AI response configuration."""

    def test_default_config(self):
        """Test default configuration values."""
        config = AIResponseConfig()

        assert config.model == "openai:gpt-3.5-turbo"
        assert config.temperature == 0.7
        assert config.max_tokens == 500
        assert config.timeout == 30

    def test_custom_config(self):
        """Test custom configuration values."""
        config = AIResponseConfig(
            model="anthropic:claude-3-5-sonnet-latest",
            temperature=0.5,
            max_tokens=1000,
            timeout=60,
        )

        assert config.model == "anthropic:claude-3-5-sonnet-latest"
        assert config.temperature == 0.5
        assert config.max_tokens == 1000
        assert config.timeout == 60

    def test_config_validation(self):
        """Test configuration validation."""
        # Temperature out of range
        with pytest.raises(ValueError):
            AIResponseConfig(temperature=3.0)

        # Max tokens too low
        with pytest.raises(ValueError):
            AIResponseConfig(max_tokens=0)


class TestPydanticAIResponseService:
    """Test PydanticAI response service functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.service = PydanticAIResponseService()

        # Create test assistant
        self.assistant = Assistant(
            id="test-assistant-id",
            name="TestBot",
            backstory="I am a helpful test assistant.",
            configuration={
                "ai": {
                    "model": "openai:gpt-3.5-turbo",
                    "temperature": 0.7,
                    "max_tokens": 150,
                }
            },
        )

        # Create test messages
        self.test_messages = [
            ChatMessage(
                id="msg1",
                conversation_id="conv1",
                sender_role="user",
                content={"parts": [{"type": "text", "content": "Hello"}]},
                timestamp=time.time(),
            ),
            ChatMessage(
                id="msg2",
                conversation_id="conv1",
                sender_role="agent",
                content={"parts": [{"type": "text", "content": "Hi there!"}]},
                timestamp=time.time(),
            ),
        ]

    def test_get_ai_config(self):
        """Test extraction of AI configuration from assistant."""
        config = self.service._get_ai_config(self.assistant)

        assert config.model == "openai:gpt-3.5-turbo"
        assert config.temperature == 0.7
        assert config.max_tokens == 150

    def test_get_ai_config_defaults(self):
        """Test AI configuration with defaults when not specified."""
        assistant_no_config = Assistant(
            id="test-id", name="TestBot", backstory="Test", configuration={}
        )

        config = self.service._get_ai_config(assistant_no_config)

        # Should use defaults
        assert config.model == "openai:gpt-3.5-turbo"
        assert config.temperature == 0.7

    def test_build_system_prompt(self):
        """Test system prompt building."""
        config = AIResponseConfig()
        prompt = self.service._build_system_prompt(self.assistant, config)

        assert "TestBot" in prompt
        assert "helpful test assistant" in prompt

    def test_extract_message_text(self):
        """Test message text extraction."""
        message = self.test_messages[0]
        text = self.service._extract_message_text(message)

        assert text == "Hello"

    def test_build_conversation_messages(self):
        """Test conversation context building."""
        context = self.service._build_conversation_messages(self.test_messages)

        assert len(context) == 2
        assert "User: Hello" in context
        assert "Assistant: Hi there!" in context

    def test_build_conversation_messages_limits(self):
        """Test conversation context with message limits."""
        # Create many messages
        many_messages = []
        for i in range(15):
            many_messages.append(
                ChatMessage(
                    id=f"msg{i}",
                    conversation_id="conv1",
                    sender_role="user",
                    content={"parts": [{"type": "text", "content": f"Message {i}"}]},
                    timestamp=time.time() + i,
                )
            )

        context = self.service._build_conversation_messages(
            many_messages, max_context=5
        )

        # Should only include last 5 messages
        assert len(context) == 5
        assert "Message 10" in context[0]  # Should start from message 10

    def test_create_model_instance_openai(self):
        """Test OpenAI model instance creation."""
        config = AIResponseConfig(model="openai:gpt-4")
        model = self.service._create_model_instance(config)

        from pydantic_ai.models.openai import OpenAIModel

        assert isinstance(model, OpenAIModel)

    def test_create_model_instance_anthropic(self):
        """Test Anthropic model instance creation."""
        config = AIResponseConfig(model="anthropic:claude-3-5-sonnet-latest")
        model = self.service._create_model_instance(config)

        from pydantic_ai.models.anthropic import AnthropicModel

        assert isinstance(model, AnthropicModel)

    def test_create_model_instance_gemini(self):
        """Test Gemini model instance creation."""
        config = AIResponseConfig(model="google-gla:gemini-2.0-flash")
        model = self.service._create_model_instance(config)

        from pydantic_ai.models.gemini import GeminiModel

        assert isinstance(model, GeminiModel)

    def test_create_model_instance_vertex(self):
        """Test Google Vertex AI model instance creation."""
        config = AIResponseConfig(model="google-vertex:gemini-2.0-flash")
        model = self.service._create_model_instance(config)

        from pydantic_ai.models.gemini import GeminiModel

        assert isinstance(model, GeminiModel)

    @pytest.mark.asyncio
    async def test_generate_response_rate_limited(self):
        """Test response generation when rate limited."""
        # Set up rate limiter to reject calls
        self.service.rate_limiter = RateLimiter(max_calls=0, window_seconds=60)

        response, is_ai = await self.service.generate_response(
            assistant=self.assistant, user_message="Hello", conversation_history=[]
        )

        # Should return fallback response
        assert response in self.service.fallback_responses
        assert is_ai is False

    @pytest.mark.asyncio
    async def test_generate_response_force_fallback(self):
        """Test forced fallback response."""
        response, is_ai = await self.service.generate_response(
            assistant=self.assistant,
            user_message="Hello",
            conversation_history=[],
            force_fallback=True,
        )

        # Should return fallback response without calling AI
        assert response in self.service.fallback_responses
        assert is_ai is False

    @pytest.mark.asyncio
    async def test_generate_response_pydantic_ai_error(self):
        """Test response generation when PydanticAI fails."""
        with patch(
            "a2a_platform.services.pydantic_ai_response_service.Agent"
        ) as mock_agent_class:
            mock_agent = AsyncMock()
            mock_agent.run.side_effect = UnexpectedModelBehavior("Test error")
            mock_agent_class.return_value = mock_agent

            response, is_ai = await self.service.generate_response(
                assistant=self.assistant, user_message="Hello", conversation_history=[]
            )

            # Should return fallback response
            assert response in self.service.fallback_responses
            assert is_ai is False

    @pytest.mark.asyncio
    async def test_generate_response_success(self):
        """Test successful response generation."""
        # Mock the Agent class and its constructor
        with patch(
            "a2a_platform.services.pydantic_ai_response_service.Agent"
        ) as mock_agent_class:
            mock_result = MagicMock()
            mock_result.output = "I'm doing well, thank you!"

            mock_agent = AsyncMock()
            mock_agent.run.return_value = mock_result
            mock_agent_class.return_value = mock_agent

            response, is_ai = await self.service.generate_response(
                assistant=self.assistant,
                user_message="How are you?",
                conversation_history=self.test_messages,
            )

            assert response == "I'm doing well, thank you!"
            assert is_ai is True

    @pytest.mark.asyncio
    async def test_test_model_availability_success(self):
        """Test model availability check success."""
        with patch(
            "a2a_platform.services.pydantic_ai_response_service.Agent"
        ) as mock_agent_class:
            mock_result = MagicMock()
            mock_result.output = "test successful"

            mock_agent = AsyncMock()
            mock_agent.run.return_value = mock_result
            mock_agent_class.return_value = mock_agent

            available = await self.service.test_model_availability(
                "openai:gpt-3.5-turbo"
            )
            assert available is True

    @pytest.mark.asyncio
    async def test_test_model_availability_failure(self):
        """Test model availability check failure."""
        with patch(
            "a2a_platform.services.pydantic_ai_response_service.Agent"
        ) as mock_agent_class:
            mock_agent = AsyncMock()
            mock_agent.run.side_effect = Exception("Model not available")
            mock_agent_class.return_value = mock_agent

            available = await self.service.test_model_availability("invalid:model")
            assert available is False

    def test_get_supported_models(self):
        """Test getting supported models list."""
        models = self.service.get_supported_models()

        assert "openai" in models
        assert "anthropic" in models
        assert "google" in models

        assert "openai:gpt-3.5-turbo" in models["openai"]
        assert "anthropic:claude-3-5-sonnet-latest" in models["anthropic"]
        assert "google-gla:gemini-2.0-flash" in models["google"]

    def test_get_fallback_response(self):
        """Test fallback response selection."""
        response = self.service._get_fallback_response()

        assert response in self.service.fallback_responses
        assert len(response) > 0
