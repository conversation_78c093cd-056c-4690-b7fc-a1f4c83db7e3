"""
Unit tests for the objective security service.
"""

import uuid
from unittest.mock import AsyncMock, patch

import pytest

from a2a_platform.db.models.assistant_objective import AssistantObjective
from a2a_platform.schemas.objective_schemas import ObjectiveCreate
from a2a_platform.services.objective_security_service import ObjectiveSecurityService


@pytest.fixture
def mock_db_session():
    """Create a mock database session for testing."""
    session = AsyncMock()
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    session.refresh = AsyncMock()
    return session


@pytest.fixture
def objective_security_service(mock_db_session):
    """Create an ObjectiveSecurityService instance with a mock database session."""
    return ObjectiveSecurityService(db_session=mock_db_session)


@pytest.fixture
def sample_user_id():
    """Create a sample user ID for testing."""
    return "user123"


@pytest.fixture
def sample_assistant_id():
    """Create a sample assistant ID for testing."""
    return uuid.uuid4()


@pytest.fixture
def sample_objective_id():
    """Create a sample objective ID for testing."""
    return uuid.uuid4()


@pytest.fixture
def sample_objective_model(sample_assistant_id):
    """Create a sample AssistantObjective model instance."""
    return AssistantObjective(
        id=uuid.uuid4(),
        assistant_id=sample_assistant_id,
        objective_text="Learn Spanish",
        status="active",
    )


@pytest.fixture
def sample_objective_create(sample_assistant_id):
    """Create a sample ObjectiveCreate instance."""
    return ObjectiveCreate(
        assistant_id=sample_assistant_id,
        objective_text="Learn Spanish",
        status="active",
    )


class TestObjectiveSecurityService:
    """Tests for the ObjectiveSecurityService class."""

    @pytest.mark.asyncio
    async def test_verify_assistant_ownership(
        self, objective_security_service, sample_user_id, sample_assistant_id
    ):
        """Test verifying assistant ownership."""
        # For now, this should always return True
        result = await objective_security_service.verify_assistant_ownership(
            sample_user_id, sample_assistant_id
        )
        assert result is True

    @pytest.mark.asyncio
    async def test_get_user_objective_by_id_success(
        self,
        objective_security_service,
        sample_user_id,
        sample_objective_id,
        sample_objective_model,
    ):
        """Test getting an objective by ID with permission."""
        # Mock the objective service's get_objective_by_id method
        with patch.object(
            objective_security_service.objective_service,
            "get_objective_by_id",
            AsyncMock(return_value=sample_objective_model),
        ) as mock_get_objective:
            # Mock the verify_assistant_ownership method
            with patch.object(
                objective_security_service,
                "verify_assistant_ownership",
                AsyncMock(return_value=True),
            ) as mock_verify:
                # Get the objective
                result = await objective_security_service.get_user_objective_by_id(
                    sample_user_id, sample_objective_id
                )

                # Assert that the objective was returned
                assert result is sample_objective_model

                # Verify the methods were called with the correct arguments
                mock_get_objective.assert_called_once_with(sample_objective_id)
                mock_verify.assert_called_once_with(
                    sample_user_id, sample_objective_model.assistant_id
                )

    @pytest.mark.asyncio
    async def test_get_user_objective_by_id_not_found(
        self, objective_security_service, sample_user_id, sample_objective_id
    ):
        """Test getting a non-existent objective by ID."""
        # Mock the objective service's get_objective_by_id method to return None
        with patch.object(
            objective_security_service.objective_service,
            "get_objective_by_id",
            AsyncMock(return_value=None),
        ) as mock_get_objective:
            # Get the objective
            result = await objective_security_service.get_user_objective_by_id(
                sample_user_id, sample_objective_id
            )

            # Assert that None was returned
            assert result is None

            # Verify the method was called with the correct argument
            mock_get_objective.assert_called_once_with(sample_objective_id)

    @pytest.mark.asyncio
    async def test_get_user_objective_by_id_permission_denied(
        self,
        objective_security_service,
        sample_user_id,
        sample_objective_id,
        sample_objective_model,
    ):
        """Test getting an objective by ID without permission."""
        # Mock the objective service's get_objective_by_id method
        with patch.object(
            objective_security_service.objective_service,
            "get_objective_by_id",
            AsyncMock(return_value=sample_objective_model),
        ) as mock_get_objective:
            # Mock the verify_assistant_ownership method to return False
            with patch.object(
                objective_security_service,
                "verify_assistant_ownership",
                AsyncMock(return_value=False),
            ) as mock_verify:
                # Assert that a PermissionError is raised
                with pytest.raises(PermissionError) as excinfo:
                    await objective_security_service.get_user_objective_by_id(
                        sample_user_id, sample_objective_id
                    )

                # Verify the error message
                assert "does not have access to this objective" in str(excinfo.value)

                # Verify the methods were called with the correct arguments
                mock_get_objective.assert_called_once_with(sample_objective_id)
                mock_verify.assert_called_once_with(
                    sample_user_id, sample_objective_model.assistant_id
                )

    @pytest.mark.asyncio
    async def test_get_user_objectives_success(
        self,
        objective_security_service,
        sample_user_id,
        sample_assistant_id,
        sample_objective_model,
    ):
        """Test getting objectives for an assistant with permission."""
        # Mock the objective service's get_objectives_by_assistant_id method
        with patch.object(
            objective_security_service.objective_service,
            "get_objectives_by_assistant_id",
            AsyncMock(return_value=[sample_objective_model]),
        ) as mock_get_objectives:
            # Mock the verify_assistant_ownership method
            with patch.object(
                objective_security_service,
                "verify_assistant_ownership",
                AsyncMock(return_value=True),
            ) as mock_verify:
                # Get the objectives
                result = await objective_security_service.get_user_objectives(
                    sample_user_id,
                    sample_assistant_id,
                    status="active",
                    limit=10,
                    offset=0,
                )

                # Assert that the objectives were returned
                assert result == [sample_objective_model]

                # Verify the methods were called with the correct arguments
                mock_get_objectives.assert_called_once_with(
                    sample_assistant_id, "active", 10, 0
                )
                mock_verify.assert_called_once_with(sample_user_id, sample_assistant_id)

    @pytest.mark.asyncio
    async def test_get_user_objectives_permission_denied(
        self, objective_security_service, sample_user_id, sample_assistant_id
    ):
        """Test getting objectives for an assistant without permission."""
        # Mock the verify_assistant_ownership method to return False
        with patch.object(
            objective_security_service,
            "verify_assistant_ownership",
            AsyncMock(return_value=False),
        ) as mock_verify:
            # Assert that a PermissionError is raised
            with pytest.raises(PermissionError) as excinfo:
                await objective_security_service.get_user_objectives(
                    sample_user_id, sample_assistant_id
                )

            # Verify the error message
            assert "does not have access to this assistant's objectives" in str(
                excinfo.value
            )

            # Verify the method was called with the correct arguments
            mock_verify.assert_called_once_with(sample_user_id, sample_assistant_id)

    @pytest.mark.asyncio
    async def test_add_user_objective_success(
        self,
        objective_security_service,
        sample_user_id,
        sample_objective_create,
        sample_objective_model,
    ):
        """Test adding an objective with permission."""
        # Mock the objective service's add_assistant_objective method
        with patch.object(
            objective_security_service.objective_service,
            "add_assistant_objective",
            AsyncMock(return_value=sample_objective_model),
        ) as mock_add_objective:
            # Mock the verify_assistant_ownership method
            with patch.object(
                objective_security_service,
                "verify_assistant_ownership",
                AsyncMock(return_value=True),
            ) as mock_verify:
                # Add the objective
                result = await objective_security_service.add_user_objective(
                    sample_user_id, sample_objective_create
                )

                # Assert that the objective was returned
                assert result is sample_objective_model

                # Verify the methods were called with the correct arguments
                mock_add_objective.assert_called_once_with(sample_objective_create)
                mock_verify.assert_called_once_with(
                    sample_user_id, sample_objective_create.assistant_id
                )

    @pytest.mark.asyncio
    async def test_add_user_objective_permission_denied(
        self, objective_security_service, sample_user_id, sample_objective_create
    ):
        """Test adding an objective without permission."""
        # Mock the verify_assistant_ownership method to return False
        with patch.object(
            objective_security_service,
            "verify_assistant_ownership",
            AsyncMock(return_value=False),
        ) as mock_verify:
            # Assert that a PermissionError is raised
            with pytest.raises(PermissionError) as excinfo:
                await objective_security_service.add_user_objective(
                    sample_user_id, sample_objective_create
                )

            # Verify the error message
            assert "does not have access to this assistant" in str(excinfo.value)

            # Verify the method was called with the correct arguments
            mock_verify.assert_called_once_with(
                sample_user_id, sample_objective_create.assistant_id
            )

    @pytest.mark.asyncio
    async def test_update_user_objective_status_success(
        self,
        objective_security_service,
        sample_user_id,
        sample_objective_id,
        sample_objective_model,
    ):
        """Test updating an objective status with permission."""
        # Mock the get_user_objective_by_id method
        with patch.object(
            objective_security_service,
            "get_user_objective_by_id",
            AsyncMock(return_value=sample_objective_model),
        ) as mock_get_objective:
            # Mock the objective service's update_objective_status method
            with patch.object(
                objective_security_service.objective_service,
                "update_objective_status",
                AsyncMock(return_value=sample_objective_model),
            ) as mock_update_status:
                # Update the objective status
                result = await objective_security_service.update_user_objective_status(
                    sample_user_id, sample_objective_id, "completed"
                )

                # Assert that the objective was returned
                assert result is sample_objective_model

                # Verify the methods were called with the correct arguments
                mock_get_objective.assert_called_once_with(
                    sample_user_id, sample_objective_id
                )
                mock_update_status.assert_called_once_with(
                    sample_objective_id, "completed"
                )

    @pytest.mark.asyncio
    async def test_update_user_objective_status_not_found(
        self, objective_security_service, sample_user_id, sample_objective_id
    ):
        """Test updating a non-existent objective status."""
        # Mock the get_user_objective_by_id method to return None
        with patch.object(
            objective_security_service,
            "get_user_objective_by_id",
            AsyncMock(return_value=None),
        ) as mock_get_objective:
            # Update the objective status
            result = await objective_security_service.update_user_objective_status(
                sample_user_id, sample_objective_id, "completed"
            )

            # Assert that None was returned
            assert result is None

            # Verify the method was called with the correct arguments
            mock_get_objective.assert_called_once_with(
                sample_user_id, sample_objective_id
            )
