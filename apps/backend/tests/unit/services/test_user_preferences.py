import uuid
from unittest.mock import AsyncMock, patch

import pytest
from sqlalchemy.exc import NoResultFound

from a2a_platform.db.models.user import User
from a2a_platform.services.user_service import (
    get_user_preferences,
    update_user_preferences,
)


@pytest.mark.asyncio
async def test_update_user_preferences_success(mock_db_session: AsyncMock):
    """Test updating a user's preferences successfully"""
    # Arrange
    clerk_user_id = "test_clerk_id"
    new_preferences = {"theme": "dark", "notifications": {"email": True, "push": False}}
    new_timezone = "America/New_York"

    # Create a mock user that will be returned by get_user_by_clerk_id
    mock_user = User(
        id=uuid.uuid4(),
        clerk_user_id=clerk_user_id,
        email="<EMAIL>",
        timezone="UTC",  # Original timezone
        preferences={
            "language": "en",
            "notifications": {"sms": True},
        },  # Original preferences
    )

    # Mock the get_user_by_clerk_id function to return our mock user
    with patch(
        "a2a_platform.services.user_service.get_user_by_clerk_id",
        return_value=mock_user,
    ):
        # Act
        updated_user = await update_user_preferences(
            mock_db_session,
            clerk_user_id=clerk_user_id,
            preferences_update=new_preferences,
            timezone_update=new_timezone,
        )

        # Assert
        assert updated_user is not None
        assert updated_user.clerk_user_id == clerk_user_id
        assert updated_user.timezone == new_timezone  # Timezone should be updated

        # Check that preferences were deep merged correctly
        assert updated_user.preferences["theme"] == "dark"  # New key
        assert updated_user.preferences["language"] == "en"  # Preserved key
        assert (
            updated_user.preferences["notifications"]["email"] is True
        )  # New nested key
        assert (
            updated_user.preferences["notifications"]["push"] is False
        )  # New nested key
        assert (
            updated_user.preferences["notifications"]["sms"] is True
        )  # Preserved nested key

        # Verify that the session was committed
        mock_db_session.commit.assert_called_once()
        mock_db_session.refresh.assert_called_once_with(mock_user)


@pytest.mark.asyncio
async def test_update_user_preferences_only_preferences(mock_db_session: AsyncMock):
    """Test updating only a user's preferences without changing timezone"""
    # Arrange
    clerk_user_id = "test_clerk_id"
    new_preferences = {"theme": "light"}
    original_timezone = "UTC"

    # Create a mock user that will be returned by get_user_by_clerk_id
    mock_user = User(
        id=uuid.uuid4(),
        clerk_user_id=clerk_user_id,
        email="<EMAIL>",
        timezone=original_timezone,
        preferences={"language": "en"},
    )

    # Mock the get_user_by_clerk_id function to return our mock user
    with patch(
        "a2a_platform.services.user_service.get_user_by_clerk_id",
        return_value=mock_user,
    ):
        # Act
        updated_user = await update_user_preferences(
            mock_db_session,
            clerk_user_id=clerk_user_id,
            preferences_update=new_preferences,
            # No timezone_update provided
        )

        # Assert
        assert updated_user is not None
        assert updated_user.timezone == original_timezone  # Timezone should not change
        assert updated_user.preferences["theme"] == "light"  # New preference
        assert updated_user.preferences["language"] == "en"  # Preserved preference

        # Verify that the session was committed
        mock_db_session.commit.assert_called_once()


@pytest.mark.asyncio
async def test_update_user_preferences_only_timezone(mock_db_session: AsyncMock):
    """Test updating only a user's timezone without changing preferences"""
    # Arrange
    clerk_user_id = "test_clerk_id"
    new_timezone = "Europe/London"
    original_preferences = {"language": "en", "theme": "dark"}

    # Create a mock user that will be returned by get_user_by_clerk_id
    mock_user = User(
        id=uuid.uuid4(),
        clerk_user_id=clerk_user_id,
        email="<EMAIL>",
        timezone="UTC",
        preferences=original_preferences.copy(),  # Copy to avoid reference issues
    )

    # Mock the get_user_by_clerk_id function to return our mock user
    with patch(
        "a2a_platform.services.user_service.get_user_by_clerk_id",
        return_value=mock_user,
    ):
        # Act
        updated_user = await update_user_preferences(
            mock_db_session,
            clerk_user_id=clerk_user_id,
            preferences_update={},  # Empty preferences update
            timezone_update=new_timezone,
        )

        # Assert
        assert updated_user is not None
        assert updated_user.timezone == new_timezone  # Timezone should be updated
        assert (
            updated_user.preferences == original_preferences
        )  # Preferences should not change

        # Verify that the session was committed
        mock_db_session.commit.assert_called_once()


@pytest.mark.asyncio
async def test_update_user_preferences_null_preferences(mock_db_session: AsyncMock):
    """Test updating a user with null preferences"""
    # Arrange
    clerk_user_id = "test_clerk_id"
    new_preferences = {"theme": "dark"}

    # Create a mock user with null preferences
    mock_user = User(
        id=uuid.uuid4(),
        clerk_user_id=clerk_user_id,
        email="<EMAIL>",
        timezone="UTC",
        preferences=None,  # Null preferences
    )

    # Mock the get_user_by_clerk_id function to return our mock user
    with patch(
        "a2a_platform.services.user_service.get_user_by_clerk_id",
        return_value=mock_user,
    ):
        # Act
        updated_user = await update_user_preferences(
            mock_db_session,
            clerk_user_id=clerk_user_id,
            preferences_update=new_preferences,
        )

        # Assert
        assert updated_user is not None
        assert updated_user.preferences is not None
        assert updated_user.preferences["theme"] == "dark"

        # Verify that the session was committed
        mock_db_session.commit.assert_called_once()


@pytest.mark.asyncio
async def test_update_user_preferences_user_not_found(mock_db_session: AsyncMock):
    """Test updating preferences when the user is not found"""
    # Arrange
    clerk_user_id = "nonexistent_clerk_id"
    new_preferences = {"theme": "dark"}

    # Mock the get_user_by_clerk_id function to return None (user not found)
    with patch(
        "a2a_platform.services.user_service.get_user_by_clerk_id", return_value=None
    ):
        # Act & Assert
        with pytest.raises(NoResultFound) as excinfo:
            await update_user_preferences(
                mock_db_session,
                clerk_user_id=clerk_user_id,
                preferences_update=new_preferences,
            )

        # Verify the error message
        assert f"User with clerk_user_id {clerk_user_id} not found" in str(
            excinfo.value
        )

        # Verify that the session was not committed
        mock_db_session.commit.assert_not_called()
        mock_db_session.refresh.assert_not_called()


@pytest.mark.asyncio
async def test_get_user_preferences_success(mock_db_session: AsyncMock):
    """Test getting a user's preferences successfully"""
    # Arrange
    clerk_user_id = "test_clerk_id"
    preferences = {"theme": "dark", "language": "en"}
    timezone = "America/New_York"

    # Create a mock user that will be returned by get_user_by_clerk_id
    mock_user = User(
        id=uuid.uuid4(),
        clerk_user_id=clerk_user_id,
        email="<EMAIL>",
        timezone=timezone,
        preferences=preferences,
    )

    # Mock the get_user_by_clerk_id function to return our mock user
    with patch(
        "a2a_platform.services.user_service.get_user_by_clerk_id",
        return_value=mock_user,
    ):
        # Act
        result = await get_user_preferences(
            mock_db_session,
            clerk_user_id=clerk_user_id,
        )

        # Assert
        assert result is not None
        assert result["preferences"] == preferences
        assert result["timezone"] == timezone


@pytest.mark.asyncio
async def test_get_user_preferences_user_not_found(mock_db_session: AsyncMock):
    """Test getting preferences when the user is not found"""
    # Arrange
    clerk_user_id = "nonexistent_clerk_id"

    # Mock the get_user_by_clerk_id function to return None (user not found)
    with patch(
        "a2a_platform.services.user_service.get_user_by_clerk_id", return_value=None
    ):
        # Act
        result = await get_user_preferences(
            mock_db_session,
            clerk_user_id=clerk_user_id,
        )

        # Assert
        assert result is None
