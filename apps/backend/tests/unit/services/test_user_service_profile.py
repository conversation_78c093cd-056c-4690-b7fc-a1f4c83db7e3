import uuid
from unittest.mock import AsyncMock, patch

import pytest
from sqlalchemy.exc import NoResultFound

from a2a_platform.db.models.user import User
from a2a_platform.services.user_service import update_user_profile


@pytest.mark.asyncio
async def test_update_user_profile_success(mock_db_session: AsyncMock):
    """Test updating a user's profile successfully"""
    # Arrange
    clerk_user_id = "test_clerk_id"
    new_timezone = "America/New_York"

    # Create a mock user that will be returned by get_user_by_clerk_id
    mock_user = User(
        id=uuid.uuid4(),
        clerk_user_id=clerk_user_id,
        email="<EMAIL>",
        timezone="UTC",  # Original timezone
        preferences={},
    )

    # Mock the get_user_by_clerk_id function to return our mock user
    with patch(
        "a2a_platform.services.user_service.get_user_by_clerk_id",
        return_value=mock_user,
    ):
        # Act
        updated_user = await update_user_profile(
            mock_db_session, clerk_user_id, new_timezone
        )

        # Assert
        assert updated_user is not None
        assert updated_user.clerk_user_id == clerk_user_id
        assert updated_user.timezone == new_timezone  # Timezone should be updated

        # Verify that the session was committed
        mock_db_session.commit.assert_called_once()
        mock_db_session.refresh.assert_called_once_with(mock_user)


@pytest.mark.asyncio
async def test_update_user_profile_user_not_found(mock_db_session: AsyncMock):
    """Test updating a user's profile when the user is not found"""
    # Arrange
    clerk_user_id = "nonexistent_clerk_id"
    new_timezone = "America/New_York"

    # Mock the get_user_by_clerk_id function to return None (user not found)
    with patch(
        "a2a_platform.services.user_service.get_user_by_clerk_id", return_value=None
    ):
        # Act & Assert
        with pytest.raises(NoResultFound) as excinfo:
            await update_user_profile(mock_db_session, clerk_user_id, new_timezone)

        # Verify the error message
        assert f"User with clerk_user_id {clerk_user_id} not found" in str(
            excinfo.value
        )

        # Verify that the session was not committed
        mock_db_session.commit.assert_not_called()
        mock_db_session.refresh.assert_not_called()
