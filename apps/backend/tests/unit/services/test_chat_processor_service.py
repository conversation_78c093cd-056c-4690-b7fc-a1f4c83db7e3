"""
Unit tests for the chat processor service.

These tests verify the functionality of the ChatProcessorService, which is responsible
for processing chat messages, identifying objectives, and storing them using the
ObjectiveService.
"""

import re
import uuid
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from sqlalchemy.exc import IntegrityError, SQLAlchemyError

from a2a_platform.db.models.assistant_objective import AssistantObjective
from a2a_platform.schemas.objective_schemas import ObjectiveCreate
from a2a_platform.services.chat_processor_service import (
    ChatProcessorService,
    ObjectiveIdentificationError,
    ObjectiveStorageError,
)


@pytest.fixture
def mock_db_session():
    """Create a mock database session for testing."""
    session = AsyncMock()
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    session.refresh = AsyncMock()
    return session


@pytest.fixture
def chat_processor_service(mock_db_session):
    """Create a ChatProcessorService instance with a mock database session."""
    return ChatProcessorService(db_session=mock_db_session)


@pytest.fixture
def sample_assistant_id():
    """Create a sample assistant ID for testing."""
    return uuid.uuid4()


@pytest.fixture
def sample_objective_model(sample_assistant_id):
    """Create a sample AssistantObjective model instance."""
    return AssistantObjective(
        id=uuid.uuid4(),
        assistant_id=sample_assistant_id,
        objective_text="Learn Spanish",
        status="active",
    )


@pytest.mark.no_db
class TestChatProcessorService:
    """Tests for the ChatProcessorService class."""

    @pytest.mark.parametrize(
        "message,expected_objective",
        [
            ("My goal is to learn Spanish", "learn Spanish"),
            ("my objective is to read one book per month", "read one book per month"),
            ("I want to save $500", "save $500"),
            ("I would like to exercise daily", "exercise daily"),
            ("Help me lose weight", "lose weight"),
            ("I need to finish my project", "finish my project"),
            ("I aim to improve my coding skills", "improve my coding skills"),
            ("I plan to travel to Japan", "travel to Japan"),
            ("This is just a regular message", None),
            ("Hello, how are you?", None),
        ],
    )
    def test_identify_potential_objective(
        self, chat_processor_service, message, expected_objective
    ):
        """Test identifying potential objectives in messages."""
        result = chat_processor_service._identify_potential_objective(message)
        assert result == expected_objective

    @pytest.mark.asyncio
    async def test_process_message_with_objective(
        self, chat_processor_service, sample_assistant_id, sample_objective_model
    ):
        """Test processing a message that contains an objective."""
        # Mock the objective service's add_assistant_objective method
        with patch.object(
            chat_processor_service.objective_service,
            "add_assistant_objective",
            AsyncMock(return_value=sample_objective_model),
        ) as mock_add_objective:
            # Process a message with an objective
            message = "My goal is to learn Spanish"
            result, objective = await chat_processor_service.process_message(
                message, sample_assistant_id
            )

            # Assert that the objective was identified and stored
            assert result is True
            assert objective is sample_objective_model

            # Verify the objective service was called with the correct data
            mock_add_objective.assert_called_once()
            call_args = mock_add_objective.call_args[0][0]
            assert isinstance(call_args, ObjectiveCreate)
            assert call_args.assistant_id == sample_assistant_id
            assert call_args.objective_text == "learn Spanish"
            assert call_args.status == "active"

    @pytest.mark.asyncio
    async def test_process_message_without_objective(
        self, chat_processor_service, sample_assistant_id
    ):
        """Test processing a message that does not contain an objective."""
        # Mock the objective service's add_assistant_objective method
        with patch.object(
            chat_processor_service.objective_service,
            "add_assistant_objective",
            AsyncMock(),
        ) as mock_add_objective:
            # Process a message without an objective
            message = "Hello, how are you?"
            result, objective = await chat_processor_service.process_message(
                message, sample_assistant_id
            )

            # Assert that no objective was identified or stored
            assert result is False
            assert objective is None

            # Verify the objective service was not called
            mock_add_objective.assert_not_called()

    @pytest.mark.asyncio
    async def test_process_message_with_value_error(
        self, chat_processor_service, sample_assistant_id
    ):
        """Test processing a message with an objective but encountering a ValueError."""
        # Mock the objective service's add_assistant_objective method to raise a ValueError
        with patch.object(
            chat_processor_service.objective_service,
            "add_assistant_objective",
            AsyncMock(side_effect=ValueError("Test error")),
        ) as mock_add_objective:
            # Process a message with an objective
            message = "My goal is to learn Spanish"
            result, objective = await chat_processor_service.process_message(
                message, sample_assistant_id
            )

            # Assert that the objective was identified but not stored
            assert result is False
            assert objective is None

            # Verify the objective service was called
            mock_add_objective.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_message_with_integrity_error(
        self, chat_processor_service, sample_assistant_id
    ):
        """Test processing a message with an objective but encountering an IntegrityError."""
        # Mock the objective service's add_assistant_objective method to raise an IntegrityError
        with patch.object(
            chat_processor_service.objective_service,
            "add_assistant_objective",
            AsyncMock(side_effect=IntegrityError("statement", "params", "orig")),
        ) as mock_add_objective:
            # Process a message with an objective
            message = "My goal is to learn Spanish"

            # Assert that the ObjectiveStorageError is raised
            with pytest.raises(ObjectiveStorageError) as excinfo:
                await chat_processor_service.process_message(
                    message, sample_assistant_id
                )

            # Verify the error message
            assert "Database integrity error" in str(excinfo.value)

            # Verify the objective service was called
            mock_add_objective.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_message_with_sqlalchemy_error(
        self, chat_processor_service, sample_assistant_id
    ):
        """Test processing a message with an objective but encountering a SQLAlchemyError."""
        # Mock the objective service's add_assistant_objective method to raise a SQLAlchemyError
        with patch.object(
            chat_processor_service.objective_service,
            "add_assistant_objective",
            AsyncMock(side_effect=SQLAlchemyError("Test error")),
        ) as mock_add_objective:
            # Process a message with an objective
            message = "My goal is to learn Spanish"

            # Assert that the ObjectiveStorageError is raised
            with pytest.raises(ObjectiveStorageError) as excinfo:
                await chat_processor_service.process_message(
                    message, sample_assistant_id
                )

            # Verify the error message
            assert "Database error" in str(excinfo.value)

            # Verify the objective service was called
            mock_add_objective.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_message_with_unexpected_error(
        self, chat_processor_service, sample_assistant_id
    ):
        """Test processing a message with an objective but encountering an unexpected error."""
        # Mock the objective service's add_assistant_objective method to raise an unexpected error
        with patch.object(
            chat_processor_service.objective_service,
            "add_assistant_objective",
            AsyncMock(side_effect=Exception("Test error")),
        ) as mock_add_objective:
            # Process a message with an objective
            message = "My goal is to learn Spanish"

            # Assert that the ObjectiveStorageError is raised
            with pytest.raises(ObjectiveStorageError) as excinfo:
                await chat_processor_service.process_message(
                    message, sample_assistant_id
                )

            # Verify the error message
            assert "Unexpected error" in str(excinfo.value)

            # Verify the objective service was called
            mock_add_objective.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_message_with_special_characters(
        self, chat_processor_service, sample_assistant_id, sample_objective_model
    ):
        """Test processing a message with special characters."""
        # Mock the objective service's add_assistant_objective method
        with patch.object(
            chat_processor_service.objective_service,
            "add_assistant_objective",
            AsyncMock(return_value=sample_objective_model),
        ) as mock_add_objective:
            # Process a message with special characters
            message = "My goal is to learn Spanish: ¡Hola, cómo estás! @#$%^&*"

            # Call the method
            result, objective = await chat_processor_service.process_message(
                message, sample_assistant_id
            )

            # Assert that an objective was identified and stored
            assert result is True
            assert objective is sample_objective_model

            # Verify the objective service was called with the correct parameters
            mock_add_objective.assert_called_once()
            # Get the first positional argument (ObjectiveCreate instance)
            objective_data = mock_add_objective.call_args[0][0]
            assert isinstance(objective_data, ObjectiveCreate)
            assert (
                "learn Spanish: ¡Hola, cómo estás! @#$%^&*"
                in objective_data.objective_text
            )
            assert objective_data.assistant_id == sample_assistant_id

    @pytest.mark.asyncio
    async def test_process_message_with_multiple_users(
        self, chat_processor_service, sample_objective_model
    ):
        """Test processing messages from multiple users (assistants)."""
        # Mock the objective service's add_assistant_objective method
        with patch.object(
            chat_processor_service.objective_service,
            "add_assistant_objective",
            AsyncMock(return_value=sample_objective_model),
        ) as mock_add_objective:
            # Create two different assistant IDs
            assistant_id_1 = uuid.uuid4()
            assistant_id_2 = uuid.uuid4()

            # Process messages from both assistants
            message_1 = "My goal is to learn Spanish"
            message_2 = "I want to save money"

            # Call the method for the first assistant
            result_1, _ = await chat_processor_service.process_message(
                message_1, assistant_id_1
            )

            # Call the method for the second assistant
            result_2, _ = await chat_processor_service.process_message(
                message_2, assistant_id_2
            )

            # Assert that objectives were identified and stored for both assistants
            assert result_1 is True
            assert result_2 is True

            # Verify the objective service was called twice with different assistant IDs
            assert mock_add_objective.call_count == 2

            # Check first call
            first_call_args = mock_add_objective.call_args_list[0][0][0]
            assert first_call_args.assistant_id == assistant_id_1
            assert "learn Spanish" in first_call_args.objective_text

            # Check second call
            second_call_args = mock_add_objective.call_args_list[1][0][0]
            assert second_call_args.assistant_id == assistant_id_2
            assert "save money" in second_call_args.objective_text

    def test_identify_potential_objective_with_invalid_input(
        self, chat_processor_service
    ):
        """Test identifying potential objectives with invalid input."""
        # Test with None
        result = chat_processor_service._identify_potential_objective(None)
        assert result is None

        # Test with empty string
        result = chat_processor_service._identify_potential_objective("")
        assert result is None

        # Test with non-string
        result = chat_processor_service._identify_potential_objective(123)
        assert result is None

    def test_identify_potential_objective_with_empty_match(
        self, chat_processor_service
    ):
        """Test identifying potential objectives with a match that results in empty text."""
        # Mock re.search to return a match that will result in empty text after processing
        with patch("re.search") as mock_search:
            mock_match = MagicMock()
            mock_match.group.return_value = (
                "!!!"  # This will become empty after removing punctuation
            )
            mock_search.return_value = mock_match

            result = chat_processor_service._identify_potential_objective(
                "My goal is to !!!"
            )
            assert result is None

    def test_identify_potential_objective_with_regex_error(
        self, chat_processor_service
    ):
        """Test identifying potential objectives with a regex error."""
        # Mock re.search to raise a re.error
        with patch("re.search", side_effect=re.error("Test regex error")):
            with pytest.raises(ObjectiveIdentificationError) as excinfo:
                chat_processor_service._identify_potential_objective(
                    "My goal is to learn Spanish"
                )

            assert "Regex error" in str(excinfo.value)

    def test_identify_potential_objective_with_unexpected_error(
        self, chat_processor_service
    ):
        """Test identifying potential objectives with an unexpected error."""
        # Mock re.search to raise an unexpected error
        with patch("re.search", side_effect=Exception("Test unexpected error")):
            with pytest.raises(ObjectiveIdentificationError) as excinfo:
                chat_processor_service._identify_potential_objective(
                    "My goal is to learn Spanish"
                )

            assert "Unexpected error" in str(excinfo.value)
