from typing import List
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from sqlalchemy.engine import Result
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql.expression import BinaryExpression

from a2a_platform.db.models.registered_agent import (
    RegisteredAgent as RegisteredAgentModel,
)
from a2a_platform.schemas.agent_schemas import AgentSkill, RegisteredAgentCreate
from a2a_platform.services.agent_service import AgentService


# Helper to create a mock SQLAlchemy execution result
def create_mock_result(
    scalar_one_or_none_return=None, scalar_one_return=None, scalars_all_return=None
):
    mock_result = MagicMock(spec=Result)
    if scalar_one_or_none_return is not None:
        mock_result.scalar_one_or_none.return_value = scalar_one_or_none_return
    if scalar_one_return is not None:
        mock_result.scalar_one.return_value = scalar_one_return
    if scalars_all_return is not None:
        mock_result.scalars.return_value.all.return_value = scalars_all_return
    else:  # Ensure scalars().all() is still mockable even if not returning a list
        mock_result.scalars.return_value.all.return_value = []
    return mock_result


@pytest.fixture
def mock_db_session() -> AsyncMock:
    session = AsyncMock(spec=AsyncSession)
    # Ensure execute returns a mock_result by default, which can be further configured per test
    session.execute.return_value = create_mock_result()
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    session.add = MagicMock()
    session.flush = AsyncMock()
    return session


@pytest.fixture
def agent_service(mock_db_session: AsyncMock) -> AgentService:
    return AgentService(db_session=mock_db_session)


@pytest.fixture
def sample_agent_create_data() -> RegisteredAgentCreate:
    return RegisteredAgentCreate(
        agent_definition_id="test_agent_v1",
        name="Test Agent",
        version="1.0.0",
        description="A test agent for unit testing.",
        endpoint_url="http://localhost:8001/invoke",
        async_queue_name="test_agent_queue",
        capabilities={"can_summarize": True, "can_translate": False},
        skills=[
            AgentSkill(name="summarization", description="Summarizes text."),
            AgentSkill(
                name="translation", parameters_schema={"target_language": "string"}
            ),
        ],
        authentication_info={"type": "api_key"},
        status="active",
        # New marketplace fields
        developer_id="dev123",
        pricing_info={"type": "subscription", "amount_monthly": 999},
        review_status="approved",
    )


@pytest.fixture
def sample_db_agent_model(
    sample_agent_create_data: RegisteredAgentCreate,
) -> RegisteredAgentModel:
    # Create a model instance that would be returned by the DB
    data = sample_agent_create_data.model_dump()
    # Convert skills back to dicts as they would be in the DB model from service logic
    if data.get("skills"):
        data["skills"] = [
            skill.model_dump() if isinstance(skill, AgentSkill) else skill
            for skill in data["skills"]
        ]

    # Pop HttpUrl if it exists as it might not serialize directly to str in all mock scenarios
    if "endpoint_url" in data and data["endpoint_url"] is not None:
        data["endpoint_url"] = str(data["endpoint_url"])

    return RegisteredAgentModel(**data)


# --- Tests for register_agent ---


@pytest.mark.no_db
@pytest.mark.asyncio
async def test_register_agent_success(
    agent_service: AgentService,
    mock_db_session: AsyncMock,
    sample_agent_create_data: RegisteredAgentCreate,
):
    created_agent = await agent_service.register_agent(sample_agent_create_data)

    mock_db_session.add.assert_called_once()
    added_object = mock_db_session.add.call_args[0][0]
    assert isinstance(added_object, RegisteredAgentModel)
    assert (
        added_object.agent_definition_id == sample_agent_create_data.agent_definition_id
    )
    # Verify marketplace fields are correctly passed to the model
    assert added_object.developer_id == sample_agent_create_data.developer_id
    assert added_object.pricing_info == sample_agent_create_data.pricing_info
    assert added_object.review_status == sample_agent_create_data.review_status
    assert added_object.name == sample_agent_create_data.name
    assert len(added_object.skills) == 2
    assert added_object.skills[0]["name"] == "summarization"

    mock_db_session.commit.assert_called_once()
    mock_db_session.refresh.assert_called_once_with(added_object)
    assert (
        created_agent.agent_definition_id
        == sample_agent_create_data.agent_definition_id
    )


@pytest.mark.no_db
@pytest.mark.asyncio
async def test_register_agent_without_marketplace_fields(
    agent_service: AgentService,
    mock_db_session: AsyncMock,
):
    # Create agent data without marketplace fields
    agent_data = RegisteredAgentCreate(
        agent_definition_id="minimal_agent_v1",
        name="Minimal Agent",
        version="1.0.0",
        # No marketplace fields provided
    )

    await agent_service.register_agent(agent_data)

    mock_db_session.add.assert_called_once()
    added_object = mock_db_session.add.call_args[0][0]
    assert isinstance(added_object, RegisteredAgentModel)
    assert added_object.agent_definition_id == "minimal_agent_v1"
    # Verify marketplace fields are None or default values
    assert added_object.developer_id is None
    assert added_object.pricing_info == {}
    assert added_object.review_status is None


@pytest.mark.no_db
@pytest.mark.asyncio
async def test_register_agent_duplicate_id_raises_value_error(
    agent_service: AgentService,
    mock_db_session: AsyncMock,
    sample_agent_create_data: RegisteredAgentCreate,
):
    # Simulate IntegrityError for primary key violation
    mock_db_session.commit.side_effect = IntegrityError(
        "mocked commit error",
        params=None,
        orig=Exception(
            "unique constraint failed: registered_agents_pkey agent_definition_id"
        ),  # Simulate DB error message
    )

    with pytest.raises(
        ValueError,
        match=f"Agent with definition ID '{sample_agent_create_data.agent_definition_id}' already exists.",
    ):
        await agent_service.register_agent(sample_agent_create_data)

    mock_db_session.rollback.assert_called_once()


@pytest.mark.no_db
@pytest.mark.asyncio
async def test_register_agent_other_integrity_error_raises_value_error(
    agent_service: AgentService,
    mock_db_session: AsyncMock,
    sample_agent_create_data: RegisteredAgentCreate,
):
    # Simulate a different IntegrityError (e.g., FK violation, though not applicable here directly)
    orig_error = Exception("some other integrity constraint failed")
    mock_db_session.commit.side_effect = IntegrityError(
        "mocked commit error", params=None, orig=orig_error
    )

    with pytest.raises(
        ValueError,
        match=f"Database integrity error during agent registration: {str(orig_error)}",
    ):
        await agent_service.register_agent(sample_agent_create_data)

    mock_db_session.rollback.assert_called_once()


# --- Tests for get_agent_by_definition_id ---


@pytest.mark.no_db
@pytest.mark.asyncio
async def test_get_agent_by_definition_id_found(
    agent_service: AgentService,
    mock_db_session: AsyncMock,
    sample_db_agent_model: RegisteredAgentModel,
):
    mock_db_session.execute.return_value = create_mock_result(
        scalar_one_or_none_return=sample_db_agent_model
    )

    found_agent = await agent_service.get_agent_by_definition_id(
        sample_db_agent_model.agent_definition_id
    )

    mock_db_session.execute.assert_called_once()  # More detailed assertion on the select statement could be added
    assert found_agent is not None
    assert found_agent.agent_definition_id == sample_db_agent_model.agent_definition_id
    assert found_agent.name == sample_db_agent_model.name


@pytest.mark.no_db
@pytest.mark.asyncio
async def test_get_agent_by_definition_id_not_found(
    agent_service: AgentService, mock_db_session: AsyncMock
):
    # Directly configure the scalar_one_or_none part of the mock chain for this specific test call
    mock_db_session.execute.return_value.scalar_one_or_none.return_value = None
    agent_id = "non_existent_agent"

    found_agent = await agent_service.get_agent_by_definition_id(agent_id)

    mock_db_session.execute.assert_called_once()
    assert found_agent is None


# --- Tests for list_agents ---


@pytest.fixture
def sample_agent_list() -> List[RegisteredAgentModel]:
    return [
        RegisteredAgentModel(
            agent_definition_id="agent1",
            name="Agent One",
            version="1.0",
            status="active",
            capabilities={"cap1": True},
        ),
        RegisteredAgentModel(
            agent_definition_id="agent2",
            name="Agent Two",
            version="1.0",
            status="inactive",
            capabilities={"cap1": True, "cap2": True},
        ),
        RegisteredAgentModel(
            agent_definition_id="agent3",
            name="Agent Three",
            version="1.0",
            status="active",
            capabilities={"cap2": True},
        ),
    ]


@pytest.mark.no_db
@pytest.mark.asyncio
async def test_list_agents_no_filters(
    agent_service: AgentService,
    mock_db_session: AsyncMock,
    sample_agent_list: List[RegisteredAgentModel],
):
    mock_db_session.execute.side_effect = [
        create_mock_result(scalar_one_return=len(sample_agent_list)),  # For count_query
        create_mock_result(scalars_all_return=sample_agent_list),  # For data_query
    ]

    agents, total_count = await agent_service.list_agents()

    assert total_count == len(sample_agent_list)
    assert len(agents) == len(sample_agent_list)
    assert agents == sample_agent_list
    assert mock_db_session.execute.call_count == 2


@pytest.mark.no_db
@pytest.mark.asyncio
async def test_list_agents_filter_by_status_active(
    agent_service: AgentService,
    mock_db_session: AsyncMock,
    sample_agent_list: List[RegisteredAgentModel],
):
    active_agents = [agent for agent in sample_agent_list if agent.status == "active"]
    mock_db_session.execute.side_effect = [
        create_mock_result(scalar_one_return=len(active_agents)),
        create_mock_result(scalars_all_return=active_agents),
    ]

    agents, total_count = await agent_service.list_agents(status="active")

    assert total_count == len(active_agents)
    assert len(agents) == len(active_agents)
    # We would need to assert the select statement passed to execute had the status filter
    # This requires deeper inspection of call_args if using MagicMock directly for SQL asserts


@patch("a2a_platform.db.models.registered_agent.RegisteredAgent.capabilities")
@pytest.mark.no_db
@pytest.mark.asyncio
async def test_list_agents_filter_by_capabilities(
    mock_capabilities_column,  # This is the mock for RegisteredAgent.capabilities column attribute
    agent_service: AgentService,
    mock_db_session: AsyncMock,
    sample_agent_list: List[RegisteredAgentModel],
):
    # mock_capabilities_column represents RegisteredAgent.capabilities.
    # The service calls mock_capabilities_column.has_key(cap_name).
    # So, mock_capabilities_column.has_key must be a callable mock (a function/method).

    # This is the mock SQL expression that .has_key(cap_name) should return.
    # Spec it as a BinaryExpression to satisfy SQLAlchemy\'s type checking in .where()
    final_mock_expression = MagicMock(
        name="final_mock_sql_expression_from_has_key", spec=BinaryExpression
    )
    # SQL expressions are truthy.
    final_mock_expression.__bool__ = lambda: True
    # Provide a self_group method that returns itself, common for expressions that don't need explicit grouping.
    final_mock_expression.self_group = MagicMock(return_value=final_mock_expression)

    # Configure mock_capabilities_column.has_key to be a MagicMock instance (which is callable)
    # that, when called, returns final_mock_expression.
    mock_capabilities_column.has_key = MagicMock(
        return_value=final_mock_expression, name="has_key_method_on_column_mock"
    )

    agents_with_cap1 = [
        agent
        for agent in sample_agent_list
        if agent.capabilities and "cap1" in agent.capabilities
    ]

    mock_db_session.execute.side_effect = [
        create_mock_result(scalar_one_return=len(agents_with_cap1)),
        create_mock_result(scalars_all_return=agents_with_cap1),
    ]

    agents, total_count = await agent_service.list_agents(capabilities_filter=["cap1"])

    assert total_count == len(agents_with_cap1)
    assert len(agents) == len(agents_with_cap1)

    # Assert that .has_key was called on our mock_capabilities_column
    mock_capabilities_column.has_key.assert_called_with("cap1")


@pytest.mark.no_db
@pytest.mark.asyncio
async def test_list_agents_pagination(
    agent_service: AgentService,
    mock_db_session: AsyncMock,
    sample_agent_list: List[RegisteredAgentModel],
):
    skip, limit = 1, 1
    expected_agents = sample_agent_list[skip : skip + limit]
    mock_db_session.execute.side_effect = [
        create_mock_result(
            scalar_one_return=len(sample_agent_list)
        ),  # Total count for all items
        create_mock_result(scalars_all_return=expected_agents),
    ]

    agents, total_count = await agent_service.list_agents(skip=skip, limit=limit)

    assert total_count == len(sample_agent_list)
    assert len(agents) == len(expected_agents)
    assert agents == expected_agents
    # Assert that offset(skip).limit(limit) was part of the query used for the second execute call


@pytest.mark.no_db
@pytest.mark.asyncio
async def test_list_agents_empty_result(
    agent_service: AgentService, mock_db_session: AsyncMock
):
    mock_db_session.execute.side_effect = [
        create_mock_result(scalar_one_return=0),
        create_mock_result(scalars_all_return=[]),
    ]

    agents, total_count = await agent_service.list_agents(status="non_existent_status")

    assert total_count == 0
    assert len(agents) == 0


# Placeholder for skills filter tests - these are complex to unit test without more specific DB mocking
# For true validation of skills JSONB querying, integration tests are more suitable.
@pytest.mark.no_db
@pytest.mark.asyncio
async def test_list_agents_filter_by_skills_placeholder():
    # This test would ideally mock the complex JSONB query for skills
    # For now, it's a placeholder acknowledging this part of the service needs specific testing
    pass
