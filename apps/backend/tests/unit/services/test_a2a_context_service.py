"""
Unit tests for A2A context service.
"""

from datetime import UTC, datetime

from a2a_platform.services.a2a_context_service import A2AContextService


class TestA2AContextService:
    def test_create_user_context(self):
        """Test creating a user context."""
        user_context = A2AContextService.create_user_context(
            user_id="user123", initiating_agent_id="agent456"
        )
        assert user_context.user_id == "user123"
        assert user_context.initiating_agent_id == "agent456"
        assert isinstance(user_context.request_timestamp, datetime)

    def test_create_a2a_message(self):
        """Test creating an A2A message."""
        payload = {"action": "test", "data": {"key": "value"}}
        message = A2AContextService.create_a2a_message(
            user_id="user123", initiating_agent_id="agent456", payload=payload
        )
        assert message.user_context.user_id == "user123"
        assert message.user_context.initiating_agent_id == "agent456"
        assert message.payload == payload

    def test_extract_user_context_valid(self):
        """Test extracting a valid user context from an A2A message."""
        a2a_message = {
            "user_context": {
                "user_id": "user123",
                "initiating_agent_id": "agent456",
                "request_timestamp": datetime.now(UTC).isoformat(),
            },
            "payload": {"action": "test"},
        }

        user_context, error_msg = A2AContextService.extract_user_context(a2a_message)
        assert user_context is not None
        assert error_msg is None
        assert user_context.user_id == "user123"
        assert user_context.initiating_agent_id == "agent456"

    def test_extract_user_context_missing(self):
        """Test extracting a user context when it's missing from the message."""
        a2a_message = {"payload": {"action": "test"}}

        user_context, error_msg = A2AContextService.extract_user_context(a2a_message)
        assert user_context is None
        assert error_msg == "No user_context found in A2A message"

    def test_extract_user_context_missing_user_id(self):
        """Test extracting a user context when user_id is missing."""
        a2a_message = {
            "user_context": {
                # Missing user_id
                "initiating_agent_id": "agent456",
                "request_timestamp": datetime.now(UTC).isoformat(),
            },
            "payload": {"action": "test"},
        }
        user_context, error_msg = A2AContextService.extract_user_context(a2a_message)
        assert user_context is None
        assert "Missing required fields in user_context: user_id" in error_msg

    def test_extract_user_context_missing_initiating_agent_id(self):
        """Test extracting a user context when initiating_agent_id is missing."""
        a2a_message = {
            "user_context": {
                "user_id": "user123",
                # Missing initiating_agent_id
                "request_timestamp": datetime.now(UTC).isoformat(),
            },
            "payload": {"action": "test"},
        }
        user_context, error_msg = A2AContextService.extract_user_context(a2a_message)
        assert user_context is None
        assert (
            "Missing required fields in user_context: initiating_agent_id" in error_msg
        )

    def test_extract_user_context_missing_multiple_fields(self):
        """Test extracting a user context when multiple required fields are missing."""
        a2a_message = {
            "user_context": {
                # Missing both user_id and initiating_agent_id
                "request_timestamp": datetime.now(UTC).isoformat()
            },
            "payload": {"action": "test"},
        }
        user_context, error_msg = A2AContextService.extract_user_context(a2a_message)
        assert user_context is None
        assert (
            "Missing required fields in user_context: user_id, initiating_agent_id"
            in error_msg
        )

    def test_extract_user_context_validation_error(self):
        """Test extracting a user context when validation fails."""
        a2a_message = {
            "user_context": {
                "user_id": "user123",
                "initiating_agent_id": "agent456",
                "request_timestamp": "invalid-timestamp",  # Invalid timestamp format
            },
            "payload": {"action": "test"},
        }
        user_context, error_msg = A2AContextService.extract_user_context(a2a_message)
        assert user_context is None
        assert "Validation error in user_context:" in error_msg

    def test_extract_user_context_exception(self):
        """Test extracting a user context when an exception occurs."""
        a2a_message = {"user_context": "not_a_dict", "payload": {"action": "test"}}
        user_context, error_msg = A2AContextService.extract_user_context(a2a_message)
        assert user_context is None
        assert "Error extracting user context from A2A message:" in error_msg
