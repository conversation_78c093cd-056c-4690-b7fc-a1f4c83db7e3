"""
Unit tests for the AssistantService class.

These tests verify the functionality of the AssistantService for creating,
updating, and managing personal assistants.
"""

import uuid
from unittest.mock import AsyncMock, MagicMock

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.assistant import Assistant
from a2a_platform.schemas.assistant_schemas import <PERSON><PERSON><PERSON>
from a2a_platform.services.assistant_service import AssistantService


@pytest.fixture
def mock_db_session():
    """Create a mock database session."""
    session = AsyncMock(spec=AsyncSession)
    session.add = MagicMock()
    session.commit = AsyncMock()
    session.refresh = AsyncMock()
    session.rollback = AsyncMock()
    session.delete = AsyncMock()

    # Mock execute method to return proper mock results
    mock_result = MagicMock()
    mock_result.scalar_one_or_none = MagicMock()
    session.execute = AsyncMock(return_value=mock_result)

    return session


@pytest.fixture
def assistant_service(mock_db_session):
    """Create an AssistantService instance with a mock database session."""
    return AssistantService(db_session=mock_db_session)


@pytest.fixture
def sample_user_id():
    """Generate a sample user ID."""
    return uuid.uuid4()


@pytest.fixture
def sample_assistant_data():
    """Create sample assistant data for testing."""
    return AssistantCreate(
        name="Test Assistant",
        backstory="A helpful test assistant",
        avatar_file_id=None,
        configuration={"theme": "default"},
    )


class TestAssistantService:
    """Tests for the AssistantService class."""

    @pytest.mark.asyncio
    async def test_create_personal_assistant_success(
        self, assistant_service, mock_db_session, sample_user_id, sample_assistant_data
    ):
        """Test successful creation of a personal assistant."""
        # Arrange - Mock user exists and no existing assistant
        from a2a_platform.db.models.user import User

        mock_user = User(
            id=sample_user_id, clerk_user_id="test_clerk_id", email="<EMAIL>"
        )

        # First call returns user, second call returns None (no existing assistant)
        mock_db_session.execute.side_effect = [
            # User lookup result
            MagicMock(scalar_one_or_none=MagicMock(return_value=mock_user)),
            # Existing assistant lookup result
            MagicMock(scalar_one_or_none=MagicMock(return_value=None)),
        ]

        # Act
        result = await assistant_service.create_personal_assistant(
            sample_user_id, sample_assistant_data
        )

        # Assert
        assert result is not None
        assert isinstance(result, Assistant)
        assert result.user_id == sample_user_id
        assert result.name == sample_assistant_data.name
        assert result.backstory == sample_assistant_data.backstory
        assert result.avatar_file_id == sample_assistant_data.avatar_file_id
        assert result.configuration == sample_assistant_data.configuration

        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
        mock_db_session.refresh.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_personal_assistant_duplicate_user(
        self, assistant_service, mock_db_session, sample_user_id, sample_assistant_data
    ):
        """Test creating a personal assistant for a user who already has one."""
        # Arrange - Mock user exists and already has an assistant
        from a2a_platform.db.models.user import User

        mock_user = User(
            id=sample_user_id, clerk_user_id="test_clerk_id", email="<EMAIL>"
        )

        existing_assistant = Assistant(
            id=uuid.uuid4(),
            user_id=sample_user_id,
            name="Existing Assistant",
            backstory="Already exists",
        )

        # First call returns user, second call returns existing assistant
        mock_db_session.execute.side_effect = [
            # User lookup result
            MagicMock(scalar_one_or_none=MagicMock(return_value=mock_user)),
            # Existing assistant lookup result
            MagicMock(scalar_one_or_none=MagicMock(return_value=existing_assistant)),
        ]

        # Act & Assert
        with pytest.raises(ValueError, match="User .* already has an assistant"):
            await assistant_service.create_personal_assistant(
                sample_user_id, sample_assistant_data
            )

    @pytest.mark.asyncio
    async def test_create_personal_assistant_with_minimal_data(
        self, assistant_service, mock_db_session, sample_user_id
    ):
        """Test creating a personal assistant with minimal required data."""
        # Arrange - Mock user exists and no existing assistant
        from a2a_platform.db.models.user import User

        mock_user = User(
            id=sample_user_id, clerk_user_id="test_clerk_id", email="<EMAIL>"
        )

        # First call returns user, second call returns None (no existing assistant)
        mock_db_session.execute.side_effect = [
            # User lookup result
            MagicMock(scalar_one_or_none=MagicMock(return_value=mock_user)),
            # Existing assistant lookup result
            MagicMock(scalar_one_or_none=MagicMock(return_value=None)),
        ]

        minimal_data = AssistantCreate(
            name="Minimal Assistant",
            backstory="Basic backstory",
        )

        # Act
        result = await assistant_service.create_personal_assistant(
            sample_user_id, minimal_data
        )

        # Assert
        assert result.name == "Minimal Assistant"
        assert result.backstory == "Basic backstory"
        assert result.avatar_file_id is None
        assert result.configuration == {}

    @pytest.mark.asyncio
    async def test_get_user_assistant_exists(
        self, assistant_service, mock_db_session, sample_user_id
    ):
        """Test getting an assistant for a user who has one."""
        # Arrange
        mock_assistant = Assistant(
            id=uuid.uuid4(),
            user_id=sample_user_id,
            name="Test Assistant",
            backstory="Test backstory",
        )

        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = mock_assistant
        mock_db_session.execute.return_value = mock_result

        # Act
        result = await assistant_service.get_user_assistant(sample_user_id)

        # Assert
        assert result == mock_assistant
        mock_db_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_user_assistant_not_exists(
        self, assistant_service, mock_db_session, sample_user_id
    ):
        """Test getting an assistant for a user who doesn't have one."""
        # Arrange
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db_session.execute.return_value = mock_result

        # Act
        result = await assistant_service.get_user_assistant(sample_user_id)

        # Assert
        assert result is None
        mock_db_session.execute.assert_called_once()
