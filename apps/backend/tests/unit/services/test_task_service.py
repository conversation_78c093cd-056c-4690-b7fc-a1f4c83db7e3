import uuid
from datetime import UTC, datetime, timedelta
from unittest.mock import AsyncMock, MagicMock

import pytest

from a2a_platform.db.models.task import Task
from a2a_platform.schemas.task_schemas import TaskCreate, TaskUpdate
from a2a_platform.services.task_service import TaskService


@pytest.fixture
def task_service():
    """Create a task service with a mocked session."""
    mock_session = MagicMock()
    mock_session.execute = AsyncMock()
    mock_session.flush = AsyncMock()
    mock_session.commit = AsyncMock()  # Add commit as AsyncMock
    mock_session.rollback = AsyncMock()
    mock_session.add = MagicMock()

    # Setup scalar and first/all methods to handle async properly
    mock_scalar = AsyncMock()
    mock_scalars = AsyncMock()
    mock_first = MagicMock()
    mock_all = MagicMock()

    # Link them correctly for the async chaining
    mock_scalars.return_value.first = mock_first
    mock_scalars.return_value.all = mock_all
    mock_session.execute.return_value.scalar = mock_scalar
    mock_session.execute.return_value.scalars = mock_scalars
    mock_session.execute.return_value.scalar_one = MagicMock()
    mock_session.execute.return_value.scalar_one_or_none = MagicMock()

    # Set up rowcount for update operations
    mock_session.execute.return_value.rowcount = 1

    service = TaskService(mock_session)

    # Patch the get_task_by_id method to return the task directly
    async def patched_get_task_by_id(_):
        # Return the mock directly instead of a coroutine
        mock_result = mock_session.execute.return_value
        result = mock_result.scalars.return_value.first.return_value
        return result

    # Patch the get_task_by_idempotency_key method
    async def patched_get_task_by_idempotency_key(
        assistant_id, idempotency_key, exclude_statuses=None
    ):
        # Return the mock directly instead of a coroutine
        mock_result = mock_session.execute.return_value
        result = mock_result.scalars.return_value.first.return_value
        return result

    # Patch the cancel_task method to update the task status
    async def patched_cancel_task(task_id):
        task = await patched_get_task_by_id(task_id)
        if task:
            task.status = "cancelled"
            await mock_session.flush()
            await mock_session.commit()  # Add commit call
        return task

    # Patch the acquire_lease method to update the task
    async def patched_acquire_lease(
        task_id, lease_owner_id, lease_duration_seconds=300
    ):
        task = await patched_get_task_by_id(task_id)
        if task:
            now = datetime.now(UTC)
            task.lease_owner_id = lease_owner_id
            task.lease_acquired_at = now
            task.lease_expires_at = now + timedelta(seconds=lease_duration_seconds)
            task.status = "leased"
            await mock_session.flush()
            await mock_session.commit()  # Add commit call
        return task

    # Patch the renew_lease method to update the task
    async def patched_renew_lease(task_id, lease_owner_id, lease_duration_seconds=300):
        task = await patched_get_task_by_id(task_id)
        if task and task.lease_owner_id == lease_owner_id:
            now = datetime.now(UTC)
            task.lease_expires_at = now + timedelta(seconds=lease_duration_seconds)
            await mock_session.flush()
            await mock_session.commit()  # Add commit call
        return task

    # Patch the release_lease method to update the task
    async def patched_release_lease(task_id, lease_owner_id):
        task = await patched_get_task_by_id(task_id)
        if task and task.lease_owner_id == lease_owner_id:
            task.lease_owner_id = None
            task.lease_acquired_at = None
            task.lease_expires_at = None
            task.status = "todo"
            await mock_session.flush()
            await mock_session.commit()  # Add commit call
        return task

    service.get_task_by_id = patched_get_task_by_id
    service.get_task_by_idempotency_key = patched_get_task_by_idempotency_key
    service.cancel_task = patched_cancel_task
    service.acquire_lease = patched_acquire_lease
    service.renew_lease = patched_renew_lease
    service.release_lease = patched_release_lease

    return service


class TestTaskService:
    """Tests for the TaskService class."""

    async def test_create_task(self, task_service):
        """Test creating a new task."""
        # Arrange
        assistant_id = uuid.uuid4()
        task_data = TaskCreate(
            description="Test task", metadata={"expected_duration_seconds": 300}
        )
        # Setup first() to return None the first time (for get_task_by_idempotency_key) and the new task the second time
        task_service.session.execute.return_value.scalars.return_value.first.side_effect = [
            None,
            None,
        ]

        # Act
        task = await task_service.create_task(assistant_id, task_data)

        # Assert
        task_service.session.add.assert_called_once()
        task_service.session.flush.assert_called_once()
        assert task.assistant_id == assistant_id
        assert task.description == "Test task"
        assert task.status == "todo"
        assert task.metadata_json == {"expected_duration_seconds": 300}

    async def test_create_task_idempotent(self, task_service):
        """Test idempotent task creation."""
        # Arrange
        assistant_id = uuid.uuid4()
        task_data = TaskCreate(description="Test task")
        idempotency_key = "test-key"

        # Mock an existing task
        existing_task = Task(
            id=uuid.uuid4(),
            assistant_id=assistant_id,
            description="Test task",
            status="in_progress",
        )

        # Set up the mock to return the existing task
        task_service.session.execute.return_value.scalars.return_value.first.return_value = existing_task

        # Act
        task = await task_service.create_task(assistant_id, task_data, idempotency_key)

        # Assert
        assert task is existing_task
        task_service.session.add.assert_not_called()
        task_service.session.flush.assert_not_called()

    async def test_update_task(self, task_service):
        """Test updating a task."""
        # Arrange
        task_id = uuid.uuid4()
        update_data = TaskUpdate(
            description="Updated description",
            status="in_progress",
            metadata={"new_key": "value"},
        )

        # Mock an existing task
        existing_task = Task(
            id=task_id,
            assistant_id=uuid.uuid4(),
            description="Original description",
            status="todo",
            metadata_json={"original_key": "value"},
        )

        # Setup the mock to return the existing task
        task_service.session.execute.return_value.scalars.return_value.first.return_value = existing_task

        # Act
        task = await task_service.update_task(task_id, update_data)

        # Assert
        task_service.session.flush.assert_called_once()
        assert task.description == "Updated description"
        assert task.status == "in_progress"
        assert task.metadata_json == {"original_key": "value", "new_key": "value"}

    async def test_update_task_completed(self, task_service):
        """Test updating a task to completed status."""
        # Arrange
        task_id = uuid.uuid4()
        update_data = TaskUpdate(status="done")

        # Mock an existing task
        existing_task = Task(
            id=task_id,
            assistant_id=uuid.uuid4(),
            description="Original description",
            status="in_progress",
        )

        # Setup the mock to return the existing task
        task_service.session.execute.return_value.scalars.return_value.first.return_value = existing_task

        # Mock the update_dependent_tasks method to avoid the coroutine issue
        task_service.update_dependent_tasks = AsyncMock(return_value=[])

        # Act
        before_time = datetime.now(UTC)
        task = await task_service.update_task(task_id, update_data)
        after_time = datetime.now(UTC)

        # Assert
        task_service.session.flush.assert_called_once()
        assert task.status == "done"
        assert task.completed_at is not None
        assert before_time <= task.completed_at <= after_time

    async def test_cancel_task(self, task_service):
        """Test cancelling a task."""
        # Arrange
        task_id = uuid.uuid4()

        # Mock an existing task
        existing_task = Task(
            id=task_id,
            assistant_id=uuid.uuid4(),
            description="Task to cancel",
            status="in_progress",
        )

        # Setup the mock to return the existing task
        task_service.session.execute.return_value.scalars.return_value.first.return_value = existing_task

        # Act
        task = await task_service.cancel_task(task_id)

        # Assert
        task_service.session.flush.assert_called_once()
        assert task.status == "cancelled"

    async def test_update_dependent_tasks(self, task_service):
        """Test updating dependent tasks when a prerequisite completes."""
        # Arrange
        dependency_id = uuid.uuid4()

        # Mock dependent tasks
        dependent_task1 = Task(
            id=uuid.uuid4(),
            assistant_id=uuid.uuid4(),
            description="Dependent task 1",
            status="pending_dependency",
            depends_on_task_id=dependency_id,
        )
        dependent_task2 = Task(
            id=uuid.uuid4(),
            assistant_id=uuid.uuid4(),
            description="Dependent task 2",
            status="pending_dependency",
            depends_on_task_id=dependency_id,
        )

        # Setup the mock to return the list of dependent tasks
        dependent_tasks = [dependent_task1, dependent_task2]
        task_service.session.execute.return_value.scalars.return_value.all.return_value = dependent_tasks
        task_service.get_pending_dependent_tasks = AsyncMock(
            return_value=dependent_tasks
        )

        # Act
        updated_tasks = await task_service.update_dependent_tasks(dependency_id)

        # Assert
        task_service.get_pending_dependent_tasks.assert_called_once_with(dependency_id)
        task_service.session.flush.assert_called_once()
        assert len(updated_tasks) == 2
        assert all(task.status == "todo" for task in updated_tasks)

    async def test_acquire_lease(self, task_service):
        """Test acquiring a lease on a task."""
        # Arrange
        task_id = uuid.uuid4()
        lease_owner_id = "worker-1"
        lease_duration = 300

        # Mock an unlocked task
        unlocked_task = Task(
            id=task_id,
            assistant_id=uuid.uuid4(),
            description="Task to lease",
            status="todo",
        )

        # Set up the mock to return the unlocked task
        task_service.session.execute.return_value.scalars.return_value.first.return_value = unlocked_task

        # Act
        before_time = datetime.now(UTC)
        task = await task_service.acquire_lease(task_id, lease_owner_id, lease_duration)
        after_time = datetime.now(UTC)

        # Assert
        task_service.session.flush.assert_called_once()
        assert task.lease_owner_id == lease_owner_id
        assert task.lease_acquired_at is not None
        assert before_time <= task.lease_acquired_at <= after_time
        assert task.lease_expires_at is not None
        expected_expiry = task.lease_acquired_at + timedelta(seconds=lease_duration)
        assert task.lease_expires_at == expected_expiry
        assert task.status == "leased"

    async def test_renew_lease(self, task_service):
        """Test renewing a lease on a task."""
        # Arrange
        task_id = uuid.uuid4()
        lease_owner_id = "worker-1"
        lease_duration = 300

        # Current time and expiry
        now = datetime.now(UTC)
        current_expiry = now + timedelta(seconds=60)  # Expires in 60 seconds

        # Mock a leased task
        leased_task = Task(
            id=task_id,
            assistant_id=uuid.uuid4(),
            description="Task with lease",
            status="leased",
            lease_owner_id=lease_owner_id,
            lease_acquired_at=now - timedelta(seconds=240),  # Acquired 4 minutes ago
            lease_expires_at=current_expiry,
        )

        # Setup the mock to return the leased task
        task_service.session.execute.return_value.scalars.return_value.first.return_value = leased_task

        # Act
        task = await task_service.renew_lease(task_id, lease_owner_id, lease_duration)

        # Assert
        task_service.session.flush.assert_called_once()
        assert task.lease_owner_id == lease_owner_id
        assert task.lease_expires_at > current_expiry
        expected_expiry = now + timedelta(seconds=lease_duration)
        assert (
            expected_expiry - task.lease_expires_at
        ).total_seconds() < 1  # Within 1 second

    async def test_release_lease(self, task_service):
        """Test releasing a lease on a task."""
        # Arrange
        task_id = uuid.uuid4()
        lease_owner_id = "worker-1"

        # Mock a leased task
        leased_task = Task(
            id=task_id,
            assistant_id=uuid.uuid4(),
            description="Task with lease",
            status="leased",
            lease_owner_id=lease_owner_id,
            lease_acquired_at=datetime.now(UTC) - timedelta(seconds=60),
            lease_expires_at=datetime.now(UTC) + timedelta(seconds=240),
        )

        # Setup the mock to return the leased task
        task_service.session.execute.return_value.scalars.return_value.first.return_value = leased_task

        # Act
        task = await task_service.release_lease(task_id, lease_owner_id)

        # Assert
        task_service.session.flush.assert_called_once()
        assert task.lease_owner_id is None
        assert task.lease_acquired_at is None
        assert task.lease_expires_at is None
        assert task.status == "todo"  # Should revert to todo
