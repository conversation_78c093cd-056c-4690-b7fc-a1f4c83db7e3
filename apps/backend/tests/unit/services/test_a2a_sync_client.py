"""
Unit tests for A2A sync client.
"""

from unittest.mock import <PERSON>Mock, patch

import httpx
import pytest

from a2a_platform.services.a2a_sync_client import A2ASyncClient


class TestA2ASyncClient:
    @pytest.mark.asyncio
    @patch("httpx.AsyncClient.post")
    async def test_call_specialized_agent(self, mock_post):
        """Test calling a specialized agent with user context."""
        # Setup mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"status": "success"}
        mock_post.return_value = mock_response

        # Create client and call method
        client = A2ASyncClient()
        result = await client.call_specialized_agent(
            endpoint_url="http://example.com/agent",
            payload={"action": "test"},
            user_id="user123",
            initiating_agent_id="agent456",
        )

        # Verify the result
        assert result == {"status": "success"}

        # Verify the request
        mock_post.assert_called_once()
        args, kwargs = mock_post.call_args
        assert args[0] == "http://example.com/agent"
        assert "json" in kwargs
        assert "headers" in kwargs
        assert kwargs["json"]["user_context"]["user_id"] == "user123"
        assert kwargs["json"]["user_context"]["initiating_agent_id"] == "agent456"
        assert kwargs["json"]["payload"]["action"] == "test"

    @pytest.mark.asyncio
    @patch("httpx.AsyncClient.post")
    async def test_call_specialized_agent_with_headers(self, mock_post):
        """Test calling a specialized agent with custom headers."""
        # Setup mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"status": "success"}
        mock_post.return_value = mock_response

        # Create client and call method with custom headers
        client = A2ASyncClient()
        result = await client.call_specialized_agent(
            endpoint_url="http://example.com/agent",
            payload={"action": "test"},
            user_id="user123",
            initiating_agent_id="agent456",
            headers={"X-Custom-Header": "value"},
        )

        # Verify the result
        assert result == {"status": "success"}

        # Verify the request headers
        mock_post.assert_called_once()
        args, kwargs = mock_post.call_args
        assert kwargs["headers"]["X-Custom-Header"] == "value"
        assert kwargs["headers"]["Content-Type"] == "application/json"

    @pytest.mark.asyncio
    @patch("httpx.AsyncClient.post")
    async def test_call_specialized_agent_error(self, mock_post):
        """Test calling a specialized agent when an error occurs."""
        # Setup mock response to raise an error
        mock_post.side_effect = httpx.HTTPError("Error")

        # Create client and call method
        client = A2ASyncClient()

        # Verify that the error is propagated
        with pytest.raises(httpx.HTTPError):
            await client.call_specialized_agent(
                endpoint_url="http://example.com/agent",
                payload={"action": "test"},
                user_id="user123",
                initiating_agent_id="agent456",
            )
