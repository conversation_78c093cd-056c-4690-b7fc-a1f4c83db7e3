import uuid
from unittest.mock import AsyncMock

import pytest

from a2a_platform.db.models.user import User
from a2a_platform.services.user_service import delete_all_user_data


@pytest.mark.asyncio
async def test_delete_all_user_data_user_not_found(mock_db_session: AsyncMock):
    """Test deleting user data when the user is not found"""
    # Arrange
    clerk_user_id = "nonexistent_clerk_id"

    # Configure the mock to return None for get_user_by_clerk_id
    mock_db_session.execute.return_value.scalar_one_or_none.return_value = None

    # Act
    result = await delete_all_user_data(mock_db_session, clerk_user_id)

    # Assert
    assert result is False
    # Verify that no delete operations were performed
    assert mock_db_session.execute.call_count == 2
    mock_db_session.commit.assert_not_called()


@pytest.mark.asyncio
async def test_delete_all_user_data_success(mock_db_session: AsyncMock):
    """Test successful deletion of all user data"""
    # Arrange
    clerk_user_id = "test_clerk_id"
    user_id = uuid.uuid4()

    # Create a mock user that will be returned by get_user_by_clerk_id
    mock_user = User(
        id=user_id,
        clerk_user_id=clerk_user_id,
        email="<EMAIL>",
        preferences={},
    )

    # Configure the mock to return the mock_user for get_user_by_clerk_id
    mock_db_session.execute.return_value.scalar_one_or_none.return_value = mock_user

    # Act
    result = await delete_all_user_data(mock_db_session, clerk_user_id)

    # Assert
    assert result is True

    # Verify that delete operations were performed for all related tables
    # The exact number of execute calls depends on the number of tables with user data
    # We expect at least 9 calls (1 for get_user_by_clerk_id + 8 for deleting from tables)
    assert mock_db_session.execute.call_count >= 9

    # Verify that the transaction was committed
    mock_db_session.commit.assert_called_once()


@pytest.mark.asyncio
async def test_delete_all_user_data_with_error(mock_db_session: AsyncMock):
    """Test handling of errors during user data deletion"""
    # Arrange
    clerk_user_id = "test_clerk_id"
    user_id = uuid.uuid4()

    # Create a mock user that will be returned by get_user_by_clerk_id
    mock_user = User(
        id=user_id,
        clerk_user_id=clerk_user_id,
        email="<EMAIL>",
        preferences={},
    )

    # Configure the mock to return the mock_user for get_user_by_clerk_id
    # Patch execute so first call returns an object whose scalar_one_or_none returns mock_user, then raise exception
    class ScalarResult:
        def scalar_one_or_none(self):
            return mock_user

    def execute_side_effect(*args, **kwargs):
        if not hasattr(execute_side_effect, "called"):
            execute_side_effect.called = True
            return ScalarResult()
        raise Exception("Database error during deletion")

    mock_db_session.execute.side_effect = execute_side_effect

    # Act & Assert
    with pytest.raises(Exception) as excinfo:
        await delete_all_user_data(mock_db_session, clerk_user_id)

    assert "Database error during deletion" in str(excinfo.value)

    # Verify that the transaction was rolled back
    mock_db_session.rollback.assert_called_once()
    mock_db_session.commit.assert_not_called()


@pytest.mark.asyncio
async def test_delete_related_records(mock_db_session: AsyncMock):
    """Test the _delete_related_records helper function"""
    # Import the function directly for testing
    from a2a_platform.services.user_service import _delete_related_records

    # Arrange
    # Use a table name from the whitelist
    table_name = "assistants"  # This is in our whitelist
    user_id_column = "user_id"  # This matches the expected column for this table
    user_id = uuid.uuid4()
    friendly_name = "assistants"

    # Configure the mock to return a result with rowcount
    mock_result = AsyncMock()
    mock_result.rowcount = 5
    mock_db_session.execute.return_value = mock_result

    # Act
    await _delete_related_records(
        mock_db_session, table_name, user_id_column, user_id, friendly_name
    )

    # Assert
    mock_db_session.execute.assert_called_once()

    # Verify that the correct statement was executed
    # With our new implementation, we're using SQLAlchemy Core instead of raw SQL text
    # So we need to check that the statement was constructed correctly
    call_args = mock_db_session.execute.call_args
    stmt = call_args[0][0]

    # Check that we're using the correct table name
    assert stmt.table.name == table_name

    # Check that we're using the correct user_id value
    # The exact assertion depends on how SQLAlchemy Core constructs the statement
    # But we can verify the execute was called with the correct parameters
    assert mock_db_session.execute.called
