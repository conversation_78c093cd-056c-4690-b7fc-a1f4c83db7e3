import uuid
from unittest.mock import AsyncMock

import pytest
from sqlalchemy.exc import IntegrityError

from a2a_platform.db.models.user import User
from a2a_platform.schemas.clerk_events import UserCreatedData
from a2a_platform.schemas.user import UserCreate
from a2a_platform.services.user_service import (
    create_user,
    create_user_from_clerk_event,
    get_user_by_clerk_id,
)


@pytest.mark.asyncio
async def test_get_user_by_clerk_id(mock_db_session: AsyncMock):
    # Arrange
    clerk_user_id = "test_clerk_id"
    expected_user = User(
        id=uuid.uuid4(), clerk_user_id=clerk_user_id, email="<EMAIL>"
    )
    mock_db_session.add(expected_user)
    await mock_db_session.commit()

    # Configure the mock for the execute chain
    mock_db_session.execute.return_value.scalar_one_or_none.return_value = expected_user

    # Act
    result = await get_user_by_clerk_id(mock_db_session, clerk_user_id)

    # Assert
    assert result is not None
    assert result.clerk_user_id == clerk_user_id
    mock_db_session.execute.assert_called_once()


@pytest.mark.asyncio
async def test_get_user_by_clerk_id_not_found(mock_db_session: AsyncMock):
    # Configure the mock for the execute chain
    mock_db_session.execute.return_value.scalar_one_or_none.return_value = None

    # Act
    result = await get_user_by_clerk_id(mock_db_session, "non_existent_id")

    # Assert
    assert result is None
    assert mock_db_session.execute.call_count == 2


@pytest.mark.asyncio
async def test_create_user(mock_db_session: AsyncMock):
    # Arrange
    user_data = UserCreate(clerk_user_id="new_clerk_id", email="<EMAIL>")

    # Create a mock user that will be returned after refresh
    mock_user = User(
        id=uuid.uuid4(),
        clerk_user_id="new_clerk_id",
        email="<EMAIL>",
        preferences={},
    )

    # Mock the refresh method to set the user
    async def mock_refresh(user):
        # Copy attributes from our mock user to the passed user
        for attr in ["id", "clerk_user_id", "email", "preferences"]:
            setattr(user, attr, getattr(mock_user, attr))
        return None

    mock_db_session.refresh.side_effect = mock_refresh

    # Act
    created_user = await create_user(mock_db_session, user_data)

    # Assert
    mock_db_session.add.assert_called_once()
    added_arg = mock_db_session.add.call_args[0][0]
    assert isinstance(added_arg, User)
    assert added_arg.clerk_user_id == user_data.clerk_user_id
    assert added_arg.email == user_data.email

    mock_db_session.commit.assert_called_once()
    mock_db_session.refresh.assert_called_once_with(added_arg)

    assert created_user is not None
    assert created_user.clerk_user_id == "new_clerk_id"
    assert created_user.email == "<EMAIL>"


@pytest.mark.asyncio
async def test_create_user_from_clerk_event(mock_db_session: AsyncMock):
    # Arrange
    clerk_event_data = {
        "id": "clerk_event_user_id",
        "email_addresses": [
            {"id": "email_id_1", "email_address": "<EMAIL>"}
        ],
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
    }

    # Mock user to be returned by create_user (which is called internally)
    expected_user = User(
        id=uuid.uuid4(),
        clerk_user_id="clerk_event_user_id",
        email="<EMAIL>",
        preferences={},
    )

    # First call to get_user_by_clerk_id should return None (user doesn't exist)
    # Second call should return the user (for idempotency check)
    mock_db_session.execute.return_value.scalar_one_or_none.side_effect = [
        None,  # For the first call to create_user_from_clerk_event -> get_user_by_clerk_id (exact match)
        None,  # For the first call to create_user_from_clerk_event -> get_user_by_clerk_id (case-insensitive match)
        expected_user,  # For the second call to create_user_from_clerk_event -> get_user_by_clerk_id (exact match for idempotency)
        expected_user,  # For the second call to create_user_from_clerk_event -> get_user_by_clerk_id (case-insensitive match for idempotency, though likely not reached if exact match works)
    ]

    # Mock the refresh method to set the user
    async def mock_refresh(user):
        # Copy attributes from our mock user to the passed user
        for attr in ["id", "clerk_user_id", "email", "preferences"]:
            setattr(user, attr, getattr(expected_user, attr))
        return None

    mock_db_session.refresh.side_effect = mock_refresh

    model = UserCreatedData(**clerk_event_data)
    # Act
    result = await create_user_from_clerk_event(mock_db_session, model)

    # Assert
    assert result is not None
    assert result.clerk_user_id == "clerk_event_user_id"
    assert result.email == "<EMAIL>"
    mock_db_session.add.assert_called_once()
    added_arg = mock_db_session.add.call_args[0][0]
    assert isinstance(added_arg, User)
    assert added_arg.clerk_user_id == "clerk_event_user_id"
    mock_db_session.commit.assert_called_once()
    mock_db_session.refresh.assert_called_once_with(added_arg)

    # Verify idempotency
    second_result = await create_user_from_clerk_event(mock_db_session, model)
    assert second_result is not None
    assert second_result.clerk_user_id == expected_user.clerk_user_id


@pytest.mark.asyncio
async def test_create_user_from_clerk_event_integrity_error(
    mock_db_session: AsyncMock,
):
    # Arrange
    clerk_event_data = {
        "id": "clerk_event_user_id",
        "email_addresses": [
            {"id": "email_id_1", "email_address": "<EMAIL>"}
        ],
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
    }
    existing_user_for_second_call = User(
        id=uuid.uuid4(),
        clerk_user_id="clerk_event_user_id_integrity",
        email="<EMAIL>",
    )

    # We don't need to create a mock_user here since we're using existing_user_for_second_call

    # First call to get_user_by_clerk_id should return None (user doesn't exist)
    # After rollback, second call should return the existing_user_for_second_call (race condition simulation)
    mock_db_session.execute.return_value.scalar_one_or_none.side_effect = [
        None,  # For the first call to get_user_by_clerk_id (exact match)
        None,  # For the first call to get_user_by_clerk_id (case-insensitive match)
        existing_user_for_second_call,  # For the second call to get_user_by_clerk_id (exact match)
        existing_user_for_second_call,  # For the second call to get_user_by_clerk_id (case-insensitive match, though likely not reached)
    ]

    # Mock commit to raise IntegrityError (simulating race condition)
    mock_db_session.commit.side_effect = IntegrityError(
        "Duplicate key value", None, None
    )

    model = UserCreatedData(**clerk_event_data)
    # Act
    result = await create_user_from_clerk_event(mock_db_session, model)

    # Assert
    assert result is not None
    assert result.clerk_user_id == "clerk_event_user_id_integrity"
    mock_db_session.rollback.assert_called_once()
    mock_db_session.add.assert_called_once()


@pytest.mark.asyncio
async def test_create_user_from_clerk_event_general_error(
    mock_db_session: AsyncMock,
):
    # Arrange
    clerk_event_data = {
        "id": "clerk_event_user_id",
        "email_addresses": [
            {"id": "email_id_1", "email_address": "<EMAIL>"}
        ],
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
    }

    mock_db_session.execute.return_value.scalar_one_or_none.return_value = None
    mock_db_session.commit.side_effect = Exception("Unexpected error")

    model = UserCreatedData(**clerk_event_data)
    # Act
    result = await create_user_from_clerk_event(mock_db_session, model)

    # Assert
    assert result is None
    mock_db_session.rollback.assert_called_once()
    mock_db_session.add.assert_called_once()
