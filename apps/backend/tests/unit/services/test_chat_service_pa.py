"""
Unit tests for ChatService PA message functionality.

Tests the send_pa_message method for creating Personal Assistant messages
with proper validation, content structure, and error handling.
"""

import uuid
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.chat_message import ChatMessage
from a2a_platform.db.models.conversation import Conversation
from a2a_platform.services.chat_service import ChatService


class TestChatServicePAMessages:
    """Tests for ChatService PA message functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session."""
        return AsyncMock(spec=AsyncSession)

    @pytest.fixture
    def chat_service(self, mock_db_session):
        """Create a ChatService instance with mocked database session."""
        return ChatService(mock_db_session)

    @pytest.fixture
    def sample_conversation_id(self):
        """Sample conversation ID for testing."""
        return uuid.uuid4()

    @pytest.fixture
    def sample_conversation(self, sample_conversation_id):
        """Sample conversation model for testing."""
        conversation = MagicMock(spec=Conversation)
        conversation.id = sample_conversation_id
        conversation.user_id = uuid.uuid4()
        conversation.assistant_id = uuid.uuid4()
        return conversation

    @pytest.mark.asyncio
    async def test_send_pa_message_success(
        self, chat_service, mock_db_session, sample_conversation_id, sample_conversation
    ):
        """Test successful PA message creation."""
        # Arrange
        content = "Hello! I'm your Personal Assistant. How can I help you today?"
        metadata = {"source": "ai_service", "model": "gpt-4"}

        # Mock database queries
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_conversation
        mock_db_session.execute.return_value = mock_result

        # Mock message creation
        expected_message = MagicMock(spec=ChatMessage)
        expected_message.id = uuid.uuid4()
        expected_message.conversation_id = sample_conversation_id
        expected_message.sender_role = "agent"
        expected_message.content = {"parts": [{"type": "text", "content": content}]}
        expected_message.message_metadata = metadata
        expected_message.timestamp = datetime.now()

        mock_db_session.refresh = AsyncMock()

        # Act
        result = await chat_service.send_pa_message(
            conversation_id=sample_conversation_id,
            content=content,
            metadata=metadata,
        )

        # Assert
        assert result is not None  # Verify result is returned
        mock_db_session.execute.assert_called_once()
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
        mock_db_session.refresh.assert_called_once()

        # Verify the message was created with correct structure
        added_message = mock_db_session.add.call_args[0][0]
        assert added_message.conversation_id == sample_conversation_id
        assert added_message.sender_role == "agent"
        assert added_message.content == {
            "parts": [{"type": "text", "content": content}]
        }
        assert added_message.message_metadata == metadata

    @pytest.mark.asyncio
    async def test_send_pa_message_empty_content(
        self, chat_service, sample_conversation_id
    ):
        """Test PA message creation with empty content."""
        # Act & Assert
        with pytest.raises(ValueError, match="PA message content cannot be empty"):
            await chat_service.send_pa_message(
                conversation_id=sample_conversation_id,
                content="",
            )

        with pytest.raises(ValueError, match="PA message content cannot be empty"):
            await chat_service.send_pa_message(
                conversation_id=sample_conversation_id,
                content="   ",  # Only whitespace
            )

    @pytest.mark.asyncio
    async def test_send_pa_message_content_too_long(
        self, chat_service, sample_conversation_id
    ):
        """Test PA message creation with content exceeding character limit."""
        # Arrange
        long_content = "x" * 4001  # Exceeds 4000 character limit

        # Act & Assert
        with pytest.raises(
            ValueError, match="PA message content cannot exceed 4000 characters"
        ):
            await chat_service.send_pa_message(
                conversation_id=sample_conversation_id,
                content=long_content,
            )

    @pytest.mark.asyncio
    async def test_send_pa_message_conversation_not_found(
        self, chat_service, mock_db_session, sample_conversation_id
    ):
        """Test PA message creation when conversation doesn't exist."""
        # Arrange
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None  # Conversation not found
        mock_db_session.execute.return_value = mock_result

        # Act & Assert
        with pytest.raises(
            ValueError, match=f"Conversation with ID {sample_conversation_id} not found"
        ):
            await chat_service.send_pa_message(
                conversation_id=sample_conversation_id,
                content="Hello from PA",
            )

    @pytest.mark.asyncio
    async def test_send_pa_message_default_metadata(
        self, chat_service, mock_db_session, sample_conversation_id, sample_conversation
    ):
        """Test PA message creation with default metadata."""
        # Arrange
        content = "Hello from PA"

        # Mock database queries
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_conversation
        mock_db_session.execute.return_value = mock_result

        # Act
        await chat_service.send_pa_message(
            conversation_id=sample_conversation_id,
            content=content,
        )

        # Assert
        added_message = mock_db_session.add.call_args[0][0]
        assert added_message.message_metadata == {}

    @pytest.mark.asyncio
    async def test_send_pa_message_database_error(
        self, chat_service, mock_db_session, sample_conversation_id, sample_conversation
    ):
        """Test PA message creation with database error."""
        # Arrange
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_conversation
        mock_db_session.execute.return_value = mock_result
        mock_db_session.commit.side_effect = Exception("Database error")

        # Act & Assert
        with pytest.raises(
            ValueError, match="Failed to send PA message: Database error"
        ):
            await chat_service.send_pa_message(
                conversation_id=sample_conversation_id,
                content="Hello from PA",
            )

        # Verify rollback was called
        mock_db_session.rollback.assert_called_once()

    @pytest.mark.asyncio
    async def test_send_pa_message_content_structure(
        self, chat_service, mock_db_session, sample_conversation_id, sample_conversation
    ):
        """Test that PA messages use the correct structured content format."""
        # Arrange
        content = "This is a test message from the PA"

        # Mock database queries
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_conversation
        mock_db_session.execute.return_value = mock_result

        # Act
        await chat_service.send_pa_message(
            conversation_id=sample_conversation_id,
            content=content,
        )

        # Assert
        added_message = mock_db_session.add.call_args[0][0]
        expected_content = {"parts": [{"type": "text", "content": content}]}
        assert added_message.content == expected_content
        assert added_message.sender_role == "agent"

    @pytest.mark.asyncio
    async def test_send_pa_message_updates_conversation_timestamp(
        self, chat_service, mock_db_session, sample_conversation_id, sample_conversation
    ):
        """Test that sending PA message updates conversation's last_message_at."""
        # Arrange
        original_timestamp = sample_conversation.last_message_at

        # Mock database queries
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_conversation
        mock_db_session.execute.return_value = mock_result

        # Act
        await chat_service.send_pa_message(
            conversation_id=sample_conversation_id,
            content="Hello from PA",
        )

        # Assert
        # The conversation's last_message_at should be updated
        assert sample_conversation.last_message_at != original_timestamp
