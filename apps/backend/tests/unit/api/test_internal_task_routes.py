import uuid
from datetime import UTC, datetime
from unittest.mock import AsyncMock, MagicMock

import pytest
from fastapi import HTT<PERSON>Exception

from a2a_platform.api.rest.routes.internal_task_routes import (
    cancel_task,
    create_task,
    get_task,
    list_tasks,
    update_task,
)
from a2a_platform.db.models.task import Task
from a2a_platform.schemas.task_schemas import TaskCreate, TaskUpdate


class TestInternalTaskRoutes:
    """Tests for the internal task routes."""

    @pytest.fixture
    def mock_task_service(self):
        """Creates a mock task service for testing."""
        mock_service = MagicMock()

        # Setup async methods with proper awaitable returns
        mock_service.create_task = AsyncMock()
        mock_service.update_task = AsyncMock()
        mock_service.cancel_task = AsyncMock()
        mock_service.get_task_by_id = AsyncMock()
        mock_service.get_tasks_for_assistant = AsyncMock()
        mock_service.update_dependent_tasks = AsyncMock()
        mock_service.get_task_by_idempotency_key = AsyncMock()

        return mock_service

    @pytest.fixture
    def test_task(self):
        """Creates a test task for tests."""
        now = datetime.now(UTC)
        return Task(
            id=uuid.uuid4(),
            assistant_id=uuid.uuid4(),
            description="Test task",
            status="todo",
            created_at=now,
            updated_at=now,
            retry_count=0,
            metadata_json={"test": "metadata"},
        )

    async def test_create_task_success(self, mock_task_service, test_task):
        """Test successful task creation."""
        # Arrange
        # Make sure the mock task has all required attributes for TaskRead validation
        test_task.__dict__ = {
            "id": test_task.id,
            "assistant_id": test_task.assistant_id,
            "description": "Test task",
            "status": "todo",
            "retry_count": 0,
            "created_at": datetime.now(UTC),
            "updated_at": datetime.now(UTC),
            "metadata_json": {"test": "metadata"},
            "objective_id": None,
            "parent_task_id": None,
            "depends_on_task_id": None,
            "completed_at": None,
            "last_progress_at": None,
            "idempotency_key": None,
            "lease_owner_id": None,
            "lease_acquired_at": None,
            "lease_expires_at": None,
        }

        # Set up the mock to return the test task
        mock_task_service.create_task.return_value = test_task
        mock_task_service.get_task_by_idempotency_key.return_value = None

        task_data = TaskCreate(description="Test task")
        assistant_id = uuid.uuid4()
        idempotency_key = "test-key"

        # Act
        result = await create_task(
            task_data=task_data,
            assistant_id=assistant_id,
            task_service=mock_task_service,
            x_idempotency_key=idempotency_key,
        )

        # Assert
        mock_task_service.create_task.assert_called_once_with(
            assistant_id=assistant_id,
            task_data=task_data,
            idempotency_key=idempotency_key,
        )
        # Check ID and assistant_id to verify it's the right task
        assert str(result.id) == str(test_task.id)
        assert str(result.assistant_id) == str(test_task.assistant_id)

    async def test_create_task_failure(self, mock_task_service):
        """Test task creation failure."""
        # Arrange
        mock_task_service.create_task.side_effect = ValueError("Test error")
        task_data = TaskCreate(description="Test task")
        assistant_id = uuid.uuid4()

        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            await create_task(
                task_data=task_data,
                assistant_id=assistant_id,
                task_service=mock_task_service,
                x_idempotency_key=None,
            )

        assert exc_info.value.status_code == 400
        assert "Test error" in exc_info.value.detail

    async def test_update_task_success(self, mock_task_service, test_task):
        """Test successful task update."""
        # Arrange
        task_id = test_task.id
        mock_task_service.update_task.return_value = test_task
        update_data = TaskUpdate(description="Updated description")

        # Act
        result = await update_task(
            task_id=task_id, update_data=update_data, task_service=mock_task_service
        )

        # Assert
        mock_task_service.update_task.assert_called_once_with(
            task_id=task_id, update_data=update_data
        )
        # Check ID and assistant_id to verify it's the right task
        assert str(result.id) == str(test_task.id)
        assert str(result.assistant_id) == str(test_task.assistant_id)

    async def test_update_task_not_found(self, mock_task_service):
        """Test task update when task not found."""
        # Arrange
        task_id = uuid.uuid4()
        mock_task_service.update_task.return_value = None
        update_data = TaskUpdate(description="Updated description")

        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            await update_task(
                task_id=task_id, update_data=update_data, task_service=mock_task_service
            )

        assert exc_info.value.status_code == 404
        assert str(task_id) in exc_info.value.detail

    async def test_update_task_completed_updates_dependencies(
        self, mock_task_service, test_task
    ):
        """Test that completing a task calls the service update method."""
        # Arrange
        task_id = test_task.id
        mock_task_service.update_task.return_value = test_task
        update_data = TaskUpdate(status="done")

        # Act
        await update_task(
            task_id=task_id, update_data=update_data, task_service=mock_task_service
        )

        # Assert - The service layer handles dependency updates internally
        mock_task_service.update_task.assert_called_once_with(
            task_id=task_id, update_data=update_data
        )

    async def test_cancel_task_success(self, mock_task_service, test_task):
        """Test successful task cancellation."""
        # Arrange
        task_id = test_task.id
        mock_task_service.cancel_task.return_value = test_task

        # Act
        result = await cancel_task(task_id=task_id, task_service=mock_task_service)

        # Assert
        mock_task_service.cancel_task.assert_called_once_with(task_id=task_id)
        # Check ID and assistant_id to verify it's the right task
        assert str(result.id) == str(test_task.id)
        assert str(result.assistant_id) == str(test_task.assistant_id)

    async def test_cancel_task_not_found(self, mock_task_service):
        """Test task cancellation when task not found."""
        # Arrange
        task_id = uuid.uuid4()
        mock_task_service.cancel_task.return_value = None

        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            await cancel_task(task_id=task_id, task_service=mock_task_service)

        assert exc_info.value.status_code == 404
        assert str(task_id) in exc_info.value.detail

    async def test_get_task_success(self, mock_task_service, test_task):
        """Test successful task retrieval."""
        # Arrange
        task_id = test_task.id
        mock_task_service.get_task_by_id.return_value = test_task

        # Act
        result = await get_task(task_id=task_id, task_service=mock_task_service)

        # Assert
        mock_task_service.get_task_by_id.assert_called_once_with(task_id=task_id)
        # Check ID and assistant_id to verify it's the right task
        assert str(result.id) == str(test_task.id)
        assert str(result.assistant_id) == str(test_task.assistant_id)

    async def test_get_task_not_found(self, mock_task_service):
        """Test task retrieval when task not found."""
        # Arrange
        task_id = uuid.uuid4()
        mock_task_service.get_task_by_id.return_value = None

        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            await get_task(task_id=task_id, task_service=mock_task_service)

        assert exc_info.value.status_code == 404
        assert str(task_id) in exc_info.value.detail

    async def test_list_tasks_success(self, mock_task_service, test_task):
        """Test successful task listing."""
        # Arrange
        assistant_id = uuid.uuid4()

        # Set up the mock to return our test task
        mock_task_service.get_tasks_for_assistant.return_value = ([test_task], 1)

        # Act
        result = await list_tasks(
            assistant_id=assistant_id,
            status=None,
            objective_id=None,
            parent_task_id=None,
            skip=0,
            limit=100,
            task_service=mock_task_service,
        )

        # Assert
        mock_task_service.get_tasks_for_assistant.assert_called_once_with(
            assistant_id=assistant_id,
            status=None,
            objective_id=None,
            parent_task_id=None,
            skip=0,
            limit=100,
        )

        # Compare the IDs rather than the full objects due to metadata handling differences
        assert len(result.items) == 1
        assert str(result.items[0].id) == str(test_task.id)
        assert result.total == 1
