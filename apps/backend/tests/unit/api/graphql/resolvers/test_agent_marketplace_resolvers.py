"""
Unit tests for agent marketplace GraphQL resolvers.
"""

from unittest.mock import MagicMock, patch

import pytest
from strawberry.types import Info

from a2a_platform.api.graphql.resolvers.agent_marketplace_resolvers import (
    _convert_db_agent_to_graphql,
    resolve_get_marketplace_agent,
    resolve_list_marketplace_agents,
    resolve_register_marketplace_agent,
    resolve_update_agent_review_status,
    resolve_update_marketplace_agent,
)
from a2a_platform.api.graphql.schemas.agent_marketplace_schemas import (
    AgentPricingInfoInput,
    AgentSkillInput,
    RegisterMarketplaceAgentInput,
    UpdateMarketplaceAgentInput,
)
from a2a_platform.db.enums import AgentReviewStatus, AgentStatus
from a2a_platform.db.models.registered_agent import (
    RegisteredAgent as RegisteredAgentModel,
)
from a2a_platform.services.agent_service import AgentService


@pytest.fixture
def mock_info():
    """Create a mock GraphQL Info object with context."""
    info = MagicMock(spec=Info)
    info.context = MagicMock()
    info.context.clerk_user_id = "test_user_id"
    info.context.db_session = MagicMock()
    return info


@pytest.fixture
def mock_db_agent():
    """Create a mock database agent model."""
    agent = MagicMock(spec=RegisteredAgentModel)
    agent.agent_definition_id = "test_agent_v1"
    agent.name = "Test Agent"
    agent.description = "A test agent"
    agent.version = "1.0.0"
    agent.developer_id = "dev123"
    agent.pricing_info = {"type": "subscription", "amount_monthly": 999}
    agent.review_status = "approved"
    agent.status = "active"
    agent.skills = [
        {"name": "skill1", "description": "Skill 1 description"},
        {"name": "skill2", "description": "Skill 2 description"},
    ]
    agent.created_at = MagicMock()
    agent.created_at.isoformat.return_value = "2023-01-01T00:00:00Z"
    agent.updated_at = MagicMock()
    agent.updated_at.isoformat.return_value = "2023-01-01T00:00:00Z"
    return agent


class TestAgentMarketplaceResolvers:
    def test_convert_db_agent_to_graphql(self, mock_db_agent):
        """Test converting a database agent model to a GraphQL type."""
        graphql_agent = _convert_db_agent_to_graphql(mock_db_agent)
        assert graphql_agent.agent_definition_id == mock_db_agent.agent_definition_id
        assert graphql_agent.name == mock_db_agent.name
        assert graphql_agent.description == mock_db_agent.description
        assert graphql_agent.version == mock_db_agent.version
        assert graphql_agent.developer_id == mock_db_agent.developer_id
        assert graphql_agent.review_status == AgentReviewStatus(
            mock_db_agent.review_status
        )
        assert graphql_agent.status == AgentStatus(mock_db_agent.status)
        assert len(graphql_agent.skills) == 2
        assert graphql_agent.skills[0].name == "skill1"
        assert graphql_agent.skills[1].name == "skill2"
        assert graphql_agent.created_at == "2023-01-01T00:00:00Z"
        assert graphql_agent.updated_at == "2023-01-01T00:00:00Z"
        assert graphql_agent.pricing_info is not None
        assert graphql_agent.pricing_info.pricing_type == "subscription"
        assert graphql_agent.pricing_info.amount_monthly == 999

    @pytest.mark.asyncio
    @patch.object(AgentService, "list_agents")
    async def test_resolve_list_marketplace_agents(
        self, mock_list_agents, mock_info, mock_db_agent
    ):
        """Test resolving the listMarketplaceAgents query."""
        # Set up the mock
        mock_list_agents.return_value = ([mock_db_agent], 1)

        # Call the resolver
        result = await resolve_list_marketplace_agents(mock_info)

        # Verify the result
        assert len(result) == 1
        assert result[0].agent_definition_id == mock_db_agent.agent_definition_id
        assert result[0].name == mock_db_agent.name

        # Verify the service was called correctly
        mock_list_agents.assert_called_once_with(status=None)

    @pytest.mark.asyncio
    @patch.object(AgentService, "get_agent_by_definition_id")
    async def test_resolve_get_marketplace_agent(
        self, mock_get_agent, mock_info, mock_db_agent
    ):
        """Test resolving the getMarketplaceAgent query."""
        # Set up the mock
        mock_get_agent.return_value = mock_db_agent

        # Call the resolver
        result = await resolve_get_marketplace_agent(mock_info, "test_agent_v1")

        # Verify the result
        assert result.agent_definition_id == mock_db_agent.agent_definition_id
        assert result.name == mock_db_agent.name

        # Verify the service was called correctly
        mock_get_agent.assert_called_once_with("test_agent_v1")

    @pytest.mark.asyncio
    @patch.object(AgentService, "register_agent")
    async def test_resolve_register_marketplace_agent(
        self, mock_register_agent, mock_info, mock_db_agent
    ):
        """Test resolving the registerMarketplaceAgent mutation."""
        # Set up the mock
        mock_register_agent.return_value = mock_db_agent

        # Create input data
        input_data = RegisterMarketplaceAgentInput(
            agent_definition_id="test_agent_v1",
            name="Test Agent",
            description="A test agent",
            version="1.0.0",
            developer_id="dev123",
            pricing_info=AgentPricingInfoInput(
                pricing_type="subscription",
                amount_monthly=999,
            ),
            skills=[
                AgentSkillInput(name="skill1", description="Skill 1 description"),
                AgentSkillInput(name="skill2", description="Skill 2 description"),
            ],
        )

        # Call the resolver
        result = await resolve_register_marketplace_agent(mock_info, input_data)

        # Verify the result
        assert result.agent_definition_id == mock_db_agent.agent_definition_id
        assert result.name == mock_db_agent.name

        # Verify the service was called correctly
        mock_register_agent.assert_called_once()

    @pytest.mark.asyncio
    @patch.object(AgentService, "get_agent_by_definition_id")
    @patch.object(AgentService, "update_agent")
    async def test_resolve_update_marketplace_agent(
        self, mock_update_agent, mock_get_agent, mock_info, mock_db_agent
    ):
        """Test resolving the updateMarketplaceAgent mutation."""
        # Set up the mocks
        mock_db_agent.developer_id = "test_user_id"  # Match the user ID in mock_info
        mock_get_agent.return_value = mock_db_agent
        mock_update_agent.return_value = mock_db_agent

        # Create input data
        input_data = UpdateMarketplaceAgentInput(
            agent_definition_id="test_agent_v1",
            name="Updated Agent",
            pricing_info=AgentPricingInfoInput(
                pricing_type="per_call",
                amount_per_call=1,
            ),
        )

        # Call the resolver
        result = await resolve_update_marketplace_agent(mock_info, input_data)

        # Verify the result
        assert result.agent_definition_id == mock_db_agent.agent_definition_id
        assert result.name == mock_db_agent.name

        # Verify the services were called correctly
        mock_get_agent.assert_called_once_with("test_agent_v1")
        mock_update_agent.assert_called_once()

    @pytest.mark.asyncio
    @patch.object(AgentService, "get_agent_by_definition_id")
    @patch.object(AgentService, "update_agent")
    async def test_resolve_update_agent_review_status(
        self, mock_update_agent, mock_get_agent, mock_info, mock_db_agent
    ):
        """Test resolving the updateAgentReviewStatus mutation."""
        # Set up the mocks
        mock_get_agent.return_value = mock_db_agent
        mock_update_agent.return_value = mock_db_agent

        # Call the resolver
        result = await resolve_update_agent_review_status(
            mock_info, "test_agent_v1", AgentReviewStatus.APPROVED
        )

        # Verify the result
        assert result.agent_definition_id == mock_db_agent.agent_definition_id
        assert result.name == mock_db_agent.name

        # Verify the services were called correctly
        mock_get_agent.assert_called_once_with("test_agent_v1")
        mock_update_agent.assert_called_once()

    # Note: Invalid status test removed because enum validation
    # is now handled by Strawberry GraphQL automatically
