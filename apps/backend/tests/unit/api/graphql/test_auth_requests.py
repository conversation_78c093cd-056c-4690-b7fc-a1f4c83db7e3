"""
Unit tests demonstrating how to make authenticated GraphQL requests.

This module provides examples and tests for:
1. Creating mock authenticated contexts
2. Making authenticated GraphQL mutations
3. Testing authentication failures
4. Verifying CLI tokens
"""

import secrets
import uuid
from datetime import UTC, datetime
from unittest.mock import AsyncMock, MagicMock, patch

import httpx
import pytest
from fastapi import FastAPI

from a2a_platform.api.graphql import graphql_app
from a2a_platform.api.graphql.middleware.auth_middleware import GraphQLContext
from a2a_platform.api.graphql.resolvers.user_profile_resolvers import CLI_TOKEN_PREFIX
from a2a_platform.api.graphql.schemas.user_profile_schemas import CreateCliTokenInput


@pytest.fixture
def app():
    """Create a FastAPI application with the GraphQL router mounted."""
    app = FastAPI()
    app.include_router(graphql_app, prefix="/graphql")
    return app


@pytest.mark.asyncio
async def test_authenticated_direct_call():
    """
    Test making an authenticated GraphQL call by directly calling a resolver.

    This demonstrates how to create a mock authenticated context for testing.
    This approach is useful for unit testing resolvers without HTTP overhead.
    """
    from a2a_platform.api.graphql.resolvers.user_profile_resolvers import (
        resolve_create_cli_token,
    )

    # 1. Create a mock authenticated context with a clerk_user_id
    mock_clerk_user_id = f"test_user_{uuid.uuid4()}"
    mock_context = MagicMock(spec=GraphQLContext)
    mock_context.clerk_user_id = mock_clerk_user_id  # Set the authenticated user ID

    # Fully mock db_session for direct resolver call
    mock_db_session_for_direct_call = (
        AsyncMock()
    )  # Session itself is async for commit/refresh
    mock_db_session_for_direct_call.add = MagicMock()  # .add() is synchronous
    mock_db_session_for_direct_call.commit = AsyncMock()
    mock_db_session_for_direct_call.refresh = AsyncMock()
    mock_context.db_session = mock_db_session_for_direct_call

    # 2. Create mock GraphQL info object with our authenticated context
    mock_info = MagicMock()
    mock_info.context = mock_context

    # 3. Create test input for the mutation
    test_input = CreateCliTokenInput(description="Test Token")

    # 4. Mock the user service to return a mock user
    mock_user_id = uuid.uuid4()
    mock_db_user = MagicMock()
    mock_db_user.id = mock_user_id
    mock_db_user.clerk_user_id = mock_clerk_user_id

    # 5. Test the mutation with our authenticated context
    with (
        patch(
            "a2a_platform.api.graphql.resolvers.user_profile_resolvers.get_user_by_clerk_id",
            new_callable=AsyncMock,
            return_value=mock_db_user,
        ),
        patch(
            "a2a_platform.api.graphql.resolvers.user_profile_resolvers.CliTokenModel",
            autospec=True,  # Keep autospec to ensure it's a mock
        ) as MockCliTokenModelInResolver,
    ):
        # Configure the mock token instance that CliTokenModel() will return
        mock_token_db_instance = MockCliTokenModelInResolver.return_value
        mock_token_db_instance.id = uuid.uuid4()
        mock_token_db_instance.token_prefix = CLI_TOKEN_PREFIX
        mock_token_db_instance.description = test_input.description
        mock_token_db_instance.user_id = mock_user_id
        mock_token_db_instance.created_at = datetime.now(UTC)
        mock_token_db_instance.expires_at = None
        mock_token_db_instance.last_used_at = None

        # Call the resolver directly with authenticated context
        result = await resolve_create_cli_token(mock_info, test_input)

        # Verify authentication worked (the function completed without auth errors)
        assert result is not None
        assert hasattr(result, "token")
        assert hasattr(result, "cliToken")
        assert result.cliToken.tokenPrefix == CLI_TOKEN_PREFIX
        assert result.cliToken.description == test_input.description
        assert str(result.cliToken.userId) == str(mock_user_id)

        # Check that db methods were called on the mocked session
        mock_db_session_for_direct_call.add.assert_called_once_with(
            mock_token_db_instance
        )
        mock_db_session_for_direct_call.commit.assert_awaited_once()
        mock_db_session_for_direct_call.refresh.assert_awaited_once_with(
            mock_token_db_instance
        )


@pytest.mark.asyncio
async def test_authenticated_http_request(app):
    """
    Test making an authenticated GraphQL HTTP request.

    This demonstrates how to include an authentication token in an HTTP request.
    This approach is useful for integration testing the full GraphQL endpoint.
    """
    # 1. Create a mock clerk user ID that will be used in the auth token
    mock_clerk_user_id = f"test_user_{uuid.uuid4()}"

    # 2. Define the GraphQL mutation
    mutation = """
    mutation CreateCliToken($description: String) {
        createCliToken(input: { description: $description }) {
            token
            cliToken {
                id
                tokenPrefix
                description
            }
        }
    }
    """

    # 3. Create variables for the mutation
    variables = {"description": "Test HTTP Auth Token"}

    # 4. Create a mock for ClerkAuthMiddleware.__call__ that returns auth credentials with our user ID
    from fastapi.security.http import HTTPAuthorizationCredentials

    mock_auth_credentials = HTTPAuthorizationCredentials(
        scheme="Bearer", credentials=mock_clerk_user_id
    )

    # 5. Setup mocks for database and user service
    mock_user_id = uuid.uuid4()
    mock_db_user = MagicMock()
    mock_db_user.id = mock_user_id
    mock_db_user.clerk_user_id = mock_clerk_user_id

    # Skip the actual database operations to avoid SQLAlchemy errors
    # Instead, we'll craft a complete response manually
    mock_complete_response = {
        "data": {
            "createCliToken": {
                "token": f"{CLI_TOKEN_PREFIX}12345_{secrets.token_urlsafe(16)}",
                "cliToken": {
                    "id": str(uuid.uuid4()),
                    "tokenPrefix": CLI_TOKEN_PREFIX,
                    "description": variables["description"],
                },
            }
        }
    }

    async with httpx.AsyncClient(
        transport=httpx.ASGITransport(app=app), base_url="http://test"
    ) as ac:
        with (
            patch(
                "a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__",
                new_callable=AsyncMock,
                return_value=mock_auth_credentials,
            ),
            patch(
                "a2a_platform.api.graphql.resolvers.user_profile_resolvers.get_user_by_clerk_id",
                new_callable=AsyncMock,
                return_value=mock_db_user,
            ),
            # Patch the GraphQL execution to return our mocked response
            patch(
                "strawberry.fastapi.GraphQLRouter.run",
                new_callable=AsyncMock,
                return_value=mock_complete_response,
            ),
        ):
            response = await ac.post(
                "/graphql",
                json={"query": mutation, "variables": variables},
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {mock_clerk_user_id}",
                },
            )

    assert response.status_code == 200, f"Response content: {response.text}"
    result = response.json()
    assert "errors" not in result, f"GraphQL errors: {result.get('errors')}"
    assert "data" in result
    data = result.get("data", {})
    assert "createCliToken" in data
    cli_token_data = data.get("createCliToken", {})
    assert "token" in cli_token_data
    assert cli_token_data.get("token", "").startswith(CLI_TOKEN_PREFIX)
    assert "cliToken" in cli_token_data
    assert "id" in cli_token_data.get("cliToken", {})
    assert "tokenPrefix" in cli_token_data.get("cliToken", {})
    assert "description" in cli_token_data.get("cliToken", {})
    assert (
        cli_token_data.get("cliToken", {}).get("description")
        == variables["description"]
    )


@pytest.mark.asyncio
async def test_unauthenticated_request_fails(app):
    """
    Test that an unauthenticated request fails with authentication error.

    This demonstrates the expected behavior when auth is missing.
    """
    # 1. Define the GraphQL mutation requiring authentication
    mutation = """
    mutation CreateCliToken($description: String) {
        createCliToken(input: { description: $description }) {
            token
            cliToken {
                id
                tokenPrefix
                description
            }
        }
    }
    """

    # 2. Create variables for the mutation
    variables = {"description": "Unauthenticated Test Token"}

    # 3. Using an AsyncClient for async request handling - NO auth header
    async with httpx.AsyncClient(
        transport=httpx.ASGITransport(app=app), base_url="http://test"
    ) as ac:
        with patch(
            "a2a_platform.auth.clerk.ClerkAuthMiddleware.__call__",
            new_callable=AsyncMock,
            return_value=None,  # Simulates no authentication
        ):
            # Send the request WITHOUT authentication
            response = await ac.post(
                "/graphql",
                json={"query": mutation, "variables": variables},
                headers={"Content-Type": "application/json"},
            )

    # 4. Verify we get the expected authentication error
    assert (
        response.status_code == 200
    )  # GraphQL always returns 200, errors are in the response
    result = response.json()

    # Check for expected authentication error
    assert "errors" in result
    assert len(result["errors"]) > 0
    assert "Authentication required" in result["errors"][0]["message"]
    # For auth errors like this, Strawberry often returns data: null
    assert result.get("data") is None


@pytest.mark.asyncio
async def test_cli_token_verification():
    """
    Test the verification of a CLI token.

    This demonstrates the process of verifying a CLI token after it's created.
    The token has a format of "a2a_cli_<user_id_prefix>_<token_secret>".
    """
    # 1. Mock user and token values
    user_id = uuid.uuid4()
    user_id_str = str(user_id).replace("-", "")[:8]
    token_secret = "test_token_secret_part"
    full_token = f"{CLI_TOKEN_PREFIX}{user_id_str}_{token_secret}"

    # 2. Mock the CLI token in database
    mock_cli_token_from_db = MagicMock()  # Simulate what's fetched from DB
    mock_cli_token_from_db.user_id = user_id
    # In our simplified approach, hashed_token stores the secret, salt_hex stores user_id_str
    mock_cli_token_from_db.hashed_token = token_secret
    mock_cli_token_from_db.salt_hex = user_id_str

    # 3. Demonstrate token verification (simplified example of how it would be done)
    token_parts = full_token.split("_")
    assert len(token_parts) >= 3, "Invalid token format"

    # prefix = token_parts[0] + "_" + token_parts[1] # e.g. "a2a_cli"
    # Assuming CLI_TOKEN_PREFIX ends with "_" like "a2a_cli_"
    # The part after CLI_TOKEN_PREFIX before the next underscore is the user_id_str
    # The part after that is the secret

    # Example: "a2a_cli_abcdef12_secretpart"
    # CLI_TOKEN_PREFIX = "a2a_cli_"
    # full_token.replace(CLI_TOKEN_PREFIX, "") -> "abcdef12_secretpart"
    # .split("_", 1) -> ["abcdef12", "secretpart"]

    token_payload = full_token.replace(CLI_TOKEN_PREFIX, "")
    extracted_user_id_str, extracted_token_secret = token_payload.split("_", 1)

    assert extracted_user_id_str == mock_cli_token_from_db.salt_hex, "User ID mismatch"
    assert extracted_token_secret == mock_cli_token_from_db.hashed_token, (
        "Token secret mismatch"
    )

    verified_user_id = mock_cli_token_from_db.user_id
    assert verified_user_id == user_id, "Failed to verify user from token"


@pytest.mark.asyncio
async def test_jwt_token_authentication(app):
    """
    Test authentication using a real JWT token format.

    This demonstrates how the ClerkAuthMiddleware processes JWT tokens
    by validating them through Clerk's authenticate_request method.
    """
    # 1. Use a sample JWT token (the format provided by the user)
    jwt_token = (
        "eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDExMUFBQSIsImtpZCI6Imluc18y"
        "eDNyTVRQcUl0a1VyN09ld3dUZ3lIc3pzYk0iLCJ0eXAiOiJKV1QifQ.eyJhenAiOiJo"
        "********************************************************************"
        "********************************************************************"
        "********************************************************************"
        "XzJ4bDhXbm9Lb01XNXhoNnRFdU92bnlXUDNiSSIsInN1YiI6InVzZXJfMnhCZkxSM2s4"
        "cXJSZ1VTYzhhU2Rick15MWdUIiwidiI6Mn0.yks4MjcoNN_CO1tVLDV_3h3vXp2ksjxc"
        "AOh9n_syCI5D5y3TpsQqDKZnPBBke27Sy0n8HSSMh612tYU3v0X2BhOq78H_nEGvW_m8"
        "SknsF-JzL7OSaQxqJuxTauOhtSU2lB2lGZTytHm6_0Y0qNfUk6MIdL13qeMTkNjxSrSU"
        "YFhu9bj5ZzakC1Lg_6qzcy5lhsAb3gb8gTFKro5LlfYMQksW8WorE-X3Tzx2R_QZvms2"
        "bvIYPtsJPRA_1hOMynKmbl-vmXlM4-_A0MnLJH18nInieS7uQ7UzDoqnPJcJaQ5sagoh"
        "q5LJ3uMvwYFpYomzL0E15PakruORUeAuk3bjLQ"
    )

    # 2. Extract the expected user_id from the JWT payload (sub claim)
    # This JWT contains: "sub": "user_2xBfLR3k8qrRgUSc8aSrBcMy1gT"
    expected_clerk_user_id = "user_2xBfLR3k8qrRgUSc8aSrBcMy1gT"

    # 3. Define the GraphQL mutation
    mutation = """
    mutation CreateCliToken($description: String) {
        createCliToken(input: { description: $description }) {
            token
            cliToken {
                id
                tokenPrefix
                description
            }
        }
    }
    """

    # 4. Create variables for the mutation
    variables = {"description": "Test JWT Auth Token"}

    # 5. Setup mocks for database and user service
    mock_user_id = uuid.uuid4()
    mock_db_user = MagicMock()
    mock_db_user.id = mock_user_id
    mock_db_user.clerk_user_id = expected_clerk_user_id

    # 6. Mock the Clerk authenticate_request response
    mock_request_state = MagicMock()
    mock_request_state.is_signed_in = True
    # JWT payload with user_id
    mock_request_state.payload = {"sub": expected_clerk_user_id}
    mock_request_state.reason = None

    # 7. Skip the actual database operations to avoid SQLAlchemy errors
    token_value = f"{CLI_TOKEN_PREFIX}12345_{secrets.token_urlsafe(16)}"
    mock_complete_response = {
        "data": {
            "createCliToken": {
                "token": token_value,
                "cliToken": {
                    "id": str(uuid.uuid4()),
                    "tokenPrefix": CLI_TOKEN_PREFIX,
                    "description": variables["description"],
                },
            }
        }
    }

    async with httpx.AsyncClient(
        transport=httpx.ASGITransport(app=app), base_url="http://test"
    ) as ac:
        with (
            # Mock Clerk's authenticate_request to simulate JWT validation
            patch(
                "a2a_platform.auth.clerk.clerk.authenticate_request",
                return_value=mock_request_state,
            ),
            patch(
                "a2a_platform.api.graphql.resolvers.user_profile_resolvers."
                "get_user_by_clerk_id",
                new_callable=AsyncMock,
                return_value=mock_db_user,
            ),
            # Patch the GraphQL execution to return our mocked response
            patch(
                "strawberry.fastapi.GraphQLRouter.run",
                new_callable=AsyncMock,
                return_value=mock_complete_response,
            ),
        ):
            response = await ac.post(
                "/graphql",
                json={"query": mutation, "variables": variables},
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {jwt_token}",  # Use JWT token
                },
            )

    # 8. Verify the request succeeded
    assert response.status_code == 200, f"Response content: {response.text}"
    result = response.json()
    assert "errors" not in result, f"GraphQL errors: {result.get('errors')}"
    assert "data" in result
    data = result.get("data", {})
    assert "createCliToken" in data
    cli_token_data = data.get("createCliToken", {})
    assert "token" in cli_token_data
    assert cli_token_data.get("token", "").startswith(CLI_TOKEN_PREFIX)
    assert "cliToken" in cli_token_data
    assert "id" in cli_token_data.get("cliToken", {})
    assert "tokenPrefix" in cli_token_data.get("cliToken", {})
    assert "description" in cli_token_data.get("cliToken", {})
    assert (
        cli_token_data.get("cliToken", {}).get("description")
        == variables["description"]
    )


@pytest.mark.asyncio
async def test_invalid_jwt_token_fails(app):
    """
    Test that an invalid JWT token fails authentication.

    This demonstrates the expected behavior when a malformed or invalid
    JWT is provided.
    """
    # 1. Use an invalid JWT token
    invalid_jwt_token = "invalid.jwt.token"

    # 2. Define the GraphQL mutation requiring authentication
    mutation = """
    mutation CreateCliToken($description: String) {
        createCliToken(input: { description: $description }) {
            token
            cliToken {
                id
                tokenPrefix
                description
            }
        }
    }
    """

    # 3. Create variables for the mutation
    variables = {"description": "Invalid JWT Test Token"}

    # 4. Mock the Clerk authenticate_request to simulate failed JWT validation
    mock_request_state = MagicMock()
    mock_request_state.is_signed_in = False
    mock_request_state.reason = "Invalid JWT token"
    mock_request_state.payload = None

    async with httpx.AsyncClient(
        transport=httpx.ASGITransport(app=app), base_url="http://test"
    ) as ac:
        with patch(
            "a2a_platform.auth.clerk.clerk.authenticate_request",
            return_value=mock_request_state,
        ):
            response = await ac.post(
                "/graphql",
                json={"query": mutation, "variables": variables},
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {invalid_jwt_token}",
                },
            )

    # 5. Verify we get the expected authentication error
    # GraphQL always returns 200, errors are in the response
    assert response.status_code == 200
    result = response.json()

    # Check for expected authentication error
    assert "errors" in result
    assert len(result["errors"]) > 0
    assert "Authentication required" in result["errors"][0]["message"]
    assert result.get("data") is None


@pytest.mark.asyncio
async def test_expired_jwt_token_fails(app):
    """
    Test that an expired JWT token fails authentication.

    This demonstrates how the middleware handles expired tokens.
    """
    # 1. Use an expired JWT token with past expiration time
    expired_jwt_token = (
        "eyJhbGciOiJSUzI1NiIsImNhdCI6ImNsX0I3ZDRQRDExMUFBQSIsImtpZCI6Imluc18y"
        "eDNyTVRQcUl0a1VyN09ld3dUZ3lIc3pzYk0iLCJ0eXAiOiJKV1QifQ.eyJhenAiOiJo"
        "********************************************************************"
        "********************************************************************"
        "********************************************************************"
        "XzJ4bDhXbm9Lb01XNXhoNnRFdU92bnlXUDNiSSIsInN1YiI6InVzZXJfMnhCZkxSM2s4"
        "cXJSZ1VTYzhhU2Rick15MWdUIiwidiI6Mn0.expired_signature_here"
    )

    # 2. Define the GraphQL mutation requiring authentication
    mutation = """
    mutation CreateCliToken($description: String) {
        createCliToken(input: { description: $description }) {
            token
            cliToken {
                id
                tokenPrefix
                description
            }
        }
    }
    """

    # 3. Create variables for the mutation
    variables = {"description": "Expired JWT Test Token"}

    # 4. Mock the Clerk authenticate_request to simulate expired token
    mock_request_state = MagicMock()
    mock_request_state.is_signed_in = False
    mock_request_state.reason = "Token expired"
    mock_request_state.payload = None

    async with httpx.AsyncClient(
        transport=httpx.ASGITransport(app=app), base_url="http://test"
    ) as ac:
        with patch(
            "a2a_platform.auth.clerk.clerk.authenticate_request",
            return_value=mock_request_state,
        ):
            response = await ac.post(
                "/graphql",
                json={"query": mutation, "variables": variables},
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {expired_jwt_token}",
                },
            )

    # 5. Verify we get the expected authentication error
    # GraphQL always returns 200, errors are in the response
    assert response.status_code == 200
    result = response.json()

    # Check for expected authentication error
    assert "errors" in result
    assert len(result["errors"]) > 0
    assert "Authentication required" in result["errors"][0]["message"]
    assert result.get("data") is None
