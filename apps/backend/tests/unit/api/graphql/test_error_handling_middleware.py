"""
Unit tests for GraphQL error handling middleware table-driven implementation.
"""

import pytest
from unittest.mock import Mock, patch
from sqlalchemy.exc import (
    IntegrityError,
    OperationalError,
    StatementError,
    TimeoutError as SQLTimeoutError,
)

from a2a_platform.api.graphql.middleware.error_handling import (
    ERROR_PATTERNS,
    GraphQLDatabaseError,
    handle_database_error,
)


class TestErrorPatternsTable:
    """Test the ERROR_PATTERNS table structure and completeness."""

    def test_error_patterns_structure(self):
        """Test that ERROR_PATTERNS has the correct structure."""
        assert isinstance(ERROR_PATTERNS, list)
        assert len(ERROR_PATTERNS) > 0

        for entry in ERROR_PATTERNS:
            assert isinstance(entry, dict)
            assert "type" in entry
            assert "patterns" in entry
            assert "default" in entry
            assert "log_level" in entry

            # Validate patterns structure
            patterns = entry["patterns"]
            assert isinstance(patterns, list)
            for pattern in patterns:
                assert isinstance(pattern, tuple)
                assert len(pattern) == 3
                keyword, code, message = pattern
                assert isinstance(keyword, str)
                assert isinstance(code, str)
                assert isinstance(message, str)

            # Validate default structure
            default = entry["default"]
            assert isinstance(default, tuple)
            assert len(default) == 2
            code, message = default
            assert isinstance(code, str)
            assert isinstance(message, str)

            # Validate log level
            log_level = entry["log_level"]
            assert log_level in ["debug", "info", "warning", "error", "critical"]

    def test_all_sqlalchemy_exceptions_covered(self):
        """Test that all major SQLAlchemy exceptions are covered."""
        covered_types = {entry["type"] for entry in ERROR_PATTERNS}

        expected_types = {
            IntegrityError,
            OperationalError,
            SQLTimeoutError,
            StatementError,
        }

        assert expected_types.issubset(covered_types), (
            f"Missing coverage for: {expected_types - covered_types}"
        )

    def test_error_codes_are_unique(self):
        """Test that all error codes are unique across patterns."""
        all_codes = set()

        for entry in ERROR_PATTERNS:
            # Check pattern codes
            for _, code, _ in entry["patterns"]:
                assert code not in all_codes, f"Duplicate error code: {code}"
                all_codes.add(code)

            # Check default code
            default_code, _ = entry["default"]
            # Default codes can be shared across different exception types
            # so we don't check for uniqueness here


class TestHandleDatabaseErrorTableDriven:
    """Test the table-driven handle_database_error function."""

    def test_integrity_error_unique_pattern_match(self):
        """Test IntegrityError with unique constraint pattern matching."""
        error = IntegrityError("statement", {}, "unique constraint failed")

        with patch(
            "a2a_platform.api.graphql.middleware.error_handling.logger"
        ) as mock_logger:
            result = handle_database_error(error, "test operation", "user123")

        assert isinstance(result, GraphQLDatabaseError)
        assert result.extensions["code"] == "DUPLICATE_RECORD"
        assert "already exists" in result.message
        mock_logger.warning.assert_called_once()

    def test_integrity_error_foreign_pattern_match(self):
        """Test IntegrityError with foreign key pattern matching."""
        error = IntegrityError("statement", {}, "foreign key constraint failed")

        with patch(
            "a2a_platform.api.graphql.middleware.error_handling.logger"
        ) as mock_logger:
            result = handle_database_error(error, "test operation", "user123")

        assert isinstance(result, GraphQLDatabaseError)
        assert result.extensions["code"] == "INVALID_REFERENCE"
        assert "does not exist" in result.message
        mock_logger.warning.assert_called_once()

    def test_integrity_error_default_fallback(self):
        """Test IntegrityError falling back to default when no pattern matches."""
        error = IntegrityError("statement", {}, "check constraint failed")

        with patch(
            "a2a_platform.api.graphql.middleware.error_handling.logger"
        ) as mock_logger:
            result = handle_database_error(error, "test operation", "user123")

        assert isinstance(result, GraphQLDatabaseError)
        assert result.extensions["code"] == "INTEGRITY_ERROR"
        assert "integrity constraint violated" in result.message.lower()
        mock_logger.warning.assert_called_once()

    def test_operational_error_connection_pattern_match(self):
        """Test OperationalError with connection pattern matching."""
        error = OperationalError("statement", {}, "connection refused")

        with patch(
            "a2a_platform.api.graphql.middleware.error_handling.logger"
        ) as mock_logger:
            result = handle_database_error(error, "test operation", "user123")

        assert isinstance(result, GraphQLDatabaseError)
        assert result.extensions["code"] == "CONNECTION_ERROR"
        assert "temporarily unavailable" in result.message
        mock_logger.error.assert_called_once()

    def test_operational_error_timeout_pattern_match(self):
        """Test OperationalError with timeout pattern matching."""
        error = OperationalError("statement", {}, "timeout expired")

        with patch(
            "a2a_platform.api.graphql.middleware.error_handling.logger"
        ) as mock_logger:
            result = handle_database_error(error, "test operation", "user123")

        assert isinstance(result, GraphQLDatabaseError)
        assert result.extensions["code"] == "TIMEOUT_ERROR"
        assert "timed out" in result.message
        mock_logger.error.assert_called_once()

    def test_operational_error_default_fallback(self):
        """Test OperationalError falling back to default when no pattern matches."""
        error = OperationalError("statement", {}, "database locked")

        with patch(
            "a2a_platform.api.graphql.middleware.error_handling.logger"
        ) as mock_logger:
            result = handle_database_error(error, "test operation", "user123")

        assert isinstance(result, GraphQLDatabaseError)
        assert result.extensions["code"] == "OPERATIONAL_ERROR"
        assert "operation failed" in result.message.lower()
        mock_logger.error.assert_called_once()

    def test_sql_timeout_error_direct_default(self):
        """Test SQLTimeoutError using direct default (no patterns)."""
        error = SQLTimeoutError("statement", {}, "query timeout")

        with patch(
            "a2a_platform.api.graphql.middleware.error_handling.logger"
        ) as mock_logger:
            result = handle_database_error(error, "test operation", "user123")

        assert isinstance(result, GraphQLDatabaseError)
        assert result.extensions["code"] == "TIMEOUT_ERROR"
        assert "timed out" in result.message
        mock_logger.error.assert_called_once()

    def test_statement_error_direct_default(self):
        """Test StatementError using direct default (no patterns)."""
        # Create a mock StatementError since constructor is complex
        error = Mock(spec=StatementError)
        error.__str__ = Mock(return_value="invalid SQL")

        with patch(
            "a2a_platform.api.graphql.middleware.error_handling.logger"
        ) as mock_logger:
            result = handle_database_error(error, "test operation", "user123")

        assert isinstance(result, GraphQLDatabaseError)
        assert result.extensions["code"] == "STATEMENT_ERROR"
        assert "invalid database operation" in result.message.lower()
        mock_logger.error.assert_called_once()

    def test_unknown_error_fallback(self):
        """Test fallback behavior for unknown error types."""
        error = ValueError("some unknown error")

        with patch(
            "a2a_platform.api.graphql.middleware.error_handling.logger"
        ) as mock_logger:
            result = handle_database_error(error, "test operation", "user123")

        assert isinstance(result, GraphQLDatabaseError)
        assert result.extensions["code"] == "UNKNOWN_DATABASE_ERROR"
        assert "unexpected database error" in result.message.lower()
        mock_logger.error.assert_called_once()

    @pytest.mark.parametrize("entry", ERROR_PATTERNS)
    def test_all_error_patterns_table_entries(self, entry):
        """Test each entry in ERROR_PATTERNS table systematically."""
        error_type = entry["type"]
        patterns = entry["patterns"]
        default = entry["default"]
        log_level = entry["log_level"]

        def create_error(message):
            """Create an error with the correct constructor signature."""
            if error_type == StatementError:
                # Create a mock StatementError since constructor is complex
                error = Mock(spec=StatementError)
                error.__str__ = Mock(return_value=message)
                return error
            else:
                return error_type("statement", {}, message)

        # Test each pattern in the entry
        for keyword, expected_code, expected_message in patterns:
            # Create an error with the keyword in the message
            error = create_error(f"test {keyword} error")

            with patch(
                "a2a_platform.api.graphql.middleware.error_handling.logger"
            ) as mock_logger:
                result = handle_database_error(error, "test operation")

            assert isinstance(result, GraphQLDatabaseError)
            assert result.extensions["code"] == expected_code
            assert result.message == expected_message

            # Verify correct log level was used
            expected_log_method = getattr(mock_logger, log_level)
            expected_log_method.assert_called_once()

        # Test default fallback for this error type
        error = create_error("no matching keywords here")

        with patch(
            "a2a_platform.api.graphql.middleware.error_handling.logger"
        ) as mock_logger:
            result = handle_database_error(error, "test operation")

        assert isinstance(result, GraphQLDatabaseError)
        default_code, default_message = default
        assert result.extensions["code"] == default_code
        assert result.message == default_message

        # Verify correct log level was used
        expected_log_method = getattr(mock_logger, log_level)
        expected_log_method.assert_called_once()

    def test_user_context_logging(self):
        """Test that user context is properly included in logs."""
        error = IntegrityError("statement", {}, "unique constraint failed")

        with patch(
            "a2a_platform.api.graphql.middleware.error_handling.logger"
        ) as mock_logger:
            handle_database_error(error, "test operation", "user123")

        # Check that the log message includes user context
        log_call_args = mock_logger.warning.call_args[0][0]
        assert "for user user123" in log_call_args

    def test_no_user_context_logging(self):
        """Test logging when no user ID is provided."""
        error = IntegrityError("statement", {}, "unique constraint failed")

        with patch(
            "a2a_platform.api.graphql.middleware.error_handling.logger"
        ) as mock_logger:
            handle_database_error(error, "test operation")

        # Check that the log message doesn't include user context
        log_call_args = mock_logger.warning.call_args[0][0]
        assert "for user" not in log_call_args

    def test_original_exception_preservation(self):
        """Test that original exception is preserved in the result."""
        original_error = IntegrityError("statement", {}, "unique constraint failed")

        result = handle_database_error(original_error, "test operation")

        assert result.extensions["exception"]["type"] == "IntegrityError"
        assert "unique constraint failed" in result.extensions["exception"]["message"]

    def test_case_insensitive_pattern_matching(self):
        """Test that pattern matching is case insensitive."""
        error = IntegrityError("statement", {}, "UNIQUE constraint failed")

        result = handle_database_error(error, "test operation")

        assert result.extensions["code"] == "DUPLICATE_RECORD"

    def test_operation_context_in_logs(self):
        """Test that operation context is included in log messages."""
        error = IntegrityError("statement", {}, "unique constraint failed")
        custom_operation = "user registration"

        with patch(
            "a2a_platform.api.graphql.middleware.error_handling.logger"
        ) as mock_logger:
            handle_database_error(error, custom_operation)

        log_call_args = mock_logger.warning.call_args[0][0]
        assert custom_operation in log_call_args


class TestTableDrivenAdvantages:
    """Test that demonstrates the advantages of the table-driven approach."""

    def test_easy_error_code_enumeration(self):
        """Test that we can easily enumerate all possible error codes."""
        all_error_codes = set()

        for entry in ERROR_PATTERNS:
            # Add pattern codes
            for _, code, _ in entry["patterns"]:
                all_error_codes.add(code)

            # Add default code
            default_code, _ = entry["default"]
            all_error_codes.add(default_code)

        # Add the unknown error fallback code
        all_error_codes.add("UNKNOWN_DATABASE_ERROR")

        # Verify we have a reasonable number of error codes
        assert len(all_error_codes) >= 8  # Should have at least 8 different error codes

        # Verify specific expected codes exist
        expected_codes = {
            "DUPLICATE_RECORD",
            "INVALID_REFERENCE",
            "INTEGRITY_ERROR",
            "CONNECTION_ERROR",
            "TIMEOUT_ERROR",
            "OPERATIONAL_ERROR",
            "STATEMENT_ERROR",
            "UNKNOWN_DATABASE_ERROR",
        }
        assert expected_codes.issubset(all_error_codes)

    def test_maintainability_adding_new_error_type(self):
        """Test that adding a new error type would be straightforward."""
        # This test demonstrates how easy it would be to add a new error type
        # We'll create a custom error class that doesn't inherit from existing ones

        class CustomDatabaseError(Exception):
            """Custom error for testing table-driven extensibility."""

            pass

        # Temporarily add a new error pattern
        new_pattern = {
            "type": CustomDatabaseError,
            "patterns": [
                ("disk", "DISK_FULL", "Database storage is full"),
                ("memory", "MEMORY_ERROR", "Database out of memory"),
            ],
            "default": ("CUSTOM_DATABASE_ERROR", "Custom database error"),
            "log_level": "error",
        }

        # Add to patterns temporarily
        ERROR_PATTERNS.append(new_pattern)

        try:
            # Test the new pattern with keyword match
            error = CustomDatabaseError("disk full")
            result = handle_database_error(error, "test operation")

            assert result.extensions["code"] == "DISK_FULL"
            assert "storage is full" in result.message

            # Test another pattern
            error = CustomDatabaseError("memory exhausted")
            result = handle_database_error(error, "test operation")

            assert result.extensions["code"] == "MEMORY_ERROR"
            assert "out of memory" in result.message

            # Test default fallback
            error = CustomDatabaseError("unknown custom error")
            result = handle_database_error(error, "test operation")

            assert result.extensions["code"] == "CUSTOM_DATABASE_ERROR"
            assert "Custom database error" in result.message

        finally:
            # Clean up
            ERROR_PATTERNS.remove(new_pattern)
