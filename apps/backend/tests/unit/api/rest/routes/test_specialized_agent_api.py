"""
Unit tests for specialized agent API routes.
"""

from datetime import UTC, datetime
from unittest.mock import AsyncMock

import pytest
from fastapi import HTT<PERSON><PERSON>xception
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.api.rest.routes.specialized_agent_api import process_request


@pytest.mark.no_db
class TestSpecializedAgentAPI:
    @pytest.mark.asyncio
    async def test_process_request_valid(self):
        """Test processing a valid request."""
        # Create a test request with valid user context
        request_data = {
            "user_context": {
                "user_id": "user123",
                "initiating_agent_id": "agent456",
                "request_timestamp": datetime.now(UTC).isoformat(),
            },
            "payload": {"action": "echo", "data": {"key": "value"}},
        }

        # Mock the database session
        mock_db = AsyncMock(spec=AsyncSession)

        # Process the request
        response = await process_request(request_data, mock_db)

        # Verify the response
        assert response["status"] == "success"
        assert response["message"] == "Request processed successfully"
        assert "data" in response
        # For echo action, the response doesn't include 'action' but has 'echo' instead
        assert "echo" in response["data"]
        assert response["data"]["echo"]["key"] == "value"
        assert response["data"]["user_id"] == "user123"
        assert response["data"]["initiating_agent_id"] == "agent456"

    @pytest.mark.asyncio
    async def test_process_request_missing_user_context(self):
        """Test processing a request with missing user context."""
        # Create a test request with missing user context
        request_data = {
            "payload": {"action": "test"},
        }

        # Mock the database session
        mock_db = AsyncMock(spec=AsyncSession)

        # Process the request and expect an exception
        with pytest.raises(HTTPException) as excinfo:
            await process_request(request_data, mock_db)

        # Verify the exception
        assert excinfo.value.status_code == 400
        assert "Invalid user context in request" in excinfo.value.detail
        assert "No user_context found in A2A message" in excinfo.value.detail

    @pytest.mark.asyncio
    async def test_process_request_invalid_user_context(self):
        """Test processing a request with invalid user context."""
        # Create a test request with invalid user context (missing required fields)
        request_data = {
            "user_context": {
                # Missing user_id
                "initiating_agent_id": "agent456",
                "request_timestamp": datetime.now(UTC).isoformat(),
            },
            "payload": {"action": "test"},
        }

        # Mock the database session
        mock_db = AsyncMock(spec=AsyncSession)

        # Process the request and expect an exception
        with pytest.raises(HTTPException) as excinfo:
            await process_request(request_data, mock_db)

        # Verify the exception
        assert excinfo.value.status_code == 400
        assert "Invalid user context in request" in excinfo.value.detail
        assert (
            "Missing required fields in user_context: user_id" in excinfo.value.detail
        )

    @pytest.mark.asyncio
    async def test_process_request_invalid_timestamp(self):
        """Test processing a request with invalid timestamp format."""
        # Create a test request with invalid timestamp format
        request_data = {
            "user_context": {
                "user_id": "user123",
                "initiating_agent_id": "agent456",
                "request_timestamp": "invalid-timestamp",  # Invalid format
            },
            "payload": {"action": "test"},
        }

        # Mock the database session
        mock_db = AsyncMock(spec=AsyncSession)

        # Process the request and expect an exception
        with pytest.raises(HTTPException) as excinfo:
            await process_request(request_data, mock_db)

        # Verify the exception
        assert excinfo.value.status_code == 400
        assert "Invalid user context in request" in excinfo.value.detail
        assert "Validation error in user_context" in excinfo.value.detail
