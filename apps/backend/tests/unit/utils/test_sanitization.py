"""
Unit tests for sanitization utilities.

Tests the sanitize_html_content and sanitize_text_content functions
to ensure proper sanitization of user input and prevention of XSS/SQL injection.

All tests are marked with @pytest.mark.no_db to run without database dependencies.
"""

import time
import pytest
from a2a_platform.utils.sanitization import sanitize_html_content, sanitize_text_content


@pytest.mark.no_db
class TestSQLInjectionSanitization:
    """Test SQL injection pattern sanitization."""

    def test_basic_sql_injection_patterns(self):
        """Test that common SQL injection patterns are completely removed."""
        test_cases = [
            # Classic OR attacks
            ("' OR '1'='1'", ""),
            ('" OR "1"="1"', ""),
            ("` OR `1`=`1`", ""),
            ("' OR '2'='2'", ""),
            ("' OR 'a'='a'", ""),
            # Without quotes
            ("' OR 1=1", ""),
            ("' OR 1=1 --", ""),
            ("' OR 1=1--", ""),
            ("' OR 1=1#", ""),  # Comments remove everything after #
            # Case variations
            ("' or '1'='1'", ""),
            ("' Or '1'='1'", ""),
            ("' oR '1'='1'", ""),
            ("' OR '1'='1'", ""),
            # With extra spaces
            ("'  OR  '1'  =  '1'", ""),
            ("'\tOR\t'1'\t=\t'1'", ""),
            ("'\nOR\n'1'\n=\n'1'", ""),
            # AND attacks
            ("' AND '1'='1'", ""),
            ("' AND 1=1", ""),
            # Comments
            ("admin' --", "admin"),
            ("admin' -- comment", "admin"),
            ("admin' /*comment*/", "admin"),
            # Command injection
            ("'; DROP TABLE users; --", ""),
            ("'; DELETE FROM users; --", ""),
            ("' UNION SELECT * FROM users", "* FROM users"),
            ("' UNION ALL SELECT password FROM users", "password FROM users"),
            # Functions
            ("' OR SLEEP(5) --", ""),
            ("' OR BENCHMARK(1000000,MD5('A')) --", ""),
            ("' OR CONCAT('a','b')='ab' --", ""),
        ]

        for input_text, expected in test_cases:
            result = sanitize_html_content(input_text)
            assert result == expected, (
                f"Failed for input: {repr(input_text)}, got: {repr(result)}"
            )

    def test_encoded_sql_injection_attempts(self):
        """Test that encoded SQL injection attempts are caught."""
        test_cases = [
            # URL encoded
            ("%27%20OR%20%271%27%3D%271%27", ""),
            # HTML entity encoded
            ("&#39; OR &#39;1&#39;=&#39;1&#39;", ""),
            ("&apos; OR &apos;1&apos;=&apos;1&apos;", ""),
            # Hex encoded
            ("0x27204F52202731273D273127", ""),
            # Mixed encoding
            ("&#39;%20OR%20'1'='1'", ""),
        ]

        for input_text, expected in test_cases:
            result = sanitize_html_content(input_text)
            assert result == expected, (
                f"Failed for encoded input: {repr(input_text)}, got: {repr(result)}"
            )

    def test_complex_sql_injection_patterns(self):
        """Test complex and nested SQL injection attempts."""
        test_cases = [
            # Multiple conditions
            ("' OR '1'='1' AND '2'='2'", ""),
            ("' OR '1'='1' OR '2'='2'", ""),
            # Nested injections
            ("admin' OR (SELECT 1)=1 --", "admin( 1)=1"),  # Partial cleanup
            # With legitimate content
            ("Hello' OR '1'='1' World", "Hello World"),
            # Path traversal combined
            ("../../../etc/passwd' OR '1'='1'", "../../../etc/passwd"),
        ]

        for input_text, expected in test_cases:
            result = sanitize_html_content(input_text)
            assert result == expected, (
                f"Failed for complex input: {repr(input_text)}, got: {repr(result)}"
            )

    def test_legitimate_content_preserved(self):
        """Test that legitimate content with quotes is preserved."""
        test_cases = [
            # Normal text
            ("Hello World", "Hello World"),
            ("This is a test message", "This is a test message"),
            # Legitimate quotes and apostrophes (preserved)
            ("It's a beautiful day", "It's a beautiful day"),
            ("She said 'hello'", "She said 'hello'"),
            ('He said "goodbye"', 'He said "goodbye"'),
            # Names with apostrophes (preserved)
            ("O'Connor", "O'Connor"),
            ("D'Angelo", "D'Angelo"),
            # JSON-like content (preserved)
            ('{"key": "value"}', '{"key": "value"}'),
            # SQL-like but safe content
            (
                "SELECT * FROM table WHERE id = 1",
                "* FROM table WHERE id = 1",
            ),  # SELECT removed
            # Numbers and math
            ("1 + 1 = 2", "1 + 1 = 2"),
            ("The score was 3-1", "The score was 3-1"),
        ]

        for input_text, expected in test_cases:
            result = sanitize_html_content(input_text)
            assert result == expected, (
                f"Failed for legitimate content: {repr(input_text)}, got: {repr(result)}"
            )


@pytest.mark.no_db
class TestXSSSanitization:
    """Test XSS prevention sanitization."""

    def test_script_tag_removal(self):
        """Test that script tags are completely removed."""
        test_cases = [
            # Basic script tags
            ("<script>alert('xss')</script>", ""),
            ("<SCRIPT>alert('xss')</SCRIPT>", ""),
            ("<script type='text/javascript'>alert('xss')</script>", ""),
            ("<script src='evil.js'></script>", ""),
            # With content around
            ("Hello<script>alert('xss')</script>World", "HelloWorld"),
            ("<div><script>alert('xss')</script></div>", ""),  # All tags removed
            # Malformed
            ("<script>alert('xss')", ""),
            ("<script alert('xss')</script>", ""),
        ]

        for input_text, expected in test_cases:
            result = sanitize_html_content(input_text)
            assert result == expected, (
                f"Failed for script: {repr(input_text)}, got: {repr(result)}"
            )

    def test_event_handler_removal(self):
        """Test that event handlers are removed."""
        test_cases = [
            # onclick
            ("<div onclick=\"alert('xss')\">click</div>", "click"),  # div tags removed
            ('<a href="#" onclick="alert(\'xss\')">link</a>', "link"),  # a tags removed
            # onload
            ("<body onload=\"alert('xss')\">", ""),
            ('<img src="x" onload="alert(\'xss\')">', ""),
            # onerror
            ('<img src=x onerror=alert("xss")>', ""),
            ("<img src=x onerror=\"alert('xss')\">", ""),
            # Other events
            ("<div onmouseover=\"alert('xss')\">hover</div>", "hover"),
            ("<input onfocus=\"alert('xss')\">", ""),
        ]

        for input_text, expected in test_cases:
            result = sanitize_html_content(input_text)
            assert result == expected, (
                f"Failed for event handler: {repr(input_text)}, got: {repr(result)}"
            )

    def test_javascript_url_removal(self):
        """Test that javascript: URLs are removed."""
        test_cases = [
            # Basic javascript URLs (a tags removed)
            ("<a href=\"javascript:alert('xss')\">link</a>", "link"),
            ('<a href="javascript:void(0)">link</a>', "link"),
            ("<a href=\"JAVASCRIPT:alert('xss')\">link</a>", "link"),
            # vbscript
            ("<a href=\"vbscript:msgbox('xss')\">link</a>", "link"),
            # data URLs with script
            (
                "<a href=\"data:text/html,<script>alert('xss')</script>\">link</a>",
                "link",
            ),
        ]

        for input_text, expected in test_cases:
            result = sanitize_html_content(input_text)
            assert result == expected, (
                f"Failed for javascript URL: {repr(input_text)}, got: {repr(result)}"
            )

    def test_dangerous_tag_removal(self):
        """Test that dangerous HTML tags are removed."""
        test_cases = [
            # iframes
            ('<iframe src="evil.com"></iframe>', ""),
            ('<IFRAME SRC="evil.com"></IFRAME>', ""),
            # embed/object
            ('<embed src="evil.swf">', ""),
            ('<object data="evil.swf"></object>', ""),
            ('<applet code="Evil.class"></applet>', ""),
            # forms
            ('<form action="evil.com"><input type="text"></form>', ""),
            ('<input type="hidden" value="evil">', ""),
            ("<textarea>evil</textarea>", ""),
            # style/meta/link
            ("<style>body{background:url(evil.com)}</style>", ""),
            ('<link rel="stylesheet" href="evil.css">', ""),
            ('<meta http-equiv="refresh" content="0;url=evil.com">', ""),
            ('<base href="evil.com">', ""),
        ]

        for input_text, expected in test_cases:
            result = sanitize_html_content(input_text)
            assert result == expected, (
                f"Failed for dangerous tag: {repr(input_text)}, got: {repr(result)}"
            )

    def test_encoded_xss_attempts(self):
        """Test that encoded XSS attempts are caught."""
        test_cases = [
            # HTML entity encoded
            ("&lt;script&gt;alert('xss')&lt;/script&gt;", ""),
            # URL encoded
            ("%3Cscript%3Ealert('xss')%3C/script%3E", ""),
            # Mixed encoding
            ("&lt;script>alert('xss')</script>", ""),
        ]

        for input_text, expected in test_cases:
            result = sanitize_html_content(input_text)
            assert result == expected, (
                f"Failed for encoded XSS: {repr(input_text)}, got: {repr(result)}"
            )

    def test_safe_html_preservation(self):
        """Test that safe HTML content is preserved (with tags stripped)."""
        test_cases = [
            # Basic formatting (tags removed but content preserved)
            ("<p>Hello World</p>", "Hello World"),
            ("<strong>Bold text</strong>", "Bold text"),
            ("<em>Italic text</em>", "Italic text"),
            # Links (tags removed but text preserved)
            ('<a href="https://safe.com">Safe link</a>', "Safe link"),
            # Mixed content
            ("<p>Hello <strong>World</strong>!</p>", "Hello World!"),
            # Plain text with special chars
            ("Hello & World", "Hello & World"),
            ("Price < $100", "Price  $100"),  # Angle brackets removed for safety
            ("A > B", "A  B"),  # Angle brackets removed for safety
        ]

        for input_text, expected in test_cases:
            result = sanitize_html_content(input_text)
            assert result == expected, (
                f"Failed for safe HTML: {repr(input_text)}, got: {repr(result)}"
            )


@pytest.mark.no_db
class TestEdgeCases:
    """Test edge cases and advanced attack patterns."""

    def test_null_bytes_and_control_characters(self):
        """Test handling of null bytes and control characters."""
        test_cases = [
            # Null bytes
            ("Hello\x00World", "HelloWorld"),
            ("\x00<script>alert('xss')</script>", ""),
            # Control characters
            ("Hello\x01\x02\x03World", "HelloWorld"),
            # Unicode replacement char
            ("Hello\ufffdWorld", "HelloWorld"),
            # Valid whitespace preserved
            ("Hello\nWorld", "Hello\nWorld"),
            ("Hello\tWorld", "Hello\tWorld"),
            ("Hello\rWorld", "Hello\rWorld"),
        ]

        for input_text, expected in test_cases:
            result = sanitize_html_content(input_text)
            assert result == expected, (
                f"Failed for control chars: {repr(input_text)}, got: {repr(result)}"
            )

    def test_unicode_attacks(self):
        """Test Unicode-based attack attempts."""
        test_cases = [
            # Unicode spaces in SQL injection
            ("'\u2000OR\u2000'1'\u2000=\u2000'1'", ""),
            # Zero-width characters
            (
                "'\u200bOR\u200b'1'='1'",
                "'1",
            ),  # Zero-width chars removed but some remnants remain
            # Unicode quotes - the sanitization function preserves Unicode quotes
            (
                "'\u2018 OR \u20181\u2019=\u20181\u2019",
                "'\u2018 OR \u20181\u2019=\u20181\u2019",
            ),  # Unicode preserved
            # Right-to-left override
            ("\u202e<script>alert('xss')</script>", "\u202e"),
        ]

        for input_text, expected in test_cases:
            result = sanitize_html_content(input_text)
            assert result == expected, (
                f"Failed for Unicode attack: {repr(input_text)}, got: {repr(result)}"
            )

    def test_nested_and_recursive_patterns(self):
        """Test nested and recursive attack patterns."""
        test_cases = [
            # Nested tags
            ("<script><script>alert('xss')</script></script>", ""),
            # Recursive encoding
            ("<<script>script>alert('xss')<</script>/script>", ""),
            # Mixed attacks
            ("<script>' OR '1'='1'</script>", ""),
            ("' OR '1'='1'<script>alert('xss')</script>", ""),
        ]

        for input_text, expected in test_cases:
            result = sanitize_html_content(input_text)
            assert result == expected, (
                f"Failed for nested pattern: {repr(input_text)}, got: {repr(result)}"
            )

    def test_empty_and_whitespace_input(self):
        """Test handling of empty and whitespace-only input."""
        test_cases = [
            ("", ""),
            ("   ", ""),
            ("\t\n\r", ""),
            ("   \t   ", ""),
        ]

        for input_text, expected in test_cases:
            result = sanitize_html_content(input_text)
            assert result == expected, (
                f"Failed for whitespace: {repr(input_text)}, got: {repr(result)}"
            )

    def test_very_long_input(self):
        """Test handling of very long input strings."""
        # Long string with injection attempt in the middle
        long_input = "A" * 2000 + "' OR '1'='1'" + "B" * 2000
        expected = "A" * 2000 + "B" * 2000

        result = sanitize_html_content(long_input)
        assert result == expected

        # Long string with XSS attempt
        long_xss = "Safe " * 1000 + "<script>alert('xss')</script>" + " Text" * 1000
        expected_xss = "Safe " * 1000 + " Text" * 1000

        result = sanitize_html_content(long_xss)
        assert result.strip() == expected_xss.strip()


@pytest.mark.no_db
class TestSanitizeTextContent:
    """Test the sanitize_text_content function."""

    def test_text_content_alias(self):
        """Test that sanitize_text_content behaves the same as sanitize_html_content."""
        test_inputs = [
            "' OR '1'='1'",
            "<script>alert('xss')</script>",
            "Normal text",
            "<p>HTML content</p>",
        ]

        for test_input in test_inputs:
            html_result = sanitize_html_content(test_input)
            text_result = sanitize_text_content(test_input)
            assert html_result == text_result, (
                f"sanitize_text_content should behave the same as sanitize_html_content for: {repr(test_input)}"
            )


@pytest.mark.no_db
class TestPerformance:
    """Test performance characteristics of sanitization."""

    def test_sanitization_performance(self):
        """Test that sanitization completes within acceptable time limits."""
        # Test with various input sizes
        test_cases = [
            ("Small input", "Hello ' OR '1'='1' World", 100),
            ("Medium input", "A" * 1000 + "' OR '1'='1'" + "B" * 1000, 50),
            ("Large input", "Safe text " * 400, 20),  # ~4000 chars
            ("Complex input", "<script>alert('xss')</script>" * 100, 20),
        ]

        for description, input_text, max_iterations in test_cases:
            start_time = time.perf_counter()

            # Run multiple iterations to get average
            for _ in range(max_iterations):
                result = sanitize_html_content(input_text)

            end_time = time.perf_counter()
            avg_time = (
                (end_time - start_time) / max_iterations * 1000
            )  # Convert to milliseconds

            # Assert that average time is less than 7.5ms
            assert avg_time < 7.5, (
                f"{description} took {avg_time:.2f}ms average (limit: 7.5ms)"
            )

            # Also verify the result is correct
            assert "' OR '1'='1'" not in result
            assert "<script>" not in result

    def test_worst_case_performance(self):
        """Test performance with worst-case regex scenarios."""
        # Pathological cases that could cause regex backtracking
        test_cases = [
            # Many quotes and special characters
            ("'" * 100 + " OR " + "'" * 100),
            # Repeated patterns
            ("OR " * 500),
            # Nested brackets
            ("<" * 50 + ">" * 50),
            # Mixed patterns
            ("'<script>' OR '1'='1'</script>'" * 50),
        ]

        for input_text in test_cases:
            start_time = time.perf_counter()
            result = sanitize_html_content(input_text)
            end_time = time.perf_counter()

            elapsed_ms = (end_time - start_time) * 1000

            # Even worst cases should complete quickly
            assert elapsed_ms < 10.0, (
                f"Worst-case input took {elapsed_ms:.2f}ms (limit: 10ms)"
            )

            # Verify sanitization worked
            assert "' OR '" not in result
            assert "<script>" not in result


@pytest.mark.no_db
class TestOWASPPatterns:
    """Test against OWASP Top 10 injection patterns."""

    def test_owasp_sql_injection_patterns(self):
        """Test against OWASP SQL injection cheat sheet patterns."""
        test_cases = [
            # Authentication bypass (-- comments remove everything after)
            (
                "admin'--",
                "admin",
            ),  # The quote and comment are both removed by sanitization
            ("admin' #", "admin"),
            ("admin'/*", "admin'"),
            ("' or 1=1--", ""),
            ("' or 1=1#", ""),
            ("' or 1=1/*", ""),
            ("') or '1'='1--", "') ='1"),
            ("') or ('1'='1--", "') or ('1"),
            # Union-based
            ("' union select null--", "null"),
            ("' union select 1,2,3--", "1,2,3"),
            # Time-based blind
            ("'; waitfor delay '0:0:5'--", " waitfor delay 0:0:5"),
            ("'; select sleep(5)--", " select"),
            # Boolean-based blind
            ("' and 1=1--", ""),
            ("' and 1=2--", " and 1=2"),
            # Stacked queries
            ("'; exec xp_cmdshell('dir')--", " xp_cmdshell(dir)"),
            ("'; drop table users--", " table users"),
        ]

        for input_text, expected in test_cases:
            result = sanitize_html_content(input_text)
            assert result == expected, (
                f"Failed OWASP SQL pattern: {repr(input_text)}, got: {repr(result)}"
            )

    def test_owasp_xss_patterns(self):
        """Test against OWASP XSS filter evasion patterns."""
        test_cases = [
            # Basic XSS
            ("<script>alert(1)</script>", ""),
            ("<img src=x onerror=alert(1)>", ""),
            ("<svg onload=alert(1)>", ""),
            # Without quotes
            ("<img src=x onerror=alert(String.fromCharCode(88,83,83))>", ""),
            # Case variations
            ("<ScRiPt>alert(1)</ScRiPt>", ""),
            ("<IMG SRC=x ONERROR=alert(1)>", ""),
            # Malformed tags
            ("<script/src=x.js></script>", ""),
            ("<<script>alert(1)//</script>", ""),  # All script content removed
            # Protocol handlers
            ("<a href=javascript:alert(1)>X</a>", "X"),
            ("<a href=vbscript:msgbox(1)>X</a>", "X"),
            # Data URLs
            ("<a href=data:text/html,<script>alert(1)</script>>X</a>", "X"),
            # Event handlers in style
            ('<div style="background:url(javascript:alert(1))">X</div>', "X"),
            # UTF-7/UTF-8 encoding attempts (decoded by our function)
            (
                "+ADw-script+AD4-alert(1)+ADw-/script+AD4-",
                "+ADw-script+AD4-alert(1)+ADw-/script+AD4-",
            ),
        ]

        for input_text, expected in test_cases:
            result = sanitize_html_content(input_text)
            assert result == expected, (
                f"Failed OWASP XSS pattern: {repr(input_text)}, got: {repr(result)}"
            )
