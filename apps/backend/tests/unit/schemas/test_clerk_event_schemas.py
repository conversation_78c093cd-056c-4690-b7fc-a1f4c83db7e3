import pytest
from pydantic import ValidationError

from a2a_platform.schemas.clerk_events import UserDeletedData, UserDeletedEvent


def test_user_deleted_data_valid():
    data = {"id": "clerk_user_id_123", "deleted": True}
    model = UserDeletedData(**data)
    assert model.id == "clerk_user_id_123"
    assert model.deleted is True


def test_user_deleted_data_default_deleted():
    data = {"id": "clerk_user_id_456"}
    model = UserDeletedData(**data)
    assert model.id == "clerk_user_id_456"
    assert model.deleted is True  # default


def test_user_deleted_data_invalid_missing_id():
    data = {"deleted": True}
    with pytest.raises(ValidationError):
        UserDeletedData(**data)


def test_user_deleted_event_valid():
    data = {
        "type": "user.deleted",
        "object": "event",
        "clerk_event_id": "evt_abc123",
        "data": {"id": "clerk_user_id_789", "deleted": True},
    }
    event = UserDeletedEvent(**data)
    assert event.type == "user.deleted"
    assert event.object == "event"
    assert event.clerk_event_id == "evt_abc123"
    assert event.data.id == "clerk_user_id_789"
    assert event.data.deleted is True


def test_user_deleted_event_invalid_missing_data():
    data = {"type": "user.deleted", "object": "event"}
    with pytest.raises(ValidationError):
        UserDeletedEvent(**data)
