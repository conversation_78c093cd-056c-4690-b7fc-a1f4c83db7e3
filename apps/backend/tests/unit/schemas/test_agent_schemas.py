"""
Unit tests for agent schemas.
"""

import pytest
from pydantic import ValidationError

from a2a_platform.schemas.agent_schemas import (
    AgentSkill,
    RegisteredAgentBase,
    RegisteredAgentCreate,
    RegisteredAgentRead,
)


class TestAgentSkill:
    def test_agent_skill_creation(self):
        """Test that AgentSkill can be created with required fields."""
        skill = AgentSkill(name="test_skill")
        assert skill.name == "test_skill"
        assert skill.description is None
        assert skill.parameters_schema is None

    def test_agent_skill_with_all_fields(self):
        """Test that AgentSkill can be created with all fields."""
        skill = AgentSkill(
            name="test_skill",
            description="A test skill",
            parameters_schema={"param1": {"type": "string"}},
        )
        assert skill.name == "test_skill"
        assert skill.description == "A test skill"
        assert skill.parameters_schema == {"param1": {"type": "string"}}

    def test_agent_skill_missing_name(self):
        """Test that AgentSkill cannot be created without a name."""
        with pytest.raises(ValidationError):
            AgentSkill()


class TestRegisteredAgentBase:
    def test_registered_agent_base_minimal(self):
        """Test that RegisteredAgentBase can be created with minimal fields."""
        agent = RegisteredAgentBase(name="Test Agent", version="1.0.0")
        assert agent.name == "Test Agent"
        assert agent.version == "1.0.0"
        assert agent.description is None
        assert agent.endpoint_url is None
        assert agent.async_queue_name is None
        assert agent.capabilities == {}
        assert agent.skills == []
        assert agent.authentication_info == {}
        assert agent.status == "active"
        # New marketplace fields
        assert agent.developer_id is None
        assert agent.pricing_info == {}
        assert agent.review_status is None

    def test_registered_agent_base_with_marketplace_fields(self):
        """Test that RegisteredAgentBase can be created with marketplace fields."""
        agent = RegisteredAgentBase(
            name="Test Agent",
            version="1.0.0",
            developer_id="dev123",
            pricing_info={"type": "subscription", "amount_monthly": 999},
            review_status="pending",
        )
        assert agent.name == "Test Agent"
        assert agent.version == "1.0.0"
        assert agent.developer_id == "dev123"
        assert agent.pricing_info == {"type": "subscription", "amount_monthly": 999}
        assert agent.review_status == "pending"

    def test_registered_agent_base_invalid_review_status(self):
        """Test that RegisteredAgentBase validates review_status."""
        with pytest.raises(ValidationError):
            RegisteredAgentBase(
                name="Test Agent",
                version="1.0.0",
                review_status="invalid_status",
            )

    def test_registered_agent_base_valid_review_status(self):
        """Test that RegisteredAgentBase accepts valid review_status values."""
        for status in ["pending", "approved", "rejected"]:
            agent = RegisteredAgentBase(
                name="Test Agent",
                version="1.0.0",
                review_status=status,
            )
            assert agent.review_status == status


class TestRegisteredAgentCreate:
    def test_registered_agent_create_minimal(self):
        """Test that RegisteredAgentCreate can be created with minimal fields."""
        agent = RegisteredAgentCreate(
            agent_definition_id="test_agent_v1",
            name="Test Agent",
            version="1.0.0",
        )
        assert agent.agent_definition_id == "test_agent_v1"
        assert agent.name == "Test Agent"
        assert agent.version == "1.0.0"
        assert agent.developer_id is None
        assert agent.pricing_info == {}
        assert agent.review_status is None

    def test_registered_agent_create_with_marketplace_fields(self):
        """Test that RegisteredAgentCreate can be created with marketplace fields."""
        agent = RegisteredAgentCreate(
            agent_definition_id="test_agent_v1",
            name="Test Agent",
            version="1.0.0",
            developer_id="dev123",
            pricing_info={"type": "per_call", "amount_per_call": 1},
            review_status="approved",
        )
        assert agent.agent_definition_id == "test_agent_v1"
        assert agent.name == "Test Agent"
        assert agent.version == "1.0.0"
        assert agent.developer_id == "dev123"
        assert agent.pricing_info == {"type": "per_call", "amount_per_call": 1}
        assert agent.review_status == "approved"

    def test_registered_agent_create_invalid_agent_definition_id(self):
        """Test that RegisteredAgentCreate validates agent_definition_id format."""
        with pytest.raises(ValidationError):
            RegisteredAgentCreate(
                agent_definition_id="invalid id with spaces",
                name="Test Agent",
                version="1.0.0",
            )


class TestRegisteredAgentRead:
    def test_registered_agent_read_from_dict(self):
        """Test that RegisteredAgentRead can be created from a dictionary."""
        data = {
            "agent_definition_id": "test_agent_v1",
            "name": "Test Agent",
            "version": "1.0.0",
            "created_at": "2023-01-01T00:00:00Z",
            "updated_at": "2023-01-01T00:00:00Z",
            "developer_id": "dev123",
            "pricing_info": {"type": "free"},
            "review_status": "approved",
        }
        agent = RegisteredAgentRead(**data)
        assert agent.agent_definition_id == "test_agent_v1"
        assert agent.name == "Test Agent"
        assert agent.version == "1.0.0"
        assert agent.created_at == "2023-01-01T00:00:00Z"
        assert agent.updated_at == "2023-01-01T00:00:00Z"
        assert agent.developer_id == "dev123"
        assert agent.pricing_info == {"type": "free"}
        assert agent.review_status == "approved"
