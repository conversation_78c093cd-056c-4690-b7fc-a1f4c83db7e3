"""
Unit tests for A2A context schemas.
"""

from datetime import UTC, datetime

import pytest

from a2a_platform.schemas.a2a_context import A2AMessage, UserContext


@pytest.mark.no_db
class TestA2AContext:
    def test_user_context_creation(self):
        """Test that UserContext can be created with required fields."""
        user_context = UserContext(
            user_id="user123",
            initiating_agent_id="agent456",
            request_timestamp=datetime.now(UTC),
        )
        assert user_context.user_id == "user123"
        assert user_context.initiating_agent_id == "agent456"
        assert isinstance(user_context.request_timestamp, datetime)

    def test_user_context_default_timestamp(self):
        """Test that User<PERSON>ontex<PERSON> sets a default timestamp if not provided."""
        user_context = UserContext(user_id="user123", initiating_agent_id="agent456")
        assert user_context.user_id == "user123"
        assert user_context.initiating_agent_id == "agent456"
        assert isinstance(user_context.request_timestamp, datetime)

    def test_a2a_message_creation(self):
        """Test that A2AMessage can be created with required fields."""
        user_context = UserContext(
            user_id="user123",
            initiating_agent_id="agent456",
            request_timestamp=datetime.now(UTC),
        )
        payload = {"action": "test", "data": {"key": "value"}}
        message = A2AMessage(user_context=user_context, payload=payload)
        assert message.user_context == user_context
        assert message.payload == payload

    def test_a2a_message_model_dump(self):
        """Test that A2AMessage can be serialized to a dictionary."""
        user_context = UserContext(
            user_id="user123",
            initiating_agent_id="agent456",
            request_timestamp=datetime.now(UTC),
        )
        payload = {"action": "test", "data": {"key": "value"}}
        message = A2AMessage(user_context=user_context, payload=payload)
        message_dict = message.model_dump()
        assert "user_context" in message_dict
        assert "payload" in message_dict
        assert message_dict["user_context"]["user_id"] == "user123"
        assert message_dict["user_context"]["initiating_agent_id"] == "agent456"
        assert "request_timestamp" in message_dict["user_context"]
        assert message_dict["payload"] == payload
