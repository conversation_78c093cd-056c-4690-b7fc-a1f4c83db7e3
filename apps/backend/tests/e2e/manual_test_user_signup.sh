#!/bin/bash
# E2E test script for User Registration and Login
# This script guides through the manual steps to test the complete user registration flow
# from frontend to backend, including webhook processing and database verification.

echo "=== US2.1 User Registration and Login E2E Test Script ==="
echo ""
echo "Follow these steps to manually verify the end-to-end workflow:"
echo ""

echo "Step 1: Ensure your development environment is running"
echo "- Backend API should be running"
echo "- Frontend dev server should be running"
echo "- Database should be accessible"
echo "- Clerk webhook should be configured to point to your backend endpoint"
echo ""

echo "Step 2: Navigate to the sign-up page in your browser"
echo "- Go to: https://localhost:5173/auth/sign-up"
echo ""

echo "Step 3: Complete the sign-up process"
echo "- Sign up using either Google, GitHub, or Email Magic Link"
echo "- If using Email Magic Link, check your email and click the verification link"
echo "- Wait for the sign-up process to complete in the frontend"
echo ""

echo "Step 4: Verify backend webhook processing (run these commands in terminal)"
echo "- Check backend logs for webhook receipt:"
echo "  \$ tail -f /path/to/backend/logs"
echo "  Look for messages like 'Received Clerk webhook event: user.created'"
echo ""

echo "Step 5: Verify database record creation"
echo "- Connect to your database and run the following SQL query:"
echo "  \$ psql -U <username> -d <database> -c \"SELECT clerk_user_id, email FROM users ORDER BY created_at DESC LIMIT 5;\""
echo "- Verify a record exists with the clerk_user_id for your newly created user"
echo ""

echo "Step 6: Verify authentication works for the new user"
echo "- Navigate to a protected route in your application"
echo "- Alternatively, use the GraphQL playground to test authentication:"
echo "  1. Go to: https://localhost:8000/graphql"
echo "  2. Execute the following query with your JWT token in the HTTP Headers tab:"
echo "     {\"Authorization\": \"Bearer YOUR_JWT_TOKEN\"}"
echo "     query {
  me
}"
echo "  3. Verify you get a response containing your clerk_user_id"
echo ""

echo "Step 7: Document your findings"
echo "- Record any issues or successful test results"
echo "- If all steps are successful, US2.1 is fully implemented"
