#!/usr/bin/env python3
"""
Debug script to find where database connections are happening during test execution.
"""
import os
import sys
import asyncio
from unittest.mock import patch, MagicMock, AsyncMock

# Set environment variables before any imports
os.environ["TEST_DATABASE_MODE"] = "no_db"
os.environ["REDIS_URL"] = "redis://localhost:6379"

def mock_create_engine(*args, **kwargs):
    print(f"🚨 SYNC ENGINE CREATION ATTEMPTED: {args}, {kwargs}")
    import traceback
    traceback.print_stack()
    return MagicMock()

def mock_create_async_engine(*args, **kwargs):
    print(f"🚨 ASYNC ENGINE CREATION ATTEMPTED: {args}, {kwargs}")
    import traceback
    traceback.print_stack()
    return AsyncMock()

async def run_test():
    # Patch before any SQLAlchemy imports
    with patch('sqlalchemy.create_engine', side_effect=mock_create_engine), \
         patch('sqlalchemy.ext.asyncio.create_async_engine', side_effect=mock_create_async_engine), \
         patch('a2a_platform.db.create_async_engine', side_effect=mock_create_async_engine), \
         patch('a2a_platform.db.session.create_async_engine', side_effect=mock_create_async_engine), \
         patch('a2a_platform.db.session.create_engine', side_effect=mock_create_engine):
        
        print("Starting test execution...")
        
        try:
            # Import the test class
            from tests.unit.api.rest.routes.test_specialized_agent_api import TestSpecializedAgentAPI
            
            # Create an instance
            test_instance = TestSpecializedAgentAPI()
            
            print("✅ Test instance created")
            
            # Try to run the test method
            print("🧪 Running test method...")
            await test_instance.test_process_request_valid()
            print("✅ Test method completed successfully")
            
        except Exception as e:
            print(f"❌ Test execution failed: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(run_test())
