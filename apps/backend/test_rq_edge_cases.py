#!/usr/bin/env python
"""
Edge case test script for Redis Queue (RQ).

This script tests edge cases in the RQ implementation by:
1. Testing race conditions
2. Testing message ordering
3. Testing persistence during restarts

The script supports a reduced load mode for CI environments by setting
the RQ_TEST_REDUCED_LOAD environment variable to 'true'.
"""

import asyncio
import logging
import os
import sys
import time
import uuid
import random
from typing import List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath("src"))

# Import the necessary modules
import redis
from rq import Queue
from a2a_platform.messaging.rq_client import RQClient
from a2a_platform.config.settings import get_settings

# Get settings
settings = get_settings()

# Check if we're running in reduced load mode (for CI)
REDUCED_LOAD = os.environ.get("RQ_TEST_REDUCED_LOAD", "").lower() == "true"
if REDUCED_LOAD:
    logger.info("Running in reduced load mode for CI environment")


async def test_race_conditions(num_concurrent: int = 10) -> bool:
    """
    Test race conditions by sending messages concurrently.

    Args:
        num_concurrent: Number of concurrent messages to send

    Returns:
        bool: True if the test passes, False otherwise
    """
    logger.info(f"Testing race conditions with {num_concurrent} concurrent messages")

    try:
        # Create an RQ client
        rq_client = RQClient(redis_url=settings.REDIS_URL)

        # Generate a unique key for this test
        test_key = f"race-test-{uuid.uuid4()}"

        # Create tasks to send messages concurrently
        tasks = []
        message_ids = []

        for i in range(num_concurrent):
            async def send_message(index: int) -> str:
                message_id = await rq_client.send_message(
                    queue_name="default",
                    message_body=f"Race condition test message {index}",
                    message_attributes={
                        "test_key": test_key,
                        "index": index,
                    }
                )
                return message_id

            task = send_message(i)
            tasks.append(task)

        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks)
        message_ids.extend(results)

        logger.info(f"Sent {len(message_ids)} messages concurrently")

        # Wait for messages to be processed
        await asyncio.sleep(2)

        # Check Redis for the jobs
        redis_conn = redis.from_url(settings.REDIS_URL)

        # Check if all jobs exist
        all_jobs_exist = True
        for message_id in message_ids:
            job_key = f"rq:job:{message_id}"
            job_exists = redis_conn.exists(job_key)
            if not job_exists:
                logger.warning(f"Job {message_id} does not exist in Redis")
                all_jobs_exist = False

        if all_jobs_exist:
            logger.info("Race condition test passed: All jobs exist in Redis")
            return True
        else:
            logger.warning("Race condition test failed: Some jobs are missing")
            return False

    except Exception as e:
        logger.error(f"Error in race condition test: {str(e)}")
        return False


async def test_message_ordering(num_messages: int = 20) -> bool:
    """
    Test message ordering by sending messages with sequence numbers.

    Args:
        num_messages: Number of messages to send

    Returns:
        bool: True if the test passes, False otherwise
    """
    logger.info(f"Testing message ordering with {num_messages} messages")

    try:
        # Create an RQ client
        rq_client = RQClient(redis_url=settings.REDIS_URL)

        # Generate a unique key for this test
        test_key = f"order-test-{uuid.uuid4()}"

        # Create a test queue
        test_queue_name = f"test-order-{uuid.uuid4()}"

        # Send messages with sequence numbers
        message_ids = []
        for i in range(num_messages):
            message_id = await rq_client.send_message(
                queue_name=test_queue_name,
                message_body=f"Message ordering test message {i}",
                message_attributes={
                    "test_key": test_key,
                    "sequence": i,
                }
            )
            message_ids.append(message_id)

            # Add a small random delay to simulate real-world conditions
            await asyncio.sleep(random.uniform(0.01, 0.05))

        logger.info(f"Sent {len(message_ids)} messages with sequence numbers")

        # Connect to Redis
        redis_conn = redis.from_url(settings.REDIS_URL)

        # Get the queue
        queue = Queue(name=test_queue_name, connection=redis_conn)

        # Get the job IDs in the queue
        job_ids = queue.get_job_ids()
        logger.info(f"Queue has {len(job_ids)} jobs")

        # Check if the jobs are in the correct order
        # Note: RQ doesn't guarantee message ordering, so this test is informational
        if job_ids:
            # Get the sequence numbers of the jobs
            sequences = []
            for job_id in job_ids:
                job = queue.fetch_job(job_id)
                if job and job.args and isinstance(job.args, dict):
                    sequence = job.args.get("sequence")
                    if sequence is not None:
                        sequences.append(sequence)

            # Check if the sequences are in order
            is_ordered = all(sequences[i] <= sequences[i+1] for i in range(len(sequences)-1))
            logger.info(f"Messages are in order: {is_ordered}")

            # RQ doesn't guarantee message ordering, so we'll consider this test passed
            # regardless of the result
            logger.info("Message ordering test passed (informational)")
            return True
        else:
            logger.info("No jobs found in queue, but test continues")
            return True

    except Exception as e:
        logger.error(f"Error in message ordering test: {str(e)}")
        return False


async def test_persistence() -> bool:
    """
    Test persistence by simulating a Redis restart using connection close and reconnect.

    Returns:
        bool: True if the test passes, False otherwise
    """
    logger.info("Testing persistence")

    try:
        # Create an RQ client
        rq_client = RQClient(redis_url=settings.REDIS_URL)

        # Generate a unique key for this test
        test_key = f"persistence-test-{uuid.uuid4()}"

        # Send a test message
        logger.info("Sending test message")
        message_id = await rq_client.send_message(
            queue_name="default",
            message_body="Persistence test message",
            message_attributes={
                "test_key": test_key,
            }
        )

        logger.info(f"Message sent with ID: {message_id}")

        # Wait for the message to be processed
        await asyncio.sleep(1)

        # Get the Redis connection
        redis_conn = redis.from_url(settings.REDIS_URL)

        # Check if the job exists before "restart"
        job_key = f"rq:job:{message_id}"
        job_exists_before = redis_conn.exists(job_key)
        logger.info(f"Job exists in Redis before 'restart': {job_exists_before}")

        # Simulate a restart by closing and reopening the connection
        logger.info("Simulating Redis restart by closing and reopening connection")
        redis_conn.close()

        # Wait a moment
        await asyncio.sleep(1)

        # Reconnect to Redis
        redis_conn = redis.from_url(settings.REDIS_URL)

        # Check if Redis is still responsive
        ping_response = redis_conn.ping()
        logger.info(f"Redis ping response after 'restart': {ping_response}")

        # Check if the job still exists
        job_exists_after = redis_conn.exists(job_key)
        logger.info(f"Job exists in Redis after 'restart': {job_exists_after}")

        if job_exists_after:
            logger.info("Persistence test passed: Job exists after 'restart'")
            return True
        else:
            logger.warning("Persistence test failed: Job does not exist after 'restart'")
            return False

    except Exception as e:
        logger.error(f"Error in persistence test: {str(e)}")
        return False


async def main() -> int:
    """
    Run all edge case tests.

    Returns:
        int: 0 if all tests pass, 1 otherwise
    """
    try:
        # Adjust test parameters based on whether we're in reduced load mode
        if REDUCED_LOAD:
            # Use smaller values for CI environment
            race_concurrent = 5
            ordering_messages = 10
        else:
            # Use normal values for local testing
            race_concurrent = 10
            ordering_messages = 20

        # Run race condition test
        race_result = await test_race_conditions(num_concurrent=race_concurrent)

        # Run message ordering test
        ordering_result = await test_message_ordering(num_messages=ordering_messages)

        # Run persistence test (no need to reduce load for this test)
        persistence_result = await test_persistence()

        # Print summary
        logger.info("Edge Case Test Summary:")
        logger.info(f"Race Conditions: {'PASSED' if race_result else 'FAILED'}")
        logger.info(f"Message Ordering: {'PASSED' if ordering_result else 'FAILED'}")
        logger.info(f"Persistence: {'PASSED' if persistence_result else 'FAILED'}")

        # Return 0 if all tests pass, 1 otherwise
        if race_result and ordering_result and persistence_result:
            logger.info("All edge case tests passed!")
            return 0
        else:
            logger.warning("Some edge case tests failed!")
            return 1

    except Exception as e:
        logger.error(f"Error in edge case tests: {str(e)}")
        return 1


if __name__ == "__main__":
    # Run the main function
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
