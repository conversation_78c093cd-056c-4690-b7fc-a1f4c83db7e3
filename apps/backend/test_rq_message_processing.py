#!/usr/bin/env python
"""
Test script to verify RQ message processing functionality.

This script tests the RQ worker's ability to process messages by:
1. Connecting to Redis directly
2. Inspecting the Redis queues
3. Verifying that messages can be enqueued and dequeued
"""

import asyncio
import logging
import os
import sys
import time
import uuid
from typing import List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath("src"))

# Import the necessary modules
import redis
from rq import Queue
from a2a_platform.config.settings import get_settings

# Get settings
settings = get_settings()


async def test_redis_connection() -> bool:
    """
    Test the connection to Redis.

    Returns:
        bool: True if the connection is successful, False otherwise
    """
    logger.info("Testing Redis connection")

    try:
        # Connect to Redis
        redis_conn = redis.from_url(settings.REDIS_URL)

        # Ping Redis to verify the connection
        response = redis_conn.ping()
        logger.info(f"Redis ping response: {response}")

        # Check if the response is True
        if response:
            logger.info("Redis connection successful")
            return True
        else:
            logger.error("Redis connection failed")
            return False

    except Exception as e:
        logger.error(f"Error connecting to Redis: {str(e)}")
        return False


async def test_queue_operations() -> bool:
    """
    Test basic queue operations (enqueue, dequeue).

    Returns:
        bool: True if the test passes, False otherwise
    """
    logger.info("Testing queue operations")

    try:
        # Connect to Redis
        redis_conn = redis.from_url(settings.REDIS_URL)

        # Create a test queue
        test_queue_name = f"test-queue-{uuid.uuid4()}"
        queue = Queue(name=test_queue_name, connection=redis_conn)

        # Create a test message
        test_message = {
            "id": str(uuid.uuid4()),
            "content": "Test message",
            "timestamp": time.time()
        }

        # Enqueue the message
        logger.info(f"Enqueuing message to {test_queue_name}")
        job = queue.enqueue(
            "test_func",  # This is just a placeholder function name
            test_message,
            job_id=test_message["id"]
        )

        # Verify the job was enqueued
        logger.info(f"Job ID: {job.id}")

        # Check if the job is in the queue
        job_ids = queue.get_job_ids()
        logger.info(f"Jobs in queue: {job_ids}")

        if job.id in job_ids:
            logger.info("Job successfully enqueued")
        else:
            logger.error("Job not found in queue")
            return False

        # Clean up
        job.delete()

        logger.info("Queue operations test passed")
        return True

    except Exception as e:
        logger.error(f"Error in queue operations test: {str(e)}")
        return False


async def test_worker_monitoring() -> bool:
    """
    Test monitoring of RQ workers.

    Returns:
        bool: True if the test passes, False otherwise
    """
    logger.info("Testing worker monitoring")

    try:
        # Connect to Redis
        redis_conn = redis.from_url(settings.REDIS_URL)

        # Get the list of workers
        from rq.worker import Worker
        workers = Worker.all(connection=redis_conn)

        # Log information about each worker
        logger.info(f"Found {len(workers)} workers")
        for i, worker in enumerate(workers):
            logger.info(f"Worker {i+1}: {worker.name}")
            logger.info(f"  Queues: {', '.join(q.name for q in worker.queues)}")
            logger.info(f"  State: {worker.state}")
            logger.info(f"  Birth date: {worker.birth_date}")

        # Check if there are any workers
        if len(workers) > 0:
            logger.info("Worker monitoring test passed")
            return True
        else:
            logger.warning("No workers found, but test continues")
            return True

    except Exception as e:
        logger.error(f"Error in worker monitoring test: {str(e)}")
        return False


async def test_queue_backlog() -> bool:
    """
    Test queue backlog handling.

    Returns:
        bool: True if the test passes, False otherwise
    """
    logger.info("Testing queue backlog handling")

    try:
        # Connect to Redis
        redis_conn = redis.from_url(settings.REDIS_URL)

        # Create a test queue
        test_queue_name = f"test-backlog-{uuid.uuid4()}"
        queue = Queue(name=test_queue_name, connection=redis_conn)

        # Enqueue multiple messages
        job_ids = []
        for i in range(5):
            test_message = {
                "id": str(uuid.uuid4()),
                "content": f"Backlog test message {i}",
                "timestamp": time.time()
            }

            job = queue.enqueue(
                "test_func",
                test_message,
                job_id=test_message["id"]
            )
            job_ids.append(job.id)

        # Verify all jobs are in the queue
        queue_job_ids = queue.get_job_ids()
        logger.info(f"Jobs in backlog queue: {len(queue_job_ids)}")

        # Check if all jobs are in the queue
        all_jobs_found = all(job_id in queue_job_ids for job_id in job_ids)

        # Clean up
        for job_id in job_ids:
            job = queue.fetch_job(job_id)
            if job:
                job.delete()

        if all_jobs_found:
            logger.info("Queue backlog test passed")
            return True
        else:
            logger.error("Not all jobs found in queue")
            return False

    except Exception as e:
        logger.error(f"Error in queue backlog test: {str(e)}")
        return False


async def main() -> int:
    """
    Run all tests.

    Returns:
        int: 0 if all tests pass, 1 otherwise
    """
    # Run the Redis connection test
    redis_test_result = await test_redis_connection()

    # Run the queue operations test
    queue_test_result = await test_queue_operations()

    # Run the worker monitoring test
    worker_test_result = await test_worker_monitoring()

    # Run the queue backlog test
    backlog_test_result = await test_queue_backlog()

    # Check if all tests passed
    if redis_test_result and queue_test_result and worker_test_result and backlog_test_result:
        logger.info("All tests passed!")
        return 0
    else:
        logger.error("Some tests failed!")
        return 1


if __name__ == "__main__":
    # Run the main function
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
