# Clerk Authentication Integration

This document explains how the VEDAVIVI application integrates with Clerk.com for user authentication, specifically focusing on the user registration/sign-up flow.

## Overview

VEDAVIVI uses Clerk.com as its authentication provider. The frontend uses Clerk.js for the user interface components and client-side SDK, while the backend uses the Clerk Python SDK for webhook processing and JWT validation.

## Architecture

```
┌─────────────┐       ┌──────────┐       ┌──────────────┐
│             │       │          │       │              │
│  Frontend   ├───────►  Clerk   ├───────►   Backend    │
│  (React)    │       │          │       │  (FastAPI)   │
│             │       └──────────┘       │              │
└─────────────┘            │             └──────┬───────┘
                           │                    │
                           │                    │
                           ▼                    ▼
                     ┌──────────┐         ┌──────────┐
                     │          │         │          │
                     │  User    │         │ VEDAVIVI │
                     │ Records  │         │ Database │
                     │          │         │          │
                     └──────────┘         └──────────┘
```

## User Registration Flow

1. A new user navigates to the VEDAVIVI sign-up page and chooses to sign up via Google, GitHub, or Email Magic Link.
2. The Clerk.js frontend components handle the authentication process with the selected provider.
3. Upon successful authentication, Clerk creates a new user in its system.
4. <PERSON> sends a webhook `user.created` event to VEDAVIVI's backend webhook endpoint.
5. The backend verifies the webhook signature using the Clerk SDK and the Clerk Webhook Signing Secret.
6. If the signature is valid, the backend extracts the user data from the webhook payload.
7. The backend creates a new user record in the VEDAVIVI PostgreSQL database, linking it to the Clerk user via the `clerk_user_id`.
8. For subsequent API requests, the frontend includes the Clerk-issued JWT in the Authorization header.
9. The backend verifies the JWT using the Clerk SDK and if valid, extracts the `clerk_user_id` to identify the user.

## Configuration

### Environment Variables

The following environment variables need to be set in the backend's `.env` file:

```plaintext
# Clerk Auth Settings
CLERK_API_KEY=your_clerk_api_key
CLERK_JWT_PUBLIC_KEY=your_clerk_jwt_public_key
CLERK_WEBHOOK_SECRET=your_clerk_webhook_secret
```

### Clerk Dashboard Configuration

1. **Create a Clerk Application**:
   - In the Clerk Dashboard, create a new application.
   - Configure the application to use the authentication methods you want to support (Google, GitHub, Email Magic Link).

2. **Configure Webhook**:
   - In the Clerk Dashboard, navigate to the "Webhooks" section.
   - Create a new webhook endpoint for the `user.created` event.
   - Set the endpoint URL to `https://your-api-domain.com/api/v1/webhooks/clerk`.
   - Note the Webhook Signing Secret and add it to your backend environment variables.

3. **JWT Settings**:
   - In the Clerk Dashboard, navigate to the "JWT Templates" section.
   - Configure the JWT claims according to your needs.
   - Copy the JWT public key and add it to your backend environment variables.

## Database Schema

The `users` table in the VEDAVIVI PostgreSQL database includes the following columns:

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    clerk_user_id VARCHAR(64) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    timezone VARCHAR(50) NULL,
    preferences JSONB NULL DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
```

## Security Considerations

1. **Webhook Signature Verification**:
   - All webhook requests from Clerk are verified using the Webhook Signing Secret.
   - Requests with invalid signatures are rejected.

2. **JWT Validation**:
   - All JWTs are validated using the Clerk SDK and the JWT public key.
   - The backend rejects any requests with invalid JWTs.

3. **Idempotency**:
   - The `user.created` event processing is designed to be idempotent to prevent duplicate user creation.

## Webhook Processing

VEDAVIVI handles Clerk webhooks for user lifecycle events using a standardized asynchronous pattern. This ensures reliable processing of events like `user.created` and `user.deleted`.

### Webhook Flow

```
┌──────────┐      ┌────────────┐      ┌───────────────┐      ┌─────────────────┐
│          │      │            │      │               │      │                 │
│  Clerk   ├─────►│ FastAPI    ├─────►│ Message Queue ├─────►│ Worker Process  │
│          │      │ Endpoint   │      │               │      │                 │
└──────────┘      └────────────┘      └───────────────┘      └────────┬────────┘
                                                                      │
                                                                      │
                                                                      ▼
                                                             ┌─────────────────┐
                                                             │                 │
                                                             │ Database        │
                                                             │                 │
                                                             └─────────────────┘
```

### Key Components

1. **Webhook Endpoint**: `/api/v1/webhooks/clerk`
   - Receives all Clerk webhook events
   - Verifies signatures using Svix library
   - Routes events based on type
   - Returns immediate 200 OK response after validation

2. **Event Validation**
   - Uses Pydantic models for strict payload validation
   - Models defined in `schemas/clerk_events.py`

3. **Asynchronous Processing**
   - Events are enqueued to worker tasks
   - `process_user_created_task` for `user.created` events
   - `process_user_deleted_task` for `user.deleted` events

4. **Idempotency**
   - Events are processed exactly once
   - Uses Redis to track processed events
   - Checks at both worker level and service level

### Handled Events

#### `user.created`
- Creates a new user record in VEDAVIVI's database
- Links the Clerk user ID to the internal user record
- Extracts and stores essential user information (email, name)
- Handles cases where the user already exists

#### `user.deleted`
- Completely removes all user data from VEDAVIVI's systems
- Deletes records from multiple tables:
  - users
  - assistants
  - chat_messages
  - file_metadata
  - assistant_objectives
  - tasks
  - external_credentials
  - cli_tokens
  - webhook_registrations
- Handles cases where the user doesn't exist

### Key Files

- **Webhook Handler**: `apps/backend/src/a2a_platform/api/rest/routes/webhooks.py`
- **Webhook Verification**: `apps/backend/src/a2a_platform/auth/clerk.py`
- **Pydantic Models**: `apps/backend/src/a2a_platform/schemas/clerk_events.py`
- **User Services**: `apps/backend/src/a2a_platform/services/user_service.py`
- **Worker Tasks**: `apps/backend/src/a2a_platform/workers/clerk_event_handlers.py`

### Security Considerations

- All webhooks are verified using Clerk's Svix signatures
- The webhook signing secret is stored securely in application settings
- Invalid signatures result in 401 Unauthorized responses
- Events with invalid payloads result in 400 Bad Request responses

## Testing

To manually test the user registration flow, use the E2E test script located at:
`/home/<USER>/workbook/a2a-platform/apps/backend/tests/e2e/manual_test_user_signup.sh`
