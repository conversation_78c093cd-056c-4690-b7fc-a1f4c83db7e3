#!/usr/bin/env python
"""
Test script to verify RQ client functionality directly.

This script tests the RQ client's ability to send messages to Redis.
"""

import asyncio
import logging
import os
import sys
import time
import uuid

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath("src"))

# Import the necessary modules
from a2a_platform.messaging.queue_factory import create_queue_client
from a2a_platform.messaging.rq_client import RQClient
from a2a_platform.config.settings import get_settings

# Get settings
settings = get_settings()


async def test_rq_client_direct() -> bool:
    """
    Test the RQ client directly.

    Returns:
        bool: True if the test passes, False otherwise
    """
    logger.info("Testing RQ client directly")

    try:
        # Create an RQ client
        rq_client = RQClient(redis_url=settings.REDIS_URL)
        logger.info("Created RQ client")

        # Generate a unique test message ID
        test_message_id = str(uuid.uuid4())
        test_message_body = f"Test message {test_message_id}"
        test_attributes = {"test_id": test_message_id, "timestamp": time.time()}

        # Send a test message to the default queue
        logger.info(f"Sending test message: {test_message_id}")
        message_id = await rq_client.send_message(
            queue_name="default",
            message_body=test_message_body,
            message_attributes=test_attributes
        )

        logger.info(f"Message sent with ID: {message_id}")

        # Wait a moment for the message to be processed
        logger.info("Waiting for message to be processed...")
        await asyncio.sleep(1)

        # Verify the message was sent by checking if it's in the queue
        queue = rq_client._get_queue("default")

        # Try multiple times to find the job
        max_attempts = 3
        for attempt in range(max_attempts):
            job_ids = queue.get_job_ids()
            logger.info(f"Job IDs in queue: {job_ids}")

            if message_id in job_ids:
                logger.info(f"Message found in queue on attempt {attempt+1}")

                # Get the job
                job = queue.fetch_job(message_id)
                if job:
                    logger.info(f"Job status: {job.get_status()}")
                    logger.info(f"Job args: {job.args}")

                    # Clean up
                    job.delete()
                    logger.info("Job deleted")

                logger.info("RQ client direct test passed")
                return True

            logger.info(f"Message not found in queue on attempt {attempt+1}, retrying...")
            await asyncio.sleep(1)

        # Check Redis directly
        logger.info("Checking Redis directly...")
        redis_conn = rq_client._redis_conn

        # Check if the job exists in Redis
        job_key = f"rq:job:{message_id}"
        job_exists = redis_conn.exists(job_key)
        logger.info(f"Job key {job_key} exists in Redis: {job_exists}")

        if job_exists:
            # Get job data from Redis
            job_data = redis_conn.hgetall(job_key)
            logger.info(f"Job data: {job_data}")

            # Check job status
            status = job_data.get(b'status', b'unknown').decode('utf-8')
            logger.info(f"Job status: {status}")

            # If the job exists and has a status, consider the test passed
            logger.info("Job exists in Redis, test passed")
            return True

        # List all keys in Redis related to jobs
        job_keys = redis_conn.keys("rq:job:*")
        logger.info(f"Total job keys in Redis: {len(job_keys)}")
        for key in job_keys:
            logger.info(f"Job key: {key}")

        # Check queues
        queue_keys = redis_conn.keys("rq:queue:*")
        logger.info(f"Queue keys in Redis: {queue_keys}")

        # Check the default queue
        default_queue_key = "rq:queue:default"
        queue_length = redis_conn.llen(default_queue_key)
        logger.info(f"Default queue length: {queue_length}")

        if queue_length > 0:
            queue_items = redis_conn.lrange(default_queue_key, 0, -1)
            logger.info(f"Default queue items: {queue_items}")

        logger.error("Message not found in queue after multiple attempts")
        return False

    except Exception as e:
        logger.error(f"Error in RQ client direct test: {str(e)}")
        return False


async def main() -> int:
    """
    Run the test.

    Returns:
        int: 0 if the test passes, 1 otherwise
    """
    # Run the RQ client direct test
    result = await test_rq_client_direct()

    if result:
        logger.info("Test passed!")
        return 0
    else:
        logger.error("Test failed!")
        return 1


if __name__ == "__main__":
    # Run the main function
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
