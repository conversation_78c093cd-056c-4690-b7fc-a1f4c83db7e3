[mypy]
# Python version and basic settings
python_version = 3.12
strict = True
warn_return_any = True
warn_unused_configs = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = False

# Import discovery
mypy_path = src
explicit_package_bases = True
namespace_packages = True

# Error output
show_error_codes = True
show_column_numbers = True
show_error_context = True
pretty = True

# Strictness settings
disallow_any_generics = True
disallow_any_unimported = True
disallow_any_expr = False
disallow_any_decorated = False
disallow_any_explicit = False
disallow_subclassing_any = True
disallow_untyped_calls = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
disallow_untyped_decorators = True
check_untyped_defs = True
no_implicit_optional = True
strict_optional = True

# Plugins
plugins = pydantic.mypy, strawberry.ext.mypy_plugin

# Third-party libraries without type stubs
[mypy-svix.*]
ignore_missing_imports = True

[mypy-rq.*]
ignore_missing_imports = True

[mypy-rq_scheduler.*]
ignore_missing_imports = True

[mypy-bleach.*]
ignore_missing_imports = True

[mypy-psycopg2.*]
ignore_missing_imports = True

[mypy-asyncpg.*]
ignore_missing_imports = True

[mypy-greenlet.*]
ignore_missing_imports = True

[mypy-alembic.*]
ignore_missing_imports = True

[mypy-clerk_backend_api.*]
ignore_missing_imports = True

[mypy-passlib.*]
ignore_missing_imports = True

[mypy-jose.*]
ignore_missing_imports = True

[mypy-tiktoken.*]
ignore_missing_imports = True

[mypy-typer.*]
ignore_missing_imports = True

[mypy-numpy.*]
ignore_missing_imports = True

[mypy-protobuf.*]
ignore_missing_imports = True

# Test files - less strict
[mypy-tests.*]
disallow_untyped_defs = False
disallow_incomplete_defs = False
disallow_untyped_calls = False
warn_return_any = False

# Scripts - less strict
[mypy-scripts.*]
disallow_untyped_defs = False
disallow_incomplete_defs = False
disallow_untyped_calls = False
warn_return_any = False

# Alembic migrations - ignore
[mypy-alembic.versions.*]
ignore_errors = True

# Cache and temporary files
[mypy-__pycache__.*]
ignore_errors = True

[mypy-*.pyc]
ignore_errors = True

# Development and debug files - handled by root level patterns

# Example files
[mypy-examples.*]
disallow_untyped_defs = False
disallow_incomplete_defs = False
warn_return_any = False
