[project]
name = "a2a-platform"
version = "0.1.0"
description = "A2A Platform Backend"
authors = []
dependencies = [
    "fastapi",
    "uvicorn[standard]",
    "strawberry-graphql[fastapi]>=0.270.5",
    "sqlalchemy>=2.0.41",
    "asyncpg>=0.27.0",
    "psycopg2-binary>=2.9.0",  # Added for PostgreSQL adapter
    "greenlet>=1.0.0",
    "alembic>=1.11.0",
    "pydantic[email]>=2.11.2",  # Prioritized for clerk-backend-api, with email validation
    "pydantic-settings",
    "redis>=4.5.0",
    "clerk-backend-api==2.2.0",
    "python-multipart>=0.0.9,<0.0.21",
    "python-jose[cryptography]",
    "passlib[bcrypt]",
    "httpx>=0.28.1",  # Updated to align with clerk-backend-api
    "numpy>=1.26.2",  # Adjusted for compatibility
    "protobuf>=3.20.0",  # Adjusted for compatibility
    "pytz>=2023.3.post1,<2026.0",
    "setuptools>=68.2.2,<71.0.0",
    "tiktoken>=0.5.1,<0.10.0",
    "typer[all]>=0.9.0,<0.17.0",
    "svix>=1.23.0",  # Added svix for webhook verification
    "rq>=2.3.3",  # Redis Queue for job processing
    "rq-scheduler>=0.13.1",  # Scheduler for Redis Queue
    "bleach>=6.0.0",  # HTML sanitization for security
    "pydantic-ai-slim[openai,anthropic,google,vertexai]>=0.2.14"  # Multi-provider LLM framework
]

[project.optional-dependencies]
dev = [
    "pytest>=7.3.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.3.0",
    "isort>=5.12.0",
    "mypy>=1.16.0",
    "ruff>=0.0.275",
    "types-python-jose",
    "psycopg2-binary>=2.9.9",  # Changed to psycopg2-binary for consistency
    "aiosqlite>=0.17.0"  # Added aiosqlite for async SQLite in tests
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/a2a_platform"]

[tool.pytest.ini_options]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"
testpaths = ["tests"]
python_paths = ["src"]
filterwarnings = [
    "error",
    "ignore::DeprecationWarning",
    "ignore::UserWarning",
]
markers = [
    "no_db: marks tests as database-free (pure business logic)",
    "fast_db: marks tests as SQLite-compatible (basic CRUD operations)",
    "slow: marks tests as requiring full PostgreSQL (complex operations)",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "e2e: marks tests as end-to-end tests",
    "performance: marks tests as performance tests",
    "bulk_data: marks tests that benefit from bulk data creation"
]
# Performance monitoring - set TEST_METRICS_FILE environment variable to save metrics
# Example: TEST_METRICS_FILE=test_metrics.json pytest tests/

[tool.black]
line-length = 88
target-version = ["py312"]

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.mypy]
python_version = "3.12"
strict = true
plugins = ["pydantic.mypy", "strawberry.ext.mypy_plugin"]
explicit_package_bases = true
mypy_path = "src"

[[tool.mypy.overrides]]
module = "svix.webhooks"
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = [
    "pydantic_ai",
    "pydantic_ai.*"
]
ignore_missing_imports = true

[tool.ruff]
target-version = "py312"

[tool.flake8]
max-line-length = 120
