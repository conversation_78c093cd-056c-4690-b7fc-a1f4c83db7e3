[tool:pytest]
# SQLite-specific pytest configuration for standalone testing
minversion = 6.0
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function
asyncio_default_test_loop_scope = function
testpaths = tests/integration
python_paths = src
addopts = -ra -q --tb=short --disable-warnings

# Properly register custom markers
markers =
    fast_db: marks tests as SQLite-compatible for fast execution
    no_db: marks tests as database-free
    slow: marks tests as slow running
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    e2e: marks tests as end-to-end tests

# Comprehensive warning filters for production-ready output
filterwarnings =
    ignore::DeprecationWarning
    ignore::UserWarning
    ignore::pytest.PytestUnknownMarkWarning
    ignore::pytest.PytestDeprecationWarning
    ignore::pytest_asyncio.plugin.PytestDeprecationWarning
    ignore:.*Support for class-based.*:pydantic._internal._config.PydanticDeprecatedSince20
    ignore:.*Valid config keys have changed.*:UserWarning
    ignore:.*asyncio test.*requested async.*:pytest_asyncio.plugin.PytestDeprecationWarning
    ignore:.*asyncio_default_fixture_loop_scope.*:pytest_asyncio.plugin.PytestDeprecationWarning

# Use SQLite conftest files
confcutdir = tests
# Load SQLite-specific conftest
python_files = conftest_sqlite.py test_*.py
