import asyncio
import os
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.orm import sessionmaker


async def check_db():
    # Get database URL from environment
    db_url = os.getenv("DATABASE_URL")
    if not db_url:
        raise ValueError("DATABASE_URL environment variable is not set. Please set it to proceed.")
    print(f"Connecting to database: {db_url}")

    # Create async engine
    engine = create_async_engine(db_url)

    try:
        # Test the connection
        async with engine.connect() as conn:
            print("Successfully connected to the database")

            # Check if tables exist
            result = await conn.execute(
                text("""
                SELECT table_name 
                FROM information_schema.tables
                WHERE table_schema = 'public'
                """)
            )
            tables = [row[0] for row in result]
            print(f"\nTables in the database: {tables}")

            # Check if alembic_version exists
            result = await conn.execute(
                text("""
                SELECT to_regclass('alembic_version')
                """)
            )
            alembic_exists = result.scalar() is not None
            print(f"Alembic version table exists: {alembic_exists}")

            if alembic_exists:
                result = await conn.execute(text("SELECT * FROM alembic_version"))
                version = result.scalar()
                print(f"Current Alembic version: {version}")

    except Exception as e:
        print(f"Error: {e}")
    finally:
        await engine.dispose()


if __name__ == "__main__":
    asyncio.run(check_db())
