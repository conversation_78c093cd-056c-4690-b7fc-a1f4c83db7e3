#!/usr/bin/env python3
"""
Standalone SQLite test that replicates test_assistant_database.py functionality
without any PostgreSQL dependencies.

This test demonstrates that all the database schema tests can run completely
independently using SQLite when PostgreSQL is down.

Run with: python test_sqlite_standalone_full.py
"""

import asyncio
import uuid
from datetime import datetime, timezone

import pytest
from sqlalchemy import (
    JSON,
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    Index,
    MetaData,
    String,
    Table,
    Text,
    create_engine,
    event,
    select,
    text,
)
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.pool import StaticPool
from sqlalchemy.sql import func


def create_sqlite_engine():
    """Create a SQLite engine for testing."""
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        poolclass=StaticPool,
        connect_args={
            "check_same_thread": False,
        },
        echo=False,
    )

    # Enable foreign key constraints for async engine
    @event.listens_for(engine.sync_engine, "connect")
    def set_sqlite_pragma_async(dbapi_connection, connection_record):
        cursor = dbapi_connection.cursor()
        cursor.execute("PRAGMA foreign_keys=ON")
        cursor.close()

    return engine


def create_tables(connection):
    """Create SQLite-compatible tables."""
    sqlite_metadata = MetaData()

    # Define SQLite-compatible User table
    users_table = Table(
        "users",
        sqlite_metadata,
        Column("id", String(36), primary_key=True),
        Column("clerk_user_id", String(64), unique=True, nullable=False),
        Column("email", String(255), unique=True, nullable=False),
        Column("timezone", String(64), nullable=True, server_default=text("'UTC'")),
        Column("preferences", JSON, nullable=True, server_default=text("'{}'")),
        Column(
            "is_pa_setup_complete",
            Boolean,
            nullable=False,
            default=False,
            server_default=text("0"),
        ),
        Column(
            "created_at",
            DateTime(timezone=True),
            nullable=False,
            server_default=func.current_timestamp(),
        ),
        Column(
            "updated_at",
            DateTime(timezone=True),
            nullable=False,
            server_default=func.current_timestamp(),
        ),
    )

    # Define SQLite-compatible Assistant table
    assistants_table = Table(
        "assistants",
        sqlite_metadata,
        Column("id", String(36), primary_key=True),
        Column(
            "user_id",
            String(36),
            ForeignKey("users.id", ondelete="CASCADE"),
            nullable=False,
            unique=True,
        ),
        Column("name", String(255), nullable=False),
        Column("backstory", Text, nullable=False),
        Column("avatar_file_id", String(36), nullable=True),
        Column("configuration", JSON, nullable=True),
        Column(
            "created_at",
            DateTime(timezone=True),
            nullable=False,
            server_default=func.current_timestamp(),
        ),
        Column(
            "updated_at",
            DateTime(timezone=True),
            nullable=False,
            server_default=func.current_timestamp(),
        ),
    )

    # Create indexes
    Index("ix_assistants_user_id", assistants_table.c.user_id)

    # Create all tables
    sqlite_metadata.create_all(connection)
    return sqlite_metadata, users_table, assistants_table


class TestAssistantDatabaseSchema:
    """Standalone SQLite version of TestAssistantDatabaseSchema tests."""

    async def test_assistants_table_schema(self):
        """Test that the assistants table has the correct schema and indexes."""
        print("🧪 Testing assistants table schema...")
        
        engine = create_sqlite_engine()
        
        async with engine.begin() as conn:
            sqlite_metadata, users_table, assistants_table = await conn.run_sync(create_tables)
        
        # Verify table exists and has correct columns
        async with engine.begin() as conn:
            # Check if table exists
            result = await conn.execute(
                text("SELECT name FROM sqlite_master WHERE type='table' AND name='assistants'")
            )
            table_exists = result.fetchone() is not None
            assert table_exists, "Assistants table should exist"
            
            # Check table schema
            result = await conn.execute(text("PRAGMA table_info(assistants)"))
            columns = result.fetchall()
            column_names = [col[1] for col in columns]
            
            expected_columns = [
                "id", "user_id", "name", "backstory", "avatar_file_id", 
                "configuration", "created_at", "updated_at"
            ]
            for col in expected_columns:
                assert col in column_names, f"Column {col} should exist in assistants table"
            
            # Check indexes
            result = await conn.execute(text("PRAGMA index_list(assistants)"))
            indexes = result.fetchall()
            index_names = [idx[1] for idx in indexes]
            assert "ix_assistants_user_id" in index_names, "Index on user_id should exist"
        
        await engine.dispose()
        print("✅ Assistants table schema test passed")

    async def test_user_id_foreign_key_constraint(self):
        """Test that user_id foreign key constraint is enforced."""
        print("🧪 Testing user_id foreign key constraint...")
        
        engine = create_sqlite_engine()
        
        async with engine.begin() as conn:
            await conn.run_sync(create_tables)
        
        async_session_maker = async_sessionmaker(
            engine, class_=AsyncSession, expire_on_commit=False
        )
        
        async with async_session_maker() as session:
            # Try to insert assistant with non-existent user_id
            try:
                await session.execute(
                    text("""
                        INSERT INTO assistants (id, user_id, name, backstory)
                        VALUES (:id, :user_id, :name, :backstory)
                    """),
                    {
                        "id": str(uuid.uuid4()),
                        "user_id": str(uuid.uuid4()),  # Non-existent user
                        "name": "Test Assistant",
                        "backstory": "Test backstory",
                    },
                )
                await session.commit()
                assert False, "Should have raised foreign key constraint error"
            except Exception as e:
                assert "FOREIGN KEY constraint failed" in str(e), f"Expected foreign key error, got: {e}"
                await session.rollback()
        
        await engine.dispose()
        print("✅ Foreign key constraint test passed")

    async def test_user_id_unique_constraint(self):
        """Test that user_id unique constraint is enforced."""
        print("🧪 Testing user_id unique constraint...")
        
        engine = create_sqlite_engine()
        
        async with engine.begin() as conn:
            await conn.run_sync(create_tables)
        
        async_session_maker = async_sessionmaker(
            engine, class_=AsyncSession, expire_on_commit=False
        )
        
        async with async_session_maker() as session:
            # Create a user first
            user_id = str(uuid.uuid4())
            await session.execute(
                text("""
                    INSERT INTO users (id, clerk_user_id, email)
                    VALUES (:id, :clerk_user_id, :email)
                """),
                {
                    "id": user_id,
                    "clerk_user_id": f"test_{uuid.uuid4()}",
                    "email": f"test_{uuid.uuid4()}@example.com",
                },
            )
            
            # Create first assistant
            await session.execute(
                text("""
                    INSERT INTO assistants (id, user_id, name, backstory)
                    VALUES (:id, :user_id, :name, :backstory)
                """),
                {
                    "id": str(uuid.uuid4()),
                    "user_id": user_id,
                    "name": "First Assistant",
                    "backstory": "First backstory",
                },
            )
            await session.commit()
            
            # Try to create second assistant for same user (should fail)
            try:
                await session.execute(
                    text("""
                        INSERT INTO assistants (id, user_id, name, backstory)
                        VALUES (:id, :user_id, :name, :backstory)
                    """),
                    {
                        "id": str(uuid.uuid4()),
                        "user_id": user_id,  # Same user
                        "name": "Second Assistant",
                        "backstory": "Second backstory",
                    },
                )
                await session.commit()
                assert False, "Should have raised unique constraint error"
            except Exception as e:
                assert "UNIQUE constraint failed" in str(e), f"Expected unique constraint error, got: {e}"
                await session.rollback()
        
        await engine.dispose()
        print("✅ Unique constraint test passed")

    async def test_name_not_null_constraint(self):
        """Test that name NOT NULL constraint is enforced."""
        print("🧪 Testing name NOT NULL constraint...")
        
        engine = create_sqlite_engine()
        
        async with engine.begin() as conn:
            await conn.run_sync(create_tables)
        
        async_session_maker = async_sessionmaker(
            engine, class_=AsyncSession, expire_on_commit=False
        )
        
        async with async_session_maker() as session:
            # Create a user first
            user_id = str(uuid.uuid4())
            await session.execute(
                text("""
                    INSERT INTO users (id, clerk_user_id, email)
                    VALUES (:id, :clerk_user_id, :email)
                """),
                {
                    "id": user_id,
                    "clerk_user_id": f"test_{uuid.uuid4()}",
                    "email": f"test_{uuid.uuid4()}@example.com",
                },
            )
            await session.commit()
            
            # Try to create assistant with NULL name
            try:
                await session.execute(
                    text("""
                        INSERT INTO assistants (id, user_id, name, backstory)
                        VALUES (:id, :user_id, :name, :backstory)
                    """),
                    {
                        "id": str(uuid.uuid4()),
                        "user_id": user_id,
                        "name": None,  # NULL name
                        "backstory": "Test backstory",
                    },
                )
                await session.commit()
                assert False, "Should have raised NOT NULL constraint error"
            except Exception as e:
                assert "NOT NULL constraint failed" in str(e), f"Expected NOT NULL error, got: {e}"
                await session.rollback()
        
        await engine.dispose()
        print("✅ Name NOT NULL constraint test passed")

    async def test_backstory_not_null_constraint(self):
        """Test that backstory NOT NULL constraint is enforced."""
        print("🧪 Testing backstory NOT NULL constraint...")
        
        engine = create_sqlite_engine()
        
        async with engine.begin() as conn:
            await conn.run_sync(create_tables)
        
        async_session_maker = async_sessionmaker(
            engine, class_=AsyncSession, expire_on_commit=False
        )
        
        async with async_session_maker() as session:
            # Create a user first
            user_id = str(uuid.uuid4())
            await session.execute(
                text("""
                    INSERT INTO users (id, clerk_user_id, email)
                    VALUES (:id, :clerk_user_id, :email)
                """),
                {
                    "id": user_id,
                    "clerk_user_id": f"test_{uuid.uuid4()}",
                    "email": f"test_{uuid.uuid4()}@example.com",
                },
            )
            await session.commit()
            
            # Try to create assistant with NULL backstory
            try:
                await session.execute(
                    text("""
                        INSERT INTO assistants (id, user_id, name, backstory)
                        VALUES (:id, :user_id, :name, :backstory)
                    """),
                    {
                        "id": str(uuid.uuid4()),
                        "user_id": user_id,
                        "name": "Test Assistant",
                        "backstory": None,  # NULL backstory
                    },
                )
                await session.commit()
                assert False, "Should have raised NOT NULL constraint error"
            except Exception as e:
                assert "NOT NULL constraint failed" in str(e), f"Expected NOT NULL error, got: {e}"
                await session.rollback()
        
        await engine.dispose()
        print("✅ Backstory NOT NULL constraint test passed")

    async def test_default_timestamps(self):
        """Test that created_at and updated_at have proper defaults and behavior."""
        print("🧪 Testing default timestamps...")
        
        engine = create_sqlite_engine()
        
        async with engine.begin() as conn:
            await conn.run_sync(create_tables)
        
        async_session_maker = async_sessionmaker(
            engine, class_=AsyncSession, expire_on_commit=False
        )
        
        async with async_session_maker() as session:
            # Create a user first
            user_id = str(uuid.uuid4())
            await session.execute(
                text("""
                    INSERT INTO users (id, clerk_user_id, email)
                    VALUES (:id, :clerk_user_id, :email)
                """),
                {
                    "id": user_id,
                    "clerk_user_id": f"test_{uuid.uuid4()}",
                    "email": f"test_{uuid.uuid4()}@example.com",
                },
            )
            
            # Create assistant without specifying timestamps
            assistant_id = str(uuid.uuid4())
            await session.execute(
                text("""
                    INSERT INTO assistants (id, user_id, name, backstory)
                    VALUES (:id, :user_id, :name, :backstory)
                """),
                {
                    "id": assistant_id,
                    "user_id": user_id,
                    "name": "Timestamp Test Assistant",
                    "backstory": "Testing timestamps",
                },
            )
            await session.commit()
            
            # Verify timestamps were set
            result = await session.execute(
                text("SELECT created_at, updated_at FROM assistants WHERE id = :id"),
                {"id": assistant_id}
            )
            row = result.fetchone()
            assert row is not None, "Assistant should exist"
            
            created_at, updated_at = row
            assert created_at is not None, "created_at should be set automatically"
            assert updated_at is not None, "updated_at should be set automatically"
            
            # Parse timestamps (SQLite returns strings)
            if isinstance(created_at, str):
                created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
            if isinstance(updated_at, str):
                updated_at = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
            
            # Verify timestamps are recent (within last minute)
            now = datetime.now(timezone.utc)
            time_diff = abs((now - created_at.replace(tzinfo=timezone.utc)).total_seconds())
            assert time_diff < 60, f"created_at should be recent, but was {time_diff}s ago"
        
        await engine.dispose()
        print("✅ Default timestamps test passed")


async def run_all_tests():
    """Run all SQLite database schema tests."""
    print("🚀 Starting SQLite Database Schema Tests")
    print("=" * 50)
    
    test_instance = TestAssistantDatabaseSchema()
    
    tests = [
        test_instance.test_assistants_table_schema,
        test_instance.test_user_id_foreign_key_constraint,
        test_instance.test_user_id_unique_constraint,
        test_instance.test_name_not_null_constraint,
        test_instance.test_backstory_not_null_constraint,
        test_instance.test_default_timestamps,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            await test()
            passed += 1
        except Exception as e:
            print(f"❌ {test.__name__} failed: {e}")
            failed += 1
    
    print("=" * 50)
    print(f"🎯 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All SQLite database schema tests passed!")
        print("✅ SQLite can fully replace PostgreSQL for these tests")
        return True
    else:
        print("❌ Some tests failed")
        return False


if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    exit(0 if success else 1)
