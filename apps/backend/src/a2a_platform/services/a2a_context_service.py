"""
Service for creating and validating A2A context objects.

This module provides utilities for working with A2A context, including
creating user context objects, creating A2A messages, and extracting
user context from A2A payloads.
"""

import logging
from datetime import UTC, datetime
from typing import Any, Dict, Optional

from a2a_platform.schemas.a2a_context import A2AMessage, UserContext

logger = logging.getLogger(__name__)


class A2AContextService:
    """
    Service for creating and validating A2A context objects.
    """

    @staticmethod
    def create_user_context(user_id: str, initiating_agent_id: str) -> UserContext:
        """
        Create a user context object with the given user_id and initiating_agent_id.

        Args:
            user_id: The unique identifier of the end-user
            initiating_agent_id: The identifier of the agent initiating the request

        Returns:
            UserContext: A user context object
        """

        # Log with redacted IDs for privacy
        user_id_redacted = (
            f"{user_id[:4]}...{user_id[-4:]}" if len(user_id) > 8 else "***"
        )
        agent_id_redacted = (
            f"{initiating_agent_id[:4]}...{initiating_agent_id[-4:]}"
            if len(initiating_agent_id) > 8
            else "***"
        )

        logger.debug(
            f"Creating user context with user_id={user_id_redacted}, initiating_agent_id={agent_id_redacted}"
        )
        return UserContext(
            user_id=user_id,
            initiating_agent_id=initiating_agent_id,
            request_timestamp=datetime.now(UTC),
        )

    @staticmethod
    def create_a2a_message(
        user_id: str, initiating_agent_id: str, payload: Dict[str, Any]
    ) -> A2AMessage:
        """
        Create an A2A message with the given user_id, initiating_agent_id, and payload.

        Args:
            user_id: The unique identifier of the end-user
            initiating_agent_id: The identifier of the agent initiating the request
            payload: The actual payload of the A2A message

        Returns:
            A2AMessage: An A2A message object
        """
        user_context = A2AContextService.create_user_context(
            user_id, initiating_agent_id
        )
        return A2AMessage(user_context=user_context, payload=payload)

    @staticmethod
    def extract_user_context(
        a2a_message: Dict[str, Any],
    ) -> tuple[Optional[UserContext], Optional[str]]:
        """
        Extract the user context from an A2A message.

        Args:
            a2a_message: The A2A message dictionary

        Returns:
            tuple: (UserContext object or None, Error message or None)
            - If successful, returns (UserContext, None)
            - If unsuccessful, returns (None, error_message)
        """
        try:
            if "user_context" not in a2a_message:
                error_msg = "No user_context found in A2A message"
                logger.error(error_msg)
                return None, error_msg

            user_context_dict = a2a_message["user_context"]

            # For non-dict user_context, handle as a special case
            if not isinstance(user_context_dict, dict):
                error_msg = "Error extracting user context from A2A message: user_context is not a dictionary"
                logger.error(error_msg)
                return None, error_msg

            # Check for required fields before validation
            missing_fields = []
            for field in ["user_id", "initiating_agent_id"]:
                if field not in user_context_dict:
                    missing_fields.append(field)

            if missing_fields:
                error_msg = f"Missing required fields in user_context: {', '.join(missing_fields)}"
                logger.error(error_msg)
                return None, error_msg

            try:
                user_context = UserContext.model_validate(user_context_dict)
                return user_context, None
            except Exception as validation_error:
                error_msg = f"Validation error in user_context: {str(validation_error)}"
                logger.error(error_msg)
                return None, error_msg

        except Exception as e:
            error_msg = f"Error extracting user context from A2A message: {str(e)}"
            logger.error(error_msg)
            return None, error_msg
