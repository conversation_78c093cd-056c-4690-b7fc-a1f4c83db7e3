import logging
import warnings
from typing import Any, Optional, Union
from uuid import UUID

from sqlalchemy import Column, MetaData, String, Table, func, select
from sqlalchemy.exc import IntegrityError, NoResultFound, SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm.attributes import flag_modified

from a2a_platform.db.models.user import User
from a2a_platform.schemas.clerk_events import UserCreatedData
from a2a_platform.schemas.user import UserCreate

# Set up logger
logger = logging.getLogger(__name__)

# Suppress the specific RuntimeWarning from Clerk SDK's internal async client
# This warning comes from the Clerk SDK's internal async client management
# and is not something we can control from our code
warnings.filterwarnings(
    "ignore",
    message="coroutine 'AsyncClient.aclose' was never awaited",
    category=RuntimeWarning,
    module="clerk_backend_api.httpclient",
)


async def fetch_user_from_clerk_api(
    clerk_user_id: str,
) -> Optional[dict[str, Any]]:
    """
    Fetch user data from Clerk API using proper async context management.

    Args:
        clerk_user_id: Clerk user ID to fetch

    Returns:
        User data from Clerk API or None if not found
    """
    import warnings

    try:
        from a2a_platform.auth.clerk import AsyncClerkClient

        logger.info(f"Fetching user {clerk_user_id} from Clerk API...")

        # Suppress the specific RuntimeWarning from Clerk SDK
        # This warning comes from the Clerk SDK's internal async client management
        with warnings.catch_warnings():
            warnings.filterwarnings(
                "ignore",
                message="coroutine 'AsyncClient.aclose' was never awaited",
                category=RuntimeWarning,
            )

            # Use async context manager to ensure proper cleanup
            client: AsyncClerkClient = AsyncClerkClient()
            async with client as clerk_client:
                # Use the Clerk client to get user data
                clerk_user = clerk_client.users.get(user_id=clerk_user_id)

                if not clerk_user:
                    logger.warning(f"User {clerk_user_id} not found in Clerk")
                    return None

                # Convert the Clerk user object to a dictionary
                user_dict = {
                    "id": clerk_user.id,
                    "email_addresses": [
                        {
                            "id": email.id,
                            "email_address": email.email_address,
                            "verified": getattr(email, "verified", False),
                        }
                        for email in (clerk_user.email_addresses or [])
                    ],
                    "first_name": clerk_user.first_name,
                    "last_name": clerk_user.last_name,
                    "public_metadata": clerk_user.public_metadata or {},
                    "created_at": (
                        str(clerk_user.created_at) if clerk_user.created_at else None
                    ),
                    "updated_at": (
                        str(clerk_user.updated_at) if clerk_user.updated_at else None
                    ),
                }

                logger.info(f"Successfully fetched user {clerk_user_id} from Clerk API")
                return user_dict

    except Exception as e:
        logger.error(f"Error fetching user {clerk_user_id} from Clerk API: {str(e)}")
        return None


async def sync_user_from_clerk(db: AsyncSession, clerk_user_id: str) -> Optional[User]:
    """
    Fetch user data from Clerk API and create user in local database.
    Uses timeout to prevent long waits.

    Args:
        db: Database session
        clerk_user_id: Clerk user ID to sync

    Returns:
        User object if successfully synced, None otherwise
    """
    import asyncio

    try:
        # Add timeout to prevent long waits (10 seconds max)
        clerk_user_data = await asyncio.wait_for(
            fetch_user_from_clerk_api(clerk_user_id), timeout=10.0
        )

        if not clerk_user_data:
            logger.warning(f"Could not fetch user {clerk_user_id} from Clerk API")
            return None

        # Convert Clerk API response to UserCreatedData format for consistency
        email_addresses = []
        for email_info in clerk_user_data.get("email_addresses", []):
            email_addresses.append(
                {
                    "id": email_info["id"],
                    "email_address": email_info["email_address"],
                    "verified": email_info.get("verified", False),
                }
            )

        # Create UserCreatedData object
        user_created_data = UserCreatedData(
            id=clerk_user_data["id"],
            email_addresses=email_addresses,
            first_name=clerk_user_data.get("first_name"),
            last_name=clerk_user_data.get("last_name"),
            public_metadata=clerk_user_data.get("public_metadata", {}),
            created_at=clerk_user_data.get("created_at", ""),
            updated_at=clerk_user_data.get("updated_at", ""),
        )

        # Use existing function to create user from Clerk data
        user = await create_user_from_clerk_event(db, user_created_data)
        if user:
            logger.info(f"Successfully synced user {clerk_user_id} from Clerk API")
        else:
            logger.error(f"Failed to create user {clerk_user_id} in local database")

        return user

    except asyncio.TimeoutError:
        logger.error(f"Timeout syncing user {clerk_user_id} from Clerk API (10s)")
        return None
    except Exception as e:
        logger.error(f"Error syncing user {clerk_user_id} from Clerk: {str(e)}")
        return None


async def get_user_by_clerk_id(db: AsyncSession, clerk_user_id: str) -> Optional[User]:
    """
    Get a user from the database by clerk_user_id.
    If not found locally, attempt to sync from Clerk API.

    Args:
        db: Database session
        clerk_user_id: Clerk user ID to search for

    Returns:
        User object if found or synced, None otherwise
    """
    # Log details for debugging
    logger.info(f"Looking up user with clerk_user_id: {clerk_user_id}")

    # First try exact match
    result = await db.execute(select(User).filter(User.clerk_user_id == clerk_user_id))
    user = result.scalar_one_or_none()

    if user:
        logger.info(f"Found user with exact clerk_user_id match: {user.id}")
        return user

    # If not found, try case-insensitive match for debugging
    logger.info(
        f"No exact match for clerk_user_id: {clerk_user_id}. Trying case-insensitive search..."
    )

    # PostgreSQL ILIKE for case-insensitive matching
    result = await db.execute(
        select(User).filter(func.lower(User.clerk_user_id) == func.lower(clerk_user_id))
    )
    user = result.scalar_one_or_none()

    if user:
        logger.info(f"Found user with case-insensitive clerk_user_id match: {user.id}")
        # Update to correct case for future lookups
        user.clerk_user_id = clerk_user_id
        await db.commit()
        logger.info(f"Updated user clerk_user_id to correct case: {clerk_user_id}")
        return user

    # Not found locally - attempt to sync from Clerk API
    logger.info(
        f"User {clerk_user_id} not found locally, attempting to sync from Clerk API..."
    )

    synced_user = await sync_user_from_clerk(db, clerk_user_id)
    if synced_user:
        logger.info(
            f"Successfully synced and created user {clerk_user_id} from Clerk API"
        )
        return synced_user

    # Only log this warning if we've tried everything
    logger.warning(
        f"User {clerk_user_id} not found locally and could not be synced from Clerk API"
    )
    return None


async def create_user(db: AsyncSession, user_data: UserCreate) -> User:
    """
    Create a new user in the database

    Args:
        db: Database session
        user_data: User creation data

    Returns:
        The newly created user

    Raises:
        IntegrityError: If a user with the same clerk_user_id already exists
    """
    db_user = User(
        clerk_user_id=user_data.clerk_user_id,
        email=user_data.email,
        timezone=user_data.timezone,
        preferences={},
    )
    db.add(db_user)
    await db.commit()
    await db.refresh(db_user)
    return db_user


async def create_user_from_clerk_event(
    db_session: AsyncSession, clerk_user_data: UserCreatedData
) -> Optional[User]:
    """
    Create a new user from a Clerk user.created event.

    This function is idempotent - if the user already exists, it will return the existing user.
    It handles race conditions by catching IntegrityError and returning the existing user.

    Args:
        db_session: Database session
        clerk_user_data: Data from the Clerk user.created event

    Returns:
        The created or existing user, or None if an error occurred
    """
    try:
        # Check if user already exists
        existing_user = await get_user_by_clerk_id(db_session, clerk_user_data.id)
        if existing_user:
            logger.info(
                f"User with clerk_user_id {clerk_user_data.id} already exists, returning existing user"
            )
            return existing_user

        # Extract primary email (verified first, then any)
        email = _extract_primary_email(clerk_user_data)

        # Create new user
        user = User(
            clerk_user_id=clerk_user_data.id,
            email=email,
            preferences={},  # Initialize with empty preferences
        )

        # Store optional fields in preferences if present
        preferences = {}
        if clerk_user_data.first_name:
            preferences["first_name"] = clerk_user_data.first_name
        if clerk_user_data.last_name:
            preferences["last_name"] = clerk_user_data.last_name

        if preferences:
            user.preferences = preferences

        # Save to database
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)

        logger.info(f"Created new user with clerk_user_id {clerk_user_data.id}")
        return user
    except IntegrityError as e:
        # This could happen in a race condition where another process created the user
        # between our check and commit
        logger.warning(
            f"IntegrityError when creating user with clerk_user_id {clerk_user_data.id}: {str(e)}"
        )
        await db_session.rollback()

        # Try to get the user again
        existing_user = await get_user_by_clerk_id(db_session, clerk_user_data.id)
        if existing_user:
            logger.info(
                f"User with clerk_user_id {clerk_user_data.id} was created by another process"
            )
            return existing_user

        # If we still can't find the user, re-raise the exception
        raise
    except Exception as e:
        # Log and handle any other exceptions
        logger.error(
            f"Error creating user with clerk_user_id {clerk_user_data.id}: {str(e)}"
        )
        await db_session.rollback()
        return None


def _extract_primary_email(clerk_user_data: UserCreatedData) -> str:
    """
    Extract the primary email from the Clerk user data.
    Prioritizes verified emails over unverified ones.

    Args:
        clerk_user_data: Data from the Clerk user.created event

    Returns:
        The primary email address

    Raises:
        ValueError: If no email address is available
    """
    if not clerk_user_data.email_addresses:
        raise ValueError(f"No email addresses available for user {clerk_user_data.id}")

    # First, try to find a verified email
    for email_info in clerk_user_data.email_addresses:
        if email_info.verified:
            return email_info.email_address

    # If no verified email, use the first one available
    return clerk_user_data.email_addresses[0].email_address


async def update_user_profile(
    db: AsyncSession, clerk_user_id: str, timezone: str
) -> User:
    """
    Update a user's profile in the database

    Args:
        db: Database session
        clerk_user_id: Clerk user ID to identify the user
        timezone: New timezone value to set

    Returns:
        The updated user object if found and updated, None otherwise

    Raises:
        NoResultFound: If no user with the given clerk_user_id exists
    """
    # Get the user by clerk_user_id
    user = await get_user_by_clerk_id(db, clerk_user_id)
    if not user:
        raise NoResultFound(f"User with clerk_user_id {clerk_user_id} not found")

    # Update the user's timezone
    user.timezone = timezone
    await db.commit()
    await db.refresh(user)
    return user


async def update_user_preferences(
    db: AsyncSession,
    *,
    clerk_user_id: str,
    preferences_update: dict[str, Any],
    timezone_update: Optional[str] = None,
) -> User:
    """
    Update a user's preferences and optionally timezone in the database.

    This function performs a deep merge of the provided preferences with the
    existing preferences. If a key exists in both, the new value will replace
    the old one. If the key only exists in the existing preferences, it will
    be preserved.

    Args:
        db: Database session
        clerk_user_id: Clerk user ID to identify the user
        preferences_update: Dictionary of preferences to update or add
        timezone_update: Optional new timezone value to set

    Returns:
        The updated user object if found and updated

    Raises:
        NoResultFound: If no user with the given clerk_user_id exists
        ValueError: If timezone_update is provided but invalid
    """
    # Get the user by clerk_user_id
    user = await get_user_by_clerk_id(db, clerk_user_id)
    if not user:
        raise NoResultFound(f"User with clerk_user_id {clerk_user_id} not found")

    # Log the input values for debugging
    logger.info(f"Updating preferences for user {clerk_user_id}")
    logger.info(f"Current preferences: {user.preferences}")
    logger.info(f"Preferences update: {preferences_update}")
    logger.info(f"Timezone update: {timezone_update}")

    # Update timezone if provided
    if timezone_update is not None:
        # TODO: Add timezone validation if needed
        user.timezone = timezone_update

    # Update preferences with deep merge
    logger.debug(f"[A2A DEBUG] Preferences BEFORE merge: {user.preferences}")
    logger.debug(f"[A2A DEBUG] Preferences to merge: {preferences_update}")

    # Handle case where preferences might be None (null in database)
    if user.preferences is None:
        user.preferences = {}

    # Perform deep merge of preferences
    _deep_merge_preferences(user.preferences, preferences_update)
    # Explicitly flag the 'preferences' attribute as modified
    flag_modified(user, "preferences")
    logger.debug(f"[A2A DEBUG] Preferences AFTER merge and flagged: {user.preferences}")

    # Save changes
    await db.commit()
    logger.debug(
        f"[A2A DEBUG] Preferences after commit (should be same as after merge): {user.preferences}"
    )
    await db.refresh(user)
    logger.debug(f"[A2A DEBUG] Preferences after refresh: {user.preferences}")

    return user


async def get_user_preferences(
    db: AsyncSession, *, clerk_user_id: str
) -> Optional[dict[str, Any]]:
    """
    Get a user's preferences and timezone

    Args:
        db: Database session
        clerk_user_id: Clerk user ID to identify the user

    Returns:
        Dictionary containing preferences and timezone, or None if user not found
    """
    # Get the user by clerk_user_id
    user = await get_user_by_clerk_id(db, clerk_user_id)
    if not user:
        return None

    # Create result dictionary with preferences and timezone
    result = {"preferences": user.preferences or {}, "timezone": user.timezone}

    return result


def _deep_merge_preferences(target: dict[str, Any], source: dict[str, Any]) -> None:
    """
    Helper function to perform a deep merge of two dictionaries.

    The source dictionary is merged into the target dictionary.
    If a key exists in both dictionaries and both values are dictionaries,
    the merge is performed recursively. Otherwise, the value from the source
    dictionary overwrites the value in the target dictionary.

    This function modifies the target dictionary in-place.

    Args:
        target: The target dictionary to merge into (modified in-place)
        source: The source dictionary to merge from
    """
    for key, value in source.items():
        if key in target and isinstance(target[key], dict) and isinstance(value, dict):
            # If both values are dictionaries, merge them recursively
            _deep_merge_preferences(target[key], value)
        else:
            # Otherwise, overwrite the value in the target dictionary
            target[key] = value


async def delete_all_user_data(db_session: AsyncSession, clerk_user_id: str) -> bool:
    """
    Delete all data associated with a user identified by clerk_user_id.

    This function removes all user data from the system, including:
    - assistants
    - chat_messages
    - file_metadata
    - assistant_objectives
    - tasks
    - external_credentials
    - cli_tokens
    - webhook_registrations
    - users (the user record itself)

    Args:
        db_session: Database session
        clerk_user_id: Clerk user ID

    Returns:
        True if user was found and deleted, False if user was not found
    """
    # Find the user by clerk_user_id
    user = await get_user_by_clerk_id(db_session, clerk_user_id)

    if not user:
        logger.info(
            f"User with clerk_user_id {clerk_user_id} not found, nothing to delete"
        )
        return False

    user_id = user.id  # We might still need user_id for _delete_related_records if they don't use the user object
    logger.info(
        f"Deleting all data for user {user_id} (clerk_user_id: {clerk_user_id})"
    )

    try:
        # Note: The exact deletion order might need to be adjusted
        # based on foreign key constraints in the database schema

        # Delete webhook_registrations
        await _delete_related_records(
            db_session,
            "webhook_registrations",
            ALLOWED_TABLES_CONFIG["webhook_registrations"]["user_id_column"],
            user_id,
            "webhook registrations",
        )

        await _delete_related_records(
            db_session,
            "external_credentials",
            ALLOWED_TABLES_CONFIG["external_credentials"]["user_id_column"],
            user_id,
            "external credentials",
        )

        await _delete_related_records(
            db_session,
            "tasks",
            ALLOWED_TABLES_CONFIG["tasks"]["user_id_column"],
            user_id,
            "tasks",
        )

        await _delete_related_records(
            db_session,
            "assistant_objectives",
            ALLOWED_TABLES_CONFIG["assistant_objectives"]["user_id_column"],
            user_id,
            "assistant objectives",
        )

        await _delete_related_records(
            db_session,
            "chat_messages",
            ALLOWED_TABLES_CONFIG["chat_messages"]["user_id_column"],
            user_id,
            "chat messages",
        )

        await _delete_related_records(
            db_session,
            "file_metadata",
            ALLOWED_TABLES_CONFIG["file_metadata"]["user_id_column"],
            user_id,
            "file metadata",
        )

        await _delete_related_records(
            db_session,
            "assistants",
            ALLOWED_TABLES_CONFIG["assistants"]["user_id_column"],
            user_id,
            "assistants",
        )

        # Explicitly delete CLI tokens before the user itself
        await _delete_related_records(
            db_session,
            "cli_tokens",
            ALLOWED_TABLES_CONFIG["cli_tokens"]["user_id_column"],
            user_id,
            "CLI tokens",
        )

        # Finally, delete the user record itself using ORM delete to trigger cascades
        await db_session.delete(user)

        # Commit the transaction
        await db_session.commit()

        logger.info(f"Successfully deleted all data for user {user_id}")
        return True

    except Exception as e:
        logger.error(f"Error deleting data for user {user_id}: {str(e)}")
        await db_session.rollback()
        raise


# Define a metadata object for table definitions
metadata = MetaData()

# Define a mapping of table configurations
# This is a more secure approach than using f-strings with table names
ALLOWED_TABLES_CONFIG = {
    "assistants": {"user_id_column": "user_id"},
    "chat_messages": {"user_id_column": "user_id"},
    "file_metadata": {"user_id_column": "user_id"},
    "assistant_objectives": {"user_id_column": "user_id"},
    "tasks": {"user_id_column": "user_id"},
    "external_credentials": {"user_id_column": "user_id"},
    "cli_tokens": {"user_id_column": "user_id"},
    "webhook_registrations": {"user_id_column": "user_id"},
    # Add other tables as needed that store user-specific data
}


async def _delete_related_records(
    db_session: AsyncSession,
    table_name: str,
    user_id_column: str,
    user_id: Union[str, UUID],
    friendly_name: str,
) -> None:
    """
    Helper function to delete records from a table based on user ID.
    Uses SQLAlchemy Core for safe query construction.

    Args:
        db_session: Database session
        table_name: Name of the table to delete from
        user_id_column: Name of the column that contains the user ID
        user_id: The user ID value
        friendly_name: A user-friendly name for the record type (for logging)
    """
    try:
        # Validate table name against whitelist
        if table_name not in ALLOWED_TABLES_CONFIG:
            logger.error(f"Table name '{table_name}' is not allowed for deletion")
            raise ValueError(f"Operation on table '{table_name}' is not permitted")

        # Validate that the provided user_id_column matches the expected one for this table
        config = ALLOWED_TABLES_CONFIG[table_name]
        expected_column = config["user_id_column"]
        if user_id_column != expected_column:
            logger.error(
                f"Column '{user_id_column}' does not match expected column '{expected_column}' for table '{table_name}'"
            )
            raise ValueError(f"Invalid column for table '{table_name}'")

        # Create a SQLAlchemy Table object dynamically
        # This is safer than string interpolation because SQLAlchemy handles
        # proper quoting and escaping of identifiers
        table = Table(
            table_name,
            metadata,
            Column(
                user_id_column, String
            ),  # We only need to define the column we're using
            extend_existing=True,  # Allow redefining the table if it already exists
        )

        # Build a delete statement using SQLAlchemy Core
        # This is safe from SQL injection as table and column names are validated
        # and the actual query is constructed by SQLAlchemy
        stmt = table.delete().where(table.c[user_id_column] == user_id)

        # Execute the statement
        result = await db_session.execute(stmt)
        deleted_count = result.rowcount if hasattr(result, "rowcount") else 0
        logger.info(f"Deleted {deleted_count} {friendly_name} for user {user_id}")
    except SQLAlchemyError as e:
        logger.error(
            f"SQLAlchemy error deleting {friendly_name} for user {user_id}: {str(e)}"
        )
        # Re-raise the exception to be handled by the caller
        raise
    except Exception as e:
        logger.error(f"Error deleting {friendly_name} for user {user_id}: {str(e)}")
        # Re-raise the exception to be handled by the caller
        raise
