"""
Service for handling security aspects of objective management.

This module provides functionality to ensure that users can only access
objectives that belong to their assistants.
"""

import logging
import uuid
from typing import List, Optional

from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.assistant_objective import AssistantObjective
from a2a_platform.schemas.objective_schemas import ObjectiveCreate
from a2a_platform.services.objective_service import ObjectiveService

logger = logging.getLogger(__name__)


class ObjectiveSecurityService:
    """Service for handling security aspects of objective management."""

    def __init__(self, db_session: AsyncSession):
        """Initialize the service with a database session.

        Args:
            db_session: The SQLAlchemy async database session.
        """
        self.db = db_session
        self.objective_service = ObjectiveService(db_session)

    async def verify_assistant_ownership(
        self, user_id: str, assistant_id: uuid.UUID
    ) -> bool:
        """Verify that a user owns an assistant.

        Args:
            user_id: The ID of the user.
            assistant_id: The ID of the assistant.

        Returns:
            True if the user owns the assistant, False otherwise.
        """
        # In a real implementation, this would query the database to check ownership
        # For now, we'll assume the check passes (to be implemented when the assistants table exists)
        logger.warning(
            f"Assistant ownership verification not fully implemented. "
            f"Assuming user {user_id} owns assistant {assistant_id}."
        )
        return True

    async def get_user_objective_by_id(
        self, user_id: str, objective_id: uuid.UUID
    ) -> Optional[AssistantObjective]:
        """Get an objective by ID, ensuring the user has access to it.

        Args:
            user_id: The ID of the user.
            objective_id: The ID of the objective.

        Returns:
            The objective if found and accessible to the user, None otherwise.

        Raises:
            PermissionError: If the user does not have access to the objective.
        """
        # Get the objective
        objective = await self.objective_service.get_objective_by_id(objective_id)
        if not objective:
            return None

        # Verify ownership
        if not await self.verify_assistant_ownership(user_id, objective.assistant_id):
            logger.warning(
                f"User {user_id} attempted to access objective {objective_id} "
                f"belonging to assistant {objective.assistant_id} without permission."
            )
            raise PermissionError(
                f"User {user_id} does not have access to this objective."
            )

        return objective

    async def get_user_objectives(
        self,
        user_id: str,
        assistant_id: uuid.UUID,
        status: Optional[str] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[AssistantObjective]:
        """Get objectives for an assistant, ensuring the user has access to them.

        Args:
            user_id: The ID of the user.
            assistant_id: The ID of the assistant.
            status: Optional status filter.
            limit: Maximum number of objectives to return.
            offset: Number of objectives to skip.

        Returns:
            A list of objectives accessible to the user.

        Raises:
            PermissionError: If the user does not have access to the assistant.
        """
        # Verify ownership
        if not await self.verify_assistant_ownership(user_id, assistant_id):
            logger.warning(
                f"User {user_id} attempted to access objectives for assistant {assistant_id} "
                f"without permission."
            )
            raise PermissionError(
                f"User {user_id} does not have access to this assistant's objectives."
            )

        # Get the objectives
        return await self.objective_service.get_objectives_by_assistant_id(
            assistant_id, status, limit, offset
        )

    async def add_user_objective(
        self, user_id: str, objective_data: ObjectiveCreate
    ) -> AssistantObjective:
        """Add an objective, ensuring the user has access to the assistant.

        Args:
            user_id: The ID of the user.
            objective_data: The data for creating the objective.

        Returns:
            The created objective.

        Raises:
            PermissionError: If the user does not have access to the assistant.
            ValueError: If there's an error creating the objective.
        """
        # Verify ownership
        if not await self.verify_assistant_ownership(
            user_id, objective_data.assistant_id
        ):
            logger.warning(
                f"User {user_id} attempted to add an objective for assistant {objective_data.assistant_id} "
                f"without permission."
            )
            raise PermissionError(
                f"User {user_id} does not have access to this assistant."
            )

        # Add the objective
        return await self.objective_service.add_assistant_objective(objective_data)

    async def update_user_objective_status(
        self, user_id: str, objective_id: uuid.UUID, new_status: str
    ) -> Optional[AssistantObjective]:
        """Update the status of an objective, ensuring the user has access to it.

        Args:
            user_id: The ID of the user.
            objective_id: The ID of the objective.
            new_status: The new status value.

        Returns:
            The updated objective if found and accessible to the user, None otherwise.

        Raises:
            PermissionError: If the user does not have access to the objective.
            ValueError: If the new status is invalid or if there's an error updating the objective.
        """
        # Get the objective and verify ownership
        objective = await self.get_user_objective_by_id(user_id, objective_id)
        if not objective:
            return None

        # Update the status
        return await self.objective_service.update_objective_status(
            objective_id, new_status
        )
