from typing import Any, List, Optional, Tuple, cast

from sqlalchemy import and_, func, select
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from ..db.models.registered_agent import RegisteredAgent as RegisteredAgentModel
from ..schemas.agent_schemas import RegisteredAgentCreate, RegisteredAgentUpdate


class AgentService:
    def __init__(self, db_session: AsyncSession):
        self.db = db_session

    async def register_agent(
        self, agent_create_data: RegisteredAgentCreate
    ) -> RegisteredAgentModel:
        """
        Registers a new specialized agent in the database.

        Args:
            agent_create_data: Pydantic schema containing the agent data to create.

        Returns:
            The created RegisteredAgent SQLAlchemy model instance.

        Raises:
            ValueError: If an agent with the same definition ID already exists or other integrity violation.
        """

        # Convert Pydantic skills to dicts if they are not None
        skills_as_dicts = []
        if agent_create_data.skills:
            for skill_schema in agent_create_data.skills:
                skills_as_dicts.append(
                    skill_schema.model_dump()
                )  # Use model_dump() for Pydantic v2

        db_agent = RegisteredAgentModel(
            agent_definition_id=agent_create_data.agent_definition_id,
            name=agent_create_data.name,
            description=agent_create_data.description,
            version=agent_create_data.version,
            endpoint_url=str(agent_create_data.endpoint_url)
            if agent_create_data.endpoint_url
            else None,
            async_queue_name=agent_create_data.async_queue_name,
            capabilities=agent_create_data.capabilities,
            skills=skills_as_dicts,  # Store as list of dicts
            authentication_info=agent_create_data.authentication_info,
            status=agent_create_data.status,
            # Marketplace fields
            developer_id=agent_create_data.developer_id,
            pricing_info=agent_create_data.pricing_info,
            review_status=agent_create_data.review_status,
        )

        try:
            self.db.add(db_agent)
            await self.db.commit()
            await self.db.refresh(db_agent)
            return db_agent
        except IntegrityError as e:
            await self.db.rollback()
            # Check if it's specifically a unique constraint violation for agent_definition_id
            # This check might depend on the DB driver and error details
            if (
                "registered_agents_pkey" in str(e.orig)
                or "unique constraint" in str(e.orig).lower()
                and "agent_definition_id" in str(e.orig).lower()
            ):
                raise ValueError(
                    f"Agent with definition ID '{agent_create_data.agent_definition_id}' already exists."
                )
            # For other IntegrityErrors (e.g., foreign key, check constraint though Pydantic should catch some)
            raise ValueError(
                f"Database integrity error during agent registration: {str(e.orig)}"
            )
        except Exception:
            await self.db.rollback()
            # Log the error `e` in a real application (e is no longer available here, but the original exception is)
            raise  # Re-raise other unexpected errors

    async def get_agent_by_definition_id(
        self, agent_definition_id: str
    ) -> Optional[RegisteredAgentModel]:
        """
        Retrieves a single specialized agent by its definition ID.

        Args:
            agent_definition_id: The unique definition ID of the agent.

        Returns:
            The RegisteredAgent SQLAlchemy model instance if found, otherwise None.
        """
        stmt = select(RegisteredAgentModel).where(
            RegisteredAgentModel.agent_definition_id == agent_definition_id
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def update_agent(
        self, agent_definition_id: str, agent_update_data: RegisteredAgentUpdate
    ) -> RegisteredAgentModel:
        """
        Updates an existing specialized agent in the database.

        Args:
            agent_definition_id: The unique identifier of the agent to update.
            agent_update_data: Pydantic schema containing the agent data to update.

        Returns:
            The updated RegisteredAgent SQLAlchemy model instance.

        Raises:
            ValueError: If the agent with the given definition ID does not exist.
        """
        # Get the existing agent
        existing_agent = await self.get_agent_by_definition_id(agent_definition_id)
        if not existing_agent:
            raise ValueError(
                f"Agent with definition ID '{agent_definition_id}' not found."
            )

        # Convert Pydantic skills to dicts if they are not None
        if agent_update_data.skills is not None:
            skills_as_dicts = []
            for skill_schema in agent_update_data.skills:
                skills_as_dicts.append(
                    skill_schema.model_dump()
                )  # Use model_dump() for Pydantic v2
            # Update the skills
            existing_agent.skills = cast(Any, skills_as_dicts)

        # Update other fields if they are not None
        if agent_update_data.name is not None:
            existing_agent.name = cast(Any, agent_update_data.name)
        if agent_update_data.description is not None:
            existing_agent.description = cast(Any, agent_update_data.description)
        if agent_update_data.version is not None:
            existing_agent.version = cast(Any, agent_update_data.version)
        if agent_update_data.endpoint_url is not None:
            existing_agent.endpoint_url = cast(Any, str(agent_update_data.endpoint_url))
        if agent_update_data.async_queue_name is not None:
            existing_agent.async_queue_name = cast(
                Any, agent_update_data.async_queue_name
            )
        if agent_update_data.capabilities is not None:
            existing_agent.capabilities = cast(Any, agent_update_data.capabilities)
        if agent_update_data.authentication_info is not None:
            existing_agent.authentication_info = cast(
                Any, agent_update_data.authentication_info
            )
        if agent_update_data.status is not None:
            existing_agent.status = cast(Any, agent_update_data.status)
        if agent_update_data.developer_id is not None:
            existing_agent.developer_id = cast(Any, agent_update_data.developer_id)
        if agent_update_data.pricing_info is not None:
            existing_agent.pricing_info = cast(Any, agent_update_data.pricing_info)
        if agent_update_data.review_status is not None:
            existing_agent.review_status = cast(Any, agent_update_data.review_status)

        try:
            self.db.add(existing_agent)
            await self.db.commit()
            await self.db.refresh(existing_agent)
            return existing_agent
        except IntegrityError as e:
            await self.db.rollback()
            raise ValueError(
                f"Database integrity error during agent update: {str(e.orig)}"
            )

    async def list_agents(
        self,
        status: Optional[
            str
        ] = None,  # Changed from 'active' default here to allow explicit None for no status filter
        capabilities_filter: Optional[List[str]] = None,
        skills_filter: Optional[List[str]] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> Tuple[List[RegisteredAgentModel], int]:
        """
        Lists specialized agents with optional filtering and pagination.

        Args:
            status: Filter by agent status ('active' or 'inactive').
            capabilities_filter: List of capability names. Agents must have ALL these capabilities.
                                 (Assumes capabilities is a dict where keys are capability names).
            skills_filter: List of skill names. Agents must have ALL these skills.
                           (Assumes skills is a list of dicts, each with a 'name' key).
            skip: Number of records to skip for pagination.
            limit: Maximum number of records to return.

        Returns:
            A tuple containing a list of RegisteredAgent SQLAlchemy model instances and the total count of matching agents.
        """
        query = select(RegisteredAgentModel)
        count_query = select(func.count()).select_from(RegisteredAgentModel)

        filters = []
        if status:
            filters.append(RegisteredAgentModel.status == status)

        # Filtering for JSONB fields can be complex and DB-specific.
        # This is a simplified approach for capabilities as dict keys and skills as list of dicts with 'name'.
        # For PostgreSQL, using jsonb_path_exists or contains operators (@>) might be more robust.

        if capabilities_filter:
            for cap_name in capabilities_filter:
                # This checks if the key exists. For specific values, you'd need a different approach.
                # E.g., RegisteredAgentModel.capabilities[cap_name].astext == 'true' (requires casting and specific structure)
                # For simply checking key existence, jsonb_exists or similar is better if available via SQLAlchemy constructs.
                # A simpler string-based like for demonstration, but consider proper JSON operators for production.
                # filters.append(RegisteredAgentModel.capabilities.op('?')(cap_name)) # Example for PostgreSQL jsonb_exists operator `?`
                # Using path existence for a key, assuming capabilities is a JSON object:
                filters.append(
                    RegisteredAgentModel.capabilities.has_key(cap_name)
                )  # SQLAlchemy specific for some backends

        if skills_filter:
            # This requires skills to be a list of objects, and each object to have a 'name' key.
            # And we want to ensure ALL specified skill names are present in the agent's skills list.
            # This is a common pattern: checking if a JSON array of objects contains objects with certain properties.
            # For PostgreSQL, `jsonb_to_recordset` or `jsonb_array_elements` followed by a WHERE could work in raw SQL.
            # TODO: Implement filtering by skills using proper JSON query capabilities
            # A simplified, less efficient, but ORM-friendly way (might require specific JSON support in SQLAlchemy):
            # This is pseudo-code for the intent. Actual implementation depends on SQLAlchemy version and dialect.
            # filters.append(RegisteredAgentModel.skills.op('@>')([{'name': skill_name_to_filter}])) # Example of JSON array contains for PG

            # A more portable but potentially less performant way if skills is a list of dicts:
            # This requires that the skills column can be compared in this manner, which is not always true for JSON.
            # A common pattern for "tags" or "elements in array" style queries.
            # Using a subquery or a more direct JSON function would be better.
            # For demonstration of a simple LIKE, assuming skills might be stringified or a specific JSON function is used.
            # This specific filter will likely need adjustment based on how skills are stored and queried.
            # For now, let's placeholder this part as it's complex to do generically and efficiently.
            # In a real app, you might use a specific PostgreSQL JSON query like:
            # from sqlalchemy.dialects.postgresql import JSONB
            # filters.append(RegisteredAgentModel.skills.cast(JSONB).contains([{'name': skill_name_to_filter}]))
            # Or, if skills are [{name: "X"}, {name: "Y"}]:
            # filters.append(RegisteredAgentModel.skills.op('@>')(func.jsonb_build_array(func.jsonb_build_object('name', skill_name_to_filter))))
            pass  # Placeholder for skills filtering - to be refined

        if filters:
            query = query.where(and_(*filters))
            count_query = count_query.where(and_(*filters))

        total_count = (await self.db.execute(count_query)).scalar_one()

        results = (
            (
                await self.db.execute(
                    query.offset(skip)
                    .limit(limit)
                    .order_by(RegisteredAgentModel.agent_definition_id)
                )
            )
            .scalars()
            .all()
        )

        return list(results), total_count
