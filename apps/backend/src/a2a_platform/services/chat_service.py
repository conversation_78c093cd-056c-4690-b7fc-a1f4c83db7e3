"""
Service for managing chat conversations and messages.

This module provides functionality for creating conversations and sending messages
between users and their personal assistants.
"""

import asyncio
import logging
import uuid
from datetime import UTC, datetime
from typing import Any, List, Optional, cast

from sqlalchemy import and_, func, or_
from sqlalchemy.exc import (
    IntegrityError,
    OperationalError,
    StatementError,
    TimeoutError,
)
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from a2a_platform.api.graphql.schemas.chat_schemas import (
    ChatMessage as ChatMessageSchema,
    ChatMessageConnection,
    ChatMessageEdge,
)
from a2a_platform.db.models.assistant import Assistant
from a2a_platform.db.models.chat_message import ChatMessage
from a2a_platform.db.models.conversation import Conversation
from a2a_platform.db.models.user import User
from a2a_platform.schemas.pagination_schemas import Edge, PageInfo
from a2a_platform.utils.cursor_pagination import CursorPagination<PERSON>rro<PERSON>, CursorUtils
from a2a_platform.utils.sanitization import sanitize_text_content

logger = logging.getLogger(__name__)


class ChatService:
    """Service for managing chat conversations and messages."""

    def __init__(self, db_session: AsyncSession):
        """Initialize the service with a database session.

        Args:
            db_session: The SQLAlchemy async database session.
        """
        self.db = db_session

    async def _publish_message_async(
        self, conversation_id: uuid.UUID, message: ChatMessage, event_type: str
    ) -> None:
        """
        Publish message to subscribers asynchronously (fire-and-forget).

        This method is designed to be called as a background task
        to avoid blocking the main message sending flow.
        """
        try:
            from a2a_platform.messaging.subscription_manager import (
                SubscriptionManager,
            )

            subscription_manager = await SubscriptionManager.get_instance()
            await subscription_manager.publish_message(
                conversation_id=conversation_id,
                message=message,
                event_type=event_type,
            )
            logger.debug(f"Published message {message.id} to subscribers")
        except Exception as pub_error:
            # Don't fail the message creation if publishing fails
            logger.warning(
                f"Failed to publish message to subscribers: {str(pub_error)}"
            )

    async def get_or_create_conversation(
        self, user_id: uuid.UUID, assistant_id: uuid.UUID
    ) -> Conversation:
        """Get or create a conversation between a user and assistant.

        Args:
            user_id: The ID of the user.
            assistant_id: The ID of the assistant.

        Returns:
            The conversation between the user and assistant.

        Raises:
            ValueError: If the user or assistant doesn't exist.
        """
        # Check if user exists
        user_result = await self.db.execute(select(User).where(User.id == user_id))
        user = user_result.scalar_one_or_none()
        if not user:
            raise ValueError(f"User with ID {user_id} does not exist")

        # Check if assistant exists
        assistant_result = await self.db.execute(
            select(Assistant).where(Assistant.id == assistant_id)
        )
        assistant = assistant_result.scalar_one_or_none()
        if not assistant:
            raise ValueError(f"Assistant with ID {assistant_id} does not exist")

        # Check if conversation already exists
        result = await self.db.execute(
            select(Conversation).where(
                Conversation.user_id == user_id,
                Conversation.assistant_id == assistant_id,
            )
        )
        conversation = result.scalar_one_or_none()

        if conversation:
            logger.debug(
                f"Found existing conversation {conversation.id} "
                f"between user {user_id} and assistant {assistant_id}"
            )
            return conversation

        # Create new conversation
        conversation = Conversation(
            user_id=user_id,
            assistant_id=assistant_id,
        )

        try:
            self.db.add(conversation)
            await self.db.commit()
            await self.db.refresh(conversation)
            logger.info(
                f"Created new conversation {conversation.id} "
                f"between user {user_id} and assistant {assistant_id}"
            )
            return conversation
        except IntegrityError as e:
            await self.db.rollback()
            logger.warning(
                f"IntegrityError creating conversation between user {user_id} "
                f"and assistant {assistant_id}: {e}"
            )

            # Try to fetch again in case of race condition
            try:
                result = await self.db.execute(
                    select(Conversation).where(
                        Conversation.user_id == user_id,
                        Conversation.assistant_id == assistant_id,
                    )
                )
                conversation = result.scalar_one_or_none()
                if conversation:
                    logger.info(
                        f"Found existing conversation {conversation.id} after race condition"
                    )
                    return conversation
            except (OperationalError, StatementError, TimeoutError) as fetch_error:
                # Handle specific database errors during fetch
                logger.error(
                    f"Database error fetching conversation after IntegrityError: {fetch_error}"
                )
                raise ValueError(
                    f"Failed to create or retrieve conversation between user {user_id} "
                    f"and assistant {assistant_id}: database error during fetch"
                )
            except Exception as fetch_error:
                # Log unexpected errors but still provide meaningful context
                logger.error(
                    f"Unexpected error fetching conversation after IntegrityError: {fetch_error}",
                    exc_info=True,
                )
                # Re-raise unexpected errors to avoid masking critical issues
                raise

            # If we still don't have a conversation, something is wrong
            raise ValueError(
                f"Failed to create conversation between user {user_id} "
                f"and assistant {assistant_id}: {e}"
            )
        except Exception as e:
            await self.db.rollback()
            logger.error(
                f"Unexpected error creating conversation between user {user_id} "
                f"and assistant {assistant_id}: {e}",
                exc_info=True,
            )
            raise ValueError(f"Database error creating conversation: {e}")

    async def send_message(
        self,
        user_id: uuid.UUID,
        conversation_id: uuid.UUID,
        content: str,
        metadata: Optional[dict[str, Any]] = None,
    ) -> ChatMessage:
        """Send a message from a user in a conversation.

        Args:
            user_id: The ID of the user sending the message.
            conversation_id: The ID of the conversation.
            content: The message content from the user.
            metadata: Optional metadata for the user message.

        Returns:
            The created user message.

        Raises:
            ValueError: If the conversation doesn't exist or user doesn't have permission.
        """
        # Validate and sanitize content
        if not content or not content.strip():
            raise ValueError("Message content cannot be empty")

        if len(content) > 4000:
            raise ValueError("Message content cannot exceed 4000 characters")

        # Sanitize content to prevent XSS attacks
        sanitized_content = sanitize_text_content(content)

        # Verify conversation exists and user has permission in a single query
        result = await self.db.execute(
            select(Conversation).where(
                Conversation.id == conversation_id, Conversation.user_id == user_id
            )
        )
        conversation = result.scalar_one_or_none()
        if not conversation:
            raise ValueError(
                f"Conversation with ID {conversation_id} not found or "
                f"user {user_id} does not have permission to access it"
            )

        # Create the message
        message = ChatMessage(
            conversation_id=conversation_id,
            sender_role="user",
            content={"parts": [{"type": "text", "content": sanitized_content}]},
            message_metadata=metadata or {},
        )

        try:
            self.db.add(message)

            # Update conversation's last_message_at
            conversation.last_message_at = datetime.now(UTC)

            await self.db.commit()
            await self.db.refresh(message)

            # Publish message to subscribers for real-time updates (non-blocking)
            asyncio.create_task(
                self._publish_message_async(conversation_id, message, "new_message")
            )

            logger.info(
                f"Sent message from user {user_id} in conversation {conversation_id}"
            )
            return message

        except Exception as e:
            await self.db.rollback()
            raise ValueError(f"Failed to send message: {str(e)}")

    async def send_pa_message(
        self,
        conversation_id: uuid.UUID,
        content: str,
        metadata: Optional[dict[str, Any]] = None,
        user_id: Optional[uuid.UUID] = None,
    ) -> ChatMessage:
        """Send a message from the Personal Assistant in a conversation.

        Args:
            conversation_id: The ID of the conversation.
            content: The message content from the PA.
            metadata: Optional metadata for the PA message.
            user_id: Optional user ID to verify conversation ownership.

        Returns:
            The created PA message.

        Raises:
            ValueError: If the conversation doesn't exist or content is invalid.
        """
        # Validate and sanitize content
        if not content or not content.strip():
            raise ValueError("PA message content cannot be empty")

        if len(content) > 4000:
            raise ValueError("PA message content cannot exceed 4000 characters")

        # Sanitize content to prevent XSS attacks
        sanitized_content = sanitize_text_content(content)

        # Verify conversation exists
        result = await self.db.execute(
            select(Conversation).where(Conversation.id == conversation_id)
        )
        conversation = result.scalar_one_or_none()
        if not conversation:
            raise ValueError(f"Conversation with ID {conversation_id} not found")

        # Verify conversation ownership if user_id is provided
        if user_id and conversation.user_id != user_id:
            raise ValueError("Conversation not found or access denied")

        # Create the PA message with structured content format
        message = ChatMessage(
            conversation_id=conversation_id,
            sender_role="agent",
            content={"parts": [{"type": "text", "content": sanitized_content}]},
            message_metadata=metadata or {},
        )

        try:
            self.db.add(message)

            # Update conversation's last_message_at
            conversation.last_message_at = datetime.now(UTC)

            await self.db.commit()
            await self.db.refresh(message)

            # Publish message to subscribers for real-time updates (non-blocking)
            asyncio.create_task(
                self._publish_message_async(conversation_id, message, "new_message")
            )

            logger.info(f"Sent PA message in conversation {conversation_id}")
            return message

        except Exception as e:
            await self.db.rollback()
            raise ValueError(f"Failed to send PA message: {str(e)}")

    async def get_conversation_messages(
        self, conversation_id: uuid.UUID, limit: int = 50, offset: int = 0
    ) -> list[ChatMessage]:
        """Get messages from a conversation.

        Args:
            conversation_id: The ID of the conversation.
            limit: Maximum number of messages to return.
            offset: Number of messages to skip.

        Returns:
            List of messages from the conversation, ordered by timestamp.

        Raises:
            ValueError: If the conversation doesn't exist.
        """
        # Verify conversation exists
        conv_result = await self.db.execute(
            select(Conversation).where(Conversation.id == conversation_id)
        )
        conversation = conv_result.scalar_one_or_none()
        if not conversation:
            raise ValueError(f"Conversation with ID {conversation_id} not found")

        # Get messages
        result = await self.db.execute(
            select(ChatMessage)
            .where(ChatMessage.conversation_id == conversation_id)
            .order_by(ChatMessage.timestamp.desc())
            .limit(limit)
            .offset(offset)
        )
        messages = result.scalars().all()

        # Return messages in chronological order
        return list(reversed(messages))

    async def get_conversation_messages_connection(
        self,
        conversation_id: uuid.UUID,
        first: Optional[int] = None,
        after: Optional[str] = None,
        last: Optional[int] = None,
        before: Optional[str] = None,
    ) -> ChatMessageConnection:
        """
        Get messages from a conversation using cursor-based pagination.

        Args:
            conversation_id: The ID of the conversation
            first: Number of items to fetch forward
            after: Cursor to start fetching items after
            last: Number of items to fetch backward
            before: Cursor to stop fetching items before

        Returns:
            ChatMessageConnection with edges, page info, and total count

        Raises:
            ValueError: If conversation doesn't exist or pagination args are invalid
            CursorPaginationError: If cursor decoding fails
        """
        # Validate pagination arguments
        CursorUtils.validate_pagination_args(first, after, last, before)

        # Get normalized pagination args with defaults
        pagination = CursorUtils.get_default_pagination_args(first, after, last, before)

        # Verify conversation exists
        conv_result = await self.db.execute(
            select(Conversation).where(Conversation.id == conversation_id)
        )
        conversation = conv_result.scalar_one_or_none()
        if not conversation:
            raise ValueError(f"Conversation with ID {conversation_id} not found")

        # Build base query
        query = select(ChatMessage).where(
            ChatMessage.conversation_id == conversation_id
        )

        # Apply cursor constraints
        if pagination["is_forward"]:
            # Forward pagination
            if pagination["after"]:
                try:
                    after_timestamp, after_id = CursorUtils.decode_cursor(
                        pagination["after"]
                    )
                    query = query.where(
                        or_(
                            ChatMessage.timestamp > after_timestamp,
                            and_(
                                ChatMessage.timestamp == after_timestamp,
                                ChatMessage.id > after_id,
                            ),
                        )
                    )
                except CursorPaginationError as e:
                    raise ValueError(f"Invalid 'after' cursor: {e}")

            # Apply limit with +1 to detect if there are more results
            limit = pagination["first"] or 20
            query = query.order_by(
                ChatMessage.timestamp.asc(), ChatMessage.id.asc()
            ).limit(limit + 1)

        else:
            # Backward pagination
            if pagination["before"]:
                try:
                    before_timestamp, before_id = CursorUtils.decode_cursor(
                        pagination["before"]
                    )
                    query = query.where(
                        or_(
                            ChatMessage.timestamp < before_timestamp,
                            and_(
                                ChatMessage.timestamp == before_timestamp,
                                ChatMessage.id < before_id,
                            ),
                        )
                    )
                except CursorPaginationError as e:
                    raise ValueError(f"Invalid 'before' cursor: {e}")

            # Apply limit with +1 to detect if there are more results
            limit = pagination["last"] or 20
            query = query.order_by(
                ChatMessage.timestamp.desc(), ChatMessage.id.desc()
            ).limit(limit + 1)

        # Execute query
        result = await self.db.execute(query)
        messages = list(result.scalars().all())

        # Determine pagination flags
        has_more = len(messages) > limit
        if has_more:
            messages = messages[:limit]  # Remove the extra item

        # For backward pagination, reverse the messages to get chronological order
        if not pagination["is_forward"]:
            messages.reverse()

        # Create edges with cursors
        edges: List[ChatMessageEdge] = []
        for message in messages:
            cursor = CursorUtils.encode_cursor(message.timestamp, message.id)
            # Convert database model to GraphQL schema type
            schema_message = ChatMessageSchema.from_db_model(message)
            edges.append(ChatMessageEdge(node=schema_message, cursor=cursor))

        # Create page info
        page_info = PageInfo(
            has_next_page=has_more
            if pagination["is_forward"]
            else bool(pagination["before"]),
            has_previous_page=bool(pagination["after"])
            if pagination["is_forward"]
            else has_more,
            start_cursor=edges[0].cursor if edges else None,
            end_cursor=edges[-1].cursor if edges else None,
        )

        # Get total count for the conversation (optional optimization)
        total_count_query = select(func.count()).select_from(
            select(ChatMessage.id)
            .where(ChatMessage.conversation_id == conversation_id)
            .subquery()
        )
        total_count_result = await self.db.execute(total_count_query)
        total_count = total_count_result.scalar() or 0

        return ChatMessageConnection(
            edges=cast(List[Edge[ChatMessageSchema]], edges),
            page_info=page_info,
            total_count=total_count,
        )
