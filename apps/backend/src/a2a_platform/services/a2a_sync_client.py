"""
Client for making synchronous A2A requests to specialized agents.

This module provides a client for making synchronous HTTP requests to
specialized agents, including user context propagation.
"""

import logging
from typing import Any, Dict, Optional, cast

import httpx

from a2a_platform.services.a2a_context_service import A2AContextService

logger = logging.getLogger(__name__)


class A2ASyncClient:
    """
    Client for making synchronous A2A requests to specialized agents.
    """

    def __init__(self, timeout: int = 30):
        """
        Initialize the A2A sync client.

        Args:
            timeout: Request timeout in seconds
        """
        self.timeout = timeout

    async def call_specialized_agent(
        self,
        endpoint_url: str,
        payload: Dict[str, Any],
        user_id: str,
        initiating_agent_id: str,
        headers: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """
        Call a specialized agent with the given payload and user context.

        Args:
            endpoint_url: The URL of the specialized agent endpoint
            payload: The payload to send to the specialized agent
            user_id: The unique identifier of the end-user
            initiating_agent_id: The identifier of the agent initiating the request
            headers: Optional additional headers to include in the request

        Returns:
            Dict[str, Any]: The response from the specialized agent

        Raises:
            httpx.HTTPError: If the request fails
        """

        # Log with redacted user ID for privacy
        user_id_redacted = (
            f"{user_id[:4]}...{user_id[-4:]}" if len(user_id) > 8 else "***"
        )
        logger.debug(
            f"Calling specialized agent at {endpoint_url} with user_id={user_id_redacted}"
        )

        # Create the A2A message with user context
        a2a_message = A2AContextService.create_a2a_message(
            user_id=user_id, initiating_agent_id=initiating_agent_id, payload=payload
        )

        # Convert to dict for the request
        request_data = a2a_message.model_dump()

        # Set up headers
        request_headers = headers or {}
        request_headers.update(
            {
                "Content-Type": "application/json",
            }
        )

        # Make the request
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            response = await client.post(
                endpoint_url, json=request_data, headers=request_headers
            )
            response.raise_for_status()
            return cast(Dict[str, Any], response.json())
