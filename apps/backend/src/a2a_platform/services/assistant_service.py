"""
Service for managing Personal Assistants.

This module provides functionality for creating, updating, and managing
personal assistants for users.
"""

import logging
import uuid
from typing import Optional

from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from a2a_platform.db.models.assistant import Assistant
from a2a_platform.db.models.user import User
from a2a_platform.schemas.assistant_schemas import (
    AssistantCreate,
    AssistantUpdate,
)

logger = logging.getLogger(__name__)


class AssistantService:
    """Service for managing personal assistants."""

    def __init__(self, db_session: AsyncSession):
        """Initialize the service with a database session.

        Args:
            db_session: The SQLAlchemy async database session.
        """
        self.db = db_session

    async def create_personal_assistant(
        self, user_id: uuid.UUID, assistant_data: AssistantCreate
    ) -> Assistant:
        """Create a new personal assistant for a user.

        Args:
            user_id: The ID of the user who will own the assistant.
            assistant_data: The data for creating the assistant.

        Returns:
            The created Assistant model instance.

        Raises:
            ValueError: If the user already has an assistant or other
                validation error.
            PermissionError: If the user doesn't exist.
        """
        try:
            # First, verify the user exists
            user_result = await self.db.execute(select(User).where(User.id == user_id))
            user = user_result.scalar_one_or_none()
            if not user:
                raise PermissionError(f"User with ID {user_id} not found")

            # Check if user already has an assistant
            existing_assistant_result = await self.db.execute(
                select(Assistant).where(Assistant.user_id == user_id)
            )
            existing_assistant = existing_assistant_result.scalar_one_or_none()
            if existing_assistant:
                raise ValueError(
                    f"User {user_id} already has an assistant with ID "
                    f"{existing_assistant.id}"
                )

            # Create the new assistant
            db_assistant = Assistant(
                user_id=user_id,
                name=assistant_data.name,
                backstory=assistant_data.backstory,
                avatar_file_id=assistant_data.avatar_file_id,
                configuration=assistant_data.configuration or {},
            )

            # Add to the session
            self.db.add(db_assistant)

            # Update user's PA setup completion status
            user.is_pa_setup_complete = True

            # Commit both changes
            await self.db.commit()
            await self.db.refresh(db_assistant)

            logger.info(
                f"Created new assistant '{assistant_data.name}' for user "
                f"{user_id} and marked PA setup as complete"
            )
            return db_assistant

        except IntegrityError as e:
            await self.db.rollback()
            # Check if it's a unique constraint violation for user_id
            if (
                "unique constraint" in str(e.orig).lower()
                and "user_id" in str(e.orig).lower()
            ):
                raise ValueError(f"User {user_id} already has an assistant")
            raise ValueError(f"Database integrity error: {str(e.orig)}")

    async def get_user_assistant(self, user_id: uuid.UUID) -> Optional[Assistant]:
        """Get the assistant for a user.

        Args:
            user_id: The ID of the user.

        Returns:
            The user's assistant if it exists, None otherwise.
        """
        result = await self.db.execute(
            select(Assistant).where(Assistant.user_id == user_id)
        )
        return result.scalar_one_or_none()

    async def update_assistant(
        self,
        assistant_id: uuid.UUID,
        user_id: uuid.UUID,
        update_data: AssistantUpdate,
    ) -> Assistant:
        """Update an existing assistant.

        Args:
            assistant_id: The ID of the assistant to update.
            user_id: The ID of the user (for ownership verification).
            update_data: The data to update.

        Returns:
            The updated Assistant model instance.

        Raises:
            PermissionError: If the user doesn't own the assistant.
            ValueError: If the assistant doesn't exist.
        """
        # Get the assistant and verify ownership
        result = await self.db.execute(
            select(Assistant).where(
                Assistant.id == assistant_id, Assistant.user_id == user_id
            )
        )
        assistant = result.scalar_one_or_none()

        if not assistant:
            raise PermissionError(
                f"Assistant {assistant_id} not found or not owned by user {user_id}"
            )

        # Update fields that are provided
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(assistant, field, value)

        try:
            await self.db.commit()
            await self.db.refresh(assistant)

            logger.info(f"Updated assistant {assistant_id} for user {user_id}")
            return assistant

        except IntegrityError as e:
            await self.db.rollback()
            raise ValueError(f"Database integrity error: {str(e.orig)}")

    async def delete_assistant(
        self, assistant_id: uuid.UUID, user_id: uuid.UUID
    ) -> bool:
        """Delete an assistant.

        Args:
            assistant_id: The ID of the assistant to delete.
            user_id: The ID of the user (for ownership verification).

        Returns:
            True if the assistant was deleted, False if it didn't exist.

        Raises:
            PermissionError: If the user doesn't own the assistant.
        """
        # Get the assistant and verify ownership
        result = await self.db.execute(
            select(Assistant).where(
                Assistant.id == assistant_id, Assistant.user_id == user_id
            )
        )
        assistant = result.scalar_one_or_none()

        if not assistant:
            return False

        await self.db.delete(assistant)
        await self.db.commit()

        logger.info(f"Deleted assistant {assistant_id} for user {user_id}")
        return True

    async def user_has_assistant(self, user_id: uuid.UUID) -> bool:
        """Check if a user has an assistant.

        Args:
            user_id: The ID of the user.

        Returns:
            True if the user has an assistant, False otherwise.
        """
        result = await self.db.execute(
            select(Assistant.id).where(Assistant.user_id == user_id)
        )
        return result.scalar_one_or_none() is not None
