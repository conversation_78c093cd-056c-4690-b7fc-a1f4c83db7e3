import logging
import uuid
from typing import Any, Dict, List, Optional

from sqlalchemy import func, select
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.assistant_objective import AssistantObjective
from a2a_platform.schemas.objective_schemas import ObjectiveCreate

logger = logging.getLogger(__name__)


class ObjectiveService:
    """Service for managing assistant objectives."""

    def __init__(self, db_session: AsyncSession):
        """Initialize the service with a database session.

        Args:
            db_session: The SQLAlchemy async database session.
        """
        self.db = db_session
        self._cache: Dict[str, Any] = {}  # Simple in-memory cache

    def _cache_key(self, method_name: str, *args: Any, **kwargs: Any) -> str:
        """Generate a cache key for a method call.

        Args:
            method_name: The name of the method being called.
            *args: Positional arguments to the method.
            **kwargs: Keyword arguments to the method.

        Returns:
            A string cache key.
        """
        # Simple cache key generation - in production, use a more robust method
        key_parts = [method_name]
        key_parts.extend([str(arg) for arg in args])
        key_parts.extend([f"{k}={v}" for k, v in sorted(kwargs.items())])
        return ":".join(key_parts)

    def _invalidate_cache_for_assistant(self, assistant_id: uuid.UUID) -> None:
        """Invalidate all cache entries for a specific assistant.

        Args:
            assistant_id: The ID of the assistant whose cache entries should be invalidated.
        """
        # Find and remove all cache entries related to this assistant
        keys_to_remove = []
        for key in self._cache.keys():
            if str(assistant_id) in key:
                keys_to_remove.append(key)

        for key in keys_to_remove:
            self._cache.pop(key, None)

        logger.debug(
            f"Invalidated {len(keys_to_remove)} cache entries for assistant {assistant_id}"
        )

    async def add_assistant_objective(
        self, objective_data: ObjectiveCreate
    ) -> AssistantObjective:
        """Add a new objective for an assistant.

        Args:
            objective_data: The data for creating the objective.

        Returns:
            The created AssistantObjective model instance.

        Raises:
            ValueError: If there's an integrity error or other database error.
        """
        try:
            # Create a new AssistantObjective instance
            db_objective = AssistantObjective(
                assistant_id=objective_data.assistant_id,
                objective_text=objective_data.objective_text,
                status=objective_data.status,
                metadata_json=objective_data.metadata,
            )

            # Add to the session and commit
            self.db.add(db_objective)
            await self.db.commit()
            await self.db.refresh(db_objective)

            # Invalidate cache for this assistant
            self._invalidate_cache_for_assistant(objective_data.assistant_id)

            logger.info(
                f"Created new objective for assistant {objective_data.assistant_id}: "
                f"{objective_data.objective_text[:50]}..."
            )
            return db_objective

        except IntegrityError as e:
            await self.db.rollback()
            error_msg = f"Could not create objective due to integrity error: {str(e)}"
            logger.error(error_msg)
            # This could happen if the assistant_id doesn't exist
            raise ValueError(error_msg)
        except Exception as e:
            await self.db.rollback()
            error_msg = f"An error occurred while creating objective: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

    async def get_objective_by_id(
        self, objective_id: uuid.UUID
    ) -> Optional[AssistantObjective]:
        """Get an objective by its ID.

        Args:
            objective_id: The ID of the objective to retrieve.

        Returns:
            The AssistantObjective if found, None otherwise.
        """
        # Check cache first
        cache_key = self._cache_key("get_objective_by_id", objective_id)
        if cache_key in self._cache:
            logger.debug(f"Cache hit for objective {objective_id}")
            return self._cache[cache_key]  # type: ignore[no-any-return]

        # Not in cache, query the database
        stmt = select(AssistantObjective).where(AssistantObjective.id == objective_id)
        result = await self.db.execute(stmt)
        # In SQLAlchemy 2.0+, scalar_one_or_none() is synchronous
        objective = result.scalar_one_or_none()

        # Cache the result (even if None)
        self._cache[cache_key] = objective

        return objective

    async def get_objectives_by_assistant_id(
        self,
        assistant_id: uuid.UUID,
        status: Optional[str] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[AssistantObjective]:
        """Get all objectives for an assistant, optionally filtered by status.

        Args:
            assistant_id: The ID of the assistant.
            status: Optional status filter (e.g., 'active', 'completed', 'cancelled').
            limit: Maximum number of objectives to return (for pagination).
            offset: Number of objectives to skip (for pagination).

        Returns:
            A list of AssistantObjective instances.
        """
        # Check cache first
        cache_key = self._cache_key(
            "get_objectives_by_assistant_id", assistant_id, status, limit, offset
        )
        if cache_key in self._cache:
            logger.debug(f"Cache hit for objectives of assistant {assistant_id}")
            return self._cache[cache_key]  # type: ignore[no-any-return]

        # Not in cache, query the database
        stmt = select(AssistantObjective).where(
            AssistantObjective.assistant_id == assistant_id
        )

        if status:
            stmt = stmt.where(AssistantObjective.status == status)

        # Add ordering and pagination
        stmt = stmt.order_by(AssistantObjective.created_at.desc())
        stmt = stmt.limit(limit).offset(offset)

        result = await self.db.execute(stmt)
        # In SQLAlchemy 2.0+, scalars() returns a ScalarResult that's already awaited
        scalars_result = result.scalars()
        # all() is synchronous in SQLAlchemy 2.0+
        objectives = list(scalars_result.all())

        # Cache the result
        self._cache[cache_key] = objectives

        return objectives

    async def count_objectives_by_assistant_id(
        self, assistant_id: uuid.UUID, status: Optional[str] = None
    ) -> int:
        """Count objectives for an assistant, optionally filtered by status.

        Args:
            assistant_id: The ID of the assistant.
            status: Optional status filter (e.g., 'active', 'completed', 'cancelled').

        Returns:
            The count of matching objectives.
        """
        # Check cache first
        cache_key = self._cache_key(
            "count_objectives_by_assistant_id", assistant_id, status
        )
        if cache_key in self._cache:
            logger.debug(f"Cache hit for objective count of assistant {assistant_id}")
            return self._cache[cache_key]  # type: ignore[no-any-return]

        # Not in cache, query the database
        stmt = (
            select(func.count())
            .select_from(AssistantObjective)
            .where(AssistantObjective.assistant_id == assistant_id)
        )

        if status:
            stmt = stmt.where(AssistantObjective.status == status)

        result = await self.db.execute(stmt)
        count = result.scalar_one()

        # Cache the result
        self._cache[cache_key] = count

        return count

    async def update_objective_status(
        self, objective_id: uuid.UUID, new_status: str
    ) -> Optional[AssistantObjective]:
        """Update the status of an objective.

        Args:
            objective_id: The ID of the objective to update.
            new_status: The new status value ('active', 'completed', or 'cancelled').

        Returns:
            The updated AssistantObjective if found, None otherwise.

        Raises:
            ValueError: If the new status is invalid or if there's a database error.
        """
        # Validate the status
        if new_status not in ["active", "completed", "cancelled"]:
            raise ValueError(f"Invalid status: {new_status}")

        try:
            # Get the objective
            objective = await self.get_objective_by_id(objective_id)
            if not objective:
                return None

            # Update the status
            objective.status = new_status
            await self.db.commit()
            await self.db.refresh(objective)

            # Invalidate cache for this assistant
            self._invalidate_cache_for_assistant(objective.assistant_id)

            logger.info(f"Updated objective {objective_id} status to {new_status}")
            return objective

        except Exception as e:
            await self.db.rollback()
            error_msg = f"Error updating objective status: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

    def clear_cache(self) -> None:
        """Clear the entire cache."""
        self._cache.clear()
        logger.debug("Cleared objective service cache")
