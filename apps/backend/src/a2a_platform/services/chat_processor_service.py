"""
Service for processing chat messages and identifying user objectives.

This module provides functionality to process chat messages, identify when a user
defines an objective, and store those objectives using the objective service.
"""

import logging
import re
import uuid
from typing import Op<PERSON>, <PERSON>ple

from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.assistant_objective import AssistantObjective
from a2a_platform.schemas.objective_schemas import ObjectiveCreate
from a2a_platform.services.objective_service import ObjectiveService

logger = logging.getLogger(__name__)


class ChatProcessorError(Exception):
    """Base exception for chat processor errors."""

    pass


class ObjectiveStorageError(ChatProcessorError):
    """Exception raised when an objective cannot be stored."""

    pass


class ObjectiveIdentificationError(ChatProcessorError):
    """Exception raised when there's an error identifying an objective."""

    pass


class ChatProcessorService:
    """Service for processing chat messages and identifying user objectives."""

    def __init__(self, db_session: AsyncSession):
        """Initialize the service with a database session.

        Args:
            db_session: The SQLAlchemy async database session.
        """
        self.db = db_session
        self.objective_service = ObjectiveService(db_session)

    async def process_message(
        self, message_text: str, assistant_id: uuid.UUID
    ) -> Tuple[bool, Optional[AssistantObjective]]:
        """Process a chat message and identify if it contains an objective.

        This method implements the logic for US1.7 (User defines objective in chat).
        It identifies potential objectives in the message and stores them if confirmed.

        Args:
            message_text: The text of the chat message.
            assistant_id: The ID of the assistant receiving the message.

        Returns:
            A tuple containing:
            - A boolean indicating if an objective was identified and stored.
            - The stored AssistantObjective if one was created, None otherwise.
        """
        # Check if the message contains a potential objective
        potential_objective = self._identify_potential_objective(message_text)

        if not potential_objective:
            return False, None

        # In a real implementation, there might be a confirmation step here
        # For now, we'll assume all identified objectives are confirmed

        # Store the objective
        objective_data = ObjectiveCreate(
            assistant_id=assistant_id,
            objective_text=potential_objective,
            status="active",
        )

        try:
            stored_objective = await self.objective_service.add_assistant_objective(
                objective_data
            )
            logger.info(
                f"Stored objective for assistant {assistant_id}: {potential_objective}"
            )
            return True, stored_objective
        except IntegrityError as e:
            error_msg = f"Database integrity error storing objective: {str(e)}"
            logger.error(error_msg)
            raise ObjectiveStorageError(error_msg) from e
        except ValueError as e:
            error_msg = f"Value error storing objective: {str(e)}"
            logger.error(error_msg)
            return False, None
        except SQLAlchemyError as e:
            error_msg = f"Database error storing objective: {str(e)}"
            logger.error(error_msg)
            raise ObjectiveStorageError(error_msg) from e
        except Exception as e:
            error_msg = f"Unexpected error storing objective: {str(e)}"
            logger.error(error_msg)
            raise ObjectiveStorageError(error_msg) from e

    def _identify_potential_objective(self, message_text: str) -> Optional[str]:
        """Identify a potential objective in a chat message.

        This method uses pattern matching to identify when a user is defining an objective.
        In a production environment, this would likely use more sophisticated NLP techniques.

        Args:
            message_text: The text of the chat message.

        Returns:
            The extracted objective text if found, None otherwise.

        Raises:
            ObjectiveIdentificationError: If there's an error in the identification process.
        """
        if not message_text or not isinstance(message_text, str):
            logger.warning(f"Invalid message text: {message_text}")
            return None

        try:
            # Simple pattern matching for objectives
            # Look for phrases like "my goal is", "I want to", "help me", etc.
            objective_patterns = [
                r"(?i)my goal is to (.+)",
                r"(?i)my objective is to (.+)",
                r"(?i)i want to (.+)",
                r"(?i)i would like to (.+)",
                r"(?i)help me (.+)",
                r"(?i)i need to (.+)",
                r"(?i)i aim to (.+)",
                r"(?i)i plan to (.+)",
            ]

            for pattern in objective_patterns:
                match = re.search(pattern, message_text)
                if match:
                    # Extract the objective text from the match
                    objective_text = match.group(1).strip()

                    # Remove trailing punctuation
                    objective_text = re.sub(r"[.!?]+$", "", objective_text)

                    # Validate the extracted objective text
                    if not objective_text:
                        logger.debug(
                            f"Empty objective text extracted from: {message_text}"
                        )
                        continue

                    logger.debug(f"Identified objective: {objective_text}")
                    return objective_text

            logger.debug(
                f"No objective pattern matched in message: {message_text[:50]}..."
            )
            return None

        except re.error as e:
            error_msg = f"Regex error in objective identification: {str(e)}"
            logger.error(error_msg)
            raise ObjectiveIdentificationError(error_msg) from e
        except Exception as e:
            error_msg = f"Unexpected error in objective identification: {str(e)}"
            logger.error(error_msg)
            raise ObjectiveIdentificationError(error_msg) from e
