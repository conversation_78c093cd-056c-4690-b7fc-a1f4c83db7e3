"""
PydanticAI-based AI Response Service for Personal Assistant response generation.

This service uses PydanticAI framework to provide vendor-agnostic LLM integration
with support for OpenAI, Anthropic Claude, and Google Gemini models.
"""

import logging
import time
from dataclasses import dataclass
from typing import Any, Dict, List, Tuple

from pydantic import BaseModel, Field
from pydantic_ai import Agent
from pydantic_ai.exceptions import UnexpectedModelBehavior


from a2a_platform.db.models.assistant import Assistant
from a2a_platform.db.models.chat_message import ChatMessage

logger = logging.getLogger(__name__)


# Lazy load settings to avoid import issues during testing
def _get_settings() -> Any:
    """Lazy load settings to handle test environments."""
    try:
        from a2a_platform.config.settings import get_settings

        return get_settings()
    except Exception:
        # Return a mock settings object for testing
        class MockSettings:
            OPENAI_API_KEY = None
            ANTHROPIC_API_KEY = None
            GEMINI_API_KEY = None
            GOOGLE_API_KEY = None
            AI_RESPONSE_ENABLED = True
            AI_RESPONSE_TIMEOUT = 30
            AI_RATE_LIMIT_CALLS = 50
            AI_RATE_LIMIT_WINDOW = 60

        return MockSettings()


class AIResponseConfig(BaseModel):
    """Configuration for AI response generation."""

    model: str = Field(default="openai:gpt-3.5-turbo", description="LLM model to use")
    temperature: float = Field(
        default=0.7, ge=0.0, le=2.0, description="Response creativity"
    )
    max_tokens: int = Field(
        default=500, ge=1, le=4000, description="Maximum response length"
    )
    timeout: int = Field(default=30, ge=5, le=120, description="API timeout in seconds")
    system_prompt_template: str = Field(
        default="You are {name}, a helpful personal assistant. {backstory}",
        description="System prompt template",
    )


class AIResponseError(Exception):
    """Custom exception for AI response generation errors."""

    pass


class RateLimiter:
    """Simple rate limiter for API calls."""

    def __init__(self, max_calls: int = 60, window_seconds: int = 60):
        self.max_calls = max_calls
        self.window_seconds = window_seconds
        self.calls: List[float] = []

    def acquire(self) -> bool:
        """Check if a call can be made within rate limits."""
        now = time.time()
        # Remove calls outside the window
        self.calls = [
            call_time
            for call_time in self.calls
            if now - call_time < self.window_seconds
        ]

        if len(self.calls) >= self.max_calls:
            return False

        self.calls.append(now)
        return True

    def time_until_next_call(self) -> float:
        """Get seconds until next call is allowed."""
        if len(self.calls) < self.max_calls:
            return 0.0

        # Handle edge case where calls list might be empty
        if not self.calls:
            return 0.0

        oldest_call = min(self.calls)
        return self.window_seconds - (time.time() - oldest_call)


@dataclass
class ConversationContext:
    """Context for conversation history and assistant configuration."""

    assistant: Assistant
    user_message: str
    conversation_history: List[ChatMessage]


class PydanticAIResponseService:
    """Service for generating AI-powered Personal Assistant responses using PydanticAI."""

    def __init__(self) -> None:
        """Initialize the PydanticAI response service."""
        self.rate_limiter = RateLimiter(
            max_calls=50, window_seconds=60
        )  # 50 calls per minute
        self.fallback_responses = [
            "I understand you're reaching out. Let me think about that and get back to you.",
            "Thank you for your message. I'm processing your request.",
            "I'm here to help! Let me consider the best way to assist you.",
            "I appreciate your patience while I formulate a thoughtful response.",
        ]

    def _get_ai_config(self, assistant: Assistant) -> AIResponseConfig:
        """Extract AI configuration from assistant settings."""
        config_dict = assistant.configuration or {}
        ai_config = config_dict.get("ai", {})

        # Merge with defaults
        return AIResponseConfig(**ai_config)

    def _build_system_prompt(
        self, assistant: Assistant, config: AIResponseConfig
    ) -> str:
        """Build the system prompt for the AI model."""
        return config.system_prompt_template.format(
            name=assistant.name,
            backstory=assistant.backstory or "I am a helpful assistant.",
        )

    def _extract_message_text(self, message: ChatMessage) -> str:
        """Extract text content from a structured message."""
        try:
            if isinstance(message.content, dict) and "parts" in message.content:
                text_parts = []
                for part in message.content["parts"]:
                    if part.get("type") == "text":
                        text_parts.append(part.get("content", ""))
                return " ".join(text_parts).strip()
            return str(message.content)
        except Exception as e:
            logger.error(f"Error extracting text from message {message.id}: {str(e)}")
            return ""

    def _build_conversation_messages(
        self, conversation_history: List[ChatMessage], max_context: int = 10
    ) -> List[str]:
        """Build conversation context from recent messages for PydanticAI."""
        # Sort messages by timestamp (most recent last)
        sorted_messages = sorted(conversation_history, key=lambda m: m.timestamp)

        # Take the last max_context messages
        recent_messages = (
            sorted_messages[-max_context:]
            if len(sorted_messages) > max_context
            else sorted_messages
        )

        context_messages = []
        for message in recent_messages:
            content_text = self._extract_message_text(message)
            if content_text:
                role = "User" if message.sender_role == "user" else "Assistant"
                context_messages.append(f"{role}: {content_text}")

        return context_messages

    def _get_model_name(self, config: AIResponseConfig) -> str:
        """Get the model name for PydanticAI Agent initialization."""
        # PydanticAI can handle model names directly with prefixes
        # We just return the configured model name as-is
        return config.model

    def _create_model_instance(self, config: AIResponseConfig) -> Any:
        """Create a PydanticAI model instance based on the configuration.

        This method is used for testing and explicit model creation.
        Returns the appropriate model class instance based on the provider.
        """
        model_name = config.model

        # Parse provider from model name (format: "provider:model")
        if ":" in model_name:
            provider, _ = model_name.split(":", 1)
        else:
            # Default to OpenAI if no provider specified
            provider = "openai"

        # For testing, provide dummy API keys if environment variables are not set
        import os

        if provider == "openai":
            from pydantic_ai.models.openai import OpenAIModel
            from pydantic_ai.providers.openai import OpenAIProvider

            api_key = os.environ.get("OPENAI_API_KEY", "test-openai-key")
            openai_provider = OpenAIProvider(api_key=api_key)
            return OpenAIModel(model_name, provider=openai_provider)

        elif provider == "anthropic":
            from pydantic_ai.models.anthropic import AnthropicModel
            from pydantic_ai.providers.anthropic import AnthropicProvider

            api_key = os.environ.get("ANTHROPIC_API_KEY", "test-anthropic-key")
            anthropic_provider = AnthropicProvider(api_key=api_key)
            return AnthropicModel(model_name, provider=anthropic_provider)

        elif provider in ["google-gla", "google-vertex"]:
            from pydantic_ai.models.gemini import GeminiModel

            if provider == "google-gla":
                from pydantic_ai.providers.google_gla import GoogleGLAProvider

                api_key = os.environ.get("GEMINI_API_KEY", "test-gemini-key")
                google_provider = GoogleGLAProvider(api_key=api_key)
                return GeminiModel(model_name, provider=google_provider)
            else:  # google-vertex
                from pydantic_ai.providers.google_vertex import GoogleVertexProvider

                # For Vertex AI, we'll use a dummy project ID for testing
                vertex_provider = GoogleVertexProvider(
                    project_id=os.environ.get("GOOGLE_CLOUD_PROJECT", "test-project")
                )
                return GeminiModel(model_name, provider=vertex_provider)
        else:
            raise ValueError(f"Unsupported model provider: {provider}")

    def _get_fallback_response(self) -> str:
        """Get a fallback response when AI generation fails."""
        import random

        return random.choice(self.fallback_responses)

    async def generate_response(
        self,
        assistant: Assistant,
        user_message: str,
        conversation_history: List[ChatMessage],
        force_fallback: bool = False,
    ) -> Tuple[str, bool]:
        """
        Generate an AI response for the Personal Assistant using PydanticAI.

        Args:
            assistant: The assistant configuration
            user_message: The user's message to respond to
            conversation_history: Recent conversation messages for context
            force_fallback: If True, skip AI generation and use fallback

        Returns:
            Tuple of (response_text, is_ai_generated)
        """
        if force_fallback:
            return self._get_fallback_response(), False

        # Check rate limits
        if not self.rate_limiter.acquire():
            wait_time = self.rate_limiter.time_until_next_call()
            logger.warning(
                f"Rate limit exceeded, using fallback. Next call in {wait_time:.1f}s"
            )
            return self._get_fallback_response(), False

        try:
            config = self._get_ai_config(assistant)
            system_prompt = self._build_system_prompt(assistant, config)

            # Get model name for PydanticAI
            model_name = self._get_model_name(config)

            # Create agent with system prompt (PydanticAI handles model creation)
            # Use cast to handle the model name type issue
            from typing import cast
            from pydantic_ai.models import KnownModelName

            agent: Any = Agent(
                cast(KnownModelName, model_name), system_prompt=system_prompt
            )

            # Build conversation context
            context_messages = self._build_conversation_messages(conversation_history)

            # Prepare the full prompt with context
            if context_messages:
                context_text = "\n".join(
                    context_messages[-5:]
                )  # Last 5 messages for context
                full_prompt = (
                    f"Recent conversation:\n{context_text}\n\nUser: {user_message}"
                )
            else:
                full_prompt = user_message

            # Generate response using PydanticAI
            result = await agent.run(full_prompt)
            response_text = result.output

            if not response_text or not response_text.strip():
                logger.warning(f"Empty response generated for assistant {assistant.id}")
                return self._get_fallback_response(), False

            logger.info(
                f"Generated AI response for assistant {assistant.id} using model {config.model}"
            )
            return response_text.strip(), True

        except UnexpectedModelBehavior as e:
            logger.error(f"PydanticAI model behavior error: {str(e)}")
            return self._get_fallback_response(), False
        except Exception as e:
            logger.error(f"Unexpected error in AI response generation: {str(e)}")
            return self._get_fallback_response(), False

    async def test_model_availability(self, model_name: str) -> bool:
        """Test if a specific model is available and working."""
        try:
            # Create a simple test agent
            from typing import cast
            from pydantic_ai.models import KnownModelName

            agent: Any = Agent(
                cast(KnownModelName, model_name),
                system_prompt="You are a test assistant.",
            )

            # Try a simple test query
            result = await agent.run("Say 'test successful'")
            return bool(result.output and "test" in result.output.lower())

        except Exception as e:
            logger.error(f"Model {model_name} availability test failed: {str(e)}")
            return False

    def get_supported_models(self) -> Dict[str, List[str]]:
        """Get list of supported models by provider."""
        return {
            "openai": [
                "openai:gpt-3.5-turbo",
                "openai:gpt-4",
                "openai:gpt-4-turbo",
                "openai:gpt-4o",
                "openai:gpt-4o-mini",
                "openai:o1-preview",
                "openai:o1-mini",
            ],
            "anthropic": [
                "anthropic:claude-3-haiku-20240307",
                "anthropic:claude-3-sonnet-20240229",
                "anthropic:claude-3-opus-20240229",
                "anthropic:claude-3-5-sonnet-latest",
                "anthropic:claude-3-5-haiku-latest",
            ],
            "google": [
                "google-gla:gemini-1.5-flash",
                "google-gla:gemini-1.5-pro",
                "google-gla:gemini-2.0-flash",
                "google-vertex:gemini-1.5-flash",
                "google-vertex:gemini-1.5-pro",
                "google-vertex:gemini-2.0-flash",
            ],
        }
