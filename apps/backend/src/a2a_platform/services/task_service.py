import uuid
from datetime import UTC, datetime, timedelta, timezone
from typing import List, <PERSON>tional, Tuple

from sqlalchemy import and_, func, or_, select, update
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db.models.task import Task
from a2a_platform.schemas.task_schemas import TaskCreate, TaskUpdate


class TaskService:
    """
    Service for managing internal tasks for Personal Assistants.
    Provides methods for creating, updating, and querying tasks.
    """

    def __init__(self, session: AsyncSession):
        self.session = session

    async def create_task(
        self,
        assistant_id: uuid.UUID,
        task_data: TaskCreate,
        idempotency_key: Optional[str] = None,
    ) -> Task:
        """
        Creates a new task for an assistant.

        If idempotency_key is provided and matches an existing active task for the assistant,
        returns the existing task instead of creating a new one.
        If a previous task with the same idempotency_key is in a terminal state, a new task is created with idempotency_key=None.
        """
        # Check for existing task with same idempotency_key if provided
        use_idempotency_key = idempotency_key
        if idempotency_key:
            terminal_statuses = [
                "done",
                "cancelled",
                "failed_max_retries",
                "quarantined",
            ]
            try:
                existing_task = await self.get_task_by_idempotency_key(
                    assistant_id, idempotency_key, exclude_statuses=terminal_statuses
                )
                if existing_task:
                    return existing_task
                # If no active task, check if a terminal task exists with this key
                # If so, do not reuse the idempotency key for the new task
                query = select(Task).where(
                    Task.assistant_id == assistant_id,
                    Task.idempotency_key == idempotency_key,
                    Task.status.in_(terminal_statuses),
                )
                result = await self.session.execute(query)
                terminal_task = result.scalar_one_or_none()
                if terminal_task:
                    use_idempotency_key = None
            except Exception as e:
                print(f"Error checking for existing task: {str(e)}")

        # Determine initial status
        initial_status = task_data.initial_status or "todo"
        # If initial_status is 'pending_dependency' and depends_on_task_id is set, accept as is
        if task_data.depends_on_task_id:
            # Check the status of the prerequisite task
            prereq_query = select(Task.status).where(
                Task.id == task_data.depends_on_task_id
            )
            prereq_result = await self.session.execute(prereq_query)
            prereq_status = prereq_result.scalar_one_or_none()
            terminal_statuses = [
                "done",
                "cancelled",
                "failed_max_retries",
                "quarantined",
            ]
            if prereq_status in terminal_statuses:
                # Prerequisite is already done/cancelled/etc, so this task should be 'todo'
                initial_status = "todo"
            elif initial_status == "pending_dependency":
                # If pending_dependency is requested and dependency is not terminal, keep as is
                pass
            else:
                # If dependency is set and initial_status is not provided or is 'todo', force pending_dependency
                if not task_data.initial_status or task_data.initial_status == "todo":
                    initial_status = "pending_dependency"
        else:
            # If pending_dependency is requested but no dependency, fallback to todo
            if initial_status == "pending_dependency":
                initial_status = "todo"

        # Create a new task
        task = Task(
            assistant_id=assistant_id,
            description=task_data.description,
            objective_id=task_data.objective_id,
            parent_task_id=task_data.parent_task_id,
            depends_on_task_id=task_data.depends_on_task_id,
            status=initial_status,
            metadata_json=task_data.metadata or {},
            idempotency_key=use_idempotency_key,
        )

        self.session.add(task)

        try:
            await self.session.flush()
            await self.session.commit()  # Commit the transaction
            return task
        except IntegrityError as e:
            # If we get a unique constraint violation, try fetching the task again
            # This handles race conditions with concurrent task creation
            if idempotency_key:
                await self.session.rollback()
                existing_task = await self.get_task_by_idempotency_key(
                    assistant_id, idempotency_key, exclude_statuses=terminal_statuses
                )
                if existing_task:
                    return existing_task
            raise e

    async def update_task(
        self, task_id: uuid.UUID, update_data: TaskUpdate
    ) -> Optional[Task]:
        """
        Updates an existing task with the provided data.
        """
        task = await self.get_task_by_id(task_id)
        if not task:
            return None

        # Update fields if provided in update_data
        if update_data.description is not None:
            task.description = update_data.description

        if update_data.status is not None:
            # Handle status transitions
            old_status = task.status
            if update_data.status == "done" and task.status != "done":
                task.completed_at = datetime.now(UTC)
            task.status = update_data.status

            # If task is being marked as done, update dependent tasks
            if update_data.status == "done" and old_status != "done":
                # Commit the current task status change first
                await self.session.flush()
                await self.session.commit()
                # Then update dependent tasks
                await self.update_dependent_tasks(task.id)
                # Return early since we already committed
                return task

        if update_data.last_progress_at is not None:
            task.last_progress_at = update_data.last_progress_at

        if update_data.metadata is not None:
            # Merge with existing metadata instead of replacing
            # Create a new dictionary to avoid reference issues
            new_metadata = dict(task.metadata_json or {})
            new_metadata.update(update_data.metadata)
            task.metadata_json = new_metadata

        await self.session.flush()
        await self.session.commit()  # Commit the transaction
        return task

    async def cancel_task(self, task_id: uuid.UUID) -> Optional[Task]:
        """Cancel a task by setting its status to 'cancelled'."""
        # First get the task to make sure it exists
        task = await self.get_task_by_id(task_id)
        if not task:
            return None

        # Update the task status directly
        task.status = "cancelled"
        task.updated_at = datetime.now(UTC)

        # Commit the changes
        await self.session.flush()
        await self.session.commit()  # Commit the transaction
        return task

    async def get_task_by_id(self, task_id: uuid.UUID) -> Optional[Task]:
        """Get a task by its ID."""
        query = select(Task).where(Task.id == task_id)
        result = await self.session.execute(query)
        task: Optional[Task] = result.scalar_one_or_none()
        return task

    async def get_task_by_idempotency_key(
        self,
        assistant_id: uuid.UUID,
        idempotency_key: str,
        exclude_statuses: Optional[List[str]] = None,
    ) -> Optional[Task]:
        """Get a task by its idempotency key."""
        # Default statuses to exclude if none provided
        if exclude_statuses is None:
            exclude_statuses = ["done", "cancelled", "failed"]

        try:
            query = select(Task).where(
                Task.assistant_id == assistant_id,
                Task.idempotency_key == idempotency_key,
                # Only consider tasks not in excluded statuses
                Task.status.notin_(exclude_statuses),
            )
            result = await self.session.execute(query)
            task: Optional[Task] = result.scalar_one_or_none()
            return task
        except Exception as e:
            print(f"Error in get_task_by_idempotency_key: {str(e)}")
            return None

    async def get_tasks_for_assistant(
        self,
        assistant_id: uuid.UUID,
        status: Optional[str] = None,
        objective_id: Optional[uuid.UUID] = None,
        parent_task_id: Optional[uuid.UUID] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> Tuple[List[Task], int]:
        """
        Retrieves tasks for an assistant with optional filtering.
        Returns a tuple of (tasks, total_count).
        """
        filters = [Task.assistant_id == assistant_id]

        if status:
            filters.append(Task.status == status)

        if objective_id:
            filters.append(Task.objective_id == objective_id)

        if parent_task_id:
            filters.append(Task.parent_task_id == parent_task_id)

        # Get count using SQLAlchemy func.count() to ensure we get an integer
        count_query = select(func.count()).select_from(
            select(Task).where(and_(*filters)).subquery()
        )
        count_result = await self.session.execute(count_query)
        # Use scalar_one() since the count function always returns exactly one row
        count_value = count_result.scalar_one()
        total_count = 0 if count_value is None else count_value

        # Get tasks with pagination
        query = select(Task).where(and_(*filters)).offset(skip).limit(limit)
        task_result = await self.session.execute(query)
        tasks = list(task_result.scalars().all())

        return tasks, total_count

    async def get_pending_dependent_tasks(
        self, dependency_task_id: uuid.UUID
    ) -> List[Task]:
        """Get all tasks that are pending on the given dependency."""
        query = select(Task).where(
            Task.depends_on_task_id == dependency_task_id,
            Task.status == "pending_dependency",
        )

        result = await self.session.execute(query)
        tasks = list(result.scalars().all())
        return tasks

    async def update_dependent_tasks(self, dependency_task_id: uuid.UUID) -> List[Task]:
        """
        When a task is marked as done, update dependent tasks from 'pending_dependency' to 'todo'.
        Returns the list of updated tasks.
        """
        pending_tasks = await self.get_pending_dependent_tasks(dependency_task_id)

        for task in pending_tasks:
            task.status = "todo"

        await self.session.flush()
        await self.session.commit()  # Commit the transaction
        return pending_tasks

    async def acquire_lease(
        self, task_id: uuid.UUID, lease_owner_id: str, lease_duration_seconds: int = 300
    ) -> Optional[Task]:
        """Attempt to acquire a lease on a task."""
        now = datetime.now(timezone.utc)
        lease_expires_at = now + timedelta(seconds=lease_duration_seconds)

        # Try to acquire a lease
        query = (
            update(Task)
            .where(
                Task.id == task_id,
                or_(
                    # Case 1: Task has no lease
                    Task.lease_owner_id.is_(None),
                    # Case 2: Task lease has expired
                    Task.lease_expires_at < now,
                ),
                # Only lease tasks that can be processed (not done, cancelled, or failed)
                Task.status.notin_(["done", "cancelled", "failed"]),
            )
            .values(
                lease_owner_id=lease_owner_id,
                lease_acquired_at=now,
                lease_expires_at=lease_expires_at,
            )
            .execution_options(synchronize_session=False)
        )

        result = await self.session.execute(query)
        rows_affected: int = result.rowcount

        if rows_affected == 0:
            # Lease could not be acquired
            return None

        # Lease acquired, get the updated task
        await self.session.flush()
        task: Optional[Task] = await self.get_task_by_id(task_id)
        return task

    async def renew_lease(
        self, task_id: uuid.UUID, lease_owner_id: str, lease_duration_seconds: int = 300
    ) -> Optional[Task]:
        """
        Renews an existing lease if the caller is the current lease owner.
        """
        now = datetime.now(timezone.utc)
        lease_expires_at = now + timedelta(seconds=lease_duration_seconds)

        # Renew the lease
        query = (
            update(Task)
            .where(
                Task.id == task_id,
                Task.lease_owner_id == lease_owner_id,
            )
            .values(
                lease_expires_at=lease_expires_at,
            )
            .execution_options(synchronize_session=False)
        )

        result = await self.session.execute(query)
        rows_affected: int = result.rowcount

        if rows_affected == 0:
            # Lease could not be renewed (caller might not be the owner)
            return None

        # Lease renewed, get the updated task
        await self.session.flush()
        task: Optional[Task] = await self.get_task_by_id(task_id)
        return task

    async def release_lease(
        self, task_id: uuid.UUID, lease_owner_id: str
    ) -> Optional[Task]:
        """Release a lease on a task if caller is the current lease owner."""
        query = (
            update(Task)
            .where(
                Task.id == task_id,
                Task.lease_owner_id == lease_owner_id,
            )
            .values(
                lease_owner_id=None,
                lease_acquired_at=None,
                lease_expires_at=None,
            )
            .execution_options(synchronize_session=False)
        )

        result = await self.session.execute(query)
        rows_affected: int = result.rowcount

        if rows_affected == 0:
            # Lease could not be released (caller might not be the owner)
            return None

        # Lease released, get the updated task
        await self.session.flush()
        task: Optional[Task] = await self.get_task_by_id(task_id)
        return task
