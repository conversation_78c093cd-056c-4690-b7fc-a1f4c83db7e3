"""
Personal Assistant Response Generation Service.

This service orchestrates the automatic generation and sending of PA responses
when users send messages. It integrates the AI response service with the chat
service to provide seamless conversational AI functionality.
"""

import asyncio
import logging
import uuid
from typing import Any, List, Optional

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from a2a_platform.db.models.assistant import Assistant
from a2a_platform.db.models.chat_message import ChatMessage
from a2a_platform.db.models.conversation import Conversation
from a2a_platform.services.pydantic_ai_response_service import PydanticAIResponseService
from a2a_platform.services.assistant_service import AssistantService
from a2a_platform.services.chat_service import ChatService

logger = logging.getLogger(__name__)


# Lazy load settings to avoid import issues during testing
def _get_settings() -> Any:
    """Lazy load settings to handle test environments."""
    try:
        from a2a_platform.config.settings import get_settings

        return get_settings()
    except Exception:
        # Return a mock settings object for testing
        class MockSettings:
            AI_RESPONSE_ENABLED = True

        return MockSettings()


class PAResponseService:
    """Service for generating and sending Personal Assistant responses."""

    def __init__(self, db_session: AsyncSession):
        """Initialize the PA response service."""
        self.db = db_session
        self.chat_service = ChatService(db_session)
        self.assistant_service = AssistantService(db_session)
        self.ai_service = PydanticAIResponseService()

    async def _get_conversation_with_messages(
        self, conversation_id: uuid.UUID
    ) -> Optional[Conversation]:
        """Get conversation with recent messages for context."""
        try:
            result = await self.db.execute(
                select(Conversation)
                .options(selectinload(Conversation.messages))
                .where(Conversation.id == conversation_id)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error fetching conversation {conversation_id}: {str(e)}")
            return None

    async def _get_recent_messages(
        self, conversation_id: uuid.UUID, limit: int = 20
    ) -> List[ChatMessage]:
        """Get recent messages from the conversation for context."""
        try:
            result = await self.db.execute(
                select(ChatMessage)
                .where(ChatMessage.conversation_id == conversation_id)
                .order_by(ChatMessage.timestamp.desc())
                .limit(limit)
            )
            messages = result.scalars().all()
            # Return in chronological order (oldest first)
            return list(reversed(messages))
        except Exception as e:
            logger.error(
                f"Error fetching recent messages for conversation {conversation_id}: {str(e)}"
            )
            return []

    async def _get_user_assistant(self, user_id: uuid.UUID) -> Optional[Assistant]:
        """Get the user's personal assistant."""
        try:
            return await self.assistant_service.get_user_assistant(user_id)
        except Exception as e:
            logger.error(f"Error fetching assistant for user {user_id}: {str(e)}")
            return None

    def _extract_message_text(self, message: ChatMessage) -> str:
        """Extract text content from a structured message with enhanced error handling."""
        try:
            # Handle None content
            if message.content is None:
                logger.debug(f"Message {message.id} has None content")
                return ""

            # Handle dictionary content with parts structure
            if isinstance(message.content, dict):
                if "parts" in message.content:
                    parts = message.content["parts"]
                    if not isinstance(parts, list):
                        logger.debug(
                            f"Message {message.id} has non-list parts: {type(parts)}"
                        )
                        return str(message.content)

                    text_parts = []
                    for part in parts:
                        if isinstance(part, dict) and part.get("type") == "text":
                            content = part.get("content", "")
                            if content:  # Only add non-empty content
                                text_parts.append(str(content))

                    if text_parts:
                        return " ".join(text_parts).strip()
                    else:
                        logger.debug(
                            f"Message {message.id} has no text parts in parts structure"
                        )
                        return ""

                # Handle dictionary content with direct text field
                elif "text" in message.content:
                    return str(message.content["text"]).strip()

                # Handle dictionary content with other structures
                else:
                    # Try to find any text-like content in the dictionary
                    for key, value in message.content.items():
                        if isinstance(value, str) and value.strip():
                            return value.strip()
                    logger.debug(
                        f"Message {message.id} has dict content but no recognizable text fields"
                    )
                    return str(message.content)

            # Handle string content directly
            elif isinstance(message.content, str):
                return message.content.strip()

            # Handle other types by converting to string
            else:
                result = str(message.content).strip()
                if result and result != "None":
                    return result
                logger.debug(
                    f"Message {message.id} has non-standard content type: {type(message.content)}"
                )
                return ""

        except (AttributeError, KeyError, TypeError, IndexError) as e:
            logger.warning(f"Could not extract text from message {message.id}: {e}")
            return ""
        except Exception as e:
            logger.error(
                f"Unexpected error extracting text from message {message.id}: {str(e)}"
            )
            return ""

    def _should_generate_response(
        self, assistant: Assistant, user_message: str
    ) -> bool:
        """Determine if PA should generate a response."""
        # Check if AI responses are enabled globally
        settings = _get_settings()
        if not settings.AI_RESPONSE_ENABLED:
            logger.debug("AI responses disabled globally")
            return False

        # Check assistant-specific configuration
        config = assistant.configuration or {}
        ai_config = config.get("ai", {})

        # Check if responses are enabled for this assistant
        if not ai_config.get("enabled", True):
            logger.debug(f"AI responses disabled for assistant {assistant.id}")
            return False

        # Check for minimum message length
        min_length = ai_config.get("min_message_length", 3)
        if len(user_message.strip()) < min_length:
            logger.debug(
                f"Message too short for response: {len(user_message)} < {min_length}"
            )
            return False

        # Check for response probability (for testing/gradual rollout)
        response_probability = ai_config.get("response_probability", 1.0)
        if response_probability < 1.0:
            import random

            if random.random() > response_probability:
                logger.debug(
                    f"Skipping response due to probability: {response_probability}"
                )
                return False

        return True

    async def generate_and_send_response(
        self, conversation_id: uuid.UUID, user_message: ChatMessage, user_id: uuid.UUID
    ) -> Optional[ChatMessage]:
        """
        Generate and send a PA response to a user message.

        Args:
            conversation_id: The conversation ID
            user_message: The user's message that triggered the response
            user_id: The user ID for assistant lookup

        Returns:
            The created PA message if successful, None otherwise
        """
        try:
            # Get the user's assistant
            assistant = await self._get_user_assistant(user_id)
            if not assistant:
                logger.warning(f"No assistant found for user {user_id}")
                return None

            # Extract text from user message
            user_message_text = self._extract_message_text(user_message)
            if not user_message_text:
                logger.warning(f"Could not extract text from message {user_message.id}")
                return None

            # Check if we should generate a response
            if not self._should_generate_response(assistant, user_message_text):
                logger.debug(
                    f"Skipping response generation for message {user_message.id}"
                )
                return None

            # Get conversation history for context
            recent_messages = await self._get_recent_messages(conversation_id, limit=15)

            # Filter out the current user message from history to avoid duplication
            history_messages = [
                msg for msg in recent_messages if msg.id != user_message.id
            ]

            logger.info(f"Generating PA response for conversation {conversation_id}")

            # Generate AI response with fallback handling
            try:
                (
                    response_text,
                    is_ai_generated,
                ) = await self.ai_service.generate_response(
                    assistant=assistant,
                    user_message=user_message_text,
                    conversation_history=history_messages,
                )
            except Exception as ai_error:
                logger.warning(
                    f"AI service failed for conversation {conversation_id}: "
                    f"{ai_error}. Using fallback response."
                )
                # Provide fallback response if AI service fails completely
                fallback_msg = (
                    "I understand you've reached out. "
                    "Let me think about that and get back to you."
                )
                response_text = fallback_msg
                is_ai_generated = False

            if not response_text:
                logger.warning(
                    f"Empty response generated for conversation {conversation_id}"
                )
                return None

            # Add metadata about the response generation
            response_metadata = {
                "ai_generated": is_ai_generated,
                "response_to_message_id": str(user_message.id),
                "assistant_id": str(assistant.id),
                "generation_timestamp": user_message.timestamp.isoformat(),
            }

            # Send the PA response
            pa_message = await self.chat_service.send_pa_message(
                conversation_id=conversation_id,
                content=response_text,
                metadata=response_metadata,
                user_id=user_id,
            )

            logger.info(
                f"Successfully sent PA response {pa_message.id} "
                f"for conversation {conversation_id} (AI: {is_ai_generated})"
            )

            return pa_message

        except Exception as e:
            logger.error(
                f"Error generating PA response for conversation {conversation_id}: {str(e)}",
                exc_info=True,
            )
            return None

    async def generate_response_async(
        self, conversation_id: uuid.UUID, user_message: ChatMessage, user_id: uuid.UUID
    ) -> None:
        """
        Generate and send PA response asynchronously (fire-and-forget).

        This method is designed to be called without awaiting to avoid
        blocking the user message flow.
        """
        try:
            # Use the existing database session instead of creating a new one
            # This avoids database connection issues in test environments
            await self.generate_and_send_response(
                conversation_id, user_message, user_id
            )
        except Exception as e:
            # Log error but don't raise to avoid affecting the calling code
            logger.error(
                f"Async PA response generation failed for conversation {conversation_id}: {str(e)}",
                exc_info=True,
            )

    def schedule_response_generation(
        self, conversation_id: uuid.UUID, user_message: ChatMessage, user_id: uuid.UUID
    ) -> None:
        """
        Schedule PA response generation as a background task.

        This method creates an asyncio task that runs independently
        of the main request flow.
        """
        try:
            # Check if we're in a test environment or if the event loop is available
            try:
                loop = asyncio.get_running_loop()
                if loop.is_closed():
                    logger.warning(
                        "Event loop is closed, skipping PA response generation"
                    )
                    return
            except RuntimeError:
                logger.warning("No running event loop, skipping PA response generation")
                return

            # Create a background task for response generation
            task = asyncio.create_task(
                self.generate_response_async(conversation_id, user_message, user_id)
            )

            # Add a callback to log task completion/failure
            def task_done_callback(task_result: Any) -> None:
                try:
                    if task_result.exception():
                        logger.error(
                            f"Background PA response task failed: {task_result.exception()}",
                            exc_info=task_result.exception(),
                        )
                    else:
                        logger.debug(
                            f"Background PA response task completed for conversation {conversation_id}"
                        )
                except Exception as callback_error:
                    # Catch any errors in the callback to prevent them from propagating
                    logger.error(f"Error in task callback: {callback_error}")

            task.add_done_callback(task_done_callback)

            logger.debug(
                f"Scheduled PA response generation for conversation {conversation_id}"
            )

        except Exception as e:
            logger.error(f"Error scheduling PA response generation: {str(e)}")
