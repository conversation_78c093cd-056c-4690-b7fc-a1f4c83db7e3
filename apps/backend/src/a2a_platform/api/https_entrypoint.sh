#!/bin/sh
set -e

# Check if HTTPS is enabled
if [ "$ENFORCE_HTTPS" = "true" ]; then
    echo "Starting API server with HTTPS enabled..."
    # Check if SSL certificates exist
    if [ ! -f "/app/ssl-certs/localhost.pem" ] || [ ! -f "/app/ssl-certs/localhost-key.pem" ]; then
        echo "ERROR: SSL certificates not found. Please run ./scripts/generate-ssl-certs.sh first."
        exit 1
    fi
    
    # Start uvicorn with HTTPS
    exec uvicorn a2a_platform.main:app --host 0.0.0.0 --port "${PORT:-8000}" \
        --ssl-keyfile /app/ssl-certs/localhost-key.pem \
        --ssl-certfile /app/ssl-certs/localhost.pem
else
    echo "Starting API server with HTTP..."
    # Start uvicorn without HTTPS
    exec uvicorn a2a_platform.main:app --host 0.0.0.0 --port "${PORT:-8000}"
fi
