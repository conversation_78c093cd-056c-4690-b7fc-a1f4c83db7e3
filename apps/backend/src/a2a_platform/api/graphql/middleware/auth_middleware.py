import logging
from typing import Any, Async<PERSON>enerator, <PERSON>wai<PERSON>, Callable, Optional, Set

# import strawberry # Removed unused import
from fastapi import Depends, Request
from fastapi.security import HTTPAuthorizationCredentials
from graphql import GraphQLError, GraphQLResolveInfo  # Import from graphql library
from sqlalchemy.ext.asyncio import AsyncSession
from strawberry.extensions import SchemaExtension
from strawberry.fastapi import BaseContext

from a2a_platform.auth.clerk import Clerk<PERSON>uthMiddleware
from a2a_platform.auth.roles import RoleBasedAuth, UserRole
from a2a_platform.config.settings import get_settings
from a2a_platform.db import get_db_session

logger = logging.getLogger(__name__)


# Define a custom exception for authentication errors
class AuthenticationError(GraphQLError):
    """Exception raised when authentication is required but not provided."""

    pass


class GraphQLContext(BaseContext):
    """GraphQL Context that includes the authenticated user ID, roles, and database session."""

    clerk_user_id: Optional[str] = None
    db_session: Optional[AsyncSession] = None
    roles: Set[UserRole] = set()

    def __init__(
        self,
        clerk_user_id: Optional[str] = None,
        db_session: Optional[AsyncSession] = None,
    ):
        super().__init__()
        self.clerk_user_id = clerk_user_id
        self.db_session = db_session
        self.roles = (
            RoleBasedAuth.get_user_roles(clerk_user_id) if clerk_user_id else set()
        )

    @property
    def is_admin(self) -> bool:
        """Check if the user is an admin."""
        return UserRole.ADMIN in self.roles

    @property
    def is_developer(self) -> bool:
        """Check if the user is a developer."""
        return UserRole.DEVELOPER in self.roles


class AuthMiddleware(SchemaExtension):
    """
    GraphQL middleware to authenticate users via Clerk.

    This middleware verifies the JWT token provided in the request
    and makes the clerk_user_id available in the context for resolvers.
    """

    def __init__(self, execution_context: Optional[Any] = None) -> None:
        super().__init__(execution_context=execution_context)
        self.clerk_auth_service: ClerkAuthMiddleware = ClerkAuthMiddleware()

    async def on_operation(self) -> AsyncGenerator[None, None]:
        logger.debug("AuthMiddleware.on_operation: Entered")

        # execution_context should always be set by the framework
        if self.execution_context is None:
            raise ValueError(
                "Execution context is not set. This is required for AuthMiddleware to function correctly."
            )

        context_obj = self.execution_context.context  # Renamed to avoid conflict
        # Ensure clerk_user_id is initialized on the context object if not present
        if not hasattr(context_obj, "clerk_user_id"):
            context_obj.clerk_user_id = None
            logger.debug(
                "AuthMiddleware.on_operation: Initialized context.clerk_user_id to None"
            )

        logger.debug(
            f"AuthMiddleware.on_operation: Using self.clerk_auth_service of type: {type(self.clerk_auth_service)}"
        )

        if not isinstance(context_obj.request, Request):
            logger.warning(
                "AuthMiddleware.on_operation: Context has no HTTP request. Setting clerk_user_id to None."
            )
            context_obj.clerk_user_id = None
            yield
            return

        fastapi_request: Request = context_obj.request
        auth_credentials: Optional[
            HTTPAuthorizationCredentials
        ] = await self.clerk_auth_service(fastapi_request)

        clerk_user_id_str: Optional[str] = None
        if auth_credentials:
            clerk_user_id_str = auth_credentials.credentials
            logger.debug(
                f"Got clerk_user_id from auth_credentials: {clerk_user_id_str}"
            )

        # Test authentication bypass - check for test headers if normal auth failed
        if not clerk_user_id_str:
            from a2a_platform.config.settings import get_settings

            settings = get_settings()
            # Check both cases for header lookup
            x_test_auth = fastapi_request.headers.get(
                "X-Test-Auth"
            ) or fastapi_request.headers.get("x-test-auth")
            x_test_clerk_user_id = fastapi_request.headers.get(
                "X-Test-Clerk-User-Id"
            ) or fastapi_request.headers.get("x-test-clerk-user-id")
            if (
                settings.ENABLE_TEST_AUTH_BYPASS
                and settings.ENVIRONMENT != "production"
                and x_test_auth == "true"
            ):
                test_clerk_user_id = x_test_clerk_user_id
                if test_clerk_user_id:
                    logger.warning(
                        f"SECURITY: Using test authentication bypass for user: {test_clerk_user_id}. "
                        f"This should ONLY be used in testing environments."
                    )
                    clerk_user_id_str = test_clerk_user_id
                else:
                    logger.info(
                        "Test auth bypass enabled but no X-Test-Clerk-User-Id header"
                    )
            else:
                logger.info("Test auth bypass conditions not met")

        context_obj.clerk_user_id = clerk_user_id_str
        logger.debug(
            f"AuthMiddleware.on_operation: Set context.clerk_user_id to: {context_obj.clerk_user_id}"
        )

        # Set user roles in the context
        if clerk_user_id_str:
            context_obj.roles = RoleBasedAuth.get_user_roles(clerk_user_id_str)
            logger.debug(
                f"AuthMiddleware.on_operation: Set context.roles to: {context_obj.roles}"
            )
        else:
            context_obj.roles = set()
            logger.debug("AuthMiddleware.on_operation: Set context.roles to empty set")

        yield

    def resolve(
        self,
        _next: Callable[..., Awaitable[object]],
        root: Any,
        info: GraphQLResolveInfo,
        *args: Any,
        **kwargs: Any,
    ) -> Awaitable[object]:
        # Log the CLERK_JWT_PUBLIC_KEY environment variable value
        settings = get_settings()
        jwt_public_key = settings.CLERK_JWT_PUBLIC_KEY
        if jwt_public_key:
            # Truncate the key for logging to avoid exposing the full key
            truncated_key = (
                jwt_public_key[:30] + "..."
                if len(jwt_public_key) > 30
                else jwt_public_key
            )
            logger.debug(
                f"AuthMiddleware.resolve: CLERK_JWT_PUBLIC_KEY is set with length {len(jwt_public_key)} chars: {truncated_key}"
            )
        else:
            logger.warning(
                "AuthMiddleware.resolve: CLERK_JWT_PUBLIC_KEY environment variable is not set"
            )

        # Perform checks on info.context which should be our GraphQLContext
        # This context is set by the context_getter in GraphQLRouter
        current_context = info.context
        if not isinstance(current_context, GraphQLContext):
            logger.error(
                f"AuthMiddleware.resolve: info.context (from GraphQLResolveInfo) is not GraphQLContext. It is {type(current_context)}. This indicates a misconfiguration."
            )

            # Return an awaitable that raises an error
            async def _config_error_raiser() -> object:
                raise RuntimeError(
                    "Server Configuration Error: Invalid GraphQL context."
                )

            return _config_error_raiser()  # Return the coroutine (Awaitable[object])

        # Now current_context is known to be GraphQLContext
        # No explicit cast needed for MyPy due to isinstance check, but good for clarity:
        current_graphql_context: GraphQLContext = current_context

        current_clerk_user_id = getattr(
            current_graphql_context, "clerk_user_id", "NOT SET ON CONTEXT"
        )
        logger.debug(
            f"AuthMiddleware.resolve: Entered for field '{info.field_name}'. Current clerk_user_id: {current_clerk_user_id}"
        )

        requires_auth = self._operation_requires_auth(info)
        logger.debug(
            f"AuthMiddleware.resolve: Field '{info.field_name}' requires_auth: {requires_auth}"
        )

        if requires_auth and not current_graphql_context.clerk_user_id:
            logger.warning(
                f"AuthMiddleware.resolve: Auth FAILED for '{info.field_name}'. clerk_user_id is '{current_graphql_context.clerk_user_id}'. Raising 'Authentication required' error."
            )

            # Return an awaitable that raises the authentication error
            async def _auth_error_raiser() -> object:
                raise Exception("Authentication required for this operation.")

            return _auth_error_raiser()  # Return the coroutine (Awaitable[object])

        if requires_auth and current_graphql_context.clerk_user_id:
            logger.debug(
                f"AuthMiddleware.resolve: Auth SUCCEEDED for '{info.field_name}'. clerk_user_id is '{current_graphql_context.clerk_user_id}'."
            )

        logger.debug(
            f"AuthMiddleware.resolve: Proceeding to next resolver for '{info.field_name}'."
        )
        # Directly return the awaitable from _next if all checks pass
        return _next(root, info, *args, **kwargs)

    def _operation_requires_auth(self, info: GraphQLResolveInfo) -> bool:
        """
        Check if the operation requires authentication

        This is determined by the presence of @strawberry.permission_classes
        with IsAuthenticated on the resolver or by manually checking the field name
        against a list of protected fields.

        Args:
            info: GraphQL resolver info

        Returns:
            bool: True if the operation requires authentication, False otherwise
        """
        logger.debug(
            f"AuthMiddleware._operation_requires_auth: Checking field: {info.field_name}"
        )
        protected_fields = [
            # User profile fields
            "me",
            "updateUser",
            "updateMyProfile",
            # Agent Marketplace fields
            "listMarketplaceAgents",
            "getMarketplaceAgent",
            "registerMarketplaceAgent",
            "updateMarketplaceAgent",
            "updateAgentReviewStatus",
            "createCliToken",  # CLI token creation requires authentication
            # Assistant fields
            "myAssistant",
            "userHasAssistant",
            "createPersonalAssistant",
            "updatePersonalAssistant",
            # Chat fields - require authentication
            "sendMessage",
            "sendMessageFromPA",
            "getOrCreateConversation",
            "getConversationMessages",
        ]
        is_protected = info.field_name in protected_fields
        logger.debug(
            f"AuthMiddleware._operation_requires_auth: Field '{info.field_name}' is_protected: {is_protected}"
        )
        return is_protected


def get_current_user_id(context: GraphQLContext) -> str:
    """
    Get the current user ID from the GraphQL context.

    Args:
        context: The GraphQL context containing user information

    Returns:
        str: The current user's Clerk ID

    Raises:
        Exception: If the user is not authenticated
    """
    # Test-only authentication bypass (explicit feature flag required)
    settings = get_settings()
    if (
        settings.ENABLE_TEST_AUTH_BYPASS
        and settings.ENVIRONMENT != "production"
        and hasattr(context, "request")
        and context.request
        and context.request.headers
    ):
        if context.request.headers.get("X-Test-Auth") == "true":
            test_clerk_user_id = context.request.headers.get("X-Test-Clerk-User-Id")
            if test_clerk_user_id:
                logger.warning(
                    f"SECURITY: Using test authentication bypass for user: {test_clerk_user_id}. "
                    f"This should ONLY be used in testing environments."
                )
                return test_clerk_user_id

    # Normal authentication check
    if not context.clerk_user_id:
        raise Exception("Authentication required for this operation.")
    return context.clerk_user_id


async def get_graphql_context(
    request: Request,
    db_session: AsyncSession = Depends(get_db_session),
) -> GraphQLContext:
    """
    Provides a GraphQLContext instance.
    AuthMiddleware is responsible for populating clerk_user_id.
    This function injects the database session into the context.

    Args:
        request: Optional request object passed by GraphQLRouter
        db_session: Database session from FastAPI dependency injection

    Returns:
        GraphQLContext: A new GraphQLContext instance with db_session
    """
    try:
        logger.debug(
            "get_graphql_context called, returning new GraphQLContext instance with db_session."
        )
        context = GraphQLContext(db_session=db_session)
        context.request = request
        logger.debug("GraphQLContext created successfully")
        return context
    except Exception as e:
        logger.error(f"Error creating GraphQLContext: {e}", exc_info=True)
        raise
