"""
GraphQL error handling middleware for database errors and API hardening.
"""

import logging
from typing import Any, Dict, List, Optional, Tuple

from graphql import GraphQLError
from sqlalchemy.exc import (
    IntegrityError,
    OperationalError,
    StatementError,
    TimeoutError as SQLTimeoutError,
)
from strawberry.types import Info

logger = logging.getLogger(__name__)


class GraphQLDatabaseError(GraphQLError):
    """Custom GraphQL error for database-related issues."""

    def __init__(
        self,
        message: str,
        error_code: str = "DATABASE_ERROR",
        original_exception: Optional[Exception] = None,
    ):
        extensions: dict[str, Any] = {
            "code": error_code,
        }
        if original_exception:
            extensions["exception"] = {
                "type": type(original_exception).__name__,
                "message": str(original_exception),
            }

        super().__init__(message=message, extensions=extensions)


class GraphQLContextError(GraphQLError):
    """Custom GraphQL error for context-related issues."""

    def __init__(self, message: str, error_code: str = "CONTEXT_ERROR"):
        super().__init__(message=message, extensions={"code": error_code})


# Table-driven error patterns
ERROR_PATTERNS: List[Dict[str, Any]] = [
    {
        "type": IntegrityError,
        "patterns": [
            (
                "unique",
                "DUPLICATE_RECORD",
                "A record with this information already exists",
            ),
            ("foreign", "INVALID_REFERENCE", "Referenced record does not exist"),
        ],
        "default": ("INTEGRITY_ERROR", "Data integrity constraint violated"),
        "log_level": "warning",
    },
    {
        "type": OperationalError,
        "patterns": [
            (
                "connection",
                "CONNECTION_ERROR",
                "Database connection temporarily unavailable. Please try again.",
            ),
            ("timeout", "TIMEOUT_ERROR", "Operation timed out. Please try again."),
        ],
        "default": (
            "OPERATIONAL_ERROR",
            "Database operation failed. Please try again.",
        ),
        "log_level": "error",
    },
    {
        "type": SQLTimeoutError,
        "patterns": [],
        "default": ("TIMEOUT_ERROR", "Operation timed out. Please try again."),
        "log_level": "error",
    },
    {
        "type": StatementError,
        "patterns": [],
        "default": ("STATEMENT_ERROR", "Invalid database operation"),
        "log_level": "error",
    },
]


def handle_database_error(
    error: Exception,
    operation_context: str = "database operation",
    user_id: Optional[str] = None,
) -> GraphQLDatabaseError:
    """
    Handle database errors and convert them to appropriate GraphQL errors.

    Args:
        error: The original database exception
        operation_context: Description of the operation that failed
        user_id: Optional user ID for logging context

    Returns:
        GraphQLDatabaseError with appropriate message and error code
    """
    user_context = f" for user {user_id}" if user_id else ""
    error_str = str(error).lower()

    for entry in ERROR_PATTERNS:
        error_type = entry["type"]
        if isinstance(error, error_type):
            # Try all patterns for this error type
            patterns: List[Tuple[str, str, str]] = entry["patterns"]
            for keyword, code, msg in patterns:
                if keyword in error_str:
                    log_level: str = entry["log_level"]
                    log = getattr(logger, log_level)
                    log(f"{msg} during {operation_context}{user_context}: {error}")
                    return GraphQLDatabaseError(
                        message=msg,
                        error_code=code,
                        original_exception=error,
                    )
            # If no keyword matched, use the default for this exception type
            default: Tuple[str, str] = entry["default"]
            code, msg = default
            log_level = entry["log_level"]
            log = getattr(logger, log_level)
            log(f"{msg} during {operation_context}{user_context}: {error}")
            return GraphQLDatabaseError(
                message=msg,
                error_code=code,
                original_exception=error,
            )

    # Fallback for any unknown database error
    logger.error(
        f"Unexpected database error during {operation_context}{user_context}: {error}",
        exc_info=True,
    )
    return GraphQLDatabaseError(
        message="An unexpected database error occurred",
        error_code="UNKNOWN_DATABASE_ERROR",
        original_exception=error,
    )


def get_db_session_safely(info: Info, operation_name: str = "operation") -> Any:
    """
    Safely get database session from GraphQL context with proper error handling.

    Args:
        info: GraphQL resolver info containing the context
        operation_name: Name of the operation for better error messages

    Returns:
        Database session from context

    Raises:
        GraphQLContextError: If database session is not available
    """
    try:
        context = info.context
        if not hasattr(context, "db_session") or context.db_session is None:
            logger.error(f"No database session found in context for {operation_name}")
            raise GraphQLContextError(
                message="Database session not available. Please try again.",
                error_code="NO_DB_SESSION",
            )

        return context.db_session

    except AttributeError as e:
        logger.error(f"Context attribute error in {operation_name}: {e}")
        raise GraphQLContextError(
            message="Invalid request context",
            error_code="INVALID_CONTEXT",
        )


def get_user_id_safely(info: Info, operation_name: str = "operation") -> str:
    """
    Safely get user ID from GraphQL context with proper error handling.

    Args:
        info: GraphQL resolver info containing the context
        operation_name: Name of the operation for better error messages

    Returns:
        User ID from context

    Raises:
        GraphQLContextError: If user is not authenticated
    """
    try:
        from a2a_platform.api.graphql.middleware.auth_middleware import (
            get_current_user_id,
        )

        user_id = get_current_user_id(info.context)
        if not user_id:
            logger.warning(f"Unauthenticated access attempt to {operation_name}")
            raise GraphQLContextError(
                message="Authentication required for this operation",
                error_code="AUTHENTICATION_REQUIRED",
            )

        return user_id

    except Exception as e:
        logger.error(f"Error getting user ID for {operation_name}: {e}")
        raise GraphQLContextError(
            message="Authentication verification failed",
            error_code="AUTH_VERIFICATION_FAILED",
        )


async def execute_db_operation_safely(
    operation_func: Any,
    operation_name: str,
    user_id: Optional[str] = None,
    **kwargs: Any,
) -> Any:
    """
    Execute a database operation with comprehensive error handling.

    Args:
        operation_func: The database operation function to execute
        operation_name: Description of the operation for logging
        user_id: Optional user ID for context
        **kwargs: Arguments to pass to the operation function

    Returns:
        Result of the operation function

    Raises:
        GraphQLDatabaseError: For database-related errors
        GraphQLError: For other errors
    """
    try:
        return await operation_func(**kwargs)

    except (
        IntegrityError,
        OperationalError,
        SQLTimeoutError,
        StatementError,
    ) as db_error:
        raise handle_database_error(db_error, operation_name, user_id)

    except ValueError as ve:
        logger.warning(f"Validation error in {operation_name}: {ve}")
        raise GraphQLError(message=str(ve), extensions={"code": "VALIDATION_ERROR"})

    except PermissionError as pe:
        logger.warning(f"Permission error in {operation_name}: {pe}")
        raise GraphQLError(message=str(pe), extensions={"code": "PERMISSION_DENIED"})

    except Exception as e:
        logger.error(
            f"Unexpected error in {operation_name}: {e}",
            exc_info=True,
        )
        raise GraphQLError(
            message=f"An unexpected error occurred during {operation_name}",
            extensions={"code": "INTERNAL_ERROR"},
        )
