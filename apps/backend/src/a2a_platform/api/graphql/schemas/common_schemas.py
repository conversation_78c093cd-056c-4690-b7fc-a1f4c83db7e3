"""
Common GraphQL schemas and types.
"""

from typing import Generic, Optional, TypeVar

import strawberry

# Generic type for entities that can be returned in payloads
T = TypeVar("T")


@strawberry.type
class MutationResponse:
    """Generic response type for mutations."""

    success: bool = True
    message: str = "Operation completed successfully"


@strawberry.type
class EntityMutationPayload(Generic[T]):
    """Generic payload for mutations that return an entity."""

    success: bool = True
    message: str = "Operation completed successfully"
    # Note: The entity field will be added by concrete implementations
    # since Strawberry doesn't support generic types directly


@strawberry.type
class ErrorPayload:
    """Payload for error responses."""

    success: bool = False
    message: str
    error_code: Optional[str] = None
