"""
GraphQL schemas for the Agent Marketplace.
"""

from typing import Any, Dict, List, Optional

import strawberry
from strawberry.types import Info

from a2a_platform.db.enums import AgentReviewStatus, AgentStatus


@strawberry.type
class AgentSkill:
    """Represents a skill an agent possesses."""

    name: str
    description: Optional[str] = None
    parameters_schema: Optional[str] = None  # JSON schema as string


@strawberry.type
class AgentPricingInfo:
    """Pricing information for an agent in the marketplace."""

    pricing_type: str  # e.g., "subscription", "per_call", "free"
    currency: Optional[str] = None
    amount_monthly: Optional[float] = None
    amount_per_call: Optional[float] = None
    features: Optional[List[str]] = None
    tier_id: Optional[str] = None

    @classmethod
    def from_json(cls, json_data: Dict[str, Any]) -> Optional["AgentPricingInfo"]:
        """Create an AgentPricingInfo from a JSON dictionary."""
        if not json_data:
            return None

        pricing_type = json_data.get("type", "free")
        currency = json_data.get("currency")
        amount_monthly = json_data.get("amount_monthly")
        amount_per_call = json_data.get("amount_per_call")
        features = json_data.get("features")
        tier_id = json_data.get("tier_id")

        return cls(
            pricing_type=pricing_type,
            currency=currency,
            amount_monthly=amount_monthly,
            amount_per_call=amount_per_call,
            features=features,
            tier_id=tier_id,
        )


@strawberry.type
class MarketplaceAgent:
    """Represents an agent in the marketplace."""

    agent_definition_id: str
    name: str
    description: Optional[str] = None
    version: str
    developer_id: Optional[str] = None
    pricing_info: Optional[AgentPricingInfo] = None
    review_status: Optional[AgentReviewStatus] = None
    status: AgentStatus
    skills: List[AgentSkill]
    created_at: str
    updated_at: str


@strawberry.input
class AgentPricingInfoInput:
    """Input type for agent pricing information."""

    pricing_type: str
    currency: Optional[str] = None
    amount_monthly: Optional[float] = None
    amount_per_call: Optional[float] = None
    features: Optional[List[str]] = None
    tier_id: Optional[str] = None

    def to_json(self) -> Dict[str, Any]:
        """Convert to JSON dictionary."""
        result: Dict[str, Any] = {"type": self.pricing_type}
        if self.currency:
            result["currency"] = self.currency
        if self.amount_monthly is not None:
            result["amount_monthly"] = self.amount_monthly
        if self.amount_per_call is not None:
            result["amount_per_call"] = self.amount_per_call
        if self.features:
            result["features"] = self.features
        if self.tier_id:
            result["tier_id"] = self.tier_id
        return result


@strawberry.input
class AgentSkillInput:
    """Input type for agent skills."""

    name: str
    description: Optional[str] = None
    parameters_schema: Optional[str] = None  # JSON schema as string

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result: Dict[str, Any] = {"name": self.name}
        if self.description:
            result["description"] = self.description
        if self.parameters_schema:
            import json

            result["parameters_schema"] = json.loads(self.parameters_schema)
        return result


@strawberry.input
class RegisterMarketplaceAgentInput:
    """Input for registering a new agent in the marketplace."""

    agent_definition_id: str
    name: str
    description: Optional[str] = None
    version: str
    developer_id: str
    pricing_info: Optional[AgentPricingInfoInput] = None
    skills: List[AgentSkillInput]
    endpoint_url: Optional[str] = None
    async_queue_name: Optional[str] = None


@strawberry.input
class UpdateMarketplaceAgentInput:
    """Input for updating an existing agent in the marketplace."""

    agent_definition_id: str
    name: Optional[str] = None
    description: Optional[str] = None
    version: Optional[str] = None
    pricing_info: Optional[AgentPricingInfoInput] = None
    skills: Optional[List[AgentSkillInput]] = None
    status: Optional[AgentStatus] = None
    endpoint_url: Optional[str] = None
    async_queue_name: Optional[str] = None


@strawberry.type
class AgentMarketplaceQuery:
    """GraphQL queries for the Agent Marketplace."""

    @strawberry.field
    async def listMarketplaceAgents(
        self, info: Info, status: Optional[AgentStatus] = None
    ) -> List[MarketplaceAgent]:
        """
        List all agents in the marketplace.

        Args:
            info: GraphQL resolver info containing the context
            status: Optional filter for agent status ('active' or 'inactive')

        Returns:
            List of marketplace agents
        """
        # This is just a schema definition
        # The actual implementation is in the resolver
        raise NotImplementedError("This is a schema definition only")

    @strawberry.field
    async def getMarketplaceAgent(
        self, info: Info, agent_definition_id: str
    ) -> Optional[MarketplaceAgent]:
        """
        Get a specific agent from the marketplace by ID.

        Args:
            info: GraphQL resolver info containing the context
            agent_definition_id: The ID of the agent to retrieve

        Returns:
            The marketplace agent if found, None otherwise
        """
        # This is just a schema definition
        # The actual implementation is in the resolver
        raise NotImplementedError("This is a schema definition only")


@strawberry.type
class AgentMarketplaceMutation:
    """GraphQL mutations for the Agent Marketplace."""

    @strawberry.field
    async def registerMarketplaceAgent(
        self, info: Info, input: RegisterMarketplaceAgentInput
    ) -> MarketplaceAgent:
        """
        Register a new agent in the marketplace.

        Args:
            info: GraphQL resolver info containing the context
            input: The agent data to register

        Returns:
            The newly registered marketplace agent
        """
        # This is just a schema definition
        # The actual implementation is in the resolver
        raise NotImplementedError("This is a schema definition only")

    @strawberry.field
    async def updateMarketplaceAgent(
        self, info: Info, input: UpdateMarketplaceAgentInput
    ) -> MarketplaceAgent:
        """
        Update an existing agent in the marketplace.

        Args:
            info: GraphQL resolver info containing the context
            input: The agent data to update

        Returns:
            The updated marketplace agent
        """
        # This is just a schema definition
        # The actual implementation is in the resolver
        raise NotImplementedError("This is a schema definition only")

    @strawberry.field
    async def updateAgentReviewStatus(
        self, info: Info, agent_definition_id: str, review_status: AgentReviewStatus
    ) -> MarketplaceAgent:
        """
        Update the review status of an agent in the marketplace.

        Args:
            info: GraphQL resolver info containing the context
            agent_definition_id: The ID of the agent to update
            review_status: The new review status ('pending', 'approved', 'rejected')

        Returns:
            The updated marketplace agent
        """
        # This is just a schema definition
        # The actual implementation is in the resolver
        raise NotImplementedError("This is a schema definition only")
