from datetime import datetime
from typing import Optional

import strawberry
from strawberry.scalars import JSO<PERSON>

from a2a_platform.db.models.cli_token import (
    CliToken as CliTokenModel,  # Assuming this is the path
)
from a2a_platform.schemas.user import User


@strawberry.type
class CliToken:
    """GraphQL type for CLI access tokens."""

    id: strawberry.ID
    tokenPrefix: str = strawberry.field(default_factory=str)
    description: Optional[str]
    createdAt: datetime
    expiresAt: Optional[datetime]
    lastUsedAt: Optional[datetime]
    userId: strawberry.ID

    @classmethod
    def from_db_model(cls, model: CliTokenModel) -> "CliToken":
        return cls(
            id=strawberry.ID(str(model.id)),
            tokenPrefix=model.token_prefix,
            description=model.description,
            createdAt=model.created_at,
            expiresAt=model.expires_at,
            lastUsedAt=model.last_used_at,
            userId=strawberry.ID(str(model.user_id)),
        )


@strawberry.input
class CreateCliTokenInput:
    description: Optional[str] = None
    # TODO: Add expiresAt once we decide on handling datetime inputs in GraphQL
    # expiresAt: Optional[datetime] = None


@strawberry.type
class CreateCliTokenPayload:
    token: str  # The full, unhashed token for the user to copy
    cliToken: CliToken  # The persisted token record


@strawberry.input
class UserProfilePreferencesInput:
    """Input type for updating user profile preferences"""

    preferences: Optional[JSON] = None
    timezone: Optional[str] = None


@strawberry.type
class UserProfileMutation:
    """GraphQL mutations for user profile operations"""

    @strawberry.field
    async def updateMyProfile(self, timezone: str) -> User:
        """
        Update the authenticated user's profile with a new timezone

        Args:
            timezone: The new timezone value to set

        Returns:
            The updated User object
        """
        # This is just a schema definition
        # The actual implementation is in the resolver
        raise NotImplementedError("This is a schema definition only")

    @strawberry.field
    async def updateUserProfilePreferences(
        self, info: strawberry.Info, input: UserProfilePreferencesInput
    ) -> User:
        """
        Update the authenticated user's profile preferences and optionally timezone

        This mutation allows updating the user's preferences JSON object and/or timezone.
        The preferences are deep-merged with existing preferences.

        Args:
            info: GraphQL resolver info containing the context
            input: The preferences and timezone data to update

        Returns:
            The updated User object
        """
        # This is just a schema definition
        # The actual implementation is in the resolver
        raise NotImplementedError("This is a schema definition only")

    @strawberry.mutation
    async def create_cli_token(
        self, info: strawberry.Info, input: CreateCliTokenInput
    ) -> CreateCliTokenPayload:
        """
        Creates a new CLI access token for the authenticated user.
        The full token is returned only once upon creation.
        """
        # This is just a schema definition
        # The actual implementation is in the resolver
        raise NotImplementedError("This is a schema definition only")
