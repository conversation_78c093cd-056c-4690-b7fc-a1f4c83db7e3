"""
GraphQL schemas for Chat operations.
"""

import uuid
from datetime import datetime
from typing import TYPE_CHECKING, Any, Optional

import strawberry
from strawberry.scalars import JSON

from a2a_platform.schemas.pagination_schemas import Connection, ConnectionArgs, Edge


if TYPE_CHECKING:
    pass


@strawberry.type
class Conversation:
    """GraphQL type for Conversation."""

    id: uuid.UUID
    user_id: uuid.UUID
    assistant_id: uuid.UUID
    created_at: datetime
    last_message_at: Optional[datetime] = None
    message_count: int = 0


@strawberry.type
class ChatMessage:
    """GraphQL type for ChatMessage."""

    id: uuid.UUID
    conversation_id: uuid.UUID
    timestamp: datetime
    metadata: Optional[JSON] = None
    # Private field for internal use
    _sender_role: strawberry.Private[str]
    _content: strawberry.Private[Any] = None

    @strawberry.field
    def sender_role(self) -> str:
        """Get the sender role in uppercase."""
        return self._sender_role.upper()

    @strawberry.field
    def content(self) -> JSON:
        """Transform the internal content format to GraphQL expected format.

        Optimized version with early returns and reduced complexity.
        """
        # Handle None case early
        if self._content is None:
            return {"text": ""}

        # Handle non-dict content early
        if not isinstance(self._content, dict):
            return {"text": ""}

        # If content is already in the expected format, return it
        if "text" in self._content:
            return self._content

        # Handle parts format with optimized logic
        parts = self._content.get("parts")
        if not parts or not isinstance(parts, list):
            return {"text": ""}

        # Optimized content extraction from parts
        content_parts = []
        for part in parts:
            if isinstance(part, dict) and "content" in part:
                content = part.get("content", "")
                if content:  # Only add non-empty content
                    content_parts.append(str(content))

        return {"text": " ".join(content_parts) if content_parts else ""}

    @classmethod
    def from_db_model(cls, message: "Any") -> "ChatMessage":
        """Convert from SQLAlchemy model to GraphQL type."""
        return cls(
            id=message.id,
            conversation_id=message.conversation_id,
            _sender_role=message.sender_role,
            _content=message.content,
            timestamp=message.timestamp,
            # Strawberry will handle JSON scalar conversion automatically
            metadata=message.message_metadata or {},
        )


@strawberry.input
class SendMessageInput:
    """Input type for sending a message."""

    conversation_id: strawberry.ID
    content: str


@strawberry.input
class SendMessageFromPAInput:
    """Input type for sending a message from the Personal Assistant."""

    conversation_id: strawberry.ID
    content: str
    metadata: Optional[JSON] = None


@strawberry.type
class SendMessagePayload:
    """Payload for sendMessage mutation."""

    message: ChatMessage
    success: bool = True
    error_message: Optional[str] = None


@strawberry.type
class MessageSubscriptionPayload:
    """Payload for message subscription events."""

    message: ChatMessage
    conversation_id: uuid.UUID
    event_type: str = "new_message"


# Relay-style connection types for ChatMessage pagination


@strawberry.type
class ChatMessageEdge(Edge[ChatMessage]):
    """An edge in a ChatMessage connection."""

    pass


@strawberry.type
class ChatMessageConnection(Connection[ChatMessage]):
    """A connection to a list of ChatMessage nodes."""

    pass


@strawberry.input
class ChatMessageConnectionArgs(ConnectionArgs):
    """Connection arguments for ChatMessage pagination."""

    pass
