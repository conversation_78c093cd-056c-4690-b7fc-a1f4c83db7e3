"""
GraphQL schemas for Assistant operations.
"""

import uuid
from datetime import datetime
from typing import Optional

import strawberry
from strawberry.scalars import JSON

from a2a_platform.db.enums import AssistantStatus
from a2a_platform.schemas.assistant_schemas import AssistantRead


@strawberry.type
class Assistant:
    """GraphQL type for Assistant."""

    id: uuid.UUID
    user_id: uuid.UUID
    name: str
    backstory: str
    avatar_file_id: Optional[uuid.UUID] = None
    configuration: Optional["JSON"] = None
    status: AssistantStatus
    created_at: datetime
    updated_at: datetime

    @classmethod
    def from_pydantic(cls, assistant: Assistant<PERSON>ead) -> "Assistant":
        """Convert from Pydantic schema to GraphQL type.

        Optimized conversion with validation.
        """
        try:
            return cls(
                id=assistant.id,
                user_id=assistant.user_id,
                name=assistant.name,
                backstory=assistant.backstory,
                avatar_file_id=assistant.avatar_file_id,
                configuration=assistant.configuration or {},  # Ensure non-null JSON
                status=AssistantStatus(assistant.status)
                if hasattr(assistant, "status")
                else AssistantStatus.SETUP_PENDING,
                created_at=assistant.created_at,
                updated_at=assistant.updated_at,
            )
        except Exception as e:
            # Log conversion error but provide a fallback
            import logging

            logging.getLogger(__name__).error(
                f"Error converting assistant to GraphQL type: {e}"
            )
            # Create a minimal valid assistant object
            return cls(
                id=assistant.id,
                user_id=assistant.user_id,
                name=assistant.name or "Unknown Assistant",
                backstory=assistant.backstory or "",
                avatar_file_id=assistant.avatar_file_id,
                configuration={},
                status=AssistantStatus.SETUP_PENDING,
                created_at=assistant.created_at,
                updated_at=assistant.updated_at,
            )


@strawberry.input
class CreateAssistantInput:
    """Input type for creating an assistant."""

    name: str
    backstory: str
    avatar_file_id: Optional[uuid.UUID] = None
    configuration: Optional["JSON"] = None
    status: Optional[AssistantStatus] = AssistantStatus.SETUP_PENDING


@strawberry.input
class UpdateAssistantInput:
    """Input type for updating an assistant."""

    name: Optional[str] = None
    backstory: Optional[str] = None
    avatar_file_id: Optional[uuid.UUID] = None
    configuration: Optional["JSON"] = None
    status: Optional[AssistantStatus] = None


@strawberry.type
class AssistantMutationPayload:
    """Unified payload for assistant mutations."""

    assistant: Assistant
    success: bool = True
    message: str = "Assistant operation completed successfully"


# Type aliases for backward compatibility and clarity
CreateAssistantPayload = AssistantMutationPayload
UpdateAssistantPayload = AssistantMutationPayload
