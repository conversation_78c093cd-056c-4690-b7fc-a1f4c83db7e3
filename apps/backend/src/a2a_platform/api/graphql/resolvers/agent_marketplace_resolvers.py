"""
GraphQL resolvers for the Agent Marketplace.
"""

import json
import logging
from typing import List, Optional

from graphql import GraphQLError
from strawberry.types import Info

from a2a_platform.api.graphql.schemas.agent_marketplace_schemas import (
    AgentPricingInfo,
    AgentSkill,
    MarketplaceAgent,
    RegisterMarketplaceAgentInput,
    UpdateMarketplaceAgentInput,
)
from a2a_platform.db.enums import AgentReviewStatus, AgentStatus
from a2a_platform.db.models.registered_agent import (
    RegisteredAgent as RegisteredAgentModel,
)
from a2a_platform.schemas.agent_schemas import (
    RegisteredAgentCreate,
    RegisteredAgentUpdate,
)
from a2a_platform.services.agent_service import AgentService

# Set up logger
logger = logging.getLogger(__name__)


def _convert_db_agent_to_graphql(db_agent: RegisteredAgentModel) -> MarketplaceAgent:
    """
    Convert a database agent model to a GraphQL MarketplaceAgent type.

    Args:
        db_agent: The database agent model to convert

    Returns:
        The converted MarketplaceAgent
    """
    # Convert skills from JSON to AgentSkill objects
    skills: List[AgentSkill] = []
    if db_agent.skills and isinstance(db_agent.skills, list):
        for skill_data in db_agent.skills:
            if isinstance(skill_data, dict):
                parameters_schema = None
                if "parameters_schema" in skill_data:
                    parameters_schema = json.dumps(skill_data["parameters_schema"])

                skills.append(
                    AgentSkill(
                        name=skill_data["name"],
                        description=skill_data.get("description"),
                        parameters_schema=parameters_schema,
                    )
                )

    # Convert pricing_info from JSON to AgentPricingInfo
    pricing_info = None
    if db_agent.pricing_info and isinstance(db_agent.pricing_info, dict):
        pricing_info = AgentPricingInfo.from_json(db_agent.pricing_info)

    # Convert fields to strings if not None
    description = None
    if db_agent.description:
        description = str(db_agent.description)

    developer_id = None
    if db_agent.developer_id:
        developer_id = str(db_agent.developer_id)

    review_status = None
    if db_agent.review_status:
        review_status = AgentReviewStatus(str(db_agent.review_status))

    # Format timestamps
    created_at = ""
    if db_agent.created_at:
        created_at = db_agent.created_at.isoformat()

    updated_at = ""
    if db_agent.updated_at:
        updated_at = db_agent.updated_at.isoformat()

    return MarketplaceAgent(
        agent_definition_id=str(db_agent.agent_definition_id),
        name=str(db_agent.name),
        description=description,
        version=str(db_agent.version),
        developer_id=developer_id,
        pricing_info=pricing_info,
        review_status=review_status,
        status=AgentStatus(str(db_agent.status)),
        skills=skills,
        created_at=created_at,
        updated_at=updated_at,
    )


async def resolve_list_marketplace_agents(
    info: Info, status: Optional[AgentStatus] = None
) -> List[MarketplaceAgent]:
    """
    Resolver for the listMarketplaceAgents query.

    Args:
        info: GraphQL resolver info containing the context
        status: Optional filter for agent status ('active' or 'inactive')

    Returns:
        List of marketplace agents
    """
    # Check if the user is authenticated
    context = info.context
    clerk_user_id = context.clerk_user_id
    if not clerk_user_id:
        logger.warning("Attempt to list marketplace agents without authentication")
        raise GraphQLError(
            "Authentication required for this operation",
            extensions={"code": "UNAUTHENTICATED"},
        )

    # Get the database session from the context
    if not hasattr(context, "db_session") or context.db_session is None:
        logger.error("No database session found in GraphQL context")
        raise GraphQLError(
            "Server configuration error: No database session available",
            extensions={"code": "INTERNAL_SERVER_ERROR"},
        )

    try:
        # Create an agent service instance
        agent_service = AgentService(db_session=context.db_session)

        # Handle both enum and string inputs for status
        status_str = None
        if status is not None:
            if hasattr(status, "value"):
                status_str = status.value
            else:
                # If it's a string, validate it's a valid status
                try:
                    if hasattr(status, "lower"):
                        status_enum = AgentStatus(status.lower())
                        status_str = status_enum.value
                    else:
                        # Assume it's already an enum if it doesn't have lower method
                        status_str = str(status)
                except (ValueError, AttributeError):
                    # If invalid status provided, treat as None
                    logger.warning(f"Invalid status value provided: {status}")
                    status_str = None

        # Get agents from the database
        # If the user is not an admin, only show active agents
        if not context.is_admin and status_str != "active":
            logger.info(
                f"User {clerk_user_id} is not an admin, showing only active agents"
            )
            status_str = "active"

        # If the user is a developer, we'll filter their own agents in the loop below

        agents, _ = await agent_service.list_agents(status=status_str)

        # Filter agents based on user role
        filtered_agents = []
        for agent in agents:
            # Admins can see all agents
            if context.is_admin:
                filtered_agents.append(agent)
            # Developers can see their own agents and active agents
            elif context.is_developer and (
                agent.developer_id == clerk_user_id or agent.status == "active"
            ):
                filtered_agents.append(agent)
            # Regular users can only see active agents
            elif agent.status == "active":
                filtered_agents.append(agent)

        # Convert to GraphQL types
        return [_convert_db_agent_to_graphql(agent) for agent in filtered_agents]
    except GraphQLError:
        # Re-raise GraphQLError exceptions
        raise
    except Exception as e:
        logger.error(f"Error listing marketplace agents: {str(e)}")
        raise GraphQLError(
            f"Error listing marketplace agents: {str(e)}",
            extensions={"code": "INTERNAL_SERVER_ERROR"},
        )


async def resolve_get_marketplace_agent(
    info: Info, agent_definition_id: str
) -> Optional[MarketplaceAgent]:
    """
    Resolver for the getMarketplaceAgent query.

    Args:
        info: GraphQL resolver info containing the context
        agent_definition_id: The ID of the agent to retrieve

    Returns:
        The marketplace agent if found, None otherwise
    """
    # Check if the user is authenticated
    context = info.context
    clerk_user_id = context.clerk_user_id
    if not clerk_user_id:
        logger.warning("Attempt to get marketplace agent without authentication")
        raise GraphQLError(
            "Authentication required for this operation",
            extensions={"code": "UNAUTHENTICATED"},
        )

    # Get the database session from the context
    if not hasattr(context, "db_session") or context.db_session is None:
        logger.error("No database session found in GraphQL context")
        raise GraphQLError(
            "Server configuration error: No database session available",
            extensions={"code": "INTERNAL_SERVER_ERROR"},
        )

    # Validate agent_definition_id
    if not agent_definition_id or not agent_definition_id.strip():
        raise GraphQLError(
            "Agent definition ID is required", extensions={"code": "BAD_USER_INPUT"}
        )

    try:
        # Create an agent service instance
        agent_service = AgentService(db_session=context.db_session)

        # Get the agent from the database
        agent = await agent_service.get_agent_by_definition_id(agent_definition_id)
        if not agent:
            raise GraphQLError(
                f"Agent with ID '{agent_definition_id}' not found",
                extensions={"code": "NOT_FOUND"},
            )

        # Check if the user has permission to view the agent
        # If the user is not an admin and the agent is not active, they can't view it
        # unless they are the developer of the agent
        if (
            not context.is_admin
            and agent.status != "active"
            and agent.developer_id != clerk_user_id
        ):
            logger.warning(
                f"User {clerk_user_id} attempted to view inactive agent {agent_definition_id} "
                f"owned by {agent.developer_id}"
            )
            raise GraphQLError(
                "You do not have permission to view this agent",
                extensions={"code": "FORBIDDEN"},
            )

        # Convert to GraphQL type
        return _convert_db_agent_to_graphql(agent)
    except GraphQLError:
        # Re-raise GraphQLError exceptions
        raise
    except Exception as e:
        logger.error(f"Error getting marketplace agent: {str(e)}")
        raise GraphQLError(
            f"Error getting marketplace agent: {str(e)}",
            extensions={"code": "INTERNAL_SERVER_ERROR"},
        )


async def resolve_register_marketplace_agent(
    info: Info, input: RegisterMarketplaceAgentInput
) -> MarketplaceAgent:
    """
    Resolver for the registerMarketplaceAgent mutation.

    Args:
        info: GraphQL resolver info containing the context
        input: The agent data to register

    Returns:
        The newly registered marketplace agent
    """
    # Check if the user is authenticated
    context = info.context
    clerk_user_id = context.clerk_user_id
    if not clerk_user_id:
        logger.warning("Attempt to register marketplace agent without authentication")
        raise GraphQLError(
            "Authentication required for this operation",
            extensions={"code": "UNAUTHENTICATED"},
        )

    # Get the database session from the context
    if not hasattr(context, "db_session") or context.db_session is None:
        logger.error("No database session found in GraphQL context")
        raise GraphQLError(
            "Server configuration error: No database session available",
            extensions={"code": "INTERNAL_SERVER_ERROR"},
        )

    # Check if the user has permission to register an agent
    if not context.is_developer and not context.is_admin:
        logger.warning(
            f"User {clerk_user_id} attempted to register an agent without developer role"
        )
        raise GraphQLError(
            "Permission denied: You must have developer privileges to register an agent",
            extensions={"code": "FORBIDDEN"},
        )

    # Validate required fields
    if not input.agent_definition_id or not input.agent_definition_id.strip():
        raise GraphQLError(
            "Agent definition ID is required", extensions={"code": "BAD_USER_INPUT"}
        )

    if not input.name or not input.name.strip():
        raise GraphQLError(
            "Agent name is required", extensions={"code": "BAD_USER_INPUT"}
        )

    if not input.version or not input.version.strip():
        raise GraphQLError(
            "Agent version is required", extensions={"code": "BAD_USER_INPUT"}
        )

    try:
        # Convert skills from input to list of dicts
        skills = [skill.to_dict() for skill in input.skills]

        # Convert pricing_info from input to dict
        pricing_info = input.pricing_info.to_json() if input.pricing_info else {}

        # Validate that the developer_id matches the authenticated user
        # Only admins can register agents for other developers
        if input.developer_id != clerk_user_id and not context.is_admin:
            logger.warning(
                f"User {clerk_user_id} attempted to register an agent for developer {input.developer_id}"
            )
            raise GraphQLError(
                "Permission denied: You can only register agents for yourself",
                extensions={"code": "FORBIDDEN"},
            )

        # Create agent data
        agent_data = RegisteredAgentCreate(
            agent_definition_id=input.agent_definition_id,
            name=input.name,
            description=input.description,
            version=input.version,
            developer_id=input.developer_id,
            pricing_info=pricing_info,
            review_status=AgentReviewStatus.PENDING.value,  # New agents start as pending
            skills=skills,
            endpoint_url=input.endpoint_url,
            async_queue_name=input.async_queue_name,
            status=AgentStatus.INACTIVE.value,  # New agents start as inactive until approved
        )

        # Create an agent service instance
        agent_service = AgentService(db_session=context.db_session)

        # Register the agent
        db_agent = await agent_service.register_agent(agent_data)

        # Convert to GraphQL type
        return _convert_db_agent_to_graphql(db_agent)
    except ValueError as e:
        # Handle specific validation errors
        logger.warning(f"Validation error registering marketplace agent: {str(e)}")
        raise GraphQLError(
            f"Validation error: {str(e)}", extensions={"code": "BAD_USER_INPUT"}
        )
    except GraphQLError:
        # Re-raise GraphQLError exceptions
        raise
    except Exception as e:
        logger.error(f"Error registering marketplace agent: {str(e)}")
        raise GraphQLError(
            f"Error registering marketplace agent: {str(e)}",
            extensions={"code": "INTERNAL_SERVER_ERROR"},
        )


async def resolve_update_marketplace_agent(
    info: Info, input: UpdateMarketplaceAgentInput
) -> MarketplaceAgent:
    """
    Resolver for the updateMarketplaceAgent mutation.

    Args:
        info: GraphQL resolver info containing the context
        input: The agent data to update

    Returns:
        The updated marketplace agent
    """
    # Check if the user is authenticated
    context = info.context
    clerk_user_id = context.clerk_user_id
    if not clerk_user_id:
        logger.warning("Attempt to update marketplace agent without authentication")
        raise GraphQLError(
            "Authentication required for this operation",
            extensions={"code": "UNAUTHENTICATED"},
        )

    # Get the database session from the context
    if not hasattr(context, "db_session") or context.db_session is None:
        logger.error("No database session found in GraphQL context")
        raise GraphQLError(
            "Server configuration error: No database session available",
            extensions={"code": "INTERNAL_SERVER_ERROR"},
        )

    # Validate agent_definition_id
    if not input.agent_definition_id or not input.agent_definition_id.strip():
        raise GraphQLError(
            "Agent definition ID is required", extensions={"code": "BAD_USER_INPUT"}
        )

    try:
        # Create an agent service instance
        agent_service = AgentService(db_session=context.db_session)

        # Check if the agent exists
        existing_agent = await agent_service.get_agent_by_definition_id(
            input.agent_definition_id
        )
        if not existing_agent:
            raise GraphQLError(
                f"Agent with ID '{input.agent_definition_id}' not found",
                extensions={"code": "NOT_FOUND"},
            )

        # Check if the user has permission to update the agent
        # Only the developer of the agent or an admin can update it
        if existing_agent.developer_id != clerk_user_id and not context.is_admin:
            logger.warning(
                f"User {clerk_user_id} attempted to update agent owned by {existing_agent.developer_id}"
            )
            raise GraphQLError(
                "Permission denied: You do not have permission to update this agent",
                extensions={"code": "FORBIDDEN"},
            )

        # Convert skills from input to list of dicts if provided
        skills = None
        if input.skills is not None:
            skills = [skill.to_dict() for skill in input.skills]

        # Convert pricing_info from input to dict if provided
        pricing_info = None
        if input.pricing_info is not None:
            pricing_info = input.pricing_info.to_json()

        # Create update data
        update_data = RegisteredAgentUpdate(
            name=input.name if input.name is not None else existing_agent.name,
            description=input.description,
            version=input.version
            if input.version is not None
            else existing_agent.version,
            pricing_info=pricing_info,
            skills=skills,
            endpoint_url=input.endpoint_url,
            async_queue_name=input.async_queue_name,
        )

        # Only admins can change the status directly
        # Regular developers can only update their own agents' metadata
        if input.status is not None:
            if context.is_admin:
                update_data.status = input.status.value
            else:
                logger.warning(
                    f"User {clerk_user_id} attempted to change agent status to {input.status}"
                )
                raise GraphQLError(
                    "Permission denied: Only administrators can change agent status",
                    extensions={"code": "FORBIDDEN"},
                )

        # Update the agent
        updated_agent = await agent_service.update_agent(
            input.agent_definition_id, update_data
        )

        # Convert to GraphQL type
        return _convert_db_agent_to_graphql(updated_agent)
    except ValueError as e:
        # Handle specific validation errors
        logger.warning(f"Validation error updating marketplace agent: {str(e)}")
        raise GraphQLError(
            f"Validation error: {str(e)}", extensions={"code": "BAD_USER_INPUT"}
        )
    except GraphQLError:
        # Re-raise GraphQLError exceptions
        raise
    except Exception as e:
        logger.error(f"Error updating marketplace agent: {str(e)}")
        raise GraphQLError(
            f"Error updating marketplace agent: {str(e)}",
            extensions={"code": "INTERNAL_SERVER_ERROR"},
        )


async def resolve_update_agent_review_status(
    info: Info, agent_definition_id: str, review_status: AgentReviewStatus
) -> MarketplaceAgent:
    """
    Resolver for the updateAgentReviewStatus mutation.

    Args:
        info: GraphQL resolver info containing the context
        agent_definition_id: The ID of the agent to update
        review_status: The new review status ('pending', 'approved', 'rejected')

    Returns:
        The updated marketplace agent
    """
    # Check if the user is authenticated and is an admin
    context = info.context
    clerk_user_id = context.clerk_user_id
    if not clerk_user_id:
        logger.warning("Attempt to update agent review status without authentication")
        raise GraphQLError(
            "Authentication required for this operation",
            extensions={"code": "UNAUTHENTICATED"},
        )

    # Check if the user is an admin
    if not context.is_admin:
        logger.warning(
            f"User {clerk_user_id} attempted to update agent review status without admin privileges"
        )
        raise GraphQLError(
            "Permission denied: Only administrators can update agent review status",
            extensions={"code": "FORBIDDEN"},
        )

    # Get the database session from the context
    if not hasattr(context, "db_session") or context.db_session is None:
        logger.error("No database session found in GraphQL context")
        raise GraphQLError(
            "Server configuration error: No database session available",
            extensions={"code": "INTERNAL_SERVER_ERROR"},
        )

    # Validate agent_definition_id
    if not agent_definition_id or not agent_definition_id.strip():
        raise GraphQLError(
            "Agent definition ID is required", extensions={"code": "BAD_USER_INPUT"}
        )

    # Enum validation is handled by Strawberry automatically

    try:
        # Create an agent service instance
        agent_service = AgentService(db_session=context.db_session)

        # Check if the agent exists
        existing_agent = await agent_service.get_agent_by_definition_id(
            agent_definition_id
        )
        if not existing_agent:
            raise GraphQLError(
                f"Agent with ID '{agent_definition_id}' not found",
                extensions={"code": "NOT_FOUND"},
            )

        # Create update data - handle both enum and string inputs
        review_status_str = (
            review_status.value if hasattr(review_status, "value") else review_status
        )

        # Convert string to enum for comparison if needed
        if isinstance(review_status, str):
            try:
                review_status_enum = AgentReviewStatus(review_status.lower())
                is_approved = review_status_enum == AgentReviewStatus.APPROVED
            except ValueError:
                is_approved = review_status.lower() == "approved"
        else:
            is_approved = review_status == AgentReviewStatus.APPROVED

        update_data = RegisteredAgentUpdate(
            review_status=review_status_str,
            # If approved, set status to active; if rejected, set to inactive
            status=AgentStatus.ACTIVE.value
            if is_approved
            else AgentStatus.INACTIVE.value,
        )

        # Update the agent
        updated_agent = await agent_service.update_agent(
            agent_definition_id, update_data
        )

        # Log the review action
        logger.info(
            f"Admin {clerk_user_id} updated agent {agent_definition_id} review status to {review_status}"
        )

        # Convert to GraphQL type
        return _convert_db_agent_to_graphql(updated_agent)
    except ValueError as e:
        # Handle specific validation errors
        logger.warning(f"Validation error updating agent review status: {str(e)}")
        raise GraphQLError(
            f"Validation error: {str(e)}", extensions={"code": "BAD_USER_INPUT"}
        )
    except GraphQLError:
        # Re-raise GraphQLError exceptions
        raise
    except Exception as e:
        logger.error(f"Error updating agent review status: {str(e)}")
        raise GraphQLError(
            f"Error updating agent review status: {str(e)}",
            extensions={"code": "INTERNAL_SERVER_ERROR"},
        )
