"""
GraphQL resolvers for Chat operations.
"""

import asyncio
import logging
import uuid
from datetime import datetime
from typing import Any, Optional, cast

import strawberry
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from strawberry.types import Info

from a2a_platform.api.graphql.middleware.auth_middleware import (
    get_current_user_id,
)
from a2a_platform.api.graphql.schemas.chat_schemas import (
    ChatMessage,
    ChatMessageConnection,
    ChatMessageConnectionArgs,
    Conversation,
    SendMessageFromPAInput,
    SendMessagePayload,
)

from a2a_platform.db.models.chat_message import ChatMessage as ChatMessageModel
from a2a_platform.services.chat_service import ChatService
from a2a_platform.services.user_service import get_user_by_clerk_id

logger = logging.getLogger(__name__)


def _create_dummy_chat_message(sender_role: str = "user") -> ChatMessage:
    """
    Create a dummy ChatMessage for error responses.

    This helper function eliminates code duplication across error handling paths
    in GraphQL resolvers by providing a consistent way to create placeholder
    ChatMessage instances.

    Args:
        sender_role: The role of the sender ("user" or "agent")

    Returns:
        A dummy ChatMessage instance with empty content
    """
    return ChatMessage(
        id=uuid.uuid4(),
        conversation_id=uuid.uuid4(),
        _sender_role=sender_role.lower(),
        _content={"parts": [{"type": "text", "content": ""}]},
        timestamp=datetime.now(),
        metadata={},
    )


def get_db_session_from_context(info: Info) -> AsyncSession:
    """
    Get the database session from the GraphQL context.

    Args:
        info: GraphQL resolver info containing the context

    Returns:
        The database session from the context

    Raises:
        Exception: If no database session is found in the context
    """
    db_session = getattr(info.context, "db_session", None)
    if db_session is None:
        raise Exception("Database session not found in GraphQL context")
    # Type assertion to help MyPy understand the return type
    return db_session  # type: ignore[no-any-return]


async def _schedule_pa_response_async(
    conversation_id: uuid.UUID,
    user_message: ChatMessageModel,
    user_id: uuid.UUID,
) -> None:
    """
    Schedule PA response generation asynchronously in a background task.

    This function is designed to be called as a fire-and-forget task
    to avoid blocking the main message sending flow.

    Note: This function creates its own database session to avoid
    connection lifecycle issues with the main request session.
    """
    try:
        # Import here to avoid circular dependencies
        from a2a_platform.db import get_session_factory
        from a2a_platform.services.pa_response_service import PAResponseService

        # Create a new session for this background task
        async_session_factory = get_session_factory()
        async with async_session_factory() as task_session:
            pa_service = PAResponseService(task_session)
            pa_service.schedule_response_generation(
                conversation_id=conversation_id,
                user_message=user_message,
                user_id=user_id,
            )
            await task_session.commit()
            logger.debug(f"Scheduled PA response for message {user_message.id}")
    except Exception as pa_error:
        # Don't fail the user message if PA response scheduling fails
        logger.warning(
            f"Failed to schedule PA response: {str(pa_error)}", exc_info=True
        )


async def resolve_send_message(
    info: Info, conversation_id: strawberry.ID, content: str
) -> SendMessagePayload:
    """
    Send a message to a conversation.

    Args:
        info: GraphQL resolver info containing the context
        conversation_id: The ID of the conversation
        content: The message content

    Returns:
        The send message payload with the created message

    Raises:
        Exception: If the user is not authenticated or message sending fails
    """
    clerk_user_id = get_current_user_id(info.context)
    db_session = get_db_session_from_context(info)

    try:
        # Get the user by Clerk ID to get the internal user UUID
        user = await get_user_by_clerk_id(db_session, clerk_user_id)
        if not user:
            logger.warning(f"User not found for clerk_user_id: {clerk_user_id}")
            raise Exception(
                "User account not found. Please try refreshing the page or "
                "contact support if the issue persists."
            )

        # Validate input
        if not content or not content.strip():
            # Create a dummy message for type safety
            dummy_message = _create_dummy_chat_message("user")
            return SendMessagePayload(
                message=dummy_message,
                success=False,
                error_message="Message content cannot be empty",
            )

        if len(content) > 4000:
            # Create a dummy message for type safety
            dummy_message = _create_dummy_chat_message("user")
            return SendMessagePayload(
                message=dummy_message,
                success=False,
                error_message="Message content cannot exceed 4000 characters",
            )

        # Convert conversation_id from strawberry.ID to UUID
        try:
            conv_uuid = uuid.UUID(str(conversation_id))
        except ValueError:
            # Create a dummy message for type safety
            dummy_message = _create_dummy_chat_message("user")
            return SendMessagePayload(
                message=dummy_message,
                success=False,
                error_message="Invalid conversation ID format",
            )

        chat_service = ChatService(db_session)

        # Send the message
        message_model = await chat_service.send_message(
            user_id=user.id,
            conversation_id=conv_uuid,
            content=content,
            metadata=None,
        )

        # Convert to GraphQL type
        message_gql = ChatMessage.from_db_model(message_model)

        # Schedule PA response generation asynchronously (fire-and-forget)
        # This is moved to a background task to avoid blocking the response
        # Create a new session for the background task to avoid connection issues
        try:
            # Don't await this task - it's intentionally fire-and-forget
            # Use asyncio.create_task but don't store reference to avoid warnings
            task = asyncio.create_task(
                _schedule_pa_response_async(
                    conversation_id=conv_uuid,
                    user_message=message_model,
                    user_id=user.id,
                )
            )
            # Explicitly suppress the task result to avoid warnings about unawaited tasks
            task.add_done_callback(lambda _: None)
        except Exception as task_error:
            # Log but don't fail the main request if background task creation fails
            logger.warning(f"Failed to create PA response task: {str(task_error)}")

        logger.info(
            f"Successfully sent message in conversation {conversation_id} "
            f"for user {user.id}"
        )

        return SendMessagePayload(
            message=message_gql,
            success=True,
            error_message=None,
        )

    except ValueError as e:
        logger.error(
            f"Validation error sending message for user {clerk_user_id}: {str(e)}"
        )
        # Create a dummy message for type safety
        dummy_message = _create_dummy_chat_message("user")
        return SendMessagePayload(
            message=dummy_message,
            success=False,
            error_message=str(e),
        )
    except Exception as e:
        logger.error(
            f"Unexpected error sending message for user {clerk_user_id}: {str(e)}"
        )
        # Create a dummy message for type safety
        dummy_message = _create_dummy_chat_message("user")
        return SendMessagePayload(
            message=dummy_message,
            success=False,
            error_message=("An unexpected error occurred while sending the message"),
        )


async def resolve_send_message_from_pa(
    info: Info, input_data: SendMessageFromPAInput
) -> SendMessagePayload:
    """
    Send a message from the Personal Assistant to a conversation.

    Args:
        info: GraphQL resolver info containing the context
        input_data: The input data containing conversation_id, content, and metadata

    Returns:
        The send message payload with the created PA message

    Raises:
        Exception: If message sending fails or conversation doesn't exist
    """
    # Require authentication for PA messages
    clerk_user_id = get_current_user_id(info.context)
    if not clerk_user_id:
        logger.warning("Unauthorized attempt to send PA message")
        # Create a dummy message for type safety
        dummy_message = _create_dummy_chat_message("agent")
        return SendMessagePayload(
            message=dummy_message,
            success=False,
            error_message="Authentication required",
        )

    db_session = get_db_session_from_context(info)

    # Get the user by Clerk ID to get the internal user UUID
    user = await get_user_by_clerk_id(db_session, clerk_user_id)
    if not user:
        logger.warning(f"User not found for clerk_user_id: {clerk_user_id}")
        dummy_message = _create_dummy_chat_message("agent")
        return SendMessagePayload(
            message=dummy_message,
            success=False,
            error_message="User account not found",
        )

    try:
        # Convert conversation_id from strawberry.ID to UUID
        try:
            conv_uuid = uuid.UUID(str(input_data.conversation_id))
        except ValueError:
            # Create a dummy message for type safety
            dummy_message = _create_dummy_chat_message("agent")
            return SendMessagePayload(
                message=dummy_message,
                success=False,
                error_message="Invalid conversation ID format",
            )

        # Validate input
        if not input_data.content or not input_data.content.strip():
            # Create a dummy message for type safety
            dummy_message = _create_dummy_chat_message("agent")
            return SendMessagePayload(
                message=dummy_message,
                success=False,
                error_message="PA message content cannot be empty",
            )

        # First verify that the conversation belongs to the authenticated user
        from a2a_platform.db.models.conversation import Conversation

        # Query to get the conversation and verify it belongs to the current user
        result = await db_session.execute(
            select(Conversation).where(
                Conversation.id == conv_uuid, Conversation.user_id == user.id
            )
        )
        conversation = result.scalar_one_or_none()

        if not conversation:
            logger.warning(
                f"User {user.id} attempted to access conversation {conv_uuid} that doesn't belong to them"
            )
            dummy_message = _create_dummy_chat_message("agent")
            return SendMessagePayload(
                message=dummy_message,
                success=False,
                error_message="Conversation not found or access denied",
            )

        chat_service = ChatService(db_session)

        # Send the PA message with verified user ownership
        message_model = await chat_service.send_pa_message(
            conversation_id=conv_uuid,
            content=input_data.content,
            metadata=(
                cast(dict[str, Any], input_data.metadata)
                if input_data.metadata
                else None
            ),
        )

        # Convert to GraphQL type
        message_gql = ChatMessage.from_db_model(message_model)

        logger.info(f"Successfully sent PA message in conversation {conv_uuid}")

        return SendMessagePayload(
            message=message_gql,
            success=True,
            error_message=None,
        )

    except ValueError as e:
        logger.error(f"Validation error sending PA message: {str(e)}")
        # Create a dummy message for type safety
        dummy_message = _create_dummy_chat_message("agent")
        return SendMessagePayload(
            message=dummy_message,
            success=False,
            error_message=str(e),
        )
    except Exception as e:
        logger.error(f"Unexpected error sending PA message: {str(e)}")
        # Create a dummy message for type safety
        dummy_message = _create_dummy_chat_message("agent")
        return SendMessagePayload(
            message=dummy_message,
            success=False,
            error_message=("An unexpected error occurred while sending the PA message"),
        )


async def resolve_get_or_create_conversation(info: Info) -> Conversation:
    """
    Get or create a conversation between the authenticated user and their assistant.

    Args:
        info: GraphQL resolver info containing the context

    Returns:
        The conversation between the user and their assistant

    Raises:
        GraphQLError: If the user is not authenticated, doesn't have an assistant,
                     or database operation fails
    """
    from a2a_platform.api.graphql.middleware.error_handling import (
        execute_db_operation_safely,
        get_db_session_safely,
        get_user_id_safely,
        handle_database_error,
    )
    from sqlalchemy.exc import IntegrityError, OperationalError

    # Safely get authentication and database context
    clerk_user_id = get_user_id_safely(info, "get_or_create_conversation")
    db_session = get_db_session_safely(info, "get_or_create_conversation")

    async def _get_or_create_conversation_operation() -> tuple[Any, Any]:
        # Get the user by Clerk ID to get the internal user UUID
        user = await get_user_by_clerk_id(db_session, clerk_user_id)
        if not user:
            logger.warning(f"User not found for clerk_user_id: {clerk_user_id}")
            raise ValueError(
                "User account not found. Please try refreshing the page or "
                "contact support if the issue persists."
            )

        # Get the user's assistant with error handling
        from a2a_platform.services.assistant_service import AssistantService

        assistant_service = AssistantService(db_session)

        try:
            assistant = await assistant_service.get_user_assistant(user.id)
        except (OperationalError, IntegrityError) as db_error:
            logger.error(
                f"Database error getting assistant for user {user.id}: {db_error}"
            )
            raise handle_database_error(
                db_error, "getting user assistant", clerk_user_id
            )

        if not assistant:
            raise ValueError(
                "No personal assistant found. Please complete the assistant setup first."
            )

        # Get or create conversation with comprehensive error handling
        chat_service = ChatService(db_session)

        try:
            conversation_model = await chat_service.get_or_create_conversation(
                user_id=user.id, assistant_id=assistant.id
            )
        except (OperationalError, IntegrityError) as db_error:
            logger.error(
                f"Database error in get_or_create_conversation for user {user.id}, "
                f"assistant {assistant.id}: {db_error}"
            )
            raise handle_database_error(
                db_error, "creating conversation", clerk_user_id
            )
        except Exception as unexpected_error:
            logger.error(
                f"Unexpected error in get_or_create_conversation for user {user.id}: {unexpected_error}",
                exc_info=True,
            )
            raise ValueError("Failed to create conversation. Please try again.")

        return conversation_model, user

    try:
        # Execute the main operation with error handling
        conversation_model, user = await execute_db_operation_safely(
            _get_or_create_conversation_operation,
            "get_or_create_conversation",
            clerk_user_id,
        )

        # Get message count for the conversation with error handling
        # Use a more efficient query with subquery optimization
        from a2a_platform.db.models.chat_message import (
            ChatMessage as ChatMessageModel,
        )
        from sqlalchemy import func

        try:
            message_count_query = select(func.count()).select_from(
                select(ChatMessageModel.id)
                .where(ChatMessageModel.conversation_id == conversation_model.id)
                .subquery()
            )
            message_count_result = await db_session.execute(message_count_query)
            message_count = message_count_result.scalar() or 0
        except (OperationalError, IntegrityError) as db_error:
            logger.warning(
                f"Database error getting message count for conversation {conversation_model.id}: {db_error}"
            )
            # Use 0 as fallback for message count if query fails
            message_count = 0

        # Convert to GraphQL type
        conversation_gql = Conversation(
            id=conversation_model.id,
            user_id=conversation_model.user_id,
            assistant_id=conversation_model.assistant_id,
            created_at=conversation_model.created_at,
            last_message_at=conversation_model.last_message_at,
            message_count=message_count,
        )

        logger.info(
            f"Retrieved conversation {conversation_model.id} for user {user.id}"
        )
        return conversation_gql

    except Exception as e:
        # All database errors should already be handled by execute_db_operation_safely
        # This catch-all is for any remaining unexpected errors
        logger.error(
            f"Final catch-all error in get_or_create_conversation for user {clerk_user_id}: {str(e)}",
            exc_info=True,
        )
        from graphql import GraphQLError

        raise GraphQLError(
            message="Unable to access conversation. Please try again.",
            extensions={"code": "CONVERSATION_ACCESS_ERROR"},
        )


async def resolve_get_conversation_messages(
    info: Info,
    conversation_id: strawberry.ID,
    pagination: Optional[ChatMessageConnectionArgs] = None,
) -> ChatMessageConnection:
    """
    Get messages for a specific conversation using cursor-based pagination.

    Args:
        info: GraphQL resolver info containing the context
        conversation_id: The ID of the conversation
        pagination: Cursor pagination arguments (first, after, last, before)

    Returns:
        ChatMessageConnection with edges, page info, and total count

    Raises:
        Exception: If the user is not authenticated or doesn't have access
    """
    clerk_user_id = get_current_user_id(info.context)
    db_session = get_db_session_from_context(info)

    try:
        # Get the user by Clerk ID to get the internal user UUID
        user = await get_user_by_clerk_id(db_session, clerk_user_id)
        if not user:
            logger.warning(f"User not found for clerk_user_id: {clerk_user_id}")
            raise Exception(
                "User account not found. Please try refreshing the page or "
                "contact support if the issue persists."
            )

        # Convert conversation_id from strawberry.ID to UUID
        try:
            conv_uuid = uuid.UUID(str(conversation_id))
        except ValueError:
            raise Exception("Invalid conversation ID format")

        # Verify user has access to this conversation
        from a2a_platform.db.models.conversation import (
            Conversation as ConversationModel,
        )

        conversation_query = select(ConversationModel).where(
            ConversationModel.id == conv_uuid, ConversationModel.user_id == user.id
        )
        conversation_result = await db_session.execute(conversation_query)
        conversation = conversation_result.scalar_one_or_none()

        if not conversation:
            raise Exception("Conversation not found or access denied")

        # Use pagination args or defaults
        if pagination is None:
            pagination = ChatMessageConnectionArgs()

        # Get messages using cursor pagination
        chat_service = ChatService(db_session)
        connection = await chat_service.get_conversation_messages_connection(
            conversation_id=conv_uuid,
            first=pagination.first,
            after=pagination.after,
            last=pagination.last,
            before=pagination.before,
        )

        # Note: Database models are already converted to GraphQL types in the service layer

        logger.info(
            f"Retrieved {len(connection.edges)} messages for conversation "
            f"{conversation_id} for user {user.id}"
        )

        return connection

    except ValueError as e:
        logger.error(
            f"Validation error getting messages for user {clerk_user_id}: {str(e)}"
        )
        raise Exception(str(e))
    except Exception as e:
        logger.error(
            f"Unexpected error getting messages for user {clerk_user_id}: {str(e)}"
        )
        raise Exception("An unexpected error occurred while fetching messages")
