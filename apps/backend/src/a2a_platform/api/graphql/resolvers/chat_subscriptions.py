"""
GraphQL subscription resolvers for chat functionality.

This module provides subscription resolvers for real-time chat features,
including new message notifications and conversation updates.
"""

import logging
import uuid
from typing import Async<PERSON><PERSON>ator

import strawberry
from sqlalchemy.ext.asyncio import AsyncSession
from strawberry.types import Info

from a2a_platform.api.graphql.middleware.auth_middleware import (
    get_current_user_id,
)
from a2a_platform.api.graphql.schemas.chat_schemas import (
    MessageSubscriptionPayload,
)
from a2a_platform.db.models.conversation import Conversation
from a2a_platform.messaging.subscription_manager import SubscriptionManager
from a2a_platform.services.user_service import get_user_by_clerk_id

logger = logging.getLogger(__name__)


def get_db_session_from_context(info: Info) -> AsyncSession:
    """
    Get the database session from the GraphQL context.

    Args:
        info: GraphQL resolver info containing the context

    Returns:
        The database session from the context

    Raises:
        Exception: If no database session is found in the context
    """
    db_session = getattr(info.context, "db_session", None)
    if db_session is None:
        raise Exception("Database session not found in GraphQL context")
    # Type assertion to help My<PERSON><PERSON> understand the return type
    return db_session  # type: ignore[no-any-return]


class ChatSubscriptionResolvers:
    """GraphQL subscription resolvers for chat functionality."""

    @staticmethod
    async def resolve_new_messages(
        info: Info, conversation_id: strawberry.ID
    ) -> AsyncGenerator[MessageSubscriptionPayload, None]:
        """
        Subscribe to new messages in a conversation.

        Args:
            info: GraphQL resolver info containing the context
            conversation_id: The ID of the conversation to subscribe to

        Yields:
            MessageSubscriptionPayload: New message events

        Raises:
            Exception: If the user is not authenticated or doesn't have access
        """
        clerk_user_id = get_current_user_id(info.context)
        db_session = get_db_session_from_context(info)

        try:
            # Get the user by Clerk ID to get the internal user UUID
            user = await get_user_by_clerk_id(db_session, clerk_user_id)
            if not user:
                logger.warning(f"User not found for clerk_user_id: {clerk_user_id}")
                raise Exception("User account not found")

            # Convert conversation_id from strawberry.ID to UUID
            try:
                conv_uuid = uuid.UUID(str(conversation_id))
            except ValueError:
                raise Exception("Invalid conversation ID format")

            # Verify user has access to the conversation
            from sqlalchemy import select

            result = await db_session.execute(
                select(Conversation).where(
                    Conversation.id == conv_uuid, Conversation.user_id == user.id
                )
            )
            conversation = result.scalar_one_or_none()
            if not conversation:
                raise Exception("Conversation not found or access denied")

            logger.info(f"User {clerk_user_id} subscribed to conversation {conv_uuid}")

            # Get subscription manager instance
            subscription_manager = await SubscriptionManager.get_instance()

            # Subscribe to messages for this conversation
            async for message_payload in subscription_manager.subscribe_to_messages(
                conversation_id=conv_uuid, user_id=user.id
            ):
                yield message_payload

        except Exception as e:
            logger.error(f"Error in new_messages subscription: {str(e)}")
            raise


async def resolve_new_messages(
    info: Info, conversation_id: strawberry.ID
) -> AsyncGenerator[MessageSubscriptionPayload, None]:
    """
    Standalone resolver function for new messages subscription.

    Args:
        info: GraphQL resolver info containing the context
        conversation_id: The ID of the conversation to subscribe to

    Yields:
        MessageSubscriptionPayload: New message events
    """
    async for payload in ChatSubscriptionResolvers.resolve_new_messages(
        info, conversation_id
    ):
        yield payload
