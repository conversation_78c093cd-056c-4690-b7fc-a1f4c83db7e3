import logging
import secrets
from datetime import datetime
from typing import Any

from graphql import GraphQLError
from sqlalchemy.exc import NoResultFound
from strawberry.types import Info

from a2a_platform.api.graphql.schemas.user_profile_schemas import (
    CliToken,
    CreateCliTokenInput,
    CreateCliTokenPayload,
    UserProfilePreferencesInput,
)
from a2a_platform.db.models.cli_token import CliToken as CliTokenModel
from a2a_platform.schemas.user import User
from a2a_platform.services.user_service import (
    get_user_by_clerk_id,
    update_user_preferences,
    update_user_profile,
)

logger = logging.getLogger(__name__)

# Constants for CLI token
CLI_TOKEN_PREFIX = "a2a_cli_"
CLI_TOKEN_BYTE_LENGTH = 32  # Length of the random part of the token


async def resolve_me(info: Info) -> User:
    """
    Resolver for the me query

    Args:
        info: GraphQL resolver info containing the context

    Returns:
        The User object for the authenticated user

    Raises:
        Exception: If the user is not authenticated or not found
    """
    # Check if the user is authenticated
    context = info.context
    clerk_user_id = context.clerk_user_id
    if not clerk_user_id:
        logger.warning("Attempt to access me query without authentication")
        raise Exception("Authentication required for this operation")

    # Get the database session from the context
    if not hasattr(context, "db_session") or context.db_session is None:
        logger.error("No database session found in GraphQL context")
        raise Exception("Server configuration error: No database session available")

    try:
        # Get the user by clerk_user_id using the session from context
        from sqlalchemy.exc import OperationalError, IntegrityError

        try:
            db_user = await get_user_by_clerk_id(context.db_session, clerk_user_id)
        except (OperationalError, IntegrityError) as db_error:
            logger.error(f"Database error retrieving user {clerk_user_id}: {db_error}")
            raise Exception("Database temporarily unavailable. Please try again.")

        if not db_user:
            logger.error(f"User with clerk_user_id {clerk_user_id} not found")
            raise Exception("User not found")

        # Convert the SQLAlchemy model to a GraphQL type
        # Create a User object with all fields
        # The preferences field is handled by the User class's preferences method
        user = User(
            id=db_user.id,
            clerk_user_id=db_user.clerk_user_id,
            email=db_user.email,
            timezone=db_user.timezone,
            created_at=db_user.created_at.isoformat() if db_user.created_at else "",
            updated_at=db_user.updated_at.isoformat() if db_user.updated_at else "",
        )
        # Set the preferences attribute directly on the object
        # This will be accessed by the preferences() method
        setattr(user, "_preferences_data", db_user.preferences)
        return user
    except Exception as e:
        logger.error(f"Error retrieving user profile: {str(e)}")
        raise Exception(f"Failed to retrieve profile: {str(e)}")


async def resolve_update_my_profile(info: Info, timezone: str) -> User:
    """
    Resolver for the updateMyProfile mutation

    Args:
        info: GraphQL resolver info containing the context
        timezone: The new timezone value to set

    Returns:
        The updated User object

    Raises:
        Exception: If the user is not authenticated or not found
    """
    # Check if the user is authenticated
    context = info.context
    clerk_user_id = context.clerk_user_id
    if not clerk_user_id:
        logger.warning("Attempt to update profile without authentication")
        raise Exception("Authentication required for this operation")

    # Get the database session from the context
    if not hasattr(context, "db_session") or context.db_session is None:
        logger.error("No database session found in GraphQL context")
        raise Exception("Server configuration error: No database session available")

    try:
        # Update the user's profile using the session from context
        updated_user = await update_user_profile(
            context.db_session, clerk_user_id, timezone
        )
        if not updated_user:
            logger.error(f"User with clerk_user_id {clerk_user_id} not found")
            raise Exception("User not found")

        # Convert the SQLAlchemy model to a GraphQL type
        # Create a User object with all fields
        # The preferences field is handled by the User class's preferences method
        user = User(
            id=updated_user.id,
            clerk_user_id=updated_user.clerk_user_id,
            email=updated_user.email,
            timezone=updated_user.timezone,
            created_at=updated_user.created_at.isoformat()
            if updated_user.created_at
            else "",
            updated_at=updated_user.updated_at.isoformat()
            if updated_user.updated_at
            else "",
        )
        # Set the preferences attribute directly on the object
        # This will be accessed by the preferences() method
        setattr(user, "_preferences_data", updated_user.preferences)
        return user
    except NoResultFound:
        logger.error(f"User with clerk_user_id {clerk_user_id} not found")
        raise Exception("User not found")
    except Exception as e:
        logger.error(f"Error updating user profile: {str(e)}")
        raise Exception(f"Failed to update profile: {str(e)}")


async def resolve_update_user_profile_preferences(
    info: Info, input: UserProfilePreferencesInput
) -> User:
    """
    Resolver for the updateUserProfilePreferences mutation

    Args:
        info: GraphQL resolver info containing the context
        input: The preferences and timezone data to update

    Returns:
        The updated User object

    Raises:
        Exception: If the user is not authenticated or not found
    """
    # Check if the user is authenticated
    context = info.context
    clerk_user_id = context.clerk_user_id
    if not clerk_user_id:
        logger.warning("Attempt to update profile preferences without authentication")
        raise Exception("Authentication required for this operation")

    # Get the database session from the context
    if not hasattr(context, "db_session") or context.db_session is None:
        logger.error("No database session found in GraphQL context")
        raise Exception("Server configuration error: No database session available")

    try:
        # Extract preferences and timezone from input
        preferences_update: dict[str, Any] = (
            input.preferences if input.preferences is not None else {}
        )
        timezone_update = input.timezone

        # Debug logging
        logger.info(f"GraphQL resolver - clerk_user_id: {clerk_user_id}")
        logger.info(f"GraphQL resolver - input.preferences: {input.preferences}")
        logger.info(f"GraphQL resolver - preferences_update: {preferences_update}")
        logger.info(f"GraphQL resolver - timezone_update: {timezone_update}")

        # Update the user's preferences using the session from context
        updated_user = await update_user_preferences(
            context.db_session,
            clerk_user_id=clerk_user_id,
            preferences_update=preferences_update,
            timezone_update=timezone_update,
        )

        # Log the updated preferences for debugging
        logger.debug(f"Updated user preferences: {updated_user.preferences}")

        # Convert the SQLAlchemy model to a GraphQL type
        user = User(
            id=updated_user.id,
            clerk_user_id=updated_user.clerk_user_id,
            email=updated_user.email,
            timezone=updated_user.timezone,
            created_at=updated_user.created_at.isoformat()
            if updated_user.created_at
            else "",
            updated_at=updated_user.updated_at.isoformat()
            if updated_user.updated_at
            else "",
        )
        # Set the preferences attribute directly on the object
        # This will be accessed by the preferences() method
        setattr(user, "_preferences_data", updated_user.preferences)
        return user
    except NoResultFound:
        logger.error(f"User with clerk_user_id {clerk_user_id} not found")
        raise Exception("User not found")
    except ValueError as ve:
        logger.error(f"Invalid input for user preferences update: {str(ve)}")
        raise Exception(f"Invalid input: {str(ve)}")
    except Exception as e:
        logger.error(f"Error updating user preferences: {str(e)}")
        raise Exception(f"Failed to update preferences: {str(e)}")


async def resolve_create_cli_token(
    info: Info, input: CreateCliTokenInput
) -> CreateCliTokenPayload:
    """
    Resolver for the create_cli_token mutation.
    Generates a new CLI token for the authenticated user with the user ID prepended.
    """
    context = info.context
    clerk_user_id = context.clerk_user_id
    db_session = context.db_session

    # Add more detailed logging
    logger.info(f"resolve_create_cli_token called with clerk_user_id: {clerk_user_id}")
    logger.info(f"GraphQL context type: {type(context)}")
    logger.info(f"Database session type: {type(db_session)}")
    logger.info(f"Description from input: {input.description}")

    if not clerk_user_id:
        logger.warning("Attempt to create CLI token without authentication")
        raise GraphQLError(
            message="Authentication required for this operation",
            extensions={"code": "AUTHENTICATION_ERROR"},
        )

    if not db_session:
        logger.error(
            "No database session found in GraphQL context for CLI token creation"
        )
        raise GraphQLError(
            message="Server configuration error: No database session available",
            extensions={"code": "CONFIGURATION_ERROR"},
        )

    try:
        # Get the internal user ID
        logger.info(f"Attempting to get user with clerk_user_id: {clerk_user_id}")

        # Log the database session to check it's valid
        logger.info(f"Database session type: {type(db_session)}")

        try:
            # NOTE: The following block for logging all users was causing test failures
            # with mocked db_session and has been removed.
            # from sqlalchemy import select
            # from a2a_platform.db.models.user import User as UserModel
            # query = select(UserModel)
            # result = await db_session.execute(query)
            # all_users = result.scalars().all()
            # logger.info(f"Users in database: {len(all_users)}")
            # for user in all_users:
            #     logger.info(
            #         f"Database user: id={user.id}, clerk_user_id={user.clerk_user_id}, email={user.email}"
            #     )

            # Now try to find the specific user
            db_user = await get_user_by_clerk_id(db_session, clerk_user_id)
            logger.info(f"Result from get_user_by_clerk_id: {db_user}")
        except Exception as lookup_error:
            logger.error(
                f"Error during user lookup: {str(lookup_error)}", exc_info=True
            )
            raise GraphQLError(
                message=f"Database error during user lookup: {str(lookup_error)}",
                extensions={"code": "DATABASE_ERROR"},
            )

        if not db_user:
            logger.error(
                f"User with clerk_user_id {clerk_user_id} not found for CLI token creation"
            )
            raise GraphQLError(
                message="User not found", extensions={"code": "NOT_FOUND"}
            )

        # 1. Generate Token
        token_secret_part = secrets.token_urlsafe(CLI_TOKEN_BYTE_LENGTH)

        # 2. Create the full token by combining prefix, user ID, and secret part
        user_id_str = str(db_user.id).replace("-", "")[
            :8
        ]  # Take first 8 chars of UUID without hyphens
        full_token_to_return = f"{CLI_TOKEN_PREFIX}{user_id_str}_{token_secret_part}"

        # 3. Store the token directly without complex hashing
        # We use the token_secret_part as the hashed_token field
        # Extract the token secret from the full token for test consistency
        token_parts = full_token_to_return.split("_")
        token_secret = token_parts[-1]  # The last part is the secret

        new_cli_token_db = CliTokenModel(
            user_id=db_user.id,
            token_prefix=CLI_TOKEN_PREFIX,
            # Store the exact token secret part directly
            hashed_token=token_secret,
            # Use the user ID prefix as the "salt" for easier lookup
            salt_hex=user_id_str,
            description=input.description,
            # Use timezone-naive datetime to match database expectation
            created_at=datetime.now(),
            # expires_at=parsed_expires_at, # If applicable
            # last_used_at=None, # Initially null
        )

        db_session.add(new_cli_token_db)
        await db_session.commit()
        await db_session.refresh(new_cli_token_db)

        # 4. Prepare and Return Payload
        gql_cli_token = CliToken.from_db_model(new_cli_token_db)

        return CreateCliTokenPayload(token=full_token_to_return, cliToken=gql_cli_token)

    except NoResultFound:
        error_msg = (
            f"User with clerk_user_id {clerk_user_id} not found for CLI token creation"
        )
        logger.error(error_msg)
        raise GraphQLError(message="User not found", extensions={"code": "NOT_FOUND"})
    except ValueError as ve:
        error_msg = f"Invalid input while creating CLI token for user {clerk_user_id}"
        logger.error(f"{error_msg}: {str(ve)}")
        raise GraphQLError(
            message="Invalid input provided",
            extensions={
                "code": "BAD_REQUEST",
                "exception": {"type": "ValueError", "message": str(ve)},
            },
        )
    except Exception as e:
        error_msg = f"Unexpected error creating CLI token for user {clerk_user_id}"
        logger.error(
            f"{error_msg}: {str(e)}",
            exc_info=True,
        )
        detailed_msg = (
            f"Failed to create CLI token. Detail: {type(e).__name__} - {str(e)}"
        )
        # Use GraphQLError for proper GraphQL error response
        raise GraphQLError(
            message=detailed_msg,
            extensions={
                "code": "DATABASE_ERROR",
                "exception": {"type": type(e).__name__, "message": str(e)},
            },
        )
