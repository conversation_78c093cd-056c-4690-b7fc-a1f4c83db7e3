"""
GraphQL resolvers for Assistant operations.
"""

import logging
from typing import Op<PERSON>

from sqlalchemy.ext.asyncio import AsyncSession
from strawberry.types import Info

from a2a_platform.api.graphql.middleware.auth_middleware import (
    get_current_user_id,
)
from a2a_platform.api.graphql.schemas.assistant_schemas import (
    Assistant,
    CreateAssistantInput,
    CreateAssistantPayload,
    UpdateAssistantInput,
    UpdateAssistantPayload,
)
from a2a_platform.schemas.assistant_schemas import (
    AssistantCreate,
    AssistantRead,
    AssistantUpdate,
)
from a2a_platform.services.assistant_service import AssistantService
from a2a_platform.services.user_service import get_user_by_clerk_id

logger = logging.getLogger(__name__)


def get_db_session_from_context(info: Info) -> AsyncSession:
    """
    Get the database session from the GraphQL context.

    Args:
        info: GraphQL resolver info containing the context

    Returns:
        The database session from the context

    Raises:
        Exception: If no database session is found in the context
    """
    try:
        db_session = getattr(info.context, "db_session", None)
        if db_session is None:
            logger.error("Database session not found in GraphQL context")
            raise Exception("Database session not available. Please try again.")
        # Type assertion to help MyPy understand the return type
        return db_session  # type: ignore[no-any-return]
    except AttributeError as e:
        logger.error(f"Context attribute error: {e}")
        raise Exception("Invalid request context")


async def resolve_my_assistant(info: Info) -> Optional[Assistant]:
    """
    Get the authenticated user's assistant.

    Returns:
        The user's assistant if it exists, None otherwise.

    Raises:
        Exception: If the user is not authenticated.
    """
    clerk_user_id = get_current_user_id(info.context)
    db_session = get_db_session_from_context(info)

    # First, get the user by Clerk ID to get the internal user UUID
    user = await get_user_by_clerk_id(db_session, clerk_user_id)
    if not user:
        logger.warning(f"User not found for clerk_user_id: {clerk_user_id}")
        raise Exception(
            "User account not found. Please try refreshing the page or "
            "contact support if the issue persists."
        )

    assistant_service = AssistantService(db_session)
    assistant_model = await assistant_service.get_user_assistant(user.id)

    if not assistant_model:
        return None

    # Convert to Pydantic schema first, then to GraphQL type
    assistant_schema = AssistantRead.model_validate(assistant_model)
    return Assistant.from_pydantic(assistant_schema)


async def resolve_create_assistant(
    info: Info, input: CreateAssistantInput
) -> CreateAssistantPayload:
    """
    Create a new personal assistant for the authenticated user.

    Args:
        info: GraphQL resolver info containing the context
        input: The assistant data to create

    Returns:
        The created assistant payload

    Raises:
        Exception: If the user is not authenticated or creation fails
    """
    clerk_user_id = get_current_user_id(info.context)
    db_session = get_db_session_from_context(info)

    try:
        # First, get the user by Clerk ID to get the internal user UUID
        user = await get_user_by_clerk_id(db_session, clerk_user_id)
        if not user:
            raise Exception(f"User not found for clerk_user_id: {clerk_user_id}")

        assistant_service = AssistantService(db_session)

        # Convert GraphQL input to Pydantic schema
        assistant_create = AssistantCreate(
            name=input.name,
            backstory=input.backstory,
            avatar_file_id=input.avatar_file_id,
            configuration=input.configuration or {},
            status=input.status.value if input.status else None,
        )

        # Store user ID before potential session closure
        user_id = user.id

        # Create the assistant using the internal user UUID
        assistant_model = await assistant_service.create_personal_assistant(
            user_id, assistant_create
        )

        # Convert to Pydantic schema first, then to GraphQL type
        assistant_schema = AssistantRead.model_validate(assistant_model)
        assistant_gql = Assistant.from_pydantic(assistant_schema)

        logger.info(f"Created assistant '{input.name}' for user {user_id}")

        return CreateAssistantPayload(
            assistant=assistant_gql,
            success=True,
            message="Assistant created successfully",
        )

    except ValueError as e:
        logger.error(f"Failed to create assistant for user {clerk_user_id}: {str(e)}")
        raise Exception(f"Failed to create assistant: {str(e)}")
    except PermissionError as e:
        logger.error(
            f"Permission error creating assistant for user {clerk_user_id}: {str(e)}"
        )
        raise Exception(f"Permission denied: {str(e)}")


async def resolve_update_assistant(
    info: Info, input: UpdateAssistantInput
) -> UpdateAssistantPayload:
    """
    Update the authenticated user's assistant.

    Args:
        info: GraphQL resolver info containing the context
        input: The assistant data to update

    Returns:
        The updated assistant payload

    Raises:
        Exception: If the user is not authenticated, doesn't have an assistant,
                  or update fails
    """
    clerk_user_id = get_current_user_id(info.context)
    db_session = get_db_session_from_context(info)

    try:
        # First, get the user by Clerk ID to get the internal user UUID
        user = await get_user_by_clerk_id(db_session, clerk_user_id)
        if not user:
            raise Exception(f"User not found for clerk_user_id: {clerk_user_id}")

        assistant_service = AssistantService(db_session)

        # First get the user's assistant to get the assistant_id
        existing_assistant = await assistant_service.get_user_assistant(user.id)
        if not existing_assistant:
            raise Exception("You don't have an assistant to update")

        # Convert GraphQL input to Pydantic schema
        assistant_update = AssistantUpdate(
            name=input.name,
            backstory=input.backstory,
            avatar_file_id=input.avatar_file_id,
            configuration=input.configuration,
            status=input.status.value if input.status else None,
        )

        # Update the assistant using the internal user UUID
        assistant_model = await assistant_service.update_assistant(
            existing_assistant.id, user.id, assistant_update
        )

        # Convert to Pydantic schema first, then to GraphQL type
        assistant_schema = AssistantRead.model_validate(assistant_model)
        assistant_gql = Assistant.from_pydantic(assistant_schema)

        logger.info(f"Updated assistant for user {user.id}")

        return UpdateAssistantPayload(
            assistant=assistant_gql,
            success=True,
            message="Assistant updated successfully",
        )

    except ValueError as e:
        logger.error(f"Failed to update assistant for user {clerk_user_id}: {str(e)}")
        raise Exception(f"Failed to update assistant: {str(e)}")
    except PermissionError as e:
        logger.error(
            f"Permission error updating assistant for user {clerk_user_id}: {str(e)}"
        )
        raise Exception(f"Permission denied: {str(e)}")


async def resolve_user_has_assistant(info: Info) -> bool:
    """
    Check if the authenticated user has an assistant.

    Returns:
        True if the user has an assistant, False otherwise.

    Raises:
        Exception: If the user is not authenticated.
    """
    clerk_user_id = get_current_user_id(info.context)
    db_session = get_db_session_from_context(info)

    # First, get the user by Clerk ID to get the internal user UUID
    user = await get_user_by_clerk_id(db_session, clerk_user_id)
    if not user:
        raise Exception(f"User not found for clerk_user_id: {clerk_user_id}")

    assistant_service = AssistantService(db_session)
    return await assistant_service.user_has_assistant(user.id)
