from typing import AsyncGenerator, Optional

import strawberry
from strawberry.fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from strawberry.types import Info

from a2a_platform.db.enums import Agent<PERSON><PERSON>iewStatus, AgentStatus

from a2a_platform.api.graphql.middleware.auth_middleware import (
    AuthMiddleware,
    get_graphql_context,
)
from a2a_platform.api.graphql.resolvers.agent_marketplace_resolvers import (
    resolve_get_marketplace_agent,
    resolve_list_marketplace_agents,
    resolve_register_marketplace_agent,
    resolve_update_agent_review_status,
    resolve_update_marketplace_agent,
)
from a2a_platform.api.graphql.resolvers.assistant_resolvers import (
    resolve_create_assistant,
    resolve_my_assistant,
    resolve_update_assistant,
    resolve_user_has_assistant,
)
from a2a_platform.api.graphql.resolvers.chat_resolvers import (
    resolve_get_conversation_messages,
    resolve_get_or_create_conversation,
    resolve_send_message,
    resolve_send_message_from_pa,
)
from a2a_platform.api.graphql.resolvers.chat_subscriptions import (
    resolve_new_messages,
)
from a2a_platform.api.graphql.resolvers.user_profile_resolvers import (
    resolve_create_cli_token,
    resolve_me,
    resolve_update_my_profile,
    resolve_update_user_profile_preferences,
)
from a2a_platform.api.graphql.schemas.agent_marketplace_schemas import (
    MarketplaceAgent,
    RegisterMarketplaceAgentInput,
    UpdateMarketplaceAgentInput,
)
from a2a_platform.api.graphql.schemas.assistant_schemas import (
    Assistant,
    CreateAssistantInput,
    CreateAssistantPayload,
    UpdateAssistantInput,
    UpdateAssistantPayload,
)
from a2a_platform.api.graphql.schemas.chat_schemas import (
    ChatMessageConnection,
    ChatMessageConnectionArgs,
    Conversation,
    MessageSubscriptionPayload,
    SendMessageFromPAInput,
    SendMessagePayload,
)
from a2a_platform.api.graphql.schemas.user_profile_schemas import (
    CreateCliTokenInput,
    CreateCliTokenPayload,
    UserProfilePreferencesInput,
)
from a2a_platform.schemas.user import User
from a2a_platform.config.settings import get_settings

# Import your resolvers here
# from .resolvers.user import UserQuery, UserMutation
# from .resolvers.assistant import AssistantQuery, AssistantMutation
# from .resolvers.chat import ChatQuery, ChatMutation, ChatSubscription


@strawberry.type
class Query:
    @strawberry.field
    async def hello(self) -> str:
        return "Hello World from VEDAVIVI!"

    @strawberry.field
    async def me(self, info: Info) -> User:
        """
        Get the authenticated user's profile

        Returns:
            The User object for the authenticated user

        Raises:
            Exception: If the user is not authenticated or not found
        """
        return await resolve_me(info)

    @strawberry.field
    async def listMarketplaceAgents(
        self, info: Info, status: Optional[AgentStatus] = None
    ) -> list[MarketplaceAgent]:
        """
        List all agents in the marketplace.

        Args:
            info: GraphQL resolver info containing the context
            status: Optional filter for agent status ('active' or 'inactive')

        Returns:
            List of marketplace agents
        """
        return await resolve_list_marketplace_agents(info, status)

    @strawberry.field
    async def getMarketplaceAgent(
        self, info: Info, agent_definition_id: str
    ) -> Optional[MarketplaceAgent]:
        """
        Get a specific agent from the marketplace by ID.

        Args:
            info: GraphQL resolver info containing the context
            agent_definition_id: The ID of the agent to retrieve

        Returns:
            The marketplace agent if found, None otherwise
        """
        return await resolve_get_marketplace_agent(info, agent_definition_id)

    @strawberry.field
    async def myAssistant(self, info: Info) -> Optional[Assistant]:
        """
        Get the authenticated user's personal assistant.

        Returns:
            The user's assistant if it exists, None otherwise
        """
        return await resolve_my_assistant(info)

    @strawberry.field
    async def userHasAssistant(self, info: Info) -> bool:
        """
        Check if the authenticated user has a personal assistant.

        Returns:
            True if the user has an assistant, False otherwise
        """
        return await resolve_user_has_assistant(info)

    @strawberry.field
    async def getOrCreateConversation(self, info: Info) -> Conversation:
        """
        Get or create a conversation between the authenticated user and their assistant.

        Returns:
            The conversation between the user and their assistant
        """
        return await resolve_get_or_create_conversation(info)

    @strawberry.field
    async def getConversationMessages(
        self,
        info: Info,
        conversation_id: strawberry.ID,
        pagination: Optional[ChatMessageConnectionArgs] = None,
    ) -> ChatMessageConnection:
        """
        Get messages for a specific conversation using cursor-based pagination.

        Args:
            info: GraphQL resolver info containing the context
            conversation_id: The ID of the conversation
            pagination: Cursor pagination arguments (first, after, last, before)

        Returns:
            ChatMessageConnection with edges, page info, and total count
        """
        return await resolve_get_conversation_messages(
            info, conversation_id, pagination
        )


@strawberry.type
class Mutation:
    @strawberry.field
    async def echo(self, message: str) -> str:
        return message

    @strawberry.field
    async def health(self) -> str:
        """Simple health check for GraphQL debugging."""
        return "GraphQL is working!"

    @strawberry.field
    async def updateMyProfile(self, info: Info, timezone: str) -> User:
        """
        Update the authenticated user's profile with a new timezone

        Args:
            info: GraphQL resolver info containing the context
            timezone: The new timezone value to set

        Returns:
            The updated User object
        """
        return await resolve_update_my_profile(info, timezone)

    @strawberry.field
    async def updateUserProfilePreferences(
        self, info: Info, input: UserProfilePreferencesInput
    ) -> User:
        """
        Update the authenticated user's profile preferences and optionally timezone

        This mutation allows updating the user's preferences JSON object and/or
        timezone. The preferences are deep-merged with existing preferences.

        Args:
            info: GraphQL resolver info containing the context
            input: The preferences and timezone data to update

        Returns:
            The updated User object
        """
        return await resolve_update_user_profile_preferences(info, input)

    @strawberry.mutation
    async def createCliToken(
        self, info: Info, input: CreateCliTokenInput
    ) -> CreateCliTokenPayload:
        """
        Creates a new CLI access token for the authenticated user.
        The full token is returned only once upon creation.
        """
        return await resolve_create_cli_token(info, input)

    @strawberry.field
    async def registerMarketplaceAgent(
        self, info: Info, input: RegisterMarketplaceAgentInput
    ) -> MarketplaceAgent:
        """
        Register a new agent in the marketplace.

        Args:
            info: GraphQL resolver info containing the context
            input: The agent data to register

        Returns:
            The newly registered marketplace agent
        """
        return await resolve_register_marketplace_agent(info, input)

    @strawberry.field
    async def updateMarketplaceAgent(
        self, info: Info, input: UpdateMarketplaceAgentInput
    ) -> MarketplaceAgent:
        """
        Update an existing agent in the marketplace.

        Args:
            info: GraphQL resolver info containing the context
            input: The agent data to update

        Returns:
            The updated marketplace agent
        """
        return await resolve_update_marketplace_agent(info, input)

    @strawberry.field
    async def updateAgentReviewStatus(
        self, info: Info, agent_definition_id: str, review_status: AgentReviewStatus
    ) -> MarketplaceAgent:
        """
        Update the review status of an agent in the marketplace.

        Args:
            info: GraphQL resolver info containing the context
            agent_definition_id: The ID of the agent to update
            review_status: The new review status ('pending', 'approved', 'rejected')

        Returns:
            The updated marketplace agent
        """
        return await resolve_update_agent_review_status(
            info, agent_definition_id, review_status
        )

    @strawberry.field
    async def createPersonalAssistant(
        self, info: Info, input: CreateAssistantInput
    ) -> CreateAssistantPayload:
        """
        Create a new personal assistant for the authenticated user.

        Args:
            info: GraphQL resolver info containing the context
            input: The assistant data to create

        Returns:
            The created assistant payload
        """
        return await resolve_create_assistant(info, input)

    @strawberry.field
    async def updatePersonalAssistant(
        self, info: Info, input: UpdateAssistantInput
    ) -> UpdateAssistantPayload:
        """
        Update the authenticated user's personal assistant.

        Args:
            info: GraphQL resolver info containing the context
            input: The assistant data to update

        Returns:
            The updated assistant payload
        """
        return await resolve_update_assistant(info, input)

    @strawberry.field
    async def sendMessage(
        self, info: Info, conversation_id: strawberry.ID, content: str
    ) -> SendMessagePayload:
        """
        Send a text message to a conversation.

        Args:
            info: GraphQL resolver info containing the context
            conversation_id: The ID of the conversation
            content: The message content (max 4000 characters)

        Returns:
            The send message payload with the created message
        """
        return await resolve_send_message(info, conversation_id, content)

    @strawberry.field
    async def sendMessageFromPA(
        self, info: Info, input: SendMessageFromPAInput
    ) -> SendMessagePayload:
        """
        Send a message from the Personal Assistant to a conversation.

        Args:
            info: GraphQL resolver info containing the context
            input: The input data containing conversation_id, content, and metadata

        Returns:
            The send message payload with the created PA message
        """
        return await resolve_send_message_from_pa(info, input)


@strawberry.type
class Subscription:
    @strawberry.subscription
    async def newMessages(
        self, info: Info, conversation_id: strawberry.ID
    ) -> AsyncGenerator[MessageSubscriptionPayload, None]:
        """
        Subscribe to new messages in a conversation.

        Args:
            info: GraphQL resolver info containing the context
            conversation_id: The ID of the conversation to subscribe to

        Yields:
            MessageSubscriptionPayload: New message events
        """
        async for payload in resolve_new_messages(info, conversation_id):
            yield payload


schema = strawberry.Schema(
    query=Query,
    mutation=Mutation,
    subscription=Subscription,
    extensions=[
        AuthMiddleware,  # Add AuthMiddleware here
    ],
)

# Use type ignore for the GraphQLRouter context_getter parameter
# This is necessary because the strawberry-graphql library expects a different return type
# than what our get_graphql_context function provides

# Only enable debug mode in development/staging environments for security
settings = get_settings()
debug_enabled = settings.ENVIRONMENT.lower() in ("development", "staging")

graphql_app = GraphQLRouter(
    schema,
    context_getter=get_graphql_context,  # type: ignore[arg-type]
    graphql_ide="graphiql",  # Use "graphiql" instead of True
    debug=debug_enabled,  # Only enable debug mode in non-production environments
)
