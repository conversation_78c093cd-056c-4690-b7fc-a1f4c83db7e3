from fastapi import APIRouter

from .routes.health import router as health_router
from .routes.internal_agent_routes import router as internal_agent_router
from .routes.internal_task_routes import router as internal_task_router
from .routes.specialized_agent_api import router as specialized_agent_router

# Import your route modules here
from .routes.webhooks import router as webhook_router

api_router = APIRouter()

# Include your route modules here
# Health endpoint should be first for easy access
api_router.include_router(health_router)
api_router.include_router(webhook_router)
api_router.include_router(internal_agent_router)
api_router.include_router(internal_task_router)
api_router.include_router(specialized_agent_router)
