"""
Health check endpoint for the API service.

This module provides a health check endpoint that can be used by Cloud Run
startup probes and load balancers to verify the service is running correctly.
"""

import logging

from fastapi import APIRouter
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter(tags=["health"])


class HealthCheckResponse(BaseModel):
    """Response model for health check endpoint."""

    status: str = "OK"
    service: str = "API"


@router.get("/health", response_model=HealthCheckResponse)
async def health_check() -> HealthCheckResponse:
    """
    Health check endpoint for the API service.

    This endpoint is used by Cloud Run startup probes and load balancers
    to verify that the service is running and ready to accept requests.

    Returns:
        HealthCheckResponse: Status information about the service
    """
    logger.debug("Health check endpoint called")
    return HealthCheckResponse(status="OK", service="API")


@router.get("/health/ready", response_model=HealthCheckResponse)
async def readiness_check() -> HealthCheckResponse:
    """
    Readiness check endpoint for the API service.

    This endpoint can be used to check if the service is ready to handle requests.
    In the future, this could include checks for database connectivity, etc.

    Returns:
        HealthCheckResponse: Readiness status of the service
    """
    logger.debug("Readiness check endpoint called")
    # TODO: Add database connectivity check, Redis check, etc.
    return HealthCheckResponse(status="OK", service="API")


@router.get("/health/live", response_model=HealthCheckResponse)
async def liveness_check() -> HealthCheckResponse:
    """
    Liveness check endpoint for the API service.

    This endpoint can be used to check if the service is alive and functioning.

    Returns:
        HealthCheckResponse: Liveness status of the service
    """
    logger.debug("Liveness check endpoint called")
    return HealthCheckResponse(status="OK", service="API")
