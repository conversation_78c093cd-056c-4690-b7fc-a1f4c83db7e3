import logging
from typing import Optional

from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON>pen<PERSON>, <PERSON><PERSON>, HTTPEx<PERSON>, Path, Query
from fastapi.encoders import jsonable_encoder
from fastapi.responses import J<PERSON><PERSON>esponse
from pydantic import UUID4

from a2a_platform.schemas.task_schemas import (
    TaskCreate,
    TaskListResponse,
    TaskRead,
    TaskUpdate,
)
from a2a_platform.services.task_service import TaskService

from ..dependencies import get_current_assistant_id, get_task_service

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/internal",
    tags=["Internal - Tasks"],
    responses={404: {"description": "Not found"}},
)


@router.post(
    "/assistants/my/tasks",
    response_model=TaskRead,
    status_code=201,
    summary="Create a new internal task",
    description="Creates a new internal task for the Personal Assistant. Supports idempotent creation.",
)
async def create_task(
    task_data: TaskCreate,
    assistant_id: UUID4 = Depends(get_current_assistant_id),
    task_service: TaskService = Depends(get_task_service),
    x_idempotency_key: Optional[str] = Header(None),
) -> TaskRead | JSONResponse:
    """
    Creates a new internal task for the Personal Assistant.

    If X-Idempotency-Key header is provided and matches an existing active task for the assistant,
    returns the existing task instead of creating a new one.

    - **description**: Task description (required)
    - **objective_id**: Optional link to an assistant objective
    - **parent_task_id**: Optional link to a parent task (for sub-tasks)
    - **depends_on_task_id**: Optional dependency on another task
    - **initial_status**: Initial status of the task (default: 'todo')
    - **metadata**: Additional task metadata (e.g., expected_duration_seconds, max_retries)
    """
    try:
        # First check if there's an existing task with the same idempotency key
        existing_task = None
        if x_idempotency_key:
            terminal_statuses = [
                "done",
                "cancelled",
                "failed_max_retries",
                "quarantined",
            ]
            existing_task = await task_service.get_task_by_idempotency_key(
                assistant_id, x_idempotency_key, exclude_statuses=terminal_statuses
            )

        # If we found an existing task, return it with 200 status code
        if existing_task:
            # Create dict with proper field mapping (metadata_json -> metadata)
            task_dict = {}
            for key, value in existing_task.__dict__.items():
                if not key.startswith("_"):
                    if key == "metadata_json":
                        task_dict["metadata"] = value
                    else:
                        task_dict[key] = value
            # Use jsonable_encoder to ensure UUIDs are serialized
            return JSONResponse(
                status_code=200,
                content=jsonable_encoder(TaskRead.model_validate(task_dict)),
            )

        # Otherwise, create a new task
        try:
            task = await task_service.create_task(
                assistant_id=assistant_id,
                task_data=task_data,
                idempotency_key=x_idempotency_key,
            )
        except Exception as e:
            import traceback

            logger.error("Exception in create_task: %s", str(e))
            logger.error(traceback.format_exc())
            raise HTTPException(
                status_code=400,
                detail=f"Failed to create task: {str(e)}",
            )

        # Create dict with proper field mapping (metadata_json -> metadata)
        task_dict = {}
        for key, value in task.__dict__.items():
            if not key.startswith("_"):
                if key == "metadata_json":
                    task_dict["metadata"] = value
                else:
                    task_dict[key] = value

        return TaskRead.model_validate(task_dict)
    except Exception as e:
        import traceback

        print("Exception in API route:", str(e))
        print(traceback.format_exc())
        raise HTTPException(
            status_code=400,
            detail=f"Failed to create task: {str(e)}",
        )


@router.put(
    "/tasks/{task_id}",
    response_model=TaskRead,
    summary="Update a task",
    description="Updates an existing task with new data.",
)
async def update_task(
    task_id: UUID4 = Path(..., description="ID of the task to update"),
    update_data: TaskUpdate = TaskUpdate(),
    task_service: TaskService = Depends(get_task_service),
) -> TaskRead:
    """
    Updates an existing task with the provided data.

    - **description**: Updated task description
    - **status**: New task status
    - **last_progress_at**: Update progress timestamp for heartbeating
    - **metadata**: Updated task metadata
    """
    task = await task_service.update_task(task_id=task_id, update_data=update_data)

    if task is None:
        raise HTTPException(
            status_code=404,
            detail=f"Task with ID {task_id} not found",
        )

    # Create dict with proper field mapping (metadata_json -> metadata)
    task_dict = {}
    for key, value in task.__dict__.items():
        if not key.startswith("_"):
            if key == "metadata_json":
                task_dict["metadata"] = value
            else:
                task_dict[key] = value

    return TaskRead.model_validate(task_dict)


@router.delete(
    "/tasks/{task_id}",
    response_model=TaskRead,
    summary="Cancel a task",
    description="Sets a task's status to 'cancelled'.",
)
async def cancel_task(
    task_id: UUID4 = Path(..., description="ID of the task to cancel"),
    task_service: TaskService = Depends(get_task_service),
) -> TaskRead:
    """
    Cancels a task by setting its status to 'cancelled'.
    """
    task = await task_service.cancel_task(task_id=task_id)

    if task is None:
        raise HTTPException(
            status_code=404,
            detail=f"Task with ID {task_id} not found",
        )

    # Create dict with proper field mapping (metadata_json -> metadata)
    task_dict = {}
    for key, value in task.__dict__.items():
        if not key.startswith("_"):
            if key == "metadata_json":
                task_dict["metadata"] = value
            else:
                task_dict[key] = value

    return TaskRead.model_validate(task_dict)


@router.get(
    "/assistants/my/tasks",
    response_model=TaskListResponse,
    summary="List assistant tasks",
    description="Lists tasks for the current assistant with optional filtering.",
)
async def list_tasks(
    assistant_id: UUID4 = Depends(get_current_assistant_id),
    status: Optional[str] = Query(None, description="Filter by task status"),
    objective_id: Optional[UUID4] = Query(None, description="Filter by objective ID"),
    parent_task_id: Optional[UUID4] = Query(
        None, description="Filter by parent task ID"
    ),
    skip: int = Query(0, ge=0, description="Number of records to skip for pagination"),
    limit: int = Query(
        100, ge=1, le=200, description="Maximum number of records to return"
    ),
    task_service: TaskService = Depends(get_task_service),
) -> TaskListResponse:
    """
    Lists tasks for the current assistant with optional filtering.

    - **status**: Filter by task status
    - **objective_id**: Filter by associated objective
    """
    tasks, total_count = await task_service.get_tasks_for_assistant(
        assistant_id=assistant_id,
        status=status,
        objective_id=objective_id,
        parent_task_id=parent_task_id,
        skip=skip,
        limit=limit,
    )

    # Convert all Task models to TaskRead schemas to handle the metadata_json mapping properly
    task_schemas = []
    for task in tasks:
        # Create dict with proper field mapping (metadata_json -> metadata)
        task_dict = {}
        for key, value in task.__dict__.items():
            if not key.startswith("_"):
                if key == "metadata_json":
                    task_dict["metadata"] = value
                else:
                    task_dict[key] = value
        task_schemas.append(TaskRead.model_validate(task_dict))

    return TaskListResponse(items=task_schemas, total=total_count)


@router.get(
    "/tasks/{task_id}",
    response_model=TaskRead,
    summary="Get task by ID",
    description="Retrieves a specific task by its ID.",
)
async def get_task(
    task_id: UUID4 = Path(..., description="ID of the task to retrieve"),
    task_service: TaskService = Depends(get_task_service),
) -> TaskRead:
    """
    Retrieves a specific task by its ID.
    """
    task = await task_service.get_task_by_id(task_id=task_id)

    if task is None:
        raise HTTPException(
            status_code=404,
            detail=f"Task with ID {task_id} not found",
        )

    # Create dict with proper field mapping (metadata_json -> metadata)
    task_dict = {}
    for key, value in task.__dict__.items():
        if not key.startswith("_"):
            if key == "metadata_json":
                task_dict["metadata"] = value
            else:
                task_dict[key] = value

    return TaskRead.model_validate(task_dict)
