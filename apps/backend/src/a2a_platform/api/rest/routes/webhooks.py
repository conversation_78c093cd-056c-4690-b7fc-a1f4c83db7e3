import asyncio
import json
import logging
import time
from typing import Any, Async<PERSON>enerator, Dict

import redis.asyncio as redis
from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.auth.clerk import verify_clerk_webhook
from a2a_platform.config.settings import get_settings
from a2a_platform.db import get_session_factory
from a2a_platform.schemas.clerk_events import UserCreatedData, UserDeletedData

# Import for metrics tracking
from a2a_platform.utils.metrics import (
    webhook_counter,
    webhook_processing_time,
    webhook_validation_error_counter,
)
from a2a_platform.workers.clerk_event_handlers import (
    IDEMPOTENCY_KEY_PREFIX,
    process_user_created_task,
    process_user_deleted_task,
)

# Set up logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

settings = get_settings()

# Initialize Redis client for idempotency checks
try:
    redis_client = redis.from_url(settings.REDIS_URL)  # type: ignore
except Exception as e:
    logger.error(f"Failed to initialize Redis client: {str(e)}", exc_info=True)
    redis_client = None

router = APIRouter(prefix="/v1/webhooks", tags=["webhooks"])


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Dependency for database session"""
    session_factory = get_session_factory()
    async with session_factory() as session:
        yield session


# Define a registry for event handlers
# This maps event_type strings to their handler configurations
EVENT_HANDLER_CONFIG: Dict[str, Dict[str, Any]] = {
    "user.created": {
        "pydantic_model": UserCreatedData,
        "task_function": process_user_created_task,
        "success_log_message": "User created event ({clerk_event_id}) task created for Clerk user ID: {data_id}",
        "response_message": "user.created event {clerk_event_id} accepted for processing",
    },
    "user.deleted": {
        "pydantic_model": UserDeletedData,
        "task_function": process_user_deleted_task,
        "success_log_message": "User deleted event ({clerk_event_id}) task created for Clerk user ID: {data_id}",
        "response_message": "user.deleted event {clerk_event_id} accepted for processing",
    },
    # New event types can be added here with their configurations
}


async def _create_task_for_event_type(
    event_type: str, clerk_event_id: str, event_data: Dict[str, Any]
) -> None:
    """
    Generic task creation function that dispatches to the correct handler based on event type.

    This helper function centralizes the task creation logic while maintaining test compatibility
    by using direct function calls that can be properly mocked in tests.

    Args:
        event_type: The type of event (e.g., "user.created", "user.deleted")
        clerk_event_id: The unique ID of the Clerk event
        event_data: The raw event data dictionary to be validated by the worker
    """
    # Use direct function calls for test compatibility
    # This allows the test mocks to be called correctly
    if event_type == "user.created":
        # Use the direct import to ensure test mocks work
        # In a production environment, this would be replaced with a task queue client
        # e.g., task_queue.send_task("process_user_created_task", args=[None, str(clerk_event_id), event_data])
        asyncio.create_task(
            process_user_created_task(None, str(clerk_event_id), event_data)
        )
    elif event_type == "user.deleted":
        # Use the direct import to ensure test mocks work
        # In a production environment, this would be replaced with a task queue client
        # e.g., task_queue.send_task("process_user_deleted_task", args=[None, str(clerk_event_id), event_data])
        asyncio.create_task(
            process_user_deleted_task(None, str(clerk_event_id), event_data)
        )
    # New event types can be added here with their task function calls


@router.post("/clerk")
async def handle_clerk_webhook(
    verified_payload: Dict[str, Any] = Depends(verify_clerk_webhook),
) -> Dict[str, Any]:
    """
    Handle Clerk webhook events

    This endpoint receives webhook events from Clerk and processes them accordingly.
    It supports the 'user.created' and 'user.deleted' events, with an extensible design
    for adding more event types.

    Args:
        verified_payload: The verified webhook payload from the verify_clerk_webhook dependency

    Returns:
        Dict[str, Any]: JSON response confirming receipt of the webhook
    """
    # Extract event type and clerk_event_id
    event_type = verified_payload.get("type", "unknown")
    clerk_event_id = verified_payload.get("clerk_event_id", "unknown")

    # Track webhook receipt in metrics
    webhook_counter(str(event_type), "received")

    # Start timing the webhook processing
    with webhook_processing_time(str(event_type)):
        # Enhanced structured logging for event receipt
        logger.info(
            "Received Clerk webhook",
            extra={
                "payload_type": event_type,
                "clerk_event_id": clerk_event_id,
                "processing_stage": "received",
                "event_timestamp": time.time(),
            },
        )

        try:
            # OPTIMIZATION 2: Early idempotency check
            # Check if this event has already been processed before doing any further work
            if redis_client is not None:
                idempotency_key = f"{IDEMPOTENCY_KEY_PREFIX}{clerk_event_id}"
                try:
                    is_already_processed = await redis_client.exists(idempotency_key)
                    if is_already_processed:
                        logger.info(
                            f"Event {clerk_event_id} already processed, acknowledging",
                            extra={
                                "payload_type": event_type,
                                "clerk_event_id": clerk_event_id,
                                "processing_stage": "idempotency_check",
                                "event_timestamp": time.time(),
                            },
                        )
                        return {
                            "success": True,
                            "message": f"Event {clerk_event_id} already processed",
                        }
                except Exception as redis_err:
                    # Log the error but continue processing
                    # This ensures we don't reject valid webhooks due to Redis issues
                    logger.warning(
                        f"Redis idempotency check failed: {str(redis_err)}",
                        extra={
                            "payload_type": event_type,
                            "clerk_event_id": clerk_event_id,
                            "error_details": str(redis_err),
                        },
                    )

            # Look up the handler configuration for this event type
            event_config = (
                EVENT_HANDLER_CONFIG.get(str(event_type)) if event_type else None
            )

            if event_config:
                # Get the event data payload
                event_data_payload = verified_payload.get("data", {})

                # OPTIMIZATION 3: Minimal validation at the endpoint
                # Just check if the required 'id' field exists in the payload
                # Full Pydantic validation will be done by the worker
                data_id = event_data_payload.get("id")
                if not data_id:
                    # Track validation error in metrics
                    webhook_validation_error_counter(str(event_type))

                    error_message = (
                        f"Missing required 'id' field in {event_type} event data"
                    )
                    logger.error(
                        error_message,
                        extra={
                            "payload_type": event_type,
                            "clerk_event_id": clerk_event_id,
                            "processing_stage": "basic_validation",
                            "event_timestamp": time.time(),
                        },
                    )

                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST, detail=error_message
                    )

                # Log task creation with structured data
                logger.info(
                    f"Creating task for {event_type}",
                    extra={
                        "payload_type": event_type,
                        "clerk_event_id": clerk_event_id,
                        "processing_stage": "task_creation",
                        "clerk_user_id": data_id,
                        "event_timestamp": time.time(),
                    },
                )

                # OPTIMIZATION 1: Task queue integration
                # Create a task using the helper function
                # In a production environment, this would be replaced with a task queue client
                await _create_task_for_event_type(
                    event_type=str(event_type),
                    clerk_event_id=str(clerk_event_id),
                    event_data=event_data_payload,
                )

                # Track successful task creation in metrics
                webhook_counter(str(event_type), "task_created")

                # Log success with structured data
                logger.info(
                    event_config["success_log_message"].format(
                        clerk_event_id=clerk_event_id, data_id=data_id
                    ),
                    extra={
                        "payload_type": event_type,
                        "clerk_event_id": clerk_event_id,
                        "processing_stage": "task_created",
                        "clerk_user_id": data_id,
                        "event_timestamp": time.time(),
                    },
                )

                # Use the specific response message from the config
                response_message = event_config["response_message"].format(
                    clerk_event_id=clerk_event_id
                )

                return {"success": True, "message": response_message}

            else:
                # Track unhandled event type in metrics
                webhook_counter(event_type, "unhandled")

                # Handle unhandled event types with structured logging
                logger.info(
                    f"Received unhandled Clerk event type: {event_type}",
                    extra={
                        "payload_type": event_type,
                        "clerk_event_id": clerk_event_id,
                        "processing_stage": "unhandled",
                        "event_timestamp": time.time(),
                    },
                )

                return {
                    "success": True,
                    "message": f"Event {event_type} acknowledged but not processed",
                }

        except HTTPException:
            # If it's an HTTPException, re-raise it so FastAPI returns the correct status
            raise
        except Exception as e:
            # Track error in metrics
            webhook_counter(str(event_type), "error")

            # Enhanced structured logging for unexpected errors
            error_message = f"Error processing webhook: {str(e)}"
            logger.exception(
                error_message,
                extra={
                    "payload_type": event_type,
                    "clerk_event_id": clerk_event_id,
                    "processing_stage": "error",
                    "error_message": str(e),
                    "event_timestamp": time.time(),
                },
            )

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=error_message
            )


@router.post("/clerk-debug")
async def debug_clerk_webhook(request: Request) -> Dict[str, Any]:
    """
    Debug endpoint for Clerk webhooks.

    This endpoint logs the request details without processing the webhook.
    It's intended for debugging webhook signature verification issues.

    WARNING: This endpoint should be disabled in production environments.
    """
    # Only allow in non-production environments for security
    from a2a_platform.config.settings import get_settings
    from fastapi import HTTPException

    settings = get_settings()
    if settings.ENVIRONMENT.lower() == "production":
        raise HTTPException(
            status_code=404, detail="Debug endpoints are not available in production"
        )

    # Log the request method and URL
    logger.info(f"Received Clerk webhook debug request: {request.method} {request.url}")

    # Get the raw request body
    body: bytes = await request.body()

    # Log the request headers (excluding Authorization headers)
    safe_headers = {
        k: v for k, v in request.headers.items() if k.lower() != "authorization"
    }
    logger.info(f"Clerk webhook debug - Headers: {safe_headers}")

    # Get the Svix headers
    svix_headers = {
        k: v
        for k, v in request.headers.items()
        if k.lower() in ["svix-id", "svix-timestamp", "svix-signature"]
    }

    # Log the Svix headers
    logger.info(f"Clerk webhook debug - Svix headers: {svix_headers}")

    # Log the body (truncated for security)
    body_str = body.decode("utf-8", errors="replace")
    truncated_body = body_str[:200] + "..." if len(body_str) > 200 else body_str
    logger.info(f"Clerk webhook debug - Body: {truncated_body}")

    # Try to parse the body as JSON
    try:
        body_json = json.loads(body)
        event_type = body_json.get("type", "unknown")
        logger.info(f"Clerk webhook debug - Event type: {event_type}")
    except json.JSONDecodeError:
        logger.warning("Clerk webhook debug - Body is not valid JSON")

    # Try to verify the signature
    signature = svix_headers.get("svix-signature", "")
    timestamp = svix_headers.get("svix-timestamp", "")
    message_id = svix_headers.get("svix-id", "")

    if signature and timestamp and message_id:
        try:
            # Use the Svix Webhook class directly to verify
            from svix.webhooks import Webhook

            webhook = Webhook(settings.CLERK_WEBHOOK_SECRET)

            # The verify method expects the payload and headers
            webhook.verify(body, svix_headers)
            logger.info("Clerk webhook debug - Signature verification succeeded")
        except Exception as e:
            logger.warning(
                f"Clerk webhook debug - Signature verification failed: {str(e)}"
            )
    else:
        logger.warning(
            "Clerk webhook debug - Missing required Svix headers for verification"
        )

    # Always return success
    return {"success": True, "message": "Debug endpoint - request logged"}
