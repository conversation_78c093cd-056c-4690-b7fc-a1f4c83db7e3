from typing import List, Optional

# If using FastAPI's Depends, uncomment this:
from fastapi import APIRouter, Depends, HTTPException, Query

from ....db.models.registered_agent import (
    RegisteredAgent as RegisteredAgentModel,  # Added import
)
from ....schemas.agent_schemas import (  # Adjusted import path
    RegisteredAgentListResponse,
    RegisteredAgentRead,
)
from ....services.agent_service import AgentService
from ..dependencies import get_agent_service  # Adjusted import path

# If using the placeholder Depends from dependencies.py, you might not need the above line in final code,
# but it's good for type hinting and clarity during development.

router = APIRouter(
    prefix="/internal/agents",
    tags=["Internal - Agents"],
    # dependencies=[Depends(check_internal_service_auth)], # Example: Common auth for internal routes
    responses={404: {"description": "Not found"}},
)


@router.get(
    "/",
    response_model=RegisteredAgentListResponse,
    summary="List Registered Specialized Agents",
    description="Retrieve a list of specialized agents, with optional filters for status, capabilities, and skills.",
)
async def list_registered_agents(
    status: Optional[str] = Query(
        default=None, description="Filter by agent status ('active' or 'inactive')."
    ),
    # For capabilities and skills, FastAPI handles multiple query params with the same name as a list
    capabilities_filter: Optional[List[str]] = Query(
        default=None,
        alias="capability",
        description="List of capability names. Agents must have ALL specified capabilities.",
    ),
    skills_filter: Optional[List[str]] = Query(
        default=None,
        alias="skill",
        description="List of skill names. Agents must have ALL specified skills.",
    ),
    skip: int = Query(
        default=0, ge=0, description="Number of records to skip for pagination."
    ),
    limit: int = Query(
        default=100, ge=1, le=200, description="Maximum number of records to return."
    ),
    agent_service: AgentService = Depends(get_agent_service),
) -> RegisteredAgentListResponse:
    """
    Lists registered specialized agents.
    - **status**: Filter by 'active' or 'inactive'.
    - **capability**: Provide multiple times for AND logic (e.g., `?capability=A&capability=B`).
    - **skill**: Provide multiple times for AND logic (e.g., `?skill=X&skill=Y`).
    """
    # Validate status if provided
    if status and status not in ["active", "inactive"]:
        raise HTTPException(
            status_code=422,  # Unprocessable Entity
            detail="Invalid status value. Allowed values are 'active' or 'inactive'.",
        )

    result = await agent_service.list_agents(
        status=status,
        capabilities_filter=capabilities_filter,
        skills_filter=skills_filter,
        skip=skip,
        limit=limit,
    )
    agents: List[RegisteredAgentModel]
    total_count: int
    agents, total_count = result

    agent_read_items = [RegisteredAgentRead.model_validate(agent) for agent in agents]
    return RegisteredAgentListResponse(items=agent_read_items, total=total_count)


@router.get(
    "/{agent_definition_id}",
    response_model=RegisteredAgentRead,
    summary="Get a Specific Specialized Agent by ID",
    description="Retrieve details for a single specialized agent by its unique agent_definition_id.",
)
async def get_specialized_agent(
    agent_definition_id: str, agent_service: AgentService = Depends(get_agent_service)
) -> RegisteredAgentRead:
    """
    Retrieves a specialized agent by its `agent_definition_id`.
    """
    agent_model = await agent_service.get_agent_by_definition_id(
        agent_definition_id=agent_definition_id
    )
    if not agent_model:
        raise HTTPException(
            status_code=404,
            detail=f"Agent with definition ID '{agent_definition_id}' not found.",
        )
    return RegisteredAgentRead.model_validate(agent_model)  # Convert to Read schema


# Example of how you might include this router in your main FastAPI app (e.g., in main.py or api.py)
# from fastapi import FastAPI
# app = FastAPI()
# app.include_router(router) # Assuming this router is imported as `router`
