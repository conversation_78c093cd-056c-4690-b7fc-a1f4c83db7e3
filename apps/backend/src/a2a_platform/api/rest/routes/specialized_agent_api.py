"""
API routes for specialized agents.

This module provides API routes for specialized agents to receive
synchronous A2A requests from personal agents.
"""

import logging
from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db import get_db_session
from a2a_platform.schemas.a2a_context import UserContext
from a2a_platform.services.a2a_context_service import A2AContextService

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/internal/specialized-agent",
    tags=["Internal - Specialized Agent"],
    responses={404: {"description": "Not found"}},
)


@router.post("/process")
async def process_request(
    request_data: Dict[str, Any], db: AsyncSession = Depends(get_db_session)
) -> Dict[str, Any]:
    """
    Process a request from a personal agent.

    Args:
        request_data: The request data, including user context and payload
        db: Database session

    Returns:
        Dict[str, Any]: The response data

    Raises:
        HTTPException: If the user context is missing or invalid
    """
    # Extract and validate the user context
    user_context, error_msg = A2AContextService.extract_user_context(request_data)
    if not user_context:
        logger.error(f"User context validation failed: {error_msg}")
        raise HTTPException(
            status_code=400, detail=f"Invalid user context in request: {error_msg}"
        )

    # Log the user context with redacted IDs for privacy
    # In production, consider using a more sophisticated redaction approach
    user_id_redacted = (
        f"{user_context.user_id[:4]}...{user_context.user_id[-4:]}"
        if len(user_context.user_id) > 8
        else "***"
    )
    agent_id_redacted = (
        f"{user_context.initiating_agent_id[:4]}...{user_context.initiating_agent_id[-4:]}"
        if len(user_context.initiating_agent_id) > 8
        else "***"
    )

    logger.info(
        f"Processing request with user_id={user_id_redacted}, "
        f"initiating_agent_id={agent_id_redacted}"
    )

    # Extract the payload
    payload = request_data.get("payload", {})

    # Process the request using the user context and payload
    # This is a placeholder - actual processing would depend on the specific specialized agent
    result = await _handle_request(db, user_context, payload)

    # Return the response
    return {
        "status": "success",
        "message": "Request processed successfully",
        "data": result,
    }


async def _handle_request(
    db: AsyncSession, user_context: UserContext, payload: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Handle a request with the given user context and payload.

    This is a placeholder implementation. In a real specialized agent,
    this would be replaced with actual business logic.

    Args:
        db: Database session
        user_context: The user context extracted from the request
        payload: The payload extracted from the request

    Returns:
        Dict[str, Any]: The result of processing the request
    Raises:
        HTTPException: If the user is not found or not authorized
    """
    # Validate that the user exists in the database
    # This is commented out for now as it would require importing the user service
    # and implementing the actual validation logic
    #
    # from a2a_platform.services.user_service import get_user_by_clerk_id
    #
    # user = await get_user_by_clerk_id(db, user_context.user_id)
    # if not user:
    #     logger.error(f"User with ID {user_context.user_id} not found")
    #     raise HTTPException(status_code=404, detail="User not found")
    #
    # # Additional authorization checks could be implemented here
    # # For example, checking if the initiating agent is allowed to access this specialized agent
    # # or if the user has the necessary permissions
    # Example: Process the payload based on an action parameter
    action = payload.get("action", "default")

    if action == "echo":
        # Simple echo action for testing
        return {
            "echo": payload.get("data", {}),
            "user_id": user_context.user_id,
            "initiating_agent_id": user_context.initiating_agent_id,
            "timestamp": user_context.request_timestamp.isoformat(),
        }

    # Default response
    return {"action": action, "result": "Processed successfully"}
