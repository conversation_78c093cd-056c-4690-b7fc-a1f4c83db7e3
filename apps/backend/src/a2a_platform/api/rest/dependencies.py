# Database session management for FastAPI dependencies
import uuid
from typing import Generator

from fastapi import Depends, <PERSON><PERSON>, HTTPException
from pydantic import <PERSON>UID4
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from a2a_platform.db import get_db_session
from a2a_platform.services.agent_service import AgentService
from a2a_platform.services.task_service import TaskService


def get_db() -> Generator[Session, None, None]:
    """FastAPI dependency to get a DB session.

    This function is deprecated, use get_db_session from a2a_platform.db
    instead. Keeping this for backward compatibility.
    """
    # Import here to avoid circular imports
    from a2a_platform.db import get_session_factory

    # Using AsyncSession but typing as Session for backward compatibility
    session_factory = get_session_factory()
    db = session_factory()
    try:
        yield db  # type: ignore
    finally:
        if db:
            # For AsyncSession, close() is a coroutine, but we can't await in a
            # generator. This is a hack to make mypy happy - in practice, the
            # session will be closed by the context manager in get_db_session
            pass


def get_agent_service(
    session: AsyncSession = Depends(get_db_session),
) -> AgentService:
    """Get an AgentService instance with the current database session.

    Note: AgentService expects a synchronous Session but works fine with
    AsyncSession since it only uses methods that are common to both.
    """
    # We're using AsyncSession but AgentService expects Session
    # This is safe because AgentService only uses methods common to both
    return AgentService(session)


def get_task_service(
    session: AsyncSession = Depends(get_db_session),
) -> TaskService:
    return TaskService(session)


async def get_current_assistant_id(
    x_assistant_id: str = Header(..., description="ID of the current assistant"),
) -> UUID4:
    """Get the current assistant ID from the X-Assistant-ID header."""
    try:
        return uuid.UUID(x_assistant_id)
    except ValueError:
        raise HTTPException(
            status_code=400,
            detail="Invalid assistant ID format. Must be a valid UUID.",
        )
