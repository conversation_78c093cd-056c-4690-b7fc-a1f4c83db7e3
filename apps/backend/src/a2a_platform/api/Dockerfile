# Dockerfile for API Service
# Python base image
FROM python:3.12-slim

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app/src

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    gcc \
    libc-dev \
    python3-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy pyproject.toml (from context root 'apps/backend') to WORKDIR '/app'
COPY pyproject.toml ./

# Upgrade pip and install dependencies from pyproject.toml
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -e "."

# Copy the 'src' directory (from context root 'apps/backend/src') into '/app/src'
COPY src /app/src/

# Copy the entrypoint script and make it executable
COPY ./src/a2a_platform/api/entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

# Expose port (Cloud Run will use the PORT env var)
EXPOSE 8080

# Command to run the application
# Assumes 'app' is in 'a2a_platform.main'
ENTRYPOINT ["/app/entrypoint.sh"]
CMD []
