from fastapi import FastAPI
from pydantic import BaseModel
from typing import Dict

app = FastAPI(title="Messaging Service")


class HealthCheckResponse(BaseModel):
    status: str = "OK"


@app.get("/health", response_model=HealthCheckResponse)
async def health_check() -> HealthCheckResponse:
    """
    Simple health check endpoint.
    """
    return HealthCheckResponse(status="OK")


# Placeholder for other messaging-related routes or startup logic
@app.on_event("startup")
async def startup_event() -> None:
    # Initialize any resources here if needed
    # e.g., database connections, queue clients
    print("Messaging service has started.")


@app.on_event("shutdown")
async def shutdown_event() -> None:
    # Clean up resources here if needed
    print("Messaging service is shutting down.")


# Example route (optional, can be removed or expanded)
class Message(BaseModel):
    text: str


@app.post("/send_message")
async def send_message(message: Message) -> Dict[str, str]:
    # In a real application, this would send the message to a queue or another service
    print(f"Received message: {message.text}")
    return {"status": "message sent", "message": message.text}


if __name__ == "__main__":
    import uvicorn

    # Default to 127.0.0.1 for local runs; containerized environments use entrypoint.sh
    uvicorn.run(app, host="127.0.0.1", port=8080)
