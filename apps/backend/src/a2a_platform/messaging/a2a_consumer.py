"""
Consumer for processing asynchronous A2A messages from personal agents.

This module provides a consumer for processing asynchronous messages from
personal agents, including user context extraction.
"""

import json
import logging
from typing import Any, Dict

from a2a_platform.messaging.queue_client import QueueClient
from a2a_platform.schemas.a2a_context import UserContext
from a2a_platform.services.a2a_context_service import A2AContextService

logger = logging.getLogger(__name__)


class A2AMessageConsumer:
    """
    Consumer for processing asynchronous A2A messages from personal agents.
    """

    def __init__(self, queue_client: QueueClient):
        """
        Initialize the A2A message consumer.

        Args:
            queue_client: The queue client to use for receiving messages
        """
        self.queue_client = queue_client

    async def process_message(self, message: Dict[str, Any]) -> bool:
        """
        Process a message from a personal agent.

        Args:
            message: The message data, including user context and payload

        Returns:
            bool: True if the message was processed successfully, False otherwise
        """
        try:
            # Parse the message body
            message_body = json.loads(message.get("body", "{}"))

            # Extract and validate the user context

            user_context, error_msg = A2AContextService.extract_user_context(
                message_body
            )
            if not user_context:
                logger.error(f"User context validation failed: {error_msg}")
                # Store the message in a dead-letter queue or error log for later analysis
                # error_msg is guaranteed to be non-None here since user_context is None
                await self._handle_invalid_message(
                    message, error_msg or "Unknown error"
                )
                return False

            # Log the user context with redacted IDs for privacy
            # In production, consider using a more sophisticated redaction approach
            user_id_redacted = (
                f"{user_context.user_id[:4]}...{user_context.user_id[-4:]}"
                if len(user_context.user_id) > 8
                else "***"
            )
            agent_id_redacted = (
                f"{user_context.initiating_agent_id[:4]}...{user_context.initiating_agent_id[-4:]}"
                if len(user_context.initiating_agent_id) > 8
                else "***"
            )

            logger.info(
                f"Processing message with user_id={user_id_redacted}, "
                f"initiating_agent_id={agent_id_redacted}"
            )

            # Extract the payload
            payload = message_body.get("payload", {})

            # Process the message using the user context and payload
            # This is a placeholder - actual processing would be implemented by subclasses
            result = await self._handle_message(user_context, payload)

            return result
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            return False

    async def _handle_invalid_message(
        self, message: Dict[str, Any], error_msg: str
    ) -> None:
        """
        Handle an invalid message by logging it for later analysis.

        In a production environment, this method could be extended to:
        1. Store the message in a dead-letter queue
        2. Send alerts to monitoring systems
        3. Implement retry logic with backoff

        Args:
            message: The original message that failed validation
            error_msg: The specific error message describing the validation failure
        """
        # Log detailed information about the invalid message
        logger.warning(
            f"Invalid message received: {error_msg}. "
            f"Message ID: {message.get('message_id', 'unknown')}. "
            f"This message will not be processed."
        )

        # In a production implementation, you might add code here to:
        # 1. Store the message in a dead-letter queue
        # 2. Increment error metrics
        # 3. Trigger alerts if error threshold is exceeded

    async def _handle_message(
        self, user_context: UserContext, payload: Dict[str, Any]
    ) -> bool:
        """
        Handle a message with the given user context and payload.

        This method should be overridden by subclasses to implement specific message handling.

        Args:
            user_context: The user context extracted from the message
            payload: The payload extracted from the message

        Returns:
            bool: True if the message was handled successfully, False otherwise
        """
        logger.warning("_handle_message not implemented")
        return True

    async def start_consumer(self, queue_name: str) -> None:
        """
        Start consuming messages from the specified queue.

        Args:
            queue_name: The name of the queue to consume messages from
        """
        logger.info(f"Starting A2A message consumer for queue {queue_name}")
        await self.queue_client.consume_messages(
            queue_name=queue_name, handler=self.process_message
        )
