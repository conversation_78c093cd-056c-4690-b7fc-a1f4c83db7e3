"""
Producer for sending asynchronous A2A messages to specialized agents.

This module provides a producer for sending asynchronous messages to
specialized agents, including user context propagation.
"""

import json
import logging
from datetime import UTC, datetime
from typing import Any, Dict, Optional

from a2a_platform.messaging.queue_client import QueueClient
from a2a_platform.services.a2a_context_service import A2AContextService

logger = logging.getLogger(__name__)


class DateTimeEncoder(json.JSONEncoder):
    """JSON encoder that handles datetime objects."""

    def default(self, obj: Any) -> Any:
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)


class A2AMessageProducer:
    """
    Producer for sending asynchronous A2A messages to specialized agents.
    """

    def __init__(self, queue_client: QueueClient):
        """
        Initialize the A2A message producer.

        Args:
            queue_client: The queue client to use for sending messages
        """
        self.queue_client = queue_client

    async def send_message(
        self,
        queue_name: str,
        payload: Dict[str, Any],
        user_id: str,
        initiating_agent_id: str,
        message_attributes: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Send a message to a specialized agent with the given payload and user context.

        Args:
            queue_name: The name of the queue to send the message to
            payload: The payload to send to the specialized agent
            user_id: The unique identifier of the end-user
            initiating_agent_id: The identifier of the agent initiating the request
            message_attributes: Optional additional attributes to include with the message

        Returns:
            str: The message ID

        Raises:
            Exception: If the message cannot be sent
        """

        # Log with redacted user ID for privacy
        user_id_redacted = (
            f"{user_id[:4]}...{user_id[-4:]}" if len(user_id) > 8 else "***"
        )
        logger.debug(
            f"Sending message to queue {queue_name} with user_id={user_id_redacted}"
        )

        # Create the A2A message with user context
        a2a_message = A2AContextService.create_a2a_message(
            user_id=user_id, initiating_agent_id=initiating_agent_id, payload=payload
        )

        # Convert to dict for the message
        message_data = a2a_message.model_dump()

        # Set up message attributes
        attrs = message_attributes or {}
        attrs.update(
            {
                "user_id": user_id,
                "initiating_agent_id": initiating_agent_id,
                "timestamp": datetime.now(UTC).isoformat(),
            }
        )

        # Send the message
        message_id = await self.queue_client.send_message(
            queue_name=queue_name,
            message_body=json.dumps(message_data, cls=DateTimeEncoder),
            message_attributes=attrs,
        )

        logger.debug(f"Message sent to queue {queue_name} with ID {message_id}")
        return message_id
