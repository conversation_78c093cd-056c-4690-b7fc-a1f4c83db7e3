"""
Queue client for asynchronous messaging.

This module provides a client for interacting with message queues,
which can be used for asynchronous A2A communication.
"""

import json
import logging
from abc import ABC, abstractmethod
from typing import Any, Callable, Dict, Optional, cast

from a2a_platform.config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class QueueClient(ABC):
    """
    Abstract base class for queue clients.

    This class defines the interface that all queue clients must implement.
    Concrete implementations will be provided for specific message queue systems
    (e.g., Google Pub/Sub, AWS SQS, etc.).
    """

    @abstractmethod
    async def send_message(
        self,
        queue_name: str,
        message_body: str,
        message_attributes: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Send a message to a queue.

        Args:
            queue_name: The name of the queue to send the message to
            message_body: The body of the message
            message_attributes: Optional attributes to include with the message

        Returns:
            str: The message ID

        Raises:
            Exception: If the message cannot be sent
        """
        pass

    @abstractmethod
    async def consume_messages(
        self, queue_name: str, handler: Callable[[Dict[str, Any]], Any]
    ) -> None:
        """
        Consume messages from a queue.

        Args:
            queue_name: The name of the queue to consume messages from
            handler: A function that will be called for each message

        Raises:
            Exception: If messages cannot be consumed
        """
        pass


class PubSubClient(QueueClient):
    """
    Google Pub/Sub implementation of the QueueClient interface.

    This class provides methods for sending and consuming messages using Google Pub/Sub.
    """

    def __init__(self, project_id: Optional[str] = None):
        """
        Initialize the Pub/Sub client.

        Args:
            project_id: The Google Cloud project ID. If not provided, it will be
                        taken from the application settings.
        """
        self.project_id = project_id or settings.PUBSUB_PROJECT_ID

        # Import here to avoid requiring google-cloud-pubsub for all users
        try:
            from google.cloud import pubsub_v1  # type: ignore

            self.publisher = pubsub_v1.PublisherClient()
            self.subscriber = pubsub_v1.SubscriberClient()
        except ImportError:
            logger.warning(
                "google-cloud-pubsub not installed. PubSubClient will not work."
            )
            self.publisher = None
            self.subscriber = None

    async def send_message(
        self,
        queue_name: str,
        message_body: str,
        message_attributes: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Send a message to a Pub/Sub topic.

        Args:
            queue_name: The name of the Pub/Sub topic
            message_body: The body of the message
            message_attributes: Optional attributes to include with the message

        Returns:
            str: The message ID

        Raises:
            Exception: If the message cannot be sent
        """
        if self.publisher is None:
            raise RuntimeError("google-cloud-pubsub not installed")

        topic_path = self.publisher.topic_path(self.project_id, queue_name)

        # Convert message attributes to the format expected by Pub/Sub
        pubsub_attributes = {}
        if message_attributes:
            for key, value in message_attributes.items():
                if isinstance(value, (str, int, float, bool)):
                    pubsub_attributes[key] = str(value)
                else:
                    pubsub_attributes[key] = json.dumps(value)

        # Publish the message
        future = self.publisher.publish(
            topic_path, data=message_body.encode("utf-8"), **pubsub_attributes
        )

        # Wait for the message to be published
        message_id = future.result()

        logger.debug(f"Published message to {queue_name} with ID: {message_id}")
        return cast(str, message_id)

    async def consume_messages(
        self, queue_name: str, handler: Callable[[Dict[str, Any]], Any]
    ) -> None:
        """
        Consume messages from a Pub/Sub subscription.

        Note: This method uses an async wrapper around the callback-based Pub/Sub client.
        The callback function is defined as async, but the Pub/Sub client itself is not
        natively async. This implementation ensures that the async handler is properly
        awaited within the callback, but care should be taken when integrating with
        other async code to avoid event loop or concurrency issues.

        Args:
            queue_name: The name of the Pub/Sub subscription
            handler: A function that will be called for each message

        Raises:
            Exception: If messages cannot be consumed
        """
        if self.subscriber is None:
            raise RuntimeError("google-cloud-pubsub not installed")

        subscription_path = self.subscriber.subscription_path(
            self.project_id, queue_name
        )

        async def callback(message: Any) -> None:
            """
            Process a Pub/Sub message.

            This is an async wrapper around the callback-based Pub/Sub client.
            It ensures that the async handler is properly awaited.
            """
            logger.debug(f"Received message: {message.message_id}")

            try:
                # Convert the message to a dictionary
                message_dict = {
                    "message_id": message.message_id,
                    "body": message.data.decode("utf-8"),
                    "attributes": dict(message.attributes),
                    "publish_time": message.publish_time.isoformat(),
                }

                # Call the handler
                result = await handler(message_dict)

                # Acknowledge the message if the handler was successful
                if result:
                    message.ack()
                else:
                    message.nack()
            except Exception as e:
                logger.error(f"Error processing message: {str(e)}")
                message.nack()

        # Start consuming messages

        # Note: The Pub/Sub client is not natively async, so we're using a callback-based approach
        # This means the subscribe method will run the callback in a separate thread
        streaming_pull_future = self.subscriber.subscribe(
            subscription_path, callback=callback
        )

        logger.info(f"Listening for messages on {subscription_path}")

        try:
            # Keep the subscriber alive
            streaming_pull_future.result()
        except Exception as e:
            streaming_pull_future.cancel()
            logger.error(f"Streaming pull future terminated: {str(e)}")
            raise
