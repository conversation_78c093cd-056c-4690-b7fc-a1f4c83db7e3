"""
Factory for creating queue clients.

This module provides a factory function for creating queue clients
based on the specified queue type.
"""

import logging
from typing import TYPE_CHECKING, Literal, Optional

from a2a_platform.config.settings import get_settings
from a2a_platform.messaging.queue_client import PubSubClient, QueueClient

if TYPE_CHECKING:
    pass

logger = logging.getLogger(__name__)
settings = get_settings()


async def create_queue_client(
    queue_type: Literal["pubsub", "rq"] = "pubsub",
    project_id: Optional[str] = None,
    redis_url: Optional[str] = None,
) -> QueueClient:
    """
    Create a queue client of the specified type.

    Args:
        queue_type: The type of queue client to create ("pubsub" or "rq")
        project_id: The Google Cloud project ID (for PubSubClient)
        redis_url: The Redis URL (for RQClient)

    Returns:
        QueueClient: The queue client instance

    Raises:
        ValueError: If an invalid queue type is specified
    """
    if queue_type == "pubsub":
        logger.info("Creating PubSubClient")
        return PubSubClient(project_id=project_id)
    elif queue_type == "rq":
        logger.info("Creating RQClient")
        # Import here to avoid circular imports
        from a2a_platform.messaging.rq_client import RQClient

        return RQClient(
            redis_url=redis_url,
            default_job_timeout=settings.RQ_DEFAULT_TIMEOUT,
            default_ttl=settings.RQ_DEFAULT_TTL,
        )
    else:
        raise ValueError(f"Invalid queue type: {queue_type}")
