"""
Subscription manager for real-time messaging using Redis pub/sub.

This module provides a centralized manager for handling GraphQL subscriptions
and real-time message delivery using Redis as the message broker.
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime
from typing import Any, Async<PERSON>enerator, Dict, Optional, Set

import redis.asyncio as redis

from a2a_platform.api.graphql.schemas.chat_schemas import (
    ChatMessage,
    MessageSubscriptionPayload,
)
from a2a_platform.config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class SubscriptionManager:
    """
    Manages GraphQL subscriptions and real-time message delivery.

    This class handles Redis pub/sub connections, subscription management,
    and message broadcasting for real-time chat functionality.
    """

    _instance: Optional["SubscriptionManager"] = None
    _lock = asyncio.Lock()

    def __init__(self, redis_url: Optional[str] = None):
        """
        Initialize the subscription manager.

        Args:
            redis_url: Redis connection URL. If not provided, uses settings.
        """
        self.redis_url = redis_url or settings.REDIS_URL
        self._redis_client: Optional[redis.Redis] = None
        # Remove shared _pubsub - each subscription will have its own
        # connection
        self._active_subscriptions: Dict[str, Set[uuid.UUID]] = {}
        self._subscription_tasks: Dict[str, asyncio.Task[Any]] = {}
        self._running = False

    @classmethod
    async def get_instance(
        cls, redis_url: Optional[str] = None
    ) -> "SubscriptionManager":
        """
        Get or create the singleton subscription manager instance.

        Args:
            redis_url: Redis connection URL

        Returns:
            SubscriptionManager: The singleton instance
        """
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls(redis_url)
                    await cls._instance.initialize()
        return cls._instance

    async def initialize(self) -> None:
        """Initialize Redis connections and start the subscription manager."""
        try:
            self._redis_client = redis.from_url(self.redis_url)  # type: ignore
            # No longer initialize shared _pubsub - each subscription creates
            # its own
            self._running = True
            logger.info("Subscription manager initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize subscription manager: {str(e)}")
            raise

    async def shutdown(self) -> None:
        """Shutdown the subscription manager and clean up resources."""
        self._running = False

        # Cancel all subscription tasks
        for task in self._subscription_tasks.values():
            if not task.done():
                task.cancel()

        # Close Redis connections
        # No longer need to close shared _pubsub - each subscription manages
        # its own connection
        if self._redis_client:
            await self._redis_client.close()

        logger.info("Subscription manager shut down")

    def _get_channel_name(self, conversation_id: uuid.UUID) -> str:
        """
        Get the Redis channel name for a conversation.

        Args:
            conversation_id: The conversation UUID

        Returns:
            str: The Redis channel name
        """
        return f"chat:conversation:{conversation_id}"

    async def publish_message(
        self, conversation_id: uuid.UUID, message: Any, event_type: str = "new_message"
    ) -> None:
        """
        Publish a message to subscribers of a conversation.

        Args:
            conversation_id: The conversation UUID
            message: The message data (ChatMessage model)
            event_type: The type of event (default: "new_message")
        """

        logger.debug(f"Publishing message: {message}")

        if not self._redis_client:
            logger.warning("Redis client not initialized, cannot publish message")
            return

        try:
            # Convert message to GraphQL type
            message_gql = ChatMessage.from_db_model(message)

            # Serialize payload for Redis
            # Safely extract content - it's a Strawberry field property
            try:
                # Ensure we call the content method if it's a method, not just
                # access the property
                content_attr = getattr(message_gql, "content")
                if callable(content_attr):
                    content_value = content_attr()
                else:
                    content_value = content_attr
            except Exception as content_error:
                logger.warning(f"Error accessing message content: {content_error}")
                content_value = {"text": ""}

            # Handle timestamp - convert to ISO format if it's a datetime,
            # otherwise use as-is
            if isinstance(message_gql.timestamp, datetime):
                timestamp_str = message_gql.timestamp.isoformat()
            else:
                # If it's already a string, use it as-is
                timestamp_str = message_gql.timestamp

            # Safely handle metadata - ensure it's JSON serializable
            try:
                metadata_value = message_gql.metadata or {}
                # Test JSON serialization to catch any non-serializable objects
                json.dumps(metadata_value)
            except (TypeError, ValueError) as metadata_error:
                logger.warning(f"Metadata not JSON serializable: {metadata_error}")
                metadata_value = {}

            payload_data = {
                "message": {
                    "id": str(message_gql.id),
                    "conversationId": str(message_gql.conversation_id),
                    "senderRole": str(getattr(message_gql, "_sender_role", "user")),
                    "content": content_value,
                    "timestamp": timestamp_str,
                    "metadata": metadata_value,
                },
                "conversationId": str(conversation_id),
                "eventType": event_type,
            }

            # Check if event loop is available and not closed
            try:
                loop = asyncio.get_running_loop()
                if loop.is_closed():
                    logger.warning("Event loop is closed, skipping message publication")
                    return
            except RuntimeError:
                logger.warning("No running event loop, skipping message publication")
                return

            channel_name = self._get_channel_name(conversation_id)
            await self._redis_client.publish(channel_name, json.dumps(payload_data))

            logger.debug(f"Published message to channel {channel_name}")

        except Exception as e:
            logger.error(f"Failed to publish message: {str(e)}")
            # Don't raise the exception to avoid affecting the main flow
            # The message was already saved to the database successfully

    async def subscribe_to_messages(
        self, conversation_id: uuid.UUID, user_id: uuid.UUID
    ) -> AsyncGenerator[MessageSubscriptionPayload, None]:
        """
        Subscribe to messages for a conversation.

        Each subscription creates its own Redis PubSub connection to avoid
        concurrency issues with multiple coroutines reading from the same
        connection.

        Args:
            conversation_id: The conversation UUID
            user_id: The user UUID (for access control)

        Yields:
            MessageSubscriptionPayload: New message events
        """
        if not self._redis_client:
            logger.error("Redis client not initialized")
            return

        channel_name = self._get_channel_name(conversation_id)

        # Create a dedicated Redis connection for this subscription
        subscription_redis = None
        subscription_pubsub = None

        try:
            # Create dedicated Redis connection for this subscription
            subscription_redis = redis.from_url(self.redis_url)  # type: ignore
            subscription_pubsub = subscription_redis.pubsub()

            # Subscribe to the conversation channel
            await subscription_pubsub.subscribe(channel_name)
            logger.info(f"User {user_id} subscribed to channel {channel_name}")

            # Track active subscription
            if channel_name not in self._active_subscriptions:
                self._active_subscriptions[channel_name] = set()
            self._active_subscriptions[channel_name].add(user_id)

            # Listen for messages on this dedicated connection
            async for message in subscription_pubsub.listen():
                if not self._running:
                    break

                if message["type"] == "message":
                    try:
                        # Parse message data
                        payload_data = json.loads(message["data"])

                        # Reconstruct message object
                        message_data = payload_data["message"]

                        # Convert timestamp string back to datetime object
                        timestamp_str = message_data["timestamp"]
                        if isinstance(timestamp_str, str):
                            timestamp = datetime.fromisoformat(
                                timestamp_str.replace("Z", "+00:00")
                            )
                        else:
                            timestamp = timestamp_str

                        # Create ChatMessage with private _content field
                        message_gql = ChatMessage(
                            id=uuid.UUID(message_data["id"]),
                            conversation_id=uuid.UUID(message_data["conversationId"]),
                            _sender_role=message_data["senderRole"],
                            timestamp=timestamp,
                            metadata=message_data.get("metadata"),
                        )
                        # Set the private _content field directly
                        message_gql._content = message_data["content"]

                        # Create subscription payload
                        subscription_payload = MessageSubscriptionPayload(
                            message=message_gql,
                            conversation_id=uuid.UUID(payload_data["conversationId"]),
                            event_type=payload_data["eventType"],
                        )

                        yield subscription_payload

                    except Exception as e:
                        logger.error(f"Error processing subscription message: {str(e)}")

        except Exception as e:
            logger.error(f"Error in message subscription: {str(e)}")
        finally:
            # Clean up subscription tracking
            if channel_name in self._active_subscriptions:
                self._active_subscriptions[channel_name].discard(user_id)
                if not self._active_subscriptions[channel_name]:
                    del self._active_subscriptions[channel_name]

            # Clean up dedicated Redis connection
            if subscription_pubsub:
                try:
                    await subscription_pubsub.unsubscribe(channel_name)
                    await subscription_pubsub.close()
                    logger.info(
                        f"User {user_id} unsubscribed from channel {channel_name}"
                    )
                except Exception as e:
                    logger.error(f"Error unsubscribing from channel: {str(e)}")

            if subscription_redis:
                try:
                    await subscription_redis.close()
                except Exception as e:
                    logger.error(
                        f"Error closing subscription Redis connection: {str(e)}"
                    )

    def get_active_subscriptions(self) -> Dict[str, Set[uuid.UUID]]:
        """
        Get the current active subscriptions.

        Returns:
            Dict[str, Set[uuid.UUID]]: Mapping of channel names to user IDs
        """
        return self._active_subscriptions.copy()
