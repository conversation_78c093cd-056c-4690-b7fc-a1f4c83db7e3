"""
Pydantic models for A2A context propagation.

This module defines the Pydantic models that represent the A2A context structure
defined in a2a-schema.json. These models are used for validation and
serialization/deserialization of A2A payloads.
"""

from datetime import UTC, datetime
from typing import Any, Dict

from pydantic import BaseModel, ConfigDict, Field


class UserContext(BaseModel):
    """
    User and initiating agent context for internal A2A communication.
    Designed to be potentially embeddable in broader A2A protocols in the future.
    """

    user_id: str = Field(
        ...,
        description="The unique identifier of the end-user.",
    )
    initiating_agent_id: str = Field(
        ..., description="The identifier of the VEDAVIVI agent initiating the request."
    )
    request_timestamp: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        description="Timestamp of when the context was generated for the request.",
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "user_id": "user123",
                "initiating_agent_id": "agent456",
                "request_timestamp": "2023-01-01T00:00:00+00:00",
            }
        }
    )


class A2AMessage(BaseModel):
    """
    Base model for A2A messages, including the user context and payload.
    """

    user_context: UserContext = Field(
        ..., description="User and initiating agent context."
    )
    payload: Dict[str, Any] = Field(
        ..., description="The actual payload of the A2A message or request."
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "user_context": {
                    "user_id": "user123",
                    "initiating_agent_id": "agent456",
                    "request_timestamp": "2023-01-01T00:00:00+00:00",
                },
                "payload": {"action": "test", "data": {"key": "value"}},
            }
        }
    )
