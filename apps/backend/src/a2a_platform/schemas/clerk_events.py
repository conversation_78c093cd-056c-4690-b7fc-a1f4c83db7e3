"""
Pydantic models for Clerk webhook events.

These models define the structure of the webhook payloads sent by <PERSON>
for various events like user.created and user.deleted.
"""

from typing import Any, Dict, List, Optional

from pydantic import BaseModel, EmailStr, Field


class ClerkEventBase(BaseModel):
    """Base model for all Clerk webhook events."""

    type: str = Field(..., description="The type of event (e.g., 'user.created')")
    object: str = Field(..., description="The object type (e.g., 'event')")
    clerk_event_id: Optional[str] = Field(
        None, description="The Clerk event ID (from Svix-Id header)"
    )


class UserVerification(BaseModel):
    """Verification information for a user email."""

    status: Optional[str] = None
    strategy: Optional[str] = None
    nonce: Optional[str] = None
    expiration_seconds: Optional[int] = None
    expired: Optional[bool] = None


class UserEmailInfo(BaseModel):
    """Email information for a Clerk user."""

    id: str = Field(..., description="The ID of the email address")
    email_address: EmailStr = Field(..., description="The email address")
    verification: Optional[UserVerification] = Field(
        None, description="Verification information for the email"
    )
    verified: bool = Field(
        False, description="Whether the email address has been verified"
    )
    linked_to: Optional[List[Dict[str, str]]] = Field(
        None, description="List of objects this email is linked to"
    )


class UserCreatedData(BaseModel):
    """Data for a user.created event."""

    id: str = Field(..., description="The Clerk user ID")
    email_addresses: List[UserEmailInfo] = Field(
        ..., description="List of email addresses associated with the user"
    )
    first_name: Optional[str] = Field(
        None, description="The user's first name, if provided"
    )
    last_name: Optional[str] = Field(
        None, description="The user's last name, if provided"
    )
    public_metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Public metadata for the user"
    )
    created_at: str = Field(
        ...,
        description="ISO8601 timestamp of user creation, e.g. '2024-01-01T00:00:00Z'",
    )
    updated_at: str = Field(
        ..., description="ISO8601 timestamp of last update, e.g. '2024-01-01T00:00:00Z'"
    )


class UserDeletedData(BaseModel):
    """Data for a user.deleted event."""

    id: str = Field(..., description="The Clerk user ID")
    deleted: bool = Field(
        default=True,
        description="Whether the user was deleted (always true for this event)",
    )


class UserCreatedEvent(ClerkEventBase):
    """Complete user.created event from Clerk."""

    data: UserCreatedData = Field(..., description="The user data")


class UserDeletedEvent(ClerkEventBase):
    """Complete user.deleted event from Clerk."""

    data: UserDeletedData = Field(..., description="The user deletion data")
