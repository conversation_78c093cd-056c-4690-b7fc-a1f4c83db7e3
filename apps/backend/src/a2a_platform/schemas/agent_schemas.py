import re
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, HttpUrl, field_validator, model_validator


# --- Helper Schemas ---
class AgentSkill(BaseModel):
    """Represents a skill an agent possesses, aligning with A2A schema concepts."""

    name: str = Field(..., description="The name of the skill.")
    # Parameters could be a flexible JSON schema definition itself, or a simpler dict for MVP
    parameters_schema: Optional[Dict[str, Any]] = Field(
        default=None, description="Schema definition for parameters this skill accepts."
    )
    description: Optional[str] = Field(
        default=None, description="A brief description of what the skill does."
    )

    model_config = {
        "extra": "forbid"  # Disallow extra fields
    }


# --- Base Schema ---
class RegisteredAgentBase(BaseModel):
    """Base schema for RegisteredAgent, containing common fields."""

    name: str = Field(
        description="Human-readable name of the SA type.", min_length=1, max_length=255
    )
    description: Optional[str] = Field(
        default=None, description="Description of the SA's purpose.", max_length=2000
    )
    version: str = Field(
        description="Version of this agent definition.", min_length=1, max_length=50
    )  # e.g., "1.0.0", "0.1.alpha"

    endpoint_url: Optional[HttpUrl] = Field(
        default=None,
        description="Internal URL for synchronous HTTP calls (if supported).",
    )
    async_queue_name: Optional[str] = Field(
        default=None,
        description="Message queue name for asynchronous calls (if supported).",
        max_length=255,
    )

    capabilities: Optional[Dict[str, Any]] = Field(
        default_factory=lambda: {},
        description='Agent capabilities (e.g., { "streaming": true } based on A2A schema).',
    )
    skills: Optional[List[AgentSkill]] = Field(
        default_factory=lambda: [], description="List of skills the agent possesses."
    )
    authentication_info: Optional[Dict[str, Any]] = Field(
        default_factory=lambda: {},
        description="Info on how internal services should authenticate (if needed).",
    )

    # Agent Marketplace fields
    developer_id: Optional[str] = Field(
        default=None,
        description="Identifier for the developer who created this agent (for marketplace).",
        max_length=255,
    )
    pricing_info: Optional[Dict[str, Any]] = Field(
        default_factory=lambda: {},
        description="""
        Pricing information for the agent in the marketplace.

        Examples:
        - Subscription model: {'type': 'subscription', 'tier_id': 'sub_123', 'currency': 'USD', 'amount_monthly': 999}
        - Per-call model: {'type': 'per_call', 'currency': 'USD', 'amount_per_call': 1}
        - Free model: {'type': 'free'}
        """,
    )
    review_status: Optional[str] = Field(
        default=None,
        description="Review status for marketplace agents (e.g., 'pending', 'approved', 'rejected').",
        max_length=50,
    )

    status: str = Field(
        default="active",
        description="Whether this agent type is currently available ('active' or 'inactive').",
    )

    @field_validator("status")
    @classmethod
    def validate_status(_cls, value: str) -> str:
        if value not in ["active", "inactive"]:
            raise ValueError("Status must be either 'active' or 'inactive'")
        return value

    @field_validator("async_queue_name")
    @classmethod
    def validate_queue_name_format(_cls, value: Optional[str]) -> Optional[str]:
        if value is None:
            return None
        # Example: basic validation for typical queue name characters
        if not re.match(r"^[a-zA-Z0-9_.-]+$", value):
            raise ValueError("Async queue name contains invalid characters.")
        return value

    @field_validator("review_status")
    @classmethod
    def validate_review_status(_cls, value: Optional[str]) -> Optional[str]:
        if value is None:
            return None
        valid_statuses = ["pending", "approved", "rejected"]
        if value not in valid_statuses:
            raise ValueError(
                f"Review status must be one of: {', '.join(valid_statuses)}"
            )
        return value

    @model_validator(mode="after")
    def check_at_least_one_contact_method(self) -> "RegisteredAgentBase":
        if not self.endpoint_url and not self.async_queue_name:
            # This validation might be too strict depending on requirements.
            # For now, let's assume an agent must have at least one way to be contacted.
            # If not, this validator can be removed or adjusted.
            # raise ValueError("An agent must have either an endpoint_url or an async_queue_name specified.")
            pass  # Allowing agents with no contact method for now, can be made stricter later if needed.
        return self

    model_config = {"extra": "forbid"}


# --- Schema for Creation ---
class RegisteredAgentCreate(RegisteredAgentBase):
    """Schema for creating a new RegisteredAgent. Includes the ID."""

    agent_definition_id: str = Field(
        description="Unique identifier for the SA type/version (e.g., 'summarizer_v1').",
        min_length=1,
        max_length=255,
    )

    @field_validator("agent_definition_id")
    @classmethod
    def validate_agent_definition_id_format(_cls, value: str) -> str:
        if not re.match(r"^[a-zA-Z0-9_.-]+$", value):
            raise ValueError("Agent definition ID contains invalid characters.")
        return value


# --- Schema for Updating (Optional for now, but good practice) ---
class RegisteredAgentUpdate(BaseModel):
    """Schema for updating an existing RegisteredAgent. All fields are optional."""

    # agent_definition_id is typically not updatable as it's a PK.
    name: Optional[str] = Field(
        default=None,
        description="Human-readable name of the SA type.",
        min_length=1,
        max_length=255,
    )
    description: Optional[str] = Field(
        default=None, description="Description of the SA's purpose.", max_length=2000
    )
    version: Optional[str] = Field(
        default=None,
        description="Version of this agent definition.",
        min_length=1,
        max_length=50,
    )

    endpoint_url: Optional[HttpUrl] = Field(
        default=None,
        description="Internal URL for synchronous HTTP calls (if supported).",
    )
    async_queue_name: Optional[str] = Field(
        default=None,
        description="Message queue name for asynchronous calls (if supported).",
        max_length=255,
    )

    capabilities: Optional[Dict[str, Any]] = Field(
        default=None, description="Agent capabilities."
    )
    skills: Optional[List[AgentSkill]] = Field(
        default=None, description="List of skills."
    )
    authentication_info: Optional[Dict[str, Any]] = Field(
        default=None, description="Authentication info."
    )
    status: Optional[str] = Field(
        default=None,
        description="Whether this agent type is currently available ('active' or 'inactive').",
    )

    # Agent Marketplace fields
    developer_id: Optional[str] = Field(
        default=None,
        description="Identifier for the developer who created this agent (for marketplace).",
        max_length=255,
    )
    pricing_info: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Pricing information for the agent in the marketplace.",
    )
    review_status: Optional[str] = Field(
        default=None,
        description="Review status for marketplace agents (e.g., 'pending', 'approved', 'rejected').",
        max_length=50,
    )

    @field_validator("status")
    @classmethod
    def validate_status(_cls, value: Optional[str]) -> Optional[str]:
        if value is None:
            return None
        if value not in ["active", "inactive"]:
            raise ValueError("Status must be either 'active' or 'inactive'")
        return value

    @field_validator("review_status")
    @classmethod
    def validate_review_status(_cls, value: Optional[str]) -> Optional[str]:
        if value is None:
            return None
        valid_statuses = ["pending", "approved", "rejected"]
        if value not in valid_statuses:
            raise ValueError(
                f"Review status must be one of: {', '.join(valid_statuses)}"
            )
        return value

    @field_validator("async_queue_name")
    @classmethod
    def validate_queue_name_format(_cls, value: Optional[str]) -> Optional[str]:
        if value is None:
            return None
        # Example: basic validation for typical queue name characters
        if not re.match(r"^[a-zA-Z0-9_.-]+$", value):
            raise ValueError("Async queue name contains invalid characters.")
        return value

    model_config = {
        "extra": "forbid"  # Disallow extra fields
    }


# --- Schema for Reading/Response ---
class RegisteredAgentRead(RegisteredAgentBase):
    """Schema for reading/returning RegisteredAgent data, includes DB-generated fields."""

    agent_definition_id: str
    created_at: str  # ISO8601 timestamp, e.g. '2024-01-01T00:00:00Z'
    updated_at: str  # ISO8601 timestamp, e.g. '2024-01-01T00:00:00Z'

    model_config = {
        "from_attributes": True  # Formerly orm_mode = True
    }


# --- Schema for API List Response (Optional, for pagination etc.) ---
class RegisteredAgentListResponse(BaseModel):
    items: List[RegisteredAgentRead]
    total: int
    # limit: int
    # offset: int
