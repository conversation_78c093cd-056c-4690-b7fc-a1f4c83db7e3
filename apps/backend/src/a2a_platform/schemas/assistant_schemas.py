"""
Pydantic schemas for Assistant operations.

These schemas define the data structures for creating, updating, and reading
personal assistants.
"""

import uuid
from datetime import datetime
from typing import Any, Dict, Optional

from pydantic import BaseModel, ConfigDict, Field


class AssistantBase(BaseModel):
    """Base schema for Assistant with common fields."""

    name: str = Field(..., min_length=1, max_length=100, description="Assistant name")
    backstory: str = Field(
        ..., min_length=1, max_length=2000, description="Assistant backstory"
    )
    avatar_file_id: Optional[uuid.UUID] = Field(
        None, description="ID of the avatar file"
    )
    configuration: Dict[str, Any] = Field(
        default_factory=dict, description="Assistant configuration settings"
    )
    status: Optional[str] = Field(
        None, description="Assistant status (active, inactive, setup_pending)"
    )


class AssistantCreate(AssistantBase):
    """Schema for creating a new assistant."""

    pass


class AssistantUpdate(BaseModel):
    """Schema for updating an existing assistant."""

    name: Optional[str] = Field(
        None, min_length=1, max_length=100, description="Assistant name"
    )
    backstory: Optional[str] = Field(
        None, min_length=1, max_length=2000, description="Assistant backstory"
    )
    avatar_file_id: Optional[uuid.UUID] = Field(
        None, description="ID of the avatar file"
    )
    configuration: Optional[Dict[str, Any]] = Field(
        None, description="Assistant configuration settings"
    )
    status: Optional[str] = Field(
        None, description="Assistant status (active, inactive, setup_pending)"
    )


class AssistantRead(AssistantBase):
    """Schema for reading assistant data."""

    id: uuid.UUID = Field(..., description="Assistant ID")
    user_id: uuid.UUID = Field(..., description="Owner user ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    model_config = ConfigDict(from_attributes=True)


class AssistantResponse(BaseModel):
    """Response schema for assistant operations."""

    assistant: AssistantRead
    success: bool = True
    message: str = "Operation completed successfully"


class AssistantListResponse(BaseModel):
    """Response schema for listing assistants."""

    assistants: list[AssistantRead]
    total: int
    success: bool = True
    message: str = "Assistants retrieved successfully"
