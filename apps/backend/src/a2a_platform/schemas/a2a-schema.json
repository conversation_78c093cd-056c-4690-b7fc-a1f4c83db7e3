{"$schema": "http://json-schema.org/draft-07/schema#", "title": "VEDAVIVI A2A Message Schema", "description": "Schema for VEDAVIVI internal Agent-to-Agent (A2A) communication messages", "type": "object", "properties": {"user_context": {"type": "object", "description": "User and initiating agent context for internal A2A communication. Designed to be potentially embeddable in broader A2A protocols in the future.", "properties": {"user_id": {"type": "string", "description": "The unique identifier of the end-user."}, "initiating_agent_id": {"type": "string", "description": "The identifier of the VEDAVIVI agent initiating the request."}, "request_timestamp": {"type": "string", "format": "date-time", "description": "Timestamp of when the context was generated for the request."}}, "required": ["user_id", "initiating_agent_id", "request_timestamp"]}, "payload": {"type": "object", "description": "The actual payload of the A2A message or request."}}, "required": ["user_context", "payload"]}