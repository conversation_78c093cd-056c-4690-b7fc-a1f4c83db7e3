"""
Relay-style cursor pagination schemas for GraphQL.

This module provides generic pagination types following the Relay connection specification:
https://relay.dev/graphql/connections.htm
"""

import strawberry
from typing import Generic, List, Optional, TypeVar


@strawberry.type
class PageInfo:
    """
    Information about pagination in a connection.

    Follows the Relay PageInfo specification.
    """

    has_next_page: bool = strawberry.field(
        description="Whether there are more edges available"
    )
    has_previous_page: bool = strawberry.field(
        description="Whether there are edges before the current set"
    )
    start_cursor: Optional[str] = strawberry.field(
        default=None, description="Cursor of the first edge in this connection"
    )
    end_cursor: Optional[str] = strawberry.field(
        default=None, description="Cursor of the last edge in this connection"
    )


# Generic type variable for node types
T = TypeVar("T")


@strawberry.type
class Edge(Generic[T]):
    """
    An edge in a connection containing a node and its cursor.

    Generic type that can be specialized for specific node types.
    """

    node: T = strawberry.field(description="The item at the end of the edge")
    cursor: str = strawberry.field(description="Cursor for this edge")


@strawberry.type
class Connection(Generic[T]):
    """
    A connection to a list of items following the Relay connection specification.

    Generic type that can be specialized for specific node types.
    """

    edges: List[Edge[T]] = strawberry.field(
        description="A list of edges containing nodes and cursors"
    )
    page_info: PageInfo = strawberry.field(
        description="Information about pagination in this connection"
    )
    total_count: Optional[int] = strawberry.field(
        default=None, description="Total number of nodes in the connection (optional)"
    )


@strawberry.input
class ConnectionArgs:
    """
    Standard Relay connection arguments for pagination.
    """

    first: Optional[int] = strawberry.field(
        default=None, description="Number of items to fetch from the beginning"
    )
    after: Optional[str] = strawberry.field(
        default=None, description="Cursor to start fetching items after"
    )
    last: Optional[int] = strawberry.field(
        default=None, description="Number of items to fetch from the end"
    )
    before: Optional[str] = strawberry.field(
        default=None, description="Cursor to stop fetching items before"
    )
