from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field, field_validator, model_validator


class TaskBase(BaseModel):
    """Base schema for task data."""

    description: str
    objective_id: Optional[UUID] = None
    parent_task_id: Optional[UUID] = None
    depends_on_task_id: Optional[UUID] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class TaskCreate(TaskBase):
    """Schema for creating a new task."""

    initial_status: Optional[str] = Field(
        default="todo",
        description="Initial status of the task",
    )

    @field_validator("initial_status")
    @classmethod
    def validate_status(cls, v: str) -> str:
        valid_statuses = ["todo", "pending_dependency", "pending_human_input"]
        if v not in valid_statuses:
            raise ValueError(f"Status must be one of {valid_statuses}")
        return v

    @model_validator(mode="after")
    def validate_dependency_status(self) -> "TaskCreate":
        if self.depends_on_task_id is not None and self.initial_status == "todo":
            # If a dependency is specified, default status should be pending_dependency
            self.initial_status = "pending_dependency"
        return self


class TaskUpdate(BaseModel):
    """Schema for updating an existing task."""

    description: Optional[str] = None
    status: Optional[str] = None
    last_progress_at: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None

    @field_validator("status")
    @classmethod
    def validate_status(cls, v: Optional[str]) -> Optional[str]:
        if v is None:
            return v

        valid_statuses = [
            "todo",
            "leased",
            "in_progress",
            "pending_dependency",
            "pending_human_input",
            "pending_user_clarification",
            "pending_admin_review",
            "done",
            "cancelled",
            "retrying",
            "failed_timeout",
            "failed_max_retries",
            "failed_human_timeout",
            "quarantined",
            "escalated",
        ]
        if v not in valid_statuses:
            raise ValueError(f"Status must be one of {valid_statuses}")
        return v


class TaskRead(TaskBase):
    """Schema for reading a task."""

    id: UUID
    assistant_id: UUID
    status: str
    retry_count: int
    due_date: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    last_progress_at: Optional[datetime] = None
    lease_owner_id: Optional[str] = None
    lease_acquired_at: Optional[datetime] = None
    lease_expires_at: Optional[datetime] = None
    idempotency_key: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)

    @classmethod
    def model_validate(
        cls,
        obj: Any,
        *,
        strict: bool | None = None,
        from_attributes: bool | None = None,
        context: Any | None = None,
        by_alias: bool | None = None,
        by_name: bool | None = None,
    ) -> "TaskRead":
        # If we have a model instance, create a dict copy with correct field mapping
        if hasattr(obj, "__table__"):
            # Create a dict from the model
            data: Dict[str, Any] = {}
            for key, value in obj.__dict__.items():
                if not key.startswith("_"):
                    # Rename metadata_json to metadata for the schema
                    if key == "metadata_json":
                        data["metadata"] = value
                    else:
                        data[key] = value
            # Ensure idempotency_key is present (even if None)
            if "idempotency_key" not in data:
                data["idempotency_key"] = None
            return super().model_validate(
                data,
                strict=strict,
                from_attributes=from_attributes,
                context=context,
                by_alias=by_alias,
                by_name=by_name,
            )
        return super().model_validate(
            obj,
            strict=strict,
            from_attributes=from_attributes,
            context=context,
            by_alias=by_alias,
            by_name=by_name,
        )


class TaskListResponse(BaseModel):
    """Schema for paginated list of tasks."""

    items: List[TaskRead]
    total: int
