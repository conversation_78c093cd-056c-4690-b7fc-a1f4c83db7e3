import uuid
from datetime import datetime
from typing import Any, Dict, Optional

from pydantic import BaseModel, Field


class ObjectiveBase(BaseModel):
    """Base schema for assistant objectives with common fields."""

    objective_text: str = Field(
        ..., description="The textual description of the objective."
    )
    status: str = Field(
        default="active",
        description="Current status of the objective (active, completed, cancelled).",
    )
    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=lambda: {},
        description="Flexible storage for any future objective metadata.",
    )


class ObjectiveCreate(ObjectiveBase):
    """Schema for creating a new assistant objective."""

    assistant_id: uuid.UUID = Field(
        ..., description="The ID of the assistant this objective belongs to."
    )


class ObjectiveUpdate(BaseModel):
    """Schema for updating an existing assistant objective."""

    objective_text: Optional[str] = Field(
        None, description="The textual description of the objective."
    )
    status: Optional[str] = Field(
        None,
        description="Current status of the objective (active, completed, cancelled).",
    )
    completed_at: Optional[datetime] = Field(
        None, description="Timestamp when the objective was completed."
    )
    metadata: Optional[Dict[str, Any]] = Field(
        None, description="Flexible storage for any future objective metadata."
    )


class ObjectiveRead(ObjectiveBase):
    """Schema for reading an assistant objective."""

    id: uuid.UUID = Field(..., description="Unique identifier for the objective.")
    assistant_id: uuid.UUID = Field(
        ..., description="The ID of the assistant this objective belongs to."
    )
    created_at: datetime = Field(
        ..., description="Timestamp when the objective was created."
    )
    updated_at: datetime = Field(
        ..., description="Timestamp when the objective was last updated."
    )
    completed_at: Optional[datetime] = Field(
        None, description="Timestamp when the objective was completed."
    )

    class Config:
        from_attributes = True
