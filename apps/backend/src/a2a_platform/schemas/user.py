import uuid
from typing import Optional

import strawberry
from pydantic import BaseModel, EmailStr
from strawberry.scalars import JSON


@strawberry.type
class User:
    id: uuid.UUID
    clerk_user_id: str
    email: str
    timezone: Optional[str] = None
    is_pa_setup_complete: bool = False
    created_at: str  # ISO8601 timestamp, e.g. '2024-01-01T00:00:00Z'
    updated_at: str  # ISO8601 timestamp, e.g. '2024-01-01T00:00:00Z'

    # We don't expose preferences_data directly as a field
    # Instead, we'll access it through the preferences method

    # Define preferences as a method that returns a JSON scalar
    # This allows us to handle the complex dictionary type in GraphQL
    @strawberry.field
    def preferences(self) -> JSON:
        # Access the preferences_data attribute from the database model
        # If it doesn't exist or is None, return an empty dict
        return getattr(self, "_preferences_data", {}) or {}


class UserCreate(BaseModel):
    clerk_user_id: str
    email: EmailStr
    timezone: Optional[str] = None


class UserUpdate(BaseModel):
    timezone: Optional[str] = None
