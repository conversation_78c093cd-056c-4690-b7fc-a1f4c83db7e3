"""
Redis Queue (RQ) worker script.

This script starts RQ workers to process jobs from Redis queues.
"""

import logging
import os
import signal
import sys
import time
from typing import Any, Callable, Dict, List, Optional, Protocol, TypedDict, cast

# Health Check Server Imports
import threading
from http.server import BaseHTTPRequestHandler, HTTPServer

import redis
from rq import Queue, Worker
from rq.job import Job

# Set up basic logging early
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


# Note: We directly use the Worker class with explicit connection parameters
# rather than relying on the Connection context manager from RQ


# Define a Protocol for task functions
class TaskFunction(Protocol):
    async def __call__(self, message_dict: Dict[str, Any]) -> bool: ...


# Define a class to extend Worker with our custom method
class ExtendedWorker(Worker):
    def register_task_function(self, name: str, func: TaskFunction) -> None:
        """
        Register a task function that can be called by the worker.

        Args:
            name: The name of the task function
            func: The task function to register
        """
        logger.debug(f"Registered task function: {name}")
        # In a real implementation, we would store the function for later use
        # For now, we just log it
        # Use parameters to avoid unused variable warnings
        _ = func


# Add the register_task_function method to the Worker class if it doesn't exist
# This is a monkey patch to add the functionality we need
# We do this early in the module to ensure it's available for all imports
if not hasattr(Worker, "register_task_function"):
    logger.debug("Adding register_task_function method to Worker class")

    # Define the method with proper type annotations
    def _register_task_function(self: Worker, name: str, func: TaskFunction) -> None:
        """
        Register a task function that can be called by the worker.

        Args:
            self: The Worker instance
            name: The name of the task function
            func: The task function to register
        """
        logger.debug(f"Registered task function: {name}")
        # Use parameters to avoid unused variable warnings
        _ = self
        _ = func

    # Add the method to the Worker class
    # We're monkey patching the Worker class at runtime
    setattr(Worker, "register_task_function", _register_task_function)


# Import settings after monkey patching
from a2a_platform.config.settings import get_settings  # noqa: E402

# Get settings
settings = get_settings()


# Health Check Server
HEALTH_CHECK_PORT = int(
    os.getenv("PORT", "8081")
)  # Use PORT env var from Cloud Run, default to 8081


class HealthCheckHandler(BaseHTTPRequestHandler):
    def do_GET(self) -> None:
        if self.path == "/health":
            self.send_response(200)
            self.send_header("Content-type", "application/json")
            self.end_headers()
            self.wfile.write(b'{"status": "OK"}')
        else:
            self.send_response(404)
            self.end_headers()


def start_health_check_server() -> None:
    # Bind to 0.0.0.0 to be accessible from outside the container via Cloud Run\'s port mapping.
    server_address = ("0.0.0.0", HEALTH_CHECK_PORT)  # nosec B104
    httpd = HTTPServer(server_address, HealthCheckHandler)
    logger.info(f"Starting health check server on port {HEALTH_CHECK_PORT}...")
    httpd.serve_forever()


# Define TypedDict for metrics to help with type checking
class MetricsDict(TypedDict):
    processed_count: int
    failed_count: int
    retry_count: int
    processing_times: List[float]
    queue_lengths: Dict[str, int]
    last_metrics_report: float
    report_interval: int


# Metrics dictionary to track worker performance
metrics: MetricsDict = {
    "processed_count": 0,
    "failed_count": 0,
    "retry_count": 0,
    "processing_times": [],  # List of processing times in seconds
    "queue_lengths": {},  # Dictionary of queue lengths by queue name
    "last_metrics_report": 0.0,  # Initialize to 0 to force an immediate report
    "report_interval": 5,  # Report metrics every 5 seconds for testing
}


def report_metrics() -> None:
    """
    Report metrics about worker performance.

    This function logs metrics about worker performance, including:
    - Number of messages processed
    - Number of messages failed
    - Number of messages retried
    - Average processing time
    - Queue lengths

    It only reports metrics at the specified interval to avoid flooding the logs.
    """
    current_time = time.time()

    # Only report metrics at the specified interval
    if current_time - metrics["last_metrics_report"] < metrics["report_interval"]:
        return

    # Update the last report time
    metrics["last_metrics_report"] = current_time

    # Calculate average processing time
    avg_processing_time = 0.0
    if metrics["processing_times"]:
        avg_processing_time = sum(metrics["processing_times"]) / len(
            metrics["processing_times"]
        )

    # Log metrics
    logger.info(
        f"Worker Metrics: "
        f"Processed: {metrics['processed_count']}, "
        f"Failed: {metrics['failed_count']}, "
        f"Retried: {metrics['retry_count']}, "
        f"Avg Processing Time: {avg_processing_time:.4f}s"
    )

    # Log queue lengths
    if metrics["queue_lengths"]:
        queue_lengths_str = ", ".join(
            f"{queue}: {length}" for queue, length in metrics["queue_lengths"].items()
        )
        logger.info(f"Queue Lengths: {queue_lengths_str}")


def update_queue_lengths(redis_conn: redis.Redis, queue_names: List[str]) -> None:
    """
    Update queue length metrics.

    Args:
        redis_conn: Redis connection
        queue_names: List of queue names to check
    """
    for queue_name in queue_names:
        queue = Queue(name=queue_name, connection=redis_conn)
        metrics["queue_lengths"][queue_name] = len(queue.get_job_ids())


async def process_message(message_dict: Dict[str, Any]) -> bool:
    """
    Process a message from an RQ queue.

    This function is called by RQ workers to process jobs.
    It's a placeholder that will be replaced by the actual handler
    when the job is enqueued.

    Args:
        message_dict: The message dictionary

    Returns:
        bool: True if the message was processed successfully, False otherwise
    """
    start_time = time.time()
    message_id = message_dict.get("message_id", "unknown")

    try:
        logger.info(f"Processing message: {message_id}")

        # Simulate some processing
        # In a real implementation, this would be replaced with actual processing logic

        # Update metrics
        metrics["processed_count"] += 1
        processing_time = time.time() - start_time
        metrics["processing_times"].append(processing_time)

        # Keep only the last 100 processing times to avoid memory issues
        if len(metrics["processing_times"]) > 100:
            metrics["processing_times"] = metrics["processing_times"][-100:]

        # Report metrics periodically
        report_metrics()

        return True
    except Exception as e:
        # Update failure metrics
        metrics["failed_count"] += 1
        logger.error(f"Error processing message {message_id}: {str(e)}")

        # Report metrics on failure
        report_metrics()

        # Re-raise the exception to let RQ handle it
        raise


def setup_worker(
    queues: Optional[List[str]] = None, redis_url: Optional[str] = None
) -> Worker:
    """
    Set up an RQ worker.

    Args:
        queues: List of queue names to listen to
        redis_url: Redis URL

    Returns:
        Worker: The RQ worker
    """
    redis_url = redis_url or settings.REDIS_URL
    queues = queues or ["default"]

    # Connect to Redis
    redis_conn = redis.from_url(redis_url)  # type: ignore

    # Create queues
    queue_instances = [
        Queue(name=queue_name, connection=redis_conn) for queue_name in queues
    ]

    # Create worker
    worker = Worker(
        queues=queue_instances,
        connection=redis_conn,
        name=f"worker-{os.getpid()}",
    )

    # Register the process_message function with its fully qualified name
    # Cast to ExtendedWorker to satisfy type checking
    extended_worker = cast(ExtendedWorker, worker)
    extended_worker.register_task_function(
        "a2a_platform.workers.rq_worker.process_message", process_message
    )

    return worker


def handle_signals() -> None:
    """
    Set up signal handlers for graceful shutdown.
    """

    def signal_handler(sig: int, _: Any) -> None:
        """
        Handle signals for graceful shutdown.

        Args:
            sig: Signal number
            _: Frame object (unused)
        """
        logger.info(f"Received signal {sig}, shutting down...")
        sys.exit(0)

    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


def main(queues_to_listen: Optional[List[str]] = None) -> None:
    """
    Start RQ workers.
    """
    # Start health check server in a separate thread
    health_thread = threading.Thread(target=start_health_check_server, daemon=True)
    health_thread.start()

    redis_url = settings.REDIS_URL
    queues = queues_to_listen or ["default"]

    logger.info(f"Starting workers for queues: {', '.join(queues)}")

    # Connect to Redis
    redis_conn = redis.from_url(redis_url)  # type: ignore

    # Create queue instances
    queue_instances = [
        Queue(name=queue_name, connection=redis_conn) for queue_name in queues
    ]

    # Start workers
    workers = []
    for i in range(settings.RQ_WORKER_COUNT):
        # Create worker with our custom Worker class
        worker = Worker(
            queues=queue_instances,  # Pass Queue instances, not queue names
            connection=redis_conn,  # Pass the Redis connection
            name=f"worker-{i}",
        )
        # Register the process_message function with its fully qualified name
        # Cast to ExtendedWorker to satisfy type checking
        extended_worker = cast(ExtendedWorker, worker)
        extended_worker.register_task_function(
            "a2a_platform.workers.rq_worker.process_message", process_message
        )
        workers.append(worker)

    # Update queue lengths before starting workers
    update_queue_lengths(redis_conn, queues)

    # Start workers
    for worker in workers:
        # Set up a pre-fork handler to update queue lengths
        def update_metrics_handler(job: Job, queue: Queue) -> None:
            # Update queue lengths before processing each job
            update_queue_lengths(redis_conn, queues)

            # Log job information
            logger.debug(f"Processing job {job.id} from queue {queue.name}")

        # Set the pre-fork handler
        # This is a method from the Worker class that takes a callable
        # Using type ignore because push_exc_handler is an untyped function in the RQ library
        worker.push_exc_handler(  # type: ignore
            cast(Callable[[Job, Queue], None], update_metrics_handler)
        )

        # Start the worker
        worker.work(with_scheduler=True)


if __name__ == "__main__":
    main()
