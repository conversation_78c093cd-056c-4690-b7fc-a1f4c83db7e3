"""
Asynchronous worker tasks for processing Clerk webhook events.

These workers handle the processing of Clerk webhook events in an asynchronous manner,
ensuring idempotency and proper error handling.
"""

import asyncio
import logging
from typing import Any, Dict, Protocol, cast

import redis.asyncio as redis

from a2a_platform.config.settings import get_settings
from a2a_platform.db import get_session_factory
from a2a_platform.schemas.clerk_events import UserCreatedData, UserDeletedData
from a2a_platform.services.user_service import (
    create_user_from_clerk_event,
    delete_all_user_data,
)


# Define a Protocol for the task functions
class TaskFunc(Protocol):
    async def __call__(
        self, _: Any, clerk_event_id: str, event_data_dict: Dict[str, Any]
    ) -> bool: ...


# Set up logger
logger = logging.getLogger(__name__)

# Get settings
settings = get_settings()

# Redis client for idempotency checks
try:
    # Use ignore_type to suppress the no-untyped-call error
    redis_client = cast(redis.Redis, redis.from_url(settings.REDIS_URL))  # type: ignore
except Exception as e:
    logger.error(f"Failed to initialize Redis client: {str(e)}", exc_info=True)
    redis_client = cast(redis.Redis, None)

# Constants
IDEMPOTENCY_KEY_PREFIX = "clerk_event_processed:"
IDEMPOTENCY_TTL = 60 * 60 * 24 * 3  # 3 days in seconds


async def check_idempotency(clerk_event_id: str) -> bool:
    """
    Check if an event has already been processed.

    Args:
        clerk_event_id: The Clerk event ID to check

    Returns:
        bool: True if the event has already been processed, False otherwise
    """
    # Redis client should always be available in production
    assert redis_client is not None, "Redis client not initialized"

    key = f"{IDEMPOTENCY_KEY_PREFIX}{clerk_event_id}"
    try:
        result = await redis_client.get(key)
        return result is not None
    except Exception as e:
        logger.error(f"Error checking idempotency: {str(e)}", exc_info=True)
        return False


async def mark_as_processed(clerk_event_id: str) -> None:
    """
    Mark an event as processed to prevent duplicate processing.

    Args:
        clerk_event_id: The Clerk event ID to mark as processed
    """
    # Redis client should always be available in production
    assert redis_client is not None, "Redis client not initialized"

    key = f"{IDEMPOTENCY_KEY_PREFIX}{clerk_event_id}"
    try:
        await redis_client.set(key, "1", ex=IDEMPOTENCY_TTL)
        logger.info(
            f"Marked event {clerk_event_id} as processed (TTL: {IDEMPOTENCY_TTL}s)"
        )
    except Exception as e:
        logger.error(f"Error marking event as processed: {str(e)}", exc_info=True)


class ProcessingError(Exception):
    """Error during event processing that should trigger a retry."""

    pass


class TaskWrapper:
    """
    A wrapper class to simulate a task queue.

    This class provides a delay method that simulates the behavior of a task queue
    by running the task in a separate thread.
    """

    def __init__(self, func: TaskFunc) -> None:
        self.func = func

    def delay(self, *args: Any, **kwargs: Any) -> bool:
        """
        Simulate the delay method of a task queue by running the task in the background.

        In a real implementation, this would enqueue the task in a message queue.

        Returns:
            bool: True if the task was successfully enqueued
        """
        # Create a task to run the function in the background
        asyncio.create_task(self.func(*args, **kwargs))
        return True


async def process_user_created_task(
    # _ctx is unused but kept for API compatibility with task queue systems
    _: Any,
    clerk_event_id: str,
    event_data_dict: Dict[str, Any],
) -> bool:
    """
    Process a user.created event from Clerk.

    This worker task:
    1. Verifies the event hasn't already been processed (idempotency)
    2. Deserializes the event data
    3. Creates a user record in the database

    Args:
        ctx: The worker context (task queue specific)
        clerk_event_id: The ID of the Clerk event
        event_data_dict: The event data as a dictionary

    Returns:
        bool: True if the event was processed successfully, False otherwise
    """
    logger.info(f"Processing user.created event {clerk_event_id}")

    # Check idempotency
    if await check_idempotency(clerk_event_id):
        logger.info(f"Event {clerk_event_id} has already been processed, skipping")
        return True

    try:
        # OPTIMIZATION 3: Full Pydantic validation now happens in the worker
        # This was previously done in the webhook handler
        try:
            # Deserialize event data
            user_data = UserCreatedData.model_validate(event_data_dict)
        except ValueError as validation_error:
            logger.error(
                f"Validation error for event {clerk_event_id}: {str(validation_error)}"
            )
            # Mark as processed to avoid retries for validation errors
            await mark_as_processed(clerk_event_id)
            return False

        # Create a database session using the lazy session factory
        session_factory = get_session_factory()
        async with session_factory() as db_session:
            # Call service function to create the user
            user = await create_user_from_clerk_event(db_session, user_data)

            if user is None:
                logger.error(f"Failed to create user for event {clerk_event_id}")
                return False

            # Mark as processed after successful execution
            await mark_as_processed(clerk_event_id)

            logger.info(f"Successfully processed user.created event {clerk_event_id}")
            return True

    except ValueError as e:
        # Data validation errors - non-retryable
        logger.error(
            f"Data validation error processing event {clerk_event_id}: {str(e)}"
        )
        # We mark it as processed to avoid retries for validation errors
        await mark_as_processed(clerk_event_id)
        return False

    except Exception as e:
        # Other errors - potentially retryable
        logger.error(
            f"Error processing user.created event {clerk_event_id}: {str(e)}",
            exc_info=True,
        )
        # Re-raise as a ProcessingError to trigger retry if supported by the task queue
        raise ProcessingError(f"Failed to process user.created: {str(e)}") from e


async def process_user_deleted_task(
    # _ctx is unused but kept for API compatibility with task queue systems
    _: Any,
    clerk_event_id: str,
    event_data_dict: Dict[str, Any],
) -> bool:
    """
    Process a user.deleted event from Clerk.

    This worker task:
    1. Verifies the event hasn't already been processed (idempotency)
    2. Deserializes the event data
    3. Deletes all associated user data from the database

    Args:
        ctx: The worker context (task queue specific)
        clerk_event_id: The ID of the Clerk event
        event_data_dict: The event data as a dictionary

    Returns:
        bool: True if the event was processed successfully, False otherwise
    """
    logger.info(f"Processing user.deleted event {clerk_event_id}")

    # Check idempotency
    if await check_idempotency(clerk_event_id):
        logger.info(f"Event {clerk_event_id} has already been processed, skipping")
        return True

    try:
        # OPTIMIZATION 3: Full Pydantic validation now happens in the worker
        # This was previously done in the webhook handler
        try:
            # Deserialize event data
            user_data = UserDeletedData.model_validate(event_data_dict)
        except ValueError as validation_error:
            logger.error(
                f"Validation error for event {clerk_event_id}: {str(validation_error)}"
            )
            # Mark as processed to avoid retries for validation errors
            await mark_as_processed(clerk_event_id)
            return False

        # Extract clerk_user_id
        clerk_user_id = user_data.id

        # Create a database session using the lazy session factory
        session_factory = get_session_factory()
        async with session_factory() as db_session:
            # Call service function to delete all user data
            success = await delete_all_user_data(db_session, clerk_user_id)

            if success:
                logger.info(
                    f"Successfully deleted data for user with clerk_user_id {clerk_user_id}"
                )
            else:
                logger.warning(
                    f"No user found with clerk_user_id {clerk_user_id}, nothing to delete"
                )

            # Mark as processed after execution (even if user not found)
            await mark_as_processed(clerk_event_id)

            logger.info(f"Successfully processed user.deleted event {clerk_event_id}")
            return True

    except ValueError as e:
        # Data validation errors - non-retryable
        logger.error(
            f"Data validation error processing event {clerk_event_id}: {str(e)}"
        )
        # We mark it as processed to avoid retries for validation errors
        await mark_as_processed(clerk_event_id)
        return False

    except Exception as e:
        # Other errors - potentially retryable
        logger.error(
            f"Error processing user.deleted event {clerk_event_id}: {str(e)}",
            exc_info=True,
        )
        # Re-raise as a ProcessingError to trigger retry if supported by the task queue
        raise ProcessingError(f"Failed to process user.deleted: {str(e)}") from e


# Create task wrappers for the worker functions
# We need to use different variable names to avoid overwriting the original functions
process_user_created_task_func = process_user_created_task
process_user_deleted_task_func = process_user_deleted_task

# Create the wrapped versions with explicit type annotations
# OPTIMIZATION 1: In a production environment, this would be replaced with a task queue client
# such as Celery, ARQ, or a cloud-native solution like Google Cloud Tasks or AWS SQS
process_user_created_task_wrapper = TaskWrapper(process_user_created_task_func)
process_user_deleted_task_wrapper = TaskWrapper(process_user_deleted_task_func)

# Replace the original functions with the wrapped versions
# Use explicit type annotations to avoid type errors
process_user_created_task = cast(Any, process_user_created_task_wrapper)
process_user_deleted_task = cast(Any, process_user_deleted_task_wrapper)
