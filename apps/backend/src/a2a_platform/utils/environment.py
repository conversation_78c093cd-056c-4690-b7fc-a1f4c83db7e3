"""
Environment detection utilities.

This module provides utilities for detecting the current runtime environment
to enable environment-specific behavior and configuration.
"""

import os
from typing import Optional


def is_cloud_run_environment() -> bool:
    """
    Detect if the application is running in a Google Cloud Run environment.

    This function checks for environment variables that are set by Cloud Run
    and other GCP services to determine if we're running in a cloud environment.

    Returns:
        bool: True if running in Cloud Run or GCP environment, False otherwise

    Environment variables checked:
        - K_SERVICE: Set by Cloud Run to the service name
        - GOOGLE_CLOUD_PROJECT: Set by GCP to the project ID
    """
    return (
        os.environ.get("K_SERVICE", "") != ""
        or os.environ.get("GOOGLE_CLOUD_PROJECT", "") != ""
    )


def is_docker_environment() -> bool:
    """
    Detect if the application is running in a Docker container.

    Returns:
        bool: True if running in Docker, False otherwise

    Detection methods:
        - Check for /.dockerenv file (created by <PERSON><PERSON>)
        - Check DOCKER_ENVIRONMENT environment variable
    """
    return (
        os.path.exists("/.dockerenv")  # <PERSON>er creates this file
        or os.environ.get("DOCKER_ENVIRONMENT", "").lower() == "true"
    )


def is_ci_environment() -> bool:
    """
    Detect if the application is running in a CI/CD environment.

    Returns:
        bool: True if running in CI, False otherwise

    Environment variables checked:
        - CI: Generic CI environment indicator
        - GITHUB_ACTIONS: GitHub Actions specific indicator
    """
    return (
        os.environ.get("CI", "").lower() == "true"
        or os.environ.get("GITHUB_ACTIONS", "").lower() == "true"
    )


def get_environment_type() -> str:
    """
    Get a string representation of the current environment type.

    Returns:
        str: One of 'cloud_run', 'docker', 'ci', or 'local'

    Priority order:
        1. Cloud Run (highest priority)
        2. CI environment
        3. Docker environment
        4. Local development (default)
    """
    if is_cloud_run_environment():
        return "cloud_run"
    elif is_ci_environment():
        return "ci"
    elif is_docker_environment():
        return "docker"
    else:
        return "local"


def should_use_cloud_sql_proxy(db_url: Optional[str] = None) -> bool:
    """
    Determine if Cloud SQL Auth Proxy should be used for database connections.

    Args:
        db_url: Optional database URL to check for proxy indicators

    Returns:
        bool: True if Cloud SQL proxy should be used, False otherwise

    Indicators for Cloud SQL proxy:
        - Running in Cloud Run environment
        - Database URL contains 127.0.0.1 (proxy address)
        - Database URL contains /cloudsql/ (Unix socket path)
    """
    if is_cloud_run_environment():
        return True

    if db_url:
        return (
            "127.0.0.1" in db_url  # Cloud SQL Auth Proxy uses 127.0.0.1
            or "/cloudsql/" in db_url  # Unix socket path
        )

    return False
