"""
Cursor pagination utilities for encoding and decoding cursor data.

This module provides utilities for handling Relay-style cursor pagination
using composite cursors based on timestamp + id for stable and efficient pagination.
"""

import base64
import json
import uuid
from datetime import datetime
from typing import Any, Dict, Optional, Tuple

import logging

logger = logging.getLogger(__name__)


class CursorPaginationError(Exception):
    """Raised when cursor pagination operations fail."""

    pass


class CursorUtils:
    """Utility class for cursor encoding and decoding operations."""

    @staticmethod
    def encode_cursor(timestamp: datetime, message_id: uuid.UUID) -> str:
        """
        Encode a cursor from timestamp and message ID.

        Args:
            timestamp: Message timestamp for primary ordering
            message_id: Message UUID for tie-breaking

        Returns:
            Base64-encoded cursor string

        Raises:
            CursorPaginationError: If encoding fails
        """
        try:
            cursor_data = {"timestamp": timestamp.isoformat(), "id": str(message_id)}
            json_str = json.dumps(cursor_data, sort_keys=True)
            encoded = base64.b64encode(json_str.encode("utf-8")).decode("ascii")
            return encoded
        except Exception as e:
            logger.error(f"Failed to encode cursor: {e}")
            raise CursorPaginationError(f"Failed to encode cursor: {e}")

    @staticmethod
    def decode_cursor(cursor: str) -> Tuple[datetime, uuid.UUID]:
        """
        Decode a cursor to timestamp and message ID.

        Args:
            cursor: Base64-encoded cursor string

        Returns:
            Tuple of (timestamp, message_id)

        Raises:
            CursorPaginationError: If cursor is invalid or corrupted
        """
        try:
            # Decode base64
            try:
                json_str = base64.b64decode(cursor.encode("ascii")).decode("utf-8")
            except Exception as e:
                raise CursorPaginationError(f"Invalid base64 encoding: {e}")

            # Parse JSON
            try:
                cursor_data = json.loads(json_str)
            except json.JSONDecodeError as e:
                raise CursorPaginationError(f"Invalid JSON in cursor: {e}")

            # Validate required fields
            if not isinstance(cursor_data, dict):
                raise CursorPaginationError("Cursor data must be a JSON object")

            if "timestamp" not in cursor_data or "id" not in cursor_data:
                raise CursorPaginationError(
                    "Cursor missing required fields: timestamp, id"
                )

            # Parse timestamp
            try:
                timestamp = datetime.fromisoformat(cursor_data["timestamp"])
            except (ValueError, TypeError) as e:
                raise CursorPaginationError(f"Invalid timestamp format: {e}")

            # Parse UUID
            try:
                message_id = uuid.UUID(cursor_data["id"])
            except (ValueError, TypeError) as e:
                raise CursorPaginationError(f"Invalid UUID format: {e}")

            return timestamp, message_id

        except CursorPaginationError:
            # Re-raise cursor pagination errors as-is
            raise
        except Exception as e:
            logger.error(f"Unexpected error decoding cursor: {e}")
            raise CursorPaginationError(f"Failed to decode cursor: {e}")

    @staticmethod
    def validate_pagination_args(
        first: Optional[int] = None,
        after: Optional[str] = None,
        last: Optional[int] = None,
        before: Optional[str] = None,
    ) -> None:
        """
        Validate Relay-style pagination arguments.

        Args:
            first: Number of items to fetch forward
            after: Cursor to start after (for forward pagination)
            last: Number of items to fetch backward
            before: Cursor to end before (for backward pagination)

        Raises:
            CursorPaginationError: If arguments are invalid
        """
        # Validate that we have either forward or backward pagination, not both
        forward_pagination = first is not None or after is not None
        backward_pagination = last is not None or before is not None

        if forward_pagination and backward_pagination:
            raise CursorPaginationError(
                "Cannot use both forward (first/after) and backward (last/before) pagination"
            )

        # Validate first/last limits
        if first is not None:
            if not isinstance(first, int) or first < 1:
                raise CursorPaginationError("'first' must be a positive integer")
            if first > 100:  # Reasonable upper limit
                raise CursorPaginationError("'first' cannot exceed 100")

        if last is not None:
            if not isinstance(last, int) or last < 1:
                raise CursorPaginationError("'last' must be a positive integer")
            if last > 100:  # Reasonable upper limit
                raise CursorPaginationError("'last' cannot exceed 100")

        # Validate cursors if provided
        if after is not None:
            try:
                CursorUtils.decode_cursor(after)
            except CursorPaginationError as e:
                raise CursorPaginationError(f"Invalid 'after' cursor: {e}")

        if before is not None:
            try:
                CursorUtils.decode_cursor(before)
            except CursorPaginationError as e:
                raise CursorPaginationError(f"Invalid 'before' cursor: {e}")

    @staticmethod
    def get_default_pagination_args(
        first: Optional[int] = None,
        after: Optional[str] = None,
        last: Optional[int] = None,
        before: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Get normalized pagination arguments with defaults.

        Args:
            first: Number of items to fetch forward
            after: Cursor to start after
            last: Number of items to fetch backward
            before: Cursor to end before

        Returns:
            Dictionary with normalized pagination parameters
        """
        # Default to forward pagination if no args provided
        if all(arg is None for arg in [first, after, last, before]):
            return {
                "first": 20,
                "after": None,
                "last": None,
                "before": None,
                "is_forward": True,
            }

        # Determine pagination direction
        is_forward = first is not None or after is not None

        return {
            "first": first,
            "after": after,
            "last": last,
            "before": before,
            "is_forward": is_forward,
        }
