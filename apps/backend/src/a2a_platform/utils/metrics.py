"""
Metrics utilities for tracking application performance and behavior.

This module provides metrics tracking capabilities using a simple in-memory
implementation that can be replaced with a production-ready metrics system
like Prometheus, StatsD, or similar.
"""

import logging
import time
from contextlib import contextmanager
from typing import Any, Dict, Generator

logger = logging.getLogger(__name__)

# Simple in-memory counters for metrics
# In a production environment, these would be replaced with a proper metrics system
_counters: Dict[str, int] = {}
_histograms: Dict[str, Dict[str, float]] = {}


def webhook_counter(event_type: str, status: str = "received") -> None:
    """
    Increment a counter for webhook events.

    Args:
        event_type: The type of webhook event (e.g., "user.created", "user.deleted")
        status: The status of the event processing (e.g., "received", "processed", "error")
    """
    key = f"webhook.{event_type}.{status}"
    _counters[key] = _counters.get(key, 0) + 1
    logger.debug(f"Metric: {key} = {_counters[key]}")


def webhook_validation_error_counter(event_type: str) -> None:
    """
    Increment a counter for webhook validation errors.

    Args:
        event_type: The type of webhook event that had a validation error
    """
    key = f"webhook.{event_type}.validation_error"
    _counters[key] = _counters.get(key, 0) + 1
    logger.debug(f"Metric: {key} = {_counters[key]}")


@contextmanager
def webhook_processing_time(event_type: str) -> Generator[None, None, None]:
    """
    Context manager to measure webhook processing time.

    Args:
        event_type: The type of webhook event being processed

    Example:
        with webhook_processing_time("user.created"):
            # Process the webhook event
    """
    start_time = time.time()
    try:
        yield
    finally:
        duration = time.time() - start_time
        key = f"webhook.{event_type}.processing_time"
        if key not in _histograms:
            _histograms[key] = {
                "count": 0,
                "sum": 0,
                "min": float("inf"),
                "max": 0,
            }

        _histograms[key]["count"] += 1
        _histograms[key]["sum"] += duration
        _histograms[key]["min"] = min(_histograms[key]["min"], duration)
        _histograms[key]["max"] = max(_histograms[key]["max"], duration)

        logger.debug(
            f"Metric: {key} = {duration:.3f}s (avg: {_histograms[key]['sum'] / _histograms[key]['count']:.3f}s)"
        )


def get_metrics() -> Dict[str, Any]:
    """
    Get all collected metrics.

    Returns:
        Dict containing all counters and histograms
    """
    return {
        "counters": _counters.copy(),
        "histograms": _histograms.copy(),
    }


def reset_metrics() -> None:
    """Reset all metrics."""
    _counters.clear()
    _histograms.clear()
