"""
GraphQL and database enums for the A2A Platform.

This module defines all enum types used throughout the platform,
providing type safety and API documentation.
"""

from enum import Enum

import strawberry


@strawberry.enum
class AssistantStatus(Enum):
    """Status of a personal assistant."""

    ACTIVE = "active"
    INACTIVE = "inactive"
    SETUP_PENDING = "setup_pending"


@strawberry.enum
class TaskStatus(Enum):
    """Status of a task in the system."""

    TODO = "todo"
    LEASED = "leased"
    IN_PROGRESS = "in_progress"
    PENDING_DEPENDENCY = "pending_dependency"
    PENDING_HUMAN_INPUT = "pending_human_input"
    PENDING_USER_CLARIFICATION = "pending_user_clarification"
    PENDING_ADMIN_REVIEW = "pending_admin_review"
    DONE = "done"
    CANCELLED = "cancelled"
    RETRYING = "retrying"
    FAILED_TIMEOUT = "failed_timeout"
    FAILED_MAX_RETRIES = "failed_max_retries"
    FAILED_HUMAN_TIMEOUT = "failed_human_timeout"
    QUARANTINED = "quarantined"
    ESCALATED = "escalated"


@strawberry.enum
class AgentStatus(Enum):
    """Status of a registered agent."""

    ACTIVE = "active"
    INACTIVE = "inactive"


@strawberry.enum
class AgentReviewStatus(Enum):
    """Review status for marketplace agents."""

    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"


@strawberry.enum
class MessageSenderRole(Enum):
    """Role of the message sender in chat."""

    USER = "user"
    AGENT = "agent"


# Helper functions to get enum values as lists for database constraints
def get_assistant_status_values() -> list[str]:
    """Get all valid assistant status values."""
    return [status.value for status in AssistantStatus]


def get_task_status_values() -> list[str]:
    """Get all valid task status values."""
    return [status.value for status in TaskStatus]


def get_agent_status_values() -> list[str]:
    """Get all valid agent status values."""
    return [status.value for status in AgentStatus]


def get_agent_review_status_values() -> list[str]:
    """Get all valid agent review status values."""
    return [status.value for status in AgentReviewStatus]


def get_message_sender_role_values() -> list[str]:
    """Get all valid message sender role values."""
    return [role.value for role in MessageSenderRole]
