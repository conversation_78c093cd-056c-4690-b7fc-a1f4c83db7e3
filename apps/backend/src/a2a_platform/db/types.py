"""
Custom SQLAlchemy types for cross-database compatibility.
"""

from typing import Any

from sqlalchemy import <PERSON><PERSON><PERSON>, TypeDecorator
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.engine import Dialect


class PortableJSON(TypeDecorator[Any]):
    """
    A portable JSON type that uses JSONB for PostgreSQL and JSON for other
    databases.
    """

    impl = JSON
    cache_ok = True

    def load_dialect_impl(self, dialect: Dialect) -> Any:
        if dialect.name == "postgresql":
            return dialect.type_descriptor(JSONB(none_as_null=True))
        else:
            return dialect.type_descriptor(JSON())
