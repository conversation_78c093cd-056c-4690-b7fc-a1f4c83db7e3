import uuid
from datetime import UTC, datetime
from typing import Any, Dict, Optional

from sqlalchemy import (
    <PERSON>SO<PERSON>,
    UUID,
    CheckConstraint,
    DateTime,
    ForeignKey,
    Index,
    String,
    Text,
)
from sqlalchemy.orm import Mapped, mapped_column, relationship

from a2a_platform.db import Base


class AssistantObjective(Base):
    """
    SQLAlchemy model for the assistant_objectives table.
    Stores objectives defined by users via chat.
    """

    __tablename__ = "assistant_objectives"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    assistant_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("assistants.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    objective_text: Mapped[str] = mapped_column(Text, nullable=False)
    status: Mapped[str] = mapped_column(String(20), nullable=False, default="active")
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=lambda: datetime.now(UTC), nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=lambda: datetime.now(UTC),
        onupdate=lambda: datetime.now(UTC),
        nullable=False,
    )
    completed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True
    )
    metadata_json: Mapped[Dict[str, Any]] = mapped_column(
        JSON, nullable=True, default=dict
    )

    # Define relationships
    # TODO: Reintroduce the `assistant` relationship once the circular dependency issue is resolved.
    # Temporarily commenting out to avoid circular import issues
    # assistant = relationship("Assistant", back_populates="objectives")
    tasks = relationship("Task", back_populates="objective")

    # Define table constraints and indexes
    __table_args__ = (
        CheckConstraint(
            status.in_(["active", "completed", "cancelled"]),
            name="assistant_objectives_status_check",
        ),
        Index("idx_assistant_objectives_assistant_id_status", assistant_id, status),
    )

    def __repr__(self) -> str:
        return f"<AssistantObjective(id='{self.id}', assistant_id='{self.assistant_id}', status='{self.status}')>"
