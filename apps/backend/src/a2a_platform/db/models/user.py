import uuid
from datetime import UTC, datetime
from typing import TYPE_CHECKING, Any, Dict, List, Optional

from sqlalchemy import UUI<PERSON>, Boolean, DateTime, String, text
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.orm.attributes import flag_modified

from a2a_platform.db import Base
from a2a_platform.db.types import PortableJSO<PERSON>


if TYPE_CHECKING:
    from .assistant import Assistant
    from .cli_token import CliToken  # Added for type hinting
    from .conversation import Conversation


class User(Base):
    __tablename__ = "users"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    clerk_user_id: Mapped[str] = mapped_column(String(64), unique=True, index=True)
    email: Mapped[str] = mapped_column(String(255), unique=True, index=True)
    timezone: Mapped[Optional[str]] = mapped_column(
        String(64), nullable=True, server_default=text("'UTC'")
    )
    preferences: Mapped[Dict[str, Any]] = mapped_column(
        PortableJSON, nullable=True, server_default=text("'{}'"), default=dict
    )
    is_pa_setup_complete: Mapped[bool] = mapped_column(
        <PERSON><PERSON><PERSON>, nullable=False, default=False, server_default=text("false")
    )
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=lambda: datetime.now(UTC)
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=lambda: datetime.now(UTC),
        onupdate=lambda: datetime.now(UTC),
    )

    # Relationship to CliToken
    cli_tokens: Mapped[List["CliToken"]] = relationship(
        back_populates="user", cascade="all, delete-orphan"
    )

    # Relationship to Assistant (one-to-one)
    assistant: Mapped[Optional["Assistant"]] = relationship(
        back_populates="user", cascade="all, delete-orphan", uselist=False
    )

    # Relationship to Conversations
    conversations: Mapped[List["Conversation"]] = relationship(
        back_populates="user", cascade="all, delete-orphan"
    )

    """
    preferences: User preferences stored as JSONB. Defaults to empty dict.
    timezone: User's preferred timezone (IANA name, e.g.,
        'America/New_York'). Nullable.
    """

    def get_preference(self, key: str, default: Any = None) -> Any:
        """Gets a user preference value."""
        return self.preferences.get(key, default)

    def set_preference(self, key: str, value: Any) -> None:
        """Sets a user preference value."""
        self.preferences[key] = value
        flag_modified(self, "preferences")
