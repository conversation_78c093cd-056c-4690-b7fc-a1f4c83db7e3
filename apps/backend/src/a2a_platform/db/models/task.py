import uuid
from datetime import UTC, datetime
from typing import Any, Dict, Optional

from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    UUID,
    CheckConstraint,
    DateTime,
    ForeignKey,
    Index,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import Mapped, mapped_column, relationship

from a2a_platform.db import Base
from a2a_platform.db.enums import TaskStatus, get_task_status_values


class Task(Base):
    """
    SQLAlchemy model for the tasks table.
    Stores internal tasks for the Personal Assistant, including hierarchical tasks,
    dependencies, and status information.
    """

    __tablename__ = "tasks"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    assistant_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("assistants.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    objective_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("assistant_objectives.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
    )
    parent_task_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("tasks.id", ondelete="CASCADE"),
        nullable=True,
        index=True,
    )
    depends_on_task_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("tasks.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
    )
    idempotency_key: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, index=True
    )
    description: Mapped[str] = mapped_column(Text, nullable=False)
    status: Mapped[str] = mapped_column(
        String(30), nullable=False, default=TaskStatus.TODO.value, index=True
    )
    due_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True
    )
    completed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True
    )
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=lambda: datetime.now(UTC), nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=lambda: datetime.now(UTC),
        onupdate=lambda: datetime.now(UTC),
        nullable=False,
    )
    retry_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    last_progress_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True
    )
    lease_owner_id: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    lease_acquired_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True
    )
    lease_expires_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, index=True
    )
    metadata_json: Mapped[Dict[str, Any]] = mapped_column(
        JSON, nullable=True, default=dict
    )

    # Define relationships
    # TODO: Re-enable the `assistant` relationship after resolving the circular dependency.
    #       Issue reference: <INSERT_ISSUE_REFERENCE_HERE>
    # assistant = relationship("Assistant", back_populates="tasks")
    objective = relationship("AssistantObjective", back_populates="tasks")

    # Self-referential relationships for parent-child hierarchy
    parent_task = relationship(
        "Task",
        remote_side=[id],
        back_populates="child_tasks",
        foreign_keys=[parent_task_id],
    )
    child_tasks = relationship(
        "Task",
        back_populates="parent_task",
        foreign_keys=[parent_task_id],
        cascade="all, delete-orphan",
    )

    # Self-referential relationships for task dependencies
    depends_on_task = relationship(
        "Task",
        remote_side=[id],
        foreign_keys=[depends_on_task_id],
        backref="dependent_tasks",
    )

    # Define table constraints and indexes
    __table_args__ = (
        CheckConstraint(
            status.in_(get_task_status_values()),
            name="tasks_status_check",
        ),
        Index("idx_tasks_assistant_id_status", assistant_id, status),
        Index("idx_tasks_status_lease_expires", status, lease_expires_at),
        Index("idx_tasks_status_updated", status, updated_at),
        # This ensures idempotency keys are unique per assistant
        Index(
            "idx_tasks_assistant_idempotency",
            assistant_id,
            idempotency_key,
            unique=True,
        ),
    )

    def __repr__(self) -> str:
        return f"<Task(id='{self.id}', assistant_id='{self.assistant_id}', status='{self.status}')>"
