import uuid
from datetime import UTC, datetime
from typing import TYPE_CHECKING, List, Optional

from sqlalchemy import UUID, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from a2a_platform.db import Base

if TYPE_CHECKING:
    from .assistant import Assistant
    from .chat_message import ChatMessage
    from .user import User


class Conversation(Base):
    """
    SQLAlchemy model for the conversations table.
    Stores conversations between users and their personal assistants.
    """

    __tablename__ = "conversations"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    assistant_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("assistants.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=lambda: datetime.now(UTC),
        nullable=False,
    )
    last_message_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
    )

    # Define relationships
    user: Mapped["User"] = relationship("User", back_populates="conversations")
    assistant: Mapped["Assistant"] = relationship(
        "Assistant", back_populates="conversations"
    )
    messages: Mapped[List["ChatMessage"]] = relationship(
        "ChatMessage",
        back_populates="conversation",
        cascade="all, delete-orphan",
        order_by="ChatMessage.timestamp",
    )

    # Unique constraint to ensure one conversation per user-assistant pair
    __table_args__ = (
        UniqueConstraint(
            "user_id", "assistant_id", name="uq_user_assistant_conversation"
        ),
    )

    def __repr__(self) -> str:
        return (
            f"<Conversation(id='{self.id}', user_id='{self.user_id}', "
            f"assistant_id='{self.assistant_id}')>"
        )
