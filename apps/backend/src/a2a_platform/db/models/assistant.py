import uuid
from datetime import UTC, datetime
from typing import TYPE_CHECKING, Any, Dict, List, Optional

from sqlalchemy import UUID, DateTime, ForeignKey, String, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from a2a_platform.db import Base
from a2a_platform.db.enums import AssistantStatus
from a2a_platform.db.types import PortableJSON

if TYPE_CHECKING:
    from .conversation import Conversation
    from .user import User


class Assistant(Base):
    """
    SQLAlchemy model for the assistants table.
    Stores configuration for the user's single personal assistant.
    """

    __tablename__ = "assistants"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,  # Enforces one assistant per user
        index=True,
    )
    name: Mapped[str] = mapped_column(Text, nullable=False)
    backstory: Mapped[str] = mapped_column(Text, nullable=False)
    avatar_file_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True),
        # Note: file_metadata table doesn't exist yet, this will be uncommented
        # when it's created
        # ForeignKey("file_metadata.id", ondelete="SET NULL"),
        nullable=True,
    )
    configuration: Mapped[Dict[str, Any]] = mapped_column(
        PortableJSON, nullable=True, default=dict, server_default="{}"
    )
    status: Mapped[str] = mapped_column(
        String(20), nullable=False, default=AssistantStatus.SETUP_PENDING.value
    )
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=lambda: datetime.now(UTC),
        nullable=False,
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=lambda: datetime.now(UTC),
        onupdate=lambda: datetime.now(UTC),
        nullable=False,
    )

    # Define relationships
    user: Mapped["User"] = relationship("User", back_populates="assistant")
    conversations: Mapped[List["Conversation"]] = relationship(
        "Conversation", back_populates="assistant", cascade="all, delete-orphan"
    )
    # avatar_file: Mapped[Optional["FileMetadata"]] = relationship(
    #     "FileMetadata", foreign_keys=[avatar_file_id]
    # )
    # Note: Temporarily commenting out these relationships to avoid circular import issues
    # They will be added back once the basic functionality is working
    # objectives: Mapped[List["AssistantObjective"]] = relationship(
    #     "AssistantObjective",
    #     back_populates="assistant",
    #     cascade="all, delete-orphan",
    # )
    # tasks: Mapped[List["Task"]] = relationship(
    #     "Task", back_populates="assistant", cascade="all, delete-orphan"
    # )

    def __repr__(self) -> str:
        return (
            f"<Assistant(id='{self.id}', user_id='{self.user_id}', name='{self.name}')>"
        )
