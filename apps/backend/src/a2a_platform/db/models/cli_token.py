import datetime
import uuid
from typing import TYPE_CHECKING, Optional

from sqlalchemy import DateTime, ForeignKey, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .. import Base

if TYPE_CHECKING:
    from .user import User


class CliToken(Base):
    __tablename__ = "cli_tokens"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True
    )
    token_prefix: Mapped[str] = mapped_column(
        String(10), nullable=False
    )  # e.g., "vv_cli_"
    hashed_token: Mapped[str] = mapped_column(
        String(255), nullable=False
    )  # To store SHA-256 hash
    salt_hex: Mapped[str] = mapped_column(
        String(255), nullable=False
    )  # Salt stored as a hex-encoded string. Decode to bytes for validation (bytes.fromhex(salt_hex)).
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    created_at: Mapped[datetime.datetime] = mapped_column(
        DateTime, default=datetime.datetime.utcnow, nullable=False
    )
    last_used_at: Mapped[Optional[datetime.datetime]] = mapped_column(
        DateTime, nullable=True
    )
    expires_at: Mapped[Optional[datetime.datetime]] = mapped_column(
        DateTime, nullable=True
    )  # Optional for future use

    user: Mapped["User"] = relationship(back_populates="cli_tokens")

    def __repr__(self) -> str:
        desc_repr = (
            (
                self.description[:20] + "..."
                if len(self.description) > 20
                else self.description
            )
            if self.description
            else "None"
        )
        return f"<CliToken(id={self.id}, user_id={self.user_id}, description='{desc_repr}')>"
