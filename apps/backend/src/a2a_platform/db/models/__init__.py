# This file intentionally left empty to make the directory a package
# Import all model classes here for Alembic to discover them
# Import order matters for SQLAlchemy relationships
from .assistant import Assistant
from .assistant_objective import AssistantObjective
from .chat_message import ChatMessage
from .conversation import Conversation
from .registered_agent import RegisteredAgent
from .task import Task
from .user import User

__all__ = [
    "Assistant",
    "AssistantObjective",
    "ChatMessage",
    "Conversation",
    "RegisteredAgent",
    "Task",
    "User",
]
