from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, CheckConstraint, DateTime, Text, func
from sqlalchemy.orm import Mapped, mapped_column

# Import the central Base
from a2a_platform.db import Base  # Corrected import path
from a2a_platform.db.enums import AgentStatus, get_agent_status_values


class RegisteredAgent(Base):
    __tablename__ = "registered_agents"

    # As per ADR-002 and Low-Level Task 1
    agent_definition_id: Mapped[str] = mapped_column(
        Text,
        primary_key=True,
        index=True,
        comment="Unique identifier for the SA type/version (e.g., 'summarizer_v1').",
    )
    name: Mapped[str] = mapped_column(
        Text, nullable=False, comment="Human-readable name of the SA type."
    )
    description: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, comment="Description of the SA's purpose."
    )
    version: Mapped[str] = mapped_column(
        Text, nullable=False, comment="Version of this agent definition."
    )

    endpoint_url: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="Internal URL for synchronous HTTP calls (if supported).",
    )
    async_queue_name: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="Message queue name for asynchronous calls (if supported).",
    )

    # Using JSON type which maps to JSONB in PostgreSQL if the dialect supports it, otherwise JSON.
    # ADR-002 specifies JSONB for capabilities and skills.
    capabilities: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        default=dict,
        comment='Agent capabilities (e.g., { "streaming": true } based on A2A schema).',
    )
    skills: Mapped[Optional[List[Any]]] = mapped_column(
        JSON,
        nullable=True,
        default=list,
        comment="List of skills (based on AgentSkill structure from A2A schema).",
    )
    authentication_info: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        default=dict,
        comment="Info on how internal services should authenticate (if needed).",
    )

    # Agent Marketplace fields
    developer_id: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="Identifier for the developer who created this agent (for marketplace).",
    )
    pricing_info: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        default=dict,
        comment="Pricing information for the agent in the marketplace (e.g., subscription, per-call).",
    )
    review_status: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="Review status for marketplace agents (e.g., 'pending', 'approved', 'rejected').",
    )

    status: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        default=AgentStatus.ACTIVE.value,
        comment="Whether this agent type is currently available ('active' or 'inactive').",
    )

    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="Timestamp of record creation.",
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="Timestamp of last update.",
    )

    __table_args__ = (
        CheckConstraint(
            status.in_(get_agent_status_values()), name="registered_agents_status_check"
        ),
        # You can add other table-level constraints or indexes here if needed
    )

    def __repr__(self) -> str:
        return f"<RegisteredAgent(agent_definition_id='{self.agent_definition_id}', name='{self.name}', version='{self.version}', status='{self.status}')>"
