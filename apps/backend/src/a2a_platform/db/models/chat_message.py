import uuid
from datetime import UTC, datetime
from typing import TYPE_CHECKING, Any, Dict

from sqlalchemy import UUID, CheckConstraint, DateTime, ForeignKey, Index, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from a2a_platform.db import Base
from a2a_platform.db.types import PortableJSON

if TYPE_CHECKING:
    from .conversation import Conversation


class ChatMessage(Base):
    """
    SQLAlchemy model for the chat_messages table.
    Stores individual messages within conversations.
    """

    __tablename__ = "chat_messages"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    conversation_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("conversations.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    sender_role: Mapped[str] = mapped_column(
        Text,
        nullable=False,
    )

    def __init__(self, **kwargs: Any) -> None:
        # Ensure sender_role is always stored in lowercase
        if "sender_role" in kwargs:
            kwargs["sender_role"] = kwargs["sender_role"].lower()
        super().__init__(**kwargs)

    content: Mapped[Dict[str, Any]] = mapped_column(
        PortableJSON,
        nullable=False,
    )
    timestamp: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=lambda: datetime.now(UTC),
        nullable=False,
    )
    message_metadata: Mapped[Dict[str, Any]] = mapped_column(
        PortableJSON,
        nullable=True,
        default=dict,
        server_default="'{}'",
    )

    # Define relationships
    conversation: Mapped["Conversation"] = relationship(
        "Conversation", back_populates="messages"
    )

    # Check constraint for sender_role
    __table_args__ = (
        CheckConstraint(
            "sender_role IN ('user', 'agent')",
            name="ck_chat_message_sender_role",
        ),
        # Composite index for efficient conversation message queries
        Index(
            "ix_chat_messages_conversation_timestamp", "conversation_id", "timestamp"
        ),
    )

    def __repr__(self) -> str:
        return (
            f"<ChatMessage(id='{self.id}', conversation_id='{self.conversation_id}', "
            f"sender_role='{self.sender_role}')>"
        )
