from sqlalchemy.orm import DeclarativeBase


class Base(DeclarativeBase):
    """
    Base class for SQLAlchemy declarative models.
    All database models should inherit from this class.
    """

    pass


# If you have common metadata or type annotations for all models,
# you can define them here. For example:
#
# from sqlalchemy.orm import Mapped, mapped_column
# from sqlalchemy import MetaData
#
# class Base(DeclarativeBase):
#     metadata = MetaData(naming_convention={
#         "ix": "ix_%(column_0_label)s",
#         "uq": "uq_%(table_name)s_%(column_0_name)s",
#         "ck": "ck_%(table_name)s_%(constraint_name)s",
#         "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
#         "pk": "pk_%(table_name)s"
#     })
#
#     # Example of a common type annotation for primary keys
#     # id: Mapped[int] = mapped_column(primary_key=True, index=True)
