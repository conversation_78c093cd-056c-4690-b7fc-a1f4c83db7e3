"""
Database session management for the A2A Platform.

This module provides the SQLAlchemy session factory and related utilities
for database connections.
"""

from typing import Any, Async<PERSON>enerator, Generator, Optional

from sqlalchemy import Engine, create_engine
from sqlalchemy.ext.asyncio import (
    AsyncEngine,
    AsyncSession,
    async_sessionmaker,
    create_async_engine,
)
from sqlalchemy.orm import Session, sessionmaker

from a2a_platform.config.settings import get_settings


# Lazy initialization to avoid database connections at import time
_engine: Optional[Engine] = None
_async_engine: Optional[AsyncEngine] = None
_session_local: Optional[sessionmaker[Session]] = None
_async_session_local: Optional[async_sessionmaker[Any]] = None


def get_engine() -> Engine:
    """Get the SQLAlchemy engine with lazy-loaded settings."""
    global _engine
    if _engine is None:
        settings = get_settings()
        _engine = create_engine(
            settings.DATABASE_URL,
            pool_pre_ping=True,
            echo=settings.SQL_ECHO,
        )
    return _engine


def get_async_engine() -> AsyncEngine:
    """Get the SQLAlchemy async engine with lazy-loaded settings."""
    global _async_engine
    if _async_engine is None:
        settings = get_settings()
        _async_engine = create_async_engine(
            settings.ASYNC_DATABASE_URL,
            pool_pre_ping=True,
            echo=settings.SQL_ECHO,
        )
    return _async_engine


def get_session_local() -> sessionmaker[Session]:
    """Get the session factory with lazy initialization."""
    global _session_local
    if _session_local is None:
        _session_local = sessionmaker(
            autocommit=False, autoflush=False, bind=get_engine()
        )
    return _session_local


def get_async_session_local() -> async_sessionmaker[Any]:
    """Get the async session factory with lazy initialization."""
    global _async_session_local
    if _async_session_local is None:
        _async_session_local = async_sessionmaker(
            bind=get_async_engine(),
            autocommit=False,
            autoflush=False,
        )
    return _async_session_local


# For backward compatibility - these will be initialized lazily when accessed
engine: Optional[Engine] = None
async_engine: Optional[AsyncEngine] = None
SessionLocal: Optional[sessionmaker[Session]] = None
AsyncSessionLocal: Optional[async_sessionmaker[Any]] = None


def _ensure_initialized() -> None:
    """Ensure engines and session factories are initialized."""
    global engine, async_engine, SessionLocal, AsyncSessionLocal
    if engine is None:
        engine = get_engine()
    if async_engine is None:
        async_engine = get_async_engine()
    if SessionLocal is None:
        SessionLocal = get_session_local()
    if AsyncSessionLocal is None:
        AsyncSessionLocal = get_async_session_local()


def get_db() -> Generator[Session, None, None]:
    """
    Get a database session.

    Yields:
        Session: A SQLAlchemy session.
    """
    _ensure_initialized()
    db = SessionLocal()  # type: ignore[misc]
    try:
        yield db
    finally:
        db.close()


async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Get an async database session with proper context management.

    Yields:
        AsyncSession: A SQLAlchemy async session.
    """
    _ensure_initialized()
    async with AsyncSessionLocal() as async_session:  # type: ignore[misc]
        try:
            yield async_session
        except Exception:
            await async_session.rollback()
            raise
        # Session cleanup is handled by async with context manager
