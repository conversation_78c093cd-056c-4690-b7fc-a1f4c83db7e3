# This file makes Python treat the directory db as a package.

from typing import AsyncGenerator, Optional

from sqlalchemy.ext.asyncio import (
    AsyncEngine,
    AsyncSession,
    async_sessionmaker,
    create_async_engine,
)
from sqlalchemy.orm import DeclarativeBase

from a2a_platform.config.settings import get_settings


# Define Base class first to avoid circular imports
class Base(DeclarativeBase):
    pass


# Import models at module level to register them with SQLAlchemy
# These imports must be after Base is defined but before it's used
# noqa: E402 - Module level import not at top of file
from .models.assistant import Assistant  # noqa: F401, E402
from .models.assistant_objective import AssistantObjective  # noqa: F401, E402
from .models.cli_token import CliToken  # noqa: F401, E402
from .models.registered_agent import RegisteredAgent  # noqa: F401, E402
from .models.task import Task  # noqa: F401, E402
from .models.user import User  # noqa: F401, E402

# Lazy initialization to avoid database connections at import time
_engine: Optional[AsyncEngine] = None
_session_factory: Optional[async_sessionmaker[AsyncSession]] = None


def get_engine() -> AsyncEngine:
    """Get or create the async database engine."""
    global _engine
    if _engine is None:
        settings = get_settings()
        _engine = create_async_engine(settings.ASYNC_DATABASE_URL, pool_pre_ping=True)
    return _engine


def get_session_factory() -> async_sessionmaker[AsyncSession]:
    """Get or create the async session factory."""
    global _session_factory
    if _session_factory is None:
        _session_factory = async_sessionmaker(
            bind=get_engine(),
            class_=AsyncSession,
            autocommit=False,
            autoflush=False,
            expire_on_commit=False,
        )
    return _session_factory


# For backward compatibility - these will be initialized lazily when accessed
engine: Optional[AsyncEngine] = None
AsyncSessionFactory: Optional[async_sessionmaker[AsyncSession]] = None


def _ensure_initialized() -> None:
    """Ensure engine and session factory are initialized."""
    global engine, AsyncSessionFactory
    if engine is None:
        engine = get_engine()
    if AsyncSessionFactory is None:
        AsyncSessionFactory = get_session_factory()


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Dependency to get an async database session."""
    _ensure_initialized()
    # After _ensure_initialized(), AsyncSessionFactory is guaranteed to be set
    async with AsyncSessionFactory() as session:  # type: ignore[misc]
        try:
            yield session
            # Commit is now handled by the caller explicitly when needed
        except Exception:
            await session.rollback()  # Rollback in case of an error
            raise
        # Session cleanup is handled by async with context manager


# For use in non-FastAPI contexts (e.g., scripts, tests)
async def get_standalone_session() -> AsyncGenerator[AsyncSession, None]:
    """Get a standalone async database session with proper cleanup."""
    _ensure_initialized()
    # After _ensure_initialized(), AsyncSessionFactory is guaranteed to be set
    async with AsyncSessionFactory() as session:  # type: ignore[misc]
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        # Session cleanup is handled by async with context manager


# If you need a synchronous session for Alembic or other tools, define it here
# from sqlalchemy import create_engine
# from sqlalchemy.orm import sessionmaker
# SYNC_DATABASE_URL = settings.DATABASE_URL # Assuming you have a sync URL
# sync_engine = create_engine(SYNC_DATABASE_URL)
# SyncSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=sync_engine)

# Helper for alembic migrations - ensure all models are imported before running migrations
# You might need to import your models here or in a common models.__init__.py
# from a2a_platform.db.models import user # Example model import
# from a2a_platform.db.models import cli_token # Example model import

__all__ = [
    "Base",
    "engine",
    "AsyncSessionFactory",
    "get_db_session",
    "get_standalone_session",
]
