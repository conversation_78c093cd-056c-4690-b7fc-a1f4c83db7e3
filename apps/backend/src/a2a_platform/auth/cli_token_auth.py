import logging
from datetime import datetime
from typing import <PERSON><PERSON>, <PERSON><PERSON>

from fastapi import Request
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.db import get_session_factory
from a2a_platform.db.models.cli_token import CliToken
from a2a_platform.db.models.user import User

logger = logging.getLogger(__name__)


class CliTokenAuthMiddleware(HTTPBearer):
    """
    Middleware to authenticate users via CLI tokens.

    This middleware verifies the CLI token provided in the X-A2A-CLI-Token header
    or in the Authorization header (Bearer <token>) and returns the associated clerk_user_id.

    The token format is: a2a_cli_<user_id_prefix>_<token_secret>
    """

    async def __call__(
        self, request: Request
    ) -> Optional[HTTPAuthorizationCredentials]:
        # Check for token in X-A2A-CLI-Token header first
        cli_token = request.headers.get("X-A2A-CLI-Token")

        # If not present, check Authorization header for Bearer token
        if not cli_token:
            authorization = request.headers.get("Authorization")
            if not authorization:
                logger.debug("No CLI token or Authorization header found")
                return None

            scheme, token = self._get_scheme_token(authorization)
            if scheme.lower() != "bearer" or not token:
                logger.debug(f"Invalid scheme or no token: {scheme}")
                return None

            cli_token = token

        # Skip further processing if no token
        if not cli_token:
            logger.debug("No CLI token found")
            return None

        # Validate token format and extract parts
        logger.info("Validating CLI token format...")
        parsed_token = self._parse_token(cli_token)
        if not parsed_token:
            logger.warning("Invalid CLI token format")
            logger.warning("Expected format: a2a_cli_<user_id_prefix>_<token_secret>")
            return None

        token_prefix, user_id_prefix, token_secret = parsed_token
        logger.debug("Token parsed successfully")

        # Verify expected token prefix
        if token_prefix != "a2a_cli":
            logger.warning("Invalid token prefix")
            return None

        # Get database session to look up token using proper async context manager
        try:
            session_factory = get_session_factory()
            async with session_factory() as db_session:
                # Validate the token
                user_clerk_id = await self._validate_token(
                    db_session, user_id_prefix, token_secret
                )

                if user_clerk_id:
                    # Update last_used_at timestamp if validation succeeds
                    await self._update_token_usage(
                        db_session, user_id_prefix, token_secret
                    )
                    logger.debug(
                        f"Successfully authenticated with CLI token for user: "
                        f"{user_clerk_id}"
                    )
                    return HTTPAuthorizationCredentials(
                        scheme="Bearer", credentials=user_clerk_id
                    )

        except Exception as e:
            logger.error(f"Error validating CLI token: {str(e)}", exc_info=True)

        logger.debug("CLI token validation failed")
        return None

    def _get_scheme_token(self, authorization: str) -> Tuple[str, str]:
        """Extract the scheme and token from an Authorization header value."""
        try:
            scheme, token = authorization.strip().split(" ", 1)
            return scheme, token
        except ValueError:
            return "", ""

    def _parse_token(self, token: str) -> Optional[Tuple[str, str, str]]:
        """
        Parse a CLI token with format: a2a_cli_<user_id_prefix>_<token_secret>
        Returns (token_prefix, user_id_prefix, token_secret) or None if invalid format
        """
        try:
            parts = token.split("_")
            if len(parts) < 4:
                return None

            # Format: a2a_cli_<user_id_prefix>_<token_secret>
            token_prefix = f"{parts[0]}_{parts[1]}"  # "a2a_cli"
            user_id_prefix = parts[2]
            token_secret = "_".join(
                parts[3:]
            )  # Join remaining parts in case secret contains underscores

            return token_prefix, user_id_prefix, token_secret
        except Exception:
            return None

    async def _validate_token(
        self, db_session: AsyncSession, user_id_prefix: str, token_secret: str
    ) -> Optional[str]:
        """
        Validate a CLI token against the database.
        Returns the clerk_user_id if valid, None otherwise.
        """
        try:
            # Validate token against database
            logger.debug("Validating token against database")

            # Find token in database where salt_hex (user_id_prefix) matches
            # and the hashed_token (token_secret) matches
            stmt = (
                select(User.clerk_user_id)
                .join(CliToken, User.id == CliToken.user_id)
                .where(
                    CliToken.salt_hex == user_id_prefix,
                    CliToken.hashed_token == token_secret,
                )
            )

            # Execute token validation query
            logger.debug("Executing token validation query")

            result = await db_session.execute(stmt)
            clerk_user_id = result.scalar_one_or_none()

            if clerk_user_id:
                logger.debug("Token validated successfully")
            else:
                logger.warning("Token validation failed: no matching token found")

            return clerk_user_id

        except Exception as e:
            logger.error(
                f"Database error validating CLI token: {str(e)}", exc_info=True
            )
            return None

    async def _update_token_usage(
        self, db_session: AsyncSession, user_id_prefix: str, token_secret: str
    ) -> bool:
        """Update the last_used_at timestamp for a CLI token."""
        try:
            # Find the token
            stmt = select(CliToken).where(
                CliToken.salt_hex == user_id_prefix,
                CliToken.hashed_token == token_secret,
            )

            result = await db_session.execute(stmt)
            token = result.scalar_one_or_none()

            if token:
                # Update the last_used_at timestamp
                token.last_used_at = datetime.now()
                try:
                    # Commit the changes but handle potential errors
                    await db_session.commit()
                    logger.debug("Updated token last_used_at timestamp")
                    return True
                except Exception as commit_error:
                    # If commit fails, rollback and log the error
                    await db_session.rollback()
                    logger.error(
                        f"Error committing token usage update: {str(commit_error)}",
                        exc_info=True,
                    )
                    return False

            logger.debug("Token not found for usage update")
            return False

        except Exception as e:
            logger.error(
                f"Error updating token usage timestamp: {str(e)}", exc_info=True
            )
            return False
