import json
import logging
from typing import Any, Dict, Optional

from clerk_backend_api import Clerk
from clerk_backend_api.jwks_helpers import AuthenticateRequestOptions
from fastapi import HTT<PERSON>Exception, Request, status
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer
from fastapi.security.utils import get_authorization_scheme_param
from jose import JOSEError
from starlette.datastructures import Headers
from svix.webhooks import Webhook, WebhookVerificationError

from a2a_platform.auth.cli_token_auth import CliTokenAuthMiddleware
from a2a_platform.config.settings import get_settings

# Set up logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


# Use lazy loading for clerk client
def get_clerk_client() -> Clerk:
    """Get the Clerk client with lazy-loaded settings."""
    settings = get_settings()
    return Clerk(bearer_auth=settings.CLERK_API_KEY)


# Initialize clerk client lazily for authentication middleware
clerk = get_clerk_client()


class AsyncClerkClient:
    """
    Async context manager for Clerk client to ensure proper cleanup.
    """

    def __init__(self) -> None:
        self._client: Optional[Clerk] = None

    async def __aenter__(self) -> Clerk:
        """Create and return a new Clerk client."""
        self._client = get_clerk_client()
        return self._client

    async def __aexit__(self, _exc_type: Any, _exc_val: Any, _exc_tb: Any) -> None:
        """Clean up the Clerk client by closing HTTP clients."""
        if self._client:
            try:
                # Close both sync and async clients
                config = self._client.sdk_configuration

                # Close async client
                if hasattr(config, "async_client") and config.async_client:
                    if hasattr(config.async_client, "aclose"):
                        await config.async_client.aclose()
                        logger.debug("Closed Clerk async HTTP client")

                # Close sync client
                if hasattr(config, "client") and config.client:
                    if hasattr(config.client, "close"):
                        config.client.close()
                        logger.debug("Closed Clerk sync HTTP client")

            except Exception as e:
                logger.debug(f"Error closing Clerk HTTP clients: {e}")
            finally:
                self._client = None


class ClerkAuthMiddleware(HTTPBearer):
    def __init__(self) -> None:
        super().__init__()
        self.cli_token_auth = CliTokenAuthMiddleware()

    async def __call__(
        self, request: Request
    ) -> Optional[HTTPAuthorizationCredentials]:
        # Log sanitized headers for debugging
        sensitive_headers = {"authorization", "cookie", "x-clerk-user-id"}
        headers_dict = {
            key: (value if key.lower() not in sensitive_headers else "***REDACTED***")
            for key, value in request.headers.items()
        }
        logger.info(f"Sanitized request headers: {headers_dict}")

        # First try CLI token authentication
        logger.info("Attempting CLI token authentication...")
        cli_auth_result = await self.cli_token_auth(request)
        if cli_auth_result:
            logger.info(
                f"Successfully authenticated with CLI token: {cli_auth_result.credentials}"
            )
            return cli_auth_result
        else:
            logger.warning("CLI token authentication failed")

        # Next check for direct clerk_user_id in header
        clerk_user_id = request.headers.get("X-Clerk-User-Id")
        if clerk_user_id:
            logger.info(f"Found X-Clerk-User-Id header: {clerk_user_id}")
            # In a real production environment, you might want to validate this ID
            # For testing/development, we trust it
            return HTTPAuthorizationCredentials(
                scheme="Bearer", credentials=clerk_user_id
            )

        # If no X-Clerk-User-Id header, fall back to standard authorization
        authorization = request.headers.get("Authorization")
        logger.warning("Attempting web authentication...")
        if authorization:
            masked_authorization = (
                authorization[:10] + "..." if len(authorization) > 10 else authorization
            )
            logger.debug(f"Authorization header (masked): {masked_authorization}")
        if not authorization:
            logger.error("No Authorization header found")
            return None

        scheme, token = get_authorization_scheme_param(authorization)
        logger.debug(f"Auth scheme: {scheme}")
        if scheme.lower() != "bearer" or not token:
            logger.error(f"Invalid scheme or no token: {scheme}")
            return None

        try:
            # Add detailed logging to help diagnose web authentication issues
            logger.info(f"Processing web auth token (first 10 chars): {token[:10]}...")

            # Create a new request object with the Authorization header
            # This is needed because the authenticate_request method expects a request object
            # with the Authorization header
            request_with_auth = Request(scope=request.scope)

            # Create a new Headers object with the authorization header
            headers_dict = dict(request.headers.items())
            headers_dict["authorization"] = f"Bearer {token}"

            # Set the headers on the request
            request_with_auth._headers = Headers(headers_dict)

            logger.debug("Authenticating request with Clerk...")
            request_state = clerk.authenticate_request(
                request_with_auth,
                AuthenticateRequestOptions(
                    # You can specify authorized parties if needed
                    # authorized_parties=["https://your-domain.com"]
                ),
            )

            if not request_state.is_signed_in:
                logger.error(f"Authentication failed: {request_state.reason}")
                return None

            logger.debug("Successfully authenticated request with Clerk")

            # Extract user_id from token payload
            # Ensure payload is not None before accessing get method
            if request_state.payload is None:
                logger.error("Token payload is None")
                return None

            user_id = request_state.payload.get("sub")
            if not user_id:
                logger.error("No user_id (sub) found in token payload")
                return None

            logger.warning(f"Successfully extracted user_id: {user_id}")
            logger.info(f"Authenticated web user with clerk_user_id: {user_id}")
            return HTTPAuthorizationCredentials(scheme=scheme, credentials=user_id)
        except JOSEError as e:
            logger.error(f"JWT Error: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}", exc_info=True)
            return None


def verify_webhook_signature(
    signature: str,
    body: bytes,
    timestamp: Optional[str] = None,
    message_id: Optional[str] = None,
) -> bool:
    """
    Verify that the incoming webhook request is from Clerk.

    Args:
        signature: The svix-signature header from the webhook request
        body: Raw request body bytes
        timestamp: Optional timestamp from the svix-timestamp header
        message_id: Optional message ID from the svix-id header

    Returns:
        bool: True if the signature is valid, False otherwise
    """
    try:
        # Log the signature format and webhook secret for debugging
        # Only log the first few characters of the secret for security
        settings = get_settings()
        secret_prefix = (
            settings.CLERK_WEBHOOK_SECRET[:8]
            if settings.CLERK_WEBHOOK_SECRET
            else "None"
        )
        logger.debug(
            f"Verifying signature format: {signature[:20]}... with secret prefix: {secret_prefix}..."
        )
        logger.debug(
            f"Body starts with: {body[:50].decode('utf-8', errors='replace')}..."
        )

        # Use the Svix Webhook class to verify the signature
        settings = get_settings()
        webhook = Webhook(settings.CLERK_WEBHOOK_SECRET)

        # Create headers dict for Svix verification
        # Svix requires all three headers: svix-id, svix-timestamp, and svix-signature
        headers = {"svix-signature": signature}

        # Add timestamp and message_id if provided
        if timestamp:
            headers["svix-timestamp"] = timestamp
            logger.debug(f"Added svix-timestamp: {timestamp}")

        if message_id:
            headers["svix-id"] = message_id
            logger.debug(f"Added svix-id: {message_id}")

        # Verify will throw an exception if invalid
        webhook.verify(body, headers)
        logger.debug("Webhook signature verified successfully")
        return True
    except WebhookVerificationError as e:
        logger.warning(f"WebhookVerificationError: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Error verifying webhook signature: {str(e)}")
        return False


async def verify_clerk_webhook(request: Request) -> Dict[str, Any]:
    """
    Verify that the incoming webhook request is from Clerk.
    This function is designed to be used as a FastAPI dependency.

    Args:
        request: The FastAPI Request object

    Returns:
        Dict[str, Any]: The verified webhook payload as a dictionary

    Raises:
        HTTPException: If the webhook signature is invalid or missing
    """
    # Log the request method and URL for debugging
    logger.debug(f"Verifying Clerk webhook: {request.method} {request.url}")

    # Get the raw request body
    body: bytes = await request.body()

    # Get the Svix headers from the request
    svix_headers = {
        k: v
        for k, v in request.headers.items()
        if k.lower() in ["svix-id", "svix-timestamp", "svix-signature"]
    }

    # Log the extracted Svix headers (masked for security)
    masked_headers = {
        k: (v[:10] + "..." if len(v) > 10 else v) for k, v in svix_headers.items()
    }
    logger.debug(f"Extracted Svix headers: {masked_headers}")

    # Check if required headers are present
    signature = svix_headers.get("svix-signature", "")
    timestamp = svix_headers.get("svix-timestamp", "")
    message_id = svix_headers.get("svix-id", "")

    if not signature or not timestamp or not message_id:
        logger.warning("Missing required Svix headers")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing required webhook headers",
        )

    # Verify the webhook signature
    try:
        settings = get_settings()
        webhook = Webhook(settings.CLERK_WEBHOOK_SECRET)
        webhook.verify(body, svix_headers)
        logger.debug("Webhook signature verified successfully")
    except WebhookVerificationError as e:
        logger.warning(f"WebhookVerificationError: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid webhook signature",
        )
    except Exception as e:
        logger.error(f"Error verifying webhook signature: {str(e)}")
        logger.error("Invalid webhook signature", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid webhook signature",
        )

    # Parse the webhook payload
    try:
        payload: Dict[str, Any] = json.loads(body)
        # Add the Clerk event ID to the payload for idempotency checks
        payload["clerk_event_id"] = message_id
        return payload
    except json.JSONDecodeError:
        logger.error("Invalid JSON in webhook body")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid JSON body",
        )
