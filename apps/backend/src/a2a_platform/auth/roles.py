"""
Role-based authorization for the A2A Platform.
"""

import enum
import logging
from typing import Dict, List, Set

from a2a_platform.config.settings import get_settings

logger = logging.getLogger(__name__)


class UserRole(str, enum.Enum):
    """User roles for the A2A Platform."""

    ADMIN = "admin"
    DEVELOPER = "developer"
    USER = "user"


class RoleBasedAuth:
    """
    Role-based authorization for the A2A Platform.

    This class provides methods to check if a user has a specific role
    and to get the roles of a user.
    """

    # Mapping of clerk_user_id to roles
    # This is a temporary solution until we have a proper role management system
    # In a production environment, this would be stored in a database or retrieved from Clerk
    _user_roles: Dict[str, Set[UserRole]] = {}

    @classmethod
    def initialize_roles(cls) -> None:
        """
        Initialize roles for testing and development.

        In a production environment, roles would be retrieved from Clerk or a database.
        """
        settings = get_settings()

        # Initialize admin users from environment variables
        admin_users = settings.ADMIN_USERS.split(",") if settings.ADMIN_USERS else []
        for admin_user in admin_users:
            if admin_user:
                cls._user_roles[admin_user.strip()] = {
                    UserRole.ADMIN,
                    UserRole.DEVELOPER,
                    UserRole.USER,
                }
                logger.info(f"Initialized admin user: {admin_user}")

        # For development, add some test users with different roles
        if settings.ENVIRONMENT == "development":
            # Add test users with different roles
            cls._user_roles["dev_user"] = {UserRole.DEVELOPER, UserRole.USER}
            cls._user_roles["test_user"] = {UserRole.USER}
            logger.info("Initialized test users for development environment")

    @classmethod
    def get_user_roles(cls, clerk_user_id: str) -> Set[UserRole]:
        """
        Get the roles of a user.

        Args:
            clerk_user_id: The Clerk user ID

        Returns:
            A set of UserRole enums representing the user's roles
        """
        # Ensure roles are initialized
        if not cls._user_roles:
            cls.initialize_roles()

        # If the user is not in the roles mapping, assume they are a regular user
        return cls._user_roles.get(clerk_user_id, {UserRole.USER})

    @classmethod
    def has_role(cls, clerk_user_id: str, role: UserRole) -> bool:
        """
        Check if a user has a specific role.

        Args:
            clerk_user_id: The Clerk user ID
            role: The role to check

        Returns:
            True if the user has the role, False otherwise
        """
        return role in cls.get_user_roles(clerk_user_id)

    @classmethod
    def is_admin(cls, clerk_user_id: str) -> bool:
        """
        Check if a user is an admin.

        Args:
            clerk_user_id: The Clerk user ID

        Returns:
            True if the user is an admin, False otherwise
        """
        return cls.has_role(clerk_user_id, UserRole.ADMIN)

    @classmethod
    def is_developer(cls, clerk_user_id: str) -> bool:
        """
        Check if a user is a developer.

        Args:
            clerk_user_id: The Clerk user ID

        Returns:
            True if the user is a developer, False otherwise
        """
        return cls.has_role(clerk_user_id, UserRole.DEVELOPER)

    @classmethod
    def add_role(cls, clerk_user_id: str, role: UserRole) -> None:
        """
        Add a role to a user.

        Args:
            clerk_user_id: The Clerk user ID
            role: The role to add
        """
        if clerk_user_id not in cls._user_roles:
            cls._user_roles[clerk_user_id] = {UserRole.USER}

        cls._user_roles[clerk_user_id].add(role)
        logger.info(f"Added role {role} to user {clerk_user_id}")

    @classmethod
    def remove_role(cls, clerk_user_id: str, role: UserRole) -> None:
        """
        Remove a role from a user.

        Args:
            clerk_user_id: The Clerk user ID
            role: The role to remove
        """
        if clerk_user_id in cls._user_roles and role in cls._user_roles[clerk_user_id]:
            cls._user_roles[clerk_user_id].remove(role)
            logger.info(f"Removed role {role} from user {clerk_user_id}")

    @classmethod
    def set_roles(cls, clerk_user_id: str, roles: List[UserRole]) -> None:
        """
        Set the roles of a user.

        Args:
            clerk_user_id: The Clerk user ID
            roles: The roles to set
        """
        cls._user_roles[clerk_user_id] = set(roles)
        logger.info(f"Set roles {roles} for user {clerk_user_id}")
