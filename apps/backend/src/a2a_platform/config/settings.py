import logging
from typing import Any, List, Optional, Union
from urllib.parse import urlparse, urlunparse

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict

logger = logging.getLogger(__name__)


class Settings(BaseSettings):
    # Application settings
    DEBUG: bool = False
    APP_TITLE: str = "A2A Platform API"
    APP_VERSION: str = "0.1.0"

    # Database settings
    # Use DATABASE_URL for sync connections (Alembic, SQLAlchemy)
    DATABASE_URL: str
    # Use DATABASE_ASYNC_URL for async connections (FastAPI, GraphQL)
    # Falls back to computed async URL from DATABASE_URL if not provided
    ASYNC_DATABASE_URL: str = Field(alias="DATABASE_ASYNC_URL", default="")
    SQL_ECHO: bool = False

    def model_post_init(self, __context: Any) -> None:
        """Post-initialization to compute async database URL if not provided."""
        _ = __context  # Satisfy vulture
        # If ASYNC_DATABASE_URL is empty, compute it from DATABASE_URL
        if not self.ASYNC_DATABASE_URL:
            if self.DATABASE_URL.startswith("postgresql://"):
                self.ASYNC_DATABASE_URL = self.DATABASE_URL.replace(
                    "postgresql://", "postgresql+asyncpg://"
                )
            else:
                self.ASYNC_DATABASE_URL = self.DATABASE_URL

    # Redis settings
    REDIS_URL: str

    # Redis Queue (RQ) settings
    RQ_DEFAULT_TIMEOUT: int = 180  # Default job timeout in seconds
    RQ_DEFAULT_TTL: int = 86400  # Default job TTL in seconds (1 day)
    RQ_RESULT_TTL: int = 3600  # How long to keep job results (1 hour)
    RQ_WORKER_COUNT: int = 2  # Number of RQ workers to start

    # Auth settings (Clerk)
    CLERK_API_KEY: str
    CLERK_JWT_PUBLIC_KEY: str
    CLERK_WEBHOOK_SECRET: str  # Webhook signing secret for verifying webhook requests

    # CORS settings
    # Can be a string (comma-separated) or a list of strings
    CORS_ORIGINS: Union[str, List[str]] = Field(
        default=["https://localhost:5173"],
        description="List of allowed CORS origins (can be a comma-separated string or a list)",
    )

    # Trusted hosts settings
    # Can be a string (comma-separated) or a list of strings
    TRUSTED_HOSTS: Union[str, List[str]] = Field(
        default=["*"],  # Allow all hosts by default for development
        description="List of allowed host headers (can be a comma-separated string or a list)",
    )

    @property
    def cors_origins_list(self) -> list[str]:
        """Convert CORS_ORIGINS to a list of origins."""
        if isinstance(self.CORS_ORIGINS, list):
            return self.CORS_ORIGINS

        if not self.CORS_ORIGINS:
            return []

        # Split by comma and strip whitespace
        if "," in self.CORS_ORIGINS:
            return [
                origin.strip()
                for origin in self.CORS_ORIGINS.split(",")
                if origin.strip()
            ]

        # Single URL
        return [self.CORS_ORIGINS.strip()]

    @property
    def trusted_hosts_list(self) -> list[str]:
        """Convert TRUSTED_HOSTS to a list of hosts."""
        if isinstance(self.TRUSTED_HOSTS, list):
            return self.TRUSTED_HOSTS

        if not self.TRUSTED_HOSTS:
            return ["*"]  # Default to allow all hosts

        # Split by comma and strip whitespace
        if "," in self.TRUSTED_HOSTS:
            return [
                host.strip() for host in self.TRUSTED_HOSTS.split(",") if host.strip()
            ]

        # Single host
        return [self.TRUSTED_HOSTS.strip()]

    # File storage settings
    STORAGE_BUCKET: str
    CDN_URL: str

    # Pub/Sub settings
    PUBSUB_PROJECT_ID: str

    # User roles settings
    ADMIN_USERS: str = (
        ""  # Comma-separated list of Clerk user IDs with admin privileges
    )

    # Environment
    ENVIRONMENT: str = "development"

    # Testing configuration
    ENABLE_TEST_AUTH_BYPASS: bool = Field(
        default=False,
        description="Enable test authentication bypass for integration testing (NEVER enable in production)",
    )

    # HTTPS and Security settings
    ENFORCE_HTTPS: bool = False
    SSL_DOMAIN: str = "domain.com"
    HSTS_MAX_AGE: int = 31536000  # 1 year in seconds
    HSTS_INCLUDE_SUBDOMAINS: bool = True
    HSTS_PRELOAD: bool = True

    # Content Security Policy (CSP)
    CSP_POLICY: str = "default-src 'self'; connect-src 'self' https: wss:; upgrade-insecure-requests; block-all-mixed-content"

    # AI Service Configuration
    OPENAI_API_KEY: Optional[str] = None
    ANTHROPIC_API_KEY: Optional[str] = None
    GEMINI_API_KEY: Optional[str] = None  # Google Gemini API key
    GOOGLE_API_KEY: Optional[str] = None  # Alternative Google API key name
    AI_RESPONSE_ENABLED: bool = True
    AI_RESPONSE_TIMEOUT: int = 30  # seconds
    AI_RATE_LIMIT_CALLS: int = 50  # calls per minute
    AI_RATE_LIMIT_WINDOW: int = 60  # seconds
    AI_DEFAULT_PROVIDER: str = (
        "openai"  # Default LLM provider (openai, anthropic, google)
    )

    # Use case_sensitive=True to ensure environment variables match exactly
    model_config = SettingsConfigDict(
        case_sensitive=True,
        extra="ignore",
    )


def _adjust_database_url_for_environment(db_url: str) -> str:
    """
    Adjust the database URL based on the environment.

    In CI environments, the database host should be 'localhost' instead of 'db'.
    In Docker environments, the database host should be 'db' instead of 'localhost'.
    In Cloud Run or production environments, preserve the original URL (especially for Cloud SQL Auth Proxy).
    """
    from a2a_platform.utils.environment import (
        is_docker_environment,
        is_ci_environment,
        should_use_cloud_sql_proxy,
    )

    # Check if we're in a Cloud Run environment or should use Cloud SQL proxy (don't adjust URLs)
    if should_use_cloud_sql_proxy(db_url):
        logger.debug(
            "Cloud Run/Production environment detected. Preserving original database URL."
        )
        return db_url

    # Check if we're in a CI environment (but not inside Docker)
    is_ci = is_ci_environment() and not is_docker_environment()

    if is_ci:
        # Always use 'localhost' in CI environments
        if "@db:" in db_url:
            # Parse the URL
            parsed_url = urlparse(db_url)

            # Replace 'db' with 'localhost' in the netloc part
            netloc = parsed_url.netloc.replace("@db:", "@localhost:")

            # Reconstruct the URL
            adjusted_url = urlunparse(
                (
                    parsed_url.scheme,
                    netloc,
                    parsed_url.path,
                    parsed_url.params,
                    parsed_url.query,
                    parsed_url.fragment,
                )
            )

            logger.debug(
                "CI environment detected. Adjusted database URL from '%s' to '%s'",
                db_url,
                adjusted_url,
            )
            return adjusted_url
    else:
        # Always use 'db' in Docker environments
        if "@localhost:" in db_url:
            # Parse the URL
            parsed_url = urlparse(db_url)

            # Replace 'localhost' with 'db' in the netloc part
            netloc = parsed_url.netloc.replace("@localhost:", "@db:")

            # Reconstruct the URL
            adjusted_url = urlunparse(
                (
                    parsed_url.scheme,
                    netloc,
                    parsed_url.path,
                    parsed_url.params,
                    parsed_url.query,
                    parsed_url.fragment,
                )
            )

            logger.debug(
                "Docker environment detected. Adjusted database URL from '%s' to '%s'",
                db_url,
                adjusted_url,
            )
            return adjusted_url

    return db_url


def parse_cors_origins(v: Union[str, List[str]]) -> List[str]:
    """Parse CORS_ORIGINS from string to list if needed.

    This is a standalone version for use in tests.
    """
    if isinstance(v, str):
        if not v:
            return []

        # Split by comma and strip whitespace
        if "," in v:
            return [origin.strip() for origin in v.split(",") if origin.strip()]

        # Single URL
        return [v.strip()]

    return list(v)


def get_settings() -> Settings:
    """
    Get application settings from environment variables.

    Environment variables are provided exclusively through Docker Compose
    or other environment variable mechanisms.

    For local development, use Docker Compose with appropriate environment variables.
    For testing, environment variables are set in docker-compose.test.yml or via CI configuration.
    """
    # Load settings from environment variables (Docker Compose or host env)
    settings = Settings()

    # Adjust database URL based on the environment
    settings.DATABASE_URL = _adjust_database_url_for_environment(settings.DATABASE_URL)

    return settings
