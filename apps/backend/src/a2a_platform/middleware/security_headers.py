"""
Security headers middleware for FastAPI applications.

This middleware adds HTTP security headers to all responses, including:
- Strict-Transport-Security (HSTS)
- Content-Security-Policy (CSP)
- X-Content-Type-Options
- X-Frame-Options
- Referrer-Policy
- X-XSS-Protection
"""

from typing import Callable, Optional

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.types import ASGIApp


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """
    Middleware to add security headers to all responses.

    This middleware enforces HTTPS by adding HSTS headers and other security-related
    headers to protect against common web vulnerabilities.
    """

    def __init__(
        self,
        app: ASGIApp,
        hsts_max_age: int = 31536000,  # 1 year in seconds
        include_subdomains: bool = True,
        preload: bool = True,
        content_security_policy: Optional[str] = None,
    ) -> None:
        """
        Initialize the security headers middleware.

        Args:
            app: The ASGI application
            hsts_max_age: Max age for HSTS header in seconds
            include_subdomains: Whether to include subdomains in HSTS
            preload: Whether to enable HSTS preloading
            content_security_policy: Custom CSP policy string, if None a default will be used
        """
        super().__init__(app)
        self.hsts_max_age = hsts_max_age
        self.include_subdomains = include_subdomains
        self.preload = preload

        # Default CSP policy focuses on HTTPS/WSS connections
        self.content_security_policy = content_security_policy or (
            "default-src 'self'; "
            "connect-src 'self' https: wss:; "
            "upgrade-insecure-requests; "
            "block-all-mixed-content"
        )

        # Relaxed CSP for Swagger UI to allow loading resources from CDNs
        self.swagger_ui_csp = (
            "default-src 'self'; "
            "connect-src 'self' https: wss:; "
            "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; "
            "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com; "
            "img-src 'self' https://fastapi.tiangolo.com data:; "
            "font-src 'self' https://cdn.jsdelivr.net https://fonts.gstatic.com; "
            "upgrade-insecure-requests; "
            "block-all-mixed-content"
        )

        # Relaxed CSP for GraphQL/GraphiQL to allow loading resources from unpkg.com and other CDNs
        self.graphql_csp = (
            "default-src 'self'; "
            "connect-src 'self' https: wss:; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://unpkg.com https://cdn.jsdelivr.net; "
            "style-src 'self' 'unsafe-inline' https://unpkg.com https://cdn.jsdelivr.net https://fonts.googleapis.com; "
            "img-src 'self' data: https:; "
            "font-src 'self' data: https://unpkg.com https://cdn.jsdelivr.net https://fonts.gstatic.com; "
            "upgrade-insecure-requests; "
            "block-all-mixed-content"
        )

    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        """
        Process the request and add security headers to the response.

        Args:
            request: The incoming request
            call_next: The next endpoint to call

        Returns:
            The response with added security headers
        """
        response = await call_next(request)

        # Build HSTS header
        hsts_value = f"max-age={self.hsts_max_age}"
        if self.include_subdomains:
            hsts_value += "; includeSubDomains"
        if self.preload:
            hsts_value += "; preload"

        # Add security headers
        response.headers["Strict-Transport-Security"] = hsts_value

        # Use relaxed CSP for specific routes
        path = request.url.path
        if (
            path.startswith("/docs")
            or path.startswith("/redoc")
            or path.startswith("/openapi.json")
        ):
            response.headers["Content-Security-Policy"] = self.swagger_ui_csp
        elif path.startswith("/graphql"):
            response.headers["Content-Security-Policy"] = self.graphql_csp
        else:
            response.headers["Content-Security-Policy"] = self.content_security_policy

        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["X-XSS-Protection"] = "1; mode=block"

        return response


def get_security_headers_middleware() -> Callable[[ASGIApp], SecurityHeadersMiddleware]:
    """
    Factory function for creating the security headers middleware.

    Returns:
        A function that takes an app and returns the configured middleware
    """

    def _add_middleware(app: ASGIApp) -> SecurityHeadersMiddleware:
        return SecurityHeadersMiddleware(app)

    return _add_middleware
