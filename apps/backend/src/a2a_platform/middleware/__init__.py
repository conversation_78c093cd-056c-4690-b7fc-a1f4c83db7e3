"""
Middleware package for the A2A Platform.

This package contains middleware components for handling security,
HTTPS redirection, and WebSocket security.
"""

from a2a_platform.middleware.https_redirect import (
    HTTPSRedirectMiddleware,
    get_https_redirect_middleware,
)
from a2a_platform.middleware.security_headers import (
    SecurityHeadersMiddleware,
    get_security_headers_middleware,
)
from a2a_platform.middleware.websocket_security import (
    WebSocketSecurityMiddleware,
    get_websocket_security_middleware,
)

__all__ = [
    "HTTPSRedirectMiddleware",
    "get_https_redirect_middleware",
    "SecurityHeadersMiddleware",
    "get_security_headers_middleware",
    "WebSocketSecurityMiddleware",
    "get_websocket_security_middleware",
]
