"""
Conditional TrustedHost middleware that skips validation for health check routes.

This middleware provides trusted host validation while allowing health check
endpoints to be accessed without host validation, which is necessary for
Cloud Run startup probes and load balancer health checks.
"""

import re
from typing import List, Optional, Pattern, Set

from fastapi import Request, Response
from starlette.middleware.base import BaseHT<PERSON>Middleware, RequestResponseEndpoint
from starlette.responses import PlainTextResponse
from starlette.types import ASGIApp


class ConditionalTrustedHostMiddleware(BaseHTTPMiddleware):
    """
    Middleware that validates trusted hosts but skips validation for health check routes.

    This allows Cloud Run startup probes and other health checks to access
    health endpoints without requiring specific host headers, while still
    protecting other routes.
    """

    # Define exact health check paths (security: no substring matching)
    DEFAULT_HEALTH_CHECK_PATHS = frozenset(
        {
            "/health",
            "/api/health",
            "/healthz",
            "/api/healthz",
            "/readiness",
            "/liveness",
            "/ping",
        }
    )

    def __init__(
        self,
        app: ASGIApp,
        allowed_hosts: Optional[List[str]] = None,
        health_check_paths: Optional[List[str]] = None,
        www_redirect: bool = True,
    ) -> None:
        """
        Initialize the conditional trusted host middleware.

        Args:
            app: The ASGI application
            allowed_hosts: List of allowed host headers for non-health routes
            health_check_paths: List of paths to exclude from host validation
            www_redirect: Whether to redirect www subdomains (not implemented)
        """
        super().__init__(app)

        # Process allowed hosts for performance optimization
        self.allowed_hosts = allowed_hosts or ["*"]
        self.allow_all = "*" in self.allowed_hosts
        self.exact_hosts: Set[str] = set()
        self.wildcard_patterns: List[Pattern[str]] = []

        # Pre-compile host patterns for performance
        for host in self.allowed_hosts:
            if host == "*":
                continue  # Already handled by allow_all
            elif host.startswith("*."):
                # Convert *.example.com to regex pattern
                domain = re.escape(host[2:])
                pattern = re.compile(f"^[^.]+\\.{domain}$", re.IGNORECASE)
                self.wildcard_patterns.append(pattern)
            else:
                self.exact_hosts.add(host.lower())

        # Use exact path matching for health checks (security improvement)
        if health_check_paths is not None:
            self.health_check_paths = frozenset(health_check_paths)
        else:
            self.health_check_paths = self.DEFAULT_HEALTH_CHECK_PATHS

        self.www_redirect = www_redirect

    def _is_health_check_path(self, path: str) -> bool:
        """Check if the request path is a health check endpoint using exact matching."""
        return path in self.health_check_paths

    def _is_valid_host(self, host: str) -> bool:
        """
        Check if the host is in the allowed hosts list (optimized for performance).

        Uses pre-compiled regex patterns for wildcard matching and set lookup
        for exact matches to minimize performance overhead.
        """
        if self.allow_all:
            return True

        host_lower = host.lower()

        # Check exact matches first (O(1) lookup)
        if host_lower in self.exact_hosts:
            return True

        # Check wildcard patterns (O(n) where n = number of wildcard patterns)
        for pattern in self.wildcard_patterns:
            if pattern.match(host_lower):
                return True

        return False

    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        """
        Process the request and validate host header unless it's a health check.

        Args:
            request: The incoming request
            call_next: The next endpoint to call

        Returns:
            The response from the next middleware or endpoint
        """
        # Skip host validation for health check paths
        if self._is_health_check_path(request.url.path):
            return await call_next(request)

        # For all other paths, validate the host header
        host = request.headers.get("host", "")

        if not self._is_valid_host(host):
            return PlainTextResponse("Invalid host header", status_code=400)

        return await call_next(request)
