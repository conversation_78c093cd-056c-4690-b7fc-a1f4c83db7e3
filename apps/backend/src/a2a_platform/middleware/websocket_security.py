"""
WebSocket security middleware for FastAPI applications.

This middleware enforces WSS protocol for WebSocket connections and
handles secure WebSocket upgrade validation.
"""

from typing import Callable, Optional, Union

from fastapi import Request, Response, status
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.responses import PlainTextResponse
from starlette.types import ASGIApp


class WebSocketSecurityMiddleware(BaseHTTPMiddleware):
    """
    Middleware to enforce WSS protocol for WebSocket connections.

    This middleware checks WebSocket connection requests and ensures they
    use the secure WSS protocol rather than WS.
    """

    def __init__(
        self,
        app: ASGIApp,
        websocket_paths: Optional[list[str]] = None,
    ) -> None:
        """
        Initialize the WebSocket security middleware.

        Args:
            app: The ASGI application
            websocket_paths: List of paths that accept WebSocket connections
        """
        super().__init__(app)
        self.websocket_paths = websocket_paths or ["/graphql"]

    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Union[Response, PlainTextResponse]:
        """
        Process the request and enforce WSS for WebSocket connections.

        Args:
            request: The incoming request
            call_next: The next endpoint to call

        Returns:
            Either an error response for insecure WebSocket or the original response
        """
        # Check if this is a WebSocket upgrade request
        if request.headers.get("Upgrade", "").lower() == "websocket":
            # Check if the path is a WebSocket path
            if any(request.url.path.startswith(path) for path in self.websocket_paths):
                # Check if the connection is using WSS
                proto = request.headers.get("X-Forwarded-Proto", "")

                # If not using HTTPS/WSS, reject the connection
                if proto == "http" or (not proto and request.url.scheme == "http"):
                    # Return 426 Upgrade Required to indicate the client needs to use WSS
                    return PlainTextResponse(
                        "WebSocket connections require WSS protocol",
                        status_code=status.HTTP_426_UPGRADE_REQUIRED,
                        headers={"Upgrade": "websocket", "Connection": "Upgrade"},
                    )

        # If not a WebSocket request or already using WSS, proceed
        return await call_next(request)


def get_websocket_security_middleware(
    websocket_paths: Optional[list[str]] = None,
) -> Callable[[ASGIApp], WebSocketSecurityMiddleware]:
    """
    Factory function for creating the WebSocket security middleware.

    Args:
        websocket_paths: List of paths that accept WebSocket connections

    Returns:
        A function that takes an app and returns the configured middleware
    """

    def _add_middleware(app: ASGIApp) -> WebSocketSecurityMiddleware:
        return WebSocketSecurityMiddleware(app, websocket_paths=websocket_paths)

    return _add_middleware
