# Ensure this is before any application-specific imports that might trigger settings loading,
# especially before:
# from a2a_platform.config import get_settings
# from a2a_platform.api.graphql import graphql_app
# or any similar lines that would lead to Pydantic's Settings() being called.
from pathlib import Path
from typing import Any

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from fastapi.staticfiles import StaticFiles

from a2a_platform.api.graphql import graphql_app
from a2a_platform.api.rest import api_router
from a2a_platform.config.settings import get_settings
from a2a_platform.middleware import (
    # HTTPSRedirectMiddleware,  # Temporarily disabled
    SecurityHeadersMiddleware,
    WebSocketSecurityMiddleware,
)
from a2a_platform.middleware.conditional_trusted_host import (
    ConditionalTrustedHostMiddleware,
)

# Get settings
settings = get_settings()

app = FastAPI(title=settings.APP_TITLE)

# Add middlewares - order matters (CORS first to ensure headers are preserved)
# 1. First add CORS middleware to ensure headers are not overridden
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins_list,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
# 2. Then validate trusted hosts (exclude health check routes)
app.add_middleware(
    ConditionalTrustedHostMiddleware,
    allowed_hosts=settings.trusted_hosts_list,
)
# 3. Then redirect HTTP to HTTPS (exclude API routes since they come through Cloudflare HTTPS)
# TEMPORARILY DISABLED for debugging - Cloudflare handles HTTPS
# app.add_middleware(
#     HTTPSRedirectMiddleware, exclude_paths=["/api", "/graphql", "/debug"]
# )
# 4. Then enforce WSS for WebSocket connections
app.add_middleware(WebSocketSecurityMiddleware)
# 5. Finally add security headers to all responses
app.add_middleware(SecurityHeadersMiddleware)

# Include GraphQL app as a router
app.include_router(graphql_app, prefix="/graphql")

# Include REST API routes
app.include_router(api_router, prefix="/api")

# Get the directory of the current file
current_dir = Path(__file__).parent
static_dir = current_dir / "static"

# Mount static files directory
app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")


# Debug endpoint to check configuration
@app.get("/debug/config", include_in_schema=False)
async def debug_config() -> dict[str, Any]:
    """Debug endpoint to check current configuration."""
    import os

    # Only allow in non-production environments for security
    if settings.ENVIRONMENT.lower() == "production":
        raise HTTPException(
            status_code=404, detail="Debug endpoints are not available in production"
        )

    config = {
        "trusted_hosts_raw": os.environ.get("TRUSTED_HOSTS", "NOT_SET"),
        "trusted_hosts_list": settings.trusted_hosts_list,
        "cors_origins_raw": os.environ.get("CORS_ORIGINS", "NOT_SET"),
        "cors_origins_list": settings.cors_origins_list,
        "environment": settings.ENVIRONMENT,
    }

    # Only include database URLs in development environment
    if settings.ENVIRONMENT.lower() == "development":
        config.update(
            {
                "database_url": settings.DATABASE_URL,
                "async_database_url": settings.ASYNC_DATABASE_URL,
            }
        )
    else:
        config.update(
            {
                "database_url": "[REDACTED - Not shown in staging/production]",
                "async_database_url": "[REDACTED - Not shown in staging/production]",
            }
        )

    return config


# Test endpoint to compare CORS behavior with GraphQL
@app.post("/debug/test-cors", include_in_schema=False)
async def test_cors() -> dict[str, str]:
    """Test endpoint to verify CORS headers are working for regular FastAPI endpoints."""
    # Only allow in non-production environments for security
    if settings.ENVIRONMENT.lower() == "production":
        raise HTTPException(
            status_code=404, detail="Debug endpoints are not available in production"
        )

    return {"message": "CORS test successful", "timestamp": "2025-01-07"}


# Test GraphQL components individually
@app.post("/debug/test-graphql-components", include_in_schema=False)
async def test_graphql_components() -> dict[str, str]:
    """Test GraphQL components individually to isolate the issue."""
    import traceback

    # Only allow in non-production environments for security
    if settings.ENVIRONMENT.lower() == "production":
        raise HTTPException(
            status_code=404, detail="Debug endpoints are not available in production"
        )

    results = {}

    # Test 1: Can we import GraphQL modules?
    try:
        from a2a_platform.api.graphql import schema

        schema_type = str(type(schema))
        results["schema_import"] = f"✅ Success - {schema_type}"
    except Exception as e:
        results["schema_import"] = f"❌ Error: {e}"
        results["schema_import_traceback"] = traceback.format_exc()

    # Test 2: Can we create a GraphQL context?
    try:
        from a2a_platform.api.graphql.middleware.auth_middleware import (
            get_graphql_context,
        )
        from a2a_platform.db import get_db_session
        from fastapi import Request

        # Create a mock request
        scope = {"type": "http", "method": "POST", "path": "/test", "headers": []}
        mock_request = Request(scope)

        # This will test if database session creation works
        db_gen = get_db_session()
        db_session = await db_gen.__anext__()

        results["db_session"] = "✅ Database session created"

        # Test context creation
        context = await get_graphql_context(mock_request, db_session)
        results["graphql_context"] = f"✅ GraphQL context created - {type(context)}"

        # Clean up
        await db_session.close()

    except Exception as e:
        results["graphql_context"] = f"❌ Error: {e}"
        results["graphql_context_traceback"] = traceback.format_exc()

    return results


# Serve favicon.ico directly from the root path
@app.get("/favicon.ico", include_in_schema=False)
@app.head("/favicon.ico", include_in_schema=False)
async def get_favicon() -> FileResponse:
    favicon_path = static_dir / "favicon.ico"
    return FileResponse(favicon_path)
