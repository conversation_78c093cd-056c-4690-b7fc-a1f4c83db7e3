# AI-Powered Personal Assistant Response Generation

This document describes the AI-powered response generation system for Personal Assistants in the A2A Platform.

## Overview

The AI response generation system automatically creates intelligent responses when users send messages to their Personal Assistants. The system uses **PydanticAI framework** to provide vendor-agnostic LLM integration with support for multiple providers including OpenAI, Anthropic Claude, and Google Gemini. It includes robust error handling, rate limiting, and configuration options.

## Architecture

### Core Components

1. **PydanticAIResponseService** (`pydantic_ai_response_service.py`)
   - **NEW**: Uses PydanticAI framework for vendor-agnostic LLM integration
   - Supports OpenAI GPT, Anthropic Claude, and Google Gemini models
   - Manages rate limiting and error handling
   - Provides fallback responses when AI is unavailable
   - Unified interface across all LLM providers

2. **PAResponseService** (`pa_response_service.py`)
   - Orchestrates the complete response generation flow
   - Manages conversation context and assistant configuration
   - Handles asynchronous response generation
   - **UPDATED**: Now uses PydanticAIResponseService

3. **Chat Resolvers Integration**
   - Automatically triggers PA response generation after user messages
   - Non-blocking implementation to avoid affecting user message flow

## Configuration

### Environment Variables

```bash
# AI Service Configuration
OPENAI_API_KEY="sk-your-openai-key"
ANTHROPIC_API_KEY="sk-ant-your-anthropic-key"
GEMINI_API_KEY="AIza-your-gemini-key"
GOOGLE_API_KEY="AIza-your-google-key"  # Alternative for Gemini
AI_RESPONSE_ENABLED=true
AI_RESPONSE_TIMEOUT=30
AI_RATE_LIMIT_CALLS=50
AI_RATE_LIMIT_WINDOW=60
AI_DEFAULT_PROVIDER="openai"  # Default provider: openai, anthropic, google
```

### Assistant Configuration

Each assistant can be configured with AI-specific settings in the `configuration` field:

```json
{
  "ai": {
    "enabled": true,
    "model": "anthropic:claude-opus-4-20250514",
    "temperature": 0.7,
    "max_tokens": 500,
    "min_message_length": 3,
    "response_probability": 1.0,
    "system_prompt_template": "You are {name}, a helpful assistant. {backstory}"
  }
}
```

#### Configuration Options

- `enabled`: Enable/disable AI responses for this assistant
- `model`: LLM model to use (e.g., "gpt-4.1", "o3", "o4-mini", "claude-opus-4-20250514", "claude-sonnet-4-20250514", "gemini-2.5-flash-preview")
- `temperature`: Response creativity (0.0-2.0)
- `max_tokens`: Maximum response length
- `min_message_length`: Minimum user message length to trigger response
- `response_probability`: Probability of generating a response (0.0-1.0)
- `system_prompt_template`: Template for system prompt with {name} and {backstory} placeholders

## Response Generation Flow

1. **User sends message** → GraphQL mutation `sendMessage`
2. **Message persisted** → ChatService stores user message
3. **PA response triggered** → PAResponseService scheduled asynchronously
4. **Context gathered** → Recent conversation history retrieved
5. **AI request made** → LLM API called with context and assistant configuration
6. **Response generated** → AI response or fallback response selected
7. **PA message sent** → ChatService stores PA response with metadata
8. **Real-time update** → Subscription notification sent to frontend

## Supported LLM Providers

### OpenAI (**UPDATED - June 2025**)
- **Models**: GPT-4.1 (flagship), o3 (reasoning), o4-mini (affordable reasoning), GPT-4.1-mini, GPT-4.1-nano, GPT-4o (latest), GPT-4o-mini, o3-mini, o1 (legacy reasoning), GPT-4-turbo, GPT-3.5-turbo
- **Configuration**: `"openai:gpt-4.1"`, `"openai:o3"`, `"openai:o4-mini"`, `"openai:gpt-4o"`, `"openai:gpt-4.1-mini"`
- **API**: https://api.openai.com/v1/chat/completions
- **Authentication**: Bearer token via `OPENAI_API_KEY`
- **Special Features**: GPT-4.1 flagship model, o3/o4 reasoning models, Realtime API, Audio capabilities, Computer use tools

### Anthropic Claude (**UPDATED - May 2025**)
- **Models**: Claude Opus 4 (claude-opus-4-20250514), Claude Sonnet 4 (claude-sonnet-4-20250514), Claude-3.5-sonnet-latest, Claude-3.5-haiku (latest), Claude-3.5-sonnet, Claude-3-haiku, Claude-3-sonnet, Claude-3-opus, Claude-3-7-sonnet-20250219
- **Configuration**: `"anthropic:claude-opus-4-20250514"`, `"anthropic:claude-sonnet-4-20250514"`, `"anthropic:claude-3-5-sonnet-latest"`, `"anthropic:claude-3-5-haiku"`
- **API**: https://api.anthropic.com/v1/messages
- **Authentication**: x-api-key header via `ANTHROPIC_API_KEY`
- **Special Features**: Claude 4 advanced reasoning, message batches, function calling, streaming responses, enhanced multimodal capabilities

### Google Gemini (**UPDATED - June 2025**)
- **Models**: Gemini-2.5-flash-preview, Gemini-2.5-pro-preview, Gemini-2.0-flash-001 (flagship stable), Gemini-2.0-flash, Gemini-1.5-pro, Gemini-1.5-flash
- **Configuration**:
  - Generative Language API: `"google-gla:gemini-2.5-flash-preview"`, `"google-gla:gemini-2.5-pro-preview"`, `"google-gla:gemini-2.0-flash-001"`, `"google-gla:gemini-1.5-pro"`
  - Vertex AI: `"google-vertex:gemini-2.5-flash-preview"`, `"google-vertex:gemini-2.5-pro-preview"`, `"google-vertex:gemini-2.0-flash-001"`, `"google-vertex:gemini-1.5-pro"`
- **API**:
  - Generative Language API: https://generativelanguage.googleapis.com/
  - Vertex AI: https://cloud.google.com/vertex-ai
- **Authentication**: API key via `GEMINI_API_KEY` or `GOOGLE_API_KEY`
- **Special Features**: Gemini 2.5 enhanced performance, function calling, file uploads, caching, JSON schema responses, advanced multimodal capabilities

## Model Recommendations

### Current Best Models (June 2025)
- **Premium Flagship**: `claude-opus-4-20250514` - Latest Claude 4 with superior reasoning and comprehension capabilities
- **OpenAI Flagship**: `gpt-4.1` - Latest GPT flagship model for complex tasks and advanced reasoning
- **Top Reasoning**: `o3` - Most powerful reasoning model for complex multi-step problems
- **Balanced Premium**: `claude-sonnet-4-20250514` - Claude 4 Sonnet for optimal performance-cost balance
- **Speed Leader**: `gemini-2.5-flash-preview` - Fastest responses with enhanced multimodal processing
- **Cost-Effective Reasoning**: `o4-mini` - Faster, more affordable reasoning model
- **Stable Production**: `gemini-2.0-flash-001` - Reliable flagship for production workloads
- **Budget Friendly**: `gpt-4.1-nano` - Fastest, most cost-effective GPT-4.1 model

### Use Case Specific Recommendations
- **Complex Reasoning**: `o3` or `claude-opus-4-20250514` for advanced analysis and problem-solving
- **Flagship Performance**: `gpt-4.1` for general-purpose complex tasks with latest capabilities
- **Fast Reasoning**: `o4-mini` for quick reasoning tasks at lower cost
- **Real-time Chat**: `gpt-4o-realtime-preview` for live voice/text interactions
- **Batch Processing**: `claude-sonnet-4-20250514` with message batches for high throughput
- **Document Analysis**: `gemini-2.5-pro-preview` with enhanced multimodal capabilities for complex content
- **Speed-Critical Apps**: `gemini-2.5-flash-preview` for ultra-fast response times
- **Code Generation**: `gpt-4.1` or `o3` for complex programming tasks
- **Cost-Optimized**: `gpt-4.1-mini` or `o4-mini` for balanced intelligence, speed, and cost
- **JSON/Structured Responses**: `gemini-2.5-flash-preview` with schema validation for reliable data extraction

## Error Handling

### Graceful Degradation
- API failures → Fallback responses used
- Rate limiting → Fallback responses used
- Network timeouts → Fallback responses used
- Invalid configuration → Default settings applied

### Fallback Responses
When AI generation fails, the system uses predefined fallback responses:
- "I understand you're reaching out. Let me think about that and get back to you."
- "Thank you for your message. I'm processing your request."
- "I'm here to help! Let me consider the best way to assist you."

### Error Logging
All errors are logged with appropriate context:
- API failures with status codes
- Rate limiting events
- Configuration issues
- Network timeouts

## Rate Limiting

### Default Limits
- 50 API calls per minute per service instance
- Configurable via environment variables
- Per-service instance (not per-user)

### Rate Limit Handling
- Calls beyond limit → Fallback responses
- Time-based window reset
- Graceful degradation without user impact

## Security Considerations

### API Key Management
- Environment variable storage
- No API keys in code or logs
- Separate keys for different environments

### Content Filtering
- User message sanitization
- Response content validation
- Metadata security (no sensitive data)

### Privacy
- User ID redaction in logs
- Conversation context limited to recent messages
- No persistent storage of API requests/responses

## Performance Optimization

### Asynchronous Processing
- Non-blocking response generation
- Background task scheduling
- User message flow unaffected by AI processing

### Context Management
- Limited conversation history (last 15 messages)
- Efficient message content extraction
- Optimized database queries

### Caching Considerations
- No response caching (each response is contextual)
- Configuration caching at assistant level
- Rate limiter state in memory

## Monitoring and Metrics

### Key Metrics
- Response generation success rate
- API response times
- Rate limiting events
- Fallback response usage
- Error rates by type

### Logging
- Structured logging with context
- Response generation events
- API call outcomes
- Performance metrics

## Usage Examples

### Basic Response Generation
```python
from a2a_platform.services.pa_response_service import PAResponseService

# In GraphQL resolver after user message is saved
pa_service = PAResponseService(db_session)
pa_service.schedule_response_generation(
    conversation_id=conversation_id,
    user_message=user_message,
    user_id=user_id
)
```

### Direct AI Service Usage
```python
from a2a_platform.services.pydantic_ai_response_service import PydanticAIResponseService

ai_service = PydanticAIResponseService()
response_text, is_ai_generated = await ai_service.generate_response(
    assistant=assistant,
    user_message="Hello, how are you?",
    conversation_history=recent_messages
)
```

### Configuration Example
```python
# Assistant with custom AI configuration
assistant = Assistant(
    name="MyBot",
    backstory="I am a helpful customer service assistant.",
    configuration={
        "ai": {
            "enabled": True,
            "model": "gpt-4",
            "temperature": 0.5,
            "max_tokens": 300,
            "system_prompt_template": "You are {name}, a professional {backstory}"
        }
    }
)
```

## Testing

### Unit Tests
- AI service functionality
- Rate limiting behavior
- Configuration handling
- Error scenarios

### Integration Tests
- Complete response flow
- Database interactions
- Async task handling
- Fallback scenarios

### Test Configuration
```python
# Test with mocked AI responses
with patch.object(PydanticAIResponseService, 'generate_response') as mock_ai:
    mock_ai.return_value = ("Test response", True)
    # Test PA response generation
```

## Troubleshooting

### Common Issues

1. **No responses generated**
   - Check `AI_RESPONSE_ENABLED` setting
   - Verify assistant configuration has `ai.enabled: true`
   - Check API key configuration

2. **Only fallback responses**
   - Verify API keys are valid
   - Check rate limiting status
   - Review error logs for API failures

3. **Slow response times**
   - Check API timeout settings
   - Monitor rate limiting
   - Review conversation context size

### Debug Mode
Enable debug logging to see detailed response generation flow:
```bash
LOG_LEVEL=debug
```

## Future Enhancements

### Planned Features
- Response caching for similar queries
- Custom prompt templates per assistant
- Response quality scoring
- A/B testing for different models
- User feedback integration
- Multi-language support

### Scalability Improvements
- Distributed rate limiting
- Response queue management
- Load balancing across API providers
- Response streaming for long outputs
