# A2A Communication Guide

This document provides guidance on how to use the A2A (Agent-to-Agent) communication system in the VEDAVIVI platform.

## Overview

The A2A communication system allows Personal Agents (PAs) to communicate with Specialized Agents (SAs) in both synchronous (HTTP) and asynchronous (queue-based) modes. All A2A communication must include a standardized user context that contains the user's ID, the initiating agent's ID, and a timestamp.

## User Context

All A2A messages must include a `user_context` object with the following fields:

- `user_id`: The unique identifier of the end-user
- `initiating_agent_id`: The identifier of the agent initiating the request
- `request_timestamp`: Timestamp of when the context was generated for the request

This context is used to:
1. Ensure proper authorization and scoping of data access
2. Provide an audit trail for A2A communication
3. Enable future interoperability with external A2A protocols

## Message Structure

### Synchronous (HTTP) Communication

For synchronous communication, the request body should follow this structure:

```json
{
  "user_context": {
    "user_id": "user123",
    "initiating_agent_id": "agent456",
    "request_timestamp": "2023-01-01T00:00:00+00:00"
  },
  "payload": {
    "action": "summarize",
    "data": {
      "text": "This is a sample text to summarize."
    }
  }
}
```

### Asynchronous (Queue-based) Communication

For asynchronous communication, the message body should follow the same structure:

```json
{
  "user_context": {
    "user_id": "user123",
    "initiating_agent_id": "agent456",
    "request_timestamp": "2023-01-01T00:00:00+00:00"
  },
  "payload": {
    "action": "analyze",
    "data": {
      "document_id": "doc123",
      "analysis_type": "sentiment"
    }
  }
}
```

## Code Examples

### Sending Synchronous A2A Messages (PA to SA)

```python
from a2a_platform.services.a2a_sync_client import A2ASyncClient

async def call_specialized_agent():
    client = A2ASyncClient()

    # Prepare the payload
    payload = {
        "action": "summarize",
        "data": {
            "text": "This is a sample text to summarize."
        }
    }

    # Call the specialized agent
    response = await client.call_specialized_agent(
        endpoint_url="http://example.com/agent",
        payload=payload,
        user_id="user123",
        initiating_agent_id="agent456"
    )

    return response
```

### Sending Asynchronous A2A Messages (PA to SA)

```python
from a2a_platform.messaging.a2a_producer import A2AMessageProducer
from a2a_platform.messaging.queue_client import PubSubClient

async def send_async_message():
    # Create a queue client
    queue_client = PubSubClient()

    # Create a producer
    producer = A2AMessageProducer(queue_client=queue_client)

    # Prepare the payload
    payload = {
        "action": "analyze",
        "data": {
            "document_id": "doc123",
            "analysis_type": "sentiment"
        }
    }

    # Send the message
    message_id = await producer.send_message(
        queue_name="analysis_queue",
        payload=payload,
        user_id="user123",
        initiating_agent_id="agent456"
    )

    return message_id
```

### Receiving Synchronous A2A Messages (SA from PA)

```python
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from a2a_platform.api.rest.dependencies import get_db
from a2a_platform.schemas.a2a_context import UserContext
from a2a_platform.services.a2a_context_service import A2AContextService

router = APIRouter()

@router.post("/process")
async def process_request(request_data: dict, db: AsyncSession = Depends(get_db)):
    # Extract and validate the user context
    user_context, error_msg = A2AContextService.extract_user_context(request_data)
    if not user_context:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid user context in request: {error_msg}"
        )

    # Extract the payload
    payload = request_data.get("payload", {})

    # Process the request
    # ...

    # Return the response
    return {
        "status": "success",
        "data": {
            "result": "Processed successfully"
        }
    }
```

### Receiving Asynchronous A2A Messages (SA from PA)

```python
from a2a_platform.messaging.a2a_consumer import A2AMessageConsumer
from a2a_platform.messaging.queue_client import PubSubClient
from a2a_platform.schemas.a2a_context import UserContext

class MyMessageConsumer(A2AMessageConsumer):
    async def _handle_message(self, user_context: UserContext, payload: dict) -> bool:
        # Process the message
        action = payload.get("action")

        if action == "analyze":
            # Perform analysis
            document_id = payload.get("data", {}).get("document_id")
            analysis_type = payload.get("data", {}).get("analysis_type")

            # ... perform analysis ...

            return True

        return False

async def start_consumer():
    # Create a queue client
    queue_client = PubSubClient()

    # Create a consumer
    consumer = MyMessageConsumer(queue_client=queue_client)

    # Start consuming messages
    await consumer.start_consumer("analysis_queue")
```

## Error Handling

When validating user context, the system provides detailed error messages to help diagnose issues:

- Missing user_context: "No user_context found in A2A message"
- Missing required fields: "Missing required fields in user_context: user_id, initiating_agent_id"
- Validation errors: "Validation error in user_context: ..."

For asynchronous messaging, invalid messages are logged and can be configured to be sent to a dead-letter queue for later analysis.

## Security Considerations

- Always validate the user_id against your user database before processing requests
- Consider implementing additional authorization checks based on the initiating_agent_id
- Use secure communication channels (HTTPS, encrypted queues) for all A2A communication
- Do not log sensitive user IDs in production environments, or implement proper redaction

## Future Interoperability

The user context structure has been designed with future interoperability in mind. It can be embedded in broader A2A protocols as they emerge in the industry.
