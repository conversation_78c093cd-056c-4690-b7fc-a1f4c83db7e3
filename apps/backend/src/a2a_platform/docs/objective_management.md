# Objective Management

This document describes how user objectives are identified, stored, and managed in the A2A Platform.

## Overview

The A2A Platform allows users to define objectives via chat with their Personal Assistant (PA). These objectives are stored in the database and can be tracked and referenced by the PA.

## Objective Identification (US1.7)

When a user sends a message to their PA, the system analyzes the message to identify if it contains an objective. This is implemented in the `ChatProcessorService` class.

The service uses pattern matching to identify common phrases that indicate a user is defining an objective, such as:
- "My goal is to..."
- "I want to..."
- "Help me..."
- "I need to..."

In a production environment, this would likely use more sophisticated NLP techniques.

## Objective Storage (US5.3)

Once an objective is identified, it is stored in the `assistant_objectives` table using the `ObjectiveService`. Each objective is linked to the assistant (and by extension, the user) who received the message.

The stored objective includes:
- The objective text as defined by the user
- A link to the assistant (assistant_id)
- A status (default: "active")
- Timestamps (created_at, updated_at)
- Optional metadata

## Database Schema

The `assistant_objectives` table has the following schema:

| Column Name | Data Type | Constraints | Notes |
| :---- | :---- | :---- | :---- |
| id | UUID | PRIMARY KEY, DEFAULT gen\_random\_uuid() | Unique identifier for the objective. |
| assistant\_id | UUID | NOT NULL, REFERENCES assistants(id) ON DELETE CASCADE | The assistant this objective belongs to. |
| objective\_text | TEXT | NOT NULL | The textual description of the objective. |
| status | TEXT | NOT NULL, DEFAULT 'active', CHECK (status IN ('active', 'completed', 'cancelled')) | Current status (managed via chat). |
| created\_at | TIMESTAMPTZ | NOT NULL, DEFAULT now() | Timestamp of creation. |
| updated\_at | TIMESTAMPTZ | NOT NULL, DEFAULT now() | Timestamp of last update (e.g., status change). |
| completed\_at | TIMESTAMPTZ | NULL | Timestamp when status became 'completed'. |
| metadata\_json | JSONB | NULL, DEFAULT '{}'::jsonb | Flexible storage for any future objective metadata. |

## Code Structure

The objective management functionality is implemented in the following files:

- `apps/backend/src/a2a_platform/db/models/assistant_objective.py`: SQLAlchemy model for the `assistant_objectives` table
- `apps/backend/src/a2a_platform/schemas/objective_schemas.py`: Pydantic schemas for objective data
- `apps/backend/src/a2a_platform/services/objective_service.py`: Service for storing and retrieving objectives
- `apps/backend/src/a2a_platform/services/chat_processor_service.py`: Service for processing chat messages and identifying objectives

## Usage Example

```python
from a2a_platform.services.chat_processor_service import ChatProcessorService

async def process_user_message(message_text: str, assistant_id: uuid.UUID, db_session):
    # Create a chat processor service
    chat_processor = ChatProcessorService(db_session)

    # Process the message
    objective_identified, stored_objective = await chat_processor.process_message(
        message_text, assistant_id
    )

    if objective_identified:
        # An objective was identified and stored
        print(f"Stored objective: {stored_objective.objective_text}")
    else:
        # No objective was identified
        print("No objective identified in the message")
```

## Future Enhancements

Future enhancements to objective management may include:
- More sophisticated objective identification using NLP
- Objective status tracking (e.g., marking objectives as completed or cancelled)
- Linking objectives to specific tasks
- User interfaces for viewing and managing objectives
