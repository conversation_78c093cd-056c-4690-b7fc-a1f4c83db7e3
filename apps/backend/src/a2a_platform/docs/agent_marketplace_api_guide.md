# Agent Marketplace API Guide

This document provides a comprehensive guide for developers on how to use the Agent Marketplace API. The Agent Marketplace allows developers to register, update, and manage their agents, and for users to discover and use these agents.

## Authentication

All Agent Marketplace API endpoints require authentication. You need to include a valid JWT token in the `Authorization` header of your GraphQL requests:

```
Authorization: Bearer <your_jwt_token>
```

## Authorization

The Agent Marketplace API uses role-based authorization:

- **Admin**: Can approve/reject agents, view all agents, and manage the marketplace
- **Developer**: Can register new agents, update their own agents, and view all active agents
- **User**: Can only view active and approved agents

## GraphQL Endpoint

The GraphQL API is available at:

```
/graphql
```

## Queries

### List Marketplace Agents

Retrieves a list of agents in the marketplace.

```graphql
query ListMarketplaceAgents($status: String) {
  listMarketplaceAgents(status: $status) {
    agentDefinitionId
    name
    description
    version
    developerId
    reviewStatus
    status
    skills {
      name
      description
    }
    pricingInfo {
      pricingType
      currency
      amountMonthly
      amountPerCall
      features
    }
    createdAt
    updatedAt
  }
}
```

Parameters:
- `status` (optional): Filter by agent status (`active` or `inactive`)

Notes:
- Regular users can only see active agents
- Developers can see their own agents regardless of status
- <PERSON>mins can see all agents

### Get Marketplace Agent

Retrieves a specific agent by ID.

```graphql
query GetMarketplaceAgent($agentDefinitionId: String!) {
  getMarketplaceAgent(agentDefinitionId: $agentDefinitionId) {
    agentDefinitionId
    name
    description
    version
    developerId
    reviewStatus
    status
    skills {
      name
      description
    }
    pricingInfo {
      pricingType
      currency
      amountMonthly
      amountPerCall
      features
    }
    createdAt
    updatedAt
  }
}
```

Parameters:
- `agentDefinitionId` (required): The unique identifier of the agent

## Mutations

### Register Marketplace Agent

Registers a new agent in the marketplace.

```graphql
mutation RegisterMarketplaceAgent($input: RegisterMarketplaceAgentInput!) {
  registerMarketplaceAgent(input: $input) {
    agentDefinitionId
    name
    description
    version
    developerId
    reviewStatus
    status
    skills {
      name
      description
    }
    createdAt
    updatedAt
  }
}
```

Input:
```graphql
input RegisterMarketplaceAgentInput {
  agentDefinitionId: String!
  name: String!
  description: String
  version: String!
  developerId: String!
  skills: [AgentSkillInput!]!
  pricingInfo: AgentPricingInfoInput
  endpointUrl: String
  asyncQueueName: String
}

input AgentSkillInput {
  name: String!
  description: String
  parametersSchema: String
}

input AgentPricingInfoInput {
  pricingType: String!
  currency: String
  amountMonthly: Float
  amountPerCall: Float
  features: [String!]
  tierId: String
}
```

Notes:
- Only users with the `DEVELOPER` role can register agents
- New agents start with `reviewStatus: "pending"` and `status: "inactive"`
- Agents must be approved by an admin before they become active

### Update Marketplace Agent

Updates an existing agent in the marketplace.

```graphql
mutation UpdateMarketplaceAgent($input: UpdateMarketplaceAgentInput!) {
  updateMarketplaceAgent(input: $input) {
    agentDefinitionId
    name
    description
    version
    developerId
    reviewStatus
    status
    skills {
      name
      description
    }
    createdAt
    updatedAt
  }
}
```

Input:
```graphql
input UpdateMarketplaceAgentInput {
  agentDefinitionId: String!
  name: String
  description: String
  version: String
  skills: [AgentSkillInput!]
  pricingInfo: AgentPricingInfoInput
  status: String
  endpointUrl: String
  asyncQueueName: String
}
```

Notes:
- Developers can only update their own agents
- Admins can update any agent
- Updating an approved agent may require re-approval (depends on implementation)

### Update Agent Review Status

Updates the review status of an agent.

```graphql
mutation UpdateAgentReviewStatus($agentDefinitionId: String!, $reviewStatus: String!) {
  updateAgentReviewStatus(agentDefinitionId: $agentDefinitionId, reviewStatus: $reviewStatus) {
    agentDefinitionId
    name
    description
    version
    developerId
    reviewStatus
    status
    skills {
      name
      description
    }
    createdAt
    updatedAt
  }
}
```

Parameters:
- `agentDefinitionId` (required): The unique identifier of the agent
- `reviewStatus` (required): The new review status (`pending`, `approved`, or `rejected`)

Notes:
- Only admins can update the review status
- Setting `reviewStatus` to `approved` automatically sets `status` to `active`
- Setting `reviewStatus` to `rejected` automatically sets `status` to `inactive`

## Error Handling

The API returns GraphQL errors with descriptive messages. Common error scenarios include:

- **Authentication Errors**: When the user is not authenticated
  ```json
  {
    "errors": [
      {
        "message": "Authentication required for this operation.",
        "path": ["listMarketplaceAgents"]
      }
    ]
  }
  ```

- **Authorization Errors**: When the user doesn't have the required permissions
  ```json
  {
    "errors": [
      {
        "message": "Permission denied: Only admins can update agent review status.",
        "path": ["updateAgentReviewStatus"]
      }
    ]
  }
  ```

- **Validation Errors**: When the input data is invalid
  ```json
  {
    "errors": [
      {
        "message": "Validation error: Review status must be one of: pending, approved, rejected",
        "path": ["updateAgentReviewStatus"]
      }
    ]
  }
  ```

- **Not Found Errors**: When the requested agent doesn't exist
  ```json
  {
    "errors": [
      {
        "message": "Agent with ID 'non_existent_agent' not found.",
        "path": ["getMarketplaceAgent"]
      }
    ]
  }
  ```

## Examples

### Register a New Agent

```graphql
mutation {
  registerMarketplaceAgent(input: {
    agentDefinitionId: "summarizer_v1",
    name: "Text Summarizer",
    description: "An agent that summarizes text content",
    version: "1.0.0",
    developerId: "dev123",
    skills: [
      {
        name: "summarize",
        description: "Summarizes text content"
      }
    ],
    pricingInfo: {
      pricingType: "subscription",
      currency: "USD",
      amountMonthly: 999
    },
    endpointUrl: "https://api.example.com/agents/summarizer"
  }) {
    agentDefinitionId
    name
    reviewStatus
    status
  }
}
```

### Update an Agent's Review Status

```graphql
mutation {
  updateAgentReviewStatus(
    agentDefinitionId: "summarizer_v1",
    reviewStatus: "approved"
  ) {
    agentDefinitionId
    name
    reviewStatus
    status
  }
}
```
