#!/usr/bin/env python3
"""Debug test to check the specialized agent endpoint."""

from datetime import UTC, datetime
from fastapi.testclient import TestClient
from a2a_platform.main import app

client = TestClient(app)

def test_endpoint():
    """Simple test of the specialized agent endpoint."""
    
    # Create a simple request payload
    request_data = {
        "user_context": {
            "user_id": "user123",
            "initiating_agent_id": "agent456", 
            "request_timestamp": datetime.now(UTC).isoformat(),
        },
        "payload": {"action": "test_action", "data": {"key": "value"}},
    }
    
    print("Making request to endpoint...")
    response = client.post(
        "/api/internal/specialized-agent/process",
        json=request_data,
        headers={
            "Content-Type": "application/json",
            "X-Forwarded-Proto": "https"
        },
    )
    
    print(f"Response status: {response.status_code}")
    print(f"Response headers: {response.headers}")
    print(f"Response text: {response.text}")
    
    if response.status_code == 200:
        print("SUCCESS: Endpoint is working!")
    else:
        print("ERROR: Endpoint failed")

if __name__ == "__main__":
    test_endpoint()
