import type { Preview } from "@storybook/react-vite";
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/clerk-react";
import "../src/index.css";

// Global Clerk configuration for all stories
const publishableKey = "pk_test_bWFzdGVyLXdlcmV3b2xmLTc5LmNsZXJrLmFjY291bnRzLmRldiQ";

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
       color: /(background|color)$/i,
       date: /Date$/i,
      },
    },

    backgrounds: {
      default: "light",
      values: [
        {
          name: "light",
          value: "#ffffff",
        },
        {
          name: "dark",
          value: "#1a1a1a",
        },
      ],
    },
    viewport: {
      viewports: {
        mobile: {
          name: "Mobile",
          styles: {
            width: "375px",
            height: "667px",
          },
        },
        tablet: {
          name: "Tablet",
          styles: {
            width: "768px",
            height: "1024px",
          },
        },
        desktop: {
          name: "Desktop",
          styles: {
            width: "1024px",
            height: "768px",
          },
        },
      },
    },
    docs: {
      toc: true,
    },
  },

  globalTypes: {
    theme: {
      description: "Global theme for components",
      defaultValue: "light",
      toolbar: {
        title: "Theme",
        icon: "circlehollow",
        items: ["light", "dark"],
        dynamicTitle: true,
      },
    },
    clerkState: {
      description: "Mock Clerk authentication state",
      defaultValue: "signedOut",
      toolbar: {
        title: "Auth State",
        icon: "user",
        items: [
          { value: "signedOut", title: "Signed Out" },
          { value: "signedIn", title: "Signed In" },
        ],
        dynamicTitle: true,
      },
    },
  },

  tags: ["autodocs"],

  decorators: [
    (Story, context) => {
      // Global ClerkProvider decorator - only wrap once at the top level
      // Skip ClerkProvider in docs view to avoid conflicts
      if (context.viewMode === "docs") {
        return React.createElement(Story);
      }

      // Mock different authentication states based on global control
      const clerkState = context.globals.clerkState || "signedOut";

      // Create mock Clerk environment
      const mockUser = clerkState === "signedIn" ? {
        id: "user_mock_123",
        emailAddresses: [{ emailAddress: "<EMAIL>" }],
        firstName: "Test",
        lastName: "User",
      } : null;

      // Set up mock Clerk globals for testing
      if (typeof window !== "undefined") {
        window.__CLERK_PUBLISHABLE_KEY__ = publishableKey;
        window.__CLERK_MOCK_USER__ = mockUser;
        window.__CLERK_MOCK_STATE__ = clerkState;
      }

      return React.createElement(
        ClerkProvider,
        {
          publishableKey,
          // Mock props for Storybook environment
          navigate: (to: string) => console.log("Navigate to:", to),
        },
        React.createElement(Story),
      );
    },
  ],
};

export default preview;
