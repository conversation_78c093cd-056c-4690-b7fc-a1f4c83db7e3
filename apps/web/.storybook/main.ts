import type { StorybookConfig } from "@storybook/react-vite";
import { loadEnv } from "vite";
import path from "path";

const config: StorybookConfig = {
  "stories": [
    "../src/**/*.mdx",
    "../src/**/*.stories.@(js|jsx|mjs|ts|tsx)",
  ],

  "addons": [
    "@chromatic-com/storybook",
    "@storybook/addon-docs",
  ],

  "framework": {
    "name": "@storybook/react-vite",
    "options": {},
  },

  "typescript": {
    "check": false,
    "reactDocgen": "react-docgen-typescript",
    "reactDocgenTypescriptOptions": {
      "shouldExtractLiteralValuesFromEnum": true,
      "propFilter": (prop) => (prop.parent ? !/node_modules/.test(prop.parent.fileName) : true),
    },
  },

  "staticDirs": ["../public"],

  "viteFinal": async (config) => {
    // Load environment variables from project root
    const projectRoot = path.resolve(__dirname, "../../../");
    const env = loadEnv(process.env.NODE_ENV || "development", projectRoot, "");

    config.optimizeDeps = config.optimizeDeps || {};
    config.optimizeDeps.include = [
      ...(config.optimizeDeps.include || []),
      "@mdx-js/react",
      "@storybook/addon-docs/blocks",
    ];

    // Make sure Vite finds all the necessary assets in the stories folder
    config.assetsInclude = [/\.svg$/, /\.png$/, /\.jpg$/, /\.jpeg$/, /\.gif$/];

    // Ensure environment variables are available in Storybook
    config.define = config.define || {};

    const clerkKey = env.VITE_CLERK_PUBLISHABLE_KEY;
    if (!clerkKey) {
      throw new Error(
        "VITE_CLERK_PUBLISHABLE_KEY environment variable is required for Storybook.\n" +
        "Please set it in the project root .env file or your environment.",
      );
    }

    config.define["import.meta.env.VITE_CLERK_PUBLISHABLE_KEY"] = JSON.stringify(clerkKey);

    return config;
  },
};
export default config;
