#!/bin/sh
#
# Enhanced entrypoint script for the frontend container
# Handles permission checks and starts the development server

set -e

# First, check permissions
. /app/fix-permissions.sh

# Check if SSL certificates exist when HTTPS is enabled
if [ "$VITE_USE_HTTPS" = "true" ]; then
    echo "Starting development server with HTTPS enabled (via vite.config.ts)..."
    if [ ! -f "/app/ssl-certs/localhost.pem" ] || [ ! -f "/app/ssl-certs/localhost-key.pem" ]; then
        echo "ERROR: SSL certificates not found. Please ensure they are correctly mounted or run ./scripts/generate-ssl-certs.sh if applicable."
        exit 1
    fi
fi

echo "🚀 Starting development server..."
exec bun run dev -- --host 0.0.0.0
