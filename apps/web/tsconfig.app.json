{"compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "verbatimModuleSyntax": true, "moduleDetection": "force", "noEmit": true, "jsx": "react", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@lib/*": ["src/lib/*"], "@components/*": ["src/components/*"], "@pages/*": ["src/pages/*"], "@hooks/*": ["src/hooks/*"], "@assets/*": ["src/assets/*"], "@config/*": ["src/config/*"]}}, "include": ["src/**/*", "src/lib/**/*", "src/components/**/*", "src/pages/**/*", "src/hooks/**/*", "src/config/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx", "**/*.stories.ts", "**/*.stories.tsx", "src/tests/**/*", "src/setupTests.ts", "src/reportWebVitals.ts"]}