# Example GitHub Actions workflow to update schema in CI
name: Update GraphQL Schema

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to fetch schema from'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

jobs:
  update-schema:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install
        working-directory: apps/web

      - name: Update GraphQL Schema
        run: ./scripts/update-graphql-schema.sh "${{ vars.VITE_GRAPHQL_API_URL }}"
        working-directory: apps/web

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v5
        with:
          commit-message: 'chore: update GraphQL schema from ${{ github.event.inputs.environment }}'
          title: 'Update GraphQL Schema from ${{ github.event.inputs.environment }}'
          body: |
            This PR updates the GraphQL schema from the ${{ github.event.inputs.environment }} environment.

            Generated by GitHub Actions workflow.
          branch: update-graphql-schema-${{ github.event.inputs.environment }}
          delete-branch: true
