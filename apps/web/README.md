# A2A Platform - Frontend

This directory contains the frontend application for the A2A Platform. It's built using React, TypeScript, and Vite for fast and efficient development.

## Prerequisites

- [Bun](https://bun.sh/) - Required for package management and running the application

## Getting Started

### Installing Dependencies

To install all the required dependencies, run:

```bash
bun install
```

### Development Mode

To start the development server with hot module replacement (HMR):

```bash
bun run dev
```

The application will be available at https://localhost:5173 (dev), staging.vedavivi.app (staging), and {prod-top-level}.vedavivi.app (prod) by default. This port is configured in the `vite.config.ts` file.

For development with specific host or port settings:

```bash
# Specify host and port
bun run dev -- --host 0.0.0.0 --port 5173
```

### Building for Production

To build the application for production:

```bash
bun run build
```

This will generate optimized assets in the `dist` directory.

### Previewing Production Build

To preview the production build locally:

```bash
bun run preview
```

### Linting

To run the linter:

```bash
bun run lint
```

## Docker Integration

This application is part of a larger system that can be run with Docker Compose. The frontend can be run in both development and production modes within Docker.

### Development Mode with Docker

For development with hot-reloading enabled:

```bash
# From the project root directory
./scripts/dev.sh --build
```

This will:
- Build the frontend container using `Dockerfile.dev`
- Mount your local code as a volume for real-time changes
- Run the Vite dev server with HMR on port 5173
- Make the application available at https://localhost:5173

### Production Mode with Docker

For production preview:

```bash
# From the project root directory
docker compose up frontend
```

This will:
- Build the frontend using the production `Dockerfile`
- Create an optimized production build
- Serve the static files using Vite's preview server
- Make the application available at https://localhost:5173

### Running the Full Stack

To run the entire system with all services:

```bash
# Development mode (frontend with HMR)
./scripts/app-runner.sh --dev-frontend --build

# Production mode
docker compose up
```

### Port Configuration

The frontend application is configured to run on port 5173 in both development and production modes. This is configured in:
- `vite.config.ts` - Sets the development server port
- `Dockerfile.dev` - Exposes and configures the port for development
- `Dockerfile` - Exposes and configures the port for production
- `docker-compose.yml` and `docker-compose.dev.yml` - Maps the container port to the host

### Troubleshooting Docker Setup

If you encounter issues with the Docker setup:

1. **Wrong mode running (preview instead of dev)**:
   - Ensure you're using `./scripts/dev.sh` or `./scripts/app-runner.sh --dev-frontend`
   - Check that `docker-compose.dev.yml` is being included in the command

2. **Port conflicts**:
   - If port 5173 is already in use, you can modify the port mapping in `docker-compose.dev.yml`
   - Example: Change `"5173:5173"` to `"3000:5173"` to use port 3000 on the host

3. **Hot reloading not working**:
   - Verify that the volume mounts in `docker-compose.dev.yml` are correct
   - Check that the container is using `Dockerfile.dev` and not the production `Dockerfile`

4. **Viewing logs**:
   - To view frontend container logs: `docker logs -f a2a-platform-frontend-1`

## Project Structure

The frontend application follows a modular architecture for better maintainability and scalability:

### Root Directory
- `public/` - Static assets that will be served directly (favicon, robots.txt, etc.)
- `src/` - Source code for the application
- `index.html` - HTML entry point
- `vite.config.ts` - Vite configuration (including port settings)
- `tsconfig.app.json` - TypeScript configuration for the application
- `tsconfig.node.json` - TypeScript configuration for Node.js environment
- `Dockerfile` - Production Docker configuration
- `Dockerfile.dev` - Development Docker configuration with HMR support

### Source Directory (`src/`)
- `assets/` - Static assets (images, icons, fonts)
- `components/` - Reusable UI components
  - `common/` - Generic components used across the application
  - `layout/` - Layout components (header, footer, sidebar)
  - `forms/` - Form-related components
- `hooks/` - Custom React hooks
- `lib/` - Utility functions and service integrations
  - `apolloClient.ts` - GraphQL client configuration
  - `api/` - API integration services
- `pages/` - Page components corresponding to routes
- `routes/` - Routing configuration
- `store/` - State management
- `styles/` - Global styles and theme configuration
- `types/` - TypeScript type definitions
- `App.tsx` - Main application component
- `main.tsx` - Application entry point

### Configuration Files
- `tailwind.config.js` - Tailwind CSS configuration
- `jest.config.js` - Jest testing configuration
- `cypress.config.cjs` - Cypress E2E testing configuration

## Technology Stack

### Core Technologies
- **React 19** - Latest version of the React library with improved performance and features
- **TypeScript** - Strongly typed programming language that builds on JavaScript
- **Vite** - Next-generation frontend build tool with extremely fast HMR
- **Bun** - JavaScript runtime, bundler, test runner, and package manager

### UI and Styling
- **Tailwind CSS** - Utility-first CSS framework for rapid UI development
- **clsx** & **tailwind-merge** - Utilities for conditional class name composition

### State Management and Data Fetching
- **Zustand** - Lightweight state management solution
- **Apollo Client** - Comprehensive GraphQL client with caching
- **GraphQL** - API query language for flexible data fetching

### Authentication
- **Clerk** - Complete authentication and user management solution

### Routing
- **React Router** - Declarative routing for React applications

### Development and Quality Tools
- **ESLint** - Static code analysis tool for identifying problematic patterns
- **Jest** - JavaScript testing framework
- **Cypress** - End-to-end testing framework
- **Docker** - Containerization platform for consistent development and deployment

## Storybook

Interactive component library for developing, testing, and documenting UI components in isolation.

📚 **[Complete Storybook Guide](../../docs/storybook.md)** - Comprehensive setup, usage, and development guide

### Quick Start
```bash
# Start Storybook development server
./scripts/storybook.sh dev --open

# Or use package scripts
bun run storybook
```

For detailed instructions, component catalog, testing, Docker integration, and troubleshooting, see the [complete guide](../../docs/storybook.md).

## Contributing

1. Make sure your code passes the linter checks
2. Test your changes thoroughly
3. Submit a pull request with a clear description of the changes

## Project Scripts

The project includes several helper scripts in the `scripts/` directory at the project root:

- `scripts/dev.sh` - Starts the frontend in development mode with hot reloading
- `scripts/app-runner.sh` - Manages all Docker containers with various options:
  - `--dev-frontend` - Use development mode for frontend
  - `--build` - Force rebuild of containers
  - `--clean` - Remove existing containers before starting
  - `--frontend-only` - Target only the frontend service
- `scripts/run-frontend-tests.sh` - Runs frontend tests with various options:
  - `--unit` - Run unit tests (default)
  - `--e2e` - Run end-to-end tests
  - `--watch` - Run tests in watch mode
  - `--coverage` - Generate coverage reports

## Additional Information

### ESLint Configuration

The project uses ESLint for code quality. To enable type-aware lint rules:

```js
export default tseslint.config({
  extends: [
    // Remove ...tseslint.configs.recommended and replace with this
    ...tseslint.configs.recommendedTypeChecked,
    // Alternatively, use this for stricter rules
    ...tseslint.configs.strictTypeChecked,
    // Optionally, add this for stylistic rules
    ...tseslint.configs.stylisticTypeChecked,
  ],
  languageOptions: {
    // other options...
    parserOptions: {
      project: ['./tsconfig.node.json', './tsconfig.app.json'],
      tsconfigRootDir: import.meta.dirname,
    },
  },
})
```

You can also install React-specific lint plugins:

```js
// eslint.config.js
import reactX from 'eslint-plugin-react-x'
import reactDom from 'eslint-plugin-react-dom'

export default tseslint.config({
  plugins: {
    'react-x': reactX,
    'react-dom': reactDom,
  },
  rules: {
    // other rules...
    ...reactX.configs['recommended-typescript'].rules,
    ...reactDom.configs.recommended.rules,
  },
})
```
