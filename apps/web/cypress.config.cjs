const { defineConfig } = require('cypress');
const path = require('path');

// Load environment variables from the root .env file
require('dotenv').config({ path: path.resolve(__dirname, '../../.env') });

module.exports = defineConfig({
  e2e: {
    baseUrl: 'https://localhost:5173',
    setupNodeEvents(on, config) {
      // Ensure Clerk environment variables are available
      config.env.CLERK_PUBLISHABLE_KEY = process.env.CLERK_PUBLISHABLE_KEY;
      config.env.CLERK_SECRET_KEY = process.env.CLERK_SECRET_KEY;
      config.env.VITE_CLERK_PUBLISHABLE_KEY = process.env.VITE_CLERK_PUBLISHABLE_KEY;
      config.env.VITE_TESTING = 'true';

      // Debug: Log the environment variables
      console.log('Cypress Clerk Config:');
      console.log('CLERK_PUBLISHABLE_KEY:', process.env.CLERK_PUBLISHABLE_KEY ? 'Set' : 'Not set');
      console.log('CLERK_SECRET_KEY:', process.env.CLERK_SECRET_KEY ? 'Set' : 'Not set');
      console.log('VITE_CLERK_PUBLISHABLE_KEY:', process.env.VITE_CLERK_PUBLISHABLE_KEY ? 'Set' : 'Not set');

      // Set up task to enable testing mode
      on('task', {
        setTestingMode() {
          return null;
        }
      });

      // Only set up Clerk if environment variables are available
      if (process.env.CLERK_PUBLISHABLE_KEY && process.env.CLERK_SECRET_KEY) {
        const { clerkSetup } = require('@clerk/testing/cypress');
        return clerkSetup({ config });
      } else {
        console.log('Clerk environment variables not set, skipping Clerk setup');
        return config;
      }
    },
    // Add support for handling SSL certificates in development
    chromeWebSecurity: false,
    // Increase timeouts for slower operations
    defaultCommandTimeout: 2000,
    requestTimeout: 2000,
    responseTimeout: 2000,
  },
});
