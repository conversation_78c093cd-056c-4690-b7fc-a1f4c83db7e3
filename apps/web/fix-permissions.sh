#!/bin/sh
#
# Ensures proper permissions in the container without requiring root access
#
set -e

# Function to detect if we have permission to modify a directory
check_permissions() {
    if [ -w "$1" ]; then
        echo "✓ Have write permissions for $1"
        return 0
    else
        echo "✗ No write permissions for $1"
        return 1
    fi
}

# Main app directories that might need permission checks
echo "📂 Checking directory permissions..."

# Try to create a test file in node_modules to check permissions
TESTFILE="/app/node_modules/.permission_test"
if ! touch "$TESTFILE" 2>/dev/null; then
    echo "⚠️ Warning: Cannot write to /app/node_modules, some operations may fail"
    echo "⚠️ Run the scripts/fix-web-permissions.sh script from the host to fix this"
else
    rm -f "$TESTFILE"
    echo "✅ /app/node_modules is writable"
fi

# Create .cache directory if it doesn't exist
if [ ! -d "/app/node_modules/.cache" ]; then
    echo "📁 Creating cache directory..."
    mkdir -p "/app/node_modules/.cache" 2>/dev/null || true
fi

# Check if we can write to cache
if check_permissions "/app/node_modules/.cache"; then
    echo "✅ Cache directory is writable"
else
    echo "⚠️ Warning: Cannot write to cache directory, some operations may fail"
    echo "⚠️ Run the scripts/fix-web-permissions.sh script from the host to fix this"
fi

echo "🔧 Permission check complete"
