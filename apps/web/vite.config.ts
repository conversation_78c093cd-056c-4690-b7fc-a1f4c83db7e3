import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import tailwindcss from "@tailwindcss/vite";
import path from "path";
import fs from "fs";
import { fileURLToPath } from "node:url";
import type { ServerOptions } from "https";

export default defineConfig(({ mode }) => {
  // Get __dirname equivalent in ESM
  const __dirname = path.dirname(fileURLToPath(import.meta.url));

  // Load env file from project root
  const projectRoot = path.resolve(__dirname, "../../");
  const env = loadEnv(mode, projectRoot, "");

  // HTTPS configuration for development
  let httpsOptions: ServerOptions | undefined = undefined;

  if (env.VITE_USE_HTTPS === "true") {
    // When running in Docker, the certs are mounted at /app/ssl-certs
    // When running locally, they're at ../../ssl-certs
    const certPath = process.env.DOCKER_ENV === "true" ? "/app/ssl-certs" : path.resolve(__dirname, "../../ssl-certs");

    console.log(`Using SSL certificates from: ${certPath}`);
    console.log(`DOCKER_ENV: ${process.env.DOCKER_ENV}`);

    const keyPath = path.resolve(certPath, "localhost-key.pem");
    const certFilePath = path.resolve(certPath, "localhost.pem");

    // Log certificate paths for debugging
    console.log(`Key path: ${keyPath}`);
    console.log(`Cert path: ${certFilePath}`);

    // Verify certificate files exist and are readable
    try {
      if (!fs.existsSync(keyPath)) {
        throw new Error(`SSL private key not found at: ${keyPath}`);
      }
      if (!fs.existsSync(certFilePath)) {
        throw new Error(`SSL certificate not found at: ${certFilePath}`);
      }

      // Read a small portion to verify the files are valid PEM format
      const keyContent = fs.readFileSync(keyPath, "utf8").substring(0, 100);
      const certContent = fs.readFileSync(certFilePath, "utf8").substring(0, 100);

      if (!keyContent.includes("-----BEGIN")) {
        throw new Error(`Invalid SSL private key format at: ${keyPath}`);
      }
      if (!certContent.includes("-----BEGIN")) {
        throw new Error(`Invalid SSL certificate format at: ${certFilePath}`);
      }

      console.log("✅ SSL certificates validated successfully");

      httpsOptions = {
        key: keyPath,
        cert: certFilePath,
      };
    } catch (error) {
      console.error("❌ SSL certificate validation failed:", error instanceof Error ? error.message : String(error));
      console.error("💡 To fix this issue:");
      console.error("   1. Run: ./scripts/generate-ssl-certs.sh");
      console.error("   2. Ensure certificates are properly mounted in Docker");
      console.error("   3. Check file permissions and format");
      throw error;
    }
  }
  return {
    plugins: [
      react({
        jsxRuntime: "classic",
        jsxImportSource: "react",
      }),
      tailwindcss(),
    ],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
        "@lib": path.resolve(__dirname, "./src/lib"),
        "@components": path.resolve(__dirname, "./src/components"),
        "@pages": path.resolve(__dirname, "./src/pages"),
        "@hooks": path.resolve(__dirname, "./src/hooks"),
        "@assets": path.resolve(__dirname, "./src/assets"),
        "@config": path.resolve(__dirname, "./src/config"),
      },
    },
    // Make sure environment variables are properly exposed
    define: {
      "process.env.NODE_ENV": JSON.stringify(mode),
      "process.env.VITE_GRAPHQL_API_URL": JSON.stringify(env.VITE_GRAPHQL_API_URL),
      "process.env.VITE_CLERK_PUBLISHABLE_KEY": JSON.stringify(env.VITE_CLERK_PUBLISHABLE_KEY),
      "process.env.VITE_API_URL": JSON.stringify(env.VITE_API_URL),
      "process.env.VITE_WS_URL": JSON.stringify(env.VITE_WS_URL),
      "process.env.VITE_ASSETS_URL": JSON.stringify(env.VITE_ASSETS_URL),
      "process.env.VITE_DEPLOY_ENV": JSON.stringify(env.VITE_DEPLOY_ENV),
      "process.env.VITE_ENABLE_WEBSOCKET": JSON.stringify(env.VITE_ENABLE_WEBSOCKET),
      "process.env.VITE_ENABLE_ANALYTICS": JSON.stringify(env.VITE_ENABLE_ANALYTICS),
    },
    build: {
      outDir: "dist",
      sourcemap: true, // Always enable source maps for debugging
      rollupOptions: {
        output: {
          // Ensure all assets have a content hash for CDN caching
          entryFileNames: "assets/[name].[hash].js",
          chunkFileNames: "assets/[name].[hash].js",
          assetFileNames: "assets/[name].[hash].[ext]",
          // Temporarily disable manual chunks to debug build hanging
          // manualChunks(id) {
          //   if (id.includes("node_modules")) {
          //     if (id.includes("react")) {
          //       return "vendor-react";
          //     }
          //     if (id.includes("apollo") || id.includes("graphql")) {
          //       return "vendor-graphql";
          //     }
          //     return "vendor";
          //   }
          // },
        },
      },
      // Optimize the build for production
      minify: "terser",
      terserOptions: {
        compress: {
          drop_console: env.VITE_DEPLOY_ENV === "production",
          drop_debugger: true,
        },
      },
    },
    server: {
      // When in development, proxy GraphQL requests to backend
      proxy: {
        "/graphql": {
          target: env.VITE_GRAPHQL_API_URL,
          changeOrigin: true,
          secure: true,  // Enable secure option for HTTPS
          rewrite: (path) => path.replace(/^\/graphql/, ""),
        },
      },
      // CORS configuration
      cors: {
        origin: [
          "https://localhost:5173",
          "https://localhost:5173",
          "https://localhost:8000",
          "https://localhost:8000",
        ],
        methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allowedHeaders: ["Content-Type", "Authorization"],
        credentials: true,
      },
      // Security headers for development
      headers: {
        "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload",
      },
      // HTTPS development server (for local testing with SSL)
      https: httpsOptions,
    },
  };
});
