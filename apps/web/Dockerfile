# Use specific Bun version for reproducible builds
FROM oven/bun:1.2.14-alpine AS base
WORKDIR /app

# Install dependencies into temp directory for better caching
FROM base AS install
RUN mkdir -p /temp/dev
COPY package.json bun.lock /temp/dev/
RUN cd /temp/dev && bun install --frozen-lockfile

# Install production dependencies separately
RUN mkdir -p /temp/prod
COPY package.json bun.lock /temp/prod/
RUN cd /temp/prod && bun install --frozen-lockfile --production

# Copy node_modules from temp directory and build application
FROM base AS prerelease
COPY --from=install /temp/dev/node_modules node_modules

# Copy package.json for scripts
COPY package.json ./

# Copy ALL TypeScript configuration files (project references)
COPY tsconfig*.json ./
COPY vite.config.ts ./

# Copy other configuration files
COPY postcss.config.js tailwind.config.js ./
COPY eslint.config.js ./

# Copy babel config
COPY babel.config.cjs ./

# Copy source code and assets
COPY src/ ./src/
COPY public/ ./public/
COPY index.html ./

# Set build environment
ENV NODE_ENV=production

# Configure Vite environment variables - make API URL configurable
ARG VITE_GRAPHQL_API_URL=https://localhost:8000/graphql
ARG VITE_CLERK_PUBLISHABLE_KEY
ARG VITE_API_URL
ARG VITE_WS_URL
ARG VITE_ASSETS_URL
ARG VITE_DEPLOY_ENV=production
ARG VITE_ENABLE_WEBSOCKET=true
ARG VITE_ENABLE_ANALYTICS=false

# Expose build args as environment variables for Vite
ENV VITE_GRAPHQL_API_URL=${VITE_GRAPHQL_API_URL}
ENV VITE_CLERK_PUBLISHABLE_KEY=${VITE_CLERK_PUBLISHABLE_KEY}
ENV VITE_API_URL=${VITE_API_URL}
ENV VITE_WS_URL=${VITE_WS_URL}
ENV VITE_ASSETS_URL=${VITE_ASSETS_URL}
ENV VITE_DEPLOY_ENV=${VITE_DEPLOY_ENV}
ENV VITE_ENABLE_WEBSOCKET=${VITE_ENABLE_WEBSOCKET}
ENV VITE_ENABLE_ANALYTICS=${VITE_ENABLE_ANALYTICS}

# Debug: Verify we have all necessary tools and project structure
RUN echo "=== Checking build tools ===" && \
    which bun && bun --version && \
    ls -la node_modules/.bin/ | grep -E "(tsc|vite)" && \
    echo "=== Vite Environment Variables ===" && \
    echo "VITE_GRAPHQL_API_URL: $VITE_GRAPHQL_API_URL" && \
    echo "VITE_CLERK_PUBLISHABLE_KEY: ${VITE_CLERK_PUBLISHABLE_KEY:0:20}..." && \
    echo "VITE_API_URL: $VITE_API_URL" && \
    echo "VITE_WS_URL: $VITE_WS_URL" && \
    echo "VITE_ASSETS_URL: $VITE_ASSETS_URL" && \
    echo "VITE_DEPLOY_ENV: $VITE_DEPLOY_ENV" && \
    echo "=== Checking TypeScript config ===" && \
    cat tsconfig.json && \
    echo "=== Checking tsconfig.app.json ===" && \
    cat tsconfig.app.json && \
    echo "=== Checking package.json scripts ===" && \
    cat package.json | grep -A 10 '"scripts"' && \
    echo "=== Checking project structure ===" && \
    ls -la && \
    echo "=== Checking src directory structure ===" && \
    find src -type f -name "*.ts" -o -name "*.tsx" | head -20 && \
    echo "=== Checking lib directory ===" && \
    ls -la src/lib/ && \
    echo "=== Current working directory ===" && \
    pwd

# Build the application with detailed error output
RUN echo "=== Starting Vite build (includes TypeScript compilation) ===" && \
    bun run build --logLevel info

# Final production stage
FROM base AS release
COPY --from=install /temp/prod/node_modules node_modules
COPY --from=prerelease /app/dist ./dist
COPY --from=prerelease /app/package.json ./

# Create non-root user for security
RUN addgroup --system --gid 1001 bunjs
RUN adduser --system --uid 1001 bunjs

# Change ownership of app directory
RUN chown -R bunjs:bunjs /app
USER bunjs

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD bun run --silent -e "fetch('http://localhost:80').then(r => process.exit(r.ok ? 0 : 1)).catch(() => process.exit(1))"

# Start the application
ENTRYPOINT ["bun", "run", "start:prod"]
