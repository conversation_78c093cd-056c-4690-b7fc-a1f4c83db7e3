overwrite: true
schema:
  - 'src/graphql/schema.graphql'
documents:
  - 'src/graphql/operations.graphql'
  - 'src/**/*.{ts,tsx}'
generates:
  src/generated/graphql.ts:
    plugins:
      - typescript
      - typescript-operations
      - typed-document-node
    config:
      withHooks: true
      withComponent: false
      namingConvention:
        typeNames: pascal-case#pascalCase
        transformUnderscore: true
      useTypeImports: true
      # Type Safety Improvements - Replace 'any' types with proper TypeScript types
      scalars:
        DateTime: string
        JSON: 'Record<string, unknown>'
        UUID: string
      # Code Quality and Documentation
      addDocBlocks: true
      documentMode: documentNode
      # ESLint and IDE Compatibility
      avoidOptionals:
        field: false
        inputValue: false
        object: false
        defaultValue: false
      # Reduce complexity by using more specific types
      strictScalars: true
      # Better tree shaking support
      enumsAsTypes: true
      # Improve IDE support with better type definitions
      declarationKind: 'interface'
      # Add file header comment for generated code identification
      addUnderscoreToArgsType: true
      # Reduce complexity in generated DocumentNode exports
      documentNodeImport: '@graphql-typed-document-node/core#TypedDocumentNode'
      # Better formatting for complex objects
      inlineFragmentTypes: 'combine'
      # Optimize for better readability and reduced complexity
      dedupeFragments: true
      # Reduce complexity in generated types
      preResolveTypes: true
      flattenGeneratedTypes: true
      onlyOperationTypes: true
      # See https://the-guild.dev/graphql/codegen/plugins/typescript/typescript for details
