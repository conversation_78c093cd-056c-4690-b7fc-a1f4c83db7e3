import { afterEach, beforeEach, describe, expect, jest, test } from "@jest/globals";
import { renderHook } from "@testing-library/react";
import { useSecureConnections } from "../src/hooks/useSecureConnections";

describe("useSecureConnections Hook", () => {
  // Store original implementation
  const originalLocation = global.window?.location;
  let mockObserve;
  let mockDisconnect;

  beforeEach(() => {
    // Create mock functions
    mockObserve = jest.fn();
    mockDisconnect = jest.fn();

    // Mock MutationObserver
    global.MutationObserver = jest.fn().mockImplementation(() => ({
      observe: mockObserve,
      disconnect: mockDisconnect,
    }));

    // Mock location
    delete window.location;
    window.location = {
      ...originalLocation,
      protocol: "https:",
      hostname: "example.com",
      href: "https://example.com",
      replace: jest.fn(),
    };
  });

  afterEach(() => {
    // Restore mocks
    jest.restoreAllMocks();
    window.location = originalLocation;
  });

  test("should identify secure context correctly", () => {
    // Test with HTTPS
    window.location.protocol = "https:";
    const { result: httpsResult } = renderHook(() => useSecureConnections());
    expect(httpsResult.current).toBe(true);

    // Test with localhost (considered secure)
    window.location.protocol = "http:";
    window.location.hostname = "localhost";
    const { result: localhostResult } = renderHook(() => useSecureConnections());
    expect(localhostResult.current).toBe(true);

    // Test with HTTP (non-localhost)
    window.location.protocol = "http:";
    window.location.hostname = "example.com";
    const { result: httpResult } = renderHook(() => useSecureConnections(false)); // Don't redirect
    expect(httpResult.current).toBe(false);
  });

  test("should redirect HTTP to HTTPS when enabled", () => {
    // Set up HTTP location
    window.location.protocol = "http:";
    window.location.hostname = "example.com";

    // Mock location.href setter
    const mockHrefSetter = jest.fn();
    Object.defineProperty(window.location, "href", {
      set: mockHrefSetter,
      get: () => "http://example.com/path?query=value",
    });

    // Render hook with redirect enabled
    renderHook(() => useSecureConnections(true));

    // Expect redirect to have been triggered
    expect(mockHrefSetter).toHaveBeenCalledWith("https://example.com/path?query=value");
  });

  test("should not redirect HTTP to HTTPS when disabled", () => {
    // Set up HTTP location
    window.location.protocol = "http:";
    window.location.hostname = "example.com";
    window.location.href = "http://example.com";

    // Mock location.href assignment
    const hrefSetter = jest.fn();
    Object.defineProperty(window.location, "href", {
      set: hrefSetter,
      get: () => "http://example.com",
    });

    // Render hook with redirect disabled
    renderHook(() => useSecureConnections(false));

    // Expect no redirect
    expect(hrefSetter).not.toHaveBeenCalled();
  });

  test("should not redirect localhost even with HTTP", () => {
    // Set up localhost HTTP location
    window.location.protocol = "http:";
    window.location.hostname = "localhost";
    window.location.href = "http://localhost:3000";

    // Mock location.href assignment
    const hrefSetter = jest.fn();
    Object.defineProperty(window.location, "href", {
      set: hrefSetter,
      get: () => "http://localhost:3000",
    });

    // Render hook with redirect enabled
    renderHook(() => useSecureConnections(true));

    // Expect no redirect for localhost
    expect(hrefSetter).not.toHaveBeenCalled();
  });

  test("should set up mixed content detection when enabled", () => {
    // Set up HTTPS location
    window.location.protocol = "https:";

    // Render hook with mixed content detection enabled
    renderHook(() => useSecureConnections(true, true));

    // Expect MutationObserver to be created and observe to be called
    expect(global.MutationObserver).toHaveBeenCalled();
    expect(mockObserve).toHaveBeenCalledWith(
      document.documentElement,
      expect.objectContaining({
        childList: true,
        subtree: true,
      }),
    );
  });

  test("should clean up MutationObserver on unmount", () => {
    // Set up HTTPS location
    window.location.protocol = "https:";

    // Render hook with mixed content detection enabled
    const { unmount } = renderHook(() => useSecureConnections(true, true));

    // Unmount the hook
    unmount();

    // Expect disconnect to be called
    expect(mockDisconnect).toHaveBeenCalled();
  });
});
