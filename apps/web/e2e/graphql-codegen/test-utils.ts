/**
 * Test utilities for GraphQL codegen tests
 * Provides mock data, helper functions, and test setup utilities
 */
import type { MockedResponse } from "@apollo/client/testing";
import type {
  Assistant,
  ChatMessage,
  Conversation,
  CreateAssistantInput,
  CreateCliTokenMutation,
  CreatePersonalAssistantMutation,
  GetConversationMessagesQuery,
  GetOrCreateConversationQuery,
  MeQuery,
  MyAssistantQuery,
  NewMessagesSubscription,
  SendMessageMutation,
  User,
  UserHasAssistantQuery,
} from "../../generated/graphql";

import {
  GET_CONVERSATION_MESSAGES_QUERY,
  GET_OR_CREATE_CONVERSATION_QUERY,
  ME_QUERY,
  MY_ASSISTANT_QUERY,
  USER_HAS_ASSISTANT_QUERY,
} from "../../graphql/queries";

import {
  CREATE_CLI_TOKEN_MUTATION,
  CREATE_PERSONAL_ASSISTANT_MUTATION,
  SEND_MESSAGE_MUTATION,
} from "../../graphql/mutations";

import { NEW_MESSAGES_SUBSCRIPTION } from "../../graphql/subscriptions";

/**
 * Mock data factories for creating test data with proper types
 */
export const createMockUser = (overrides: Partial<User> = {}): User => ({
  __typename: "User",
  id: "user-123",
  clerkUserId: "clerk-456",
  email: "<EMAIL>",
  timezone: "UTC",
  preferences: { theme: "dark" },
  isPaSetupComplete: false,
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: "2024-01-01T00:00:00Z",
  ...overrides,
});

export const createMockAssistant = (overrides: Partial<Assistant> = {}): Assistant => ({
  __typename: "Assistant",
  id: "assistant-123",
  name: "Test Assistant",
  backstory: "A helpful test assistant",
  userId: "user-123",
  avatarFileId: null,
  configuration: null,
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: "2024-01-01T00:00:00Z",
  ...overrides,
});

export const createMockChatMessage = (overrides: Partial<ChatMessage> = {}): ChatMessage => ({
  __typename: "ChatMessage",
  id: "msg-123",
  conversationId: "conv-123",
  senderRole: "user",
  content: "Test message",
  timestamp: "2024-01-01T00:00:00Z",
  metadata: null,
  ...overrides,
});

export const createMockConversation = (overrides: Partial<Conversation> = {}): Conversation => ({
  __typename: "Conversation",
  id: "conv-123",
  userId: "user-123",
  assistantId: "assistant-123",
  createdAt: "2024-01-01T00:00:00Z",
  lastMessageAt: "2024-01-01T12:00:00Z",
  messageCount: 5,
  ...overrides,
});

/**
 * Mock response factories for Apollo Client MockedProvider
 */
export const createMeQueryMock = (userOverrides: Partial<User> = {}): MockedResponse<MeQuery> => ({
  request: {
    query: ME_QUERY,
  },
  result: {
    data: {
      __typename: "Query",
      me: createMockUser(userOverrides),
    },
  },
});

export const createMyAssistantQueryMock = (
  assistant: Assistant | null = null,
): MockedResponse<MyAssistantQuery> => ({
  request: {
    query: MY_ASSISTANT_QUERY,
  },
  result: {
    data: {
      __typename: "Query",
      myAssistant: assistant,
    },
  },
});

export const createUserHasAssistantQueryMock = (
  hasAssistant: boolean = false,
): MockedResponse<UserHasAssistantQuery> => ({
  request: {
    query: USER_HAS_ASSISTANT_QUERY,
  },
  result: {
    data: {
      __typename: "Query",
      userHasAssistant: hasAssistant,
    },
  },
});

export const createGetOrCreateConversationQueryMock = (
  conversationOverrides: Partial<Conversation> = {},
): MockedResponse<GetOrCreateConversationQuery> => ({
  request: {
    query: GET_OR_CREATE_CONVERSATION_QUERY,
  },
  result: {
    data: {
      __typename: "Query",
      getOrCreateConversation: createMockConversation(conversationOverrides),
    },
  },
});

export const createGetConversationMessagesQueryMock = (
  messages: ChatMessage[] = [],
  variables: { conversationId: string; limit?: number; offset?: number } = {
    conversationId: "conv-123",
    limit: 50,
    offset: 0,
  },
): MockedResponse<GetConversationMessagesQuery> => ({
  request: {
    query: GET_CONVERSATION_MESSAGES_QUERY,
    variables,
  },
  result: {
    data: {
      __typename: "Query",
      getConversationMessages: messages.length > 0 ? messages : [createMockChatMessage()],
    },
  },
});

export const createCreatePersonalAssistantMutationMock = (
  input: CreateAssistantInput,
  success: boolean = true,
  assistantOverrides: Partial<Assistant> = {},
): MockedResponse<CreatePersonalAssistantMutation> => ({
  request: {
    query: CREATE_PERSONAL_ASSISTANT_MUTATION,
    variables: { input },
  },
  result: {
    data: {
      __typename: "Mutation",
      createPersonalAssistant: {
        __typename: "CreateAssistantPayload",
        success,
        message: success ? "Assistant created successfully" : "Failed to create assistant",
        assistant: createMockAssistant({
          name: input.name,
          backstory: input.backstory,
          avatarFileId: input.avatarFileId || null,
          configuration: input.configuration || null,
          ...assistantOverrides,
        }),
      },
    },
  },
});

export const createSendMessageMutationMock = (
  variables: { conversationId: string; content: string },
  success: boolean = true,
  errorMessage: string | null = null,
  messageOverrides: Partial<ChatMessage> = {},
): MockedResponse<SendMessageMutation> => ({
  request: {
    query: SEND_MESSAGE_MUTATION,
    variables,
  },
  result: {
    data: {
      __typename: "Mutation",
      sendMessage: {
        __typename: "SendMessagePayload",
        success,
        errorMessage,
        message: createMockChatMessage({
          conversationId: variables.conversationId,
          content: variables.content,
          senderRole: "user",
          ...messageOverrides,
        }),
      },
    },
  },
});

export const createCreateCliTokenMutationMock = (
  description: string,
  tokenOverrides: {
    token?: string;
    tokenPrefix?: string;
    id?: string;
  } = {},
): MockedResponse<CreateCliTokenMutation> => ({
  request: {
    query: CREATE_CLI_TOKEN_MUTATION,
    variables: { description },
  },
  result: {
    data: {
      __typename: "Mutation",
      createCliToken: {
        __typename: "CreateCliTokenPayload",
        token: tokenOverrides.token || "cli_token_abcd1234567890",
        cliToken: {
          __typename: "CliTokenGraphQLType",
          id: tokenOverrides.id || "token-123",
          tokenPrefix: tokenOverrides.tokenPrefix || "cli_",
          description,
          createdAt: "2024-01-01T00:00:00Z",
          expiresAt: null,
          lastUsedAt: null,
        },
      },
    },
  },
});

export const createNewMessagesSubscriptionMock = (
  conversationId: string,
  messageOverrides: Partial<ChatMessage> = {},
  eventType: string = "message_sent",
): MockedResponse<NewMessagesSubscription> => ({
  request: {
    query: NEW_MESSAGES_SUBSCRIPTION,
    variables: { conversationId },
  },
  result: {
    data: {
      __typename: "Subscription",
      newMessages: {
        __typename: "MessageSubscriptionPayload",
        conversationId,
        eventType,
        message: createMockChatMessage({
          conversationId,
          senderRole: "assistant",
          content: "New subscription message",
          ...messageOverrides,
        }),
      },
    },
  },
});

/**
 * Error mock factories for testing error scenarios
 */
export const createNetworkErrorMock = (query: unknown, variables?: unknown): MockedResponse => ({
  request: {
    query,
    variables,
  },
  error: new Error("Network error: Failed to fetch"),
});

export const createGraphQLErrorMock = (
  query: unknown,
  variables: unknown,
  errorMessage: string,
  errorCode?: string,
): MockedResponse => ({
  request: {
    query,
    variables,
  },
  result: {
    errors: [
      {
        message: errorMessage,
        extensions: errorCode ? { code: errorCode } : undefined,
      },
    ],
  },
});

/**
 * Helper functions for test assertions
 */
export const expectDocumentNodeStructure = (doc: unknown) => {
  expect(doc).toBeDefined();
  expect(doc.kind).toBe("Document");
  expect(doc.definitions).toBeInstanceOf(Array);
  expect(doc.definitions.length).toBeGreaterThan(0);
};

export const expectOperationDefinition = (
  doc: unknown,
  operationType: "query" | "mutation" | "subscription",
  operationName?: string,
) => {
  expectDocumentNodeStructure(doc);

  const [operation] = doc.definitions;
  expect(operation.kind).toBe("OperationDefinition");
  expect(operation.operation).toBe(operationType);

  if (operationName) {
    expect(operation.name?.value).toBe(operationName);
  }

  expect(operation.selectionSet).toBeDefined();
  expect(operation.selectionSet.selections.length).toBeGreaterThan(0);
};

export const expectFragmentDefinition = (doc: unknown, fragmentName: string) => {
  expectDocumentNodeStructure(doc);

  const [fragment] = doc.definitions;
  expect(fragment.kind).toBe("FragmentDefinition");
  expect(fragment.name.value).toBe(fragmentName);
  expect(fragment.selectionSet).toBeDefined();
  expect(fragment.selectionSet.selections.length).toBeGreaterThan(0);
};

/**
 * Type assertion helpers for testing TypeScript inference
 */
export const assertMeQueryType = (data: MeQuery) => {
  // This function serves as a compile-time type check
  // If the types are wrong, TypeScript will catch it
  const user = data.me;

  // Assert required fields
  expect(typeof user.id).toBe("string");
  expect(typeof user.clerkUserId).toBe("string");
  expect(typeof user.email).toBe("string");
  expect(typeof user.isPaSetupComplete).toBe("boolean");
  expect(typeof user.createdAt).toBe("string");
  expect(typeof user.updatedAt).toBe("string");

  // Assert optional fields
  if (user.timezone !== null) {
    expect(typeof user.timezone).toBe("string");
  }

  // Assert complex fields
  expect(typeof user.preferences).toBe("object");
};

export const assertMyAssistantQueryType = (data: MyAssistantQuery) => {
  const assistant = data.myAssistant;

  if (assistant !== null) {
    expect(typeof assistant.id).toBe("string");
    expect(typeof assistant.name).toBe("string");
    expect(typeof assistant.backstory).toBe("string");
    expect(typeof assistant.createdAt).toBe("string");
    expect(typeof assistant.updatedAt).toBe("string");

    // Optional fields
    if (assistant.avatarFileId !== null) {
      expect(typeof assistant.avatarFileId).toBe("string");
    }

    if (assistant.configuration !== null) {
      expect(typeof assistant.configuration).toBe("object");
    }
  }
};

export const assertChatMessageType = (message: ChatMessage) => {
  expect(typeof message.id).toBe("string");
  expect(typeof message.conversationId).toBe("string");
  expect(typeof message.senderRole).toBe("string");
  expect(typeof message.timestamp).toBe("string");

  // Content can be string or object
  expect(typeof message.content).toMatch(/^(string|object)$/);

  // Metadata is optional
  if (message.metadata !== null) {
    expect(typeof message.metadata).toBe("object");
  }
};

/**
 * Mock combinations for complex test scenarios
 */
export const createCompleteUserMocks = (
  userOverrides: Partial<User> = {},
  assistantOverrides: Partial<Assistant> = {},
): MockedResponse[] => {
  const _user = createMockUser(userOverrides);
  const assistant = createMockAssistant(assistantOverrides);

  return [
    createMeQueryMock(userOverrides),
    createMyAssistantQueryMock(assistant),
    createUserHasAssistantQueryMock(true),
    createGetOrCreateConversationQueryMock(),
  ];
};

export const createEmptyUserMocks = (userOverrides: Partial<User> = {}): MockedResponse[] => [
  createMeQueryMock(userOverrides),
  createMyAssistantQueryMock(null),
  createUserHasAssistantQueryMock(false),
];

export const createConversationWithMessagesMocks = (
  conversationId: string = "conv-123",
  messageCount: number = 3,
): MockedResponse[] => {
  const messages = Array.from({ length: messageCount }, (_, _index) =>
    createMockChatMessage({
      id: `msg-${index + 1}`,
      conversationId,
      content: `Message ${index + 1}`,
      senderRole: index % 2 === 0 ? "user" : "assistant",
    }),
  );

  return [
    createGetOrCreateConversationQueryMock({
      id: conversationId,
      messageCount,
    }),
    createGetConversationMessagesQueryMock(messages, {
      conversationId,
      limit: 50,
      offset: 0,
    }),
  ];
};

/**
 * Test data constants for common scenarios
 */
export const TEST_USER_DATA = {
  BASIC_USER: createMockUser(),
  COMPLETE_USER: createMockUser({
    isPaSetupComplete: true,
    timezone: "America/New_York",
    preferences: {
      theme: "dark",
      notifications: true,
      language: "en",
    },
  }),
  NEW_USER: createMockUser({
    isPaSetupComplete: false,
    timezone: null,
    preferences: {},
  }),
};

export const TEST_ASSISTANT_DATA = {
  BASIC_ASSISTANT: createMockAssistant(),
  CONFIGURED_ASSISTANT: createMockAssistant({
    name: "Advanced Assistant",
    backstory: "A highly configured assistant with custom settings",
    avatarFileId: "avatar-789",
    configuration: {
      personality: "professional",
      expertise: ["coding", "writing", "analysis"],
      responseStyle: "detailed",
    },
  }),
  MINIMAL_ASSISTANT: createMockAssistant({
    name: "Simple Assistant",
    backstory: "A basic assistant",
    avatarFileId: null,
    configuration: null,
  }),
};

export const TEST_MESSAGE_DATA = {
  USER_MESSAGE: createMockChatMessage({
    senderRole: "user",
    content: "Hello, how can you help me?",
  }),
  ASSISTANT_MESSAGE: createMockChatMessage({
    senderRole: "assistant",
    content: "I'm here to help! What would you like to know?",
    metadata: { confidence: 0.95, responseTime: 250 },
  }),
  RICH_MESSAGE: createMockChatMessage({
    senderRole: "assistant",
    content: {
      text: "Here's a code example:",
      code: 'console.log("Hello, world!");',
      language: "javascript",
    },
    metadata: { type: "code_response" },
  }),
};
