/**
 * Tests for GraphQL codegen integration and compatibility
 * Ensures codegen works well with various versions and tools in the ecosystem
 */
import fs from "fs";
import path from "path";

const GENERATED_FILE_PATH = path.join(process.cwd(), "src/generated/graphql.ts");
const PACKAGE_JSON_PATH = path.join(process.cwd(), "package.json");

describe("GraphQL Codegen Integration and Compatibility", () => {
  describe("Apollo Client Compatibility", () => {
    test("should work with Apollo Client useQuery hook", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should generate DocumentNode objects compatible with useQuery
      const documentExports = content.match(/export const \w+Document = /g) || [];
      expect(documentExports.length).toBeGreaterThan(0);

      // Should use TypedDocumentNode for type safety
      expect(content).toContain("TypedDocumentNode");
    });

    test("should support Apollo Client mutations", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should have mutation types compatible with useMutation
      const mutationTypes = content.match(/export type \w+Mutation = {/g) || [];
      expect(mutationTypes.length).toBeGreaterThan(0);

      // Should have mutation variables
      const variableTypes = content.match(/export type \w+MutationVariables = /g) || [];
      expect(variableTypes.length).toBeGreaterThan(0);
    });

    test("should support Apollo Client subscriptions", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should have subscription types
      const subscriptionTypes = content.match(/export type \w+Subscription = {/g) || [];
      expect(subscriptionTypes.length).toBeGreaterThan(0);
    });
  });

  describe("React Version Compatibility", () => {
    test("should work with React 18+", () => {
      const packageJson = JSON.parse(fs.readFileSync(PACKAGE_JSON_PATH, "utf8"));
      const reactVersion = packageJson.dependencies?.react || packageJson.devDependencies?.react;

      if (reactVersion) {
        // Should be compatible with modern React
        expect(reactVersion).toMatch(/^\^?1[8-9]\./);
      }
    });

    test("should support React hooks patterns", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should generate types that work with hooks
      expect(content).toContain("DocumentNode");

      // Should not generate class-component specific types
      expect(content).not.toContain("ComponentClass");
      expect(content).not.toContain("React.Component");
    });

    test("should work with React Server Components", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should not include client-only patterns
      expect(content).not.toContain("useEffect");
      expect(content).not.toContain("useState");
      expect(content).not.toContain("window.");
    });
  });

  describe("TypeScript Version Compatibility", () => {
    test("should work with TypeScript 5.x", () => {
      const packageJson = JSON.parse(fs.readFileSync(PACKAGE_JSON_PATH, "utf8"));
      const tsVersion = packageJson.devDependencies?.typescript;

      if (tsVersion) {
        // Should support modern TypeScript
        expect(tsVersion).toMatch(/[~^]?5\./);
      }
    });

    test("should use modern TypeScript features appropriately", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should use template literal types if needed
      // Should use utility types
      expect(content).toContain("Exact<");
      expect(content).toContain("Maybe<");
    });

    test("should avoid deprecated TypeScript patterns", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should not use old namespace syntax
      expect(content).not.toContain("namespace ");
      expect(content).not.toContain("module ");

      // Should not use triple-slash directives unnecessarily
      expect(content).not.toMatch(/\/\/\/ <reference/);
    });
  });

  describe("Build Tool Compatibility", () => {
    test("should work with Vite", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should use ES modules compatible with Vite
      expect(content).toContain("export");
      expect(content).not.toContain("require(");
      expect(content).not.toContain("module.exports");
    });

    test("should work with Webpack", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should be tree-shakeable for Webpack
      expect(content).toMatch(/^export/m);
      expect(content).not.toContain("export default");
    });

    test("should work with esbuild", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should not use features that break esbuild
      expect(content).not.toMatch(/export\s*{[^}]*,\s*}/); // Trailing commas in exports
    });

    test("should work with Rollup", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should use named exports for Rollup tree shaking
      expect(content).toMatch(/export (type|const) \w+/);
    });
  });

  describe("Testing Framework Compatibility", () => {
    test("should work with Jest", () => {
      const packageJson = JSON.parse(fs.readFileSync(PACKAGE_JSON_PATH, "utf8"));

      // Should have Jest configuration or dependency
      expect(packageJson.devDependencies?.jest || packageJson.scripts?.test).toBeTruthy();
    });

    test("should work with Vitest", () => {
      const packageJson = JSON.parse(fs.readFileSync(PACKAGE_JSON_PATH, "utf8"));

      // May have Vitest as alternative
      const hasVitest = packageJson.devDependencies?.vitest;
      if (hasVitest) {
        expect(hasVitest).toMatch(/\d+\./);
      }
    });

    test("should support testing utilities", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should generate types that work with @testing-library
      const queryTypes = content.match(/export type \w+Query = {/g) || [];
      expect(queryTypes.length).toBeGreaterThan(0);
    });
  });

  describe("Linting Tool Compatibility", () => {
    test("should work with ESLint", () => {
      const packageJson = JSON.parse(fs.readFileSync(PACKAGE_JSON_PATH, "utf8"));

      // Should have ESLint configuration
      expect(packageJson.devDependencies?.eslint).toBeTruthy();
    });

    test("should pass TypeScript ESLint rules", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should not trigger common ESLint TypeScript rules
      expect(content).not.toMatch(/@ts-ignore/); // Should not need ts-ignore
      expect(content).not.toMatch(/@ts-nocheck/); // Should not need ts-nocheck
    });

    test("should work with Prettier", () => {
      const packageJson = JSON.parse(fs.readFileSync(PACKAGE_JSON_PATH, "utf8"));

      // Should have Prettier configuration
      expect(packageJson.devDependencies?.prettier).toBeTruthy();
    });
  });

  describe("GraphQL Ecosystem Compatibility", () => {
    test("should work with graphql package", () => {
      const packageJson = JSON.parse(fs.readFileSync(PACKAGE_JSON_PATH, "utf8"));

      // Should have GraphQL dependency
      const graphqlVersion = packageJson.dependencies?.graphql || packageJson.devDependencies?.graphql;
      expect(graphqlVersion).toBeTruthy();
    });

    test("should support GraphQL fragments", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should handle fragments if they exist
      if (content.includes("Fragment")) {
        expect(content).toMatch(/export type \w+Fragment = {/);
      }
    });

    test("should work with GraphQL subscriptions", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should generate subscription types
      expect(content).toMatch(/export type \w+Subscription = {/);
    });
  });

  describe("Node.js Compatibility", () => {
    test("should work with modern Node.js versions", () => {
      const nodeVersion = process.version;
      const majorVersion = parseInt(nodeVersion.slice(1).split(".")[0]);

      // Should work with Node.js 18+
      expect(majorVersion).toBeGreaterThanOrEqual(18);
    });

    test("should not use Node.js specific APIs inappropriately", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should not use Node.js APIs in generated types
      expect(content).not.toContain("process.");
      expect(content).not.toContain("require.resolve");
      expect(content).not.toContain("__dirname");
      expect(content).not.toContain("__filename");
    });
  });

  describe("Bundle Analyzer Compatibility", () => {
    test("should be analyzable by bundle analyzers", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should use named exports for analysis
      expect(content).toMatch(/export (type|const) \w+/);

      // Should not have dynamic imports that confuse analyzers
      expect(content).not.toContain("import(");
    });

    test("should support tree shaking analysis", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should not have side effects
      expect(content).not.toContain("console.log");
      expect(content).not.toContain("new Date()");
      expect(content).not.toMatch(/\w+\(\)/); // Function calls at module level
    });
  });

  describe("Development Server Compatibility", () => {
    test("should work with hot module replacement", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should use ES modules for HMR
      expect(content).toContain("export");
      expect(content).not.toContain("module.hot");
    });

    test("should work with development proxies", () => {
      // Should not include absolute URLs that break proxies
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should not hardcode development URLs
      expect(content).not.toContain("localhost:3000");
      expect(content).not.toContain("127.0.0.1:3000");
    });
  });

  describe("Framework Compatibility", () => {
    test("should work with Next.js", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should be compatible with Next.js SSR
      expect(content).not.toContain("window.");
      expect(content).not.toContain("document.");
      expect(content).not.toContain("localStorage");
    });

    test("should work with Remix", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should work with Remix's module system
      expect(content).toContain("export");
      expect(content).not.toContain("export default");
    });

    test("should work with Gatsby", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should work with Gatsby's static generation
      expect(content).not.toContain("window.");
      expect(content).not.toContain("navigator.");
    });
  });
});
