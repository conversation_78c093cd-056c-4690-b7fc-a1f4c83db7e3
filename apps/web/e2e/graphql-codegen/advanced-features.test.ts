/**
 * Tests for GraphQL codegen advanced features and customization
 * Ensures codegen supports complex use cases and customization options
 */
import fs from "fs";
import path from "path";
import yaml from "js-yaml";

const CODEGEN_CONFIG_PATH = path.join(process.cwd(), "codegen.yml");
const GENERATED_FILE_PATH = path.join(process.cwd(), "src/generated/graphql.ts");

describe("GraphQL Codegen Advanced Features", () => {
  describe("Fragment Composition", () => {
    test("should handle fragment definitions", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should support fragments if they exist
      if (content.includes("Fragment")) {
        expect(content).toMatch(/export type \w+Fragment = {/);
        expect(content).toContain("__typename");
      }
    });

    test("should generate fragment documents", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Fragment documents should be properly typed
      if (content.includes("FragmentDoc")) {
        expect(content).toContain("DocumentNode");
        expect(content).toContain('kind: "Document"');
      }
    });

    test("should handle fragment composition", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should handle fragment spreads in operations
      if (content.includes("Fragment")) {
        // Should have proper type relationships
        expect(content).toContain("__typename");
      }
    });
  });

  describe("Custom Naming Conventions", () => {
    test("should respect configured naming conventions", () => {
      const config = yaml.load(fs.readFileSync(CODEGEN_CONFIG_PATH, "utf8")) as Record<string, unknown>;
      const namingConvention = config?.generates?.["src/generated/graphql.ts"]?.config?.namingConvention;

      if (namingConvention === "keep") {
        // Should preserve original naming
        const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");
        expect(content).toMatch(/export type \w+/);
      }
    });

    test("should handle type suffix/prefix customization", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should have consistent type naming
      expect(content).toMatch(/export type \w+Query = {/);
      expect(content).toMatch(/export type \w+Mutation = {/);
      expect(content).toMatch(/export type \w+Variables = /);
    });

    test("should support custom enum values", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should handle enums if they exist in schema
      if (content.includes("enum") || content.includes("ENUM")) {
        // Should use string literals or proper enum types
        expect(content).toMatch(/["']\w+["']/);
      }
    });
  });

  describe("Plugin Configuration", () => {
    test("should use configured plugins correctly", () => {
      const config = yaml.load(fs.readFileSync(CODEGEN_CONFIG_PATH, "utf8")) as Record<string, unknown>;
      const plugins = config?.generates?.["src/generated/graphql.ts"]?.plugins || [];

      expect(plugins).toContain("typescript");
      expect(plugins).toContain("typescript-operations");
      expect(plugins).toContain("typed-document-node");
    });

    test("should apply plugin configurations", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should use TypedDocumentNode from plugin
      expect(content).toContain("TypedDocumentNode");
      expect(content).toContain("as unknown as DocumentNode");
    });

    test.skip("should support custom scalar mappings", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should map custom scalars appropriately
      expect(content).toContain("DateTime: { input: unknown; output: unknown }");
      expect(content).toContain("JSON: { input: unknown; output: unknown }");
      expect(content).toContain("UUID: { input: unknown; output: unknown }");
    });
  });

  describe("TypedDocumentNode Integration", () => {
    test("should generate TypedDocumentNode exports", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should export properly typed document nodes
      const documentExports = content.match(/export const \w+Document = /g) || [];
      expect(documentExports.length).toBeGreaterThan(0);

      // Should use TypedDocumentNode typing
      expect(content).toContain("as unknown as DocumentNode<");
    });

    test("should provide type-safe operation variables", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should have properly typed variables
      const variableTypes = content.match(/export type \w+Variables = Exact<{/g) || [];
      expect(variableTypes.length).toBeGreaterThan(0);

      // Variables should use Exact for type safety
      expect(content).toContain("Exact<{");
    });

    test("should support operation result types", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should have result types for operations
      expect(content).toMatch(/export type \w+Query = {/);
      expect(content).toMatch(/export type \w+Mutation = {/);

      // Should include __typename for type discrimination
      expect(content).toContain("__typename");
    });
  });

  describe("Subscription Support", () => {
    test("should generate subscription types", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should have subscription types
      const subscriptionTypes = content.match(/export type \w+Subscription = {/g) || [];
      expect(subscriptionTypes.length).toBeGreaterThan(0);
    });

    test("should generate subscription documents", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should have subscription document exports
      if (content.includes("Subscription")) {
        expect(content).toMatch(/export const \w+Document = {[\s\S]*?operation: "subscription"/);
      }
    });

    test("should support subscription variables", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should have subscription variable types
      const subscriptionVars = content.match(/export type \w+SubscriptionVariables = /g) || [];
      if (subscriptionVars.length > 0) {
        expect(content).toContain("Exact<{");
      }
    });
  });

  describe("Input Type Generation", () => {
    test("should generate input types for mutations", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should have input types (generated as type aliases)
      const inputTypes = content.match(/export type \w+Input = {/g) || [];
      expect(inputTypes.length).toBeGreaterThan(0);
    });

    test("should handle nested input types", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should handle complex input structures
      if (content.includes("Input = {")) {
        expect(content).toMatch(/\w+:\s*[^;]+;/);
      }
    });

    test("should support optional input fields", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should have optional fields in input types
      if (content.includes("Input = {")) {
        const optionalFields = content.match(/\w+\?\s*:/g) || [];
        expect(optionalFields.length).toBeGreaterThan(0);
      }
    });
  });

  describe("Union and Interface Support", () => {
    test("should handle union types", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should handle union types if they exist
      if (content.includes(" | ") && !content.includes("| null")) {
        // Should have proper union type syntax
        expect(content).toMatch(/\w+\s*\|\s*\w+/);
      }
    });

    test("should handle interface types", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should handle interfaces with __typename
      expect(content).toContain("__typename");
    });

    test("should support type discrimination", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should include __typename for type discrimination
      if (content.includes("__typename")) {
        expect(content).toMatch(/__typename\?\s*:\s*["']\w+["']/);
      }
    });
  });

  describe("Directive Support", () => {
    test("should handle GraphQL directives in operations", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should include directive information in DocumentNode
      if (content.includes("directive")) {
        expect(content).toContain('kind: "Directive"');
      }
    });

    test("should support conditional field inclusion", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should handle @include/@skip directives if present
      if (content.includes("include") || content.includes("skip")) {
        expect(content).toContain("directives:");
      }
    });
  });

  describe("Schema Stitching and Federation", () => {
    test("should handle federated schema types", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should work with federated schemas
      expect(content).toContain("export type");

      // Should not break with federation directives
      expect(content).not.toContain("@external");
      expect(content).not.toContain("@requires");
    });

    test("should support schema extensions", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should handle extended types gracefully
      expect(content).toMatch(/export type \w+ = {/);
    });
  });

  describe("Custom Configuration Options", () => {
    test("should respect avoidOptionals configuration", () => {
      const config = yaml.load(fs.readFileSync(CODEGEN_CONFIG_PATH, "utf8")) as Record<string, unknown>;
      const avoidOptionals = config?.generates?.["src/generated/graphql.ts"]?.config?.avoidOptionals;

      if (avoidOptionals === false) {
        // Should use optional field syntax
        const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");
        expect(content).toMatch(/\w+\?\s*:/);
      }
    });

    test("should respect maybeValue configuration", () => {
      const config = yaml.load(fs.readFileSync(CODEGEN_CONFIG_PATH, "utf8")) as Record<string, unknown>;
      const maybeValue = config?.generates?.["src/generated/graphql.ts"]?.config?.maybeValue;

      if (maybeValue === "T | null") {
        const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");
        expect(content).toContain("export type Maybe<T> = T | null");
      }
    });

    test("should handle custom field mappings", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should map fields consistently
      expect(content).toMatch(/\w+:\s*[^;]+;/);
    });
  });

  describe("Code Generation Optimization", () => {
    test("should eliminate unused types", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should not generate unused helper types
      expect(content).not.toMatch(/export type Unused\w+ = /);
      expect(content).not.toMatch(/export type Temp\w+ = /);
    });

    test("should optimize for bundle size", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should use shared utility types
      expect(content).toContain("export type Maybe<T>");
      expect(content).toContain("export type Exact<T extends");

      // Should not duplicate common patterns
      const maybeUsage = (content.match(/Maybe</g) || []).length;
      expect(maybeUsage).toBeGreaterThan(1); // Should be reused
    });

    test("should generate deterministic output", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should not contain timestamps or random values
      expect(content).not.toMatch(/generated.*\d{4}-\d{2}-\d{2}/i);
      expect(content).not.toMatch(/Math\.random/);
      expect(content).not.toMatch(/Date\.now/);
    });
  });

  describe("Advanced TypeScript Features", () => {
    test("should use conditional types appropriately", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should use utility types that may include conditionals
      expect(content).toContain("MakeOptional<T, K extends keyof T>");
      expect(content).toContain("MakeMaybe<T, K extends keyof T>");
    });

    test("should support template literal types", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should use modern TypeScript features where appropriate
      if (content.includes("template")) {
        expect(content).toMatch(/`[^`]*\$\{[^}]+\}[^`]*`/);
      }
    });

    test("should handle recursive types safely", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should not have infinite recursion in types
      expect(content).not.toMatch(/type\s+(\w+).*=.*\1.*\1/);
    });
  });
});
