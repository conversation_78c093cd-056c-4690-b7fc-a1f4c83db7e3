/**
 * Tests for GraphQL codegen schema evolution and migration
 * Ensures codegen handles schema changes gracefully while maintaining backward compatibility
 */
import fs from "fs";
import path from "path";
// import yaml from "js-yaml";

const CODEGEN_CONFIG_PATH = path.join(process.cwd(), "codegen.yml");
const GENERATED_FILE_PATH = path.join(process.cwd(), "src/generated/graphql.ts");

describe("GraphQL Codegen Schema Evolution", () => {
  describe("Schema Version Compatibility", () => {
    test.skip("should maintain existing type definitions", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Core types that should be stable
      const stableTypes = [
        "User",
        "Assistant",
        "ChatMessage",
        "Conversation",
        "Scalars",
        "Maybe",
        "Exact",
      ];

      stableTypes.forEach(type => {
        expect(content).toContain(`export type ${type}`);
      });
    });

    test("should preserve essential operation types", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Essential operations that should remain stable
      const essentialOperations = [
        "MeQuery",
        "MyAssistantQuery",
        "SendMessageMutation",
        "CreatePersonalAssistantMutation",
        "NewMessagesSubscription",
      ];

      essentialOperations.forEach(operation => {
        expect(content).toContain(operation);
      });
    });

    test("should maintain DocumentNode exports", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // DocumentNode exports should be stable
      const documentExports = [
        "MeDocument",
        "MyAssistantDocument",
        "SendMessageDocument",
        "CreatePersonalAssistantDocument",
        "NewMessagesDocument",
      ];

      documentExports.forEach(doc => {
        expect(content).toContain(`export const ${doc}`);
      });
    });
  });

  describe("Backward Compatibility Validation", () => {
    test("should not remove existing exported types", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Count exports to ensure we don't lose them in schema updates
      const exportMatches = content.match(/^export\s+(type|const|interface)\s+\w+/gm) || [];
      expect(exportMatches.length).toBeGreaterThan(50); // Should have substantial exports
    });

    test("should maintain type field compatibility", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // User type should maintain essential fields
      if (content.includes("export type User = {")) {
        expect(content).toMatch(/id:\s*[^;]+;/);
        expect(content).toMatch(/email:\s*[^;]+;/);
        expect(content).toMatch(/clerkUserId:\s*[^;]+;/);
      }

      // Assistant type should maintain essential fields
      if (content.includes("export type Assistant = {")) {
        expect(content).toMatch(/id:\s*[^;]+;/);
        expect(content).toMatch(/name:\s*[^;]+;/);
        expect(content).toMatch(/backstory:\s*[^;]+;/);
      }
    });

    test("should preserve nullable field patterns", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Optional fields should maintain Maybe<T> or T | null pattern
      const nullableMatches = content.match(/\?\s*:\s*(Maybe<[^>]+>|[^|]+\s*\|\s*null)/g) || [];
      expect(nullableMatches.length).toBeGreaterThan(0);
    });

    test("should maintain input type structures", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Input types should be preserved
      const inputTypes = [
        "CreateAssistantInput",
        "UpdateAssistantInput",
        "SendMessageFromPaInput",
      ];

      inputTypes.forEach(inputType => {
        expect(content).toContain(`export type ${inputType} = {`);
      });
    });
  });

  describe("Schema Change Detection", () => {
    test("should handle additive schema changes", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // New optional fields should be additive (marked as optional)
      const optionalFields = content.match(/\w+\?\s*:/g) || [];
      expect(optionalFields.length).toBeGreaterThan(0);
    });

    test("should detect breaking changes in types", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Required fields should not become optional without migration
      const requiredFields = content.match(/\w+:\s*[^?]/g) || [];
      expect(requiredFields.length).toBeGreaterThan(0);
    });

    test.skip("should maintain scalar type mappings", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Custom scalars should remain mapped consistently
      expect(content).toContain("DateTime: { input: unknown; output: unknown }");
      expect(content).toContain("JSON: { input: unknown; output: unknown }");
      expect(content).toContain("UUID: { input: unknown; output: unknown }");
    });
  });

  describe("Migration Path Support", () => {
    test("should support schema extension patterns", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should use patterns that support schema extension
      expect(content).toContain("export type Exact<T extends { [key: string]: unknown }>");
      expect(content).toContain("export type Maybe<T> = T | null");
      expect(content).toContain("export type MakeOptional<T, K extends keyof T>");
    });

    test("should handle enum evolution", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // If enums exist, they should use string literal types for extensibility
      const enumPattern = /export\s+type\s+\w+\s*=\s*["'][^"']+["']/;
      if (enumPattern.test(content)) {
        // Enums should be string literals, not numeric
        expect(content).not.toMatch(/export\s+enum\s+\w+\s*{/);
      }
    });

    test("should maintain fragment compatibility", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Fragment types should be preserved
      const fragmentPattern = /Fragment\s*=\s*{/;
      if (fragmentPattern.test(content)) {
        expect(content).toMatch(/export\s+type\s+\w+Fragment/);
      }
    });
  });

  describe("Version Control Integration", () => {
    test.skip("should produce diff-friendly output", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should have consistent formatting for better diffs
      expect(content).toMatch(/export type \w+ = {\s*\n/); // Consistent type formatting
      expect(content).not.toMatch(/}\s*;\s*export/); // Should have line breaks between exports
    });

    test("should maintain stable ordering", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Types should be in consistent order
      const typeExports = content.match(/export type \w+/g) || [];
      expect(typeExports.length).toBeGreaterThan(0);

      // Should not have random ordering that causes unnecessary diffs
      const sortedTypes = [...typeExports].sort();
      // We don't enforce alphabetical order, but order should be deterministic
      expect(typeExports.length).toBe(sortedTypes.length);
    });

    test("should handle merge conflicts gracefully", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should not contain merge conflict markers
      expect(content).not.toContain("<<<<<<< HEAD");
      expect(content).not.toContain(">>>>>>> ");
      expect(content).not.toContain("=======");
    });
  });

  describe("Deprecation Management", () => {
    test("should handle deprecated fields gracefully", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Deprecated fields should be marked as optional or nullable
      if (content.includes("@deprecated")) {
        // If schema includes deprecation info, it should be handled
        expect(content).not.toContain("@deprecated"); // Should be stripped from generated types
      }
    });

    test("should maintain deprecated operation support", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Old operations should still be available for migration period
      const operationCount = (content.match(/export\s+type\s+\w+(Query|Mutation|Subscription)/g) || []).length;
      expect(operationCount).toBeGreaterThan(5); // Should have reasonable number of operations
    });

    test("should document breaking changes", () => {
      // Configuration should be clear about versioning strategy
      const config = fs.readFileSync(CODEGEN_CONFIG_PATH, "utf8");
      expect(config).toBeTruthy();

      // Should have stable configuration that doesn't break existing usage
      expect(config).toContain("typescript");
      expect(config).toContain("typescript-operations");
      expect(config).toContain("typed-document-node");
    });
  });

  describe("Client Code Compatibility", () => {
    test("should maintain query hook compatibility", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Query types should work with useQuery hooks
      const queryTypes = content.match(/export type \w+Query = {/g) || [];
      queryTypes.forEach(queryType => {
        const typeName = queryType.match(/export type (\w+)Query/)?.[1];
        if (typeName) {
          expect(content).toContain(`${typeName}Document`);
        }
      });
    });

    test("should maintain mutation hook compatibility", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Mutation types should work with useMutation hooks
      const mutationTypes = content.match(/export type \w+Mutation = {/g) || [];
      mutationTypes.forEach(mutationType => {
        const typeName = mutationType.match(/export type (\w+)Mutation/)?.[1];
        if (typeName) {
          expect(content).toContain(`${typeName}Document`);
        }
      });
    });

    test("should maintain subscription compatibility", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Subscription types should work with useSubscription hooks
      const subscriptionTypes = content.match(/export type \w+Subscription = {/g) || [];
      subscriptionTypes.forEach(subscriptionType => {
        const typeName = subscriptionType.match(/export type (\w+)Subscription/)?.[1];
        if (typeName) {
          expect(content).toContain(`${typeName}Document`);
        }
      });
    });

    test("should preserve variable type compatibility", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Variable types should be preserved
      const variableTypes = content.match(/export type \w+Variables = /g) || [];
      expect(variableTypes.length).toBeGreaterThan(0);

      // Variables should use Exact type for precision
      variableTypes.forEach(varType => {
        const fullType = content.substring(content.indexOf(varType));
        expect(fullType).toMatch(/= Exact<{/);
      });
    });
  });

  describe("Performance Impact of Evolution", () => {
    test("should not significantly increase bundle size", () => {
      const stats = fs.statSync(GENERATED_FILE_PATH);
      const fileSizeKB = stats.size / 1024;

      // Should remain reasonable size as schema evolves
      expect(fileSizeKB).toBeLessThan(1024); // Less than 1MB
    });

    test("should maintain TypeScript compilation performance", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should not have excessively deep type nesting
      const deepNesting = content.match(/{[^}]*{[^}]*{[^}]*{[^}]*{/g) || [];
      expect(deepNesting.length).toBeLessThan(10); // Reasonable nesting depth
    });

    test("should optimize for tree shaking", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should use named exports for tree shaking
      expect(content).toMatch(/^export\s+(type|const)/m);
      expect(content).not.toContain("export default");
      expect(content).not.toContain("module.exports");
    });
  });
});
