/**
 * Tests for static GraphQL schema validation
 * Validates that the hybrid schema approach works correctly
 */
import fs from "fs";
import path from "path";
import yaml from "js-yaml";

const SCHEMA_FILE_PATH = path.join(process.cwd(), "src/graphql/schema.graphql");
const GENERATED_FILE_PATH = path.join(process.cwd(), "src/generated/graphql.ts");
const TEST_CONFIG_PATH = path.join(process.cwd(), "codegen.test.yml");
const DEV_CONFIG_PATH = path.join(process.cwd(), "codegen.yml");

describe("Static GraphQL Schema Validation", () => {
  describe("Schema File Existence", () => {
    test("should have a committed schema file", () => {
      expect(fs.existsSync(SCHEMA_FILE_PATH)).toBe(true);
    });

    test("schema file should not be empty", () => {
      const schemaContent = fs.readFileSync(SCHEMA_FILE_PATH, "utf8");
      expect(schemaContent.trim()).toBeTruthy();
      expect(schemaContent.length).toBeGreaterThan(100);
    });

    test("schema file should contain valid GraphQL SDL", () => {
      const schemaContent = fs.readFileSync(SCHEMA_FILE_PATH, "utf8");
      expect(schemaContent).toContain("schema {");
      expect(schemaContent).toContain("type Query");
      expect(schemaContent).toContain("type Mutation");
      expect(schemaContent).toContain("type Subscription");
    });
  });

  describe("Configuration Files", () => {
    test("should have test configuration that uses static schema", () => {
      expect(fs.existsSync(TEST_CONFIG_PATH)).toBe(true);

      const configContent = fs.readFileSync(TEST_CONFIG_PATH, "utf8");
      const config = yaml.load(configContent) as Record<string, unknown>;

      expect(config.schema).toBe("./src/graphql/schema.graphql");
      expect(config.generates["src/generated/graphql.ts"]).toBeDefined();
    });

    test("should have development configuration", () => {
      expect(fs.existsSync(DEV_CONFIG_PATH)).toBe(true);
    });
  });

  describe("Generated Types", () => {
    test("should have generated types file", () => {
      expect(fs.existsSync(GENERATED_FILE_PATH)).toBe(true);
    });

    test("generated types should be valid TypeScript", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should have TypeScript exports
      expect(content).toContain("export type");
      expect(content).toContain("export const");

      // Should have DocumentNode exports for operations
      expect(content).toContain("DocumentNode");

      // Should not have syntax errors (basic check)
      expect(content).not.toContain("undefined");
    });

    test("should contain expected scalar types", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should have custom scalars from our schema
      expect(content).toContain("DateTime");
      expect(content).toContain("UUID");
      expect(content).toContain("JSON");
    });

    test("should contain expected entity types", () => {
      const content = fs.readFileSync(GENERATED_FILE_PATH, "utf8");

      // Should have main entity types
      expect(content).toContain("User");
      expect(content).toContain("Assistant");
      expect(content).toContain("ChatMessage");
      expect(content).toContain("Conversation");
    });
  });

  describe("Schema Validation", () => {
    test("schema should contain expected types", () => {
      const schemaContent = fs.readFileSync(SCHEMA_FILE_PATH, "utf8");

      // Core types
      expect(schemaContent).toContain("type User");
      expect(schemaContent).toContain("type Assistant");
      expect(schemaContent).toContain("type ChatMessage");
      expect(schemaContent).toContain("type Conversation");

      // Custom scalars
      expect(schemaContent).toContain("scalar DateTime");
      expect(schemaContent).toContain("scalar UUID");
      expect(schemaContent).toContain("scalar JSON");
    });

    test("schema should have proper query operations", () => {
      const schemaContent = fs.readFileSync(SCHEMA_FILE_PATH, "utf8");

      expect(schemaContent).toContain("me: User!");
      expect(schemaContent).toContain("userHasAssistant: Boolean!");
      expect(schemaContent).toContain("getConversationMessages(");
    });

    test("schema should have proper mutation operations", () => {
      const schemaContent = fs.readFileSync(SCHEMA_FILE_PATH, "utf8");

      expect(schemaContent).toContain("sendMessage(");
      expect(schemaContent).toContain("createPersonalAssistant(");
    });

    test("schema should have proper subscription operations", () => {
      const schemaContent = fs.readFileSync(SCHEMA_FILE_PATH, "utf8");

      expect(schemaContent).toContain("newMessages(");
    });
  });

  describe("Hybrid Approach Benefits", () => {
    test("static schema approach eliminates network dependencies", () => {
      // This test passes simply by running without network calls
      expect(true).toBe(true);
    });

    test("tests can run offline", () => {
      // This test demonstrates offline capability
      expect(fs.existsSync(SCHEMA_FILE_PATH)).toBe(true);
      expect(fs.existsSync(GENERATED_FILE_PATH)).toBe(true);
    });

    test("deterministic test results", () => {
      // Schema file content should be consistent
      const schemaContent1 = fs.readFileSync(SCHEMA_FILE_PATH, "utf8");
      const schemaContent2 = fs.readFileSync(SCHEMA_FILE_PATH, "utf8");

      expect(schemaContent1).toBe(schemaContent2);
    });
  });
});
