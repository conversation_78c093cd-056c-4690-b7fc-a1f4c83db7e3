/**
 * Tests for Apollo Client integration with generated GraphQL types
 */
import React from "react";
import { act, render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { MockedProvider, MockedResponse } from "@apollo/client/testing";
import { useMutation, useQuery, useSubscription } from "@apollo/client";

import {
  GET_CONVERSATION_MESSAGES_QUERY,
  ME_QUERY,
  MY_ASSISTANT_QUERY,
  USER_HAS_ASSISTANT_QUERY,
} from "../../src/graphql/queries";

import { CREATE_PERSONAL_ASSISTANT_MUTATION, SEND_MESSAGE_MUTATION } from "../../src/graphql/mutations";

import { NEW_MESSAGES_SUBSCRIPTION } from "../../src/graphql/subscriptions";

import type {
  CreatePersonalAssistantMutation,
  GetConversationMessagesQuery,
  <PERSON><PERSON><PERSON><PERSON>,
  MyAssistant<PERSON>uery,
  NewMessagesSubscription,
  SendMessageMutation,
  UserHasAssistantQuery,
} from "../../generated/graphql";

// Test component that uses useQuery with generated types
const MeQueryComponent: React.FC = () => {
  const { data, loading, error } = useQuery(ME_QUERY);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  // TypeScript should infer the correct types here
  const user = data?.me;

  return (
    <div>
      <div data-testid="user-email">{user?.email}</div>
      <div data-testid="user-setup-complete">
        {user?.isPaSetupComplete ? "Complete" : "Incomplete"}
      </div>
      <div data-testid="user-timezone">{user?.timezone || "No timezone"}</div>
    </div>
  );
};

// Test component that uses useMutation with generated types
const CreateAssistantComponent: React.FC = () => {
  const [createAssistant, { data, loading, error }] = useMutation(
    CREATE_PERSONAL_ASSISTANT_MUTATION,
  );

  const handleCreate = () => {
    createAssistant({
      variables: {
        input: {
          name: "Test Assistant",
          backstory: "A test assistant",
        },
      },
    });
  };

  return (
    <div>
      <button onClick={handleCreate} disabled={loading}>
        Create Assistant
      </button>
      {data?.createPersonalAssistant.success && (
        <div data-testid="assistant-name">{data.createPersonalAssistant.assistant.name}</div>
      )}
      {error && <div data-testid="error">{error.message}</div>}
    </div>
  );
};

// Test component that uses useSubscription with generated types
const MessagesSubscriptionComponent: React.FC<{ conversationId: string }> = ({
  conversationId,
}) => {
  const { data, loading, error } = useSubscription(NEW_MESSAGES_SUBSCRIPTION, {
    variables: { conversationId },
  });

  if (loading) return <div>Subscribing...</div>;
  if (error) return <div>Subscription error: {error.message}</div>;

  const newMessage = data?.newMessages;

  return (
    <div>
      {newMessage && (
        <div data-testid="new-message">
          <div data-testid="sender-role">{newMessage.message.senderRole}</div>
          <div data-testid="message-content">{String(newMessage.message.content)}</div>
          <div data-testid="event-type">{newMessage.eventType}</div>
        </div>
      )}
    </div>
  );
};

describe("Apollo Client Integration with Generated Types", () => {
  describe("useQuery Hook Integration", () => {
    test("should work with ME_QUERY and provide correct TypeScript types", async () => {
      const meMock: MockedResponse<MeQuery> = {
        request: {
          query: ME_QUERY,
        },
        result: {
          data: {
            __typename: "Query",
            me: {
              __typename: "User",
              id: "user-123",
              clerkUserId: "clerk-456",
              email: "<EMAIL>",
              timezone: "UTC",
              preferences: { theme: "dark" },
              isPaSetupComplete: true,
              createdAt: "2024-01-01T00:00:00Z",
              updatedAt: "2024-01-01T00:00:00Z",
            },
          },
        },
      };

      render(
        <MockedProvider mocks={[meMock]} addTypename={false}>
          <MeQueryComponent />
        </MockedProvider>,
      );

      expect(screen.getByText("Loading...")).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByTestId("user-email")).toHaveTextContent("<EMAIL>");
        expect(screen.getByTestId("user-setup-complete")).toHaveTextContent("Complete");
        expect(screen.getByTestId("user-timezone")).toHaveTextContent("UTC");
      });
    });

    test("should handle nullable fields in MyAssistantQuery", async () => {
      const assistantMock: MockedResponse<MyAssistantQuery> = {
        request: {
          query: MY_ASSISTANT_QUERY,
        },
        result: {
          data: {
            __typename: "Query",
            myAssistant: null, // Testing null case
          },
        },
      };

      const TestComponent: React.FC = () => {
        const { data, loading } = useQuery(MY_ASSISTANT_QUERY);

        if (loading) return <div>Loading...</div>;

        return (
          <div data-testid="assistant-status">
            {data?.myAssistant ? "Has Assistant" : "No Assistant"}
          </div>
        );
      };

      render(
        <MockedProvider mocks={[assistantMock]} addTypename={false}>
          <TestComponent />
        </MockedProvider>,
      );

      await waitFor(() => {
        expect(screen.getByTestId("assistant-status")).toHaveTextContent("No Assistant");
      });
    });

    test("should handle connection responses in GetConversationMessagesQuery", async () => {
      const messagesMock: MockedResponse<GetConversationMessagesQuery> = {
        request: {
          query: GET_CONVERSATION_MESSAGES_QUERY,
          variables: {
            conversationId: "conv-123",
            first: 10,
          },
        },
        result: {
          data: {
            __typename: "Query",
            getConversationMessages: {
              __typename: "ChatMessageConnection",
              edges: [
                {
                  __typename: "ChatMessageEdge",
                  cursor: "cursor-1",
                  node: {
                    __typename: "ChatMessage",
                    id: "msg-1",
                    conversationId: "conv-123",
                    senderRole: "user",
                    content: "Hello",
                    timestamp: "2024-01-01T00:00:00Z",
                    metadata: null,
                  },
                },
                {
                  __typename: "ChatMessageEdge",
                  cursor: "cursor-2",
                  node: {
                    __typename: "ChatMessage",
                    id: "msg-2",
                    conversationId: "conv-123",
                    senderRole: "assistant",
                    content: "Hi there!",
                    timestamp: "2024-01-01T00:00:01Z",
                    metadata: { confidence: 0.95 },
                  },
                },
              ],
              pageInfo: {
                __typename: "PageInfo",
                hasNextPage: false,
                hasPreviousPage: false,
                startCursor: "cursor-1",
                endCursor: "cursor-2",
              },
              totalCount: 2,
            },
          },
        },
      };

      const TestComponent: React.FC = () => {
        const { data, loading } = useQuery(GET_CONVERSATION_MESSAGES_QUERY, {
          variables: { conversationId: "conv-123", first: 10 },
        });

        if (loading) return <div>Loading...</div>;

        const connection = data?.getConversationMessages;
        const messages = connection?.edges.map(edge => edge.node) || [];

        return (
          <div>
            <div data-testid="message-count">{messages.length}</div>
            <div data-testid="total-count">{connection?.totalCount || 0}</div>
            <div data-testid="has-next-page">{connection?.pageInfo.hasNextPage ? "true" : "false"}</div>
            {messages.map(message => (
              <div key={message.id} data-testid={`message-${message.id}`}>
                {message.senderRole}: {String(message.content)}
              </div>
            ))}
          </div>
        );
      };

      render(
        <MockedProvider mocks={[messagesMock]} addTypename={false}>
          <TestComponent />
        </MockedProvider>,
      );

      await waitFor(() => {
        expect(screen.getByTestId("message-count")).toHaveTextContent("2");
        expect(screen.getByTestId("total-count")).toHaveTextContent("2");
        expect(screen.getByTestId("has-next-page")).toHaveTextContent("false");
        expect(screen.getByTestId("message-msg-1")).toHaveTextContent("user: Hello");
        expect(screen.getByTestId("message-msg-2")).toHaveTextContent("assistant: Hi there!");
      });
    });

    test("should provide proper TypeScript inference for boolean queries", async () => {
      const hasAssistantMock: MockedResponse<UserHasAssistantQuery> = {
        request: {
          query: USER_HAS_ASSISTANT_QUERY,
        },
        result: {
          data: {
            __typename: "Query",
            userHasAssistant: true,
          },
        },
      };

      const TestComponent: React.FC = () => {
        const { data, loading } = useQuery(USER_HAS_ASSISTANT_QUERY);

        if (loading) return <div>Loading...</div>;

        // TypeScript should infer that userHasAssistant is boolean
        const hasAssistant = data?.userHasAssistant;

        return (
          <div>
            <div data-testid="has-assistant">{hasAssistant ? "Yes" : "No"}</div>
            <div data-testid="type-check">{typeof hasAssistant}</div>
          </div>
        );
      };

      render(
        <MockedProvider mocks={[hasAssistantMock]} addTypename={false}>
          <TestComponent />
        </MockedProvider>,
      );

      await waitFor(() => {
        expect(screen.getByTestId("has-assistant")).toHaveTextContent("Yes");
        expect(screen.getByTestId("type-check")).toHaveTextContent("boolean");
      });
    });
  });

  describe("useMutation Hook Integration", () => {
    test("should work with CREATE_PERSONAL_ASSISTANT_MUTATION and handle success", async () => {
      const user = userEvent.setup();
      const createAssistantMock: MockedResponse<CreatePersonalAssistantMutation> = {
        request: {
          query: CREATE_PERSONAL_ASSISTANT_MUTATION,
          variables: {
            input: {
              name: "Test Assistant",
              backstory: "A test assistant",
            },
          },
        },
        result: {
          data: {
            __typename: "Mutation",
            createPersonalAssistant: {
              __typename: "CreateAssistantPayload",
              success: true,
              message: "Assistant created successfully",
              assistant: {
                __typename: "Assistant",
                id: "assistant-123",
                name: "Test Assistant",
                backstory: "A test assistant",
                avatarFileId: null,
                configuration: null,
                createdAt: "2024-01-01T00:00:00Z",
                updatedAt: "2024-01-01T00:00:00Z",
              },
            },
          },
        },
      };

      render(
        <MockedProvider mocks={[createAssistantMock]} addTypename={false}>
          <CreateAssistantComponent />
        </MockedProvider>,
      );

      const createButton = screen.getByText("Create Assistant");
      await act(async () => {
        await user.click(createButton);
      });

      await waitFor(() => {
        expect(screen.getByTestId("assistant-name")).toHaveTextContent("Test Assistant");
      });
    });

    test("should work with SEND_MESSAGE_MUTATION and handle errors", async () => {
      const user = userEvent.setup();
      const sendMessageMock: MockedResponse<SendMessageMutation> = {
        request: {
          query: SEND_MESSAGE_MUTATION,
          variables: {
            conversationId: "conv-123",
            content: "Test message",
          },
        },
        result: {
          data: {
            __typename: "Mutation",
            sendMessage: {
              __typename: "SendMessagePayload",
              success: false,
              errorMessage: "Message is too long",
              message: {
                __typename: "ChatMessage",
                id: "msg-123",
                conversationId: "conv-123",
                senderRole: "user",
                content: "Test message",
                timestamp: "2024-01-01T00:00:00Z",
                metadata: null,
              },
            },
          },
        },
      };

      const TestComponent: React.FC = () => {
        const [sendMessage, { data, loading }] = useMutation(SEND_MESSAGE_MUTATION);

        const handleSend = () => {
          sendMessage({
            variables: {
              conversationId: "conv-123",
              content: "Test message",
            },
          });
        };

        return (
          <div>
            <button onClick={handleSend} disabled={loading}>
              Send Message
            </button>
            {data?.sendMessage.success === false && (
              <div data-testid="error-message">{data.sendMessage.errorMessage}</div>
            )}
            {data?.sendMessage.success === true && (
              <div data-testid="success">Message sent successfully</div>
            )}
          </div>
        );
      };

      render(
        <MockedProvider mocks={[sendMessageMock]} addTypename={false}>
          <TestComponent />
        </MockedProvider>,
      );

      const sendButton = screen.getByText("Send Message");
      await act(async () => {
        await user.click(sendButton);
      });

      await waitFor(() => {
        expect(screen.getByTestId("error-message")).toHaveTextContent("Message is too long");
      });
    });

    test("should provide proper type checking for mutation variables", async () => {
      // This test validates TypeScript compile-time checking
      const TestComponent: React.FC = () => {
        const [createAssistant] = useMutation(CREATE_PERSONAL_ASSISTANT_MUTATION);

        const handleValidCreate = () => {
          // This should compile fine
          createAssistant({
            variables: {
              input: {
                name: "Valid Assistant",
                backstory: "Valid backstory",
                // Optional fields
                avatarFileId: "avatar-123",
                configuration: { theme: "dark" },
              },
            },
          });
        };

        // This would cause a TypeScript error if uncommented:
        // const handleInvalidCreate = () => {
        //   createAssistant({
        //     variables: {
        //       input: {
        //         // Missing required 'backstory' field
        //         name: "Invalid Assistant",
        //       },
        //     },
        //   });
        // };

        return <button onClick={handleValidCreate}>Create Valid Assistant</button>;
      };

      // If this component renders without TypeScript errors, the test passes
      render(
        <MockedProvider mocks={[]} addTypename={false}>
          <TestComponent />
        </MockedProvider>,
      );

      expect(screen.getByText("Create Valid Assistant")).toBeInTheDocument();
    });
  });

  describe("useSubscription Hook Integration", () => {
    test("should work with NEW_MESSAGES_SUBSCRIPTION", async () => {
      const subscriptionMock: MockedResponse<NewMessagesSubscription> = {
        request: {
          query: NEW_MESSAGES_SUBSCRIPTION,
          variables: {
            conversationId: "conv-123",
          },
        },
        result: {
          data: {
            __typename: "Subscription",
            newMessages: {
              __typename: "MessageSubscriptionPayload",
              conversationId: "conv-123",
              eventType: "message_sent",
              message: {
                __typename: "ChatMessage",
                id: "msg-456",
                conversationId: "conv-123",
                senderRole: "assistant",
                content: "New subscription message",
                timestamp: "2024-01-01T00:00:00Z",
                metadata: { realtime: true },
              },
            },
          },
        },
      };

      render(
        <MockedProvider mocks={[subscriptionMock]} addTypename={false}>
          <MessagesSubscriptionComponent conversationId="conv-123" />
        </MockedProvider>,
      );

      await waitFor(() => {
        expect(screen.getByTestId("sender-role")).toHaveTextContent("assistant");
        expect(screen.getByTestId("message-content")).toHaveTextContent("New subscription message");
        expect(screen.getByTestId("event-type")).toHaveTextContent("message_sent");
      });
    });

    test("should handle subscription errors with proper types", async () => {
      const subscriptionErrorMock: MockedResponse = {
        request: {
          query: NEW_MESSAGES_SUBSCRIPTION,
          variables: {
            conversationId: "invalid-conv",
          },
        },
        error: new Error("Subscription failed: Invalid conversation ID"),
      };

      render(
        <MockedProvider mocks={[subscriptionErrorMock]} addTypename={false}>
          <MessagesSubscriptionComponent conversationId="invalid-conv" />
        </MockedProvider>,
      );

      await waitFor(() => {
        expect(
          screen.getByText("Subscription error: Subscription failed: Invalid conversation ID"),
        ).toBeInTheDocument();
      });
    });
  });

  describe("Error Handling with Types", () => {
    test("should handle network errors with proper typing", async () => {
      const networkErrorMock: MockedResponse = {
        request: {
          query: ME_QUERY,
        },
        error: new Error("Network error: Failed to fetch"),
      };

      render(
        <MockedProvider mocks={[networkErrorMock]} addTypename={false}>
          <MeQueryComponent />
        </MockedProvider>,
      );

      await waitFor(() => {
        expect(screen.getByText("Error: Network error: Failed to fetch")).toBeInTheDocument();
      });
    });

    test("should provide proper error types for mutations", () => {
      // This test validates that error types are properly typed at compile time
      // If error types are wrong, TypeScript will catch it during compilation

      type CreateAssistantError = unknown; // In real usage, this would be properly typed

      const mockError: CreateAssistantError = {
        message: "User already has an assistant",
        graphQLErrors: [],
        networkError: null,
        extraInfo: undefined,
      };

      expect(mockError.message).toBe("User already has an assistant");
      expect(Array.isArray(mockError.graphQLErrors)).toBe(true);
    });
  });

  describe("Type Inference Validation", () => {
    test("should provide correct TypeScript inference in complex scenarios", () => {
      // This test validates that TypeScript properly infers types in complex scenarios
      const TestComponent: React.FC = () => {
        const { data: meData } = useQuery(ME_QUERY);
        const { data: assistantData } = useQuery(MY_ASSISTANT_QUERY);

        // TypeScript should properly infer these types
        const userEmail = meData?.me.email; // string
        const assistantName = assistantData?.myAssistant?.name; // string | undefined
        const isPaSetupComplete = meData?.me.isPaSetupComplete; // boolean | undefined

        return (
          <div>
            <div data-testid="user-email-type">{typeof userEmail}</div>
            <div data-testid="assistant-name-type">{typeof assistantName}</div>
            <div data-testid="setup-complete-type">{typeof isPaSetupComplete}</div>
          </div>
        );
      };

      const mocks: MockedResponse[] = [
        {
          request: { query: ME_QUERY },
          result: {
            data: {
              __typename: "Query",
              me: {
                __typename: "User",
                id: "user-123",
                clerkUserId: "clerk-456",
                email: "<EMAIL>",
                timezone: null,
                preferences: {},
                isPaSetupComplete: true,
                createdAt: "2024-01-01",
                updatedAt: "2024-01-01",
              },
            },
          },
        },
        {
          request: { query: MY_ASSISTANT_QUERY },
          result: {
            data: {
              __typename: "Query",
              myAssistant: null,
            },
          },
        },
      ];

      render(
        <MockedProvider mocks={mocks} addTypename={false}>
          <TestComponent />
        </MockedProvider>,
      );

      // If the component renders, TypeScript inference is working correctly
      expect(screen.getByTestId("user-email-type")).toBeInTheDocument();
      expect(screen.getByTestId("assistant-name-type")).toBeInTheDocument();
      expect(screen.getByTestId("setup-complete-type")).toBeInTheDocument();
    });
  });
});
