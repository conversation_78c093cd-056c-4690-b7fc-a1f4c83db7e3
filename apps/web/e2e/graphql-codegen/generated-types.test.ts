/**
 * Tests for generated GraphQL types to ensure type safety and correctness
 */
import type {
  Assistant,
  ChatMessage,
  Conversation,
  CreateAssistantInput,
  CreateCliTokenMutation,
  CreatePersonalAssistantMutation,
  GetConversationMessagesQuery,
  MeQuery,
  MessageSubscriptionDataFragment,
  MyAssistantQuery,
  NewMessagesSubscription,
  SendMessageFromPaInput,
  SendMessageMutation,
  User,
  UserHasAssistantQuery,
  // GetOrCreateConversationQuery,
} from "../../generated/graphql";

describe("Generated GraphQL Types", () => {
  describe("Query Types", () => {
    test("MeQuery should have correct structure", () => {
      // This is a compile-time test - if types are wrong, TypeScript will fail
      const mockMeQuery: MeQuery = {
        __typename: "Query",
        me: {
          __typename: "User",
          id: "test-id",
          clerkUserId: "clerk-123",
          email: "<EMAIL>",
          timezone: "UTC",
          preferences: {},
          isPaSetupComplete: true,
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z",
        },
      };

      expect(mockMeQuery.me.id).toBeDefined();
      expect(mockMeQuery.me.email).toBeDefined();
      expect(typeof mockMeQuery.me.isPaSetupComplete).toBe("boolean");
    });

    test("MyAssistantQuery should handle nullable assistant", () => {
      // Test case where user has no assistant
      const queryWithoutAssistant: MyAssistantQuery = {
        __typename: "Query",
        myAssistant: null,
      };

      // Test case where user has an assistant
      const queryWithAssistant: MyAssistantQuery = {
        __typename: "Query",
        myAssistant: {
          __typename: "Assistant",
          id: "assistant-123",
          name: "My Assistant",
          backstory: "A helpful assistant",
          avatarFileId: null,
          configuration: null,
          createdAt: "2024-01-01T00:00:00Z",
          updatedAt: "2024-01-01T00:00:00Z",
        },
      };

      expect(queryWithoutAssistant.myAssistant).toBeNull();
      expect(queryWithAssistant.myAssistant).not.toBeNull();
      expect(queryWithAssistant.myAssistant?.name).toBe("My Assistant");
    });

    test("UserHasAssistantQuery should return boolean", () => {
      const query: UserHasAssistantQuery = {
        __typename: "Query",
        userHasAssistant: true,
      };

      expect(typeof query.userHasAssistant).toBe("boolean");
    });

    test("GetConversationMessagesQuery should handle connection structure", () => {
      const query: GetConversationMessagesQuery = {
        __typename: "Query",
        getConversationMessages: {
          __typename: "ChatMessageConnection",
          edges: [
            {
              __typename: "ChatMessageEdge",
              cursor: "cursor-1",
              node: {
                __typename: "ChatMessage",
                id: "msg-1",
                conversationId: "conv-1",
                senderRole: "user",
                content: "Hello",
                timestamp: "2024-01-01T00:00:00Z",
                metadata: null,
              },
            },
            {
              __typename: "ChatMessageEdge",
              cursor: "cursor-2",
              node: {
                __typename: "ChatMessage",
                id: "msg-2",
                conversationId: "conv-1",
                senderRole: "assistant",
                content: "Hi there!",
                timestamp: "2024-01-01T00:00:01Z",
                metadata: { confidence: 0.95 },
              },
            },
          ],
          pageInfo: {
            __typename: "PageInfo",
            hasNextPage: false,
            hasPreviousPage: false,
            startCursor: "cursor-1",
            endCursor: "cursor-2",
          },
          totalCount: 2,
        },
      };

      expect(query.getConversationMessages.edges).toHaveLength(2);
      expect(query.getConversationMessages.edges[0].node.senderRole).toBe("user");
      expect(query.getConversationMessages.edges[1].node.metadata).toEqual({ confidence: 0.95 });
      expect(query.getConversationMessages.pageInfo.hasNextPage).toBe(false);
      expect(query.getConversationMessages.totalCount).toBe(2);
    });
  });

  describe("Mutation Types", () => {
    test("CreatePersonalAssistantMutation should have correct payload structure", () => {
      const mutation: CreatePersonalAssistantMutation = {
        __typename: "Mutation",
        createPersonalAssistant: {
          __typename: "CreateAssistantPayload",
          success: true,
          message: "Assistant created successfully",
          assistant: {
            __typename: "Assistant",
            id: "assistant-123",
            name: "New Assistant",
            backstory: "A newly created assistant",
            avatarFileId: null,
            configuration: null,
            createdAt: "2024-01-01T00:00:00Z",
            updatedAt: "2024-01-01T00:00:00Z",
          },
        },
      };

      expect(typeof mutation.createPersonalAssistant.success).toBe("boolean");
      expect(mutation.createPersonalAssistant.message).toBeDefined();
      expect(mutation.createPersonalAssistant.assistant.name).toBe("New Assistant");
    });

    test("SendMessageMutation should handle success and error states", () => {
      // Success case
      const successMutation: SendMessageMutation = {
        __typename: "Mutation",
        sendMessage: {
          __typename: "SendMessagePayload",
          success: true,
          errorMessage: null,
          message: {
            __typename: "ChatMessage",
            id: "msg-123",
            conversationId: "conv-123",
            senderRole: "user",
            content: "Test message",
            timestamp: "2024-01-01T00:00:00Z",
            metadata: null,
          },
        },
      };

      // Error case
      const errorMutation: SendMessageMutation = {
        __typename: "Mutation",
        sendMessage: {
          __typename: "SendMessagePayload",
          success: false,
          errorMessage: "Message too long",
          message: {
            __typename: "ChatMessage",
            id: "msg-124",
            conversationId: "conv-123",
            senderRole: "user",
            content: "",
            timestamp: "2024-01-01T00:00:00Z",
            metadata: null,
          },
        },
      };

      expect(successMutation.sendMessage.success).toBe(true);
      expect(successMutation.sendMessage.errorMessage).toBeNull();
      expect(errorMutation.sendMessage.success).toBe(false);
      expect(errorMutation.sendMessage.errorMessage).toBe("Message too long");
    });

    test("CreateCliTokenMutation should return token and metadata", () => {
      const mutation: CreateCliTokenMutation = {
        __typename: "Mutation",
        createCliToken: {
          __typename: "CreateCliTokenPayload",
          token: "cli_token_abcd1234567890",
          cliToken: {
            __typename: "CliTokenGraphQLType",
            id: "token-123",
            tokenPrefix: "cli_",
            description: "Development token",
            createdAt: "2024-01-01T00:00:00Z",
            expiresAt: null,
            lastUsedAt: null,
          },
        },
      };

      expect(mutation.createCliToken.token).toMatch(/^cli_token_/);
      expect(mutation.createCliToken.cliToken.tokenPrefix).toBe("cli_");
      expect(mutation.createCliToken.cliToken.description).toBe("Development token");
    });
  });

  describe("Subscription Types", () => {
    test("NewMessagesSubscription should have correct structure", () => {
      const subscription: NewMessagesSubscription = {
        __typename: "Subscription",
        newMessages: {
          __typename: "MessageSubscriptionPayload",
          conversationId: "conv-123",
          eventType: "message_sent",
          message: {
            __typename: "ChatMessage",
            id: "msg-123",
            conversationId: "conv-123",
            senderRole: "assistant",
            content: "New message received",
            timestamp: "2024-01-01T00:00:00Z",
            metadata: null,
          },
        },
      };

      expect(subscription.newMessages.eventType).toBe("message_sent");
      expect(subscription.newMessages.conversationId).toBe("conv-123");
      expect(subscription.newMessages.message.senderRole).toBe("assistant");
    });
  });

  describe("Input Types", () => {
    test("CreateAssistantInput should enforce required fields", () => {
      const validInput: CreateAssistantInput = {
        name: "Test Assistant",
        backstory: "A test assistant for validation",
      };

      const inputWithOptionalFields: CreateAssistantInput = {
        name: "Advanced Assistant",
        backstory: "An assistant with configuration",
        avatarFileId: "avatar-123",
        configuration: {
          temperature: 0.7,
          maxTokens: 1000,
        },
      };

      expect(validInput.name).toBeDefined();
      expect(validInput.backstory).toBeDefined();
      expect(inputWithOptionalFields.avatarFileId).toBe("avatar-123");
      expect(inputWithOptionalFields.configuration).toEqual({
        temperature: 0.7,
        maxTokens: 1000,
      });
    });

    test("SendMessageFromPaInput should have correct structure", () => {
      const input: SendMessageFromPaInput = {
        conversationId: "conv-123",
        content: "Response from PA",
        metadata: {
          responseTime: 150,
          confidence: 0.95,
        },
      };

      expect(input.conversationId).toBe("conv-123");
      expect(input.content).toBe("Response from PA");
      expect(input.metadata).toEqual({
        responseTime: 150,
        confidence: 0.95,
      });
    });
  });

  describe("Fragment Types", () => {
    test("MessageSubscriptionDataFragment should match subscription payload", () => {
      const fragment: MessageSubscriptionDataFragment = {
        __typename: "MessageSubscriptionPayload",
        conversationId: "conv-123",
        eventType: "message_received",
        message: {
          __typename: "ChatMessage",
          id: "msg-456",
          conversationId: "conv-123",
          senderRole: "user",
          content: "Fragment test message",
          timestamp: "2024-01-01T00:00:00Z",
          metadata: null,
        },
      };

      expect(fragment.eventType).toBe("message_received");
      expect(fragment.message.content).toBe("Fragment test message");
    });
  });

  describe("Base Types", () => {
    test("User type should have all required fields", () => {
      const user: User = {
        __typename: "User",
        id: "user-123",
        clerkUserId: "clerk-456",
        email: "<EMAIL>",
        timezone: "America/New_York",
        preferences: {
          theme: "dark",
          notifications: true,
        },
        isPaSetupComplete: false,
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      expect(user.id).toBeDefined();
      expect(user.clerkUserId).toBeDefined();
      expect(user.email).toMatch(/@/);
      expect(typeof user.isPaSetupComplete).toBe("boolean");
    });

    test("Assistant type should handle optional fields", () => {
      const minimalAssistant: Assistant = {
        __typename: "Assistant",
        id: "assistant-123",
        name: "Minimal Assistant",
        backstory: "Basic assistant",
        userId: "user-123",
        avatarFileId: null,
        configuration: null,
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      const fullAssistant: Assistant = {
        __typename: "Assistant",
        id: "assistant-456",
        name: "Full Assistant",
        backstory: "Fully configured assistant",
        userId: "user-123",
        avatarFileId: "avatar-789",
        configuration: {
          personality: "helpful",
          expertise: ["coding", "writing"],
        },
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      };

      expect(minimalAssistant.avatarFileId).toBeNull();
      expect(minimalAssistant.configuration).toBeNull();
      expect(fullAssistant.avatarFileId).toBe("avatar-789");
      expect(fullAssistant.configuration).toEqual({
        personality: "helpful",
        expertise: ["coding", "writing"],
      });
    });

    test("ChatMessage type should handle various content types", () => {
      const textMessage: ChatMessage = {
        __typename: "ChatMessage",
        id: "msg-1",
        conversationId: "conv-1",
        senderRole: "user",
        content: "Simple text message",
        timestamp: "2024-01-01T00:00:00Z",
        metadata: null,
      };

      const richMessage: ChatMessage = {
        __typename: "ChatMessage",
        id: "msg-2",
        conversationId: "conv-1",
        senderRole: "assistant",
        content: {
          text: "Rich message with attachments",
          attachments: [{ type: "image", url: "https://example.com/image.png" }],
        },
        timestamp: "2024-01-01T00:00:01Z",
        metadata: {
          processingTime: 250,
          model: "gpt-4",
        },
      };

      expect(typeof textMessage.content).toBe("string");
      expect(typeof richMessage.content).toBe("object");
      expect(richMessage.metadata).toHaveProperty("processingTime");
    });

    test("Conversation type should have correct structure", () => {
      const conversation: Conversation = {
        __typename: "Conversation",
        id: "conv-123",
        userId: "user-123",
        assistantId: "assistant-123",
        createdAt: "2024-01-01T00:00:00Z",
        lastMessageAt: "2024-01-01T12:00:00Z",
        messageCount: 42,
      };

      expect(conversation.id).toBeDefined();
      expect(conversation.userId).toBeDefined();
      expect(conversation.assistantId).toBeDefined();
      expect(typeof conversation.messageCount).toBe("number");
      expect(conversation.messageCount).toBeGreaterThan(0);
    });
  });

  describe("Type Constraints", () => {
    test("should enforce non-null constraints", () => {
      // This test validates that TypeScript compiler catches null assignments
      // to non-nullable fields. The test passes if the code compiles.

      const validUser: User = {
        __typename: "User",
        id: "test-id", // Required - cannot be null
        clerkUserId: "test-clerk-id", // Required - cannot be null
        email: "<EMAIL>", // Required - cannot be null
        timezone: null, // Optional - can be null
        preferences: {}, // Required but can be empty object
        isPaSetupComplete: false, // Required boolean
        createdAt: "2024-01-01", // Required
        updatedAt: "2024-01-01", // Required
      };

      expect(validUser.id).not.toBeNull();
      expect(validUser.email).not.toBeNull();
      expect(validUser.timezone).toBeNull(); // This is allowed
    });

    test("should handle union types correctly", () => {
      // Test that content can be either string or object
      const stringContent: ChatMessage["content"] = "Text content";
      const objectContent: ChatMessage["content"] = {
        text: "Rich content",
        formatting: { bold: true },
      };

      expect(typeof stringContent).toBe("string");
      expect(typeof objectContent).toBe("object");
    });
  });
});
