import { afterEach, beforeEach, describe, expect, jest, test } from "@jest/globals";
import { upgradeToHttps } from "../src/lib/mixed-content-detection";

describe("Mixed Content Detection", () => {
  // Mock console methods
  const originalConsoleWarn = console.warn;
  const originalConsoleError = console.error;

  beforeEach(() => {
    console.warn = jest.fn();
    console.error = jest.fn();

    // Mock document querySelector
    global.document.querySelectorAll = jest.fn().mockImplementation(selector => {
      if (selector === "img") {
        return [
          { src: "https://secure.example.com/image.jpg" },
          { src: "http://insecure.example.com/image.jpg" },
        ];
      } else if (selector === "script") {
        return [
          { src: "https://secure.example.com/script.js" },
          { src: "http://insecure.example.com/script.js" },
        ];
      } else if (selector === "iframe") {
        return [
          { src: "https://secure.example.com/frame.html" },
          { src: "http://insecure.example.com/frame.html" },
        ];
      } else if (selector === "link") {
        return [
          { href: "https://secure.example.com/style.css" },
          { href: "http://insecure.example.com/style.css" },
        ];
      }
      return [];
    });

    // Reset any mocks that might have been applied
    jest.resetModules();
  });

  afterEach(() => {
    console.warn = originalConsoleWarn;
    console.error = originalConsoleError;
    jest.resetAllMocks();
  });

  test("upgradeToHttps should properly upgrade HTTP URLs to HTTPS", () => {
    expect(upgradeToHttps("http://example.com")).toBe("https://example.com");
    expect(upgradeToHttps("https://example.com")).toBe("https://example.com");
    expect(upgradeToHttps("ws://example.com")).toBe("wss://example.com");
    expect(upgradeToHttps("wss://example.com")).toBe("wss://example.com");
  });

  test("upgradeToHttps should handle URLs with paths and query params", () => {
    expect(upgradeToHttps("http://example.com/path")).toBe("https://example.com/path");
    expect(upgradeToHttps("http://example.com/path?query=value")).toBe(
      "https://example.com/path?query=value",
    );
    expect(upgradeToHttps("ws://example.com/socket?token=123")).toBe(
      "wss://example.com/socket?token=123",
    );
  });
});
