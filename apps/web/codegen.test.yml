overwrite: true
# Schema from local file instead of live backend
schema: './src/graphql/schema.graphql'
documents:
  - 'src/graphql/operations.graphql'
  - 'src/**/*.{ts,tsx}'
generates:
  src/generated/graphql.ts:
    plugins:
      - typescript
      - typescript-operations
      - typed-document-node
    config:
      namingConvention:
        typeNames: pascal-case#pascalCase
        transformUnderscore: true
        enumValues: keep
        enumValuesTypeSuffix: Enum
      useTypeImports: true
      declarationKind: 'type'
      documentMode: documentNode
      enumsAsTypes: true
      dedupeFragments: true
      preResolveTypes: true
      flattenGeneratedTypes: true
      onlyOperationTypes: true
      avoidOptionals: true
      strictScalars: true
      inlineFragmentTypes: 'combine'
      scalars:
        DateTime: string
        JSON: 'Record<string, unknown>'
        UUID: string
      addDocBlocks: true
      addUnderscoreToArgsType: true
      documentNodeImport: '@graphql-typed-document-node/core#TypedDocumentNode'
