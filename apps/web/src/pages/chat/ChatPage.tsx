import React from "react";
import { useParams } from "react-router-dom";
import { ChatInterface } from "../../components/chat";
import { type Message } from "../../components/chat/types";
import { useConversationMessages } from "../../hooks/useConversationMessages";

export const ChatPage: React.FC = () => {
  // Get conversation ID from route parameters
  const { conversationId } = useParams<{ conversationId: string }>();

  // Fallback to demo ID if no conversationId in route
  const actualConversationId = conversationId || "demo-conversation-id";

  // Fetch existing messages for the conversation
  const {
    messages: existingMessages,
    loading: messagesLoading,
    error: messagesError,
  } = useConversationMessages({
    conversationId: actualConversationId,
    enabled: !!actualConversationId,
  });

  const handleMessageSent = (newMessage: Message) => {
    // In a real app, you might want to refetch messages or update cache
    console.log("Message sent:", newMessage);

    // For demo purposes, simulate an assistant response after a delay
    // Only if this is the demo conversation
    if (actualConversationId === "demo-conversation-id") {
      setTimeout(() => {
        const assistantResponse: Message = {
          id: `response-${Date.now()}`,
          conversationId: newMessage.conversationId,
          senderRole: "AGENT",
          content: { text: "Thank you for your message! I'm here to help." },
          timestamp: new Date().toISOString(),
          metadata: {},
        };
        // Don't add to local state - let the subscription handle it
        console.log("Assistant response:", assistantResponse);
      }, 1000);
    }
  };

  // Determine error state
  const error = messagesError ? `Failed to load messages: ${messagesError.message}` : undefined;

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-center mb-2">Chat with Your Personal Assistant</h1>
        <p className="text-muted-foreground text-center">
          Send messages to communicate with your AI assistant
        </p>
      </div>

      <ChatInterface
        conversationId={actualConversationId}
        assistantName="Personal Assistant"
        messages={existingMessages}
        onMessageSent={handleMessageSent}
        loading={messagesLoading}
        error={error}
      />
    </div>
  );
};
