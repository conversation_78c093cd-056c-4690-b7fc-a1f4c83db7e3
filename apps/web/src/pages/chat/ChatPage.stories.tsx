import React from "react";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { MemoryRouter, Route, Routes } from "react-router-dom";
import { ChatPage } from "./ChatPage";
import { type Message } from "../../components/chat/types";

// Mock messages for the chat
const mockMessages: Message[] = [
  {
    id: "msg-1",
    conversationId: "test-convo-1",
    senderRole: "agent",
    content: { text: "Hello! How can I assist you today?" },
    timestamp: new Date(Date.now() - 3600000).toISOString(),
    metadata: {},
  },
  {
    id: "msg-2",
    conversationId: "test-convo-1",
    senderRole: "user",
    content: { text: "I need help with my account." },
    timestamp: new Date(Date.now() - 1800000).toISOString(),
    metadata: {},
  },
];

const meta: Meta<typeof ChatPage> = {
  title: "Pages/Chat/ChatPage",
  component: ChatPage,
  decorators: [
    Story => (
      <MemoryRouter initialEntries={["/test/chat/test-convo-1"]}>
        <Routes>
          <Route path="/test/chat/:conversationId" element={<Story />} />
        </Routes>
      </MemoryRouter>
    ),
  ],
  parameters: {
    layout: "fullscreen",
    docs: {
      story: {
        inline: false,
        iframeHeight: 800,
      },
    },
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  name: "Default View",
  args: {},
  // Mock the ChatPage's internal state for the story
  render: function Render() {
    return <ChatPage />;
  },
};

export const WithInitialMessages: Story = {
  name: "With Initial Messages",
  args: {},
  // Override the default render to provide initial messages
  render: function Render() {
    const [_messages, setMessages] = useState<Message[]>(mockMessages);

    const _handleMessageSent = (newMessage: Message) => {
      // Simulate a response after a delay
      setTimeout(() => {
        const assistantResponse: Message = {
          id: `response-${Date.now()}`,
          conversationId: newMessage.conversationId,
          senderRole: "agent" as const,
          content: { text: "I've received your message. Let me help with that!" },
          timestamp: new Date().toISOString(),
          metadata: {},
        };
        setMessages(prev => [...prev, assistantResponse]);
      }, 1000);

      setMessages(prev => [...prev, newMessage]);
    };

    return (
      <div className="h-screen">
        <ChatPage />
      </div>
    );
  },
};
