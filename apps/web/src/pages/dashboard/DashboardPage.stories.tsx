import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react-vite";
import { MemoryRouter, Route, Routes } from "react-router-dom";
import { MockedProvider } from "@apollo/client/testing";
import DashboardPage from "./DashboardPage";
import { MY_CONVERSATIONS_QUERY, USER_HAS_ASSISTANT_QUERY } from "../../graphql/queries";
import React from "react";

const meta: Meta<typeof DashboardPage> = {
  title: "Pages/Dashboard/DashboardPage",
  component: DashboardPage,
  decorators: [
    (Story, { parameters }) => {
      const _clerkMock = parameters.clerkMock || {
        user: {
          id: "user_default_id",
          primaryEmailAddress: { emailAddress: "<EMAIL>" },
          fullName: "Default User",
        },
      };
      return (
        <MemoryRouter initialEntries={["/pa/dashboard"]}>
          <Routes>
            <Route path="/pa/dashboard" element={<Story />} />
            {/* Add sign-out redirect route if needed for testing signOut behavior */}
            <Route path="/pa/sign-in" element={<div>Mock Sign In Page (after sign out)</div>} />
          </Routes>
        </MemoryRouter>
      );
    },
  ],
  parameters: {
    layout: "fullscreen",
    docs: {
      story: {
        inline: false,
        iframeHeight: 600,
      },
    },
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  name: "Default View (With User Email)",
  parameters: {
    clerkMock: {
      user: {
        id: "user_123_abc",
        primaryEmailAddress: { emailAddress: "<EMAIL>" },
        fullName: "Test User FullName",
      },
    },
  },
};

export const WithUserFullName: Story = {
  name: "With User Full Name (No Email)",
  parameters: {
    clerkMock: {
      user: {
        id: "user_456_def",
        primaryEmailAddress: null, // Simulate no primary email
        fullName: "Jane Doe",
      },
    },
  },
};

export const WithMinimalUserData: Story = {
  name: "With Minimal User Data",
  parameters: {
    clerkMock: {
      user: {
        id: "user_789_ghi",
        primaryEmailAddress: null,
        fullName: null, // Simulate no full name either
      },
    },
  },
};

// The SignOut button's functionality relies on `useClerk().signOut()`.
// In a Storybook environment, this will call the mock Clerk instance.
// Full sign-out flow testing (including redirect) would require more setup
// or interaction testing. This story mainly shows the button is present.
export const SignOutButtonVisible: Story = {
  name: "Sign Out Button Presence",
  parameters: {
    clerkMock: {
      user: {
        id: "user_signout_test",
        primaryEmailAddress: { emailAddress: "<EMAIL>" },
        fullName: "Signout User",
      },
    },
  },
};

// New stories for conversation states
export const WithConversations: Story = {
  name: "User with Conversations",
  decorators: [
    (Story, { parameters }) => {
      const _clerkMock = parameters.clerkMock || {
        user: {
          id: "user_with_conversations",
          primaryEmailAddress: { emailAddress: "<EMAIL>" },
          fullName: "User With Conversations",
        },
      };

      const mocks = [
        {
          request: {
            query: USER_HAS_ASSISTANT_QUERY,
          },
          result: {
            data: {
              userHasAssistant: true,
            },
          },
        },
        {
          request: {
            query: MY_CONVERSATIONS_QUERY,
          },
          result: {
            data: {
              getOrCreateConversation: {
                id: "conv-1",
                userId: "user_with_conversations",
                assistantId: "assistant-1",
                createdAt: "2024-01-15T10:30:00Z",
                lastMessageAt: "2024-01-15T14:45:00Z",
              },
            },
          },
        },
      ];

      return (
        <MemoryRouter initialEntries={["/pa/dashboard"]}>
          <MockedProvider mocks={mocks} addTypename={false}>
            <Routes>
              <Route path="/pa/dashboard" element={<Story />} />
              <Route path="/pa/sign-in" element={<div>Mock Sign In Page</div>} />
            </Routes>
          </MockedProvider>
        </MemoryRouter>
      );
    },
  ],
};

export const EmptyConversationsState: Story = {
  name: "User with No Conversations (Empty State)",
  decorators: [
    (Story, { parameters }) => {
      const _clerkMock = parameters.clerkMock || {
        user: {
          id: "user_no_conversations",
          primaryEmailAddress: { emailAddress: "<EMAIL>" },
          fullName: "User No Conversations",
        },
      };

      const mocks = [
        {
          request: {
            query: USER_HAS_ASSISTANT_QUERY,
          },
          result: {
            data: {
              userHasAssistant: true,
            },
          },
        },
        {
          request: {
            query: MY_CONVERSATIONS_QUERY,
          },
          result: {
            data: {
              getOrCreateConversation: null,
            },
          },
        },
      ];

      return (
        <MemoryRouter initialEntries={["/pa/dashboard"]}>
          <MockedProvider mocks={mocks} addTypename={false}>
            <Routes>
              <Route path="/pa/dashboard" element={<Story />} />
              <Route path="/pa/sign-in" element={<div>Mock Sign In Page</div>} />
            </Routes>
          </MockedProvider>
        </MemoryRouter>
      );
    },
  ],
};
