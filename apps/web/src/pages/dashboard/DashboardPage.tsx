import React from "react";
import { useClerk, useUser } from "@clerk/clerk-react";
import { useQuery } from "@apollo/client";
import { Button } from "../../components/ui/button";
import { SetupWizard } from "../../components/pa_setup_wizard";
import { USER_HAS_ASSISTANT_QUERY } from "../../graphql/queries";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useConversations } from "../../hooks/useConversations";
import { EmptyConversationsState } from "../../components/dashboard/EmptyConversationsState";
import { ConversationList } from "../../components/dashboard/ConversationList";
import { debugError, debugLog } from "../../utils/debug";

const DashboardPage = () => {
  const { user, isLoaded: userLoaded } = useUser();
  const { signOut } = useClerk();
  const navigate = useNavigate();
  const [showSetupSuccess, setShowSetupSuccess] = useState(false);

  // Check for setup completion flag on component mount
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const setupCompleted = urlParams.get("setup-completed");

    if (setupCompleted === "true") {
      setShowSetupSuccess(true);

      // Remove the parameter from URL without page reload
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete("setup-completed");
      window.history.replaceState({}, "", newUrl.toString());

      // Hide the message after 4 seconds
      const timer = setTimeout(() => {
        setShowSetupSuccess(false);
      }, 4000);

      return () => clearTimeout(timer);
    }
  }, []);

  // Debug logging
  debugLog("DashboardPage render:", {
    userLoaded,
    user: user ? { id: user.id, email: user.primaryEmailAddress?.emailAddress } : null,
    skipQuery: !userLoaded || !user,
    showSetupSuccess,
  });

  // Check if user has an assistant - only run query when user is loaded
  const { data, loading, error } = useQuery(USER_HAS_ASSISTANT_QUERY, {
    skip: !userLoaded || !user, // Skip query if user is not loaded or not signed in
    errorPolicy: "all",
    onError: error => {
      debugError("GraphQL Error in USER_HAS_ASSISTANT_QUERY:", error);
      debugError("Error details:", {
        message: error.message,
        graphQLErrors: error.graphQLErrors,
        networkError: error.networkError,
      });
    },
    onCompleted: data => {
      debugLog("USER_HAS_ASSISTANT_QUERY completed:", data);
    },
  });

  // Get conversations data - only fetch when user has an assistant
  const {
    conversations,
    hasConversations,
    loading: conversationsLoading,
    error: conversationsError,
  } = useConversations({
    skip: !userLoaded || !user || !data?.userHasAssistant,
  });

  const handleSignOut = () => {
    signOut();
  };

  const handleSetupComplete = () => {
    // Redirect to dashboard with setup completion flag
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set("setup-completed", "true");
    window.location.href = currentUrl.toString();
  };

  const handleStartConversation = () => {
    // Navigate to chat page - this will trigger the getOrCreateConversation query
    navigate("/pa/chat");
  };

  const handleConversationClick = (conversationId: string) => {
    // Navigate to specific conversation
    navigate(`/pa/chat/${conversationId}`);
  };

  // Show loading state
  if (!userLoaded || loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Show error if user is not signed in
  if (!user) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">Please sign in to continue</p>
          <Button onClick={() => (window.location.href = "/sign-in")}>Sign In</Button>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error loading dashboard: {error.message}</p>
          <Button onClick={() => window.location.reload()}>Try Again</Button>
        </div>
      </div>
    );
  }

  // Show setup wizard if user doesn't have an assistant
  if (!data?.userHasAssistant) {
    return <SetupWizard onComplete={handleSetupComplete} />;
  }

  // Show main dashboard if user has an assistant
  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-10 border-b bg-[hsl(var(--background))]">
        <div className="container flex h-16 items-center justify-between py-4">
          <div className="flex items-center gap-2">
            <h1 className="text-xl font-bold">VEDAVIVI</h1>
          </div>
          <Button variant="outline" onClick={handleSignOut}>
            Sign out
          </Button>
        </div>
      </header>
      <main className="flex-1">
        <div className="container py-8">
          <div className="max-w-2xl mx-auto">
            {/* Welcome Section */}
            <div className="rounded-lg border bg-[hsl(var(--card))] p-6 shadow-sm mb-8">
              <h2 className="text-2xl font-bold mb-4">Welcome back!</h2>
              <p className="text-[hsl(var(--muted-foreground))] mb-6">
                You're signed in as{" "}
                {user?.primaryEmailAddress?.emailAddress || user?.fullName || "User"}
              </p>
              {showSetupSuccess && (
                <div className="grid gap-4">
                  <div className="rounded-md bg-green-50 border border-green-200 p-4">
                    <h3 className="font-medium mb-2 text-green-900">✅ Assistant Setup Complete</h3>
                    <p className="text-sm text-green-800">
                      Your personal assistant is ready to help!
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Conversations Section */}
            <div>
              {conversationsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-4"></div>
                  <p className="text-gray-600">Loading conversations...</p>
                </div>
              ) : conversationsError ? (
                <div className="text-center py-8">
                  <p className="text-red-600 mb-4">
                    Error loading conversations: {conversationsError.message}
                  </p>
                  <Button onClick={() => window.location.reload()}>Try Again</Button>
                </div>
              ) : hasConversations ? (
                <div>
                  <div className="mb-6">
                    <h3 className="text-xl font-semibold mb-2">Your Conversations</h3>
                    <p className="text-[hsl(var(--muted-foreground))]">
                      Continue your chats with your personal assistant
                    </p>
                  </div>
                  <ConversationList
                    conversations={conversations}
                    onConversationClick={handleConversationClick}
                  />
                </div>
              ) : (
                <EmptyConversationsState
                  onStartConversation={handleStartConversation}
                  loading={conversationsLoading}
                />
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default DashboardPage;
