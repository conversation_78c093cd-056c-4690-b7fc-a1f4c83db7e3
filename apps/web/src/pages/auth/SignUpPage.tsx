import React, { useState } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import { Link } from "react-router-dom";
import SignUpForm from "../../components/auth/SignUpForm";

/**
 * SignUpPage component that displays the sign-up options and form
 */
const SignUpPage = () => {
  const [verificationSent, setVerificationSent] = useState(false);
  const [verificationEmail, setVerificationEmail] = useState("");
  const [error, setError] = useState<string | null>(null);

  const handleVerificationSent = (email: string) => {
    setVerificationEmail(email);
    setVerificationSent(true);
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-[hsl(var(--background))] p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">Create an account</CardTitle>
          <CardDescription>Sign up to get started with VEDAVIVI</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <div
              className="p-3 text-sm bg-[hsl(var(--destructive))]/10 text-[hsl(var(--destructive))] rounded-md"
              role="alert"
              aria-live="assertive"
            >
              {error}
            </div>
          )}

          {verificationSent ? (
            <div className="p-4 text-center space-y-2">
              <h3 className="font-medium">Verification email sent!</h3>
              <p className="text-sm text-[hsl(var(--muted-foreground))]">
                Please check your email ({verificationEmail}) and click the link to complete your
                sign up.
              </p>
            </div>
          ) : (
            <SignUpForm onVerificationSent={handleVerificationSent} onError={handleError} />
          )}
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          <div className="text-sm text-center text-[hsl(var(--muted-foreground))]">
            By signing up, you agree to our{" "}
            <a href="#" className="underline underline-offset-4 hover:text-[hsl(var(--primary))]">
              Terms of Service
            </a>{" "}
            and{" "}
            <a href="#" className="underline underline-offset-4 hover:text-[hsl(var(--primary))]">
              Privacy Policy
            </a>
            .
          </div>
          <div className="text-sm text-center">
            Already have an account?{" "}
            <Link
              to="/pa/sign-in"
              className="underline underline-offset-4 hover:text-[hsl(var(--primary))]"
            >
              Sign in
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default SignUpPage;
