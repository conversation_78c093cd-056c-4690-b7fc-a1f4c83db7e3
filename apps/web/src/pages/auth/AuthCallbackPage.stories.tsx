import type { <PERSON>a, StoryObj } from "@storybook/react-vite";
import { MemoryRouter, Route, Routes } from "react-router-dom";
import AuthCallbackPage from "./AuthCallbackPage";
import React from "react";

const meta: Meta<typeof AuthCallbackPage> = {
  title: "Pages/Auth/AuthCallbackPage",
  component: AuthCallbackPage,
  parameters: {
    layout: "fullscreen",
    docs: {
      story: {
        inline: false,
        iframeHeight: 400,
      },
    },
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Helper function to wrap stories with providers and router
const StoryWrapper = ({
  initialEntries = ["/auth-callback"],
  children,
}: {
  initialEntries?: string[];
  children: React.ReactNode;
}) => (
  <MemoryRouter initialEntries={initialEntries}>
    <Routes>
      <Route path="/auth-callback" element={<>{children}</>} />
      <Route
        path="/pa/dashboard"
        element={<div>Mock Dashboard: Redirected after sign-in/up</div>}
      />
      <Route
        path="/pa/sign-in"
        element={<div>Mock Sign-In: Redirected on error or specific conditions</div>}
      />
    </Routes>
  </MemoryRouter>
);

export const DefaultProcessing: Story = {
  name: "Processing Callback",
  render: () => (
    <StoryWrapper>
      <AuthCallbackPage />
    </StoryWrapper>
  ),
};

export const WithErrorInUrl: Story = {
  name: "Error from OAuth Provider",
  render: () => (
    <StoryWrapper
      initialEntries={[
        "/auth-callback?error=oauth_error&error_description=OAuth+provider+returned+an+error.",
      ]}
    >
      <AuthCallbackPage />
    </StoryWrapper>
  ),
};

// Simulating a successful callback that leads to navigation is tricky in Storybook
// because `handleRedirectCallback` internally manages redirects.
// This story will likely show the "Processing..." state unless `handleRedirectCallback` is mocked
// to immediately navigate or resolve in a way that changes the component's state.
// For now, it demonstrates the component rendering.
export const SimulatingSuccess: Story = {
  name: "Simulating Successful Callback (Conceptual)",
  render: () => (
    <StoryWrapper>
      <AuthCallbackPage />
    </StoryWrapper>
  ),
  // To truly test the success path, you'd mock `useClerk`'s `handleRedirectCallback`
  // to simulate a successful promise resolution and navigation.
  // e.g., mock it to call `navigate('/pa/dashboard')`.
};

// Note: Mocking Clerk's handleRedirectCallback error now handled by global ClerkProvider

export const ErrorDuringProcessing: Story = {
  name: "Error During Clerk Processing",
  render: () => (
    <MemoryRouter initialEntries={["/auth-callback"]}>
      <Routes>
        <Route path="/auth-callback" element={<AuthCallbackPage />} />
        <Route path="/pa/sign-in" element={<div>Mock Sign-In: Redirected on error</div>} />
      </Routes>
    </MemoryRouter>
  ),
};
