import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import { MemoryRouter, Route, Routes } from "react-router-dom";
import SignInPage from "./SignInPage";
import React from "react";

const meta: Meta<typeof SignInPage> = {
  title: "Pages/Auth/SignInPage",
  component: SignInPage,
  decorators: [
    Story => (
      <MemoryRouter initialEntries={["/pa/sign-in"]}>
        <Routes>
          <Route path="/pa/sign-in" element={<Story />} />
          <Route path="/pa/sign-up" element={<div>Mock Sign Up Page</div>} />
          {/* Add other routes Link might point to if necessary for testing */}
        </Routes>
      </MemoryRouter>
    ),
  ],
  parameters: {
    layout: "fullscreen",
    docs: {
      story: {
        inline: false,
        iframeHeight: 600,
      },
    },
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  name: "Default Sign-In Form",
};

// To simulate verification sent or error states, we would ideally pass props
// or use component's internal logic. Since SignInPage manages these states internally
// based on SignInForm's callbacks, we can't directly set these states via args
// without modifying SignInPage or using more complex interaction tests.

// The stories below are conceptual. To make them work, you'd need to:
// 1. Modify SignInPage to accept initial state props (e.g., `initialError`, `initialVerificationSent`).
// OR
// 2. Use Storybook's `play` function with `@storybook/addon-interactions` to simulate user actions
//    that trigger these states (e.g., mock `SignInForm` to call `onError` or `onVerificationSent`).

export const VerificationSent: Story = {
  name: "After Email Verification Sent",
  // This story requires internal state change.
  // Conceptual: If SignInPage took props for initial state:
  // args: {
  //   initialVerificationEmail: '<EMAIL>',
  //   initialVerificationSent: true,
  // }
  // For now, it will render the default state.
  // A `play` function could simulate the form submission and callback.
  parameters: {
    docs: {
      description: {
        story:
          "This state is normally reached after the user submits the email form. To view this state directly in Storybook without interaction, the component would need to support an initial state prop. Alternatively, use Storybook interactions to fill and submit the form.",
      },
    },
  },
};

export const WithError: Story = {
  name: "With Error Message Displayed",
  // Conceptual: If SignInPage took props for initial state:
  // args: {
  //   initialError: 'Invalid email or password.',
  // }
  // For now, it will render the default state.
  // A `play` function could simulate an error callback.
  parameters: {
    docs: {
      description: {
        story:
          "This state is normally reached if the sign-in attempt fails. To view this state directly in Storybook without interaction, the component would need to support an initial state prop. Alternatively, use Storybook interactions to trigger an error.",
      },
    },
  },
};
