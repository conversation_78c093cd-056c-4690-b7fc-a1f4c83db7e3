import { render, screen } from "@testing-library/react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useClerk } from "@clerk/clerk-react";
import AuthCallbackPage from "../AuthCallbackPage";

// Mock the react-router-dom hooks
jest.mock("react-router-dom", () => ({
  useNavigate: jest.fn(),
  useSearchParams: jest.fn(),
}));

// Mock the Clerk hook
jest.mock("@clerk/clerk-react", () => ({
  useClerk: jest.fn(),
}));

describe("AuthCallbackPage", () => {
  const mockNavigate = jest.fn();
  const mockHandleRedirectCallback = jest.fn();
  const mockSearchParams = new URLSearchParams();
  const mockSetSearchParams = jest.fn();

  beforeEach(() => {
    (useNavigate as jest.Mock).mockReturnValue(mockNavigate);
    (useSearchParams as jest.Mock).mockReturnValue([mockSearchParams, mockSetSearchParams]);
    (useClerk as jest.Mock).mockReturnValue({
      handleRedirectCallback: mockHandleRedirectCallback,
    });

    // Mock the handleRedirectCallback to resolve successfully
    mockHandleRedirectCallback.mockResolvedValue({
      createdSessionId: "test_session_id",
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("renders the loading state correctly", () => {
    render(<AuthCallbackPage />);

    expect(screen.getByText("Processing your authentication...")).toBeInTheDocument();
    expect(screen.getByText("Please wait while we complete your sign-in.")).toBeInTheDocument();
  });

  it("calls handleRedirectCallback with correct parameters", () => {
    render(<AuthCallbackPage />);

    expect(mockHandleRedirectCallback).toHaveBeenCalledWith({
      afterSignInUrl: "/pa/dashboard",
      afterSignUpUrl: "/pa/dashboard",
      redirectUrl: "/pa/sign-in",
    });
  });

  it("handles error from URL parameters", () => {
    // Setup error params
    const errorParams = new URLSearchParams();
    errorParams.set("error", "access_denied");
    errorParams.set("error_description", "User denied access");

    (useSearchParams as jest.Mock).mockReturnValue([errorParams, mockSetSearchParams]);

    render(<AuthCallbackPage />);

    // Check that error is displayed
    expect(screen.getByText("Authentication Error")).toBeInTheDocument();
    expect(screen.getByText("User denied access")).toBeInTheDocument();
    expect(screen.getByText("Return to Sign In")).toBeInTheDocument();
  });
});
