import { render, screen } from "@testing-library/react";
import SignInPage from "../SignInPage";

// Mock the SignInForm component
jest.mock("../../../components/auth/SignInForm", () => {
  return {
    __esModule: true,
    default: () => <div data-testid="mock-signin-form">Mock SignIn Form</div>,
  };
});

describe("SignInPage", () => {
  it("renders the sign-in page correctly", () => {
    render(<SignInPage />);

    // Check for main elements
    expect(screen.getByText("Sign in to your account")).toBeInTheDocument();
    expect(screen.getByText("Sign in to access VEDAVIVI")).toBeInTheDocument();

    // Check that the form is rendered
    expect(screen.getByTestId("mock-signin-form")).toBeInTheDocument();
  });
});
