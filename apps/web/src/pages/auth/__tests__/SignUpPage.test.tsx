import { render, screen } from "@testing-library/react";
import SignUpPage from "../SignUpPage";

// Mock the SignUpForm component
jest.mock("../../../components/auth/SignUpForm", () => {
  return {
    __esModule: true,
    default: () => <div data-testid="mock-signup-form">Mock SignUp Form</div>,
  };
});

describe("SignUpPage", () => {
  it("renders the sign-up page correctly", () => {
    render(<SignUpPage />);

    // Check for main elements
    expect(screen.getByText("Create an account")).toBeInTheDocument();
    expect(screen.getByText("Sign up to get started with VEDAVIVI")).toBeInTheDocument();

    // Check that the form is rendered
    expect(screen.getByTestId("mock-signup-form")).toBeInTheDocument();
  });
});
