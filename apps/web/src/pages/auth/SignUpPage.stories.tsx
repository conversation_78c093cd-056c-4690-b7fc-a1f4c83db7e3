import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import { MemoryRouter, Route, Routes } from "react-router-dom";
import SignUpPage from "./SignUpPage";
import React from "react";

const meta: Meta<typeof SignUpPage> = {
  title: "Pages/Auth/SignUpPage",
  component: SignUpPage,
  decorators: [
    Story => (
      <MemoryRouter initialEntries={["/pa/sign-up"]}>
        <Routes>
          <Route path="/pa/sign-up" element={<Story />} />
          <Route path="/pa/sign-in" element={<div>Mock Sign In Page</div>} />
          {/* Add other routes Link might point to if necessary for testing */}
        </Routes>
      </MemoryRouter>
    ),
  ],
  parameters: {
    layout: "fullscreen",
    docs: {
      story: {
        inline: false,
        iframeHeight: 700, // Adjusted height for potentially longer content
      },
    },
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  name: "Default Sign-Up Form",
};

// Similar to SignInPage, simulating verificationSent or error states
// without interaction or modifying SignUpPage is conceptual.
export const VerificationSent: Story = {
  name: "After Email Verification Sent",
  parameters: {
    docs: {
      description: {
        story:
          "This state is normally reached after the user submits the email form for sign-up. To view this state directly in Storybook without interaction, the component would need to support an initial state prop. Alternatively, use Storybook interactions to fill and submit the form.",
      },
    },
  },
};

export const WithError: Story = {
  name: "With Error Message Displayed",
  parameters: {
    docs: {
      description: {
        story:
          "This state is normally reached if the sign-up attempt fails (e.g., email already exists, network error). To view this state directly in Storybook without interaction, the component would need to support an initial state prop. Alternatively, use Storybook interactions to trigger an error.",
      },
    },
  },
};
