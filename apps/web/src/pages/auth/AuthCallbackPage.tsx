import React, { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useClerk } from "@clerk/clerk-react";

/**
 * AuthCallbackPage handles redirects from authentication providers
 * and processes the authentication result.
 */
const AuthCallbackPage = () => {
  const { handleRedirectCallback } = useClerk();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Check for error in URL params (some OAuth providers return errors this way)
    const errorParam = searchParams.get("error");
    const errorDescription = searchParams.get("error_description");

    if (errorParam) {
      console.error(`Auth error: ${errorParam} - ${errorDescription}`);
      setError(errorDescription || "Authentication failed. Please try again.");
      return;
    }

    // Process the authentication callback
    handleRedirectCallback({
      // After successful authentication, redirect to dashboard
      afterSignInUrl: "/pa/dashboard",
      afterSignUpUrl: "/pa/dashboard",
      // If there's an error, redirect back to sign-in
      redirectUrl: "/pa/sign-in",
    })
      .then(() => {
        // After successful authentication, navigate to dashboard
        navigate("/pa/dashboard");
      })
      .catch((err: Error & { code?: string }) => {
        console.error("Error handling auth callback:", err);

        // Handle specific error types
        if (err.code === "network_error") {
          setError("Network error. Please check your internet connection and try again.");
        } else if (err.code === "not_found_error") {
          setError("Account not found. Please sign up first.");
        } else if (err.message?.includes("session")) {
          setError("Session error. Please try signing in again.");
        } else {
          setError(err.message || "Authentication failed. Please try again.");
        }
      });
  }, [handleRedirectCallback, navigate, searchParams]);

  // If there's an error, show it and provide a way to go back to sign-in
  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-[hsl(var(--background))]">
        <div className="text-center max-w-md p-6">
          <h1 className="text-2xl font-bold mb-4">Authentication Error</h1>
          <div
            className="p-3 mb-4 text-sm bg-[hsl(var(--destructive))]/10 text-[hsl(var(--destructive))] rounded-md"
            role="alert"
          >
            {error}
          </div>
          <button
            onClick={() => navigate("/pa/sign-in")}
            className="px-4 py-2 bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] rounded-md hover:bg-[hsl(var(--primary))]/90"
          >
            Return to Sign In
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-[hsl(var(--background))]">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Processing your authentication...</h1>
        <p className="text-[hsl(var(--muted-foreground))]">
          Please wait while we complete your sign-in.
        </p>
        <div className="mt-6 flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[hsl(var(--primary))]"></div>
        </div>
      </div>
    </div>
  );
};

export default AuthCallbackPage;
