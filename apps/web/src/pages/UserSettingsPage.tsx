import React from "react";
import { RedirectToSignIn, SignedIn, SignedOut, useAuth } from "@clerk/clerk-react";
import CliTokenManager from "../components/CliTokenManager"; // Adjust path if necessary

const UserSettingsPage: React.FC = () => {
  const { isLoaded } = useAuth();

  // Show loading state while Clerk is initializing
  if (!isLoaded) {
    return <div>Loading authentication status...</div>;
  }

  return (
    <div className="container mx-auto p-4 md:p-8">
      {" "}
      {/* Responsive padding */}
      <SignedIn>
        {/* Only show content to authenticated users */}
        <header className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800">User Settings</h1>
        </header>

        <section className="bg-white shadow-xl rounded-lg p-6 md:p-8">
          <h2 className="text-2xl font-semibold text-gray-700 mb-4">CLI Access Tokens</h2>
          <p className="mb-6 text-gray-600">
            Manage your CLI access tokens. These tokens allow you to authenticate your identity when
            using our command-line interface (CLI) tools. Treat them like passwords and keep them
            secure.
          </p>
          <CliTokenManager />
        </section>

        {/*
          Future sections for other user settings can be added here, for example:
          <section className="bg-white shadow-xl rounded-lg p-6 md:p-8 mt-8">
            <h2 className="text-2xl font-semibold text-gray-700 mb-4">Profile Information</h2>
            <p className="mb-6 text-gray-600">
              Update your personal details.
            </p>
            {/* ...Profile form components... * /}
          </section>

          <section className="bg-white shadow-xl rounded-lg p-6 md:p-8 mt-8">
            <h2 className="text-2xl font-semibold text-gray-700 mb-4">Notification Preferences</h2>
            <p className="mb-6 text-gray-600">
              Manage how you receive notifications from us.
            </p>
            {/* ...Notification settings components... * /}
          </section>
        */}
      </SignedIn>
      <SignedOut>
        <RedirectToSignIn />
      </SignedOut>
    </div>
  );
};

export default UserSettingsPage;
