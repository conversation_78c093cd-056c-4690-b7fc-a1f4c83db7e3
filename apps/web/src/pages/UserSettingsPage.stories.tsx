import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import { MemoryRouter } from "react-router-dom"; // For RedirectToSignIn
import { ApolloClient, ApolloProvider, InMemoryCache, createHttpLink } from "@apollo/client";
import UserSettingsPage from "./UserSettingsPage";
import React from "react";

// Mock Apollo Client
const mockHttpLink = createHttpLink({
  uri: "https://mock.graphql.api", // This URI will not be hit
});

const mockApolloClient = new ApolloClient({
  link: mockHttpLink,
  cache: new InMemoryCache(),
});

const meta: Meta<typeof UserSettingsPage> = {
  title: "Pages/UserSettingsPage",
  component: UserSettingsPage,
  decorators: [
    (Story, { parameters }) => {
      // Default to signed-in state for most stories
      const _clerkMock = parameters.clerkMock || {
        user: {
          id: "user_123",
          primaryEmailAddress: { emailAddress: "<EMAIL>" },
        },
      };

      return (
        <MemoryRouter initialEntries={["/settings"]}>
          <ApolloProvider client={mockApolloClient}>
            <Story />
          </ApolloProvider>
        </MemoryRouter>
      );
    },
  ],
  parameters: {
    layout: "fullscreen", // Pages often take full screen
    docs: {
      story: {
        inline: false,
        iframeHeight: 700,
      },
    },
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const DefaultSignedIn: Story = {
  name: "Signed In State",
  parameters: {
    clerkMock: {
      user: {
        id: "user_123",
        primaryEmailAddress: { emailAddress: "<EMAIL>" },
      },
    },
  },
};

export const LoadingState: Story = {
  name: "Loading Authentication",
  parameters: {
    // To simulate loading, we might need a ClerkProvider that sets isLoaded to false.
    // This is hard to achieve without more complex mocking of useAuth.
    // For now, this story will likely show the signed-in state due to default provider setup.
    // A more accurate loading story would involve deeper Clerk mocking.
    clerkMock: { user: null }, // Attempt to show loading or signed out
  },
  // If ClerkProvider could be configured to simulate `isLoaded: false` easily, that would be ideal.
  // For example, if there was a prop like `initialIsLoaded={false}`.
  // This story is more conceptual without deeper hook mocking.
  decorators: [
    // Override default decorator for this specific story
    Story => (
      <MemoryRouter initialEntries={["/settings"]}>
        {/* Intentionally not providing user/session to see if loading shows or redirects */}
        <ApolloProvider client={mockApolloClient}>
          <Story />
        </ApolloProvider>
      </MemoryRouter>
    ),
  ],
};

export const SignedOutRedirect: Story = {
  name: "Signed Out (Redirects)",
  parameters: {
    clerkMock: { user: null }, // Ensure no user is set
  },
  // This story should demonstrate the <RedirectToSignIn /> behavior.
  // MemoryRouter is important here.
};
