import React from "react";
import { act, render, screen } from "@testing-library/react";

// Simple test component to verify our setup
const TestComponent = ({ message }: { message: string }) => {
  return <div data-testid="test-message">{message}</div>;
};

describe("Test Setup Verification", () => {
  it("should render a simple component", () => {
    render(<TestComponent message="Hello World" />);
    expect(screen.getByTestId("test-message")).toBeInTheDocument();
    expect(screen.getByText(/hello world/i)).toBeInTheDocument();
  });

  it("should handle act() correctly", async () => {
    const message = "Initial";

    const AsyncComponent = () => {
      const [text, setText] = React.useState(message);

      React.useEffect(() => {
        const timer = setTimeout(() => {
          setText("Updated");
        }, 10);
        return () => clearTimeout(timer);
      }, []);

      return <div data-testid="async-message">{text}</div>;
    };

    await act(async () => {
      render(<AsyncComponent />);
    });

    // Initial render
    expect(screen.getByTestId("async-message")).toHaveTextContent("Initial");

    // Wait for async update
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 20));
    });

    expect(screen.getByTestId("async-message")).toHaveTextContent("Updated");
  });

  it("should verify IS_REACT_ACT_ENVIRONMENT is set", () => {
    // @ts-ignore - accessing global for test
    expect(global.IS_REACT_ACT_ENVIRONMENT).toBe(true);
  });
});
