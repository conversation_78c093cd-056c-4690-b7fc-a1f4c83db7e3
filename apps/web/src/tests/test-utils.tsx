/* eslint-disable react-refresh/only-export-components */
import React, { ReactElement } from "react";
import { RenderOptions, RenderResult, render } from "@testing-library/react";
import { act } from "@testing-library/react";
import userEvent from "@testing-library/user-event";

// Re-export everything from React Testing Library
export * from "@testing-library/react";

// Custom render function with common providers
interface CustomRenderOptions extends Omit<RenderOptions, "wrapper"> {
  // Add any custom options here for providers if needed
  preloadedState?: Record<string, unknown>;
}

const customRender = (ui: ReactElement, options?: CustomRenderOptions): RenderResult => {
  // You can add common providers here (Apollo, Router, etc.)
  // For now, just use the basic render
  return render(ui, options);
};

// Enhanced user event with proper act() wrapping
export const createUserEvent = () => {
  return userEvent.setup({
    // Configure user-event to work properly with act()
    advanceTimers: jest.advanceTimersByTime,
  });
};

// Utility function to wait for async operations with act()
export const waitForAsyncOperations = async () => {
  await act(async () => {
    // Wait for any pending timers or promises
    await new Promise(resolve => setTimeout(resolve, 0));
  });
};

// Enhanced form submission helper with act()
export const submitForm = async (form: HTMLElement) => {
  await act(async () => {
    form.dispatchEvent(new Event("submit", { bubbles: true, cancelable: true }));
  });
};

// Enhanced click helper with act()
export const clickElement = async (element: HTMLElement) => {
  const user = createUserEvent();
  await act(async () => {
    await user.click(element);
  });
};

// Enhanced type helper with act()
export const typeInElement = async (element: HTMLElement, text: string) => {
  const user = createUserEvent();
  await act(async () => {
    await user.type(element, text);
  });
};

// Enhanced clear and type helper with act()
export const clearAndType = async (element: HTMLElement, text: string) => {
  const user = createUserEvent();
  await act(async () => {
    await user.clear(element);
    await user.type(element, text);
  });
};

// Helper for testing async state updates
export const waitForStateUpdate = async (callback: () => void | Promise<void>) => {
  await act(async () => {
    await callback();
  });
};

// Helper for testing component mounting with async effects
export const renderWithAsyncEffects = async (ui: ReactElement, options?: CustomRenderOptions) => {
  let result: RenderResult;

  await act(async () => {
    result = customRender(ui, options);
  });

  return result!;
};

// Mock timer utilities with act()
export const advanceTimersWithAct = async (ms: number) => {
  await act(async () => {
    jest.advanceTimersByTime(ms);
  });
};

export const runAllTimersWithAct = async () => {
  await act(async () => {
    jest.runAllTimers();
  });
};

// Override the default render to use our custom one
export { customRender as render };

// Common test utilities for React components
export const createMockComponent = (name: string) => {
  const MockComponent = (props: Record<string, unknown>) => (
    <div data-testid={`mock-${name.toLowerCase()}`} {...props}>
      Mock {name}
    </div>
  );
  MockComponent.displayName = `Mock${name}`;
  return MockComponent;
};

// Utility for testing error boundaries
export const ThrowError = ({ error }: { error: Error }) => {
  throw error;
};

// Common assertions for React components
export const expectElementToBeInDocument = (element: HTMLElement | null) => {
  expect(element).toBeInTheDocument();
};

export const expectElementNotToBeInDocument = (element: HTMLElement | null) => {
  expect(element).not.toBeInTheDocument();
};

export const expectElementToBeDisabled = (element: HTMLElement) => {
  expect(element).toBeDisabled();
};

export const expectElementToBeEnabled = (element: HTMLElement) => {
  expect(element).toBeEnabled();
};
