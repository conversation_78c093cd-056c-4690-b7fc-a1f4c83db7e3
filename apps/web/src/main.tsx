import React, { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import App from "./App.tsx";

// Extend Window interface for Apollo dev messages flag
declare global {
  interface Window {
    __APOLLO_DEV_MESSAGES_LOADED__?: boolean;
  }
}

// Load Apollo Client error messages in development and staging
// This prevents the go.apollo.dev URLs from appearing in error messages
// and shows actual error text instead
if (
  import.meta.env.DEV ||
  import.meta.env.MODE === "development" ||
  import.meta.env.MODE === "staging"
) {
  // Check if we're in a browser environment
  if (typeof window !== "undefined") {
    // Prevent multiple loads
    if (!window.__APOLLO_DEV_MESSAGES_LOADED__) {
      window.__APOLLO_DEV_MESSAGES_LOADED__ = true;

      import("@apollo/client/dev")
        .then(({ loadErrorMessages, loadDevMessages }) => {
          loadDevMessages();
          loadErrorMessages();
          console.log("Apollo Client dev messages loaded successfully");
        })
        .catch(error => {
          console.warn("Failed to load Apollo Client dev messages:", error);
        });
    }
  }
}

// Mount the app
createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <App />
  </StrictMode>,
);
