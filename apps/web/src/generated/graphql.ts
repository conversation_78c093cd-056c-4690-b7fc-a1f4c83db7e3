import type { TypedDocumentNode as DocumentNode } from "@graphql-typed-document-node/core";
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = {
  [_ in K]?: never;
};
export type Incremental<T> =
  | T
  | { [P in keyof T]?: P extends " $fragmentName" | "__typename" ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string };
  String: { input: string; output: string };
  Boolean: { input: boolean; output: boolean };
  Int: { input: number; output: number };
  Float: { input: number; output: number };
  /** Date with time (isoformat) */
  DateTime: { input: string; output: string };
  /** The `JSON` scalar type represents JSON values as specified by [ECMA-404](https://ecma-international.org/wp-content/uploads/ECMA-404_2nd_edition_december_2017.pdf). */
  JSON: { input: Record<string, unknown>; output: Record<string, unknown> };
  UUID: { input: string; output: string };
};

export type AgentPricingInfoInput = {
  amountMonthly: InputMaybe<Scalars["Float"]["input"]>;
  amountPerCall: InputMaybe<Scalars["Float"]["input"]>;
  currency: InputMaybe<Scalars["String"]["input"]>;
  features: InputMaybe<Array<Scalars["String"]["input"]>>;
  pricingType: Scalars["String"]["input"];
  tierId: InputMaybe<Scalars["String"]["input"]>;
};

export type AgentSkillInput = {
  description: InputMaybe<Scalars["String"]["input"]>;
  name: Scalars["String"]["input"];
  parametersSchema: InputMaybe<Scalars["String"]["input"]>;
};

export type ChatMessageConnectionArgs = {
  after: InputMaybe<Scalars["String"]["input"]>;
  before: InputMaybe<Scalars["String"]["input"]>;
  first: InputMaybe<Scalars["Int"]["input"]>;
  last: InputMaybe<Scalars["Int"]["input"]>;
};

export type CreateAssistantInput = {
  avatarFileId: InputMaybe<Scalars["UUID"]["input"]>;
  backstory: Scalars["String"]["input"];
  configuration: InputMaybe<Scalars["JSON"]["input"]>;
  name: Scalars["String"]["input"];
};

export type CreateCliTokenInput = {
  description: InputMaybe<Scalars["String"]["input"]>;
};

export type RegisterMarketplaceAgentInput = {
  agentDefinitionId: Scalars["String"]["input"];
  asyncQueueName: InputMaybe<Scalars["String"]["input"]>;
  description: InputMaybe<Scalars["String"]["input"]>;
  developerId: Scalars["String"]["input"];
  endpointUrl: InputMaybe<Scalars["String"]["input"]>;
  name: Scalars["String"]["input"];
  pricingInfo: InputMaybe<AgentPricingInfoInput>;
  skills: Array<AgentSkillInput>;
  version: Scalars["String"]["input"];
};

export type SendMessageFromPaInput = {
  content: Scalars["String"]["input"];
  conversationId: Scalars["ID"]["input"];
  metadata: InputMaybe<Scalars["JSON"]["input"]>;
};

export type UpdateAssistantInput = {
  avatarFileId: InputMaybe<Scalars["UUID"]["input"]>;
  backstory: InputMaybe<Scalars["String"]["input"]>;
  configuration: InputMaybe<Scalars["JSON"]["input"]>;
  name: InputMaybe<Scalars["String"]["input"]>;
};

export type UpdateMarketplaceAgentInput = {
  agentDefinitionId: Scalars["String"]["input"];
  asyncQueueName: InputMaybe<Scalars["String"]["input"]>;
  description: InputMaybe<Scalars["String"]["input"]>;
  endpointUrl: InputMaybe<Scalars["String"]["input"]>;
  name: InputMaybe<Scalars["String"]["input"]>;
  pricingInfo: InputMaybe<AgentPricingInfoInput>;
  skills: InputMaybe<Array<AgentSkillInput>>;
  status: InputMaybe<Scalars["String"]["input"]>;
  version: InputMaybe<Scalars["String"]["input"]>;
};

export type UserProfilePreferencesInput = {
  preferences: InputMaybe<Scalars["JSON"]["input"]>;
  timezone: InputMaybe<Scalars["String"]["input"]>;
};

export type MeQueryVariables = Exact<{ [key: string]: never }>;

export type MeQuery = {
  __typename?: "Query";
  me: {
    __typename?: "User";
    id: string;
    clerkUserId: string;
    email: string;
    timezone: string | null;
    preferences: Record<string, unknown>;
    isPaSetupComplete: boolean;
    createdAt: string;
    updatedAt: string;
  };
};

export type UserHasAssistantQueryVariables = Exact<{ [key: string]: never }>;

export type UserHasAssistantQuery = { __typename?: "Query"; userHasAssistant: boolean };

export type MyAssistantQueryVariables = Exact<{ [key: string]: never }>;

export type MyAssistantQuery = {
  __typename?: "Query";
  myAssistant: {
    __typename?: "Assistant";
    id: string;
    name: string;
    backstory: string;
    avatarFileId: string | null;
    configuration: Record<string, unknown> | null;
    createdAt: string;
    updatedAt: string;
  } | null;
};

export type GetOrCreateConversationQueryVariables = Exact<{ [key: string]: never }>;

export type GetOrCreateConversationQuery = {
  __typename?: "Query";
  getOrCreateConversation: {
    __typename?: "Conversation";
    id: string;
    userId: string;
    assistantId: string;
    createdAt: string;
    lastMessageAt: string | null;
    messageCount: number;
  };
};

export type MyConversationsQueryVariables = Exact<{ [key: string]: never }>;

export type MyConversationsQuery = {
  __typename?: "Query";
  getOrCreateConversation: {
    __typename?: "Conversation";
    id: string;
    userId: string;
    assistantId: string;
    createdAt: string;
    lastMessageAt: string | null;
    messageCount: number;
  };
};

export type GetConversationMessagesQueryVariables = Exact<{
  conversationId: Scalars["ID"]["input"];
  first?: InputMaybe<Scalars["Int"]["input"]>;
  after: InputMaybe<Scalars["String"]["input"]>;
  last: InputMaybe<Scalars["Int"]["input"]>;
  before: InputMaybe<Scalars["String"]["input"]>;
}>;

export type GetConversationMessagesQuery = {
  __typename?: "Query";
  getConversationMessages: {
    __typename?: "ChatMessageConnection";
    totalCount: number | null;
    edges: Array<{
      __typename?: "ChatMessageEdge";
      cursor: string;
      node: {
        __typename?: "ChatMessage";
        id: string;
        conversationId: string;
        senderRole: string;
        content: Record<string, unknown>;
        timestamp: string;
        metadata: Record<string, unknown> | null;
      };
    }>;
    pageInfo: {
      __typename?: "PageInfo";
      hasNextPage: boolean;
      hasPreviousPage: boolean;
      startCursor: string | null;
      endCursor: string | null;
    };
  };
};

export type CreatePersonalAssistantMutationVariables = Exact<{
  input: CreateAssistantInput;
}>;

export type CreatePersonalAssistantMutation = {
  __typename?: "Mutation";
  createPersonalAssistant: {
    __typename?: "AssistantMutationPayload";
    success: boolean;
    message: string;
    assistant: {
      __typename?: "Assistant";
      id: string;
      name: string;
      backstory: string;
      avatarFileId: string | null;
      configuration: Record<string, unknown> | null;
      createdAt: string;
      updatedAt: string;
    };
  };
};

export type UpdatePersonalAssistantMutationVariables = Exact<{
  input: UpdateAssistantInput;
}>;

export type UpdatePersonalAssistantMutation = {
  __typename?: "Mutation";
  updatePersonalAssistant: {
    __typename?: "AssistantMutationPayload";
    success: boolean;
    message: string;
    assistant: {
      __typename?: "Assistant";
      id: string;
      name: string;
      backstory: string;
      avatarFileId: string | null;
      configuration: Record<string, unknown> | null;
      createdAt: string;
      updatedAt: string;
    };
  };
};

export type SendMessageMutationVariables = Exact<{
  conversationId: Scalars["ID"]["input"];
  content: Scalars["String"]["input"];
}>;

export type SendMessageMutation = {
  __typename?: "Mutation";
  sendMessage: {
    __typename?: "SendMessagePayload";
    success: boolean;
    errorMessage: string | null;
    message: {
      __typename?: "ChatMessage";
      id: string;
      conversationId: string;
      senderRole: string;
      content: Record<string, unknown>;
      timestamp: string;
      metadata: Record<string, unknown> | null;
    };
  };
};

export type SendMessageFromPaMutationVariables = Exact<{
  input: SendMessageFromPaInput;
}>;

export type SendMessageFromPaMutation = {
  __typename?: "Mutation";
  sendMessageFromPA: {
    __typename?: "SendMessagePayload";
    success: boolean;
    errorMessage: string | null;
    message: {
      __typename?: "ChatMessage";
      id: string;
      conversationId: string;
      senderRole: string;
      content: Record<string, unknown>;
      timestamp: string;
      metadata: Record<string, unknown> | null;
    };
  };
};

export type CreateCliTokenMutationVariables = Exact<{
  description: Scalars["String"]["input"];
}>;

export type CreateCliTokenMutation = {
  __typename?: "Mutation";
  createCliToken: {
    __typename?: "CreateCliTokenPayload";
    token: string;
    cliToken: {
      __typename?: "CliToken";
      id: string;
      tokenPrefix: string;
      description: string | null;
      createdAt: string;
      expiresAt: string | null;
      lastUsedAt: string | null;
    };
  };
};

export type NewMessagesSubscriptionVariables = Exact<{
  conversationId: Scalars["ID"]["input"];
}>;

export type NewMessagesSubscription = {
  __typename?: "Subscription";
  newMessages: {
    __typename?: "MessageSubscriptionPayload";
    conversationId: string;
    eventType: string;
    message: {
      __typename?: "ChatMessage";
      id: string;
      conversationId: string;
      senderRole: string;
      content: Record<string, unknown>;
      timestamp: string;
      metadata: Record<string, unknown> | null;
    };
  };
};

export const MeDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "Me" },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "me" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "clerkUserId" } },
                { kind: "Field", name: { kind: "Name", value: "email" } },
                { kind: "Field", name: { kind: "Name", value: "timezone" } },
                { kind: "Field", name: { kind: "Name", value: "preferences" } },
                { kind: "Field", name: { kind: "Name", value: "isPaSetupComplete" } },
                { kind: "Field", name: { kind: "Name", value: "createdAt" } },
                { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<MeQuery, MeQueryVariables>;
export const UserHasAssistantDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "UserHasAssistant" },
      selectionSet: {
        kind: "SelectionSet",
        selections: [{ kind: "Field", name: { kind: "Name", value: "userHasAssistant" } }],
      },
    },
  ],
} as unknown as DocumentNode<UserHasAssistantQuery, UserHasAssistantQueryVariables>;
export const MyAssistantDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "MyAssistant" },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "myAssistant" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
                { kind: "Field", name: { kind: "Name", value: "backstory" } },
                { kind: "Field", name: { kind: "Name", value: "avatarFileId" } },
                { kind: "Field", name: { kind: "Name", value: "configuration" } },
                { kind: "Field", name: { kind: "Name", value: "createdAt" } },
                { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<MyAssistantQuery, MyAssistantQueryVariables>;
export const GetOrCreateConversationDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "GetOrCreateConversation" },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "getOrCreateConversation" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "userId" } },
                { kind: "Field", name: { kind: "Name", value: "assistantId" } },
                { kind: "Field", name: { kind: "Name", value: "createdAt" } },
                { kind: "Field", name: { kind: "Name", value: "lastMessageAt" } },
                { kind: "Field", name: { kind: "Name", value: "messageCount" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<GetOrCreateConversationQuery, GetOrCreateConversationQueryVariables>;
export const MyConversationsDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "MyConversations" },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "getOrCreateConversation" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "userId" } },
                { kind: "Field", name: { kind: "Name", value: "assistantId" } },
                { kind: "Field", name: { kind: "Name", value: "createdAt" } },
                { kind: "Field", name: { kind: "Name", value: "lastMessageAt" } },
                { kind: "Field", name: { kind: "Name", value: "messageCount" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<MyConversationsQuery, MyConversationsQueryVariables>;
export const GetConversationMessagesDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "GetConversationMessages" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "conversationId" } },
          type: {
            kind: "NonNullType",
            type: { kind: "NamedType", name: { kind: "Name", value: "ID" } },
          },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "first" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "Int" } },
          defaultValue: { kind: "IntValue", value: "20" },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "after" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "last" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "Int" } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "before" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "getConversationMessages" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "conversationId" },
                value: { kind: "Variable", name: { kind: "Name", value: "conversationId" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "pagination" },
                value: {
                  kind: "ObjectValue",
                  fields: [
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "first" },
                      value: { kind: "Variable", name: { kind: "Name", value: "first" } },
                    },
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "after" },
                      value: { kind: "Variable", name: { kind: "Name", value: "after" } },
                    },
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "last" },
                      value: { kind: "Variable", name: { kind: "Name", value: "last" } },
                    },
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "before" },
                      value: { kind: "Variable", name: { kind: "Name", value: "before" } },
                    },
                  ],
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "edges" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "cursor" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "node" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "id" } },
                            { kind: "Field", name: { kind: "Name", value: "conversationId" } },
                            { kind: "Field", name: { kind: "Name", value: "senderRole" } },
                            { kind: "Field", name: { kind: "Name", value: "content" } },
                            { kind: "Field", name: { kind: "Name", value: "timestamp" } },
                            { kind: "Field", name: { kind: "Name", value: "metadata" } },
                          ],
                        },
                      },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "pageInfo" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "hasNextPage" } },
                      { kind: "Field", name: { kind: "Name", value: "hasPreviousPage" } },
                      { kind: "Field", name: { kind: "Name", value: "startCursor" } },
                      { kind: "Field", name: { kind: "Name", value: "endCursor" } },
                    ],
                  },
                },
                { kind: "Field", name: { kind: "Name", value: "totalCount" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<GetConversationMessagesQuery, GetConversationMessagesQueryVariables>;
export const CreatePersonalAssistantDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "CreatePersonalAssistant" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "input" } },
          type: {
            kind: "NonNullType",
            type: { kind: "NamedType", name: { kind: "Name", value: "CreateAssistantInput" } },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "createPersonalAssistant" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "input" },
                value: { kind: "Variable", name: { kind: "Name", value: "input" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "success" } },
                { kind: "Field", name: { kind: "Name", value: "message" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "assistant" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      { kind: "Field", name: { kind: "Name", value: "name" } },
                      { kind: "Field", name: { kind: "Name", value: "backstory" } },
                      { kind: "Field", name: { kind: "Name", value: "avatarFileId" } },
                      { kind: "Field", name: { kind: "Name", value: "configuration" } },
                      { kind: "Field", name: { kind: "Name", value: "createdAt" } },
                      { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  CreatePersonalAssistantMutation,
  CreatePersonalAssistantMutationVariables
>;
export const UpdatePersonalAssistantDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "UpdatePersonalAssistant" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "input" } },
          type: {
            kind: "NonNullType",
            type: { kind: "NamedType", name: { kind: "Name", value: "UpdateAssistantInput" } },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "updatePersonalAssistant" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "input" },
                value: { kind: "Variable", name: { kind: "Name", value: "input" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "success" } },
                { kind: "Field", name: { kind: "Name", value: "message" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "assistant" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      { kind: "Field", name: { kind: "Name", value: "name" } },
                      { kind: "Field", name: { kind: "Name", value: "backstory" } },
                      { kind: "Field", name: { kind: "Name", value: "avatarFileId" } },
                      { kind: "Field", name: { kind: "Name", value: "configuration" } },
                      { kind: "Field", name: { kind: "Name", value: "createdAt" } },
                      { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  UpdatePersonalAssistantMutation,
  UpdatePersonalAssistantMutationVariables
>;
export const SendMessageDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "SendMessage" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "conversationId" } },
          type: {
            kind: "NonNullType",
            type: { kind: "NamedType", name: { kind: "Name", value: "ID" } },
          },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "content" } },
          type: {
            kind: "NonNullType",
            type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "sendMessage" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "conversationId" },
                value: { kind: "Variable", name: { kind: "Name", value: "conversationId" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "content" },
                value: { kind: "Variable", name: { kind: "Name", value: "content" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "message" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      { kind: "Field", name: { kind: "Name", value: "conversationId" } },
                      { kind: "Field", name: { kind: "Name", value: "senderRole" } },
                      { kind: "Field", name: { kind: "Name", value: "content" } },
                      { kind: "Field", name: { kind: "Name", value: "timestamp" } },
                      { kind: "Field", name: { kind: "Name", value: "metadata" } },
                    ],
                  },
                },
                { kind: "Field", name: { kind: "Name", value: "success" } },
                { kind: "Field", name: { kind: "Name", value: "errorMessage" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<SendMessageMutation, SendMessageMutationVariables>;
export const SendMessageFromPaDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "SendMessageFromPA" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "input" } },
          type: {
            kind: "NonNullType",
            type: { kind: "NamedType", name: { kind: "Name", value: "SendMessageFromPAInput" } },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "sendMessageFromPA" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "input" },
                value: { kind: "Variable", name: { kind: "Name", value: "input" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "message" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      { kind: "Field", name: { kind: "Name", value: "conversationId" } },
                      { kind: "Field", name: { kind: "Name", value: "senderRole" } },
                      { kind: "Field", name: { kind: "Name", value: "content" } },
                      { kind: "Field", name: { kind: "Name", value: "timestamp" } },
                      { kind: "Field", name: { kind: "Name", value: "metadata" } },
                    ],
                  },
                },
                { kind: "Field", name: { kind: "Name", value: "success" } },
                { kind: "Field", name: { kind: "Name", value: "errorMessage" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<SendMessageFromPaMutation, SendMessageFromPaMutationVariables>;
export const CreateCliTokenDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "CreateCliToken" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "description" } },
          type: {
            kind: "NonNullType",
            type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "createCliToken" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "input" },
                value: {
                  kind: "ObjectValue",
                  fields: [
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "description" },
                      value: { kind: "Variable", name: { kind: "Name", value: "description" } },
                    },
                  ],
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "token" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "cliToken" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      { kind: "Field", name: { kind: "Name", value: "tokenPrefix" } },
                      { kind: "Field", name: { kind: "Name", value: "description" } },
                      { kind: "Field", name: { kind: "Name", value: "createdAt" } },
                      { kind: "Field", name: { kind: "Name", value: "expiresAt" } },
                      { kind: "Field", name: { kind: "Name", value: "lastUsedAt" } },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<CreateCliTokenMutation, CreateCliTokenMutationVariables>;
export const NewMessagesDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "subscription",
      name: { kind: "Name", value: "NewMessages" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "conversationId" } },
          type: {
            kind: "NonNullType",
            type: { kind: "NamedType", name: { kind: "Name", value: "ID" } },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "newMessages" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "conversationId" },
                value: { kind: "Variable", name: { kind: "Name", value: "conversationId" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "message" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      { kind: "Field", name: { kind: "Name", value: "conversationId" } },
                      { kind: "Field", name: { kind: "Name", value: "senderRole" } },
                      { kind: "Field", name: { kind: "Name", value: "content" } },
                      { kind: "Field", name: { kind: "Name", value: "timestamp" } },
                      { kind: "Field", name: { kind: "Name", value: "metadata" } },
                    ],
                  },
                },
                { kind: "Field", name: { kind: "Name", value: "conversationId" } },
                { kind: "Field", name: { kind: "Name", value: "eventType" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<NewMessagesSubscription, NewMessagesSubscriptionVariables>;
