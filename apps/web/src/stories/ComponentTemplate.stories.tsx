import React from "react";
import type { Meta, StoryObj } from "@storybook/react-vite";
import { fn } from "storybook/test";

// Import your component here
// import { YourComponent } from './YourComponent';

// This is a template for creating new component stories
// Copy this file and modify it for your component

// Mock component for template demonstration
const TemplateComponent = ({
  variant = "primary",
  size = "medium",
  disabled = false,
  children = "Template Component",
  onClick,
}: {
  variant?: "primary" | "secondary" | "destructive";
  size?: "small" | "medium" | "large";
  disabled?: boolean;
  children?: React.ReactNode;
  onClick?: () => void;
}) => (
  <button
    className={`
      px-4 py-2 rounded font-medium transition-colors
      ${variant === "primary" ? "bg-blue-600 text-white hover:bg-blue-700" : ""}
      ${variant === "secondary" ? "bg-gray-200 text-gray-900 hover:bg-gray-300" : ""}
      ${variant === "destructive" ? "bg-red-600 text-white hover:bg-red-700" : ""}
      ${size === "small" ? "text-sm px-3 py-1" : ""}
      ${size === "large" ? "text-lg px-6 py-3" : ""}
      ${disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
    `}
    disabled={disabled}
    onClick={onClick}
  >
    {children}
  </button>
);

const meta: Meta<typeof TemplateComponent> = {
  title: "Templates/ComponentTemplate",
  component: TemplateComponent,
  parameters: {
    // More on how to position stories at: https://storybook.js.org/docs/configure/story-layout
    layout: "centered",
    // Add documentation
    docs: {
      description: {
        component:
          "This is a template for creating component stories. Replace this with your component description.",
      },
    },
  },
  // This creates the "Docs" tab and auto-generates documentation
  tags: ["autodocs"],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {
    variant: {
      control: { type: "select" },
      options: ["primary", "secondary", "destructive"],
      description: "The visual style variant of the component",
    },
    size: {
      control: { type: "select" },
      options: ["small", "medium", "large"],
      description: "The size of the component",
    },
    disabled: {
      control: "boolean",
      description: "Whether the component is disabled",
    },
    children: {
      control: "text",
      description: "The content of the component",
    },
  },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked
  args: { onClick: fn() },
};

export default meta;
type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Primary: Story = {
  args: {
    variant: "primary",
    children: "Primary Button",
  },
};

export const Secondary: Story = {
  args: {
    variant: "secondary",
    children: "Secondary Button",
  },
};

export const Destructive: Story = {
  args: {
    variant: "destructive",
    children: "Delete",
  },
};

export const Small: Story = {
  args: {
    size: "small",
    children: "Small Button",
  },
};

export const Large: Story = {
  args: {
    size: "large",
    children: "Large Button",
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
    children: "Disabled Button",
  },
};

// Example of a story with custom render function
export const WithCustomContent: Story = {
  args: {
    variant: "primary",
  },
  render: args => (
    <TemplateComponent {...args}>
      <span>🚀</span> Custom Content
    </TemplateComponent>
  ),
};

// Example of a story that demonstrates interaction
export const Interactive: Story = {
  args: {
    variant: "primary",
    children: "Click me!",
  },
  play: async ({ canvasElement, args }) => {
    // This is an example of how to test interactions
    // You can use @storybook/test utilities here
    console.log("Story rendered on canvas:", canvasElement);
    console.log("Story args:", args);
  },
};

// Example of multiple components in one story
export const AllVariants: Story = {
  render: () => (
    <div className="flex gap-4 flex-wrap">
      <TemplateComponent variant="primary">Primary</TemplateComponent>
      <TemplateComponent variant="secondary">Secondary</TemplateComponent>
      <TemplateComponent variant="destructive">Destructive</TemplateComponent>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "This story shows all variants of the component side by side.",
      },
    },
  },
};

// Example of responsive story
export const ResponsiveExample: Story = {
  args: {
    variant: "primary",
    children: "Responsive Button",
  },
  parameters: {
    viewport: {
      viewports: {
        mobile: {
          name: "Mobile",
          styles: { width: "375px", height: "667px" },
        },
        tablet: {
          name: "Tablet",
          styles: { width: "768px", height: "1024px" },
        },
      },
    },
    docs: {
      description: {
        story: "Test this story on different viewport sizes using the viewport toolbar.",
      },
    },
  },
};
