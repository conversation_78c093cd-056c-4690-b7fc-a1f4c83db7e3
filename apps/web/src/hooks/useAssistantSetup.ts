import { useState } from "react";
import { useMutation, useQuery } from "@apollo/client";
import { CREATE_PERSONAL_ASSISTANT_MUTATION } from "../graphql/mutations";
import { MY_ASSISTANT_QUERY, USER_HAS_ASSISTANT_QUERY } from "../graphql/queries";
import type { MyAssistantQuery } from "../generated/graphql";

export interface AssistantFormData {
  name: string;
  backstory: string;
}

type Assistant = NonNullable<MyAssistantQuery["myAssistant"]>;

export interface UseAssistantSetupReturn {
  // State
  currentStep: number;
  formData: AssistantFormData;
  isLoading: boolean;
  error: string | null;

  // Assistant status
  hasAssistant: boolean;
  assistant: Assistant | null;
  checkingAssistant: boolean;

  // Actions
  setCurrentStep: (step: number) => void;
  updateFormData: (data: Partial<AssistantFormData>) => void;
  nextStep: () => void;
  prevStep: () => void;
  createAssistant: () => Promise<void>;
  resetForm: () => void;
}

const TOTAL_STEPS = 3;
const DEFAULT_BACKSTORY =
  "I am your personal assistant, ready to help you with various tasks and provide information when needed.";

export const useAssistantSetup = (): UseAssistantSetupReturn => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<AssistantFormData>({
    name: "",
    backstory: DEFAULT_BACKSTORY,
  });
  const [error, setError] = useState<string | null>(null);

  // Check if user has assistant
  const {
    data: hasAssistantData,
    loading: checkingAssistant,
    refetch: refetchHasAssistant,
  } = useQuery(USER_HAS_ASSISTANT_QUERY, {
    errorPolicy: "all",
  });

  // Get assistant details
  const { data: assistantData, refetch: refetchAssistant } = useQuery(MY_ASSISTANT_QUERY, {
    skip: !hasAssistantData?.userHasAssistant,
    errorPolicy: "all",
  });

  // Create assistant mutation
  const [createAssistantMutation, { loading: isLoading }] = useMutation(
    CREATE_PERSONAL_ASSISTANT_MUTATION,
    {
      onCompleted: () => {
        setError(null);
        // Refetch queries to update the UI
        refetchHasAssistant();
        refetchAssistant();
      },
      onError: error => {
        setError(error.message);
      },
    },
  );

  const updateFormData = (data: Partial<AssistantFormData>) => {
    setFormData(prev => ({ ...prev, ...data }));
    setError(null);
  };

  const nextStep = () => {
    if (currentStep < TOTAL_STEPS) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const createAssistant = async () => {
    try {
      setError(null);

      if (!formData.name.trim()) {
        setError("Assistant name is required");
        return;
      }

      await createAssistantMutation({
        variables: {
          input: {
            name: formData.name.trim(),
            backstory: formData.backstory.trim() || DEFAULT_BACKSTORY,
            avatarFileId: null,
            configuration: null,
          },
        },
      });
    } catch (err) {
      // Error is handled by onError callback
      console.error("Failed to create assistant:", err);
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      backstory: DEFAULT_BACKSTORY,
    });
    setCurrentStep(1);
    setError(null);
  };

  return {
    // State
    currentStep,
    formData,
    isLoading,
    error,

    // Assistant status
    hasAssistant: hasAssistantData?.userHasAssistant || false,
    assistant: assistantData?.myAssistant || null,
    checkingAssistant,

    // Actions
    setCurrentStep,
    updateFormData,
    nextStep,
    prevStep,
    createAssistant,
    resetForm,
  };
};
