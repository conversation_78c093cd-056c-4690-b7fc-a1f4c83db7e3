import { useEffect, useRef } from "react";
import { useApolloClient, useSubscription } from "@apollo/client";
import { NEW_MESSAGES_SUBSCRIPTION } from "../graphql/subscriptions";
import { GET_CONVERSATION_MESSAGES_QUERY } from "../graphql/queries";
import { type Message } from "../components/chat/types";
import { DEFAULT_PAGE_SIZE } from "../constants/pagination";
import { CursorUtils } from "../utils/cursorUtils";

interface MessageSubscriptionData {
  newMessages: {
    message: {
      id: string;
      conversationId: string;
      senderRole: string;
      content: Record<string, unknown>;
      timestamp: string;
      metadata?: Record<string, unknown>;
    };
    conversationId: string;
    eventType: string;
  };
}

interface UseMessageSubscriptionProps {
  conversationId: string;
  onNewMessage?: (message: Message) => void;
  enabled?: boolean;
}

export function useMessageSubscription({
  conversationId,
  onNewMessage,
  enabled = true,
}: UseMessageSubscriptionProps) {
  const onNewMessageRef = useRef(onNewMessage);
  onNewMessageRef.current = onNewMessage;
  const client = useApolloClient();

  const { data, loading, error } = useSubscription<MessageSubscriptionData>(
    NEW_MESSAGES_SUBSCRIPTION,
    {
      variables: { conversationId },
      skip: !enabled || !conversationId,
      onData: ({ data: subscriptionData }) => {
        if (subscriptionData?.data?.newMessages) {
          const { message } = subscriptionData.data.newMessages;

          // Convert GraphQL message to frontend Message type
          const frontendMessage: Message = {
            id: message.id,
            conversationId: message.conversationId,
            senderRole: message.senderRole as "USER" | "AGENT",
            content: message.content,
            timestamp: message.timestamp,
            metadata: message.metadata,
          };

          // Update Apollo cache to include the new message in connection format
          try {
            const existingData = client.readQuery({
              query: GET_CONVERSATION_MESSAGES_QUERY,
              variables: {
                conversationId,
                first: DEFAULT_PAGE_SIZE,
                after: null,
                last: null,
                before: null,
              }, // Use same variables as query
            });

            if (existingData?.getConversationMessages) {
              const connection = existingData.getConversationMessages;

              // Check if message already exists to prevent duplicates
              const messageExists = connection.edges.some(
                (edge: { node: { id: string } }) => edge.node.id === message.id,
              );

              if (!messageExists) {
                // Create new edge for the message
                const newEdge = {
                  __typename: "ChatMessageEdge" as const,
                  cursor: CursorUtils.encode(message.timestamp, message.id),
                  node: {
                    __typename: "ChatMessage" as const,
                    id: message.id,
                    conversationId: message.conversationId,
                    senderRole: message.senderRole,
                    content: message.content,
                    timestamp: message.timestamp,
                    metadata: message.metadata || null,
                  },
                };

                // Add new edge to existing edges
                const updatedConnection = {
                  ...connection,
                  edges: [...connection.edges, newEdge],
                  totalCount: connection.totalCount ? connection.totalCount + 1 : 1,
                  pageInfo: {
                    ...connection.pageInfo,
                    endCursor: newEdge.cursor,
                  },
                };

                client.writeQuery({
                  query: GET_CONVERSATION_MESSAGES_QUERY,
                  variables: {
                    conversationId,
                    first: DEFAULT_PAGE_SIZE,
                    after: null,
                    last: null,
                    before: null,
                  },
                  data: {
                    getConversationMessages: updatedConnection,
                  },
                });
              }
            }
          } catch (_error) {
            // If the query hasn't been executed yet, the cache read will fail
            // This is expected and we can safely ignore it
            console.log("Cache update skipped - query not in cache yet");
          }

          // Call the callback if provided
          onNewMessageRef.current?.(frontendMessage);
        }
      },
      onError: subscriptionError => {
        console.error("Message subscription error:", subscriptionError);
      },
    },
  );

  useEffect(() => {
    if (enabled && conversationId) {
      console.log(`Subscribed to messages for conversation: ${conversationId}`);
    }

    return () => {
      if (enabled && conversationId) {
        console.log(`Unsubscribed from messages for conversation: ${conversationId}`);
      }
    };
  }, [conversationId, enabled]);

  return {
    subscriptionData: data,
    subscriptionLoading: loading,
    subscriptionError: error,
  };
}
