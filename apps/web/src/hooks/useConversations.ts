import { ApolloError, useQuery } from "@apollo/client";
import { MY_CONVERSATIONS_QUERY } from "../graphql/queries";
import type { ConversationListItem } from "../types/conversation";

interface UseConversationsOptions {
  skip?: boolean;
}

interface UseConversationsReturn {
  conversations: ConversationListItem[];
  hasConversations: boolean;
  loading: boolean;
  error: ApolloError | undefined;
  refetch: () => void;
}

export const useConversations = (options: UseConversationsOptions = {}): UseConversationsReturn => {
  const { skip = false } = options;

  const { data, loading, error, refetch } = useQuery(MY_CONVERSATIONS_QUERY, {
    skip,
    errorPolicy: "all",
    onError: error => {
      console.error("Error getting conversations:", error);
    },
    onCompleted: data => {
      console.log("Conversations query completed:", data);
    },
  });

  // Transform the single conversation into a list format
  // This is a temporary solution until the backend supports multiple conversations
  const conversations: ConversationListItem[] = [];

  if (data?.getOrCreateConversation) {
    const conv = data.getOrCreateConversation;
    conversations.push({
      id: conv.id,
      title: "Chat with Personal Assistant",
      messageCount: conv.messageCount || 0,
      createdAt: conv.createdAt,
      lastMessageAt: conv.lastMessageAt || undefined,
      status: "active" as const,
      lastMessage: conv.lastMessageAt
        ? {
            content: "Continue your conversation...",
            timestamp: conv.lastMessageAt || "",
            senderRole: "agent" as const,
          }
        : undefined,
    });
  }

  const hasConversations = conversations.length > 0;

  return {
    conversations,
    hasConversations,
    loading,
    error,
    refetch,
  };
};
