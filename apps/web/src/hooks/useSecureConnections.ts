/**
 * Hook for enforcing HTTPS connections.
 *
 * This hook provides functionality to enforce HTTPS for all connections
 * and detect mixed content issues in the application.
 */
import { useEffect } from "react";
import { upgradeToHttps } from "@lib/mixed-content-detection";

/**
 * Custom hook to enforce HTTPS connections and detect mixed content.
 *
 * @param {boolean} redirectToHttps - Whether to redirect HTTP to HTTPS automatically
 * @param {boolean} detectMixedContent - Whether to detect mixed content issues
 * @returns {boolean} - Whether the app is running in a secure context
 */
export const useSecureConnections = (
  redirectToHttps = true,
  detectMixedContent = true,
): boolean => {
  // Is the app running in a secure context (HTTPS)
  const isSecureContext =
    typeof window !== "undefined" &&
    (window.location.protocol === "https:" || window.location.hostname === "localhost");

  useEffect(() => {
    // Handle redirecting HTTP to HTTPS if needed
    if (
      redirectToHttps &&
      typeof window !== "undefined" &&
      window.location.protocol === "http:" &&
      window.location.hostname !== "localhost"
    ) {
      // Redirect to HTTPS (preserving path and query params)
      window.location.href = window.location.href.replace("http:", "https:");
    }
  }, [redirectToHttps]);

  useEffect(() => {
    // Set up mixed content detection
    if (detectMixedContent && isSecureContext) {
      // Detect any mixed content on the page
      const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
          if (mutation.type === "childList" && mutation.addedNodes.length) {
            checkAndFixInsecureElements(mutation.addedNodes);
          }
        });
      });

      // Start observing the document
      observer.observe(document.documentElement, {
        childList: true,
        subtree: true,
      });

      // Check existing elements
      checkAndFixInsecureElements([document.documentElement]);

      // Clean up observer on unmount
      return () => {
        observer.disconnect();
      };
    }
  }, [detectMixedContent, isSecureContext]);

  return isSecureContext;
};

/**
 * Checks and fixes insecure elements in the DOM.
 *
 * @param {NodeList|Node[]} nodes - The nodes to check
 */
const checkAndFixInsecureElements = (nodes: NodeList | Node[]): void => {
  Array.from(nodes).forEach(node => {
    if (node.nodeType !== Node.ELEMENT_NODE) return;

    const element = node as HTMLElement;

    // Check for insecure images
    if (element.tagName === "IMG") {
      const img = element as HTMLImageElement;
      if (img.src && img.src.startsWith("http:")) {
        console.warn(`[SECURITY] Fixing insecure image: ${img.src}`);
        img.src = upgradeToHttps(img.src);
      }
    }

    // Check for insecure iframes
    if (element.tagName === "IFRAME") {
      const iframe = element as HTMLIFrameElement;
      if (iframe.src && iframe.src.startsWith("http:")) {
        console.warn(`[SECURITY] Fixing insecure iframe: ${iframe.src}`);
        iframe.src = upgradeToHttps(iframe.src);
      }
    }

    // Check for insecure links
    if (element.tagName === "LINK") {
      const link = element as HTMLLinkElement;
      if (link.href && link.href.startsWith("http:")) {
        console.warn(`[SECURITY] Fixing insecure link: ${link.href}`);
        link.href = upgradeToHttps(link.href);
      }
    }

    // Check for insecure scripts
    if (element.tagName === "SCRIPT") {
      const script = element as HTMLScriptElement;
      if (script.src && script.src.startsWith("http:")) {
        console.warn(`[SECURITY] Fixing insecure script: ${script.src}`);
        // Create a new script element with the secure URL
        const secureScript = document.createElement("script");
        secureScript.src = upgradeToHttps(script.src);
        secureScript.async = script.async;
        secureScript.defer = script.defer;
        secureScript.type = script.type;

        // Replace the insecure script with the secure one
        if (script.parentNode) {
          script.parentNode.replaceChild(secureScript, script);
        }
      }
    }

    // Check for insecure background images in style attributes
    if (element.style && element.style.backgroundImage) {
      const bgImage = element.style.backgroundImage;
      if (bgImage.includes("http:")) {
        console.warn(`[SECURITY] Fixing insecure background image: ${bgImage}`);
        element.style.backgroundImage = bgImage.replace(/http:/g, "https:");
      }
    }
  });
};
