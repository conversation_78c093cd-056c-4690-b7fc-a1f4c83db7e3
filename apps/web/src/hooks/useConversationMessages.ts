import { useQuery } from "@apollo/client";
import { GET_CONVERSATION_MESSAGES_QUERY } from "../graphql/queries";
import { type Message } from "../components/chat/types";
import { DEFAULT_PAGE_SIZE } from "../constants/pagination";

interface PageInfo {
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  startCursor?: string;
  endCursor?: string;
}

interface ConversationMessageEdge {
  node: {
    id: string;
    conversationId: string;
    senderRole: "USER" | "AGENT";
    content: Record<string, unknown>;
    timestamp: string;
    metadata?: Record<string, unknown>;
  };
  cursor: string;
}

interface ConversationMessageConnection {
  edges: ConversationMessageEdge[];
  pageInfo: PageInfo;
  totalCount?: number;
}

interface GetConversationMessagesData {
  getConversationMessages: ConversationMessageConnection;
}

interface GetConversationMessagesVariables {
  conversationId: string;
  first?: number;
  after?: string;
  last?: number;
  before?: string;
}

interface UseConversationMessagesProps {
  conversationId: string;
  first?: number;
  after?: string;
  last?: number;
  before?: string;
  enabled?: boolean;
}

export function useConversationMessages({
  conversationId,
  first = DEFAULT_PAGE_SIZE,
  after,
  last,
  before,
  enabled = true,
}: UseConversationMessagesProps) {
  const { data, loading, error, refetch, fetchMore } = useQuery<
    GetConversationMessagesData,
    GetConversationMessagesVariables
  >(GET_CONVERSATION_MESSAGES_QUERY, {
    variables: {
      conversationId,
      first,
      after,
      last,
      before,
    },
    skip: !enabled || !conversationId,
    fetchPolicy: "cache-first", // Use cache-first to avoid unnecessary network requests
    errorPolicy: "all",
  });

  // Transform GraphQL messages to frontend Message type
  const messages: Message[] =
    data?.getConversationMessages?.edges.map(edge => ({
      id: edge.node.id,
      conversationId: edge.node.conversationId,
      senderRole: edge.node.senderRole,
      content: edge.node.content,
      timestamp: edge.node.timestamp,
      metadata: edge.node.metadata || {},
    })) || [];

  const pageInfo = data?.getConversationMessages?.pageInfo;
  const totalCount = data?.getConversationMessages?.totalCount;

  // Function to load more messages (forward pagination)
  const loadMore = async () => {
    if (!pageInfo?.hasNextPage || !pageInfo.endCursor) return;

    return fetchMore({
      variables: {
        conversationId,
        first,
        after: pageInfo.endCursor,
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;

        return {
          getConversationMessages: {
            ...prev.getConversationMessages,
            edges: [
              ...prev.getConversationMessages.edges,
              ...fetchMoreResult.getConversationMessages.edges,
            ],
            pageInfo: {
              ...prev.getConversationMessages.pageInfo,
              endCursor: fetchMoreResult.getConversationMessages.pageInfo.endCursor,
              hasNextPage: fetchMoreResult.getConversationMessages.pageInfo.hasNextPage,
            },
            totalCount:
              fetchMoreResult.getConversationMessages.totalCount ??
              prev.getConversationMessages.totalCount,
          },
        };
      },
    });
  };

  // Function to load previous messages (backward pagination)
  const loadPrevious = async () => {
    if (!pageInfo?.hasPreviousPage || !pageInfo.startCursor) return;

    return fetchMore({
      variables: {
        conversationId,
        last: first, // Use same limit for consistency
        before: pageInfo.startCursor,
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        if (!fetchMoreResult) return prev;

        return {
          getConversationMessages: {
            ...prev.getConversationMessages,
            edges: [
              ...fetchMoreResult.getConversationMessages.edges,
              ...prev.getConversationMessages.edges,
            ],
            pageInfo: {
              ...prev.getConversationMessages.pageInfo,
              startCursor: fetchMoreResult.getConversationMessages.pageInfo.startCursor,
              hasPreviousPage: fetchMoreResult.getConversationMessages.pageInfo.hasPreviousPage,
            },
            totalCount:
              fetchMoreResult.getConversationMessages.totalCount ??
              prev.getConversationMessages.totalCount,
          },
        };
      },
    });
  };

  return {
    messages,
    loading,
    error,
    refetch,
    pageInfo,
    totalCount,
    loadMore,
    loadPrevious,
    hasNextPage: pageInfo?.hasNextPage || false,
    hasPreviousPage: pageInfo?.hasPreviousPage || false,
  };
}
