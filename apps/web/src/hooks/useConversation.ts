import { ApolloError, useQuery } from "@apollo/client";
import { GET_OR_CREATE_CONVERSATION_QUERY } from "../graphql/queries";

interface ConversationData {
  id: string;
  userId: string;
  assistantId: string;
  createdAt: string;
  lastMessageAt?: string;
}

interface UseConversationOptions {
  skip?: boolean;
}

interface UseConversationReturn {
  conversationId: string | null;
  conversation: ConversationData | null;
  loading: boolean;
  error: ApolloError | undefined;
  refetch: () => void;
}

export const useConversation = (options: UseConversationOptions = {}): UseConversationReturn => {
  const { skip = false } = options;

  const { data, loading, error, refetch } = useQuery(GET_OR_CREATE_CONVERSATION_QUERY, {
    skip,
    errorPolicy: "all",
    onError: error => {
      console.error("Error getting conversation:", error);
    },
    onCompleted: data => {
      console.log("Conversation query completed:", data);
    },
  });

  const conversation = data?.getOrCreateConversation
    ? {
        ...data.getOrCreateConversation,
        lastMessageAt: data.getOrCreateConversation.lastMessageAt || undefined,
      }
    : null;
  const conversationId = conversation?.id || null;

  return {
    conversationId,
    conversation,
    loading,
    error,
    refetch,
  };
};
