// Import the generated typed documents
export {
  CreateCliTokenDocument as CREATE_CLI_TOKEN_MUTATION,
  CreatePersonalAssistantDocument as CREATE_PERSONAL_ASSISTANT_MUTATION,
  SendMessageDocument as SEND_MESSAGE_MUTATION,
  SendMessageFromPaDocument as SEND_MESSAGE_FROM_PA_MUTATION,
  UpdatePersonalAssistantDocument as UPDATE_PERSONAL_ASSISTANT_MUTATION,
} from "../generated/graphql";

// Re-export types for convenience
export type {
  CreateCliTokenMutation,
  CreatePersonalAssistantMutation,
  SendMessageMutation,
  SendMessageFromPaMutation,
  UpdatePersonalAssistantMutation,
} from "../generated/graphql";
