// Import the generated typed documents
export {
  UserHasAssistantDocument as USER_HAS_ASSISTANT_QUERY,
  MyAssistantDocument as MY_ASSISTANT_QUERY,
  MeDocument as ME_QUERY,
  GetOrCreateConversationDocument as GET_OR_CREATE_CONVERSATION_QUERY,
  MyConversationsDocument as MY_CONVERSATIONS_QUERY,
  GetConversationMessagesDocument as GET_CONVERSATION_MESSAGES_QUERY,
} from "../generated/graphql";

// Re-export types for convenience
export type {
  UserHasAssistantQuery,
  MyAssistantQuery,
  MeQuery,
  GetOrCreateConversationQuery,
  MyConversationsQuery,
  GetConversationMessagesQuery,
} from "../generated/graphql";
