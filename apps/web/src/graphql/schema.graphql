schema {
  query: Query
  mutation: Mutation
  subscription: Subscription
}

type AgentPricingInfo {
  amountMonthly: Float
  amountPerCall: Float
  currency: String
  features: [String!]
  pricingType: String!
  tierId: String
}

input AgentPricingInfoInput {
  amountMonthly: Float = null
  amountPerCall: Float = null
  currency: String = null
  features: [String!] = null
  pricingType: String!
  tierId: String = null
}

type AgentSkill {
  description: String
  name: String!
  parametersSchema: String
}

input AgentSkillInput {
  description: String = null
  name: String!
  parametersSchema: String = null
}

type Assistant {
  avatarFileId: UUID
  backstory: String!
  configuration: JSON
  createdAt: DateTime!
  id: UUID!
  name: String!
  updatedAt: DateTime!
  userId: UUID!
}

type AssistantMutationPayload {
  assistant: Assistant!
  message: String!
  success: Boolean!
}

type ChatMessage {
  content: JSON!
  conversationId: UUID!
  id: UUID!
  metadata: JSON
  senderRole: String!
  timestamp: DateTime!
}

type ChatMessageConnection {
  edges: [ChatMessageEdge!]!
  pageInfo: PageInfo!
  totalCount: Int
}

type PageInfo {
  endCursor: String
  hasNextPage: Boolean!
  hasPreviousPage: Boolean!
  startCursor: String
}

input ChatMessageConnectionArgs {
  after: String
  before: String
  first: Int
  last: Int
}

type ChatMessageEdge {
  cursor: String!
  node: ChatMessage!
}

type CliToken {
  createdAt: DateTime!
  description: String
  expiresAt: DateTime
  id: ID!
  lastUsedAt: DateTime
  tokenPrefix: String!
  userId: ID!
}

type Conversation {
  assistantId: UUID!
  createdAt: DateTime!
  id: UUID!
  lastMessageAt: DateTime
  messageCount: Int!
  userId: UUID!
}

input CreateAssistantInput {
  avatarFileId: UUID = null
  backstory: String!
  configuration: JSON = null
  name: String!
}

input CreateCliTokenInput {
  description: String = null
}

type CreateCliTokenPayload {
  cliToken: CliToken!
  token: String!
}

"""Date with time (isoformat)"""
scalar DateTime

"""
The `JSON` scalar type represents JSON values as specified by [ECMA-404](https://ecma-international.org/wp-content/uploads/ECMA-404_2nd_edition_december_2017.pdf).
"""
scalar JSON

type MarketplaceAgent {
  agentDefinitionId: String!
  createdAt: String!
  description: String
  developerId: String
  name: String!
  pricingInfo: AgentPricingInfo
  reviewStatus: String
  skills: [AgentSkill!]!
  status: String!
  updatedAt: String!
  version: String!
}

type MessageSubscriptionPayload {
  conversationId: UUID!
  eventType: String!
  message: ChatMessage!
}

type Mutation {
  createCliToken(input: CreateCliTokenInput!): CreateCliTokenPayload!
  createPersonalAssistant(input: CreateAssistantInput!): AssistantMutationPayload!
  echo(message: String!): String!
  registerMarketplaceAgent(input: RegisterMarketplaceAgentInput!): MarketplaceAgent!
  sendMessage(content: String!, conversationId: ID!): SendMessagePayload!
  sendMessageFromPA(input: SendMessageFromPAInput!): SendMessagePayload!
  updateAgentReviewStatus(agentDefinitionId: String!, reviewStatus: String!): MarketplaceAgent!
  updateMarketplaceAgent(input: UpdateMarketplaceAgentInput!): MarketplaceAgent!
  updateMyProfile(timezone: String!): User!
  updatePersonalAssistant(input: UpdateAssistantInput!): AssistantMutationPayload!
  updateUserProfilePreferences(input: UserProfilePreferencesInput!): User!
}

type Query {
  getConversationMessages(conversationId: ID!, pagination: ChatMessageConnectionArgs = null): ChatMessageConnection!
  getMarketplaceAgent(agentDefinitionId: String!): MarketplaceAgent
  getOrCreateConversation: Conversation!
  hello: String!
  listMarketplaceAgents(status: String = null): [MarketplaceAgent!]!
  me: User!
  myAssistant: Assistant
  userHasAssistant: Boolean!
}

input RegisterMarketplaceAgentInput {
  agentDefinitionId: String!
  asyncQueueName: String = null
  description: String = null
  developerId: String!
  endpointUrl: String = null
  name: String!
  pricingInfo: AgentPricingInfoInput = null
  skills: [AgentSkillInput!]!
  version: String!
}

input SendMessageFromPAInput {
  content: String!
  conversationId: ID!
  metadata: JSON = null
}

type SendMessagePayload {
  errorMessage: String
  message: ChatMessage!
  success: Boolean!
}

type Subscription {
  newMessages(conversationId: ID!): MessageSubscriptionPayload!
}

scalar UUID

input UpdateAssistantInput {
  avatarFileId: UUID = null
  backstory: String = null
  configuration: JSON = null
  name: String = null
}

input UpdateMarketplaceAgentInput {
  agentDefinitionId: String!
  asyncQueueName: String = null
  description: String = null
  endpointUrl: String = null
  name: String = null
  pricingInfo: AgentPricingInfoInput = null
  skills: [AgentSkillInput!] = null
  status: String = null
  version: String = null
}

type User {
  clerkUserId: String!
  createdAt: String!
  email: String!
  id: UUID!
  isPaSetupComplete: Boolean!
  preferences: JSON!
  timezone: String
  updatedAt: String!
}

input UserProfilePreferencesInput {
  preferences: JSON = null
  timezone: String = null
}