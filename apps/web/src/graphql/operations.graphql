# Fragments - Define reusable field sets

# Core entity fragments
fragment UserDetails on User {
  id
  clerkUserId
  email
  timezone
  preferences
  isPaSetupComplete
  createdAt
  updatedAt
}

fragment AssistantDetails on Assistant {
  id
  name
  backstory
  avatarFileId
  configuration
  createdAt
  updatedAt
}

fragment ConversationDetails on Conversation {
  id
  userId
  assistantId
  createdAt
  lastMessageAt
  messageCount
}

fragment ChatMessageDetails on ChatMessage {
  id
  conversationId
  senderRole
  content
  timestamp
  metadata
}

fragment CliTokenDetails on CliToken {
  id
  tokenPrefix
  description
  createdAt
  expiresAt
  lastUsedAt
}

fragment AssistantMutationResponse on AssistantMutationPayload {
  success
  message
}

fragment MessageMutationResponse on SendMessagePayload {
  success
  errorMessage
}

fragment MessageSubscriptionData on MessageSubscriptionPayload {
  message {
    ...ChatMessageDetails
  }
  conversationId
  eventType
}

# Composite fragments for complex operations
fragment ConversationWithMessages on Conversation {
  ...ConversationDetails
  # Future: when messages are part of conversation type
  # messages(first: 10) {
  #   ...ChatMessageDetails
  # }
}

fragment AssistantWithDetails on Assistant {
  ...AssistantDetails
  # Future: when user relationship is exposed
  # user {
  #   id
  #   email
  # }
}

# Connection Pattern Fragments - Cursor-based pagination
fragment PageInfo on PageInfo {
  hasNextPage
  hasPreviousPage
  startCursor
  endCursor
}

fragment ChatMessageEdge on ChatMessageEdge {
  cursor
  node {
    ...ChatMessageDetails
  }
}

fragment ChatMessageConnection on ChatMessageConnection {
  edges {
    ...ChatMessageEdge
  }
  pageInfo {
    ...PageInfo
  }
  totalCount
}

# fragment ConversationEdge on ConversationEdge {
#   cursor
#   node {
#     ...ConversationDetails
#   }
# }

# User Queries
query Me {
  me {
    ...UserDetails
  }
}

query UserHasAssistant {
  userHasAssistant
}

# Assistant Queries
query MyAssistant {
  myAssistant {
    ...AssistantDetails
  }
}

# Future: Parameterized assistant query for more flexibility
# query Assistant($id: ID) {
#   assistant(id: $id) {
#     ...AssistantDetails
#   }
# }

# Conversation Queries - Consolidated
query GetOrCreateConversation {
  getOrCreateConversation {
    ...ConversationDetails
  }
}

# Alias for backward compatibility - both queries do the same thing
query MyConversations {
  getOrCreateConversation {
    ...ConversationDetails
  }
}

# Message Queries - Cursor-based pagination
query GetConversationMessages(
  $conversationId: ID!
  $first: Int = 20
  $after: String
  $last: Int
  $before: String
) {
  getConversationMessages(
    conversationId: $conversationId
    pagination: {
      first: $first
      after: $after
      last: $last
      before: $before
    }
  ) {
    ...ChatMessageConnection
  }
}

# Future: Conversation list pagination (to be implemented)
# query GetConversationsConnection($first: Int = 10, $after: String) {
#   me {
#     conversations(first: $first, after: $after) {
#       edges {
#         ...ConversationEdge
#       }
#       pageInfo {
#         ...PageInfo
#       }
#     }
#   }
# }

# Assistant Mutations
mutation CreatePersonalAssistant($input: CreateAssistantInput!) {
  createPersonalAssistant(input: $input) {
    ...AssistantMutationResponse
    assistant {
      ...AssistantDetails
    }
  }
}

mutation UpdatePersonalAssistant($input: UpdateAssistantInput!) {
  updatePersonalAssistant(input: $input) {
    ...AssistantMutationResponse
    assistant {
      ...AssistantDetails
    }
  }
}

# Message Mutations
mutation SendMessage($conversationId: ID!, $content: String!) {
  sendMessage(conversationId: $conversationId, content: $content) {
    message {
      ...ChatMessageDetails
    }
    ...MessageMutationResponse
  }
}

mutation SendMessageFromPA($input: SendMessageFromPAInput!) {
  sendMessageFromPA(input: $input) {
    message {
      ...ChatMessageDetails
    }
    ...MessageMutationResponse
  }
}

# CLI Token Mutations
mutation CreateCliToken($description: String!) {
  createCliToken(input: { description: $description }) {
    token
    cliToken {
      ...CliTokenDetails
    }
  }
}

# Message Subscriptions
subscription NewMessages($conversationId: ID!) {
  newMessages(conversationId: $conversationId) {
    ...MessageSubscriptionData
  }
}