import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, useAuth } from "@clerk/clerk-react";
import { ApolloProvider } from "@apollo/client";
import { RouterProvider } from "react-router-dom";
import router from "./routes";
import ErrorBoundary from "./components/ErrorBoundary";
import { createApolloClient } from "./lib/apollo";

const clerkPubKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;

// Component that provides Apollo Client with Clerk authentication
function AppWithApollo() {
  const { getToken } = useAuth();

  // Create Apollo Client with Clerk token getter - memoize to prevent recreation
  const apolloClient = React.useMemo(
    () => createApolloClient(() => getToken({ template: "a2a_backend" })),
    [getToken],
  );

  return (
    <ApolloProvider client={apolloClient}>
      <RouterProvider router={router} />
    </ApolloProvider>
  );
}

function App() {
  if (!clerkPub<PERSON>ey) {
    return (
      <div>
        Error: Clerk publishable key is not configured. Please set VITE_CLERK_PUBLISHABLE_KEY.
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <ClerkProvider publishableKey={clerkPubKey}>
        <AppWithApollo />
      </ClerkProvider>
    </ErrorBoundary>
  );
}

export default App;
