/**
 * Utilities for detecting and reporting mixed content issues.
 *
 * This module provides functions to help detect and report mixed content
 * issues in the application, helping to maintain a secure HTTPS environment.
 */

/**
 * Upgrades HTTP URLs to HTTPS.
 * @param url The URL to upgrade
 * @returns The upgraded URL
 */
export const upgradeToHttps = (url: string): string => {
  if (url.startsWith("http:")) {
    return url.replace("http:", "https:");
  }

  return url;
};
