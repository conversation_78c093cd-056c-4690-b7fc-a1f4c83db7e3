// Asset path utility for handling CDN asset URLs

/**
 * Creates a CDN-aware asset path
 *
 * This utility handles creating the correct asset path based on whether the app
 * is running locally or from a CDN. In production and staging, assets should be
 * prefixed with the CDN URL if available.
 *
 * @param path - The relative path to the asset
 * @returns The complete path with CDN prefix when appropriate
 */
export function getAssetPath(path: string): string {
  // Remove leading slash if present
  const normalizedPath = path.startsWith("/") ? path.substring(1) : path;

  // Check if we have a CDN assets URL defined
  const assetsUrl = import.meta.env.VITE_ASSETS_URL;

  // In development, just use the relative path
  if (!assetsUrl || import.meta.env.DEV) {
    return `/${normalizedPath}`;
  }

  // In production/staging, use the CDN URL for the assets
  return `${assetsUrl}/${normalizedPath}`;
}

/**
 * Gets the public URL for any asset
 * This is useful for images and other static files
 *
 * @param path - The path relative to the public directory
 * @returns The complete URL to the asset
 */
export function getPublicAssetUrl(path: string): string {
  // For assets in the public directory
  const deployEnv = import.meta.env.VITE_DEPLOY_ENV || "development";

  // If using the CDN in production or staging
  if (deployEnv !== "development") {
    const cdnDomain = deployEnv === "production" ? "vedavivi.app" : "www-staging.vedavivi.app";

    // Remove leading slash if present
    const normalizedPath = path.startsWith("/") ? path.substring(1) : path;
    return `https://${cdnDomain}/${normalizedPath}`;
  }

  // In development, just use the relative path
  return path;
}
