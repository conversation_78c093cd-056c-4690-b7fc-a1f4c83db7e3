/**
 * Apollo Client configuration for the A2A Platform.
 *
 * This module sets up the Apollo Client for GraphQL operations,
 * ensuring secure HTTPS communication for all queries, mutations,
 * and subscriptions using multipart HTTP protocol.
 */
import { ApolloClient, HttpLink, InMemoryCache } from "@apollo/client";
import { setContext } from "@apollo/client/link/context";
import { GRAPHQL_API_URL } from "@config/api";

// Create an HTTP link for all operations (queries, mutations, and subscriptions)
// Apollo Client 3.7.11+ automatically handles multipart HTTP subscriptions
const httpLink = new HttpLink({
  uri: GRAPHQL_API_URL,
  credentials: "include",
});

// Create the Apollo Client instance
export const apolloClient = new ApolloClient({
  link: httpLink,
  cache: new InMemoryCache({
    typePolicies: {
      Query: {
        fields: {
          // Prevent refetching conversation messages unless explicitly requested
          getConversationMessages: {
            keyArgs: ["conversationId", "limit", "offset"],
          },
        },
      },
    },
  }),
  defaultOptions: {
    watchQuery: {
      fetchPolicy: "cache-first", // Default to cache-first to avoid unnecessary network requests
    },
  },
});

// Export a function to create a new client with custom token getter
export const createApolloClient = (getToken?: () => Promise<string | null>) => {
  // Create HTTP link for all operations (including subscriptions via multipart HTTP)
  const httpLink = new HttpLink({
    uri: GRAPHQL_API_URL,
    credentials: "include",
  });

  // Create auth link for HTTP requests
  const authLink = setContext(async (_, { headers }) => {
    try {
      if (getToken) {
        const token = await getToken();
        return {
          headers: {
            ...headers,
            ...(token ? { authorization: `Bearer ${token}` } : {}),
          },
        };
      }
      return { headers };
    } catch (error) {
      console.warn("Failed to get auth token:", error);
      return { headers };
    }
  });

  // Combine auth and HTTP links
  const httpLinkWithAuth = authLink.concat(httpLink);

  return new ApolloClient({
    link: httpLinkWithAuth,
    cache: new InMemoryCache({
      typePolicies: {
        Query: {
          fields: {
            // Prevent refetching conversation messages unless explicitly requested
            getConversationMessages: {
              keyArgs: ["conversationId", "limit", "offset"],
            },
          },
        },
      },
    }),
    defaultOptions: {
      watchQuery: {
        fetchPolicy: "cache-first", // Default to cache-first to avoid unnecessary network requests
        errorPolicy: "all",
      },
      query: {
        errorPolicy: "all",
      },
    },
  });
};
