import { ApolloClient, InMemoryCache, createHttpLink, from } from "@apollo/client";
import { setContext } from "@apollo/client/link/context";
import { useAuth } from "@clerk/clerk-react";
import { debugLog } from "../utils/debug";

// Create a function to get the Apollo Client with the current auth token
export const createApolloClient = (getToken: () => Promise<string | null>) => {
  // Determine GraphQL endpoint
  const graphqlUrl =
    import.meta.env.VITE_GRAPHQL_API_URL ||
    `${import.meta.env.VITE_API_URL || "http://localhost:8000"}/graphql`;

  // Debug logging for GraphQL URL (development only)
  debugLog("Apollo Client Configuration:", {
    VITE_GRAPHQL_API_URL: import.meta.env.VITE_GRAPHQL_API_URL,
    VITE_API_URL: import.meta.env.VITE_API_URL,
    resolvedGraphQLUrl: graphqlUrl,
  });

  // HTTP connection to the API
  const httpLink = createHttpLink({
    uri: graphqlUrl,
  });

  // Auth link to add the token to requests
  const authLink = setContext(async (_, { headers }) => {
    // Get the authentication token from Clerk
    const token = await getToken();

    // Debug logging for authentication (development only)
    debugLog("Apollo authLink: Getting token...", token ? "success" : "null");
    if (token) {
      debugLog("Apollo authLink: Token preview:", `${token.substring(0, 20)}...`);
    }

    // Return the headers to the context so httpLink can read them
    return {
      headers: {
        ...headers,
        authorization: token ? `Bearer ${token}` : "",
      },
    };
  });

  // Create the Apollo Client
  return new ApolloClient({
    link: from([authLink, httpLink]),
    cache: new InMemoryCache(),
  });
};

// Hook to get the Apollo Client with the current user's token
export const useApolloClient = () => {
  const { getToken } = useAuth();
  return createApolloClient(() => getToken());
};
