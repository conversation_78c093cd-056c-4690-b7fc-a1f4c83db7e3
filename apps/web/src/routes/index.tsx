import React from "react";
import { createBrowserRouter } from "react-router-dom";
import AuthCallbackPage from "../pages/auth/AuthCallbackPage";
import { ChatPage } from "../pages/chat/ChatPage";
import { ProductionGuard } from "../components/security/ProductionGuard";
import {
  ChatRoute,
  DashboardRoute,
  RootRoute,
  SettingsRoute,
  SignInRoute,
  SignUpRoute,
} from "../components/routes/RouteComponents";

const router = createBrowserRouter([
  {
    path: "/",
    element: <RootRoute />,
  },
  {
    path: "/index.html",
    element: <RootRoute />,
  },
  {
    path: "/pa/sign-up",
    element: <SignUpRoute />,
  },
  {
    path: "/pa/sign-in",
    element: <SignInRoute />,
  },
  {
    path: "/pa/dashboard",
    element: <DashboardRoute />,
  },
  {
    path: "/pa/settings",
    element: <SettingsRoute />,
  },
  {
    path: "/pa/auth-callback",
    element: <AuthCallbackPage />,
  },
  {
    path: "/chat/:conversationId",
    element: <ChatRoute />,
  },
  {
    path: "/pa/chat",
    element: <ChatRoute />,
  },
  {
    path: "/pa/chat/:conversationId",
    element: <ChatRoute />,
  },
  // Test-only route that bypasses authentication (only available in development)
  // This route is conditionally added based on the build environment
  ...(import.meta.env.MODE === "development" || import.meta.env.MODE === "test"
    ? [
        {
          path: "/test/chat/:conversationId",
          element: (
            <ProductionGuard feature="Test Chat Routes">
              <ChatPage />
            </ProductionGuard>
          ),
        },
      ]
    : []),
]);

export default router;
