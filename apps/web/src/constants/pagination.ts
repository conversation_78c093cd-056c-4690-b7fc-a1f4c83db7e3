/**
 * Shared pagination constants for consistent page sizing across the application.
 */

/**
 * Default page size for GraphQL connection queries.
 * Used consistently across useConversationMessages and useMessageSubscription hooks.
 */
export const DEFAULT_PAGE_SIZE = 20;

/**
 * Maximum page size allowed for pagination queries.
 * Prevents excessive data fetching that could impact performance.
 */
export const MAX_PAGE_SIZE = 100;
