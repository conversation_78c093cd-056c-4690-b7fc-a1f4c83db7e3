/**
 * Debug utilities for development-only logging
 *
 * This module provides safe logging functions that only output
 * in development mode, preventing console pollution in production.
 */

type LogLevel = "log" | "info" | "warn" | "error" | "debug";

/**
 * Safe console logging that only outputs in development mode
 *
 * @param level - The log level (log, info, warn, error, debug)
 * @param message - The message to log
 * @param data - Optional data to log alongside the message
 */
const safeLog = (level: LogLevel, message: string, data?: unknown) => {
  if (import.meta.env.DEV) {
    if (data !== undefined) {
      console[level](message, data);
    } else {
      console[level](message);
    }
  }
};

/**
 * Development-only console.log
 */
export const debugLog = (message: string, data?: unknown) => {
  safeLog("log", message, data);
};

/**
 * Development-only console.info
 */
export const debugInfo = (message: string, data?: unknown) => {
  safeLog("info", message, data);
};

/**
 * Development-only console.warn
 */
export const debugWarn = (message: string, data?: unknown) => {
  safeLog("warn", message, data);
};

/**
 * Development-only console.error
 */
export const debugError = (message: string, data?: unknown) => {
  safeLog("error", message, data);
};

/**
 * Development-only console.debug
 */
export const debugDebug = (message: string, data?: unknown) => {
  safeLog("debug", message, data);
};

/**
 * Check if we're in development mode
 */
export const isDevelopment = () => import.meta.env.DEV;

/**
 * Check if we're in production mode
 */
export const isProduction = () => import.meta.env.PROD;
