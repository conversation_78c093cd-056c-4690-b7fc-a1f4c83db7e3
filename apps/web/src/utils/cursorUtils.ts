/**
 * Frontend cursor utilities that match the backend cursor encoding format.
 *
 * This ensures consistency between frontend and backend cursor pagination,
 * using the same base64-encoded JSON format with timestamp + UUID.
 */

export interface CursorData {
  timestamp: string;
  id: string;
}

/**
 * Cursor utilities for encoding and decoding pagination cursors.
 * Matches the backend CursorUtils implementation.
 */
export const CursorUtils = {
  /**
   * Encode a cursor from timestamp and ID.
   *
   * @param timestamp - ISO timestamp string
   * @param id - UUID string
   * @returns Base64-encoded cursor string
   */
  encode: (timestamp: string, id: string): string => {
    const cursorData: CursorData = { timestamp, id };
    const jsonString = JSON.stringify(cursorData);
    return btoa(jsonString);
  },

  /**
   * Decode a cursor to extract timestamp and ID.
   *
   * @param cursor - Base64-encoded cursor string
   * @returns Decoded cursor data with timestamp and id
   * @throws Error if cursor format is invalid
   */
  decode: (cursor: string): CursorData => {
    try {
      const jsonString = atob(cursor);
      const cursorData = JSON.parse(jsonString);

      if (!cursorData.timestamp || !cursorData.id) {
        throw new Error("Invalid cursor format: missing timestamp or id");
      }

      return cursorData;
    } catch (error) {
      throw new Error(
        `Failed to decode cursor: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  },

  /**
   * Validate if a cursor string is properly formatted.
   *
   * @param cursor - Cursor string to validate
   * @returns True if cursor is valid, false otherwise
   */
  isValid: (cursor: string): boolean => {
    try {
      CursorUtils.decode(cursor);
      return true;
    } catch {
      return false;
    }
  },
};
