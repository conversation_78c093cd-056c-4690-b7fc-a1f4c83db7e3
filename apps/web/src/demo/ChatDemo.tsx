import React, { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>ider } from "@apollo/client/testing";
import { ChatInterface } from "../components/chat";
import { type Message } from "../components/chat/types";
import { SEND_MESSAGE_MUTATION } from "../graphql/mutations";

// Mock data for demonstration
const mockMessages: Message[] = [
  {
    id: "msg-1",
    conversationId: "demo-conversation",
    senderRole: "AGENT",
    content: {
      text: "Hello! I'm your Personal Assistant. How can I help you today?",
    },
    timestamp: "2024-01-15T10:00:00Z",
  },
  {
    id: "msg-2",
    conversationId: "demo-conversation",
    senderRole: "USER",
    content: { text: "Hi! I need help with planning my day." },
    timestamp: "2024-01-15T10:01:00Z",
  },
  {
    id: "msg-3",
    conversationId: "demo-conversation",
    senderRole: "AGENT",
    content: {
      text: "I'd be happy to help you plan your day! What are your main priorities or tasks you need to accomplish?",
    },
    timestamp: "2024-01-15T10:01:30Z",
  },
];

// Mock GraphQL responses
const mocks = [
  {
    request: {
      query: SEND_MESSAGE_MUTATION,
      variables: {
        conversationId: "demo-conversation",
        content: "This is a test message",
      },
    },
    result: {
      data: {
        sendMessage: {
          message: {
            id: "new-msg-id",
            conversationId: "demo-conversation",
            senderRole: "USER",
            content: { text: "This is a test message" },
            timestamp: new Date().toISOString(),
            metadata: {},
          },
          success: true,
          errorMessage: null,
        },
      },
    },
  },
];

export const ChatDemo: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>(mockMessages);

  const handleMessageSent = (newMessage: Message) => {
    setMessages(prev => [...prev, newMessage]);

    // Simulate assistant response after a delay
    setTimeout(() => {
      const assistantResponse: Message = {
        id: `response-${Date.now()}`,
        conversationId: newMessage.conversationId,
        senderRole: "AGENT",
        content: {
          text: "Thank you for your message! I'm here to help you with whatever you need.",
        },
        timestamp: new Date().toISOString(),
        metadata: {},
      };
      setMessages(prev => [...prev, assistantResponse]);
    }, 1500);
  };

  return (
    <MockedProvider mocks={mocks} addTypename={false}>
      <div className="min-h-screen bg-background p-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold mb-4">Chat Interface Demo</h1>
            <p className="text-muted-foreground text-lg">
              This demonstrates the text messaging feature for communicating with your Personal
              Assistant.
            </p>
          </div>

          <ChatInterface
            conversationId="demo-conversation"
            assistantName="Demo Assistant"
            messages={messages}
            onMessageSent={handleMessageSent}
          />

          <div className="mt-8 text-center text-sm text-muted-foreground">
            <p>Try sending a message! The assistant will respond automatically in this demo.</p>
          </div>
        </div>
      </div>
    </MockedProvider>
  );
};
