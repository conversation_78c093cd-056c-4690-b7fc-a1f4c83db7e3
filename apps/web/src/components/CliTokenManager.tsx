import React, { useState } from "react";
import { useMutation } from "@apollo/client";
import { useAuth } from "@clerk/clerk-react";
import { CREATE_CLI_TOKEN_MUTATION } from "../graphql/mutations.tsx";

// Define the structure of the data returned by the mutation
interface CreateCliTokenData {
  createCliToken: {
    token: string;
    cliToken: {
      id: string;
      tokenPrefix: string;
      description: string | null;
      createdAt: string;
    };
  };
}

interface CreateCliTokenVars {
  description: string;
}

const CliTokenManager: React.FC = () => {
  const [showModal, setShowModal] = useState(false);
  const [description, setDescription] = useState("");
  const [generatedToken, setGeneratedToken] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);
  const { isSignedIn, getToken } = useAuth();

  const [createCliToken, { loading }] = useMutation<CreateCliTokenData, CreateCliTokenVars>(
    CREATE_CLI_TOKEN_MUTATION,
    {
      onCompleted: data => {
        setGeneratedToken(data.createCliToken.token);
        setShowModal(false); // Close modal after successful creation
        setDescription(""); // Reset description
        setError(null);
      },
      onError: apolloError => {
        console.error("Token creation error:", apolloError);

        // Special handling for authentication errors
        if (apolloError.message.includes("Authentication required")) {
          setError(
            "Authentication error: Your session may have expired. Please refresh the page and try again.",
          );

          // Check if we have a token at all - using the same template as in App.tsx
          getToken({ template: "a2a_backend" }).then(token => {
            if (!token) {
              setError("You are not authenticated. Please sign in again.");
            } else {
              console.log("Token exists but may be invalid. Token length:", token.length);
            }
          });
        } else {
          setError(
            apolloError.graphQLErrors[0]?.message ||
              apolloError.message ||
              "Failed to create token. Please try again.",
          );
        }
        setGeneratedToken(null);
      },
    },
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!description.trim()) {
      setError("Description cannot be empty.");
      return;
    }

    // Reset states
    setError(null);
    setCopied(false);

    // Check if user is signed in before attempting to create token
    if (!isSignedIn) {
      setError("You must be signed in to create tokens. Please sign in and try again.");
      return;
    }

    try {
      // Check for valid token before proceeding
      const token = await getToken({ template: "a2a_backend" });
      if (!token) {
        setError("Unable to retrieve authentication token. Please sign in again.");
        return;
      }

      await createCliToken({ variables: { description } });
    } catch (err) {
      console.error("Unexpected error during mutation execution:", err);
      setError("An unexpected error occurred. Please try again later.");
    }
  };

  const handleCopyToken = () => {
    if (generatedToken) {
      navigator.clipboard
        .writeText(generatedToken)
        .then(() => {
          setCopied(true);
          setTimeout(() => setCopied(false), 2000); // Reset after 2s
        })
        .catch(err => {
          console.error("Failed to copy token: ", err);
          setError("Failed to copy token to clipboard.");
        });
    }
  };

  const openModal = () => {
    setShowModal(true);
    setGeneratedToken(null); // Clear previous token when opening modal
    setError(null);
    setCopied(false);
    setDescription("");
  };

  const closeModal = () => {
    setShowModal(false);
    // Optionally clear token and error when modal is explicitly closed without saving
    // setGeneratedToken(null);
    // setError(null);
  };

  return (
    <div>
      <h2>CLI Token Management</h2>
      <button
        onClick={openModal}
        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition ease-in-out duration-150"
      >
        Create New Token
      </button>

      {generatedToken && !showModal && (
        <div className="mt-6 p-4 border border-gray-200 rounded-md bg-gray-50">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">New Token Generated:</h3>
          <p className="text-sm text-yellow-600 bg-yellow-50 p-3 rounded-md border border-yellow-300 mb-3">
            Please copy this token. It will not be shown again for security reasons.
          </p>
          <div className="p-3 bg-gray-100 border border-gray-300 rounded-md my-2 break-all font-mono text-sm text-gray-700">
            {generatedToken}
          </div>
          <button
            onClick={handleCopyToken}
            className="mt-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50 transition ease-in-out duration-150"
          >
            {copied ? "Copied!" : "Copy to Clipboard"}
          </button>
          {error && <p className="mt-2 text-sm text-red-600">{error}</p>}
        </div>
      )}

      {showModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center z-50">
          <div className="relative mx-auto p-6 border w-full max-w-md shadow-lg rounded-md bg-white">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Create New CLI Token</h3>
            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <label
                  htmlFor="tokenDescription"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Description:
                </label>
                <input
                  type="text"
                  id="tokenDescription"
                  value={description}
                  onChange={e => setDescription(e.target.value)}
                  placeholder="e.g., My development machine token"
                  className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  required
                />
              </div>
              {error && <p className="mb-3 text-sm text-red-600">{error}</p>}
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={closeModal}
                  disabled={loading}
                  className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 transition ease-in-out duration-150 disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition ease-in-out duration-150 disabled:opacity-50"
                >
                  {loading ? "Creating..." : "Create Token"}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default CliTokenManager;
