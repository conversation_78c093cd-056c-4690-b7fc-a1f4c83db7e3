import type { Meta, StoryObj } from "@storybook/react-vite";
import { action } from "storybook/actions";
import SignUpForm from "./SignUpForm";
import React from "react";

const meta: Meta<typeof SignUpForm> = {
  title: "Components/Auth/SignUpForm",
  component: SignUpForm,
  decorators: [
    Story => (
      <div style={{ maxWidth: "400px", margin: "auto", paddingTop: "20px" }}>
        <Story />
      </div>
    ),
  ],
  parameters: {
    layout: "centered",
    docs: {
      story: {
        inline: false,
        iframeHeight: 500, // Adjust height as needed
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    onVerificationSent: { action: "verificationSent" },
    onError: { action: "errorOccurred" },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    onVerificationSent: action("onVerificationSent"),
    onError: action("onError"),
  },
};

export const LoadingState: Story = {
  name: "Loading State (Conceptual)",
  args: {
    onVerificationSent: action("onVerificationSent"),
    onError: action("onError"),
  },
  // Similar to SignInForm, truly showing a loading state for SignUpForm
  // would require deeper mocking of the `useSignUp` hook or internal state manipulation.
  // This story primarily demonstrates the component with default props.
};

// Advanced stories could involve mocking `useSignUp` to simulate specific scenarios
// like email already exists, network errors, or successful sign-up flow.
// This requires a setup like:
// import { useSignUp } from '@clerk/clerk-react';
// jest.mock('@clerk/clerk-react', () => ({
//   ...jest.requireActual('@clerk/clerk-react'),
//   useSignUp: () => ({
//     isLoaded: true,
//     signUp: {
//       authenticateWithRedirect: jest.fn().mockRejectedValue(new Error('OAuth Error')),
//       create: jest.fn().mockResolvedValue({}), // Mock successful creation
//       prepareEmailAddressVerification: jest.fn().mockResolvedValue({}), // Mock email verification prep
//     },
//   }),
// }));
//
// export const EmailSignUpSuccess: Story = {
//   name: 'Email Sign-Up Success Path (Conceptual)',
//   args: {
//     onVerificationSent: action('onVerificationSent'), // This should be called
//     onError: action('onError'),
//   },
//   // User would type email and click "Sign up with Email"
//   // Requires interaction and the mock setup above.
// };
