import type { Meta, StoryObj } from "@storybook/react-vite";
import { action } from "storybook/actions";
import SignInForm from "./SignInForm";
import React from "react";

const meta: Meta<typeof SignInForm> = {
  title: "Components/Auth/SignInForm",
  component: SignInForm,
  decorators: [
    Story => (
      <div style={{ maxWidth: "400px", margin: "auto", paddingTop: "20px" }}>
        <Story />
      </div>
    ),
  ],
  parameters: {
    layout: "centered",
    docs: {
      story: {
        inline: false,
        iframeHeight: 500, // Adjust height as needed for this form
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    onVerificationSent: { action: "verificationSent" },
    onError: { action: "errorOccurred" },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    onVerificationSent: action("onVerificationSent"),
    onError: action("onError"),
  },
};

export const LoadingState: Story = {
  name: "Loading State (Conceptual)",
  args: {
    onVerificationSent: action("onVerificationSent"),
    onError: action("onError"),
  },
  // To truly show a loading state, you'd need to mock `useSignIn`
  // to return `isLoaded: false` or manipulate internal component state,
  // which is beyond basic prop-based story configuration.
  // This story primarily demonstrates the component with default props.
  // Clerk's `isLoaded` typically becomes true quickly.
  // The internal `isLoading` state is managed within the component upon form submission.
};

// You can add more stories to simulate different scenarios if you mock `useSignIn` from Clerk.
// For example, mocking `signIn.authenticateWithRedirect` to throw an error.
// Or mocking `signIn.create` and `signIn.prepareEmailAddressVerification` for email sign-in.
// These are more advanced and would require deeper mocking of the `useSignIn` hook.
// Example:
// import { useSignIn } from '@clerk/clerk-react';
// jest.mock('@clerk/clerk-react', () => ({
//   ...jest.requireActual('@clerk/clerk-react'),
//   useSignIn: () => ({
//     isLoaded: true,
//     signIn: {
//       authenticateWithRedirect: jest.fn().mockRejectedValue(new Error('Network Error')),
//       create: jest.fn().mockResolvedValue({}),
//       prepareEmailAddressVerification: jest.fn().mockResolvedValue({}),
//     },
//   }),
// }));
//
// export const GoogleSignInError: Story = {
//   name: 'Google Sign-In Network Error',
//   args: {
//     onVerificationSent: action('onVerificationSent'),
//     onError: action('onError'),
//   },
//   // This story would ideally trigger the Google sign-in and show the error.
//   // Requires interaction and the mock setup above.
// };
