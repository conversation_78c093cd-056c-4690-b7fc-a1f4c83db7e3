import { act, fireEvent, render, screen } from "@testing-library/react";
import { useSignIn } from "@clerk/clerk-react";
import SignInForm from "../SignInForm";

// Mock the Clerk hook
jest.mock("@clerk/clerk-react", () => ({
  useSignIn: jest.fn(),
}));

describe("SignInForm", () => {
  const mockSignIn = {
    isLoaded: true,
    authenticateWithRedirect: jest.fn(),
    create: jest.fn(),
    prepareEmailAddressVerification: jest.fn(),
  };

  const mockOnVerificationSent = jest.fn();
  const mockOnError = jest.fn();

  beforeEach(() => {
    (useSignIn as jest.Mock).mockReturnValue({
      signIn: mockSignIn,
      isLoaded: true,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("renders the sign-in form correctly", () => {
    render(<SignInForm onVerificationSent={mockOnVerificationSent} onError={mockOnError} />);

    // Check for sign-in options
    expect(screen.getByText("Sign in with Google")).toBeInTheDocument();
    expect(screen.getByText("Sign in with GitHub")).toBeInTheDocument();
    expect(screen.getByText("Sign in with Email")).toBeInTheDocument();

    // Check for email input
    expect(screen.getByLabelText("Email")).toBeInTheDocument();
  });

  it("handles Google sign-in correctly", () => {
    render(<SignInForm onVerificationSent={mockOnVerificationSent} onError={mockOnError} />);

    const googleButton = screen.getByText("Sign in with Google");
    fireEvent.click(googleButton);

    expect(mockSignIn.authenticateWithRedirect).toHaveBeenCalledWith({
      strategy: "oauth_google",
      redirectUrl: "/pa/auth-callback",
      redirectUrlComplete: "/pa/dashboard",
    });
  });

  it("handles GitHub sign-in correctly", () => {
    render(<SignInForm onVerificationSent={mockOnVerificationSent} onError={mockOnError} />);

    const githubButton = screen.getByText("Sign in with GitHub");
    fireEvent.click(githubButton);

    expect(mockSignIn.authenticateWithRedirect).toHaveBeenCalledWith({
      strategy: "oauth_github",
      redirectUrl: "/pa/auth-callback",
      redirectUrlComplete: "/pa/dashboard",
    });
  });

  it("handles email sign-in correctly", async () => {
    // Mock the async functions to resolve immediately
    mockSignIn.create.mockResolvedValue({});

    render(<SignInForm onVerificationSent={mockOnVerificationSent} onError={mockOnError} />);

    // Fill in email
    const emailInput = screen.getByLabelText("Email");
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });

    // Submit form using act to handle state updates
    await act(async () => {
      const form = screen.getByRole("form");
      fireEvent.submit(form);
    });

    expect(mockSignIn.create).toHaveBeenCalledWith({
      identifier: "<EMAIL>",
      redirectUrl: expect.any(String),
      strategy: "email_link",
    });

    expect(mockOnVerificationSent).toHaveBeenCalledWith("<EMAIL>");
  });

  // New tests for error handling
  it("handles network error during Google sign-in", async () => {
    // Mock the authenticateWithRedirect to reject with a network error
    const networkError = new Error("Network error");
    networkError["code"] = "network_error";
    mockSignIn.authenticateWithRedirect.mockRejectedValue(networkError);

    render(<SignInForm onVerificationSent={mockOnVerificationSent} onError={mockOnError} />);

    const googleButton = screen.getByText("Sign in with Google");

    await act(async () => {
      fireEvent.click(googleButton);
    });

    expect(mockOnError).toHaveBeenCalledWith(
      "Network error. Please check your internet connection and try again.",
    );
  });

  it("handles account not found error during GitHub sign-in", async () => {
    // Mock the authenticateWithRedirect to reject with a not found error
    const notFoundError = new Error("Account not found");
    notFoundError["code"] = "not_found_error";
    mockSignIn.authenticateWithRedirect.mockRejectedValue(notFoundError);

    render(<SignInForm onVerificationSent={mockOnVerificationSent} onError={mockOnError} />);

    const githubButton = screen.getByText("Sign in with GitHub");

    await act(async () => {
      fireEvent.click(githubButton);
    });

    expect(mockOnError).toHaveBeenCalledWith(
      "No account found with GitHub. Please sign up first or try another method.",
    );
  });

  it("handles provider error during Google sign-in", async () => {
    // Mock the authenticateWithRedirect to reject with an OAuth provider error
    const providerError = new Error("OAuth provider error");
    providerError["code"] = "oauth_provider_error";
    mockSignIn.authenticateWithRedirect.mockRejectedValue(providerError);

    render(<SignInForm onVerificationSent={mockOnVerificationSent} onError={mockOnError} />);

    const googleButton = screen.getByText("Sign in with Google");

    await act(async () => {
      fireEvent.click(googleButton);
    });

    expect(mockOnError).toHaveBeenCalledWith(
      "Error with Google authentication service. Please try again later.",
    );
  });

  it("handles email not found error during email sign-in", async () => {
    // Mock the create method to reject with a not found error
    const notFoundError = new Error("Email not found");
    notFoundError["code"] = "form_identifier_not_found";
    mockSignIn.create.mockRejectedValue(notFoundError);

    render(<SignInForm onVerificationSent={mockOnVerificationSent} onError={mockOnError} />);

    // Fill in email
    const emailInput = screen.getByLabelText("Email");
    fireEvent.change(emailInput, {
      target: { value: "<EMAIL>" },
    });

    // Submit form
    await act(async () => {
      const form = screen.getByRole("form");
      fireEvent.submit(form);
    });

    expect(mockOnError).toHaveBeenCalledWith(
      "No account found with this email. Please sign up first or try another method.",
    );
  });

  it("handles rate limit error during email sign-in", async () => {
    // Mock the create method to reject with a rate limit error
    const rateLimitError = new Error("Rate limit exceeded");
    rateLimitError["code"] = "rate_limit_exceeded";
    mockSignIn.create.mockRejectedValue(rateLimitError);

    render(<SignInForm onVerificationSent={mockOnVerificationSent} onError={mockOnError} />);

    // Fill in email
    const emailInput = screen.getByLabelText("Email");
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });

    // Submit form
    await act(async () => {
      const form = screen.getByRole("form");
      fireEvent.submit(form);
    });

    expect(mockOnError).toHaveBeenCalledWith("Too many sign-in attempts. Please try again later.");
  });
});
