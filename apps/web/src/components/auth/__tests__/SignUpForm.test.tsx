import { act, fireEvent, render, screen } from "@testing-library/react";
import { useSignUp } from "@clerk/clerk-react";
import SignUpForm from "../SignUpForm";

// Mock the Clerk hook
jest.mock("@clerk/clerk-react", () => ({
  useSignUp: jest.fn(),
}));

describe("SignUpForm", () => {
  const mockSignUp = {
    isLoaded: true,
    authenticateWithRedirect: jest.fn(),
    create: jest.fn(),
    prepareEmailAddressVerification: jest.fn(),
  };

  const mockOnVerificationSent = jest.fn();
  const mockOnError = jest.fn();

  beforeEach(() => {
    (useSignUp as jest.Mock).mockReturnValue({
      signUp: mockSignUp,
      isLoaded: true,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("renders the sign-up form correctly", () => {
    render(<SignUpForm onVerificationSent={mockOnVerificationSent} onError={mockOnError} />);

    // Check for sign-up options
    expect(screen.getByText("Sign up with Google")).toBeInTheDocument();
    expect(screen.getByText("Sign up with GitHub")).toBeInTheDocument();
    expect(screen.getByText("Sign up with Email")).toBeInTheDocument();

    // Check for email input
    expect(screen.getByLabelText("Email")).toBeInTheDocument();
  });

  it("handles Google sign-up correctly", () => {
    render(<SignUpForm onVerificationSent={mockOnVerificationSent} onError={mockOnError} />);

    const googleButton = screen.getByText("Sign up with Google");
    fireEvent.click(googleButton);

    expect(mockSignUp.authenticateWithRedirect).toHaveBeenCalledWith({
      strategy: "oauth_google",
      redirectUrl: "/pa/auth-callback",
      redirectUrlComplete: "/pa/dashboard",
    });
  });

  it("handles GitHub sign-up correctly", () => {
    render(<SignUpForm onVerificationSent={mockOnVerificationSent} onError={mockOnError} />);

    const githubButton = screen.getByText("Sign up with GitHub");
    fireEvent.click(githubButton);

    expect(mockSignUp.authenticateWithRedirect).toHaveBeenCalledWith({
      strategy: "oauth_github",
      redirectUrl: "/pa/auth-callback",
      redirectUrlComplete: "/pa/dashboard",
    });
  });

  it("handles email sign-up correctly", async () => {
    // Mock the async functions to resolve immediately
    mockSignUp.create.mockResolvedValue({});
    mockSignUp.prepareEmailAddressVerification.mockResolvedValue({});

    render(<SignUpForm onVerificationSent={mockOnVerificationSent} onError={mockOnError} />);

    // Fill in email
    const emailInput = screen.getByLabelText("Email");
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });

    // Submit form using act to handle state updates
    await act(async () => {
      const form = screen.getByRole("form");
      fireEvent.submit(form);
    });

    expect(mockSignUp.create).toHaveBeenCalledWith({
      emailAddress: "<EMAIL>",
    });

    expect(mockSignUp.prepareEmailAddressVerification).toHaveBeenCalledWith({
      strategy: "email_link",
      redirectUrl: expect.any(String),
    });

    expect(mockOnVerificationSent).toHaveBeenCalledWith("<EMAIL>");
  });
});
