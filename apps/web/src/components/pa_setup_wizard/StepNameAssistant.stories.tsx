import React from "react";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import StepNameAssistant from "./StepNameAssistant";

const meta: Meta<typeof StepNameAssistant> = {
  title: "PA Setup/StepNameAssistant",
  component: StepNameAssistant,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component: `
StepNameAssistant is the first step of the PA setup wizard.
It allows users to enter a name for their personal assistant with validation.
        `,
      },
    },
  },
  argTypes: {
    name: {
      control: { type: "text" },
      description: "Current name value",
    },
    onNameChange: {
      action: "onNameChange",
      description: "Callback when name changes",
    },
    onNext: {
      action: "onNext",
      description: "Callback when user clicks Continue",
    },
    isLoading: {
      control: { type: "boolean" },
      description: "Loading state",
    },
    error: {
      control: { type: "text" },
      description: "Error message to display",
    },
  },
};

export default meta;
type Story = StoryObj<typeof StepNameAssistant>;

export const Default: Story = {
  args: {
    name: "",
    isLoading: false,
    error: null,
  },
  parameters: {
    docs: {
      description: {
        story: "Default state with empty name field",
      },
    },
  },
};

export const WithName: Story = {
  args: {
    name: "Alex",
    isLoading: false,
    error: null,
  },
  parameters: {
    docs: {
      description: {
        story: "State with a name already entered",
      },
    },
  },
};

export const Loading: Story = {
  args: {
    name: "Assistant",
    isLoading: true,
    error: null,
  },
  parameters: {
    docs: {
      description: {
        story: "Loading state when processing the name",
      },
    },
  },
};

export const WithError: Story = {
  args: {
    name: "",
    isLoading: false,
    error: "Please enter a name for your assistant",
  },
  parameters: {
    docs: {
      description: {
        story: "Error state showing validation message",
      },
    },
  },
};

// Component wrapper for validation demo
const ValidationDemoComponent = () => {
  const [name, setName] = React.useState("");
  const [error, setError] = React.useState<string | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);

  const handleNext = () => {
    if (!name.trim()) {
      setError("Please enter a name for your assistant");
      return;
    }
    if (name.trim().length < 2) {
      setError("Assistant name must be at least 2 characters long");
      return;
    }
    if (name.trim().length > 50) {
      setError("Assistant name must be less than 50 characters");
      return;
    }

    setError(null);
    setIsLoading(true);

    // Simulate processing
    setTimeout(() => {
      setIsLoading(false);
      alert(`Successfully validated: "${name}"`);
    }, 2000);
  };

  const handleNameChange = (newName: string) => {
    setName(newName);
    if (error) {
      setError(null);
    }
  };

  return (
    <div className="w-full max-w-2xl">
      <div className="mb-4 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold text-blue-900 mb-2">Interactive Demo</h3>
        <p className="text-sm text-blue-800">
          Try entering different names to see validation in action:
        </p>
        <ul className="text-sm text-blue-700 mt-2 space-y-1">
          <li>• Empty name - shows required error</li>
          <li>• Single character - shows length error</li>
          <li>• Very long name (50+ chars) - shows max length error</li>
          <li>• Valid name - allows continuation</li>
        </ul>
      </div>

      <StepNameAssistant
        name={name}
        onNameChange={handleNameChange}
        onNext={handleNext}
        isLoading={isLoading}
        error={error}
      />
    </div>
  );
};

export const ValidationDemo: Story = {
  name: "Validation Demo",
  render: () => <ValidationDemoComponent />,
  parameters: {
    docs: {
      description: {
        story: "Interactive demo showing validation behavior with different inputs",
      },
    },
  },
};

// Component wrapper for popular names showcase
const PopularNamesComponent = () => {
  const popularNames = ["Alex", "Sam", "Maya", "Assistant", "Sage", "Nova", "Echo"];
  const [selectedName, setSelectedName] = React.useState("Alex");

  return (
    <div className="w-full max-w-2xl">
      <div className="mb-6 p-4 bg-green-50 rounded-lg">
        <h3 className="font-semibold text-green-900 mb-3">Popular Assistant Names</h3>
        <div className="flex flex-wrap gap-2">
          {popularNames.map(name => (
            <button
              key={name}
              onClick={() => setSelectedName(name)}
              className={`px-3 py-1 rounded-full text-sm transition-colors ${
                selectedName === name
                  ? "bg-green-600 text-white"
                  : "bg-white border border-green-300 text-green-700 hover:bg-green-100"
              }`}
            >
              {name}
            </button>
          ))}
        </div>
        <p className="text-sm text-green-700 mt-2">
          Click a name above to see it in the input field
        </p>
      </div>

      <StepNameAssistant
        name={selectedName}
        onNameChange={setSelectedName}
        onNext={() => alert(`Selected: ${selectedName}`)}
        isLoading={false}
        error={null}
      />
    </div>
  );
};

export const PopularNames: Story = {
  name: "Popular Names Showcase",
  render: () => <PopularNamesComponent />,
  parameters: {
    docs: {
      description: {
        story: "Showcase of popular assistant names with interactive selection",
      },
    },
  },
};

export const ErrorStates: Story = {
  name: "Error States",
  render: () => {
    const errorScenarios = [
      { name: "", error: "Please enter a name for your assistant", label: "Empty Name" },
      { name: "A", error: "Assistant name must be at least 2 characters long", label: "Too Short" },
      {
        name: "A".repeat(51),
        error: "Assistant name must be less than 50 characters",
        label: "Too Long",
      },
      {
        name: "Valid Name",
        error: "Network error occurred. Please try again.",
        label: "Network Error",
      },
    ];

    return (
      <div className="space-y-8">
        <h3 className="text-xl font-semibold text-center">Error State Examples</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {errorScenarios.map((scenario, index) => (
            <div key={index} className="border rounded-lg p-4">
              <h4 className="font-medium mb-3 text-center text-gray-700">{scenario.label}</h4>
              <div className="scale-90 origin-top">
                <StepNameAssistant
                  name={scenario.name}
                  onNameChange={() => {}}
                  onNext={() => {}}
                  isLoading={false}
                  error={scenario.error}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story: "Various error states displayed side by side for comparison",
      },
    },
  },
};

export const AccessibilityFeatures: Story = {
  name: "Accessibility Features",
  args: {
    name: "Screen Reader Test",
    isLoading: false,
    error: null,
  },
  parameters: {
    docs: {
      description: {
        story: "Demonstrates accessibility features like focus, labels, and keyboard navigation",
      },
    },
  },
  render: args => (
    <div className="w-full max-w-2xl">
      <div className="mb-6 p-4 bg-purple-50 rounded-lg">
        <h3 className="font-semibold text-purple-900 mb-2">♿ Accessibility Features</h3>
        <ul className="text-sm text-purple-800 space-y-1">
          <li>• Auto-focus on input field for immediate typing</li>
          <li>• Proper label association with htmlFor</li>
          <li>• Enter key submits the form</li>
          <li>• Error messages are announced to screen readers</li>
          <li>• Keyboard navigation between elements</li>
          <li>• Loading state disables interaction</li>
        </ul>
      </div>

      <StepNameAssistant {...args} />
    </div>
  ),
};
