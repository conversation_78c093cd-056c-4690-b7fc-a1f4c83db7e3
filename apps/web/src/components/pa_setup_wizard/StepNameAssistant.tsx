import React, { useState } from "react";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Alert, AlertDescription } from "../ui/alert";

interface StepNameAssistantProps {
  name: string;
  onNameChange: (name: string) => void;
  onNext: () => void;
  isLoading?: boolean;
  error?: string | null;
}

const StepNameAssistant: React.FC<StepNameAssistantProps> = ({
  name,
  onNameChange,
  onNext,
  isLoading = false,
  error,
}) => {
  const [validationError, setValidationError] = useState<string>("");

  const handleNext = () => {
    if (!name.trim()) {
      setValidationError("Please enter a name for your assistant");
      return;
    }

    if (name.trim().length < 2) {
      setValidationError("Assistant name must be at least 2 characters long");
      return;
    }

    if (name.trim().length > 50) {
      setValidationError("Assistant name must be less than 50 characters");
      return;
    }

    setValidationError("");
    onNext();
  };

  const handleNameChange = (value: string) => {
    onNameChange(value);
    if (validationError) {
      setValidationError("");
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleNext();
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">
          What would you like to name your assistant?
        </h2>
        <p className="text-gray-600">
          Choose a name that feels right to you. You can always change it later.
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <label htmlFor="assistant-name" className="block text-sm font-medium text-gray-700 mb-2">
            Assistant Name
          </label>
          <Input
            id="assistant-name"
            type="text"
            value={name}
            onChange={e => handleNameChange(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="e.g., Alex, Sam, or Assistant"
            className="text-lg"
            disabled={isLoading}
            autoFocus
          />
        </div>

        {(validationError || error) && (
          <Alert variant="destructive">
            <AlertDescription>{validationError || error}</AlertDescription>
          </Alert>
        )}

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-900 mb-1">💡 Naming Tips</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Choose something easy to remember and pronounce</li>
            <li>• Consider a name that reflects your assistant's personality</li>
            <li>• Popular choices include Alex, Sam, Maya, or simply "Assistant"</li>
          </ul>
        </div>
      </div>

      <div className="flex justify-end">
        <Button onClick={handleNext} disabled={isLoading || !name.trim()} className="px-8">
          {isLoading ? "Processing..." : "Continue"}
        </Button>
      </div>
    </div>
  );
};

export default StepNameAssistant;
