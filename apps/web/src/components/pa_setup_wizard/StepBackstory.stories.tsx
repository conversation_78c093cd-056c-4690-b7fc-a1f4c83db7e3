import React from "react";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import StepBackstory from "./StepBackstory";

const meta: Meta<typeof StepBackstory> = {
  title: "PA Setup/StepBackstory",
  component: StepBackstory,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component: `
StepBackstory is the second step of the PA setup wizard.
It allows users to provide a backstory for their personal assistant, shaping its personality and role.
        `,
      },
    },
  },
  argTypes: {
    backstory: {
      control: { type: "text" },
      description: "Current backstory value",
    },
    onBackstoryChange: {
      action: "onBackstoryChange",
      description: "Callback when backstory changes",
    },
    onNext: {
      action: "onNext",
      description: "Callback when user clicks Create Assistant",
    },
    onPrev: {
      action: "onPrev",
      description: "Callback when user clicks Back",
    },
    isLoading: {
      control: { type: "boolean" },
      description: "Loading state during assistant creation",
    },
    error: {
      control: { type: "text" },
      description: "Error message to display",
    },
  },
};

export default meta;
type Story = StoryObj<typeof StepBackstory>;

export const Default: Story = {
  args: {
    backstory: "",
    isLoading: false,
    error: null,
  },
  parameters: {
    docs: {
      description: {
        story: "Default state with empty backstory field",
      },
    },
  },
};

export const WithDefaultBackstory: Story = {
  args: {
    backstory:
      "I am your personal assistant, ready to help you with various tasks and provide information when needed. I'm here to make your day more productive and organized.",
    isLoading: false,
    error: null,
  },
  parameters: {
    docs: {
      description: {
        story: "State with the default backstory filled in",
      },
    },
  },
};

export const WithCustomBackstory: Story = {
  args: {
    backstory:
      "I'm a highly knowledgeable assistant with expertise in software development, project management, and creative problem-solving. I have a friendly, professional demeanor and enjoy helping with complex technical challenges. My goal is to enhance your productivity while maintaining a collaborative and supportive approach.",
    isLoading: false,
    error: null,
  },
  parameters: {
    docs: {
      description: {
        story: "State with a custom, detailed backstory",
      },
    },
  },
};

export const Loading: Story = {
  args: {
    backstory:
      "I am your personal assistant, ready to help you with various tasks and provide information when needed.",
    isLoading: true,
    error: null,
  },
  parameters: {
    docs: {
      description: {
        story: "Loading state during assistant creation",
      },
    },
  },
};

export const WithError: Story = {
  args: {
    backstory: "Short",
    isLoading: false,
    error: "Backstory should be at least 10 characters long",
  },
  parameters: {
    docs: {
      description: {
        story: "Error state showing validation message",
      },
    },
  },
};

export const CharacterLimitWarning: Story = {
  args: {
    backstory: "A".repeat(1950),
    isLoading: false,
    error: null,
  },
  parameters: {
    docs: {
      description: {
        story: "Near character limit showing counter",
      },
    },
  },
};

// Component wrapper for interactive demo
const InteractiveDemoComponent = () => {
  const [backstory, setBackstory] = React.useState("");
  const [error, setError] = React.useState<string | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);

  const handleNext = () => {
    if (!backstory.trim()) {
      setError("Please provide a backstory for your assistant");
      return;
    }
    if (backstory.trim().length < 10) {
      setError("Backstory should be at least 10 characters long");
      return;
    }
    if (backstory.trim().length > 2000) {
      setError("Backstory must be less than 2000 characters");
      return;
    }

    setError(null);
    setIsLoading(true);

    // Simulate assistant creation
    setTimeout(() => {
      setIsLoading(false);
      alert("Assistant created successfully!");
    }, 3000);
  };

  const handleBackstoryChange = (value: string) => {
    setBackstory(value);
    if (error) {
      setError(null);
    }
  };

  return (
    <div className="w-full max-w-2xl">
      <div className="mb-4 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold text-blue-900 mb-2">Interactive Demo</h3>
        <p className="text-sm text-blue-800">
          Try different backstories to see validation and character counting:
        </p>
        <ul className="text-sm text-blue-700 mt-2 space-y-1">
          <li>• Empty backstory - shows required error</li>
          <li>• Very short backstory - shows minimum length error</li>
          <li>• Very long backstory (2000+ chars) - shows max length error</li>
          <li>• Valid backstory - enables assistant creation</li>
        </ul>
      </div>

      <StepBackstory
        backstory={backstory}
        onBackstoryChange={handleBackstoryChange}
        onNext={handleNext}
        onPrev={() => alert("Going back to previous step")}
        isLoading={isLoading}
        error={error}
      />
    </div>
  );
};

export const InteractiveDemo: Story = {
  name: "Interactive Demo",
  render: () => <InteractiveDemoComponent />,
  parameters: {
    docs: {
      description: {
        story: "Interactive demo showing validation and character limit behavior",
      },
    },
  },
};

// Component wrapper for backstory templates
const BackstoryTemplatesComponent = () => {
  const templates = [
    {
      name: "Professional Assistant",
      backstory:
        "I am a highly professional assistant with expertise in business operations, project management, and executive support. I maintain a formal yet approachable demeanor and excel at organizing complex information and workflows.",
    },
    {
      name: "Creative Partner",
      backstory:
        "I'm a creative and inspiring assistant who thrives on brainstorming, artistic projects, and innovative problem-solving. I bring enthusiasm and fresh perspectives to every challenge while supporting your creative endeavors.",
    },
    {
      name: "Technical Expert",
      backstory:
        "I am a technically-minded assistant with deep knowledge in software development, engineering, and technology trends. I enjoy tackling complex technical challenges and explaining complicated concepts in accessible ways.",
    },
    {
      name: "Personal Organizer",
      backstory:
        "I'm your dedicated personal organizer, focused on helping you manage your daily life, schedule, and personal goals. I excel at creating systems that work for your lifestyle and keeping you on track with what matters most.",
    },
  ];

  const [selectedTemplate, setSelectedTemplate] = React.useState(templates[0]);

  return (
    <div className="w-full max-w-4xl">
      <div className="mb-6 p-4 bg-green-50 rounded-lg">
        <h3 className="font-semibold text-green-900 mb-3">Backstory Templates</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          {templates.map(template => (
            <button
              key={template.name}
              onClick={() => setSelectedTemplate(template)}
              className={`p-2 rounded text-sm transition-colors text-left ${
                selectedTemplate.name === template.name
                  ? "bg-green-600 text-white"
                  : "bg-white border border-green-300 text-green-700 hover:bg-green-100"
              }`}
            >
              {template.name}
            </button>
          ))}
        </div>
        <p className="text-sm text-green-700 mt-2">
          Click a template above to see different backstory styles
        </p>
      </div>

      <StepBackstory
        backstory={selectedTemplate.backstory}
        onBackstoryChange={() => {}}
        onNext={() => alert(`Creating ${selectedTemplate.name}`)}
        onPrev={() => alert("Going back")}
        isLoading={false}
        error={null}
      />
    </div>
  );
};

export const BackstoryTemplates: Story = {
  name: "Backstory Templates",
  render: () => <BackstoryTemplatesComponent />,
  parameters: {
    docs: {
      description: {
        story: "Different backstory templates for various assistant personalities",
      },
    },
  },
};

// Component wrapper for error states
const ErrorStatesComponent = () => {
  const errorScenarios = [
    {
      backstory: "",
      error: "Please provide a backstory for your assistant",
      label: "Empty Backstory",
    },
    {
      backstory: "Short",
      error: "Backstory should be at least 10 characters long",
      label: "Too Short",
    },
    {
      backstory: "A".repeat(2001),
      error: "Backstory must be less than 2000 characters",
      label: "Too Long",
    },
    {
      backstory: "Valid backstory that meets all requirements",
      error: "Failed to create assistant. Please try again.",
      label: "Creation Error",
    },
  ];

  return (
    <div className="space-y-8">
      <h3 className="text-xl font-semibold text-center">Error State Examples</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {errorScenarios.map((scenario, index) => (
          <div key={index} className="border rounded-lg p-4">
            <h4 className="font-medium mb-3 text-center text-gray-700">{scenario.label}</h4>
            <div className="scale-75 origin-top">
              <StepBackstory
                backstory={scenario.backstory}
                onBackstoryChange={() => {}}
                onNext={() => {}}
                onPrev={() => {}}
                isLoading={false}
                error={scenario.error}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export const ErrorStates: Story = {
  name: "Error States",
  render: () => <ErrorStatesComponent />,
  parameters: {
    docs: {
      description: {
        story: "Various error states displayed side by side for comparison",
      },
    },
  },
};

// Component wrapper for character count demo
const CharacterCountDemoComponent = () => {
  const [backstory, setBackstory] = React.useState("");

  const scenarios = [
    { chars: 50, color: "green", label: "Good Start" },
    { chars: 500, color: "blue", label: "Detailed" },
    { chars: 1000, color: "yellow", label: "Comprehensive" },
    { chars: 1800, color: "orange", label: "Near Limit" },
    { chars: 2000, color: "red", label: "At Limit" },
  ];

  return (
    <div className="w-full max-w-2xl">
      <div className="mb-6 p-4 bg-purple-50 rounded-lg">
        <h3 className="font-semibold text-purple-900 mb-3">Character Count Visualization</h3>
        <div className="flex flex-wrap gap-2 mb-3">
          {scenarios.map(scenario => (
            <button
              key={scenario.chars}
              onClick={() => setBackstory("A".repeat(scenario.chars))}
              className={`px-3 py-1 rounded text-sm border transition-colors ${
                scenario.color === "green"
                  ? "border-green-500 text-green-700 hover:bg-green-50"
                  : scenario.color === "blue"
                    ? "border-blue-500 text-blue-700 hover:bg-blue-50"
                    : scenario.color === "yellow"
                      ? "border-yellow-500 text-yellow-700 hover:bg-yellow-50"
                      : scenario.color === "orange"
                        ? "border-orange-500 text-orange-700 hover:bg-orange-50"
                        : "border-red-500 text-red-700 hover:bg-red-50"
              }`}
            >
              {scenario.chars} chars ({scenario.label})
            </button>
          ))}
        </div>
        <p className="text-sm text-purple-700">
          Click buttons above to test different character counts
        </p>
      </div>

      <StepBackstory
        backstory={backstory}
        onBackstoryChange={setBackstory}
        onNext={() => alert("Creating assistant")}
        onPrev={() => alert("Going back")}
        isLoading={false}
        error={null}
      />
    </div>
  );
};

export const CharacterCountDemo: Story = {
  name: "Character Count Demo",
  render: () => <CharacterCountDemoComponent />,
  parameters: {
    docs: {
      description: {
        story: "Interactive demo showing character count behavior at different lengths",
      },
    },
  },
};
