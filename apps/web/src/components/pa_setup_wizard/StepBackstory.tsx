import React, { useState } from "react";
import { Button } from "../ui/button";
import { Textarea } from "../ui/textarea";
import { Alert, AlertDescription } from "../ui/alert";

interface StepBackstoryProps {
  backstory: string;
  onBackstoryChange: (backstory: string) => void;
  onNext: () => void;
  onPrev: () => void;
  isLoading?: boolean;
  error?: string | null;
}

const StepBackstory: React.FC<StepBackstoryProps> = ({
  backstory,
  onBackstoryChange,
  onNext,
  onPrev,
  isLoading = false,
  error,
}) => {
  const [validationError, setValidationError] = useState<string>("");

  const handleNext = () => {
    if (!backstory.trim()) {
      setValidationError("Please provide a backstory for your assistant");
      return;
    }

    if (backstory.trim().length < 10) {
      setValidationError("Backstory should be at least 10 characters long");
      return;
    }

    if (backstory.trim().length > 2000) {
      setValidationError("Backstory must be less than 2000 characters");
      return;
    }

    setValidationError("");
    onNext();
  };

  const handleBackstoryChange = (value: string) => {
    onBackstoryChange(value);
    if (validationError) {
      setValidationError("");
    }
  };

  const useDefaultBackstory = () => {
    const defaultBackstory =
      "I am your personal assistant, ready to help you with various tasks and provide information when needed. I'm here to make your day more productive and organized.";
    onBackstoryChange(defaultBackstory);
  };

  const characterCount = backstory.length;
  const maxCharacters = 2000;

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">Tell us about your assistant</h2>
        <p className="text-gray-600">
          Describe your assistant's personality and role. This helps shape how they'll interact with
          you.
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <label
            htmlFor="assistant-backstory"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Assistant Backstory
          </label>
          <Textarea
            id="assistant-backstory"
            value={backstory}
            onChange={e => handleBackstoryChange(e.target.value)}
            placeholder="Describe your assistant's personality, expertise, and how they should help you..."
            className="min-h-[120px] resize-none"
            disabled={isLoading}
            autoFocus
          />
          <div className="flex justify-between items-center mt-2">
            <Button variant="outline" size="sm" onClick={useDefaultBackstory} disabled={isLoading}>
              Use Default
            </Button>
            <span
              className={`text-sm ${
                characterCount > maxCharacters ? "text-red-600" : "text-gray-500"
              }`}
            >
              {characterCount}/{maxCharacters}
            </span>
          </div>
        </div>

        {(validationError || error) && (
          <Alert variant="destructive">
            <AlertDescription>{validationError || error}</AlertDescription>
          </Alert>
        )}

        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-green-900 mb-1">✨ Backstory Ideas</h3>
          <ul className="text-sm text-green-800 space-y-1">
            <li>• Professional and efficient, focused on productivity</li>
            <li>• Friendly and conversational, like a helpful colleague</li>
            <li>• Knowledgeable expert in your field of work</li>
            <li>• Creative and inspiring, great for brainstorming</li>
          </ul>
        </div>
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={onPrev} disabled={isLoading}>
          Back
        </Button>
        <Button
          onClick={handleNext}
          disabled={isLoading || !backstory.trim() || characterCount > maxCharacters}
          className="px-8"
        >
          {isLoading ? "Creating Assistant..." : "Create Assistant"}
        </Button>
      </div>
    </div>
  );
};

export default StepBackstory;
