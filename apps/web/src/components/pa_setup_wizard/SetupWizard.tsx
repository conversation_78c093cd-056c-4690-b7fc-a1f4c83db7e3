import React from "react";
import { Card } from "../ui/card";
import { Progress } from "../ui/progress";
import { useAssistantSetup } from "../../hooks/useAssistantSetup";
import StepNameAssistant from "./StepNameAssistant";
import StepBackstory from "./StepBackstory";
import StepComplete from "./StepComplete";

interface SetupWizardProps {
  onComplete?: () => void;
}

const SetupWizard: React.FC<SetupWizardProps> = ({ onComplete }) => {
  const {
    currentStep,
    formData,
    isLoading,
    error,
    nextStep,
    prevStep,
    updateFormData,
    createAssistant,
  } = useAssistantSetup();

  const totalSteps = 3;
  const progress = (currentStep / totalSteps) * 100;

  const handleNext = () => {
    if (currentStep === 1 && !formData.name.trim()) {
      return; // Validation will be handled in the step component
    }

    if (currentStep === 2) {
      // Create the assistant on the final step
      createAssistant().then(() => {
        nextStep();
        if (onComplete) {
          setTimeout(onComplete, 2000); // Give time to show success message
        }
      });
    } else {
      nextStep();
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <StepNameAssistant
            name={formData.name}
            onNameChange={name => updateFormData({ name })}
            onNext={handleNext}
            isLoading={isLoading}
            error={error}
          />
        );
      case 2:
        return (
          <StepBackstory
            backstory={formData.backstory}
            onBackstoryChange={backstory => updateFormData({ backstory })}
            onNext={handleNext}
            onPrev={prevStep}
            isLoading={isLoading}
            error={error}
          />
        );
      case 3:
        return <StepComplete assistantName={formData.name} onFinish={onComplete} />;
      default:
        return null;
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4">
      <div className="w-full max-w-2xl">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Welcome to VEDAVIVI</h1>
          <p className="text-gray-600">Let's set up your personal assistant to get you started</p>
        </div>

        <Card className="p-8">
          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>
                Step {currentStep} of {totalSteps}
              </span>
              <span>{Math.round(progress)}% complete</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>

          {/* Step Content */}
          {renderStep()}
        </Card>

        {/* Step Indicators */}
        <div className="mt-6 flex justify-center space-x-2">
          {Array.from({ length: totalSteps }, (_, i) => (
            <div
              key={i}
              className={`h-2 w-8 rounded-full ${
                i + 1 <= currentStep ? "bg-blue-600" : "bg-gray-300"
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default SetupWizard;
