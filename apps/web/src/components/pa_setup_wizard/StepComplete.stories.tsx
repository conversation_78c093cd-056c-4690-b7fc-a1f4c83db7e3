import React from "react";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import StepComplete from "./StepComplete";

const meta: Meta<typeof StepComplete> = {
  title: "PA Setup/StepComplete",
  component: StepComplete,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component: `
StepComplete is the final step of the PA setup wizard.
It shows a success message and provides next steps for the user to get started with their new assistant.
        `,
      },
    },
  },
  argTypes: {
    assistantName: {
      control: { type: "text" },
      description: "Name of the newly created assistant",
    },
    onFinish: {
      action: "onFinish",
      description: "Callback when user clicks the Get Started button",
    },
  },
};

export default meta;
type Story = StoryObj<typeof StepComplete>;

export const Default: Story = {
  args: {
    assistantName: "Alex",
  },
  parameters: {
    docs: {
      description: {
        story: "Default completion screen with assistant name",
      },
    },
  },
};

export const ShortName: Story = {
  args: {
    assistantName: "AI",
  },
  parameters: {
    docs: {
      description: {
        story: "Completion screen with a short assistant name",
      },
    },
  },
};

export const LongName: Story = {
  args: {
    assistantName: "Professor Knowledge Helper",
  },
  parameters: {
    docs: {
      description: {
        story: "Completion screen with a longer assistant name",
      },
    },
  },
};

// Component wrapper for personal names showcase
const PersonalNamesComponent = () => {
  const personalNames = [
    "Sarah",
    "Michael",
    "Emma",
    "David",
    "Sophie",
    "Alexander",
    "Maya",
    "Christopher",
  ];
  const [selectedName, setSelectedName] = React.useState("Sarah");

  return (
    <div className="w-full max-w-2xl">
      <div className="mb-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold text-blue-900 mb-3">Personal Assistant Names</h3>
        <div className="flex flex-wrap gap-2">
          {personalNames.map(name => (
            <button
              key={name}
              onClick={() => setSelectedName(name)}
              className={`px-3 py-1 rounded-full text-sm transition-colors ${
                selectedName === name
                  ? "bg-blue-600 text-white"
                  : "bg-white border border-blue-300 text-blue-700 hover:bg-blue-100"
              }`}
            >
              {name}
            </button>
          ))}
        </div>
        <p className="text-sm text-blue-700 mt-2">
          Click a name above to see how it appears in the completion screen
        </p>
      </div>

      <StepComplete
        assistantName={selectedName}
        onFinish={() => alert(`Starting with ${selectedName}!`)}
      />
    </div>
  );
};

export const PersonalNames: Story = {
  name: "Personal Names Showcase",
  render: () => <PersonalNamesComponent />,
  parameters: {
    docs: {
      description: {
        story: "Showcase of different personal names in the completion screen",
      },
    },
  },
};

// Component wrapper for professional names showcase
const ProfessionalNamesComponent = () => {
  const professionalNames = [
    "Assistant",
    "Helper",
    "Advisor",
    "Coordinator",
    "Specialist",
    "Consultant",
    "Manager",
    "Agent",
  ];
  const [selectedName, setSelectedName] = React.useState("Assistant");

  return (
    <div className="w-full max-w-2xl">
      <div className="mb-6 p-4 bg-green-50 rounded-lg">
        <h3 className="font-semibold text-green-900 mb-3">Professional Assistant Names</h3>
        <div className="flex flex-wrap gap-2">
          {professionalNames.map(name => (
            <button
              key={name}
              onClick={() => setSelectedName(name)}
              className={`px-3 py-1 rounded-full text-sm transition-colors ${
                selectedName === name
                  ? "bg-green-600 text-white"
                  : "bg-white border border-green-300 text-green-700 hover:bg-green-100"
              }`}
            >
              {name}
            </button>
          ))}
        </div>
        <p className="text-sm text-green-700 mt-2">
          Click a name above to see professional naming styles
        </p>
      </div>

      <StepComplete
        assistantName={selectedName}
        onFinish={() => alert(`Getting started with ${selectedName}!`)}
      />
    </div>
  );
};

export const ProfessionalNames: Story = {
  name: "Professional Names Showcase",
  render: () => <ProfessionalNamesComponent />,
  parameters: {
    docs: {
      description: {
        story: "Showcase of different professional names in the completion screen",
      },
    },
  },
};

// Component wrapper for interactive demo
const InteractiveDemoComponent = () => {
  const [assistantName, setAssistantName] = React.useState("Demo Assistant");
  const [showConfetti, setShowConfetti] = React.useState(false);

  const handleFinish = () => {
    setShowConfetti(true);
    setTimeout(() => {
      setShowConfetti(false);
      alert(`🎉 Welcome to your journey with ${assistantName}!`);
    }, 2000);
  };

  return (
    <div className="w-full max-w-2xl relative">
      {showConfetti && (
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2">
            <div className="text-4xl animate-bounce">🎉</div>
          </div>
          <div className="absolute top-10 left-1/4 transform -translate-x-1/2">
            <div className="text-2xl animate-bounce" style={{ animationDelay: "0.1s" }}>
              ✨
            </div>
          </div>
          <div className="absolute top-10 right-1/4 transform translate-x-1/2">
            <div className="text-2xl animate-bounce" style={{ animationDelay: "0.2s" }}>
              🎊
            </div>
          </div>
        </div>
      )}

      <div className="mb-6 p-4 bg-purple-50 rounded-lg">
        <h3 className="font-semibold text-purple-900 mb-3">Interactive Demo</h3>
        <div className="space-y-2">
          <label className="block text-sm font-medium text-purple-800">Assistant Name:</label>
          <input
            type="text"
            value={assistantName}
            onChange={e => setAssistantName(e.target.value)}
            className="w-full px-3 py-2 border border-purple-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
            placeholder="Enter assistant name"
          />
        </div>
        <p className="text-sm text-purple-700 mt-2">
          Customize the assistant name to see how it appears in the completion message
        </p>
      </div>

      <StepComplete assistantName={assistantName} onFinish={handleFinish} />
    </div>
  );
};

export const InteractiveDemo: Story = {
  name: "Interactive Demo",
  render: () => <InteractiveDemoComponent />,
  parameters: {
    docs: {
      description: {
        story: "Interactive demo with customizable assistant name and celebration effect",
      },
    },
  },
};

export const WelcomeMessageVariations: Story = {
  name: "Welcome Message Variations",
  render: () => {
    const variations = [
      { name: "Alex", emoji: "👋", style: "Friendly" },
      { name: "Professor Einstein", emoji: "🧠", style: "Academic" },
      { name: "Creative Muse", emoji: "🎨", style: "Artistic" },
      { name: "Code Ninja", emoji: "💻", style: "Technical" },
      { name: "Life Coach", emoji: "🌟", style: "Motivational" },
      { name: "Data Wizard", emoji: "📊", style: "Analytical" },
    ];

    return (
      <div className="space-y-8">
        <h3 className="text-xl font-semibold text-center">Welcome Message Variations</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {variations.map((variation, index) => (
            <div key={index} className="border rounded-lg p-4">
              <div className="text-center mb-3">
                <span className="text-2xl">{variation.emoji}</span>
                <h4 className="font-medium text-gray-700 mt-1">{variation.style} Style</h4>
              </div>
              <div className="scale-75 origin-top">
                <StepComplete
                  assistantName={variation.name}
                  onFinish={() => alert(`Starting with ${variation.name}!`)}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story: "Different assistant personalities shown in completion screens",
      },
    },
  },
};

export const NextStepsGuide: Story = {
  name: "Next Steps Guide",
  args: {
    assistantName: "Productivity Partner",
  },
  render: args => (
    <div className="w-full max-w-2xl">
      <div className="mb-6 p-4 bg-yellow-50 rounded-lg">
        <h3 className="font-semibold text-yellow-900 mb-2">📋 What Users See Next</h3>
        <p className="text-sm text-yellow-800">
          This completion screen provides clear next steps to help users get started with their new
          assistant. The numbered steps guide them through their first interactions.
        </p>
      </div>

      <StepComplete {...args} />

      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-medium text-gray-900 mb-2">Design Notes:</h4>
        <ul className="text-sm text-gray-700 space-y-1">
          <li>• Success icon reinforces positive completion</li>
          <li>• Personalized welcome message creates connection</li>
          <li>• Clear next steps reduce user confusion</li>
          <li>• Prominent CTA button guides immediate action</li>
          <li>• Secondary text provides reassurance about settings</li>
        </ul>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Detailed view of the completion screen with design annotations",
      },
    },
  },
};

export const SuccessStates: Story = {
  name: "Success States",
  render: () => {
    const successScenarios = [
      { name: "First Assistant", description: "New user's first setup" },
      { name: "Work Assistant", description: "Professional context" },
      { name: "Study Buddy", description: "Educational context" },
      { name: "Creative Partner", description: "Creative projects" },
    ];

    return (
      <div className="space-y-8">
        <h3 className="text-xl font-semibold text-center">Success State Examples</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {successScenarios.map((scenario, index) => (
            <div key={index} className="border rounded-lg p-4">
              <h4 className="font-medium mb-1 text-center text-gray-700">{scenario.description}</h4>
              <p className="text-sm text-gray-500 text-center mb-3">Assistant: {scenario.name}</p>
              <div className="scale-75 origin-top">
                <StepComplete
                  assistantName={scenario.name}
                  onFinish={() => alert(`Welcome ${scenario.name}!`)}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story: "Different use case scenarios for the completion screen",
      },
    },
  },
};
