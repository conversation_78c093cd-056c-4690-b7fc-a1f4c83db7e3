import React from "react";
import { But<PERSON> } from "../ui/button";

interface StepCompleteProps {
  assistantName: string;
  onFinish?: () => void;
}

const StepComplete: React.FC<StepCompleteProps> = ({ assistantName, onFinish }) => {
  return (
    <div className="space-y-6 text-center">
      <div className="space-y-4">
        {/* Success Icon */}
        <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
          <svg
            className="w-8 h-8 text-green-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>

        <div>
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">
            🎉 Welcome, {assistantName}!
          </h2>
          <p className="text-gray-600">
            Your personal assistant has been successfully created and is ready to help you.
          </p>
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-blue-900 mb-3">What's Next?</h3>
        <div className="space-y-3 text-left">
          <div className="flex items-start space-x-3">
            <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium mt-0.5">
              1
            </div>
            <div>
              <h4 className="font-medium text-blue-900">Start Chatting</h4>
              <p className="text-sm text-blue-800">
                Begin conversations with {assistantName} to get help with your tasks
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium mt-0.5">
              2
            </div>
            <div>
              <h4 className="font-medium text-blue-900">Customize Settings</h4>
              <p className="text-sm text-blue-800">
                Adjust preferences and settings to personalize your experience
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium mt-0.5">
              3
            </div>
            <div>
              <h4 className="font-medium text-blue-900">Explore Features</h4>
              <p className="text-sm text-blue-800">
                Discover all the ways {assistantName} can assist you
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-3">
        <Button onClick={onFinish} className="w-full py-3 text-lg" size="lg">
          Get Started with {assistantName}
        </Button>

        <p className="text-sm text-gray-500">
          You can always modify your assistant's settings later in your profile
        </p>
      </div>
    </div>
  );
};

export default StepComplete;
