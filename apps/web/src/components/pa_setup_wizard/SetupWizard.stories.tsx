import React from "react";
import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react-vite";
import { Mo<PERSON><PERSON>rovider } from "@apollo/client/testing";
import SetupWizard from "./SetupWizard";
import { CREATE_PERSONAL_ASSISTANT_MUTATION } from "@/graphql/mutations";
import { MY_ASSISTANT_QUERY, USER_HAS_ASSISTANT_QUERY } from "@/graphql/queries";

// Mock the useAssistantSetup hook
const mockUseAssistantSetup = {
  currentStep: 1,
  formData: { name: "", backstory: "" },
  isLoading: false,
  error: null,
  hasAssistant: false,
  assistant: null,
  checkingAssistant: false,
  setCurrentStep: () => {},
  updateFormData: () => {},
  nextStep: () => {},
  prevStep: () => {},
  createAssistant: () => Promise.resolve(),
  resetForm: () => {},
};

// Note: Hook mocking in Storybook is complex. These stories show the component
// with the actual hook behavior, which may require authentication and GraphQL setup.

const mockGraphQLQueries = [
  {
    request: {
      query: USER_HAS_ASSISTANT_QUERY,
    },
    result: {
      data: {
        userHasAssistant: false,
      },
    },
  },
  {
    request: {
      query: MY_ASSISTANT_QUERY,
    },
    result: {
      data: {
        myAssistant: null,
      },
    },
  },
  {
    request: {
      query: CREATE_PERSONAL_ASSISTANT_MUTATION,
      variables: {
        input: {
          name: "Test Assistant",
          backstory: "Test backstory",
          avatarFileId: null,
          configuration: null,
        },
      },
    },
    result: {
      data: {
        createPersonalAssistant: {
          assistant: {
            id: "test-id",
            name: "Test Assistant",
            backstory: "Test backstory",
          },
          success: true,
          errorMessage: null,
        },
      },
    },
  },
];

const meta: Meta<typeof SetupWizard> = {
  title: "PA Setup/SetupWizard",
  component: SetupWizard,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component: `
The SetupWizard component guides users through creating their personal assistant.
It includes three steps: naming the assistant, providing a backstory, and completion.
The wizard shows progress and allows navigation between steps.
        `,
      },
    },
  },
  decorators: [
    Story => (
      <MockedProvider mocks={mockGraphQLQueries} addTypename={false}>
        <Story />
      </MockedProvider>
    ),
  ],
  argTypes: {
    onComplete: {
      action: "onComplete",
      description: "Callback function called when the wizard is completed",
    },
  },
};

export default meta;
type Story = StoryObj<typeof SetupWizard>;

export const Step1_NameAssistant: Story = {
  name: "Step 1 - Name Assistant",
  args: {
    onComplete: undefined,
  },
  parameters: {
    docs: {
      description: {
        story: "First step of the wizard where users enter their assistant's name",
      },
    },
  },
  beforeEach: () => {
    mockUseAssistantSetup.currentStep = 1;
    mockUseAssistantSetup.formData = { name: "", backstory: "" };
    mockUseAssistantSetup.isLoading = false;
    mockUseAssistantSetup.error = null;
  },
};

export const Step1_WithValidationError: Story = {
  name: "Step 1 - With Validation Error",
  args: {
    onComplete: undefined,
  },
  parameters: {
    docs: {
      description: {
        story: "First step showing validation error state",
      },
    },
  },
  beforeEach: () => {
    mockUseAssistantSetup.currentStep = 1;
    mockUseAssistantSetup.formData = { name: "", backstory: "" };
    mockUseAssistantSetup.isLoading = false;
    mockUseAssistantSetup.error = "Please enter a name for your assistant";
  },
};

export const Step1_WithNameEntered: Story = {
  name: "Step 1 - With Name Entered",
  args: {
    onComplete: undefined,
  },
  parameters: {
    docs: {
      description: {
        story: "First step with a name already entered",
      },
    },
  },
  beforeEach: () => {
    mockUseAssistantSetup.currentStep = 1;
    mockUseAssistantSetup.formData = { name: "Alex", backstory: "" };
    mockUseAssistantSetup.isLoading = false;
    mockUseAssistantSetup.error = null;
  },
};

export const Step2_Backstory: Story = {
  name: "Step 2 - Backstory",
  args: {
    onComplete: undefined,
  },
  parameters: {
    docs: {
      description: {
        story: "Second step where users provide their assistant's backstory",
      },
    },
  },
  beforeEach: () => {
    mockUseAssistantSetup.currentStep = 2;
    mockUseAssistantSetup.formData = {
      name: "Alex",
      backstory:
        "I am your personal assistant, ready to help you with various tasks and provide information when needed.",
    };
    mockUseAssistantSetup.isLoading = false;
    mockUseAssistantSetup.error = null;
  },
};

export const Step2_Loading: Story = {
  name: "Step 2 - Creating Assistant",
  args: {
    onComplete: undefined,
  },
  parameters: {
    docs: {
      description: {
        story: "Second step in loading state while creating the assistant",
      },
    },
  },
  beforeEach: () => {
    mockUseAssistantSetup.currentStep = 2;
    mockUseAssistantSetup.formData = {
      name: "Alex",
      backstory: "I am your personal assistant with expertise in productivity and organization.",
    };
    mockUseAssistantSetup.isLoading = true;
    mockUseAssistantSetup.error = null;
  },
};

export const Step2_WithError: Story = {
  name: "Step 2 - With Error",
  args: {
    onComplete: undefined,
  },
  parameters: {
    docs: {
      description: {
        story: "Second step showing an error during assistant creation",
      },
    },
  },
  beforeEach: () => {
    mockUseAssistantSetup.currentStep = 2;
    mockUseAssistantSetup.formData = {
      name: "Alex",
      backstory: "I am your personal assistant.",
    };
    mockUseAssistantSetup.isLoading = false;
    mockUseAssistantSetup.error = "Failed to create assistant. Please try again.";
  },
};

export const Step3_Complete: Story = {
  name: "Step 3 - Complete",
  args: {
    onComplete: undefined,
  },
  parameters: {
    docs: {
      description: {
        story: "Final step showing successful completion",
      },
    },
  },
  beforeEach: () => {
    mockUseAssistantSetup.currentStep = 3;
    mockUseAssistantSetup.formData = {
      name: "Alex",
      backstory:
        "I am your personal assistant, ready to help you with various tasks and provide information when needed.",
    };
    mockUseAssistantSetup.isLoading = false;
    mockUseAssistantSetup.error = null;
  },
};

// Component wrapper for interactive demo
const ProgressDemoComponent = () => {
  const [currentStep, setCurrentStep] = React.useState(1);

  // Override the mock for this story
  const _customMockSetup = {
    ...mockUseAssistantSetup,
    currentStep,
    formData: {
      name: currentStep >= 2 ? "Demo Assistant" : "",
      backstory: currentStep === 3 ? "I am a demo assistant for showcasing the setup wizard." : "",
    },
    nextStep: () => setCurrentStep(prev => Math.min(prev + 1, 3)),
    prevStep: () => setCurrentStep(prev => Math.max(prev - 1, 1)),
  };

  // Note: In a real implementation, this would require proper hook mocking

  return (
    <MockedProvider mocks={mockGraphQLQueries} addTypename={false}>
      <div>
        <div className="mb-4 p-4 bg-gray-100 rounded">
          <h3 className="font-semibold mb-2">Interactive Demo</h3>
          <p className="text-sm text-gray-600 mb-2">
            Use the buttons below to navigate through the wizard steps
          </p>
          <div className="space-x-2">
            <button
              onClick={() => setCurrentStep(1)}
              className={`px-3 py-1 rounded text-sm ${
                currentStep === 1 ? "bg-blue-600 text-white" : "bg-white border"
              }`}
            >
              Step 1
            </button>
            <button
              onClick={() => setCurrentStep(2)}
              className={`px-3 py-1 rounded text-sm ${
                currentStep === 2 ? "bg-blue-600 text-white" : "bg-white border"
              }`}
            >
              Step 2
            </button>
            <button
              onClick={() => setCurrentStep(3)}
              className={`px-3 py-1 rounded text-sm ${
                currentStep === 3 ? "bg-blue-600 text-white" : "bg-white border"
              }`}
            >
              Step 3
            </button>
          </div>
        </div>
        <SetupWizard />
      </div>
    </MockedProvider>
  );
};

export const ProgressDemo: Story = {
  name: "Progress Demo",
  render: () => <ProgressDemoComponent />,
  parameters: {
    docs: {
      description: {
        story: "Interactive demo showing all steps of the wizard with navigation controls",
      },
    },
  },
};

export const WizardFlow: Story = {
  name: "Complete Wizard Flow",
  render: () => (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-4">Complete Setup Wizard Flow</h2>
        <p className="text-gray-600">All three steps of the wizard shown in sequence</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="border rounded-lg p-4">
          <h3 className="font-semibold mb-2 text-center">Step 1: Name Assistant</h3>
          <div className="scale-75 origin-top">
            <MockedProvider mocks={mockGraphQLQueries} addTypename={false}>
              <SetupWizard />
            </MockedProvider>
          </div>
        </div>

        <div className="border rounded-lg p-4">
          <h3 className="font-semibold mb-2 text-center">Step 2: Backstory</h3>
          <div className="scale-75 origin-top">
            <MockedProvider mocks={mockGraphQLQueries} addTypename={false}>
              <SetupWizard />
            </MockedProvider>
          </div>
        </div>

        <div className="border rounded-lg p-4">
          <h3 className="font-semibold mb-2 text-center">Step 3: Complete</h3>
          <div className="scale-75 origin-top">
            <MockedProvider mocks={mockGraphQLQueries} addTypename={false}>
              <SetupWizard />
            </MockedProvider>
          </div>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Overview of all three wizard steps displayed side by side",
      },
    },
  },
};
