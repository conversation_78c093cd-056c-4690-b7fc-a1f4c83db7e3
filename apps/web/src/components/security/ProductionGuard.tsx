/**
 * Production Security Guard Component
 *
 * This component provides an additional layer of security by blocking
 * access to development-only features in production environments.
 */

import React from "react";

interface ProductionGuardProps {
  children: React.ReactNode;
  feature: string;
}

export function ProductionGuard({ children, feature }: ProductionGuardProps) {
  const isProduction = import.meta.env.MODE === "production";

  if (isProduction) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
          <div className="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full mb-4">
            <svg
              className="w-6 h-6 text-red-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h1 className="text-xl font-semibold text-gray-900 text-center mb-2">Access Denied</h1>
          <p className="text-gray-600 text-center mb-4">
            The feature "{feature}" is not available in production environments.
          </p>
          <div className="text-center">
            <a
              href="/"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Return to Home
            </a>
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
