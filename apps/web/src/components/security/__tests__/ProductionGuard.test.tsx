import React from "react";
import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";

// Mock the import.meta.env at the module level
const mockIsProduction = jest.fn(() => false);

jest.mock("../ProductionGuard", () => {
  return {
    ProductionGuard: ({ children, feature }: { children: React.ReactNode; feature: string }) => {
      const isProduction = mockIsProduction();

      if (isProduction) {
        return (
          <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
              <div className="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full mb-4">
                <svg
                  className="w-6 h-6 text-red-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <h1 className="text-xl font-semibold text-gray-900 text-center mb-2">
                Access Denied
              </h1>
              <p className="text-gray-600 text-center mb-4">
                The feature "{feature}" is not available in production environments.
              </p>
              <div className="text-center">
                <a
                  href="/"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Return to Home
                </a>
              </div>
            </div>
          </div>
        );
      }

      return <>{children}</>;
    },
  };
});

import { ProductionGuard } from "../ProductionGuard";

describe("ProductionGuard", () => {
  const testChildren = <div data-testid="protected-content">Protected Feature Content</div>;
  const testFeatureName = "Test Feature";

  beforeEach(() => {
    // Reset to development mode by default
    mockIsProduction.mockReturnValue(false);
  });

  describe("Development Mode", () => {
    it("renders children when in development mode", () => {
      mockIsProduction.mockReturnValue(false);

      render(<ProductionGuard feature={testFeatureName}>{testChildren}</ProductionGuard>);

      expect(screen.getByTestId("protected-content")).toBeInTheDocument();
      expect(screen.getByText("Protected Feature Content")).toBeInTheDocument();
    });

    it("does not show access denied message in development", () => {
      mockIsProduction.mockReturnValue(false);

      render(<ProductionGuard feature={testFeatureName}>{testChildren}</ProductionGuard>);

      expect(screen.queryByText("Access Denied")).not.toBeInTheDocument();
      expect(
        screen.queryByText(/not available in production environments/),
      ).not.toBeInTheDocument();
    });

    it("renders multiple children in development mode", () => {
      mockIsProduction.mockReturnValue(false);

      render(
        <ProductionGuard feature={testFeatureName}>
          <div data-testid="child-1">Child 1</div>
          <div data-testid="child-2">Child 2</div>
          <span data-testid="child-3">Child 3</span>
        </ProductionGuard>,
      );

      expect(screen.getByTestId("child-1")).toBeInTheDocument();
      expect(screen.getByTestId("child-2")).toBeInTheDocument();
      expect(screen.getByTestId("child-3")).toBeInTheDocument();
    });
  });

  describe("Production Mode", () => {
    it("shows access denied message when in production mode", () => {
      mockIsProduction.mockReturnValue(true);

      render(<ProductionGuard feature={testFeatureName}>{testChildren}</ProductionGuard>);

      expect(screen.getByText("Access Denied")).toBeInTheDocument();
      expect(screen.getByText(/not available in production environments/)).toBeInTheDocument();
      expect(screen.queryByTestId("protected-content")).not.toBeInTheDocument();
    });

    it("displays the feature name in the access denied message", () => {
      mockIsProduction.mockReturnValue(true);
      const featureName = "Database Admin Panel";

      render(<ProductionGuard feature={featureName}>{testChildren}</ProductionGuard>);

      expect(
        screen.getByText(
          `The feature "${featureName}" is not available in production environments.`,
        ),
      ).toBeInTheDocument();
    });

    it("shows return to home link in production mode", () => {
      mockIsProduction.mockReturnValue(true);

      render(<ProductionGuard feature={testFeatureName}>{testChildren}</ProductionGuard>);

      const homeLink = screen.getByRole("link", { name: "Return to Home" });
      expect(homeLink).toBeInTheDocument();
      expect(homeLink).toHaveAttribute("href", "/");
    });

    it("blocks all children content in production mode", () => {
      mockIsProduction.mockReturnValue(true);

      render(
        <ProductionGuard feature="Debug Tools">
          <div data-testid="debug-panel">Debug Panel</div>
          <button data-testid="danger-button">Delete All Data</button>
          <input data-testid="admin-input" placeholder="Admin command" />
        </ProductionGuard>,
      );

      expect(screen.queryByTestId("debug-panel")).not.toBeInTheDocument();
      expect(screen.queryByTestId("danger-button")).not.toBeInTheDocument();
      expect(screen.queryByTestId("admin-input")).not.toBeInTheDocument();
    });
  });

  describe("Feature Name Handling", () => {
    beforeEach(() => {
      mockIsProduction.mockReturnValue(true);
    });

    it("handles empty feature name", () => {
      render(<ProductionGuard feature="">{testChildren}</ProductionGuard>);

      expect(
        screen.getByText('The feature "" is not available in production environments.'),
      ).toBeInTheDocument();
    });

    it("handles feature name with special characters", () => {
      const specialFeatureName = "Admin Panel (v2.0) - Database & User Management";

      render(<ProductionGuard feature={specialFeatureName}>{testChildren}</ProductionGuard>);

      expect(
        screen.getByText(
          `The feature "${specialFeatureName}" is not available in production environments.`,
        ),
      ).toBeInTheDocument();
    });

    it("handles very long feature names", () => {
      const longFeatureName =
        "Super Long Feature Name That Should Still Be Displayed Correctly In The UI Even If It Takes Up Multiple Lines";

      render(<ProductionGuard feature={longFeatureName}>{testChildren}</ProductionGuard>);

      expect(
        screen.getByText(
          `The feature "${longFeatureName}" is not available in production environments.`,
        ),
      ).toBeInTheDocument();
    });
  });

  describe("UI and Styling", () => {
    beforeEach(() => {
      mockIsProduction.mockReturnValue(true);
    });

    it("renders with proper CSS classes for container", () => {
      render(<ProductionGuard feature={testFeatureName}>{testChildren}</ProductionGuard>);

      const heading = screen.getByText("Access Denied");
      expect(heading).toBeInTheDocument();
      expect(heading).toHaveClass(
        "text-xl",
        "font-semibold",
        "text-gray-900",
        "text-center",
        "mb-2",
      );
    });

    it("has proper warning icon", () => {
      render(<ProductionGuard feature={testFeatureName}>{testChildren}</ProductionGuard>);

      const svg = document.querySelector("svg");
      expect(svg).toBeInTheDocument();
      expect(svg).toHaveClass("w-6", "h-6", "text-red-600");
    });

    it("has styled return to home button", () => {
      render(<ProductionGuard feature={testFeatureName}>{testChildren}</ProductionGuard>);

      const homeLink = screen.getByRole("link", { name: "Return to Home" });
      expect(homeLink).toHaveClass(
        "inline-flex",
        "items-center",
        "px-4",
        "py-2",
        "border",
        "border-transparent",
        "text-sm",
        "font-medium",
        "rounded-md",
        "text-white",
        "bg-blue-600",
        "hover:bg-blue-700",
      );
    });
  });

  describe("Accessibility", () => {
    beforeEach(() => {
      mockIsProduction.mockReturnValue(true);
    });

    it("has proper heading structure", () => {
      render(<ProductionGuard feature={testFeatureName}>{testChildren}</ProductionGuard>);

      const heading = screen.getByRole("heading", { level: 1 });
      expect(heading).toHaveTextContent("Access Denied");
    });

    it("has accessible link to home", () => {
      render(<ProductionGuard feature={testFeatureName}>{testChildren}</ProductionGuard>);

      const homeLink = screen.getByRole("link", { name: "Return to Home" });
      expect(homeLink).toBeInTheDocument();
      expect(homeLink).toHaveAttribute("href", "/");
    });

    it("provides clear error message for screen readers", () => {
      render(<ProductionGuard feature="Debug Console">{testChildren}</ProductionGuard>);

      const errorMessage = screen.getByText(
        'The feature "Debug Console" is not available in production environments.',
      );
      expect(errorMessage).toBeInTheDocument();
    });
  });

  describe("Security Scenarios", () => {
    const securityTestCases = [
      {
        feature: "Database Administration",
        content: <div data-testid="db-admin">DROP TABLE users;</div>,
      },
      {
        feature: "User Data Export",
        content: <div data-testid="data-export">Export all user data</div>,
      },
      {
        feature: "System Configuration",
        content: <div data-testid="sys-config">Modify system settings</div>,
      },
      {
        feature: "Debug Mode",
        content: <div data-testid="debug-mode">Enable debug logging</div>,
      },
    ];

    securityTestCases.forEach(({ feature, content }) => {
      it(`blocks ${feature} in production`, () => {
        mockIsProduction.mockReturnValue(true);

        render(<ProductionGuard feature={feature}>{content}</ProductionGuard>);

        expect(screen.queryByTestId(content.props["data-testid"])).not.toBeInTheDocument();
        expect(screen.getByText("Access Denied")).toBeInTheDocument();
      });

      it(`allows ${feature} in development`, () => {
        mockIsProduction.mockReturnValue(false);

        render(<ProductionGuard feature={feature}>{content}</ProductionGuard>);

        expect(screen.getByTestId(content.props["data-testid"])).toBeInTheDocument();
        expect(screen.queryByText("Access Denied")).not.toBeInTheDocument();
      });
    });
  });
});
