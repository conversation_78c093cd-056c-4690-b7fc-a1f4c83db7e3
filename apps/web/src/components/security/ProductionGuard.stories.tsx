import React from "react";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import { ProductionGuard } from "./ProductionGuard";

const meta: Meta<typeof ProductionGuard> = {
  title: "Security/ProductionGuard",
  component: ProductionGuard,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component: `
ProductionGuard is a security component that blocks access to development-only features in production environments.
It checks the current environment mode and either renders the children (in development) or shows an access denied message (in production).
        `,
      },
    },
  },
  argTypes: {
    feature: {
      control: { type: "text" },
      description: "Name of the feature being protected",
    },
    children: {
      control: { type: "object" },
      description: "Content to render when not in production",
    },
  },
};

export default meta;
type Story = StoryObj<typeof ProductionGuard>;

export const DevelopmentMode: Story = {
  args: {
    feature: "Debug Console",
    children: (
      <div className="p-8 bg-green-50 rounded-lg">
        <h2 className="text-2xl font-bold text-green-800 mb-4">🛠️ Development Feature</h2>
        <p className="text-green-700 mb-4">
          This feature is only available in development mode. You can see this content because the
          environment is set to development.
        </p>
        <div className="bg-white p-4 rounded border">
          <h3 className="font-semibold mb-2">Debug Information:</h3>
          <ul className="list-disc list-inside text-sm">
            <li>Environment: development</li>
            <li>Feature: Debug Console</li>
            <li>Access: Granted</li>
          </ul>
        </div>
      </div>
    ),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Shows the content when running in development mode - children are rendered normally",
      },
    },
  },
};

export const ProductionMode: Story = {
  args: {
    feature: "Debug Console",
    children: (
      <div className="p-8 bg-green-50 rounded-lg">
        <h2 className="text-2xl font-bold text-green-800 mb-4">🛠️ Development Feature</h2>
        <p className="text-green-700">This content should not be visible in production mode.</p>
      </div>
    ),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Shows the access denied message when running in production mode - children are blocked",
      },
    },
    // Simulate production mode for this story
    mockData: [
      {
        url: "/api/env",
        method: "GET",
        status: 200,
        response: { MODE: "production" },
      },
    ],
  },
  // Override the import.meta.env for this story
  decorators: [
    Story => {
      // Mock production environment
      const originalEnv = import.meta.env;
      import.meta.env = { ...originalEnv, MODE: "production" };

      return <Story />;
    },
  ],
};

export const DatabaseAdminFeature: Story = {
  args: {
    feature: "Database Administration Panel",
    children: (
      <div className="p-8 bg-red-50 rounded-lg border-2 border-red-200">
        <h2 className="text-2xl font-bold text-red-800 mb-4">⚠️ Database Admin Panel</h2>
        <p className="text-red-700 mb-4">
          This is a dangerous administrative interface that should never be exposed in production.
        </p>
        <div className="bg-white p-4 rounded border border-red-300">
          <h3 className="font-semibold text-red-800 mb-2">Available Actions:</h3>
          <ul className="list-disc list-inside text-sm text-red-700">
            <li>Direct database queries</li>
            <li>User data modification</li>
            <li>System configuration changes</li>
            <li>Debug mode activation</li>
          </ul>
        </div>
      </div>
    ),
  },
  parameters: {
    docs: {
      description: {
        story: "Example of protecting a dangerous administrative feature",
      },
    },
  },
};

export const APITestingInterface: Story = {
  args: {
    feature: "API Testing Interface",
    children: (
      <div className="p-8 bg-blue-50 rounded-lg">
        <h2 className="text-2xl font-bold text-blue-800 mb-4">🧪 API Testing Tools</h2>
        <p className="text-blue-700 mb-4">
          Internal tools for testing API endpoints and GraphQL mutations.
        </p>
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-white p-4 rounded border">
            <h3 className="font-semibold mb-2">GraphQL Playground</h3>
            <p className="text-sm text-gray-600">Interactive GraphQL query builder</p>
          </div>
          <div className="bg-white p-4 rounded border">
            <h3 className="font-semibold mb-2">REST API Tester</h3>
            <p className="text-sm text-gray-600">Test REST endpoints with custom payloads</p>
          </div>
        </div>
      </div>
    ),
  },
  parameters: {
    docs: {
      description: {
        story: "Example of protecting API testing tools from production users",
      },
    },
  },
};

export const SystemMetricsFeature: Story = {
  args: {
    feature: "System Metrics Dashboard",
    children: (
      <div className="p-8 bg-purple-50 rounded-lg">
        <h2 className="text-2xl font-bold text-purple-800 mb-4">📊 System Metrics</h2>
        <p className="text-purple-700 mb-4">Internal system monitoring and performance metrics.</p>
        <div className="bg-white p-4 rounded border">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-purple-600">98.5%</div>
              <div className="text-sm text-gray-600">Uptime</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-purple-600">245ms</div>
              <div className="text-sm text-gray-600">Avg Response</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-purple-600">1,247</div>
              <div className="text-sm text-gray-600">Active Users</div>
            </div>
          </div>
        </div>
      </div>
    ),
  },
  parameters: {
    docs: {
      description: {
        story: "Example of protecting internal system metrics from end users",
      },
    },
  },
};
