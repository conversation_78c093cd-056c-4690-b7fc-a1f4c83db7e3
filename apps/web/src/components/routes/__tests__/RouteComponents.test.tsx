import React from "react";
import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import { render, screen } from "@testing-library/react";
import { MemoryRouter } from "react-router-dom";
import "@testing-library/jest-dom";
import {
  ChatRoute,
  DashboardRoute,
  RootRoute,
  SettingsRoute,
  SignInRoute,
  SignUpRoute,
} from "../RouteComponents";

// Mock the page components
jest.mock("../../../pages/auth/SignUpPage", () => {
  return function MockSignUpPage() {
    return <div data-testid="signup-page">Sign Up Page</div>;
  };
});

jest.mock("../../../pages/auth/SignInPage", () => {
  return function MockSignInPage() {
    return <div data-testid="signin-page">Sign In Page</div>;
  };
});

jest.mock("../../../pages/dashboard/DashboardPage", () => {
  return function MockDashboardPage() {
    return <div data-testid="dashboard-page">Dashboard Page</div>;
  };
});

jest.mock("../../../pages/UserSettingsPage", () => {
  return function MockUserSettingsPage() {
    return <div data-testid="settings-page">Settings Page</div>;
  };
});

jest.mock("../../../pages/chat/ChatPage", () => {
  return {
    ChatPage: function MockChatPage() {
      return <div data-testid="chat-page">Chat Page</div>;
    },
  };
});

// Mock Clerk components and react-router-dom Navigate
const mockNavigate = jest.fn();
let mockIsSignedIn = false;

jest.mock("@clerk/clerk-react", () => ({
  SignedIn: ({ children }: { children: React.ReactNode }) => {
    if (mockIsSignedIn) {
      return <div data-testid="signed-in">{children}</div>;
    }
    return <div data-testid="signed-in" style={{ display: "none" }} />;
  },
  SignedOut: ({ children }: { children: React.ReactNode }) => {
    if (!mockIsSignedIn) {
      return <div data-testid="signed-out">{children}</div>;
    }
    return <div data-testid="signed-out" style={{ display: "none" }} />;
  },
}));

jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  Navigate: ({ to, replace }: { to: string; replace?: boolean }) => {
    mockNavigate(to, { replace });
    return <div data-testid="navigate" data-to={to} data-replace={replace?.toString()} />;
  },
}));

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <MemoryRouter>{children}</MemoryRouter>
);

describe("RouteComponents", () => {
  beforeEach(() => {
    mockNavigate.mockClear();
    mockIsSignedIn = false; // Default to signed out
  });

  describe("RootRoute", () => {
    it("renders navigation for signed out users", () => {
      mockIsSignedIn = false;

      render(
        <TestWrapper>
          <RootRoute />
        </TestWrapper>,
      );

      expect(screen.getByTestId("signed-out")).toBeInTheDocument();
      expect(screen.getByTestId("navigate")).toHaveAttribute("data-to", "/pa/sign-up");
    });

    it("renders navigation for signed in users", () => {
      mockIsSignedIn = true;

      render(
        <TestWrapper>
          <RootRoute />
        </TestWrapper>,
      );

      expect(screen.getByTestId("signed-in")).toBeInTheDocument();
      expect(screen.getByTestId("navigate")).toHaveAttribute("data-to", "/pa/dashboard");
    });
  });

  describe("SignUpRoute", () => {
    it("shows sign up page when signed out", () => {
      mockIsSignedIn = false;

      render(
        <TestWrapper>
          <SignUpRoute />
        </TestWrapper>,
      );

      expect(screen.getByTestId("signup-page")).toBeInTheDocument();
    });

    it("redirects to dashboard when signed in", () => {
      mockIsSignedIn = true;

      render(
        <TestWrapper>
          <SignUpRoute />
        </TestWrapper>,
      );

      expect(screen.getByTestId("navigate")).toHaveAttribute("data-to", "/pa/dashboard");
    });
  });

  describe("SignInRoute", () => {
    it("shows sign in page when signed out", () => {
      mockIsSignedIn = false;

      render(
        <TestWrapper>
          <SignInRoute />
        </TestWrapper>,
      );

      expect(screen.getByTestId("signin-page")).toBeInTheDocument();
    });

    it("redirects to dashboard when signed in", () => {
      mockIsSignedIn = true;

      render(
        <TestWrapper>
          <SignInRoute />
        </TestWrapper>,
      );

      expect(screen.getByTestId("navigate")).toHaveAttribute("data-to", "/pa/dashboard");
    });
  });

  describe("DashboardRoute", () => {
    it("shows dashboard page when signed in", () => {
      mockIsSignedIn = true;

      render(
        <TestWrapper>
          <DashboardRoute />
        </TestWrapper>,
      );

      expect(screen.getByTestId("dashboard-page")).toBeInTheDocument();
    });

    it("redirects to sign up when signed out", () => {
      mockIsSignedIn = false;

      render(
        <TestWrapper>
          <DashboardRoute />
        </TestWrapper>,
      );

      expect(screen.getByTestId("navigate")).toHaveAttribute("data-to", "/pa/sign-up");
    });
  });

  describe("SettingsRoute", () => {
    it("shows settings page when signed in", () => {
      mockIsSignedIn = true;

      render(
        <TestWrapper>
          <SettingsRoute />
        </TestWrapper>,
      );

      expect(screen.getByTestId("settings-page")).toBeInTheDocument();
    });

    it("redirects to sign in when signed out", () => {
      mockIsSignedIn = false;

      render(
        <TestWrapper>
          <SettingsRoute />
        </TestWrapper>,
      );

      expect(screen.getByTestId("navigate")).toHaveAttribute("data-to", "/pa/sign-in");
    });
  });

  describe("ChatRoute", () => {
    it("shows chat page when signed in", () => {
      mockIsSignedIn = true;

      render(
        <TestWrapper>
          <ChatRoute />
        </TestWrapper>,
      );

      expect(screen.getByTestId("chat-page")).toBeInTheDocument();
    });

    it("redirects to sign in when signed out", () => {
      mockIsSignedIn = false;

      render(
        <TestWrapper>
          <ChatRoute />
        </TestWrapper>,
      );

      expect(screen.getByTestId("navigate")).toHaveAttribute("data-to", "/pa/sign-in");
    });
  });

  describe("Authentication Flow Logic", () => {
    it("all protected routes redirect unauthenticated users appropriately", () => {
      mockIsSignedIn = false;

      const protectedRoutes = [
        { component: DashboardRoute, expectedRedirect: "/pa/sign-up" },
        { component: SettingsRoute, expectedRedirect: "/pa/sign-in" },
        { component: ChatRoute, expectedRedirect: "/pa/sign-in" },
      ];

      protectedRoutes.forEach(({ component: Component, expectedRedirect }) => {
        const { unmount } = render(
          <TestWrapper>
            <Component />
          </TestWrapper>,
        );

        expect(screen.getByTestId("navigate")).toHaveAttribute("data-to", expectedRedirect);
        unmount();
      });
    });

    it("all auth routes redirect authenticated users to dashboard", () => {
      mockIsSignedIn = true;

      const authRoutes = [RootRoute, SignUpRoute, SignInRoute];

      authRoutes.forEach(Component => {
        const { unmount } = render(
          <TestWrapper>
            <Component />
          </TestWrapper>,
        );

        expect(screen.getByTestId("navigate")).toHaveAttribute("data-to", "/pa/dashboard");
        unmount();
      });
    });

    it("uses replace navigation to prevent back button issues", () => {
      mockIsSignedIn = false;

      render(
        <TestWrapper>
          <RootRoute />
        </TestWrapper>,
      );

      expect(screen.getByTestId("navigate")).toHaveAttribute("data-replace", "true");
    });
  });

  describe("URL Path Consistency", () => {
    it("uses consistent /pa/ prefix for all routes", () => {
      mockIsSignedIn = false;

      const routes = [
        { component: RootRoute, expectedPath: "/pa/sign-up" },
        { component: DashboardRoute, expectedPath: "/pa/sign-up" },
        { component: SettingsRoute, expectedPath: "/pa/sign-in" },
        { component: ChatRoute, expectedPath: "/pa/sign-in" },
      ];

      routes.forEach(({ component: Component, expectedPath }) => {
        const { unmount } = render(
          <TestWrapper>
            <Component />
          </TestWrapper>,
        );

        const navigateElement = screen.getByTestId("navigate");
        expect(navigateElement.getAttribute("data-to")).toMatch(/^\/pa\//);
        expect(navigateElement.getAttribute("data-to")).toBe(expectedPath);

        unmount();
      });
    });

    it("authenticated routes redirect to dashboard with /pa/ prefix", () => {
      mockIsSignedIn = true;

      const authRoutes = [RootRoute, SignUpRoute, SignInRoute];

      authRoutes.forEach(Component => {
        const { unmount } = render(
          <TestWrapper>
            <Component />
          </TestWrapper>,
        );

        const navigateElement = screen.getByTestId("navigate");
        expect(navigateElement.getAttribute("data-to")).toBe("/pa/dashboard");

        unmount();
      });
    });
  });
});
