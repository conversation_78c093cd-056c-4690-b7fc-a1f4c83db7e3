import React from "react";
import { Navigate } from "react-router-dom";
import SignUpPage from "../../pages/auth/SignUpPage";
import SignInPage from "../../pages/auth/SignInPage";
import DashboardPage from "../../pages/dashboard/DashboardPage";
import UserSettingsPage from "../../pages/UserSettingsPage";
import { ChatPage } from "../../pages/chat/ChatPage";
import { SignedIn, SignedOut } from "@clerk/clerk-react";

export function RootRoute() {
  return (
    <>
      <SignedIn>
        <Navigate to="/pa/dashboard" replace />
      </SignedIn>
      <SignedOut>
        <Navigate to="/pa/sign-up" replace />
      </SignedOut>
    </>
  );
}

export function SignUpRoute() {
  return (
    <>
      <SignedIn>
        <Navigate to="/pa/dashboard" replace />
      </SignedIn>
      <SignedOut>
        <SignUpPage />
      </SignedOut>
    </>
  );
}

export function SignInRoute() {
  return (
    <>
      <SignedIn>
        <Navigate to="/pa/dashboard" replace />
      </SignedIn>
      <SignedOut>
        <SignInPage />
      </SignedOut>
    </>
  );
}

export function DashboardRoute() {
  return (
    <>
      <SignedIn>
        <DashboardPage />
      </SignedIn>
      <SignedOut>
        <Navigate to="/pa/sign-up" replace />
      </SignedOut>
    </>
  );
}

export function SettingsRoute() {
  return (
    <>
      <SignedIn>
        <UserSettingsPage />
      </SignedIn>
      <SignedOut>
        <Navigate to="/pa/sign-in" replace />
      </SignedOut>
    </>
  );
}

export function ChatRoute() {
  return (
    <>
      <SignedIn>
        <ChatPage />
      </SignedIn>
      <SignedOut>
        <Navigate to="/pa/sign-in" replace />
      </SignedOut>
    </>
  );
}
