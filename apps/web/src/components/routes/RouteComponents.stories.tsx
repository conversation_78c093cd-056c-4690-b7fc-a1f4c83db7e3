import React from "react";
import type { Met<PERSON>, StoryObj } from "@storybook/react-vite";
import { MemoryRouter } from "react-router-dom";
import { SignedIn, SignedOut } from "@clerk/clerk-react";
import {
  ChatRoute,
  DashboardRoute,
  RootRoute,
  SettingsRoute,
  SignInRoute,
  SignUpRoute,
} from "./RouteComponents";

// Create a mock user state wrapper
const AuthStateWrapper = ({
  isSignedIn,
  children,
}: {
  isSignedIn: boolean;
  children: React.ReactNode;
}) => {
  if (isSignedIn) {
    return (
      <>
        <SignedIn>{children}</SignedIn>
        <SignedOut>
          <div style={{ display: "none" }} />
        </SignedOut>
      </>
    );
  } else {
    return (
      <>
        <SignedIn>
          <div style={{ display: "none" }} />
        </SignedIn>
        <SignedOut>{children}</SignedOut>
      </>
    );
  }
};

const meta: Meta = {
  title: "Routes/RouteComponents",
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component: `
RouteComponents provide authentication-aware routing logic using <PERSON>.
Each route component handles authentication state and redirects users appropriately:
- Signed in users are redirected to protected routes
- Signed out users are redirected to authentication routes
        `,
      },
    },
  },
  decorators: [
    Story => (
      <MemoryRouter initialEntries={["/"]}>
        <Story />
      </MemoryRouter>
    ),
  ],
};

export default meta;
type Story = StoryObj;

export const RootRouteSignedOut: Story = {
  name: "Root Route - Signed Out",
  render: () => (
    <AuthStateWrapper isSignedIn={false}>
      <RootRoute />
    </AuthStateWrapper>
  ),
  parameters: {
    docs: {
      description: {
        story: "Root route when user is signed out - should redirect to sign up",
      },
    },
  },
};

export const RootRouteSignedIn: Story = {
  name: "Root Route - Signed In",
  render: () => (
    <AuthStateWrapper isSignedIn={true}>
      <RootRoute />
    </AuthStateWrapper>
  ),
  parameters: {
    docs: {
      description: {
        story: "Root route when user is signed in - should redirect to dashboard",
      },
    },
  },
};

export const SignUpRouteSignedOut: Story = {
  name: "Sign Up Route - Signed Out",
  render: () => (
    <AuthStateWrapper isSignedIn={false}>
      <SignUpRoute />
    </AuthStateWrapper>
  ),
  parameters: {
    docs: {
      description: {
        story: "Sign up route when user is signed out - shows sign up page",
      },
    },
  },
};

export const SignUpRouteSignedIn: Story = {
  name: "Sign Up Route - Signed In",
  render: () => (
    <AuthStateWrapper isSignedIn={true}>
      <SignUpRoute />
    </AuthStateWrapper>
  ),
  parameters: {
    docs: {
      description: {
        story: "Sign up route when user is already signed in - should redirect to dashboard",
      },
    },
  },
};

export const SignInRouteSignedOut: Story = {
  name: "Sign In Route - Signed Out",
  render: () => (
    <AuthStateWrapper isSignedIn={false}>
      <SignInRoute />
    </AuthStateWrapper>
  ),
  parameters: {
    docs: {
      description: {
        story: "Sign in route when user is signed out - shows sign in page",
      },
    },
  },
};

export const SignInRouteSignedIn: Story = {
  name: "Sign In Route - Signed In",
  render: () => (
    <AuthStateWrapper isSignedIn={true}>
      <SignInRoute />
    </AuthStateWrapper>
  ),
  parameters: {
    docs: {
      description: {
        story: "Sign in route when user is already signed in - should redirect to dashboard",
      },
    },
  },
};

export const DashboardRouteSignedIn: Story = {
  name: "Dashboard Route - Signed In",
  render: () => (
    <AuthStateWrapper isSignedIn={true}>
      <DashboardRoute />
    </AuthStateWrapper>
  ),
  parameters: {
    docs: {
      description: {
        story: "Dashboard route when user is signed in - shows dashboard page",
      },
    },
  },
};

export const DashboardRouteSignedOut: Story = {
  name: "Dashboard Route - Signed Out",
  render: () => (
    <AuthStateWrapper isSignedIn={false}>
      <DashboardRoute />
    </AuthStateWrapper>
  ),
  parameters: {
    docs: {
      description: {
        story: "Dashboard route when user is signed out - should redirect to sign up",
      },
    },
  },
};

export const SettingsRouteSignedIn: Story = {
  name: "Settings Route - Signed In",
  render: () => (
    <AuthStateWrapper isSignedIn={true}>
      <SettingsRoute />
    </AuthStateWrapper>
  ),
  parameters: {
    docs: {
      description: {
        story: "Settings route when user is signed in - shows settings page",
      },
    },
  },
};

export const SettingsRouteSignedOut: Story = {
  name: "Settings Route - Signed Out",
  render: () => (
    <AuthStateWrapper isSignedIn={false}>
      <SettingsRoute />
    </AuthStateWrapper>
  ),
  parameters: {
    docs: {
      description: {
        story: "Settings route when user is signed out - should redirect to sign in",
      },
    },
  },
};

export const ChatRouteSignedIn: Story = {
  name: "Chat Route - Signed In",
  render: () => (
    <AuthStateWrapper isSignedIn={true}>
      <ChatRoute />
    </AuthStateWrapper>
  ),
  parameters: {
    docs: {
      description: {
        story: "Chat route when user is signed in - shows chat page",
      },
    },
  },
};

export const ChatRouteSignedOut: Story = {
  name: "Chat Route - Signed Out",
  render: () => (
    <AuthStateWrapper isSignedIn={false}>
      <ChatRoute />
    </AuthStateWrapper>
  ),
  parameters: {
    docs: {
      description: {
        story: "Chat route when user is signed out - should redirect to sign in",
      },
    },
  },
};

// Overview story showing the routing logic
export const RoutingOverview: Story = {
  name: "Routing Logic Overview",
  render: () => (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Route Component Logic</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-green-50 p-6 rounded-lg border border-green-200">
          <h2 className="text-xl font-semibold text-green-800 mb-4">✅ Signed In Users</h2>
          <ul className="space-y-2 text-green-700">
            <li>
              • <strong>Root (/) :</strong> → Dashboard
            </li>
            <li>
              • <strong>Sign Up:</strong> → Dashboard
            </li>
            <li>
              • <strong>Sign In:</strong> → Dashboard
            </li>
            <li>
              • <strong>Dashboard:</strong> → Dashboard Page
            </li>
            <li>
              • <strong>Settings:</strong> → Settings Page
            </li>
            <li>
              • <strong>Chat:</strong> → Chat Page
            </li>
          </ul>
        </div>

        <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
          <h2 className="text-xl font-semibold text-blue-800 mb-4">🔒 Signed Out Users</h2>
          <ul className="space-y-2 text-blue-700">
            <li>
              • <strong>Root (/) :</strong> → Sign Up
            </li>
            <li>
              • <strong>Sign Up:</strong> → Sign Up Page
            </li>
            <li>
              • <strong>Sign In:</strong> → Sign In Page
            </li>
            <li>
              • <strong>Dashboard:</strong> → Sign Up
            </li>
            <li>
              • <strong>Settings:</strong> → Sign In
            </li>
            <li>
              • <strong>Chat:</strong> → Sign In
            </li>
          </ul>
        </div>
      </div>

      <div className="bg-yellow-50 p-6 rounded-lg border border-yellow-200">
        <h3 className="text-lg font-semibold text-yellow-800 mb-3">🛡️ Security Features</h3>
        <ul className="space-y-1 text-yellow-700">
          <li>• Automatic redirects prevent unauthorized access</li>
          <li>• No flash of unauthenticated content</li>
          <li>• Consistent authentication flow</li>
          <li>• Protected routes require authentication</li>
        </ul>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Overview of how the route components handle authentication and redirection logic",
      },
    },
  },
};
