import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import React from "react";

// Mock component for Storybook that simulates the CliTokenManager behavior

// Create a mock version of the CliTokenManager component for Storybook
const MockCliTokenManager = ({
  isSignedIn = true,
  simulateError = false,
}: {
  isSignedIn?: boolean;
  simulateError?: boolean;
}) => {
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const [description, setDescription] = React.useState("");
  const [generatedToken, setGeneratedToken] = React.useState<string | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  const handleCreateToken = async () => {
    if (!isSignedIn) {
      setError("You must be signed in to create a token");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (simulateError) {
        throw new Error("Simulated API error");
      }

      // Mock successful token creation
      const mockToken = `a2a_cli_user123_${Math.random().toString(36).substring(2, 15)}`;
      setGeneratedToken(mockToken);
    } catch (err) {
      setError(
        simulateError
          ? "Simulated error: Failed to create token"
          : `Failed to create token: ${err instanceof Error ? err.message : "Unknown error"}`,
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyToken = () => {
    if (generatedToken) {
      navigator.clipboard.writeText(generatedToken);
    }
  };

  const resetModal = () => {
    setDescription("");
    setGeneratedToken(null);
    setError(null);
    setIsLoading(false);
  };

  const openModal = () => {
    resetModal();
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    resetModal();
  };

  if (!isSignedIn) {
    return (
      <div className="text-center p-4">
        <p className="text-gray-600">Please sign in to manage CLI tokens.</p>
      </div>
    );
  }

  return (
    <div className="p-4">
      <button
        onClick={openModal}
        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors"
      >
        Create CLI Token
      </button>

      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h2 className="text-xl font-semibold mb-4">Create CLI Token</h2>

            {!generatedToken ? (
              <div>
                <div className="mb-4">
                  <label
                    htmlFor="description"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Description
                  </label>
                  <input
                    id="description"
                    type="text"
                    value={description}
                    onChange={e => setDescription(e.target.value)}
                    placeholder="Enter token description"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                {error && (
                  <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                    {error}
                  </div>
                )}

                <div className="flex justify-end space-x-3">
                  <button
                    onClick={closeModal}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleCreateToken}
                    disabled={isLoading || !description.trim()}
                    className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md transition-colors"
                  >
                    {isLoading ? "Creating..." : "Create Token"}
                  </button>
                </div>
              </div>
            ) : (
              <div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Your CLI Token
                  </label>
                  <div className="bg-gray-100 p-3 rounded-md font-mono text-sm break-all">
                    {generatedToken}
                  </div>
                  <p className="text-sm text-gray-600 mt-2">
                    Copy this token and store it securely. You won't be able to see it again.
                  </p>
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    onClick={handleCopyToken}
                    className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md transition-colors"
                  >
                    Copy Token
                  </button>
                  <button
                    onClick={closeModal}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors"
                  >
                    Done
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

const meta: Meta<typeof MockCliTokenManager> = {
  title: "Components/CliTokenManager",
  component: MockCliTokenManager,
  parameters: {
    layout: "centered",
    docs: {
      story: {
        inline: false,
        iframeHeight: 500,
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    isSignedIn: {
      control: "boolean",
      description: "Whether the user is signed in",
      defaultValue: true,
    },
    simulateError: {
      control: "boolean",
      description: "Whether to simulate an error during token creation",
      defaultValue: false,
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default story: Renders the component in its initial state with successful token creation
export const Default: Story = {
  name: "Default (Signed In)",
  args: {
    isSignedIn: true,
    simulateError: false,
  },
};

// Story showing the component when user is signed out
export const SignedOut: Story = {
  name: "When Signed Out",
  args: {
    isSignedIn: false,
    simulateError: false,
  },
};

// Story showing error handling during token creation
export const TokenCreationError: Story = {
  name: "Token Creation Error",
  args: {
    isSignedIn: true,
    simulateError: true,
  },
};

// Interactive demo story
export const InteractiveDemo: Story = {
  name: "Interactive Demo",
  args: {
    isSignedIn: true,
    simulateError: false,
  },
};
