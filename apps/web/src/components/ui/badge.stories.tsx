import React from "react";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import { Badge } from "./badge";

const meta: Meta<typeof Badge> = {
  title: "Components/UI/Badge",
  component: Badge,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: { type: "select" },
      options: ["default", "secondary", "destructive", "outline"],
    },
    asChild: {
      control: { type: "boolean" },
    },
    children: {
      control: { type: "text" },
      defaultValue: "Badge",
    },
  },
};

export default meta;
type Story = StoryObj<typeof Badge>;

export const Default: Story = {
  args: {
    variant: "default",
    children: "Default",
  },
};

export const Secondary: Story = {
  args: {
    variant: "secondary",
    children: "Secondary",
  },
};

export const Destructive: Story = {
  args: {
    variant: "destructive",
    children: "Destructive",
  },
};

export const Outline: Story = {
  args: {
    variant: "outline",
    children: "Outline",
  },
};

export const AllVariants: Story = {
  name: "All Variants",
  render: () => (
    <div className="flex flex-wrap gap-2">
      <Badge variant="default">Default</Badge>
      <Badge variant="secondary">Secondary</Badge>
      <Badge variant="destructive">Destructive</Badge>
      <Badge variant="outline">Outline</Badge>
    </div>
  ),
};

export const ConversationBadges: Story = {
  name: "Conversation Use Cases",
  render: () => (
    <div className="flex flex-col gap-4">
      <div className="flex flex-wrap gap-2">
        <span className="text-sm font-medium">Status badges:</span>
        <Badge variant="default">Active</Badge>
        <Badge variant="secondary">Archived</Badge>
        <Badge variant="outline">Draft</Badge>
      </div>
      <div className="flex flex-wrap gap-2">
        <span className="text-sm font-medium">Message count badges:</span>
        <Badge>5 new</Badge>
        <Badge variant="secondary">12 total</Badge>
        <Badge variant="outline">No messages</Badge>
      </div>
      <div className="flex flex-wrap gap-2">
        <span className="text-sm font-medium">Time-based badges:</span>
        <Badge variant="default">Today</Badge>
        <Badge variant="secondary">Yesterday</Badge>
        <Badge variant="outline">Last week</Badge>
      </div>
    </div>
  ),
};
