import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react-vite";
import { Input } from "./input";

const meta: Meta<typeof Input> = {
  title: "Components/UI/Input",
  component: Input,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    type: {
      control: { type: "select" },
      options: ["text", "password", "email", "number", "search", "tel", "url", "date"],
      defaultValue: "text",
    },
    placeholder: {
      control: "text",
      defaultValue: "Type here...",
    },
    disabled: {
      control: "boolean",
      defaultValue: false,
    },
    value: {
      control: "text",
      defaultValue: "",
    },
    onChange: { action: "changed" },
    className: {
      control: "text",
    },
    // Include other relevant HTML input attributes as needed
  },
};

export default meta;
type Story = StoryObj<typeof Input>;

export const Default: Story = {
  args: {
    type: "text",
    placeholder: "Enter text...",
  },
};

export const EmailInput: Story = {
  args: {
    type: "email",
    placeholder: "<EMAIL>",
    name: "email", // Good practice for forms
  },
};

export const PasswordInput: Story = {
  args: {
    type: "password",
    placeholder: "Enter password",
    name: "password",
  },
};

export const NumberInput: Story = {
  args: {
    type: "number",
    placeholder: "Enter a number",
    name: "quantity",
  },
};

export const DisabledInput: Story = {
  args: {
    type: "text",
    placeholder: "Cannot type here",
    disabled: true,
    value: "Disabled value",
  },
};

export const WithDefaultValue: Story = {
  args: {
    type: "text",
    value: "This is a default value",
  },
};

export const CustomStyled: Story = {
  args: {
    type: "text",
    placeholder: "Custom style input",
    className: "border-green-500 focus:ring-green-500",
  },
};

// Example of controlled input story
// For actual control, you'd need to use Storybook's args and potentially a local state wrapper if needed.
// This story just sets an initial value.
export const ControlledValueStory: Story = {
  name: "Input with Value (State Managed by Storybook)",
  args: {
    type: "text",
    value: "Initial controlled text",
    placeholder: "Change me via controls",
    // onChange will be logged by action addon
  },
};
