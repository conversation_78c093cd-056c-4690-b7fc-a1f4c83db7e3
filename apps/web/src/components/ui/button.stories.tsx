import React from "react";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import { Button } from "./button"; // Assuming ButtonProps is exported, if not, define inline

const meta: Meta<typeof Button> = {
  title: "Components/UI/Button",
  component: Button,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: { type: "select" },
      options: ["default", "destructive", "outline", "secondary", "ghost", "link"],
    },
    size: {
      control: { type: "select" },
      options: ["default", "sm", "lg", "icon"],
    },
    asChild: {
      control: { type: "boolean" },
    },
    disabled: {
      control: { type: "boolean" },
    },
    onClick: { action: "clicked" },
    children: {
      control: { type: "text" },
      defaultValue: "Button Text",
    },
  },
};

export default meta;
type Story = StoryObj<typeof Button>;

export const Default: Story = {
  args: {
    variant: "default",
    size: "default",
    children: "Default Button",
  },
};

export const Destructive: Story = {
  args: {
    variant: "destructive",
    children: "Destructive Button",
  },
};

export const Outline: Story = {
  args: {
    variant: "outline",
    children: "Outline Button",
  },
};

export const Secondary: Story = {
  args: {
    variant: "secondary",
    children: "Secondary Button",
  },
};

export const Ghost: Story = {
  args: {
    variant: "ghost",
    children: "Ghost Button",
  },
};

export const Link: Story = {
  args: {
    variant: "link",
    children: "Link Button",
  },
};

export const Small: Story = {
  args: {
    size: "sm",
    children: "Small Button",
  },
};

export const Large: Story = {
  args: {
    size: "lg",
    children: "Large Button",
  },
};

export const Icon: Story = {
  args: {
    variant: "outline", // Icon often used with outline or ghost
    size: "icon",
    children: "⚙️", // Example icon, could be an SVG component
    "aria-label": "Settings", // Important for accessibility
  },
};

export const Disabled: Story = {
  args: {
    children: "Disabled Button",
    disabled: true,
  },
};

export const AsChild: Story = {
  name: "AsChild (renders child component)",
  args: {
    asChild: true,
    children: (
      <a
        href="https://storybook.js.org/"
        target="_blank"
        rel="noopener noreferrer"
        style={{
          display: "inline-block",
          padding: "10px 20px",
          backgroundColor: "#007bff",
          color: "white",
          borderRadius: "4px",
          textDecoration: "none",
        }}
      >
        Link via AsChild
      </a>
    ),
  },
  // Note: Styling the child 'a' tag directly for demonstration.
  // The button's own variant/size classes won't directly apply to the 'a' tag unless the 'a' tag itself is styled to mimic button styles or inherits them.
  // `asChild` is useful when you want the child to take over the role and functionality of the button, often for routing links.
};
