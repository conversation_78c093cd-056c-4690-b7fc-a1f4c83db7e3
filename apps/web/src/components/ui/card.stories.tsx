import React from "react";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "./card";
import { <PERSON><PERSON> } from "./button"; // Import Button for footer example

const meta: Meta<typeof Card> = {
  title: "Components/UI/Card",
  component: Card,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  // Subcomponents can be listed for better documentation, but args are primarily for Card
  subcomponents: {
    CardHeader,
    CardFooter,
    CardTitle,
    CardDescription,
    CardContent,
  },
  argTypes: {
    // Args for the main Card component itself, if any (e.g., className)
    className: {
      control: "text",
      description: "Custom CSS classes for the Card container.",
    },
    // Children are implicitly handled by Storybook
  },
};

export default meta;
type Story = StoryObj<typeof Card>;

export const Default: Story = {
  name: "Complete Card",
  args: {
    className: "w-[350px]", // Example width for the card
  },
  render: args => (
    <Card {...args}>
      <CardHeader>
        <CardTitle>Card Title</CardTitle>
        <CardDescription>This is a description of the card. It provides context.</CardDescription>
      </CardHeader>
      <CardContent>
        <p>This is the main content area of the card. You can put any React nodes here.</p>
        <p>For example, text, images, or other components.</p>
      </CardContent>
      <CardFooter>
        <Button variant="outline">Cancel</Button>
        <Button className="ml-2">Confirm</Button>
      </CardFooter>
    </Card>
  ),
};

export const WithHeaderAndContent: Story = {
  name: "Header and Content Only",
  args: {
    className: "w-[350px]",
  },
  render: args => (
    <Card {...args}>
      <CardHeader>
        <CardTitle>Another Card Title</CardTitle>
      </CardHeader>
      <CardContent>
        <p>This card only has a header and content. No footer or description in the header.</p>
      </CardContent>
    </Card>
  ),
};

export const SimpleContent: Story = {
  name: "Content Only",
  args: {
    className: "w-[350px]",
  },
  render: args => (
    <Card {...args}>
      <CardContent>
        <p>A simple card with just content. Useful for displaying basic information.</p>
      </CardContent>
    </Card>
  ),
};

export const WithCustomStyling: Story = {
  name: "Card with Custom Styling",
  args: {
    className: "w-[400px] border-blue-500 shadow-xl",
  },
  render: args => (
    <Card {...args}>
      <CardHeader className="bg-blue-50">
        <CardTitle className="text-blue-700">Custom Styled Title</CardTitle>
        <CardDescription className="text-blue-600">
          With a custom styled description.
        </CardDescription>
      </CardHeader>
      <CardContent className="p-8">
        <p>
          This card demonstrates applying custom Tailwind classes to Card and its subcomponents.
        </p>
      </CardContent>
      <CardFooter className="border-t border-blue-200 pt-4">
        <Button variant="link">Learn More</Button>
      </CardFooter>
    </Card>
  ),
};
