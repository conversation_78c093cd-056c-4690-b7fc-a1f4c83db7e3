import React, { useEffect, useState } from "react";
import { getAssetPath } from "@lib/assetUtils";

interface CdnImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  loading?: "lazy" | "eager";
  onLoad?: () => void;
  onError?: () => void;
}

/**
 * CdnImage component for loading images with CDN awareness
 *
 * This component automatically prepends the CDN URL to image paths
 * when the application is running in production or staging environments.
 */
export function CdnImage({
  src,
  alt,
  className = "",
  width,
  height,
  loading = "lazy",
  onLoad,
  onError,
}: CdnImageProps) {
  const [imageSrc, setImageSrc] = useState<string>("");

  useEffect(() => {
    // Get the CDN-aware path for the image
    const cdnPath = getAssetPath(src);
    setImageSrc(cdnPath);
  }, [src]);

  return (
    <img
      src={imageSrc}
      alt={alt}
      className={className}
      width={width}
      height={height}
      loading={loading}
      onLoad={onLoad}
      onError={onError}
    />
  );
}

export default CdnImage;
