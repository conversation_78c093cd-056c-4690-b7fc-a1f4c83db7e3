import React from "react";
import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { EmptyConversationsState } from "../EmptyConversationsState";
import "@testing-library/jest-dom";

// Mock the UI components
jest.mock("../../ui/button", () => ({
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Button: ({ children, onClick, disabled, className, size, ...props }: any) => (
    <button
      className={className}
      onClick={onClick}
      disabled={disabled}
      data-testid="start-conversation-button"
      data-size={size}
      {...props}
    >
      {children}
    </button>
  ),
}));

jest.mock("../../ui/card", () => ({
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Card: ({ children, className }: any) => (
    <div className={className} data-testid="empty-state-card">
      {children}
    </div>
  ),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  CardContent: ({ children, className }: any) => (
    <div className={className} data-testid="card-content">
      {children}
    </div>
  ),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  CardHeader: ({ children, className }: any) => (
    <div className={className} data-testid="card-header">
      {children}
    </div>
  ),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  CardDescription: ({ children, className }: any) => (
    <p className={className} data-testid="card-description">
      {children}
    </p>
  ),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  CardTitle: ({ children, className }: any) => (
    <h2 className={className} data-testid="card-title">
      {children}
    </h2>
  ),
}));

// Mock lucide-react icons
jest.mock("lucide-react", () => ({
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  MessageCircle: ({ className }: any) => (
    <span className={className} data-testid="message-circle-icon">
      💬
    </span>
  ),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Sparkles: ({ className }: any) => (
    <span className={className} data-testid="sparkles-icon">
      ✨
    </span>
  ),
}));

const defaultProps = {
  onStartConversation: jest.fn(),
};

describe("EmptyConversationsState", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Rendering", () => {
    it("renders the empty state card", () => {
      render(<EmptyConversationsState {...defaultProps} />);

      expect(screen.getByTestId("empty-state-card")).toBeInTheDocument();
    });

    it("displays the main title", () => {
      render(<EmptyConversationsState {...defaultProps} />);

      expect(screen.getByText("Start Your First Conversation")).toBeInTheDocument();
    });

    it("displays the description text", () => {
      render(<EmptyConversationsState {...defaultProps} />);

      expect(
        screen.getByText(
          "Connect with your personal assistant and begin chatting. Get help, ask questions, or just have a friendly conversation.",
        ),
      ).toBeInTheDocument();
    });

    it("displays the helper text", () => {
      render(<EmptyConversationsState {...defaultProps} />);

      expect(
        screen.getByText("Your assistant is ready to help with any questions or tasks you have."),
      ).toBeInTheDocument();
    });

    it("renders the message circle icon", () => {
      render(<EmptyConversationsState {...defaultProps} />);

      expect(screen.getByTestId("message-circle-icon")).toBeInTheDocument();
    });

    it("renders the start conversation button", () => {
      render(<EmptyConversationsState {...defaultProps} />);

      expect(screen.getByTestId("start-conversation-button")).toBeInTheDocument();
      expect(screen.getByText("Start Conversation")).toBeInTheDocument();
    });

    it("renders sparkles icon in the button", () => {
      render(<EmptyConversationsState {...defaultProps} />);

      expect(screen.getByTestId("sparkles-icon")).toBeInTheDocument();
    });
  });

  describe("Button States", () => {
    it("renders button in normal state by default", () => {
      render(<EmptyConversationsState {...defaultProps} />);

      const button = screen.getByTestId("start-conversation-button");
      expect(button).not.toBeDisabled();
      expect(screen.getByText("Start Conversation")).toBeInTheDocument();
      expect(screen.getByTestId("sparkles-icon")).toBeInTheDocument();
    });

    it("renders button in loading state when loading is true", () => {
      render(<EmptyConversationsState {...defaultProps} loading={true} />);

      const button = screen.getByTestId("start-conversation-button");
      expect(button).toBeDisabled();
      expect(screen.getByText("Starting...")).toBeInTheDocument();
      expect(screen.queryByTestId("sparkles-icon")).not.toBeInTheDocument();
    });

    it("shows loading spinner when in loading state", () => {
      const { container: _container } = render(
        <EmptyConversationsState {...defaultProps} loading={true} />,
      );

      // Check for loading spinner classes
      expect(_container.querySelector(".animate-spin")).toBeInTheDocument();
      expect(_container.querySelector(".rounded-full")).toBeInTheDocument();
      expect(_container.querySelector(".border-b-2")).toBeInTheDocument();
    });

    it("button has correct size attribute", () => {
      render(<EmptyConversationsState {...defaultProps} />);

      const button = screen.getByTestId("start-conversation-button");
      expect(button).toHaveAttribute("data-size", "lg");
    });
  });

  describe("Event Handling", () => {
    it("calls onStartConversation when button is clicked", async () => {
      const user = userEvent.setup();
      render(<EmptyConversationsState {...defaultProps} />);

      const button = screen.getByTestId("start-conversation-button");
      await user.click(button);

      expect(defaultProps.onStartConversation).toHaveBeenCalledTimes(1);
    });

    it("does not call onStartConversation when button is disabled (loading)", async () => {
      const user = userEvent.setup();
      render(<EmptyConversationsState {...defaultProps} loading={true} />);

      const button = screen.getByTestId("start-conversation-button");
      expect(button).toBeDisabled();

      // Attempt to click disabled button
      await user.click(button);

      expect(defaultProps.onStartConversation).not.toHaveBeenCalled();
    });

    it("handles multiple rapid clicks properly", async () => {
      const user = userEvent.setup();
      render(<EmptyConversationsState {...defaultProps} />);

      const button = screen.getByTestId("start-conversation-button");

      // Rapidly click the button multiple times
      await user.click(button);
      await user.click(button);
      await user.click(button);

      expect(defaultProps.onStartConversation).toHaveBeenCalledTimes(3);
    });
  });

  describe("Styling and Layout", () => {
    it("applies correct card styling classes", () => {
      render(<EmptyConversationsState {...defaultProps} />);

      const card = screen.getByTestId("empty-state-card");
      expect(card).toHaveClass("w-full", "max-w-md", "mx-auto");
    });

    it("centers the card header content", () => {
      render(<EmptyConversationsState {...defaultProps} />);

      const cardHeader = screen.getByTestId("card-header");
      expect(cardHeader).toHaveClass("text-center");
    });

    it("centers the card content", () => {
      render(<EmptyConversationsState {...defaultProps} />);

      const cardContent = screen.getByTestId("card-content");
      expect(cardContent).toHaveClass("text-center");
    });

    it("applies correct styling to the icon container", () => {
      const { container: _container } = render(<EmptyConversationsState {...defaultProps} />);

      // Look for the icon container with specific classes
      const iconContainer = _container.querySelector(".rounded-full.bg-primary\\/10");
      expect(iconContainer).toBeInTheDocument();
      expect(iconContainer).toHaveClass("h-16", "w-16", "items-center", "justify-center");
    });

    it("applies correct button styling", () => {
      render(<EmptyConversationsState {...defaultProps} />);

      const button = screen.getByTestId("start-conversation-button");
      expect(button).toHaveClass("w-full");
    });
  });

  describe("Accessibility", () => {
    it("has proper heading hierarchy", () => {
      render(<EmptyConversationsState {...defaultProps} />);

      const title = screen.getByTestId("card-title");
      expect(title.tagName.toLowerCase()).toBe("h2");
      expect(title).toHaveClass("text-xl");
    });

    it("has descriptive text for screen readers", () => {
      render(<EmptyConversationsState {...defaultProps} />);

      const description = screen.getByTestId("card-description");
      expect(description.tagName.toLowerCase()).toBe("p");
      expect(description).toHaveClass("text-base");
    });

    it("button is focusable when not disabled", () => {
      render(<EmptyConversationsState {...defaultProps} />);

      const button = screen.getByTestId("start-conversation-button");
      expect(button).not.toHaveAttribute("disabled");

      button.focus();
      expect(document.activeElement).toBe(button);
    });

    it("button is not focusable when disabled", () => {
      render(<EmptyConversationsState {...defaultProps} loading={true} />);

      const button = screen.getByTestId("start-conversation-button");
      expect(button).toBeDisabled();
    });

    it("provides clear visual hierarchy", () => {
      render(<EmptyConversationsState {...defaultProps} />);

      const title = screen.getByTestId("card-title");
      const description = screen.getByTestId("card-description");

      expect(title).toHaveClass("text-xl");
      expect(description).toHaveClass("text-base");
    });
  });

  describe("Icon Rendering", () => {
    it("renders message circle icon with correct styling", () => {
      render(<EmptyConversationsState {...defaultProps} />);

      const messageIcon = screen.getByTestId("message-circle-icon");
      expect(messageIcon).toHaveClass("h-8", "w-8", "text-primary");
    });

    it("renders sparkles icon in normal state", () => {
      render(<EmptyConversationsState {...defaultProps} />);

      const sparklesIcon = screen.getByTestId("sparkles-icon");
      expect(sparklesIcon).toBeInTheDocument();
      expect(sparklesIcon).toHaveClass("mr-2", "h-4", "w-4");
    });

    it("does not render sparkles icon in loading state", () => {
      render(<EmptyConversationsState {...defaultProps} loading={true} />);

      expect(screen.queryByTestId("sparkles-icon")).not.toBeInTheDocument();
    });
  });

  describe("Props Handling", () => {
    it("defaults loading to false when not provided", () => {
      const propsWithoutLoading = {
        onStartConversation: jest.fn(),
      };

      render(<EmptyConversationsState {...propsWithoutLoading} />);

      const button = screen.getByTestId("start-conversation-button");
      expect(button).not.toBeDisabled();
      expect(screen.getByText("Start Conversation")).toBeInTheDocument();
    });

    it("handles loading prop correctly", () => {
      render(<EmptyConversationsState {...defaultProps} loading={false} />);

      const button = screen.getByTestId("start-conversation-button");
      expect(button).not.toBeDisabled();
      expect(screen.getByText("Start Conversation")).toBeInTheDocument();
    });

    it("accepts and calls custom onStartConversation function", async () => {
      const customHandler = jest.fn();
      const user = userEvent.setup();

      render(<EmptyConversationsState onStartConversation={customHandler} />);

      const button = screen.getByTestId("start-conversation-button");
      await user.click(button);

      expect(customHandler).toHaveBeenCalledTimes(1);
    });
  });

  describe("Edge Cases", () => {
    it("handles rapid state changes between loading and normal", () => {
      const { rerender } = render(<EmptyConversationsState {...defaultProps} loading={false} />);

      expect(screen.getByText("Start Conversation")).toBeInTheDocument();
      expect(screen.getByTestId("start-conversation-button")).not.toBeDisabled();

      rerender(<EmptyConversationsState {...defaultProps} loading={true} />);

      expect(screen.getByText("Starting...")).toBeInTheDocument();
      expect(screen.getByTestId("start-conversation-button")).toBeDisabled();

      rerender(<EmptyConversationsState {...defaultProps} loading={false} />);

      expect(screen.getByText("Start Conversation")).toBeInTheDocument();
      expect(screen.getByTestId("start-conversation-button")).not.toBeDisabled();
    });

    it("maintains component stability during prop changes", () => {
      const { rerender } = render(<EmptyConversationsState {...defaultProps} />);

      const initialCard = screen.getByTestId("empty-state-card");
      const initialTitle = screen.getByText("Start Your First Conversation");

      rerender(<EmptyConversationsState {...defaultProps} loading={true} />);

      // Core elements should remain stable
      expect(screen.getByTestId("empty-state-card")).toBe(initialCard);
      expect(screen.getByText("Start Your First Conversation")).toBe(initialTitle);
    });

    it("handles undefined onStartConversation gracefully", () => {
      const propsWithUndefinedHandler = {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onStartConversation: undefined as any,
      };

      expect(() => {
        render(<EmptyConversationsState {...propsWithUndefinedHandler} />);
      }).not.toThrow();

      expect(screen.getByTestId("start-conversation-button")).toBeInTheDocument();
    });
  });

  describe("Loading State Animation", () => {
    it("includes proper loading animation classes", () => {
      const { container: _container } = render(
        <EmptyConversationsState {...defaultProps} loading={true} />,
      );

      const spinner = _container.querySelector(".animate-spin");
      expect(spinner).toBeInTheDocument();
      expect(spinner).toHaveClass(
        "rounded-full",
        "h-4",
        "w-4",
        "border-b-2",
        "border-white",
        "mr-2",
      );
    });

    it("shows different content in loading vs normal state", () => {
      const { rerender } = render(<EmptyConversationsState {...defaultProps} loading={false} />);

      expect(screen.getByText("Start Conversation")).toBeInTheDocument();
      expect(screen.queryByText("Starting...")).not.toBeInTheDocument();

      rerender(<EmptyConversationsState {...defaultProps} loading={true} />);

      expect(screen.queryByText("Start Conversation")).not.toBeInTheDocument();
      expect(screen.getByText("Starting...")).toBeInTheDocument();
    });
  });
});
