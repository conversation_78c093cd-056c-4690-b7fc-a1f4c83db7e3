import React from "react";
import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { ConversationList } from "../ConversationList";
import type { ConversationListItem } from "../../../types/conversation";
import "@testing-library/jest-dom";

// Mock the ConversationListItem component
jest.mock("../ConversationListItem", () => ({
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ConversationListItem: ({ conversation, onClick, onDelete, onArchive }: any) => (
    <div data-testid={`conversation-item-${conversation.id}`}>
      <span data-testid="conversation-title">{conversation.title}</span>
      <span data-testid="message-count">{conversation.messageCount}</span>
      <button data-testid="click-conversation" onClick={() => onClick(conversation.id)}>
        Open Conversation
      </button>
      {onDelete && (
        <button data-testid="delete-conversation" onClick={() => onDelete(conversation.id)}>
          Delete
        </button>
      )}
      {onArchive && (
        <button data-testid="archive-conversation" onClick={() => onArchive(conversation.id)}>
          Archive
        </button>
      )}
    </div>
  ),
}));

const mockConversations: ConversationListItem[] = [
  {
    id: "conv-1",
    title: "First Conversation",
    messageCount: 5,
    createdAt: "2024-01-15T10:00:00Z",
    lastMessageAt: "2024-01-15T10:30:00Z",
    status: "active",
    lastMessage: {
      content: "Hello, how can I help you?",
      timestamp: "2024-01-15T10:30:00Z",
      senderRole: "agent",
    },
  },
  {
    id: "conv-2",
    title: "Project Planning Discussion",
    messageCount: 12,
    createdAt: "2024-01-14T14:00:00Z",
    lastMessageAt: "2024-01-14T16:45:00Z",
    status: "active",
    lastMessage: {
      content: "Thanks for the help with planning!",
      timestamp: "2024-01-14T16:45:00Z",
      senderRole: "user",
    },
  },
  {
    id: "conv-3",
    title: "Archived Chat",
    messageCount: 3,
    createdAt: "2024-01-10T09:00:00Z",
    lastMessageAt: "2024-01-10T09:15:00Z",
    status: "archived",
    lastMessage: {
      content: "This conversation is archived",
      timestamp: "2024-01-10T09:15:00Z",
      senderRole: "agent",
    },
  },
];

const defaultProps = {
  conversations: mockConversations,
  onConversationClick: jest.fn(),
  onDeleteConversation: jest.fn(),
  onArchiveConversation: jest.fn(),
};

describe("ConversationList", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Rendering", () => {
    it("renders all conversations", () => {
      render(<ConversationList {...defaultProps} />);

      expect(screen.getByTestId("conversation-item-conv-1")).toBeInTheDocument();
      expect(screen.getByTestId("conversation-item-conv-2")).toBeInTheDocument();
      expect(screen.getByTestId("conversation-item-conv-3")).toBeInTheDocument();
    });

    it("renders conversation titles correctly", () => {
      render(<ConversationList {...defaultProps} />);

      expect(screen.getByText("First Conversation")).toBeInTheDocument();
      expect(screen.getByText("Project Planning Discussion")).toBeInTheDocument();
      expect(screen.getByText("Archived Chat")).toBeInTheDocument();
    });

    it("passes conversation data to ConversationListItem", () => {
      render(<ConversationList {...defaultProps} />);

      // Check that message counts are displayed (passed through to items)
      expect(screen.getByText("5")).toBeInTheDocument(); // conv-1 message count
      expect(screen.getByText("12")).toBeInTheDocument(); // conv-2 message count
      expect(screen.getByText("3")).toBeInTheDocument(); // conv-3 message count
    });

    it("renders empty list when no conversations provided", () => {
      render(<ConversationList {...defaultProps} conversations={[]} />);

      expect(screen.queryByTestId(/conversation-item-/)).not.toBeInTheDocument();
    });

    it("applies correct container styling", () => {
      const { container } = render(<ConversationList {...defaultProps} />);

      expect(container.firstChild).toHaveClass("space-y-4");
    });
  });

  describe("Loading State", () => {
    it("renders loading skeleton when loading is true", () => {
      render(<ConversationList {...defaultProps} loading={true} />);

      // Should render skeleton items instead of actual conversations
      expect(screen.queryByTestId(/conversation-item-/)).not.toBeInTheDocument();
      expect(screen.queryByText("First Conversation")).not.toBeInTheDocument();

      // Check for loading skeleton elements
      const skeletons = screen.getAllByText("", { selector: ".animate-pulse" });
      expect(skeletons.length).toBeGreaterThan(0);
    });

    it("renders 3 skeleton items during loading", () => {
      const { container } = render(<ConversationList {...defaultProps} loading={true} />);

      const skeletonItems = container.querySelectorAll(".animate-pulse");
      expect(skeletonItems).toHaveLength(3);
    });

    it("skeleton items have correct structure", () => {
      const { container } = render(<ConversationList {...defaultProps} loading={true} />);

      const skeletonItems = container.querySelectorAll(".animate-pulse");
      skeletonItems.forEach(skeleton => {
        expect(skeleton).toHaveClass("rounded-lg", "border", "bg-card", "p-4");
      });
    });

    it("does not render actual conversations when loading", () => {
      render(<ConversationList {...defaultProps} loading={true} />);

      expect(screen.queryByText("First Conversation")).not.toBeInTheDocument();
      expect(screen.queryByText("Project Planning Discussion")).not.toBeInTheDocument();
      expect(screen.queryByTestId("click-conversation")).not.toBeInTheDocument();
    });
  });

  describe("Event Handling", () => {
    it("calls onConversationClick when conversation is clicked", async () => {
      const user = userEvent.setup();
      render(<ConversationList {...defaultProps} />);

      const [conversationButton] = screen.getAllByTestId("click-conversation");
      await user.click(conversationButton);

      expect(defaultProps.onConversationClick).toHaveBeenCalledWith("conv-1");
      expect(defaultProps.onConversationClick).toHaveBeenCalledTimes(1);
    });

    it("calls onDeleteConversation when delete is triggered", async () => {
      const user = userEvent.setup();
      render(<ConversationList {...defaultProps} />);

      const [deleteButton] = screen.getAllByTestId("delete-conversation");
      await user.click(deleteButton);

      expect(defaultProps.onDeleteConversation).toHaveBeenCalledWith("conv-1");
      expect(defaultProps.onDeleteConversation).toHaveBeenCalledTimes(1);
    });

    it("calls onArchiveConversation when archive is triggered", async () => {
      const user = userEvent.setup();
      render(<ConversationList {...defaultProps} />);

      const [archiveButton] = screen.getAllByTestId("archive-conversation");
      await user.click(archiveButton);

      expect(defaultProps.onArchiveConversation).toHaveBeenCalledWith("conv-1");
      expect(defaultProps.onArchiveConversation).toHaveBeenCalledTimes(1);
    });

    it("handles multiple conversation clicks correctly", async () => {
      const user = userEvent.setup();
      render(<ConversationList {...defaultProps} />);

      const conversationButtons = screen.getAllByTestId("click-conversation");

      await user.click(conversationButtons[0]); // conv-1
      await user.click(conversationButtons[1]); // conv-2

      expect(defaultProps.onConversationClick).toHaveBeenCalledTimes(2);
      expect(defaultProps.onConversationClick).toHaveBeenNthCalledWith(1, "conv-1");
      expect(defaultProps.onConversationClick).toHaveBeenNthCalledWith(2, "conv-2");
    });
  });

  describe("Optional Props", () => {
    it("renders without onDeleteConversation", () => {
      const propsWithoutDelete = {
        ...defaultProps,
        onDeleteConversation: undefined,
      };

      render(<ConversationList {...propsWithoutDelete} />);

      expect(screen.queryByTestId("delete-conversation")).not.toBeInTheDocument();
      expect(screen.getByTestId("conversation-item-conv-1")).toBeInTheDocument();
    });

    it("renders without onArchiveConversation", () => {
      const propsWithoutArchive = {
        ...defaultProps,
        onArchiveConversation: undefined,
      };

      render(<ConversationList {...propsWithoutArchive} />);

      expect(screen.queryByTestId("archive-conversation")).not.toBeInTheDocument();
      expect(screen.getByTestId("conversation-item-conv-1")).toBeInTheDocument();
    });

    it("renders with only required props", () => {
      const minimalProps = {
        conversations: mockConversations,
        onConversationClick: jest.fn(),
      };

      render(<ConversationList {...minimalProps} />);

      expect(screen.getByTestId("conversation-item-conv-1")).toBeInTheDocument();
      expect(screen.queryByTestId("delete-conversation")).not.toBeInTheDocument();
      expect(screen.queryByTestId("archive-conversation")).not.toBeInTheDocument();
    });

    it("defaults loading to false", () => {
      const propsWithoutLoading = {
        conversations: mockConversations,
        onConversationClick: jest.fn(),
      };

      render(<ConversationList {...propsWithoutLoading} />);

      // Should render conversations, not loading state
      expect(screen.getByTestId("conversation-item-conv-1")).toBeInTheDocument();
      expect(screen.queryByText("", { selector: ".animate-pulse" })).not.toBeInTheDocument();
    });
  });

  describe("Data Handling", () => {
    it("handles conversations with different statuses", () => {
      const conversationsWithStatuses: ConversationListItem[] = [
        { ...mockConversations[0], status: "active" },
        { ...mockConversations[1], status: "archived" },
        { ...mockConversations[2], status: "draft" },
      ];

      render(<ConversationList {...defaultProps} conversations={conversationsWithStatuses} />);

      // All conversations should be rendered regardless of status
      expect(screen.getByTestId("conversation-item-conv-1")).toBeInTheDocument();
      expect(screen.getByTestId("conversation-item-conv-2")).toBeInTheDocument();
      expect(screen.getByTestId("conversation-item-conv-3")).toBeInTheDocument();
    });

    it("handles conversations without optional fields", () => {
      const minimalConversations: ConversationListItem[] = [
        {
          id: "minimal-conv",
          title: "Minimal Conversation",
          messageCount: 0,
          createdAt: "2024-01-15T10:00:00Z",
          status: "active",
        },
      ];

      render(<ConversationList {...defaultProps} conversations={minimalConversations} />);

      expect(screen.getByTestId("conversation-item-minimal-conv")).toBeInTheDocument();
      expect(screen.getByText("Minimal Conversation")).toBeInTheDocument();
    });

    it("handles large number of conversations", () => {
      const manyConversations: ConversationListItem[] = Array.from({ length: 50 }, (_, index) => ({
        id: `conv-${index}`,
        title: `Conversation ${index + 1}`,
        messageCount: index + 1,
        createdAt: "2024-01-15T10:00:00Z",
        status: "active" as const,
      }));

      render(<ConversationList {...defaultProps} conversations={manyConversations} />);

      // Should render all conversations
      expect(screen.getByTestId("conversation-item-conv-0")).toBeInTheDocument();
      expect(screen.getByTestId("conversation-item-conv-49")).toBeInTheDocument();
      expect(screen.getByText("Conversation 1")).toBeInTheDocument();
      expect(screen.getByText("Conversation 50")).toBeInTheDocument();
    });
  });

  describe("Performance", () => {
    it("renders efficiently with many conversations", () => {
      const startTime = performance.now();

      const manyConversations: ConversationListItem[] = Array.from({ length: 100 }, (_, index) => ({
        id: `perf-conv-${index}`,
        title: `Performance Test Conversation ${index + 1}`,
        messageCount: index + 1,
        createdAt: "2024-01-15T10:00:00Z",
        status: "active" as const,
      }));

      render(<ConversationList {...defaultProps} conversations={manyConversations} />);

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Should render in reasonable time (adjust threshold as needed)
      expect(renderTime).toBeLessThan(1000); // 1 second threshold
      expect(screen.getByTestId("conversation-item-perf-conv-0")).toBeInTheDocument();
    });
  });

  describe("Edge Cases", () => {
    it("handles conversations with empty titles", () => {
      const conversationWithEmptyTitle: ConversationListItem[] = [
        {
          id: "empty-title",
          title: "",
          messageCount: 1,
          createdAt: "2024-01-15T10:00:00Z",
          status: "active",
        },
      ];

      render(<ConversationList {...defaultProps} conversations={conversationWithEmptyTitle} />);

      expect(screen.getByTestId("conversation-item-empty-title")).toBeInTheDocument();
    });

    it("handles conversations with special characters in titles", () => {
      const conversationWithSpecialChars: ConversationListItem[] = [
        {
          id: "special-chars",
          title: "Special chars: @#$%^&*()_+-=[]{}|;':\",./<>? 🚀 測試",
          messageCount: 1,
          createdAt: "2024-01-15T10:00:00Z",
          status: "active",
        },
      ];

      render(<ConversationList {...defaultProps} conversations={conversationWithSpecialChars} />);

      expect(
        screen.getByText("Special chars: @#$%^&*()_+-=[]{}|;':\",./<>? 🚀 測試"),
      ).toBeInTheDocument();
    });

    it("handles conversations with very long titles", () => {
      const longTitle = "A".repeat(200);
      const conversationWithLongTitle: ConversationListItem[] = [
        {
          id: "long-title",
          title: longTitle,
          messageCount: 1,
          createdAt: "2024-01-15T10:00:00Z",
          status: "active",
        },
      ];

      render(<ConversationList {...defaultProps} conversations={conversationWithLongTitle} />);

      expect(screen.getByText(longTitle)).toBeInTheDocument();
    });
  });
});
