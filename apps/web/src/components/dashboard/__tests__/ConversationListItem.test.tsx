import React from "react";
import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import { fireEvent, render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { ConversationListItem } from "../ConversationListItem";
import type { ConversationListItem as ConversationData } from "../../../types/conversation";
import "@testing-library/jest-dom";

// Mock the UI components
jest.mock("../../ui/card", () => ({
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Card: ({ children, className, onClick }: any) => (
    <div className={className} onClick={onClick} data-testid="conversation-card">
      {children}
    </div>
  ),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  CardContent: ({ children, className }: any) => (
    <div className={className} data-testid="card-content">
      {children}
    </div>
  ),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  CardHeader: ({ children, className }: any) => (
    <div className={className} data-testid="card-header">
      {children}
    </div>
  ),
}));

jest.mock("../../ui/badge", () => ({
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Badge: ({ children, variant, className }: any) => (
    <span className={className} data-testid={`badge-${variant}`}>
      {children}
    </span>
  ),
}));

jest.mock("../../ui/button", () => ({
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Button: ({ children, onClick, className, ...props }: any) => (
    <button className={className} onClick={onClick} {...props} data-testid="action-button">
      {children}
    </button>
  ),
}));

// Mock lucide-react icons
jest.mock("lucide-react", () => ({
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Clock: ({ className }: any) => (
    <span className={className} data-testid="clock-icon">
      ⏰
    </span>
  ),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  MessageCircle: ({ className }: any) => (
    <span className={className} data-testid="message-icon">
      💬
    </span>
  ),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  MoreVertical: ({ className }: any) => (
    <span className={className} data-testid="more-icon">
      ⋮
    </span>
  ),
}));

const mockConversation: ConversationData = {
  id: "test-conversation-1",
  title: "Test Conversation",
  messageCount: 5,
  createdAt: "2024-01-15T10:00:00Z",
  lastMessageAt: "2024-01-15T14:30:00Z",
  status: "active",
  lastMessage: {
    content: "This is the last message in the conversation",
    timestamp: "2024-01-15T14:30:00Z",
    senderRole: "user",
  },
};

const defaultProps = {
  conversation: mockConversation,
  onClick: jest.fn(),
  onDelete: jest.fn(),
  onArchive: jest.fn(),
};

describe("ConversationListItem", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock Date.now() to ensure consistent timestamp testing
    jest.spyOn(Date, "now").mockReturnValue(new Date("2024-01-15T15:00:00Z").getTime());
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe("Rendering", () => {
    it("renders conversation title", () => {
      render(<ConversationListItem {...defaultProps} />);

      expect(screen.getByText("Test Conversation")).toBeInTheDocument();
    });

    it("displays message count badge when greater than 0", () => {
      render(<ConversationListItem {...defaultProps} />);

      expect(screen.getByText("5")).toBeInTheDocument();
      expect(screen.getByTestId("badge-secondary")).toBeInTheDocument();
    });

    it("does not display message count badge when count is 0", () => {
      const conversationWithNoMessages = {
        ...mockConversation,
        messageCount: 0,
      };

      render(<ConversationListItem {...defaultProps} conversation={conversationWithNoMessages} />);

      expect(screen.queryByText("0")).not.toBeInTheDocument();
      expect(screen.getByTestId("badge-default")).toBeInTheDocument(); // Status badge still shows
    });

    it("displays status badge with correct variant", () => {
      render(<ConversationListItem {...defaultProps} />);

      expect(screen.getByText("active")).toBeInTheDocument();
      expect(screen.getByTestId("badge-default")).toBeInTheDocument();
    });

    it("displays last message content and sender", () => {
      render(<ConversationListItem {...defaultProps} />);

      expect(
        screen.getByText(/You: This is the last message in the conversation/),
      ).toBeInTheDocument();
    });

    it("renders message and clock icons", () => {
      render(<ConversationListItem {...defaultProps} />);

      expect(screen.getAllByTestId("message-icon")).toHaveLength(2); // Header and footer
      expect(screen.getByTestId("clock-icon")).toBeInTheDocument();
    });

    it("renders action button with more icon", () => {
      render(<ConversationListItem {...defaultProps} />);

      expect(screen.getByTestId("action-button")).toBeInTheDocument();
      expect(screen.getByTestId("more-icon")).toBeInTheDocument();
    });
  });

  describe("Status Badge Variants", () => {
    it("shows correct variant for active status", () => {
      const activeConversation = { ...mockConversation, status: "active" as const };
      render(<ConversationListItem {...defaultProps} conversation={activeConversation} />);

      expect(screen.getByTestId("badge-default")).toBeInTheDocument();
    });

    it("shows correct variant for archived status", () => {
      const archivedConversation = { ...mockConversation, status: "archived" as const };
      render(<ConversationListItem {...defaultProps} conversation={archivedConversation} />);

      // Should have both message count badge (secondary) and status badge (secondary)
      const badges = screen.getAllByTestId("badge-secondary");
      expect(badges.length).toBeGreaterThan(0);
    });

    it("shows correct variant for draft status", () => {
      const draftConversation = { ...mockConversation, status: "draft" as const };
      render(<ConversationListItem {...defaultProps} conversation={draftConversation} />);

      expect(screen.getByTestId("badge-outline")).toBeInTheDocument();
    });

    it("defaults to outline variant for unknown status", () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const unknownStatusConversation = { ...mockConversation, status: "unknown" as any };
      render(<ConversationListItem {...defaultProps} conversation={unknownStatusConversation} />);

      expect(screen.getByTestId("badge-outline")).toBeInTheDocument();
    });
  });

  describe("Timestamp Formatting", () => {
    it("displays relative time format", () => {
      render(<ConversationListItem {...defaultProps} />);

      // Should show some form of time formatting (specific format depends on current time)
      const timeRegex = /\d+\/\d+\/\d+|\d+h ago|Yesterday|Just now/;
      expect(screen.getByText(timeRegex)).toBeInTheDocument();
    });

    it("shows formatted date for older messages", () => {
      const oldConversation = {
        ...mockConversation,
        lastMessageAt: "2024-01-10T15:00:00Z", // 5 days ago
      };

      render(<ConversationListItem {...defaultProps} conversation={oldConversation} />);

      // Should show formatted date (format may vary by locale)
      const dateRegex = /\d+\/\d+\/\d+/;
      expect(screen.getByText(dateRegex)).toBeInTheDocument();
    });

    it("handles invalid timestamps gracefully", () => {
      const invalidTimestampConversation = {
        ...mockConversation,
        lastMessageAt: "invalid-date",
      };

      expect(() => {
        render(
          <ConversationListItem {...defaultProps} conversation={invalidTimestampConversation} />,
        );
      }).not.toThrow();
    });
  });

  describe("Last Message Display", () => {
    it("shows user message with 'You:' prefix", () => {
      const userMessageConversation = {
        ...mockConversation,
        lastMessage: {
          content: "User's message content",
          timestamp: "2024-01-15T14:30:00Z",
          senderRole: "user" as const,
        },
      };

      render(<ConversationListItem {...defaultProps} conversation={userMessageConversation} />);

      expect(screen.getByText(/You: User's message content/)).toBeInTheDocument();
    });

    it("shows agent message with 'Assistant:' prefix", () => {
      const agentMessageConversation = {
        ...mockConversation,
        lastMessage: {
          content: "Agent's helpful response",
          timestamp: "2024-01-15T14:30:00Z",
          senderRole: "agent" as const,
        },
      };

      render(<ConversationListItem {...defaultProps} conversation={agentMessageConversation} />);

      expect(screen.getByText(/Assistant: Agent's helpful response/)).toBeInTheDocument();
    });

    it("handles conversation without last message", () => {
      const noLastMessageConversation = {
        ...mockConversation,
        lastMessage: undefined,
      };

      render(<ConversationListItem {...defaultProps} conversation={noLastMessageConversation} />);

      expect(screen.queryByText(/You:/)).not.toBeInTheDocument();
      expect(screen.queryByText(/Assistant:/)).not.toBeInTheDocument();
      // Should still render other parts of the conversation
      expect(screen.getByText("Test Conversation")).toBeInTheDocument();
    });

    it("truncates long message content", () => {
      const longMessageConversation = {
        ...mockConversation,
        lastMessage: {
          content: "A".repeat(200), // Very long message
          timestamp: "2024-01-15T14:30:00Z",
          senderRole: "user" as const,
        },
      };

      const { container } = render(
        <ConversationListItem {...defaultProps} conversation={longMessageConversation} />,
      );

      expect(container.querySelector(".truncate")).toBeInTheDocument();
    });
  });

  describe("Event Handling", () => {
    it("calls onClick when conversation card is clicked", async () => {
      const user = userEvent.setup();
      render(<ConversationListItem {...defaultProps} />);

      await user.click(screen.getByTestId("conversation-card"));

      expect(defaultProps.onClick).toHaveBeenCalledWith("test-conversation-1");
      expect(defaultProps.onClick).toHaveBeenCalledTimes(1);
    });

    it("prevents card click when action button is clicked", async () => {
      const user = userEvent.setup();
      render(<ConversationListItem {...defaultProps} />);

      const actionButton = screen.getByTestId("action-button");
      await user.click(actionButton);

      // onClick should not be called when action button is clicked
      expect(defaultProps.onClick).not.toHaveBeenCalled();
    });

    it("stops propagation when action button is clicked", () => {
      render(<ConversationListItem {...defaultProps} />);

      const actionButton = screen.getByTestId("action-button");
      const stopPropagationSpy = jest.spyOn(Event.prototype, "stopPropagation");

      fireEvent.click(actionButton);

      expect(stopPropagationSpy).toHaveBeenCalled();

      stopPropagationSpy.mockRestore();
    });
  });

  describe("Accessibility", () => {
    it("has appropriate cursor styling for clickable card", () => {
      const { container } = render(<ConversationListItem {...defaultProps} />);

      expect(container.querySelector(".cursor-pointer")).toBeInTheDocument();
    });

    it("has hover effects for better interaction feedback", () => {
      const { container } = render(<ConversationListItem {...defaultProps} />);

      expect(container.querySelector(".hover\\:shadow-md")).toBeInTheDocument();
      expect(container.querySelector(".hover\\:border-primary\\/20")).toBeInTheDocument();
    });

    it("has transition effects for smooth interactions", () => {
      const { container } = render(<ConversationListItem {...defaultProps} />);

      expect(container.querySelector(".transition-all")).toBeInTheDocument();
    });
  });

  describe("Optional Props", () => {
    it("renders without onDelete prop", () => {
      const propsWithoutDelete = {
        conversation: mockConversation,
        onClick: jest.fn(),
        onArchive: jest.fn(),
      };

      expect(() => {
        render(<ConversationListItem {...propsWithoutDelete} />);
      }).not.toThrow();

      expect(screen.getByText("Test Conversation")).toBeInTheDocument();
    });

    it("renders without onArchive prop", () => {
      const propsWithoutArchive = {
        conversation: mockConversation,
        onClick: jest.fn(),
        onDelete: jest.fn(),
      };

      expect(() => {
        render(<ConversationListItem {...propsWithoutArchive} />);
      }).not.toThrow();

      expect(screen.getByText("Test Conversation")).toBeInTheDocument();
    });

    it("renders with minimal required props", () => {
      const minimalProps = {
        conversation: mockConversation,
        onClick: jest.fn(),
      };

      expect(() => {
        render(<ConversationListItem {...minimalProps} />);
      }).not.toThrow();

      expect(screen.getByText("Test Conversation")).toBeInTheDocument();
    });
  });

  describe("Edge Cases", () => {
    it("handles conversation with empty title", () => {
      const emptyTitleConversation = {
        ...mockConversation,
        title: "",
      };

      render(<ConversationListItem {...defaultProps} conversation={emptyTitleConversation} />);

      // Should render without crashing
      expect(screen.getByTestId("conversation-card")).toBeInTheDocument();
    });

    it("handles conversation with special characters in title", () => {
      const specialCharConversation = {
        ...mockConversation,
        title: "Special chars: @#$%^&*()_+-=[]{}|;':\",./<>? 🚀 測試",
      };

      render(<ConversationListItem {...defaultProps} conversation={specialCharConversation} />);

      expect(
        screen.getByText("Special chars: @#$%^&*()_+-=[]{}|;':\",./<>? 🚀 測試"),
      ).toBeInTheDocument();
    });

    it("handles negative message count", () => {
      const negativeCountConversation = {
        ...mockConversation,
        messageCount: -1,
      };

      render(<ConversationListItem {...defaultProps} conversation={negativeCountConversation} />);

      // Should not show negative count badge
      expect(screen.queryByText("-1")).not.toBeInTheDocument();
    });

    it("handles very large message count", () => {
      const largeCountConversation = {
        ...mockConversation,
        messageCount: 999999,
      };

      render(<ConversationListItem {...defaultProps} conversation={largeCountConversation} />);

      expect(screen.getByText("999999")).toBeInTheDocument();
    });

    it("handles conversation without lastMessageAt", () => {
      const noLastMessageAtConversation = {
        ...mockConversation,
        lastMessageAt: undefined,
      };

      render(<ConversationListItem {...defaultProps} conversation={noLastMessageAtConversation} />);

      // Should not crash and should not show timestamp
      expect(screen.queryByTestId("clock-icon")).not.toBeInTheDocument();
      expect(screen.getByText("Test Conversation")).toBeInTheDocument();
    });

    it("handles very long titles with truncation", () => {
      const longTitleConversation = {
        ...mockConversation,
        title: "A".repeat(200),
      };

      const { container } = render(
        <ConversationListItem {...defaultProps} conversation={longTitleConversation} />,
      );

      expect(container.querySelector(".truncate")).toBeInTheDocument();
      expect(screen.getByText("A".repeat(200))).toBeInTheDocument();
    });
  });

  describe("Message Count Display", () => {
    it("shows correct message count text", () => {
      render(<ConversationListItem {...defaultProps} />);

      expect(screen.getByText("5 messages")).toBeInTheDocument();
    });

    it("handles singular message count", () => {
      const singleMessageConversation = {
        ...mockConversation,
        messageCount: 1,
      };

      render(<ConversationListItem {...defaultProps} conversation={singleMessageConversation} />);

      expect(screen.getByText("1 messages")).toBeInTheDocument(); // Component uses plural form
    });

    it("handles zero message count", () => {
      const zeroMessageConversation = {
        ...mockConversation,
        messageCount: 0,
      };

      render(<ConversationListItem {...defaultProps} conversation={zeroMessageConversation} />);

      expect(screen.getByText("0 messages")).toBeInTheDocument();
    });
  });
});
