# Dashboard Components

This directory contains the dashboard components that implement conditional rendering based on the user's conversation state.

## Components

### EmptyConversationsState

A call-to-action component displayed when users have no conversations.

**Props:**

- `onStartConversation: () => void` - Callback when user clicks to start a conversation
- `loading?: boolean` - Whether the start conversation action is in progress

**Features:**

- Engaging copy explaining the benefit of starting a conversation
- Prominent call-to-action button
- Loading state with spinner
- Uses ShadCN UI components (Card, Button)
- Responsive design

### ConversationListItem

Individual conversation card component for displaying conversation metadata.

**Props:**

- `conversation: ConversationListItem` - Conversation data object
- `onClick: (conversationId: string) => void` - Callback when conversation is clicked
- `onDelete?: (conversationId: string) => void` - Optional delete callback
- `onArchive?: (conversationId: string) => void` - Optional archive callback

**Features:**

- Displays conversation title, status, and metadata
- Shows last message preview with sender indication
- Message count and timestamp formatting
- Status badges (active, archived, draft)
- Hover effects and click handling
- More actions button (placeholder for future dropdown)

### ConversationList

Container component for displaying a list of conversations.

**Props:**

- `conversations: ConversationListItem[]` - Array of conversation data
- `onConversationClick: (conversationId: string) => void` - Conversation click handler
- `onDeleteConversation?: (conversationId: string) => void` - Optional delete handler
- `onArchiveConversation?: (conversationId: string) => void` - Optional archive handler
- `loading?: boolean` - Loading state

**Features:**

- Renders list of ConversationListItem components
- Loading skeleton with animated placeholders
- Responsive grid layout
- Handles empty states gracefully

## Types

### ConversationListItem

```typescript
interface ConversationListItem {
  id: string;
  title: string;
  lastMessage?: {
    content: string;
    timestamp: string;
    senderRole: "user" | "agent";
  };
  messageCount: number;
  createdAt: string;
  lastMessageAt?: string;
  status: "active" | "archived" | "draft";
}
```

## Hooks

### useConversations

Custom hook for fetching and managing conversation data.

**Options:**

- `skip?: boolean` - Skip the query execution

**Returns:**

- `conversations: ConversationListItem[]` - Array of conversations
- `hasConversations: boolean` - Whether user has any conversations
- `loading: boolean` - Loading state
- `error: ApolloError | undefined` - Error state
- `refetch: () => void` - Function to refetch data

## Usage

### Basic Implementation

```tsx
import { useConversations } from "../../hooks/useConversations";
import { EmptyConversationsState } from "./EmptyConversationsState";
import { ConversationList } from "./ConversationList";

function Dashboard() {
  const { conversations, hasConversations, loading, error } = useConversations();

  const handleStartConversation = () => {
    navigate("/pa/chat");
  };

  const handleConversationClick = (conversationId: string) => {
    navigate(`/pa/chat/${conversationId}`);
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      {hasConversations ? (
        <ConversationList
          conversations={conversations}
          onConversationClick={handleConversationClick}
        />
      ) : (
        <EmptyConversationsState onStartConversation={handleStartConversation} />
      )}
    </div>
  );
}
```

## Storybook Stories

All components include comprehensive Storybook stories. See the [Complete Storybook Guide](../../../../docs/storybook.md) for setup and usage.

- **EmptyConversationsState.stories.tsx** - Default, loading, and interactive states
- **ConversationListItem.stories.tsx** - All prop variations, different data scenarios
- **ConversationList.stories.tsx** - Loading, empty, single, and multiple conversations

## Styling

Components use:

- ShadCN UI design system
- Tailwind CSS for styling
- Lucide React for icons
- Consistent spacing and typography
- Responsive design patterns
- Hover and focus states

## Testing

Run component tests:

```bash
bun test
```

View in Storybook:

```bash
bun run dev:storybook
```
