import React from "react";
import { ConversationListItem } from "./ConversationListItem";
import type { ConversationListItem as ConversationData } from "../../types/conversation";

interface ConversationListProps {
  conversations: ConversationData[];
  onConversationClick: (conversationId: string) => void;
  onDeleteConversation?: (conversationId: string) => void;
  onArchiveConversation?: (conversationId: string) => void;
  loading?: boolean;
}

export const ConversationList: React.FC<ConversationListProps> = ({
  conversations,
  onConversationClick,
  onDeleteConversation,
  onArchiveConversation,
  loading = false,
}) => {
  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, index) => (
          <div key={index} className="animate-pulse rounded-lg border bg-card p-4">
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-2">
                <div className="h-4 w-4 bg-muted rounded" />
                <div className="h-4 w-32 bg-muted rounded" />
              </div>
              <div className="h-5 w-16 bg-muted rounded-full" />
            </div>
            <div className="space-y-2">
              <div className="h-3 w-full bg-muted rounded" />
              <div className="flex justify-between">
                <div className="h-3 w-20 bg-muted rounded" />
                <div className="h-3 w-16 bg-muted rounded" />
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {conversations.map(conversation => (
        <ConversationListItem
          key={conversation.id}
          conversation={conversation}
          onClick={onConversationClick}
          onDelete={onDeleteConversation}
          onArchive={onArchiveConversation}
        />
      ))}
    </div>
  );
};
