import React from "react";
import { <PERSON>, CardContent, CardHeader } from "../ui/card";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import type { ConversationListItem as ConversationData } from "../../types/conversation";
import { Clock, MessageCircle, MoreVertical } from "lucide-react";

interface ConversationListItemProps {
  conversation: ConversationData;
  onClick: (conversationId: string) => void;
  onDelete?: (conversationId: string) => void;
  onArchive?: (conversationId: string) => void;
}

export const ConversationListItem: React.FC<ConversationListItemProps> = ({
  conversation,
  onClick,
  onDelete: _onDelete,
  onArchive: _onArchive,
}) => {
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return "Just now";
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else if (diffInHours < 48) {
      return "Yesterday";
    } else {
      return date.toLocaleDateString();
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "active":
        return "default";
      case "archived":
        return "secondary";
      case "draft":
        return "outline";
      default:
        return "outline";
    }
  };

  return (
    <Card
      className="cursor-pointer transition-all hover:shadow-md hover:border-primary/20"
      onClick={() => onClick(conversation.id)}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            <MessageCircle className="h-4 w-4 text-muted-foreground" />
            <h3 className="font-semibold text-sm truncate">{conversation.title}</h3>
          </div>
          <div className="flex items-center gap-2">
            {conversation.messageCount > 0 && (
              <Badge variant="secondary" className="text-xs">
                {conversation.messageCount}
              </Badge>
            )}
            <Badge variant={getStatusBadgeVariant(conversation.status)}>
              {conversation.status}
            </Badge>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={e => {
                e.stopPropagation();
                // TODO: Implement dropdown menu for actions
              }}
            >
              <MoreVertical className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2">
          {conversation.lastMessage && (
            <div className="text-sm text-muted-foreground">
              <p className="truncate">
                {conversation.lastMessage.senderRole === "user" ? "You: " : "Assistant: "}
                {conversation.lastMessage.content}
              </p>
            </div>
          )}

          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-4">
              <span className="flex items-center gap-1">
                <MessageCircle className="h-3 w-3" />
                {conversation.messageCount} messages
              </span>
              {conversation.lastMessageAt && (
                <span className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {formatTimestamp(conversation.lastMessageAt)}
                </span>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
