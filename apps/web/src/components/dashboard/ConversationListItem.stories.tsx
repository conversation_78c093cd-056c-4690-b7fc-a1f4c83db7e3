import React from "react";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import { ConversationListItem } from "./ConversationListItem";
import { ConversationListItem as ConversationData } from "../../types/conversation";

const meta: Meta<typeof ConversationListItem> = {
  title: "Components/Dashboard/ConversationListItem",
  component: ConversationListItem,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    onClick: { action: "conversation-clicked" },
    onDelete: { action: "conversation-deleted" },
    onArchive: { action: "conversation-archived" },
  },
};

export default meta;
type Story = StoryObj<typeof ConversationListItem>;

const mockConversation: ConversationData = {
  id: "conv-1",
  title: "Chat with Personal Assistant",
  messageCount: 12,
  createdAt: "2024-01-15T10:30:00Z",
  lastMessageAt: "2024-01-15T14:45:00Z",
  status: "active",
  lastMessage: {
    content: "Thank you for your help with the project planning!",
    timestamp: "2024-01-15T14:45:00Z",
    senderRole: "user",
  },
};

export const Default: Story = {
  args: {
    conversation: mockConversation,
  },
};

export const WithAssistantMessage: Story = {
  args: {
    conversation: {
      ...mockConversation,
      lastMessage: {
        content: "I'm here to help you with any questions you might have.",
        timestamp: "2024-01-15T14:45:00Z",
        senderRole: "agent",
      },
    },
  },
};

export const ArchivedConversation: Story = {
  args: {
    conversation: {
      ...mockConversation,
      status: "archived",
      title: "Archived Project Discussion",
    },
  },
};

export const DraftConversation: Story = {
  args: {
    conversation: {
      ...mockConversation,
      status: "draft",
      title: "Draft Conversation",
      messageCount: 0,
      lastMessage: undefined,
    },
  },
};

export const NoMessages: Story = {
  args: {
    conversation: {
      ...mockConversation,
      messageCount: 0,
      lastMessage: undefined,
      title: "New Conversation",
    },
  },
};

export const LongTitle: Story = {
  args: {
    conversation: {
      ...mockConversation,
      title:
        "This is a very long conversation title that should be truncated properly to fit within the card layout",
    },
  },
};

export const RecentActivity: Story = {
  args: {
    conversation: {
      ...mockConversation,
      lastMessageAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
      lastMessage: {
        content: "Just sent a few minutes ago",
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        senderRole: "user",
      },
    },
  },
};

export const MessageCountVariations: Story = {
  name: "Message Count Variations",
  render: () => (
    <div className="space-y-4 w-full max-w-md">
      <ConversationListItem
        conversation={{
          ...mockConversation,
          messageCount: 0,
          title: "No Messages",
          lastMessage: undefined,
        }}
        onClick={() => {}}
      />
      <ConversationListItem
        conversation={{ ...mockConversation, messageCount: 5, title: "Few Messages" }}
        onClick={() => {}}
      />
      <ConversationListItem
        conversation={{ ...mockConversation, messageCount: 42, title: "Many Messages" }}
        onClick={() => {}}
      />
      <ConversationListItem
        conversation={{ ...mockConversation, messageCount: 999, title: "Lots of Messages" }}
        onClick={() => {}}
      />
    </div>
  ),
};

export const AllStates: Story = {
  name: "All States",
  render: () => (
    <div className="space-y-4 w-full max-w-md">
      <ConversationListItem
        conversation={{ ...mockConversation, status: "active" }}
        onClick={() => {}}
      />
      <ConversationListItem
        conversation={{ ...mockConversation, status: "archived", title: "Archived Chat" }}
        onClick={() => {}}
      />
      <ConversationListItem
        conversation={{
          ...mockConversation,
          status: "draft",
          title: "Draft Chat",
          messageCount: 0,
          lastMessage: undefined,
        }}
        onClick={() => {}}
      />
    </div>
  ),
};
