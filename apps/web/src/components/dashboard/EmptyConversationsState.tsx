import React from "react";
import { <PERSON><PERSON> } from "../ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { MessageCircle, Sparkles } from "lucide-react";

interface EmptyConversationsStateProps {
  onStartConversation: () => void;
  loading?: boolean;
}

export const EmptyConversationsState: React.FC<EmptyConversationsStateProps> = ({
  onStartConversation,
  loading = false,
}) => {
  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
          <MessageCircle className="h-8 w-8 text-primary" />
        </div>
        <CardTitle className="text-xl">Start Your First Conversation</CardTitle>
        <CardDescription className="text-base">
          Connect with your personal assistant and begin chatting. Get help, ask questions, or just
          have a friendly conversation.
        </CardDescription>
      </CardHeader>
      <CardContent className="text-center">
        <Button onClick={onStartConversation} disabled={loading} className="w-full" size="lg">
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              Starting...
            </>
          ) : (
            <>
              <Sparkles className="mr-2 h-4 w-4" />
              Start Conversation
            </>
          )}
        </Button>
        <p className="mt-4 text-sm text-muted-foreground">
          Your assistant is ready to help with any questions or tasks you have.
        </p>
      </CardContent>
    </Card>
  );
};
