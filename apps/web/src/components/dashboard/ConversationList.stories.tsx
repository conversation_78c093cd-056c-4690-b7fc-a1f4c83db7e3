import React from "react";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import { ConversationList } from "./ConversationList";
import { ConversationListItem as ConversationData } from "../../types/conversation";

const meta: Meta<typeof ConversationList> = {
  title: "Components/Dashboard/ConversationList",
  component: ConversationList,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    loading: {
      control: { type: "boolean" },
    },
    onConversationClick: { action: "conversation-clicked" },
    onDeleteConversation: { action: "conversation-deleted" },
    onArchiveConversation: { action: "conversation-archived" },
  },
};

export default meta;
type Story = StoryObj<typeof ConversationList>;

const mockConversations: ConversationData[] = [
  {
    id: "conv-1",
    title: "Project Planning Discussion",
    messageCount: 15,
    createdAt: "2024-01-15T10:30:00Z",
    lastMessageAt: "2024-01-15T14:45:00Z",
    status: "active",
    lastMessage: {
      content: "Thank you for your help with the project planning!",
      timestamp: "2024-01-15T14:45:00Z",
      senderRole: "user",
    },
  },
  {
    id: "conv-2",
    title: "Daily Standup Questions",
    messageCount: 8,
    createdAt: "2024-01-14T09:15:00Z",
    lastMessageAt: "2024-01-14T16:20:00Z",
    status: "active",
    lastMessage: {
      content: "I can help you prepare for tomorrow's standup meeting.",
      timestamp: "2024-01-14T16:20:00Z",
      senderRole: "agent",
    },
  },
  {
    id: "conv-3",
    title: "Code Review Assistance",
    messageCount: 23,
    createdAt: "2024-01-13T11:00:00Z",
    lastMessageAt: "2024-01-13T17:30:00Z",
    status: "archived",
    lastMessage: {
      content: "The code review is complete. Great work!",
      timestamp: "2024-01-13T17:30:00Z",
      senderRole: "agent",
    },
  },
  {
    id: "conv-4",
    title: "Draft Ideas",
    messageCount: 0,
    createdAt: "2024-01-16T08:00:00Z",
    status: "draft",
  },
];

export const Default: Story = {
  args: {
    conversations: mockConversations,
    loading: false,
  },
};

export const Loading: Story = {
  args: {
    conversations: [],
    loading: true,
  },
};

export const SingleConversation: Story = {
  args: {
    conversations: [mockConversations[0]],
    loading: false,
  },
};

export const EmptyList: Story = {
  args: {
    conversations: [],
    loading: false,
  },
};

export const ManyConversations: Story = {
  args: {
    conversations: [
      ...mockConversations,
      {
        id: "conv-5",
        title: "Bug Investigation",
        messageCount: 7,
        createdAt: "2024-01-12T13:45:00Z",
        lastMessageAt: "2024-01-12T15:20:00Z",
        status: "active",
        lastMessage: {
          content: "Found the root cause of the issue.",
          timestamp: "2024-01-12T15:20:00Z",
          senderRole: "user",
        },
      },
      {
        id: "conv-6",
        title: "Feature Requirements",
        messageCount: 12,
        createdAt: "2024-01-11T10:00:00Z",
        lastMessageAt: "2024-01-11T14:30:00Z",
        status: "archived",
        lastMessage: {
          content: "All requirements have been documented.",
          timestamp: "2024-01-11T14:30:00Z",
          senderRole: "agent",
        },
      },
    ],
    loading: false,
  },
};

export const InDashboardLayout: Story = {
  name: "In Dashboard Layout",
  args: {
    conversations: mockConversations,
    loading: false,
  },
  render: args => (
    <div className="min-h-screen bg-background">
      <div className="container py-8">
        <div className="max-w-2xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">Your Conversations</h1>
            <p className="text-muted-foreground">
              Continue your chats with your personal assistant
            </p>
          </div>
          <ConversationList {...args} />
        </div>
      </div>
    </div>
  ),
  parameters: {
    layout: "fullscreen",
  },
};
