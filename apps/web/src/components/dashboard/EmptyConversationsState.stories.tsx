import React from "react";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import { EmptyConversationsState } from "./EmptyConversationsState";

const meta: Meta<typeof EmptyConversationsState> = {
  title: "Components/Dashboard/EmptyConversationsState",
  component: EmptyConversationsState,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    loading: {
      control: { type: "boolean" },
    },
    onStartConversation: { action: "start-conversation" },
  },
};

export default meta;
type Story = StoryObj<typeof EmptyConversationsState>;

export const Default: Story = {
  args: {
    loading: false,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const Interactive: Story = {
  name: "Interactive Demo",
  args: {
    loading: false,
  },
  render: args => {
    return (
      <div className="w-full max-w-lg">
        <EmptyConversationsState {...args} />
      </div>
    );
  },
};

export const InDashboardContext: Story = {
  name: "In Dashboard Layout",
  args: {
    loading: false,
  },
  render: args => {
    return (
      <div className="min-h-screen bg-background">
        <div className="container py-8">
          <div className="max-w-2xl mx-auto">
            <div className="mb-8">
              <h1 className="text-3xl font-bold mb-2">Dashboard</h1>
              <p className="text-muted-foreground">Welcome to your personal assistant dashboard</p>
            </div>
            <EmptyConversationsState {...args} />
          </div>
        </div>
      </div>
    );
  },
  parameters: {
    layout: "fullscreen",
  },
};
