import React from "react";
import { afterEach, beforeEach, describe, expect, it, jest } from "@jest/globals";
import { fireEvent, render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import ErrorBoundary from "../ErrorBoundary";

// Suppress console.error for error boundary tests
const originalError = console.error;
beforeEach(() => {
  console.error = jest.fn();
});

afterEach(() => {
  console.error = originalError;
});

// Component that throws an error when shouldThrow is true
const TestComponent = ({
  shouldThrow = false,
  errorMessage = "Test error",
}: {
  shouldThrow?: boolean;
  errorMessage?: string;
}) => {
  if (shouldThrow) {
    throw new Error(errorMessage);
  }
  return <div data-testid="working-component">Component is working</div>;
};

describe("ErrorBoundary", () => {
  beforeEach(() => {
    // Mock window.location.reload for JSDOM
    Object.defineProperty(window, "location", {
      value: {
        ...window.location,
        reload: jest.fn(),
      },
      writable: true,
    });
  });

  describe("Normal Operation", () => {
    it("renders children when no error occurs", () => {
      render(
        <ErrorBoundary>
          <TestComponent />
        </ErrorBoundary>,
      );

      expect(screen.getByTestId("working-component")).toBeInTheDocument();
      expect(screen.getByText("Component is working")).toBeInTheDocument();
    });

    it("renders multiple children when no error occurs", () => {
      render(
        <ErrorBoundary>
          <div data-testid="child-1">Child 1</div>
          <div data-testid="child-2">Child 2</div>
        </ErrorBoundary>,
      );

      expect(screen.getByTestId("child-1")).toBeInTheDocument();
      expect(screen.getByTestId("child-2")).toBeInTheDocument();
    });
  });

  describe("Error Handling", () => {
    it("renders default error UI when child component throws", () => {
      render(
        <ErrorBoundary>
          <TestComponent shouldThrow={true} />
        </ErrorBoundary>,
      );

      expect(screen.getByText("Something went wrong")).toBeInTheDocument();
      expect(
        screen.getByText(/We encountered an error while loading the application/),
      ).toBeInTheDocument();
      expect(screen.getByRole("button", { name: "Refresh Page" })).toBeInTheDocument();
      expect(screen.getByRole("button", { name: "Try Again" })).toBeInTheDocument();
    });

    it("renders custom fallback when provided", () => {
      const customFallback = <div data-testid="custom-fallback">Custom Error UI</div>;

      render(
        <ErrorBoundary fallback={customFallback}>
          <TestComponent shouldThrow={true} />
        </ErrorBoundary>,
      );

      expect(screen.getByTestId("custom-fallback")).toBeInTheDocument();
      expect(screen.getByText("Custom Error UI")).toBeInTheDocument();
      expect(screen.queryByText("Something went wrong")).not.toBeInTheDocument();
    });

    it("logs error to console when error occurs", () => {
      render(
        <ErrorBoundary>
          <TestComponent shouldThrow={true} errorMessage="Console log test error" />
        </ErrorBoundary>,
      );

      expect(console.error).toHaveBeenCalledWith(
        "ErrorBoundary caught an error:",
        expect.any(Error),
        expect.any(Object),
      );
    });
  });

  describe("Apollo Error Detection", () => {
    it("detects and logs Apollo GraphQL errors", () => {
      const apolloErrorMessage = "Invariant Violation: Apollo client error - apollo.dev/react";

      render(
        <ErrorBoundary>
          <TestComponent shouldThrow={true} errorMessage={apolloErrorMessage} />
        </ErrorBoundary>,
      );

      expect(console.error).toHaveBeenCalledWith(
        "Apollo GraphQL error detected:",
        expect.objectContaining({
          message: apolloErrorMessage,
        }),
      );
    });

    it("detects apollo.dev references in error messages", () => {
      const apolloErrorMessage = "Error from apollo.dev testing utilities";

      render(
        <ErrorBoundary>
          <TestComponent shouldThrow={true} errorMessage={apolloErrorMessage} />
        </ErrorBoundary>,
      );

      expect(console.error).toHaveBeenCalledWith(
        "Apollo GraphQL error detected:",
        expect.objectContaining({
          message: apolloErrorMessage,
        }),
      );
    });
  });

  describe("Development Mode Features", () => {
    const originalNodeEnv = process.env.NODE_ENV;

    afterEach(() => {
      process.env.NODE_ENV = originalNodeEnv;
    });

    it("shows error details in development mode", () => {
      process.env.NODE_ENV = "development";

      render(
        <ErrorBoundary>
          <TestComponent shouldThrow={true} errorMessage="Development error details" />
        </ErrorBoundary>,
      );

      expect(screen.getByText("Error Details (Development)")).toBeInTheDocument();
    });

    it("hides error details in production mode", () => {
      process.env.NODE_ENV = "production";

      render(
        <ErrorBoundary>
          <TestComponent shouldThrow={true} errorMessage="Production error" />
        </ErrorBoundary>,
      );

      expect(screen.queryByText("Error Details (Development)")).not.toBeInTheDocument();
    });
  });

  describe("User Interactions", () => {
    it("refresh page button exists and is clickable", () => {
      render(
        <ErrorBoundary>
          <TestComponent shouldThrow={true} />
        </ErrorBoundary>,
      );

      const refreshButton = screen.getByRole("button", { name: "Refresh Page" });
      expect(refreshButton).toBeInTheDocument();

      // Button should be clickable (no error thrown)
      fireEvent.click(refreshButton);
    });

    it("try again button resets error state", () => {
      render(
        <ErrorBoundary>
          <TestComponent shouldThrow={true} />
        </ErrorBoundary>,
      );

      // Initially should show error UI
      expect(screen.getByText("Something went wrong")).toBeInTheDocument();

      const tryAgainButton = screen.getByRole("button", { name: "Try Again" });
      fireEvent.click(tryAgainButton);

      // The error boundary should reset its state, but since our component still throws,
      // it will show the error again. This tests the reset mechanism.
      expect(screen.getByText("Something went wrong")).toBeInTheDocument();
    });
  });

  describe("Accessibility", () => {
    it("has proper button roles for error state", () => {
      render(
        <ErrorBoundary>
          <TestComponent shouldThrow={true} />
        </ErrorBoundary>,
      );

      const refreshButton = screen.getByRole("button", { name: "Refresh Page" });
      const tryAgainButton = screen.getByRole("button", { name: "Try Again" });

      expect(refreshButton).toBeInTheDocument();
      expect(tryAgainButton).toBeInTheDocument();
    });

    it("has focus styles for keyboard navigation", () => {
      render(
        <ErrorBoundary>
          <TestComponent shouldThrow={true} />
        </ErrorBoundary>,
      );

      const refreshButton = screen.getByRole("button", { name: "Refresh Page" });
      const tryAgainButton = screen.getByRole("button", { name: "Try Again" });

      expect(refreshButton).toHaveClass("focus:outline-none");
      expect(tryAgainButton).toHaveClass("focus:outline-none");
    });
  });

  describe("Component State Management", () => {
    it("maintains separate error states for multiple ErrorBoundary instances", () => {
      render(
        <div>
          <ErrorBoundary>
            <TestComponent shouldThrow={true} />
          </ErrorBoundary>
          <ErrorBoundary>
            <TestComponent shouldThrow={false} />
          </ErrorBoundary>
        </div>,
      );

      // First boundary shows error
      expect(screen.getByText("Something went wrong")).toBeInTheDocument();

      // Second boundary shows working component
      expect(screen.getByTestId("working-component")).toBeInTheDocument();
    });
  });
});
