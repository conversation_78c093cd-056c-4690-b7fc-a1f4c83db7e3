import { act, fireEvent, render, screen, waitFor } from "@testing-library/react";
import { useMutation } from "@apollo/client";
import { useAuth } from "@clerk/clerk-react";
import CliTokenManager from "../CliTokenManager";

// Mock Apollo Client
jest.mock("@apollo/client", () => ({
  useMutation: jest.fn(),
}));

// Mock Clerk
jest.mock("@clerk/clerk-react", () => ({
  useAuth: jest.fn(),
}));

// Mock navigator.clipboard
Object.assign(navigator, {
  clipboard: {
    writeText: jest.fn(),
  },
});

describe("CliTokenManager", () => {
  const mockCreateCliToken = jest.fn();
  const mockGetToken = jest.fn();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let mutationOptions: any = {};

  const defaultAuthState = {
    isSignedIn: true,
    getToken: mockGetToken,
  };

  const defaultMutationState = {
    loading: false,
  };

  beforeEach(() => {
    (useAuth as jest.Mock).mockReturnValue(defaultAuthState);
    (useMutation as jest.Mock).mockImplementation((mutation, options) => {
      mutationOptions = options || {};
      return [mockCreateCliToken, defaultMutationState];
    });
    mockGetToken.mockResolvedValue("valid-token");
    (navigator.clipboard.writeText as jest.Mock).mockResolvedValue(undefined);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("Initial Render", () => {
    it("renders the CLI token manager correctly", () => {
      render(<CliTokenManager />);

      expect(screen.getByText("CLI Token Management")).toBeInTheDocument();
      expect(screen.getByText("Create New Token")).toBeInTheDocument();
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
    });

    it("does not show modal or generated token initially", () => {
      render(<CliTokenManager />);

      expect(screen.queryByText("Create New CLI Token")).not.toBeInTheDocument();
      expect(screen.queryByText("New Token Generated:")).not.toBeInTheDocument();
    });
  });

  describe("Modal Management", () => {
    it("opens modal when create button is clicked", () => {
      render(<CliTokenManager />);

      const createButton = screen.getByText("Create New Token");
      fireEvent.click(createButton);

      expect(screen.getByText("Create New CLI Token")).toBeInTheDocument();
      expect(screen.getByLabelText("Description:")).toBeInTheDocument();
      expect(screen.getByPlaceholderText("e.g., My development machine token")).toBeInTheDocument();
      expect(screen.getByText("Cancel")).toBeInTheDocument();
      expect(screen.getByText("Create Token")).toBeInTheDocument();
    });

    it("closes modal when cancel button is clicked", () => {
      render(<CliTokenManager />);

      // Open modal
      fireEvent.click(screen.getByText("Create New Token"));
      expect(screen.getByText("Create New CLI Token")).toBeInTheDocument();

      // Close modal
      fireEvent.click(screen.getByText("Cancel"));
      expect(screen.queryByText("Create New CLI Token")).not.toBeInTheDocument();
    });

    it("clears form state when opening modal", () => {
      render(<CliTokenManager />);

      // Open modal and fill form
      fireEvent.click(screen.getByText("Create New Token"));
      const descriptionInput = screen.getByLabelText("Description:");
      fireEvent.change(descriptionInput, { target: { value: "test description" } });

      // Close and reopen modal
      fireEvent.click(screen.getByText("Cancel"));
      fireEvent.click(screen.getByText("Create New Token"));

      // Form should be cleared
      const newDescriptionInput = screen.getByLabelText("Description:");
      expect(newDescriptionInput).toHaveValue("");
    });
  });

  describe("Form Validation", () => {
    it("shows error for empty description", async () => {
      render(<CliTokenManager />);

      // Open modal and submit empty form
      fireEvent.click(screen.getByText("Create New Token"));

      // Find the form element and submit it directly
      const form = document.querySelector("form");
      expect(form).toBeTruthy();

      await act(async () => {
        fireEvent.submit(form!);
      });

      // The error should appear immediately since it's client-side validation
      expect(screen.getByText("Description cannot be empty.")).toBeInTheDocument();
      expect(mockCreateCliToken).not.toHaveBeenCalled();
    });

    it("shows error for whitespace-only description", async () => {
      render(<CliTokenManager />);

      // Open modal and submit form with whitespace
      fireEvent.click(screen.getByText("Create New Token"));
      const descriptionInput = screen.getByLabelText("Description:");
      fireEvent.change(descriptionInput, { target: { value: "   " } });

      await act(async () => {
        const submitButton = screen.getByText("Create Token");
        fireEvent.click(submitButton);
      });

      expect(screen.getByText("Description cannot be empty.")).toBeInTheDocument();
      expect(mockCreateCliToken).not.toHaveBeenCalled();
    });

    it("accepts valid description", async () => {
      render(<CliTokenManager />);

      // Open modal and submit valid form
      fireEvent.click(screen.getByText("Create New Token"));
      const descriptionInput = screen.getByLabelText("Description:");
      fireEvent.change(descriptionInput, { target: { value: "Valid description" } });

      await act(async () => {
        const submitButton = screen.getByText("Create Token");
        fireEvent.click(submitButton);
      });

      expect(screen.queryByText("Description cannot be empty.")).not.toBeInTheDocument();
      expect(mockCreateCliToken).toHaveBeenCalledWith({
        variables: { description: "Valid description" },
      });
    });
  });

  describe("Authentication Handling", () => {
    it("shows error when user is not signed in", async () => {
      (useAuth as jest.Mock).mockReturnValue({
        isSignedIn: false,
        getToken: mockGetToken,
      });

      render(<CliTokenManager />);

      // Open modal and submit form
      fireEvent.click(screen.getByText("Create New Token"));
      const descriptionInput = screen.getByLabelText("Description:");
      fireEvent.change(descriptionInput, { target: { value: "Test description" } });

      await act(async () => {
        const submitButton = screen.getByText("Create Token");
        fireEvent.click(submitButton);
      });

      expect(
        screen.getByText("You must be signed in to create tokens. Please sign in and try again."),
      ).toBeInTheDocument();
      expect(mockCreateCliToken).not.toHaveBeenCalled();
    });

    it("shows error when token retrieval fails", async () => {
      mockGetToken.mockResolvedValue(null);

      render(<CliTokenManager />);

      // Open modal and submit form
      fireEvent.click(screen.getByText("Create New Token"));
      const descriptionInput = screen.getByLabelText("Description:");
      fireEvent.change(descriptionInput, { target: { value: "Test description" } });

      await act(async () => {
        const submitButton = screen.getByText("Create Token");
        fireEvent.click(submitButton);
      });

      expect(
        screen.getByText("Unable to retrieve authentication token. Please sign in again."),
      ).toBeInTheDocument();
      expect(mockCreateCliToken).not.toHaveBeenCalled();
    });

    it("handles unexpected errors during token retrieval", async () => {
      mockGetToken.mockRejectedValue(new Error("Unexpected error"));

      render(<CliTokenManager />);

      // Open modal and submit form
      fireEvent.click(screen.getByText("Create New Token"));
      const descriptionInput = screen.getByLabelText("Description:");
      fireEvent.change(descriptionInput, { target: { value: "Test description" } });

      await act(async () => {
        const submitButton = screen.getByText("Create Token");
        fireEvent.click(submitButton);
      });

      expect(
        screen.getByText("An unexpected error occurred. Please try again later."),
      ).toBeInTheDocument();
      expect(mockCreateCliToken).not.toHaveBeenCalled();
    });
  });

  describe("Token Creation Success", () => {
    it("shows generated token after successful creation", async () => {
      const mockTokenData = {
        createCliToken: {
          token: "cli_token_12345",
          cliToken: {
            id: "token-id-1",
            tokenPrefix: "cli_",
            description: "Test token",
            createdAt: "2023-01-01T00:00:00Z",
          },
        },
      };

      render(<CliTokenManager />);

      // Simulate successful mutation callback
      await act(async () => {
        mutationOptions.onCompleted(mockTokenData);
      });

      expect(screen.getByText("New Token Generated:")).toBeInTheDocument();
      expect(
        screen.getByText(
          "Please copy this token. It will not be shown again for security reasons.",
        ),
      ).toBeInTheDocument();
      expect(screen.getByText("cli_token_12345")).toBeInTheDocument();
      expect(screen.getByText("Copy to Clipboard")).toBeInTheDocument();
    });

    it("resets form state after successful creation", async () => {
      const mockTokenData = {
        createCliToken: {
          token: "cli_token_12345",
          cliToken: {
            id: "token-id-1",
            tokenPrefix: "cli_",
            description: "Test token",
            createdAt: "2023-01-01T00:00:00Z",
          },
        },
      };

      render(<CliTokenManager />);

      // Simulate successful creation
      await act(async () => {
        mutationOptions.onCompleted(mockTokenData);
      });

      // Open modal again - should be clear
      fireEvent.click(screen.getByText("Create New Token"));
      const descriptionInput = screen.getByLabelText("Description:");
      expect(descriptionInput).toHaveValue("");
    });
  });

  describe("Clipboard Functionality", () => {
    it("copies token to clipboard successfully", async () => {
      const mockTokenData = {
        createCliToken: {
          token: "cli_token_12345",
          cliToken: {
            id: "token-id-1",
            tokenPrefix: "cli_",
            description: "Test token",
            createdAt: "2023-01-01T00:00:00Z",
          },
        },
      };

      render(<CliTokenManager />);

      // Simulate successful token creation
      await act(async () => {
        mutationOptions.onCompleted(mockTokenData);
      });

      // Click copy button
      const copyButton = screen.getByText("Copy to Clipboard");
      await act(async () => {
        fireEvent.click(copyButton);
      });

      expect(navigator.clipboard.writeText).toHaveBeenCalledWith("cli_token_12345");
      expect(screen.getByText("Copied!")).toBeInTheDocument();
    });

    it("shows copied state temporarily", async () => {
      jest.useFakeTimers();

      const mockTokenData = {
        createCliToken: {
          token: "cli_token_12345",
          cliToken: {
            id: "token-id-1",
            tokenPrefix: "cli_",
            description: "Test token",
            createdAt: "2023-01-01T00:00:00Z",
          },
        },
      };

      render(<CliTokenManager />);

      // Simulate successful token creation
      await act(async () => {
        mutationOptions.onCompleted(mockTokenData);
      });

      // Click copy button
      const copyButton = screen.getByText("Copy to Clipboard");
      await act(async () => {
        fireEvent.click(copyButton);
      });

      expect(screen.getByText("Copied!")).toBeInTheDocument();

      // Fast-forward time
      act(() => {
        jest.advanceTimersByTime(2000);
      });

      expect(screen.getByText("Copy to Clipboard")).toBeInTheDocument();
      expect(screen.queryByText("Copied!")).not.toBeInTheDocument();

      jest.useRealTimers();
    });

    it("handles clipboard write failure", async () => {
      (navigator.clipboard.writeText as jest.Mock).mockRejectedValue(new Error("Clipboard failed"));

      const mockTokenData = {
        createCliToken: {
          token: "cli_token_12345",
          cliToken: {
            id: "token-id-1",
            tokenPrefix: "cli_",
            description: "Test token",
            createdAt: "2023-01-01T00:00:00Z",
          },
        },
      };

      render(<CliTokenManager />);

      // Simulate successful token creation
      await act(async () => {
        mutationOptions.onCompleted(mockTokenData);
      });

      // Click copy button
      const copyButton = screen.getByText("Copy to Clipboard");
      await act(async () => {
        fireEvent.click(copyButton);
      });

      expect(screen.getByText("Failed to copy token to clipboard.")).toBeInTheDocument();
    });
  });

  describe("Error Handling", () => {
    it("handles authentication errors with special message", async () => {
      const authError = {
        message: "Authentication required for this operation",
        graphQLErrors: [],
      };

      render(<CliTokenManager />);

      // Open modal first to show errors
      fireEvent.click(screen.getByText("Create New Token"));

      // Simulate authentication error
      await act(async () => {
        mutationOptions.onError(authError);
      });

      await waitFor(() => {
        expect(
          screen.getByText(
            "Authentication error: Your session may have expired. Please refresh the page and try again.",
          ),
        ).toBeInTheDocument();
      });
      expect(mockGetToken).toHaveBeenCalledWith({ template: "a2a_backend" });
    });

    it("handles authentication errors when token is null", async () => {
      mockGetToken.mockResolvedValue(null);

      const authError = {
        message: "Authentication required for this operation",
        graphQLErrors: [],
      };

      render(<CliTokenManager />);

      // Open modal first to show errors
      fireEvent.click(screen.getByText("Create New Token"));

      // Simulate authentication error and wait for async operations
      await act(async () => {
        mutationOptions.onError(authError);
      });

      await waitFor(() => {
        expect(
          screen.getByText("You are not authenticated. Please sign in again."),
        ).toBeInTheDocument();
      });
    });

    it("handles GraphQL errors", async () => {
      const graphQLError = {
        message: "Server error",
        graphQLErrors: [{ message: "Invalid token description" }],
      };

      render(<CliTokenManager />);

      // Open modal first to show errors
      fireEvent.click(screen.getByText("Create New Token"));

      // Simulate GraphQL error
      await act(async () => {
        mutationOptions.onError(graphQLError);
      });

      await waitFor(() => {
        expect(screen.getByText("Invalid token description")).toBeInTheDocument();
      });
    });

    it("handles generic errors", async () => {
      const genericError = {
        message: "Network error",
        graphQLErrors: [],
      };

      render(<CliTokenManager />);

      // Open modal first to show errors
      fireEvent.click(screen.getByText("Create New Token"));

      // Simulate generic error
      await act(async () => {
        mutationOptions.onError(genericError);
      });

      await waitFor(() => {
        expect(screen.getByText("Network error")).toBeInTheDocument();
      });
    });

    it("handles fallback error message", async () => {
      const errorWithoutMessage = {
        message: "",
        graphQLErrors: [],
      };

      render(<CliTokenManager />);

      // Open modal first to show errors
      fireEvent.click(screen.getByText("Create New Token"));

      // Simulate error without meaningful message
      await act(async () => {
        mutationOptions.onError(errorWithoutMessage);
      });

      await waitFor(() => {
        expect(screen.getByText("Failed to create token. Please try again.")).toBeInTheDocument();
      });
    });
  });

  describe("Loading States", () => {
    it("shows loading state during token creation", () => {
      (useMutation as jest.Mock).mockReturnValue([mockCreateCliToken, { loading: true }]);

      render(<CliTokenManager />);

      // Open modal
      fireEvent.click(screen.getByText("Create New Token"));

      expect(screen.getByText("Creating...")).toBeInTheDocument();
      expect(screen.getByText("Creating...")).toBeDisabled();
    });

    it("disables form buttons during loading", () => {
      (useMutation as jest.Mock).mockReturnValue([mockCreateCliToken, { loading: true }]);

      render(<CliTokenManager />);

      // Open modal
      fireEvent.click(screen.getByText("Create New Token"));

      expect(screen.getByText("Cancel")).toBeDisabled();
      expect(screen.getByText("Creating...")).toBeDisabled();
    });
  });

  describe("State Management", () => {
    it("clears previous token when opening modal", async () => {
      const mockTokenData = {
        createCliToken: {
          token: "cli_token_12345",
          cliToken: {
            id: "token-id-1",
            tokenPrefix: "cli_",
            description: "Test token",
            createdAt: "2023-01-01T00:00:00Z",
          },
        },
      };

      render(<CliTokenManager />);

      // Simulate token creation
      await act(async () => {
        mutationOptions.onCompleted(mockTokenData);
      });

      expect(screen.getByText("cli_token_12345")).toBeInTheDocument();

      // Open modal again
      fireEvent.click(screen.getByText("Create New Token"));

      // Previous token should not be visible in modal context
      expect(screen.queryByText("New Token Generated:")).not.toBeInTheDocument();
      expect(screen.getByText("Create New CLI Token")).toBeInTheDocument();
    });

    it("resets error state when opening modal", async () => {
      render(<CliTokenManager />);

      // Simulate error
      await act(async () => {
        mutationOptions.onError({ message: "Test error", graphQLErrors: [] });
      });

      // Open modal
      fireEvent.click(screen.getByText("Create New Token"));

      // Error should be cleared when opening modal
      expect(screen.queryByText("Test error")).not.toBeInTheDocument();
    });
  });
});
