import React from "react";
import type { Meta, StoryObj } from "@storybook/react-vite";
import ErrorBoundary from "./ErrorBoundary";

// Component that throws an error for testing
const ErrorThrowingComponent = ({ shouldThrow }: { shouldThrow: boolean }) => {
  if (shouldThrow) {
    throw new Error("Test error for ErrorBoundary story");
  }
  return <div className="p-4 bg-green-100 rounded">This component works fine!</div>;
};

// Component that throws an Apollo-style error
const ApolloErrorComponent = () => {
  throw new Error("Invariant Violation: Apollo GraphQL client error - apollo.dev/react/testing");
};

const meta: Meta<typeof ErrorBoundary> = {
  title: "Components/ErrorBoundary",
  component: ErrorBoundary,
  parameters: {
    layout: "fullscreen",
  },
  argTypes: {
    fallback: {
      control: { type: "object" },
      description: "Custom fallback UI to display when an error occurs",
    },
  },
};

export default meta;
type Story = StoryObj<typeof ErrorBoundary>;

export const Default: Story = {
  args: {
    children: <div className="p-4 bg-green-100 rounded">No error - children render normally</div>,
  },
};

export const WithError: Story = {
  args: {
    children: <ErrorThrowingComponent shouldThrow={true} />,
  },
  parameters: {
    docs: {
      description: {
        story: "Shows the default error UI when a child component throws an error",
      },
    },
  },
};

export const WithApolloError: Story = {
  args: {
    children: <ApolloErrorComponent />,
  },
  parameters: {
    docs: {
      description: {
        story: "Shows how Apollo GraphQL errors are handled with additional logging",
      },
    },
  },
};

export const WithCustomFallback: Story = {
  args: {
    children: <ErrorThrowingComponent shouldThrow={true} />,
    fallback: (
      <div className="min-h-screen flex items-center justify-center bg-purple-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-900 mb-4">Custom Error UI</h1>
          <p className="text-purple-700">This is a custom fallback component</p>
        </div>
      </div>
    ),
  },
  parameters: {
    docs: {
      description: {
        story: "Shows how to provide a custom fallback UI",
      },
    },
  },
};

export const NoError: Story = {
  args: {
    children: (
      <div className="p-8">
        <h2 className="text-xl font-semibold mb-4">Working Component</h2>
        <p className="text-gray-600">
          This story shows the ErrorBoundary when everything is working correctly. The children are
          rendered normally.
        </p>
        <div className="mt-4 p-4 bg-blue-100 rounded">
          <p>Child component content renders as expected</p>
        </div>
      </div>
    ),
  },
  parameters: {
    docs: {
      description: {
        story: "Shows normal operation when no errors occur",
      },
    },
  },
};
