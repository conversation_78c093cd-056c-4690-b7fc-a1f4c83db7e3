import React from "react";
import type { MessageContent, MessageMetadata } from "./types";

// Local implementation of cn utility function
const cn = (...classes: (string | boolean | undefined)[]) => classes.filter(Boolean).join(" ");

interface PAMessageProps {
  id: string;
  content: MessageContent;
  timestamp: string;
  metadata?: MessageMetadata;
}

export const PAMessage: React.FC<PAMessageProps> = ({ content, timestamp }) => {
  // Extract text content from structured format
  const getMessageText = (content: MessageContent): string => {
    // Handle structured content format: {"parts": [{"type": "text", "content": "..."}]}
    if (content.parts && Array.isArray(content.parts)) {
      const textParts = content.parts
        .filter((part: { type: string; content?: string }) => part.type === "text")
        .map((part: { type: string; content?: string }) => part.content || "")
        .join(" ");
      return textParts || "";
    }

    // Fallback to simple text format
    return content.text || "";
  };

  const messageText = getMessageText(content);

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  return (
    <div className="flex w-full mb-4 justify-start">
      <div className="flex items-start space-x-3 max-w-[70%]">
        {/* PA Avatar */}
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
          <span className="text-white text-sm font-medium">PA</span>
        </div>

        {/* Message Content */}
        <div
          className={cn(
            "rounded-lg px-4 py-2 shadow-sm",
            "bg-muted text-muted-foreground border border-border",
          )}
        >
          <div className="text-sm font-medium mb-1 text-blue-600">Personal Assistant</div>

          <div className="text-sm whitespace-pre-wrap break-words">{messageText}</div>

          <div className="text-xs opacity-70 mt-2">{formatTimestamp(timestamp)}</div>
        </div>
      </div>
    </div>
  );
};
