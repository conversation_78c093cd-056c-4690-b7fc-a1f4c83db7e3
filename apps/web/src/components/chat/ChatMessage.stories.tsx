import React from "react";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import { ChatMessage } from "./ChatMessage";

const meta: Meta<typeof ChatMessage> = {
  title: "Chat/ChatMessage",
  component: ChatMessage,
  parameters: {
    layout: "centered",
  },
  decorators: [
    Story => (
      <div className="w-[600px] p-4">
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof ChatMessage>;

export const UserMessage: Story = {
  args: {
    id: "user-message-1",
    content: { text: "Hello! Can you help me with something?" },
    senderRole: "user",
    timestamp: new Date().toISOString(),
  },
};

export const AssistantMessage: Story = {
  args: {
    id: "assistant-message-1",
    content: {
      text: "Of course! I'd be happy to help you. What do you need assistance with?",
    },
    senderRole: "agent",
    timestamp: new Date().toISOString(),
  },
};

export const LongMessage: Story = {
  args: {
    id: "long-message-1",
    content: {
      text: "This is a much longer message that demonstrates how the chat component handles text wrapping and longer content. It should wrap nicely within the message bubble and maintain good readability even with multiple lines of text.",
    },
    senderRole: "user",
    timestamp: new Date().toISOString(),
  },
};

export const MessageWithLineBreaks: Story = {
  args: {
    id: "multiline-message-1",
    content: {
      text: "This message has\nmultiple lines\n\nAnd even some empty lines between paragraphs.\n\nIt should preserve the formatting.",
    },
    senderRole: "agent",
    timestamp: new Date().toISOString(),
  },
};
