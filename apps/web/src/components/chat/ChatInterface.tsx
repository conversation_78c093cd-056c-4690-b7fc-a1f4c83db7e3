import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { MessageInput } from "./MessageInput";
import { ChatMessage } from "./ChatMessage";
import { Alert } from "../ui/alert";
import { type Message } from "./types";
import { useMessageSubscription } from "@hooks/useMessageSubscription";

interface ChatInterfaceProps {
  conversationId: string;
  assistantName?: string;
  messages?: Message[];
  onMessageSent?: (message: Message) => void;
  loading?: boolean;
  error?: string;
  height?: string;
  className?: string;
  showHeader?: boolean;
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  conversationId,
  assistantName = "Personal Assistant",
  messages = [],
  onMessageSent,
  loading = false,
  error,
  height = "h-[calc(100vh-200px)]", // Subtract space for page header and padding
  className = "",
  showHeader = true,
}) => {
  const [optimisticMessages, setOptimisticMessages] = useState<Message[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Combine props messages with optimistic messages, filtering out duplicates
  const allMessages = useMemo(() => {
    // Create a Set of message IDs from props messages for fast lookup
    const existingMessageIds = new Set(messages.map(msg => msg.id));

    // Filter optimistic messages to exclude ones that already exist in props messages
    const uniqueOptimisticMessages = optimisticMessages.filter(
      msg => !existingMessageIds.has(msg.id),
    );

    return [...messages, ...uniqueOptimisticMessages];
  }, [messages, optimisticMessages]);

  // Handle new messages from subscription
  const handleNewMessage = useCallback((newMessage: Message) => {
    console.log("Received new message from subscription:", newMessage);

    // Remove from optimistic messages if it was there (now confirmed by server)
    setOptimisticMessages(prev => {
      const filtered = prev.filter(msg => msg.id !== newMessage.id);
      if (filtered.length !== prev.length) {
        console.log(`Removed optimistic message ${newMessage.id} - now in server messages`);
      }
      return filtered;
    });

    // Note: The message should automatically appear in the UI through the Apollo cache update
    // in useMessageSubscription hook, which will trigger a re-render of useConversationMessages
  }, []);

  // Subscribe to real-time messages
  const { subscriptionError } = useMessageSubscription({
    conversationId,
    onNewMessage: handleNewMessage,
    enabled: !!conversationId,
  });

  // Clean up optimistic messages that now exist in server messages
  useEffect(() => {
    if (messages.length > 0 && optimisticMessages.length > 0) {
      const serverMessageIds = new Set(messages.map(msg => msg.id));
      const remainingOptimistic = optimisticMessages.filter(msg => !serverMessageIds.has(msg.id));

      if (remainingOptimistic.length !== optimisticMessages.length) {
        console.log("Cleaning up optimistic messages that are now in server messages");
        setOptimisticMessages(remainingOptimistic);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [messages]); // Only depend on messages, not optimisticMessages to avoid infinite loop

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [allMessages]);

  const handleMessageSent = (newMessage: Message) => {
    // Add to optimistic messages for immediate UI feedback
    setOptimisticMessages(prev => [...prev, newMessage]);
    onMessageSent?.(newMessage);
  };

  return (
    <div className={`flex flex-col ${height} ${className}`}>
      {showHeader && (
        <div className="border-b bg-background p-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold">Chat with {assistantName}</h2>
            {loading && <span className="text-sm text-muted-foreground">Loading...</span>}
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            Send messages to communicate with your AI assistant
          </p>
        </div>
      )}

      {/* Messages Area - Takes up remaining space */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4" data-testid="message-list">
        {error && <Alert variant="destructive">{error}</Alert>}
        {subscriptionError && (
          <Alert variant="destructive">
            Real-time updates unavailable: {subscriptionError.message}
          </Alert>
        )}

        {allMessages.length === 0 && !loading && (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <div className="text-center">
              <p className="text-lg mb-2">Start a conversation</p>
              <p className="text-sm">Send a message to your {assistantName} to get started.</p>
            </div>
          </div>
        )}

        {allMessages.map(message => (
          <ChatMessage
            key={message.id}
            id={message.id}
            content={message.content}
            senderRole={message.senderRole}
            timestamp={message.timestamp}
            metadata={message.metadata}
          />
        ))}

        <div ref={messagesEndRef} />
      </div>

      {/* Message Input Area - Stuck to bottom */}
      <div className="border-t bg-background p-4">
        <MessageInput
          conversationId={conversationId}
          onMessageSent={handleMessageSent}
          disabled={loading}
          placeholder={`Send a message to ${assistantName}...`}
        />
      </div>
    </div>
  );
};
