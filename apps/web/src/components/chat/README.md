# Chat Components

This directory contains the frontend components for the text messaging feature that allows users to communicate with their Personal Assistant.

## Components

### ChatInterface

The main chat interface component that combines message display and input functionality.

**Props:**

- `conversationId: string` - The ID of the conversation
- `assistantName?: string` - Name of the assistant (default: "Personal Assistant")
- `messages?: Message[]` - Array of messages to display
- `onMessageSent?: (message: Message) => void` - Callback when a message is sent
- `loading?: boolean` - Loading state
- `error?: string` - Error message to display

**Features:**

- Displays conversation messages in a scrollable area
- Auto-scrolls to bottom when new messages arrive
- Integrates MessageInput for sending new messages
- Shows empty state when no messages
- Handles loading and error states

### MessageInput

A text input component for composing and sending messages.

**Props:**

- `conversationId: string` - The ID of the conversation
- `onMessageSent?: (message: Message) => void` - Callback when a message is sent
- `disabled?: boolean` - Whether the input is disabled
- `placeholder?: string` - Placeholder text

**Features:**

- Text area with character count (max 4000 characters)
- Client-side validation for empty messages and character limit
- Send button that's disabled when input is empty or loading
- Enter key to send (Shift+Enter for new line)
- Error handling and display
- Loading states during message sending

### ChatMessage

Component for displaying individual chat messages.

**Props:**

- `id: string` - Message ID
- `content: any` - Message content (expects `{text: string}` format)
- `senderRole: "user" | "agent"` - Who sent the message
- `timestamp: string` - When the message was sent
- `metadata?: any` - Additional message metadata

**Features:**

- Different styling for user vs assistant messages
- Timestamp formatting
- Text wrapping and line break preservation
- Responsive design

## GraphQL Integration

The components use the `SEND_MESSAGE_MUTATION` GraphQL mutation:

```graphql
mutation SendMessage($conversationId: ID!, $content: String!) {
  sendMessage(conversationId: $conversationId, content: $content) {
    message {
      id
      conversationId
      senderRole
      content
      timestamp
      metadata
    }
    success
    errorMessage
  }
}
```

## Usage Example

```tsx
import { ChatInterface } from "./components/chat";

function MyPage() {
  const [messages, setMessages] = useState([]);

  const handleMessageSent = newMessage => {
    setMessages(prev => [...prev, newMessage]);
  };

  return (
    <ChatInterface
      conversationId="your-conversation-id"
      assistantName="My Assistant"
      messages={messages}
      onMessageSent={handleMessageSent}
    />
  );
}
```

## Styling

Components use Tailwind CSS with the ShadCN UI design system. They follow the established color scheme:

- User messages: Primary color background
- Assistant messages: Muted color background
- Consistent spacing and typography
- Responsive design

## Testing

Components include comprehensive tests covering:

- Rendering and basic functionality
- User interactions (typing, sending messages)
- Validation (empty messages, character limits)
- Keyboard shortcuts (Enter to send, Shift+Enter for new line)
- Error handling
- GraphQL integration with mocked responses

Run tests with:

```bash
npm test
```

## Storybook

Interactive component documentation is available in Storybook. See the [Complete Storybook Guide](../../../../docs/storybook.md) for setup and usage.

```bash
# Quick start
./scripts/storybook.sh dev --open
```

Stories include different message types, loading/error states, and various configurations.
