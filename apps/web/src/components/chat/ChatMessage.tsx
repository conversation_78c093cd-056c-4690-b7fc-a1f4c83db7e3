import React from "react";
import type { MessageContent, MessageMetadata } from "./types";
import { PAMessage } from "./PAMessage";

interface ChatMessageProps {
  id: string;
  content: MessageContent;
  senderRole: "USER" | "AGENT";
  timestamp: string;
  metadata?: MessageMetadata;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({
  id,
  content,
  senderRole,
  timestamp,
  metadata,
}) => {
  // Use PAMessage component for agent messages
  if (senderRole === "AGENT") {
    return <PAMessage id={id} content={content} timestamp={timestamp} metadata={metadata} />;
  }

  // Render user messages with existing styling
  const messageText = content.text || "";

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  return (
    <div className="flex w-full mb-4 justify-end">
      <div className="max-w-[70%] rounded-lg px-4 py-2 shadow-sm bg-primary text-primary-foreground">
        <div className="text-sm font-medium mb-1">You</div>

        <div className="text-sm whitespace-pre-wrap break-words">{messageText}</div>

        <div className="text-xs opacity-70 mt-2">{formatTimestamp(timestamp)}</div>
      </div>
    </div>
  );
};
