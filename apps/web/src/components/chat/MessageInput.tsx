import React, { useRef, useState } from "react";
import { useMutation } from "@apollo/client";
import { Button } from "../ui/button";
import { Textarea } from "../ui/textarea";
import { Alert } from "../ui/alert";
import { SEND_MESSAGE_MUTATION } from "../../graphql/mutations";
import { type Message, type SendMessageResponse } from "./types";

interface MessageInputProps {
  conversationId: string;
  onMessageSent?: (message: Message) => void;
  disabled?: boolean;
  placeholder?: string;
}

export const MessageInput: React.FC<MessageInputProps> = ({
  conversationId,
  onMessageSent,
  disabled = false,
  placeholder = "Type your message...",
}) => {
  const [message, setMessage] = useState("");
  const [localError, setLocalError] = useState<string | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const [sendMessage, { loading, error }] = useMutation<SendMessageResponse>(
    SEND_MESSAGE_MUTATION,
    {
      onCompleted: data => {
        if (data.sendMessage.success) {
          setMessage("");
          setLocalError(null);
          onMessageSent?.(data.sendMessage.message);
          // Focus the input after successful send
          requestAnimationFrame(() => {
            textareaRef.current?.focus();
          });
        } else {
          setLocalError(data.sendMessage.errorMessage || "Failed to send message");
        }
      },
      onError: () => {
        setLocalError("Failed to send message");
      },
    },
  );

  const handleSubmit = async (e?: React.FormEvent) => {
    e?.preventDefault();

    console.log("=== MessageInput handleSubmit called ===");
    console.log("Current message state:", message);
    console.log("Conversation ID:", conversationId);

    // Get the current value from the textarea element directly to ensure we have the latest value
    const currentValue = textareaRef.current?.value || message;
    const trimmedMessage = currentValue.trim();
    console.log("Trimmed message:", trimmedMessage);

    // Client-side validation
    if (!trimmedMessage) {
      console.log("Validation failed: empty message");
      setLocalError("Message cannot be empty");
      return;
    }

    if (trimmedMessage.length > 4000) {
      console.log("Validation failed: message too long");
      setLocalError("Message cannot exceed 4000 characters");
      return;
    }

    console.log("Validation passed, calling sendMessage");
    setLocalError(null);

    // Add a small delay to ensure React state is fully synchronized
    await new Promise(resolve => setTimeout(resolve, 10));

    try {
      const variables = {
        conversationId,
        content: trimmedMessage,
      };
      console.log("Mutation variables:", variables);

      await sendMessage({
        variables,
      });
      console.log("Mutation completed successfully");
    } catch (err) {
      // Error is handled by onError callback
      console.error("Error sending message:", err);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      // Call handleSubmit directly without creating a synthetic event
      handleSubmit();
    }
  };

  const isDisabled = disabled || loading;
  const errorMessage = localError || error?.message;

  return (
    <div className="space-y-4">
      {errorMessage && (
        <Alert variant="destructive" data-testid="error-message">
          {errorMessage}
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-4" data-testid="message-form">
        <div className="flex flex-col space-y-2">
          <Textarea
            ref={textareaRef}
            value={message}
            onChange={e => setMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={isDisabled}
            className="min-h-[100px] resize-none"
            aria-label="Message input"
            data-testid="message-input"
          />

          <div className="flex justify-between items-center">
            <span
              className={`text-sm ${
                message.length > 4000 ? "text-red-500" : "text-muted-foreground"
              }`}
              data-testid="character-count"
            >
              {message.length}/4000 characters
            </span>

            <Button type="submit" disabled={isDisabled} size="sm" data-testid="send-button">
              {loading ? "Sending..." : "Send"}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
};
