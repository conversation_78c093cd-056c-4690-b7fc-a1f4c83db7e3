import React from "react";
import type { Meta, StoryObj } from "@storybook/react-vite";
import { MessageInput } from "./MessageInput";
import { MockedProvider } from "@apollo/client/testing";
import { SEND_MESSAGE_MUTATION } from "../../graphql/mutations";

const meta: Meta<typeof MessageInput> = {
  title: "Chat/MessageInput",
  component: MessageInput,
  parameters: {
    layout: "centered",
  },
  decorators: [
    Story => (
      <MockedProvider
        mocks={[
          {
            request: {
              query: SEND_MESSAGE_MUTATION,
              variables: {
                conversationId: "test-conversation",
                content: "Hello, world!",
              },
            },
            result: {
              data: {
                sendMessage: {
                  message: {
                    id: "test-message-id",
                    conversationId: "test-conversation",
                    senderRole: "user",
                    content: { text: "Hello, world!" },
                    timestamp: new Date().toISOString(),
                    metadata: {},
                  },
                  success: true,
                  errorMessage: null,
                },
              },
            },
          },
        ]}
      >
        <div className="w-[500px]">
          <Story />
        </div>
      </MockedProvider>
    ),
  ],
  args: {
    conversationId: "test-conversation",
    placeholder: "Type your message...",
  },
};

export default meta;
type Story = StoryObj<typeof MessageInput>;

export const Default: Story = {
  args: {},
};

export const Disabled: Story = {
  args: {
    disabled: true,
  },
};

export const CustomPlaceholder: Story = {
  args: {
    placeholder: "Ask your assistant anything...",
  },
};
