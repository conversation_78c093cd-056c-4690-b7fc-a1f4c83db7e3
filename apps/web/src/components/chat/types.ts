export interface MessageContent {
  text?: string;
  [key: string]: unknown;
}

export interface MessageMetadata {
  [key: string]: unknown;
}

export interface Message {
  id: string;
  conversationId: string;
  senderRole: "USER" | "AGENT";
  content: MessageContent;
  timestamp: string;
  metadata?: MessageMetadata;
}

export interface Conversation {
  id: string;
  userId: string;
  assistantId: string;
  createdAt: string;
  lastMessageAt?: string;
}

export interface SendMessageInput {
  conversationId: string;
  content: string;
}

export interface SendMessageResponse {
  sendMessage: {
    message: Message;
    success: boolean;
    errorMessage?: string;
  };
}
