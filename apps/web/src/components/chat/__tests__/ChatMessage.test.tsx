import React from "react";
import { describe, expect, it, jest } from "@jest/globals";
import { render, screen } from "@testing-library/react";
import { ChatMessage } from "../ChatMessage";
import { type MessageContent, type MessageMetadata } from "../types";
import "@testing-library/jest-dom";

// Mock the PAMessage component
jest.mock("../PAMessage", () => ({
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  PAMessage: ({ id, content, timestamp, metadata }: any) => (
    <div data-testid={`pa-message-${id}`}>
      <span data-testid="pa-content">{content.text || content.parts?.[0]?.content}</span>
      <span data-testid="pa-timestamp">{timestamp}</span>
      <span data-testid="pa-metadata">{JSON.stringify(metadata)}</span>
    </div>
  ),
}));

const mockTimestamp = "2024-01-15T14:30:00Z";

describe("ChatMessage", () => {
  describe("User Messages", () => {
    const userMessageProps = {
      id: "user-msg-1",
      content: { text: "Hello, this is a user message!" } as MessageContent,
      senderRole: "USER" as const,
      timestamp: mockTimestamp,
      metadata: { source: "web_client" } as MessageMetadata,
    };

    it("renders user message with correct content", () => {
      render(<ChatMessage {...userMessageProps} />);

      expect(screen.getByText("You")).toBeInTheDocument();
      expect(screen.getByText("Hello, this is a user message!")).toBeInTheDocument();
    });

    it("formats timestamp correctly for user messages", () => {
      render(<ChatMessage {...userMessageProps} />);

      // Should display time in HH:MM format (local timezone)
      const timeRegex = /\d{1,2}:\d{2}/;
      expect(screen.getByText(timeRegex)).toBeInTheDocument();
    });

    it("applies correct styling for user messages", () => {
      const { container } = render(<ChatMessage {...userMessageProps} />);

      expect(container.querySelector(".justify-end")).toBeInTheDocument();
      expect(container.querySelector(".bg-primary")).toBeInTheDocument();
      expect(container.querySelector(".text-primary-foreground")).toBeInTheDocument();
    });

    it("handles empty user message content", () => {
      const emptyContentProps = {
        ...userMessageProps,
        content: {} as MessageContent,
      };

      const { container } = render(<ChatMessage {...emptyContentProps} />);

      expect(screen.getByText("You")).toBeInTheDocument();
      // Check for empty content div instead of trying to match empty text
      const contentDiv = container.querySelector(".whitespace-pre-wrap");
      expect(contentDiv).toBeInTheDocument();
      expect(contentDiv).toHaveTextContent("");
    });

    it("handles user message with only whitespace", () => {
      const whitespaceProps = {
        ...userMessageProps,
        content: { text: "   \n\t   " } as MessageContent,
      };

      const { container } = render(<ChatMessage {...whitespaceProps} />);

      expect(screen.getByText("You")).toBeInTheDocument();
      // Check for whitespace content preservation using textContent
      const contentDiv = container.querySelector(".whitespace-pre-wrap");
      expect(contentDiv).toBeInTheDocument();
      expect(contentDiv?.textContent).toBe("   \n\t   ");
    });

    it("preserves line breaks in user messages", () => {
      const multilineProps = {
        ...userMessageProps,
        content: { text: "Line 1\nLine 2\nLine 3" } as MessageContent,
      };

      const { container } = render(<ChatMessage {...multilineProps} />);

      // Use a more flexible text matcher for multiline content
      expect(
        screen.getByText((content, element) => {
          return element?.textContent === "Line 1\nLine 2\nLine 3";
        }),
      ).toBeInTheDocument();
      expect(container.querySelector(".whitespace-pre-wrap")).toBeInTheDocument();
    });

    it("handles long user messages with word breaking", () => {
      const longMessageProps = {
        ...userMessageProps,
        content: {
          text: "This is a very long message that should break words properly when it exceeds the maximum width of the message container to prevent layout issues",
        } as MessageContent,
      };

      const { container } = render(<ChatMessage {...longMessageProps} />);

      expect(container.querySelector(".break-words")).toBeInTheDocument();
      expect(container.querySelector(".max-w-\\[70\\%\\]")).toBeInTheDocument();
    });

    it("displays user message metadata (though not visible in UI)", () => {
      render(<ChatMessage {...userMessageProps} />);

      // Metadata is passed to component but not displayed in user messages
      // This test ensures the component accepts metadata without breaking
      expect(screen.getByText("You")).toBeInTheDocument();
    });
  });

  describe("Agent Messages", () => {
    const agentMessageProps = {
      id: "agent-msg-1",
      content: {
        parts: [{ type: "text", content: "Hello! I'm your personal assistant." }],
      } as MessageContent,
      senderRole: "AGENT" as const,
      timestamp: mockTimestamp,
      metadata: { source: "ai_service", model: "gpt-4" } as MessageMetadata,
    };

    it("renders agent message using PAMessage component", () => {
      render(<ChatMessage {...agentMessageProps} />);

      expect(screen.getByTestId("pa-message-agent-msg-1")).toBeInTheDocument();
      expect(screen.getByTestId("pa-content")).toHaveTextContent(
        "Hello! I'm your personal assistant.",
      );
      expect(screen.getByTestId("pa-timestamp")).toHaveTextContent(mockTimestamp);
    });

    it("passes metadata to PAMessage component", () => {
      render(<ChatMessage {...agentMessageProps} />);

      const metadataElement = screen.getByTestId("pa-metadata");
      expect(metadataElement).toHaveTextContent(JSON.stringify(agentMessageProps.metadata));
    });

    it("handles agent message with simple text content", () => {
      const simpleTextProps = {
        ...agentMessageProps,
        content: { text: "Simple agent response" } as MessageContent,
      };

      render(<ChatMessage {...simpleTextProps} />);

      expect(screen.getByTestId("pa-message-agent-msg-1")).toBeInTheDocument();
      expect(screen.getByTestId("pa-content")).toHaveTextContent("Simple agent response");
    });

    it("handles agent message without metadata", () => {
      const noMetadataProps = {
        ...agentMessageProps,
        metadata: undefined,
      };

      render(<ChatMessage {...noMetadataProps} />);

      expect(screen.getByTestId("pa-message-agent-msg-1")).toBeInTheDocument();
      expect(screen.getByTestId("pa-metadata")).toHaveTextContent("");
    });
  });

  describe("Message Content Handling", () => {
    it("handles structured content format", () => {
      const structuredContent = {
        parts: [
          { type: "text", content: "First part" },
          { type: "text", content: "Second part" },
          { type: "image", content: "image-url" },
        ],
      } as MessageContent;

      const props = {
        id: "structured-msg",
        content: structuredContent,
        senderRole: "AGENT" as const,
        timestamp: mockTimestamp,
      };

      render(<ChatMessage {...props} />);

      expect(screen.getByTestId("pa-message-structured-msg")).toBeInTheDocument();
    });

    it("handles null/undefined content gracefully", () => {
      const nullContentProps = {
        id: "null-content",
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        content: {} as any, // Use empty object instead of null
        senderRole: "USER" as const,
        timestamp: mockTimestamp,
      };

      // Should not throw an error
      expect(() => render(<ChatMessage {...nullContentProps} />)).not.toThrow();
    });

    it("handles content with special characters", () => {
      const specialCharsProps = {
        id: "special-chars",
        content: { text: "Special chars: @#$%^&*()_+-=[]{}|;':\",./<>? 🚀 測試" } as MessageContent,
        senderRole: "USER" as const,
        timestamp: mockTimestamp,
      };

      render(<ChatMessage {...specialCharsProps} />);

      expect(
        screen.getByText("Special chars: @#$%^&*()_+-=[]{}|;':\",./<>? 🚀 測試"),
      ).toBeInTheDocument();
    });
  });

  describe("Timestamp Formatting", () => {
    it("handles different timestamp formats", () => {
      const timestamps = [
        "2024-01-15T14:30:00Z",
        "2024-01-15T14:30:00.000Z",
        "2024-01-15T14:30:00+00:00",
        "2024-01-15 14:30:00",
      ];

      timestamps.forEach((timestamp, index) => {
        const props = {
          id: `timestamp-test-${index}`,
          content: { text: `Message ${index}` } as MessageContent,
          senderRole: "USER" as const,
          timestamp,
        };

        const { unmount } = render(<ChatMessage {...props} />);

        // Should render without throwing errors
        expect(screen.getByText("You")).toBeInTheDocument();

        unmount();
      });
    });

    it("handles invalid timestamp gracefully", () => {
      const invalidTimestampProps = {
        id: "invalid-timestamp",
        content: { text: "Test message" } as MessageContent,
        senderRole: "USER" as const,
        timestamp: "invalid-date-string",
      };

      // Should not throw an error
      expect(() => render(<ChatMessage {...invalidTimestampProps} />)).not.toThrow();
    });

    it("formats timestamp in local timezone", () => {
      // Mock the Date constructor to control timezone behavior
      const originalDate = Date;
      const mockDate = jest.fn().mockImplementation(dateString => {
        if (dateString) {
          return new originalDate(dateString);
        }
        return new originalDate("2024-01-15T14:30:00Z");
      });
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      global.Date = mockDate as any;

      const props = {
        id: "timezone-test",
        content: { text: "Timezone test" } as MessageContent,
        senderRole: "USER" as const,
        timestamp: "2024-01-15T14:30:00Z",
      };

      render(<ChatMessage {...props} />);

      // Should display some form of time (exact format depends on locale)
      const timeRegex = /\d{1,2}:\d{2}/;
      expect(screen.getByText(timeRegex)).toBeInTheDocument();

      // Restore original Date
      global.Date = originalDate;
    });
  });

  describe("Component Structure", () => {
    it("has correct DOM structure for user messages", () => {
      const props = {
        id: "structure-test",
        content: { text: "Structure test" } as MessageContent,
        senderRole: "USER" as const,
        timestamp: mockTimestamp,
      };

      const { container } = render(<ChatMessage {...props} />);

      // Check for proper container structure
      expect(container.querySelector(".flex.w-full.mb-4.justify-end")).toBeInTheDocument();
      expect(container.querySelector(".max-w-\\[70\\%\\]")).toBeInTheDocument();
      expect(container.querySelector(".rounded-lg")).toBeInTheDocument();
    });

    it("delegates agent message structure to PAMessage", () => {
      const props = {
        id: "pa-structure-test",
        content: { text: "PA structure test" } as MessageContent,
        senderRole: "AGENT" as const,
        timestamp: mockTimestamp,
      };

      render(<ChatMessage {...props} />);

      // Should render PAMessage component
      expect(screen.getByTestId("pa-message-pa-structure-test")).toBeInTheDocument();
    });
  });

  describe("Accessibility", () => {
    it("maintains readable text contrast", () => {
      const props = {
        id: "accessibility-test",
        content: { text: "Accessibility test message" } as MessageContent,
        senderRole: "USER" as const,
        timestamp: mockTimestamp,
      };

      const { container } = render(<ChatMessage {...props} />);

      // User messages should have primary background with contrasting text
      expect(container.querySelector(".bg-primary")).toBeInTheDocument();
      expect(container.querySelector(".text-primary-foreground")).toBeInTheDocument();
    });

    it("provides proper semantic structure", () => {
      const props = {
        id: "semantic-test",
        content: { text: "Semantic structure test" } as MessageContent,
        senderRole: "USER" as const,
        timestamp: mockTimestamp,
      };

      render(<ChatMessage {...props} />);

      // Messages should have clear sender identification
      expect(screen.getByText("You")).toBeInTheDocument();
      expect(screen.getByText("Semantic structure test")).toBeInTheDocument();
    });
  });

  describe("Edge Cases", () => {
    it("handles extremely long messages", () => {
      const longText = "A".repeat(10000);
      const props = {
        id: "long-message",
        content: { text: longText } as MessageContent,
        senderRole: "USER" as const,
        timestamp: mockTimestamp,
      };

      expect(() => render(<ChatMessage {...props} />)).not.toThrow();
      expect(screen.getByText(longText)).toBeInTheDocument();
    });

    it("handles empty string message", () => {
      const props = {
        id: "empty-message",
        content: { text: "" } as MessageContent,
        senderRole: "USER" as const,
        timestamp: mockTimestamp,
      };

      render(<ChatMessage {...props} />);

      expect(screen.getByText("You")).toBeInTheDocument();
      // Empty text should still render the message container
    });

    it("handles message with only emoji", () => {
      const props = {
        id: "emoji-message",
        content: { text: "🚀🎉✨💖🌟" } as MessageContent,
        senderRole: "USER" as const,
        timestamp: mockTimestamp,
      };

      render(<ChatMessage {...props} />);

      expect(screen.getByText("🚀🎉✨💖🌟")).toBeInTheDocument();
    });

    it("handles mixed content types", () => {
      const mixedContent = {
        text: "Primary text",
        parts: [{ type: "text", content: "Secondary text" }],
        extra: "Additional data",
      } as MessageContent;

      const userProps = {
        id: "mixed-user",
        content: mixedContent,
        senderRole: "USER" as const,
        timestamp: mockTimestamp,
      };

      const agentProps = {
        id: "mixed-agent",
        content: mixedContent,
        senderRole: "AGENT" as const,
        timestamp: mockTimestamp,
      };

      // User message should use text property
      render(<ChatMessage {...userProps} />);
      expect(screen.getByText("Primary text")).toBeInTheDocument();

      // Agent message should be handled by PAMessage
      const { unmount } = render(<ChatMessage {...agentProps} />);
      expect(screen.getByTestId("pa-message-mixed-agent")).toBeInTheDocument();

      unmount();
    });
  });
});
