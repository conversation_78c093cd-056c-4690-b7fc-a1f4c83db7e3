import React from "react";
import { beforeEach, describe, expect, it } from "@jest/globals";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { MockedProvider } from "@apollo/client/testing";
import { MessageInput } from "../MessageInput";
import { SEND_MESSAGE_MUTATION } from "@/graphql/mutations";
import "@testing-library/jest-dom";

const mockOnMessageSent = jest.fn();

const createMockResponse = (content: string) => ({
  request: {
    query: SEND_MESSAGE_MUTATION,
    variables: {
      conversationId: "test-conversation",
      content,
    },
  },
  result: {
    data: {
      sendMessage: {
        message: {
          id: "test-message-id",
          conversationId: "test-conversation",
          senderRole: "user",
          content: { text: content },
          timestamp: new Date().toISOString(),
          metadata: {},
        },
        success: true,
        errorMessage: null,
      },
    },
  },
});

const createErrorMockResponse = (content: string, errorMessage: string) => ({
  request: {
    query: SEND_MESSAGE_MUTATION,
    variables: {
      conversationId: "test-conversation",
      content,
    },
  },
  result: {
    data: {
      sendMessage: {
        message: null,
        success: false,
        errorMessage,
      },
    },
  },
});

const mocks = [
  createMockResponse("Hello, world!"),
  createMockResponse("Hello"),
  createMockResponse("Test message"),
  createMockResponse("Hello 🚀 测试 العربية русский 🎉"),
  createMockResponse("Special chars: @#$%^&*()_+-=[]{}|;':\",./<>?"),
  createErrorMockResponse("", "Message cannot be empty"),
  {
    request: {
      query: SEND_MESSAGE_MUTATION,
      variables: {
        conversationId: "test-conversation",
        content: "XSS <script>alert('xss')</script>",
      },
    },
    result: {
      data: {
        sendMessage: {
          message: {
            id: "test-message-xss",
            conversationId: "test-conversation",
            senderRole: "user",
            content: { text: "XSS" }, // Sanitized response
            timestamp: new Date().toISOString(),
            metadata: {},
          },
          success: true,
          errorMessage: null,
        },
      },
    },
  },
  {
    request: {
      query: SEND_MESSAGE_MUTATION,
      variables: {
        conversationId: "test-conversation",
        content: "a".repeat(4001), // Exceeds max length
      },
    },
    result: {
      data: {
        sendMessage: {
          message: null,
          success: false,
          errorMessage: "Message cannot exceed 4000 characters",
        },
      },
    },
  },
  {
    request: {
      query: SEND_MESSAGE_MUTATION,
      variables: {
        conversationId: "test-conversation",
        content: "Server error test",
      },
    },
    error: new Error("Network error"),
  },
];

const renderMessageInput = (props = {}) => {
  const defaultProps = {
    conversationId: "test-conversation",
    onMessageSent: mockOnMessageSent,
    ...props,
  };

  return render(
    <MockedProvider mocks={mocks} addTypename={false}>
      <MessageInput {...defaultProps} />
    </MockedProvider>,
  );
};

describe("MessageInput", () => {
  beforeEach(() => {
    mockOnMessageSent.mockClear();
  });

  it("renders correctly", () => {
    renderMessageInput();

    expect(screen.getByRole("textbox")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /send/i })).toBeInTheDocument();
    expect(screen.getByText("0/4000 characters")).toBeInTheDocument();
  });

  it("updates character count as user types", async () => {
    const user = userEvent.setup();
    renderMessageInput();

    const textarea = screen.getByRole("textbox");
    await user.type(textarea, "Hello");

    expect(screen.getByText("5/4000 characters")).toBeInTheDocument();
  });

  it("does not disable send button when input is empty", () => {
    renderMessageInput();

    const sendButton = screen.getByRole("button", { name: /send/i });
    expect(sendButton).not.toBeDisabled();
  });

  it("enables send button when input has content", async () => {
    const user = userEvent.setup();
    renderMessageInput();

    const textarea = screen.getByRole("textbox");
    const sendButton = screen.getByRole("button", { name: /send/i });

    await user.type(textarea, "Hello");
    expect(sendButton).not.toBeDisabled();
  });

  it("shows error for empty message submission", async () => {
    renderMessageInput();

    const form = screen.getByTestId("message-form");

    // Submit empty form
    fireEvent.submit(form);

    await waitFor(() => {
      expect(screen.getByText("Message cannot be empty")).toBeInTheDocument();
    });
  });

  it("shows error for message exceeding character limit", async () => {
    renderMessageInput();

    const textarea = screen.getByRole("textbox");
    const longMessage = "a".repeat(4001);

    // Set the value directly to avoid timeout
    fireEvent.change(textarea, { target: { value: longMessage } });

    const form = screen.getByTestId("message-form");
    fireEvent.submit(form);

    await waitFor(() => {
      expect(screen.getByText("Message cannot exceed 4000 characters")).toBeInTheDocument();
    });
  });

  it("submits message on Enter key press", async () => {
    const user = userEvent.setup();
    renderMessageInput();

    const textarea = screen.getByRole("textbox");
    await user.type(textarea, "Hello, world!");

    fireEvent.keyDown(textarea, { key: "Enter", shiftKey: false });

    await waitFor(
      () => {
        expect(mockOnMessageSent).toHaveBeenCalled();
      },
      { timeout: 3000 },
    );
  });

  it("does not submit on Shift+Enter", async () => {
    const user = userEvent.setup();
    renderMessageInput();

    const textarea = screen.getByRole("textbox");
    await user.type(textarea, "Hello, world!");

    fireEvent.keyDown(textarea, { key: "Enter", shiftKey: true });

    // Wait a bit to ensure no submission happens
    await new Promise(resolve => setTimeout(resolve, 100));

    expect(mockOnMessageSent).not.toHaveBeenCalled();
  });

  it("handles unicode characters correctly", async () => {
    const user = userEvent.setup();
    renderMessageInput();

    const textarea = screen.getByRole("textbox");
    const unicodeMessage = "Hello 🚀 测试 العربية русский 🎉";

    await user.type(textarea, unicodeMessage);

    expect(screen.getByText(`${unicodeMessage.length}/4000 characters`)).toBeInTheDocument();

    const form = screen.getByTestId("message-form");
    fireEvent.submit(form);

    await waitFor(
      () => {
        expect(mockOnMessageSent).toHaveBeenCalled();
      },
      { timeout: 3000 },
    );
  });

  it("sanitizes XSS attempts in message content", async () => {
    const user = userEvent.setup();
    renderMessageInput();

    const textarea = screen.getByRole("textbox");
    const xssMessage = "XSS <script>alert('xss')</script>";

    await user.type(textarea, xssMessage);
    fireEvent.submit(screen.getByTestId("message-form"));

    await waitFor(() => {
      expect(mockOnMessageSent).toHaveBeenCalled();
      // The mock response should return sanitized content
      expect(mockOnMessageSent).toHaveBeenCalledWith(
        expect.objectContaining({
          content: { text: "XSS" },
        }),
      );
    });
  });

  it("prevents submission of messages exceeding max length", async () => {
    renderMessageInput();

    const textarea = screen.getByRole("textbox");
    const longMessage = "a".repeat(4001);

    // Using fireEvent to bypass the browser's maxlength attribute
    fireEvent.change(textarea, { target: { value: longMessage } });

    // Submit the form to trigger validation
    const form = screen.getByTestId("message-form");
    fireEvent.submit(form);

    // Should show error message after form submission
    await waitFor(() => {
      expect(screen.getByText("Message cannot exceed 4000 characters")).toBeInTheDocument();
    });
  });

  it("handles network errors gracefully", async () => {
    const user = userEvent.setup();
    const consoleError = jest.spyOn(console, "error").mockImplementation(() => {});

    renderMessageInput();

    const textarea = screen.getByRole("textbox");
    await user.type(textarea, "Server error test");

    fireEvent.submit(screen.getByTestId("message-form"));

    // Should show error message
    await waitFor(() => {
      expect(screen.getByText("Failed to send message")).toBeInTheDocument();
    });

    consoleError.mockRestore();
  });

  it("shows loading state during submission", async () => {
    // Mock a delayed response using a delayed Promise
    const delayedMock = {
      request: {
        query: SEND_MESSAGE_MUTATION,
        variables: {
          conversationId: "test-conversation",
          content: "Loading test",
        },
      },
      delay: 1000, // Use Apollo's built-in delay mechanism
      result: {
        data: {
          sendMessage: {
            message: {
              id: "test-message-loading",
              conversationId: "test-conversation",
              senderRole: "user",
              content: { text: "Loading test" },
              timestamp: new Date().toISOString(),
              metadata: {},
            },
            success: true,
            errorMessage: null,
          },
        },
      },
    };

    render(
      <MockedProvider mocks={[delayedMock]} addTypename={false}>
        <MessageInput conversationId="test-conversation" onMessageSent={mockOnMessageSent} />
      </MockedProvider>,
    );

    const user = userEvent.setup();
    const textarea = screen.getByRole("textbox");
    const sendButton = screen.getByRole("button", { name: /send/i });

    await user.type(textarea, "Loading test");

    // Click the button instead of submitting the form directly
    await user.click(sendButton);

    // Should show loading state - check for "Sending..." text
    await waitFor(
      () => {
        expect(screen.getByRole("button", { name: /sending/i })).toBeInTheDocument();
        expect(screen.getByRole("button", { name: /sending/i })).toBeDisabled();
      },
      { timeout: 1500 },
    );

    // Wait for the mock to resolve and loading state to be removed
    await waitFor(
      () => {
        expect(screen.queryByRole("button", { name: /sending/i })).not.toBeInTheDocument();
        expect(screen.getByRole("button", { name: /send/i })).toBeInTheDocument();
      },
      { timeout: 2500 },
    );
  });

  it("handles special characters correctly", async () => {
    renderMessageInput();

    const textarea = screen.getByRole("textbox");
    const specialMessage = "Special chars: @#$%^&*()_+-=[]{}|;':\",./<>?";

    // Use fireEvent.change instead of userEvent.type for special characters
    fireEvent.change(textarea, { target: { value: specialMessage } });

    expect(screen.getByText(`${specialMessage.length}/4000 characters`)).toBeInTheDocument();

    const form = screen.getByTestId("message-form");
    fireEvent.submit(form);

    await waitFor(
      () => {
        expect(mockOnMessageSent).toHaveBeenCalled();
      },
      { timeout: 3000 },
    );
  });

  it("trims whitespace from message content", async () => {
    const user = userEvent.setup();
    renderMessageInput();

    const textarea = screen.getByRole("textbox");
    await user.type(textarea, "   Hello   ");

    const form = screen.getByTestId("message-form");
    fireEvent.submit(form);

    await waitFor(
      () => {
        expect(mockOnMessageSent).toHaveBeenCalled();
      },
      { timeout: 3000 },
    );
  });

  it("shows error for whitespace-only message", async () => {
    renderMessageInput();

    const textarea = screen.getByRole("textbox");
    fireEvent.change(textarea, { target: { value: "   \t\n   " } });

    const form = screen.getByTestId("message-form");
    fireEvent.submit(form);

    await waitFor(() => {
      expect(screen.getByText("Message cannot be empty")).toBeInTheDocument();
    });
  });

  it("clears input after successful submission", async () => {
    const user = userEvent.setup();
    renderMessageInput();

    const textarea = screen.getByRole("textbox");
    await user.type(textarea, "Hello, world!");

    const form = screen.getByTestId("message-form");
    fireEvent.submit(form);

    await waitFor(
      () => {
        expect(mockOnMessageSent).toHaveBeenCalled();
      },
      { timeout: 3000 },
    );

    // Check that input is cleared
    expect(textarea).toHaveValue("");
    expect(screen.getByText("0/4000 characters")).toBeInTheDocument();
  });

  it("disables send button during submission", async () => {
    // Create a delayed mock for this test using Apollo's built-in delay
    const delayedMock = {
      request: {
        query: SEND_MESSAGE_MUTATION,
        variables: {
          conversationId: "test-conversation",
          content: "Hello",
        },
      },
      delay: 1000, // Use Apollo's built-in delay mechanism
      result: {
        data: {
          sendMessage: {
            message: {
              id: "test-message-disable",
              conversationId: "test-conversation",
              senderRole: "user",
              content: { text: "Hello" },
              timestamp: new Date().toISOString(),
              metadata: {},
            },
            success: true,
            errorMessage: null,
          },
        },
      },
    };

    render(
      <MockedProvider mocks={[delayedMock]} addTypename={false}>
        <MessageInput conversationId="test-conversation" onMessageSent={mockOnMessageSent} />
      </MockedProvider>,
    );

    const user = userEvent.setup();
    const textarea = screen.getByRole("textbox");
    const sendButton = screen.getByRole("button", { name: /send/i });

    await user.type(textarea, "Hello");
    expect(sendButton).not.toBeDisabled();

    // Click the button to submit
    await user.click(sendButton);

    // Button should be disabled during submission
    await waitFor(
      () => {
        expect(sendButton).toBeDisabled();
      },
      { timeout: 1500 },
    );

    // Wait for the mock to resolve and loading state to be removed
    await waitFor(
      () => {
        expect(mockOnMessageSent).toHaveBeenCalled();
        expect(sendButton).not.toBeDisabled();
      },
      { timeout: 2500 },
    );
  });
});
