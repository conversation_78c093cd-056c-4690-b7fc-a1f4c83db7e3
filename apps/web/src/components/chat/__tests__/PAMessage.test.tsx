import React from "react";
import { render, screen } from "@testing-library/react";
import { PAMessage } from "../PAMessage";

// Mock the MessageContent type to make text optional
jest.mock("../types", () => ({
  __esModule: true,
  MessageContent: { text: undefined },
}));

describe("PAMessage", () => {
  const defaultProps = {
    id: "test-message-1",
    content: {
      parts: [
        {
          type: "text",
          content: "Hello! I'm your Personal Assistant. How can I help you today?",
        },
      ],
    },
    timestamp: "2024-01-15T10:00:00Z",
    metadata: { source: "ai_service" },
  };

  it("renders PA message with correct content", () => {
    render(<PAMessage {...defaultProps} />);

    expect(screen.getByText("Personal Assistant")).toBeInTheDocument();
    expect(
      screen.getByText("Hello! I'm your Personal Assistant. How can I help you today?"),
    ).toBeInTheDocument();
  });

  it("renders PA avatar", () => {
    render(<PAMessage {...defaultProps} />);

    expect(screen.getByText("PA")).toBeInTheDocument();
  });

  it("formats timestamp correctly", () => {
    render(<PAMessage {...defaultProps} />);

    // The timestamp should be formatted as HH:MM
    expect(screen.getByText(/\d{1,2}:\d{2}/)).toBeInTheDocument();
  });

  it("handles structured content format", () => {
    const structuredContent = {
      parts: [
        { type: "text", content: "First part" },
        { type: "text", content: "Second part" },
      ],
    };

    render(<PAMessage {...defaultProps} content={structuredContent} />);

    expect(screen.getByText("First part Second part")).toBeInTheDocument();
  });

  it("handles fallback text content format", () => {
    const textContent = {
      text: "Simple text message",
    };

    render(<PAMessage {...defaultProps} content={textContent} />);

    expect(screen.getByText("Simple text message")).toBeInTheDocument();
  });

  it("handles empty content gracefully", () => {
    const emptyContent = {};

    render(<PAMessage {...defaultProps} content={emptyContent} />);

    // Should render without crashing, even with empty content
    expect(screen.getByText("Personal Assistant")).toBeInTheDocument();
  });

  it("applies correct styling classes", () => {
    const { container } = render(<PAMessage {...defaultProps} />);

    // Check for PA-specific styling
    expect(container.querySelector(".justify-start")).toBeInTheDocument();
    expect(container.querySelector(".bg-muted")).toBeInTheDocument();
    expect(container.querySelector(".text-blue-600")).toBeInTheDocument();
  });
});
