import React from "react";
import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { MockedProvider } from "@apollo/client/testing";
import { ChatInterface } from "../ChatInterface";
import { type Message } from "../types";
import "@testing-library/jest-dom";

// Mock the useMessageSubscription hook
jest.mock("@hooks/useMessageSubscription", () => ({
  useMessageSubscription: jest.fn(),
}));

// Mock the MessageInput component
jest.mock("../MessageInput", () => ({
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  MessageInput: ({ onMessageSent, disabled, placeholder }: any) => (
    <div data-testid="message-input">
      <button
        data-testid="mock-send-button"
        disabled={disabled}
        onClick={() =>
          onMessageSent?.({
            id: "mock-message-id",
            conversationId: "test-conversation",
            senderRole: "USER" as const,
            content: { text: "Mock message" },
            timestamp: new Date().toISOString(),
          })
        }
      >
        Send Mock Message
      </button>
      <span data-testid="placeholder">{placeholder}</span>
    </div>
  ),
}));

// Mock the ChatMessage component
jest.mock("../ChatMessage", () => ({
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ChatMessage: ({ id, content, senderRole, timestamp }: any) => (
    <div data-testid={`chat-message-${id}`}>
      <span data-testid="sender-role">{senderRole}</span>
      <span data-testid="message-content">{content.text}</span>
      <span data-testid="timestamp">{timestamp}</span>
    </div>
  ),
}));

const mockMessages: Message[] = [
  {
    id: "msg-1",
    conversationId: "test-conversation",
    senderRole: "USER",
    content: { text: "Hello, assistant!" },
    timestamp: "2024-01-15T10:00:00Z",
    metadata: {},
  },
  {
    id: "msg-2",
    conversationId: "test-conversation",
    senderRole: "AGENT",
    content: { text: "Hello! How can I help you today?" },
    timestamp: "2024-01-15T10:01:00Z",
    metadata: { source: "ai_service" },
  },
];

const defaultProps = {
  conversationId: "test-conversation",
  assistantName: "Test Assistant",
  messages: mockMessages,
};

const renderChatInterface = (props = {}) => {
  const finalProps = { ...defaultProps, ...props };

  return render(
    <MockedProvider mocks={[]} addTypename={false}>
      <ChatInterface {...finalProps} />
    </MockedProvider>,
  );
};

// Get the mocked function
// eslint-disable-next-line @typescript-eslint/no-require-imports
const mockUseMessageSubscription = require("@hooks/useMessageSubscription")
  .useMessageSubscription as jest.MockedFunction<any>; // eslint-disable-line @typescript-eslint/no-explicit-any

// Mock scrollIntoView for JSDOM
Object.defineProperty(HTMLElement.prototype, "scrollIntoView", {
  value: jest.fn(),
  writable: true,
});

describe("ChatInterface", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseMessageSubscription.mockReturnValue({
      subscriptionError: null,
    });
  });

  describe("Rendering", () => {
    it("renders the chat interface with header", () => {
      renderChatInterface();

      expect(screen.getByText("Chat with Test Assistant")).toBeInTheDocument();
      expect(
        screen.getByText("Send messages to communicate with your AI assistant"),
      ).toBeInTheDocument();
    });

    it("renders without header when showHeader is false", () => {
      renderChatInterface({ showHeader: false });

      expect(screen.queryByText("Chat with Test Assistant")).not.toBeInTheDocument();
      expect(
        screen.queryByText("Send messages to communicate with your AI assistant"),
      ).not.toBeInTheDocument();
    });

    it("displays loading indicator when loading", () => {
      renderChatInterface({ loading: true });

      expect(screen.getByText("Loading...")).toBeInTheDocument();
    });

    it("applies custom height and className", () => {
      const { container } = renderChatInterface({
        height: "h-96",
        className: "custom-chat",
      });

      const chatContainer = container.firstChild as HTMLElement;
      expect(chatContainer).toHaveClass("h-96", "custom-chat");
    });

    it("uses default assistant name when not provided", () => {
      renderChatInterface({ assistantName: undefined });

      expect(screen.getByText("Chat with Personal Assistant")).toBeInTheDocument();
    });
  });

  describe("Messages Display", () => {
    it("renders all provided messages", () => {
      renderChatInterface();

      expect(screen.getByTestId("chat-message-msg-1")).toBeInTheDocument();
      expect(screen.getByTestId("chat-message-msg-2")).toBeInTheDocument();
      expect(screen.getByText("Hello, assistant!")).toBeInTheDocument();
      expect(screen.getByText("Hello! How can I help you today?")).toBeInTheDocument();
    });

    it("shows empty state when no messages", () => {
      renderChatInterface({ messages: [] });

      expect(screen.getByText("Start a conversation")).toBeInTheDocument();
      expect(
        screen.getByText("Send a message to your Test Assistant to get started."),
      ).toBeInTheDocument();
    });

    it("does not show empty state when loading", () => {
      renderChatInterface({ messages: [], loading: true });

      expect(screen.queryByText("Start a conversation")).not.toBeInTheDocument();
    });

    it("displays error message when provided", () => {
      renderChatInterface({ error: "Failed to load messages" });

      expect(screen.getByText("Failed to load messages")).toBeInTheDocument();
    });

    it("displays subscription error when present", () => {
      mockUseMessageSubscription.mockReturnValue({
        subscriptionError: new Error("Connection failed"),
      });

      renderChatInterface();

      expect(
        screen.getByText("Real-time updates unavailable: Connection failed"),
      ).toBeInTheDocument();
    });
  });

  describe("Message Input Integration", () => {
    it("renders message input with correct props", () => {
      renderChatInterface();

      expect(screen.getByTestId("message-input")).toBeInTheDocument();
      expect(screen.getByText("Send a message to Test Assistant...")).toBeInTheDocument();
    });

    it("disables message input when loading", () => {
      renderChatInterface({ loading: true });

      const sendButton = screen.getByTestId("mock-send-button");
      expect(sendButton).toBeDisabled();
    });

    it("calls onMessageSent when message is sent", async () => {
      const mockOnMessageSent = jest.fn();
      renderChatInterface({ onMessageSent: mockOnMessageSent });

      const sendButton = screen.getByTestId("mock-send-button");
      await userEvent.click(sendButton);

      expect(mockOnMessageSent).toHaveBeenCalledWith(
        expect.objectContaining({
          id: "mock-message-id",
          conversationId: "test-conversation",
          senderRole: "USER",
          content: { text: "Mock message" },
        }),
      );
    });
  });

  describe("Optimistic Messages", () => {
    it("handles optimistic message updates", async () => {
      renderChatInterface({ messages: [] });

      // Simulate sending a message which adds it to optimistic messages
      const sendButton = screen.getByTestId("mock-send-button");
      await userEvent.click(sendButton);

      // The optimistic message should be displayed immediately
      // Note: This is handled internally by the component state
      expect(screen.getByTestId("message-input")).toBeInTheDocument();
    });

    it("removes duplicates between server messages and optimistic messages", () => {
      // This test verifies the useMemo logic for combining messages

      renderChatInterface();

      // Should only render the message once (from server messages)
      const messageElements = screen.getAllByTestId(/^chat-message-msg-1$/);
      expect(messageElements).toHaveLength(1);
    });
  });

  describe("Message Subscription", () => {
    it("initializes message subscription with correct parameters", () => {
      renderChatInterface();

      expect(mockUseMessageSubscription).toHaveBeenCalledWith({
        conversationId: "test-conversation",
        onNewMessage: expect.any(Function),
        enabled: true,
      });
    });

    it("disables subscription when conversationId is not provided", () => {
      renderChatInterface({ conversationId: "" });

      expect(mockUseMessageSubscription).toHaveBeenCalledWith({
        conversationId: "",
        onNewMessage: expect.any(Function),
        enabled: false,
      });
    });

    it("handles new messages from subscription", () => {
      let onNewMessageCallback: (message: Message) => void;

      mockUseMessageSubscription.mockImplementation(({ onNewMessage }) => {
        onNewMessageCallback = onNewMessage;
        return { subscriptionError: null };
      });

      renderChatInterface();

      // Simulate receiving a new message
      const newMessage: Message = {
        id: "new-msg",
        conversationId: "test-conversation",
        senderRole: "AGENT",
        content: { text: "New subscription message" },
        timestamp: "2024-01-15T10:02:00Z",
      };

      onNewMessageCallback!(newMessage);

      // The callback should handle the new message appropriately
      expect(mockUseMessageSubscription).toHaveBeenCalled();
    });
  });

  describe("Auto-scroll Behavior", () => {
    it("has scroll container with correct attributes", () => {
      renderChatInterface();

      const messageList = screen.getByTestId("message-list");
      expect(messageList).toHaveClass("overflow-y-auto");
    });

    // Note: Testing actual scroll behavior would require more complex DOM mocking
    // The auto-scroll logic uses scrollIntoView which is tested in integration tests
  });

  describe("Accessibility", () => {
    it("has proper ARIA labels and roles", () => {
      renderChatInterface();

      const messageList = screen.getByTestId("message-list");
      expect(messageList).toBeInTheDocument();
    });

    it("maintains proper heading hierarchy", () => {
      renderChatInterface();

      const heading = screen.getByRole("heading", { level: 2 });
      expect(heading).toHaveTextContent("Chat with Test Assistant");
    });
  });

  describe("Edge Cases", () => {
    it("handles empty conversation ID gracefully", () => {
      renderChatInterface({ conversationId: "" });

      expect(screen.getByTestId("message-input")).toBeInTheDocument();
      expect(mockUseMessageSubscription).toHaveBeenCalledWith({
        conversationId: "",
        onNewMessage: expect.any(Function),
        enabled: false,
      });
    });

    it("handles messages with missing content", () => {
      const messagesWithMissingContent: Message[] = [
        {
          id: "msg-empty",
          conversationId: "test-conversation",
          senderRole: "USER",
          content: {},
          timestamp: "2024-01-15T10:00:00Z",
        },
      ];

      renderChatInterface({ messages: messagesWithMissingContent });

      expect(screen.getByTestId("chat-message-msg-empty")).toBeInTheDocument();
    });

    it("handles invalid timestamps gracefully", () => {
      const messagesWithInvalidTimestamp: Message[] = [
        {
          id: "msg-invalid-time",
          conversationId: "test-conversation",
          senderRole: "USER",
          content: { text: "Test message" },
          timestamp: "invalid-timestamp",
        },
      ];

      renderChatInterface({ messages: messagesWithInvalidTimestamp });

      expect(screen.getByTestId("chat-message-msg-invalid-time")).toBeInTheDocument();
    });
  });
});
