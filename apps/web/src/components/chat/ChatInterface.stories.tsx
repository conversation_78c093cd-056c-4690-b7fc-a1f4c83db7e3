import React from "react";
import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react-vite";
import { ChatInterface } from "./ChatInterface";
import { MockedProvider } from "@apollo/client/testing";
import { SEND_MESSAGE_MUTATION } from "../../graphql/mutations";
import type { Message } from "./types";

const mockMessages: Message[] = [
  {
    id: "msg-1",
    conversationId: "conv-1",
    senderRole: "user",
    content: { text: "Hello! I need help with my project." },
    timestamp: "2024-01-15T10:00:00Z",
  },
  {
    id: "msg-2",
    conversationId: "conv-1",
    senderRole: "agent",
    content: {
      text: "Hello! I'd be happy to help you with your project. What specific assistance do you need?",
    },
    timestamp: "2024-01-15T10:00:30Z",
  },
  {
    id: "msg-3",
    conversationId: "conv-1",
    senderRole: "user",
    content: {
      text: "I'm working on a React application and I'm having trouble with state management.",
    },
    timestamp: "2024-01-15T10:01:00Z",
  },
];

const meta: Meta<typeof ChatInterface> = {
  title: "Chat/ChatInterface",
  component: ChatInterface,
  parameters: {
    layout: "fullscreen",
  },
  decorators: [
    Story => (
      <MockedProvider
        mocks={[
          {
            request: {
              query: SEND_MESSAGE_MUTATION,
              variables: {
                conversationId: "conv-1",
                content: "Test message",
              },
            },
            result: {
              data: {
                sendMessage: {
                  message: {
                    id: "new-message-id",
                    conversationId: "conv-1",
                    senderRole: "user",
                    content: { text: "Test message" },
                    timestamp: new Date().toISOString(),
                    metadata: {},
                  },
                  success: true,
                  errorMessage: null,
                },
              },
            },
          },
        ]}
      >
        <div className="p-8">
          <Story />
        </div>
      </MockedProvider>
    ),
  ],
  args: {
    conversationId: "conv-1",
    assistantName: "Personal Assistant",
  },
};

export default meta;
type Story = StoryObj<typeof ChatInterface>;

export const EmptyChat: Story = {
  args: {
    messages: [],
  },
};

export const WithMessages: Story = {
  args: {
    messages: mockMessages,
  },
};

export const Loading: Story = {
  args: {
    messages: mockMessages,
    loading: true,
  },
};

export const WithError: Story = {
  args: {
    messages: mockMessages,
    error: "Failed to load conversation. Please try again.",
  },
};

export const CustomAssistantName: Story = {
  args: {
    messages: mockMessages,
    assistantName: "Code Helper AI",
  },
};
