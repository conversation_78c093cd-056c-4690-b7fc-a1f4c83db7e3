/**
 * API configuration for the A2A Platform frontend.
 *
 * This file configures API endpoints and ensures all connections
 * use HTTPS/WSS for security.
 */

// Get the base URL from environment variable or use a secure default
const getBaseUrl = (): string => {
  const url = import.meta.env.VITE_API_URL || window.location.origin;

  // Ensure the URL uses HTTPS
  return url.replace(/^http:\/\//i, "https://");
};

// GraphQL API URL (HTTPS) - Used for all operations including subscriptions via multipart HTTP
export const GRAPHQL_API_URL = `${getBaseUrl()}/graphql`;

// REST API base URL (HTTPS)
export const REST_API_URL = `${getBaseUrl()}/api`;

// Construct specific API endpoints
export const API_ENDPOINTS = {
  health: `${REST_API_URL}/health`,
  // Add other endpoints as needed
};

/**
 * Helper function to ensure a URL uses HTTPS
 *
 * @param url The URL to ensure is HTTPS
 * @returns The URL with HTTPS protocol
 */
export const ensureHttps = (url: string): string => {
  return url.replace(/^http:\/\//i, "https://");
};
