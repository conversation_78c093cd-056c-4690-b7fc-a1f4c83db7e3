export interface ConversationData {
  id: string;
  userId: string;
  assistantId: string;
  createdAt: string;
  lastMessageAt?: string;
  messageCount?: number;
  title?: string;
  status?: "active" | "archived" | "draft";
}

export interface ConversationListItem {
  id: string;
  title: string;
  lastMessage?: {
    content: string;
    timestamp: string;
    senderRole: "user" | "agent";
  };
  messageCount: number;
  createdAt: string;
  lastMessageAt?: string;
  status: "active" | "archived" | "draft";
}

export interface ConversationMetadata {
  totalCount: number;
  activeCount: number;
  archivedCount: number;
}
