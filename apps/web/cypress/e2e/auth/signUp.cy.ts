describe("Sign Up Flow", () => {
  beforeEach(() => {
    // Visit the sign-up page before each test
    cy.visit("/pa/sign-up");
  });

  it("should display the sign-up page correctly", () => {
    // Check that the page title is correct
    cy.contains("Create an account").should("be.visible");

    // Check that all sign-up options are visible
    cy.contains("Sign up with Google").should("be.visible");
    cy.contains("Sign up with GitHub").should("be.visible");
    cy.contains("Sign up with <PERSON><PERSON>").should("be.visible");

    // Check that the email input field is present
    cy.get('input[type="email"]').should("be.visible");
  });

  it("should validate email input", () => {
    // Check that the submit button exists
    cy.contains('button[type="submit"]', "Sign up with <PERSON><PERSON>").should("exist");

    // Enter invalid email
    cy.get('input[type="email"]').type("invalid-email");

    // Enter valid email
    cy.get('input[type="email"]').clear().type("<EMAIL>");

    // The email input should have the correct value
    cy.get('input[type="email"]').should("have.value", "<EMAIL>");
  });

  // Note: Full OAuth and email verification flows are difficult to test in E2E
  // This is a simplified test that checks the UI state changes
  it("should show loading state when submitting email", () => {
    // Enter email
    cy.get('input[type="email"]').type("<EMAIL>");

    // Get the button and check its initial text
    cy.contains("Sign up with Email").should("be.visible");

    // Submit the form (we won't actually wait for the API call)
    cy.get("form").submit();

    // Check that the button text changes to indicate loading
    cy.contains("Sending link...").should("exist");
  });
});
