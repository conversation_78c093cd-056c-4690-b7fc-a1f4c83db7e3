/// <reference types="cypress" />

/**
 * E2E tests for PA message functionality.
 *
 * Tests the complete PA message workflow including:
 * - PA message display in chat UI
 * - Real-time message updates via subscriptions
 * - Message ordering and attribution
 * - Accessibility compliance
 * - Mobile responsiveness
 */

describe("PA Message Flow", () => {
  beforeEach(() => {
    // Set environment variable to enable test routes
    cy.window().then(win => {
      win.localStorage.setItem("VITE_TESTING", "true");
    });

    // Intercept GraphQL requests for both user messages and PA messages
    cy.intercept("POST", "/graphql", req => {
      if (req.body.query.includes("sendMessage") && !req.body.query.includes("sendMessageFromPA")) {
        // Mock successful user message sending
        req.reply({
          statusCode: 200,
          body: {
            data: {
              sendMessage: {
                message: {
                  id: "test-user-message-id",
                  conversationId: "test-conversation-id",
                  senderRole: "user",
                  content: { text: req.body.variables.content },
                  timestamp: new Date().toISOString(),
                  metadata: {},
                },
                success: true,
                errorMessage: null,
              },
            },
          },
        });
      } else if (req.body.query.includes("sendMessageFromPA")) {
        // Mock successful PA message sending
        req.reply({
          statusCode: 200,
          body: {
            data: {
              sendMessageFromPA: {
                message: {
                  id: "test-pa-message-id",
                  conversationId: "test-conversation-id",
                  senderRole: "agent",
                  content: {
                    parts: [
                      {
                        type: "text",
                        content: req.body.variables.input.content,
                      },
                    ],
                  },
                  timestamp: new Date().toISOString(),
                  metadata: { source: "ai_service" },
                },
                success: true,
                errorMessage: null,
              },
            },
          },
        });
      }
    }).as("graphqlRequests");

    // Mock subscription for real-time updates
    cy.intercept("GET", "/graphql", req => {
      if (req.url.includes("subscription")) {
        // Mock subscription connection
        req.reply({
          statusCode: 200,
          body: { data: { newMessages: null } },
        });
      }
    }).as("subscribeToMessages");

    // Visit the test chat page
    cy.visit("/test/chat/test-conversation-id");
  });

  describe("PA Message Display (US7.2-TC-06)", () => {
    it("should display PA message with proper attribution and styling", () => {
      // Send a user message to trigger a PA response
      cy.get('[data-testid="message-input"]').should("be.visible").type("Hello PA!");
      cy.get('[data-testid="send-button"]').click();

      // Wait for the user message to be sent and PA response to be generated
      cy.wait(3000); // Increased wait time to ensure PA response is generated

      // Debug: Check what messages are actually in the list
      cy.get('[data-testid="message-list"]').then($list => {
        cy.log("Message list content:", $list.text());
      });

      // Verify PA message appears with correct styling
      cy.get('[data-testid="message-list"]').within(() => {
        // First check if any messages exist
        cy.get("div").should("exist");

        // Check for PA message container
        cy.get(".flex.w-full.mb-4.justify-start").should("exist");

        // Check for PA avatar
        cy.get(".bg-gradient-to-br.from-blue-500.to-purple-600").should("exist");
        cy.contains("PA").should("be.visible");

        // Check for PA attribution
        cy.contains("Personal Assistant").should("be.visible");

        // Check message content
        cy.contains("Thank you for your message! I'm here to help.").should("be.visible");
      });
    });

    it("should display PA message timestamp correctly", () => {
      // First, send a user message to trigger a PA response
      cy.get('[data-testid="message-input"]').should("be.visible").type("Test timestamp");
      cy.get('[data-testid="send-button"]').click();

      // Wait for the simulated PA response
      cy.wait(2000);

      // Verify timestamp formatting
      cy.get('[data-testid="message-list"]').within(() => {
        // Check that timestamp is displayed (format may vary)
        cy.get(".text-xs.opacity-70").should("exist");
      });
    });
  });

  describe("PA Message Chronological Ordering (US7.2-TC-07)", () => {
    it("should display multiple PA messages in timestamp order", () => {
      // This test would require multiple PA messages to be added
      // For now, we'll verify the structure exists for ordering
      cy.get('[data-testid="message-list"]').should("exist");

      // Verify messages are in a container that supports ordering
      cy.get('[data-testid="message-list"]').should("have.class", "space-y-4");
    });
  });

  describe("PA Message Real-time Updates (US7.2-TC-08)", () => {
    it("should handle subscription connection for real-time updates", () => {
      // Verify that the chat interface is set up for real-time updates
      cy.get('[data-testid="message-list"]').should("be.visible");

      // In a real implementation, this would test WebSocket connections
      // For now, we verify the UI structure supports real-time updates
      cy.get(".flex-1.overflow-y-auto").should("exist");
    });
  });

  describe("PA Message Accessibility (US7.2-TC-09)", () => {
    it("should be accessible to screen readers", () => {
      // First, send a user message to trigger a PA response
      cy.get('[data-testid="message-input"]').should("be.visible").type("Accessibility test");
      cy.get('[data-testid="send-button"]').click();

      // Wait for the simulated PA response
      cy.wait(2000);

      // Check for proper ARIA labels and semantic structure
      cy.get('[data-testid="message-list"]').should("have.attr", "data-testid");

      // Verify PA messages have proper semantic structure
      cy.get(".flex.w-full.mb-4.justify-start").within(() => {
        // Check that content is in readable text elements
        cy.get(".text-sm.whitespace-pre-wrap.break-words").should("exist");
      });
    });

    it("should support keyboard navigation", () => {
      // Verify that the chat interface is keyboard accessible
      cy.get('[data-testid="message-input"]').should("be.visible").focus();
      cy.get('[data-testid="message-input"]').should("be.focused");
    });
  });

  describe("PA Message Mobile Responsiveness (US7.2-TC-10)", () => {
    it("should adapt to mobile viewport", () => {
      // Test mobile viewport
      cy.viewport(375, 667); // iPhone SE dimensions

      // First, send a user message to trigger a PA response
      cy.get('[data-testid="message-input"]').should("be.visible").type("Mobile test");
      cy.get('[data-testid="send-button"]').click();

      // Wait for the simulated PA response
      cy.wait(2000);

      // Verify PA message layout adapts to mobile
      cy.get('[data-testid="message-list"]').should("be.visible");
      cy.get(".max-w-\\[70\\%\\]").should("exist"); // Responsive width constraint

      // Verify message content is readable on mobile
      cy.get(".text-sm.whitespace-pre-wrap.break-words").should("be.visible");
    });

    it("should maintain proper spacing on mobile", () => {
      cy.viewport(375, 667);

      // First, send a user message to trigger a PA response
      cy.get('[data-testid="message-input"]').should("be.visible").type("Spacing test");
      cy.get('[data-testid="send-button"]').click();

      // Wait for the simulated PA response
      cy.wait(2000);

      // Verify spacing between messages
      cy.get(".space-y-4").should("exist");
      cy.get(".mb-4").should("exist");
    });
  });

  describe("Error Handling", () => {
    it("should handle PA message display errors gracefully", () => {
      // Test error scenarios
      cy.get('[data-testid="message-list"]').should("be.visible");

      // Verify error handling structure exists
      cy.get(".flex.items-center.justify-center").should("exist");
    });
  });

  describe("Performance", () => {
    it("should render PA messages efficiently", () => {
      // Verify that the chat interface loads quickly
      cy.get('[data-testid="message-list"]').should("be.visible");

      // Check that message rendering doesn't cause layout shifts
      cy.get(".overflow-y-auto").should("exist");
    });
  });
});

/**
 * Additional test scenarios for comprehensive coverage
 */
describe("PA Message Integration", () => {
  beforeEach(() => {
    cy.window().then(win => {
      win.localStorage.setItem("VITE_TESTING", "true");
    });
    cy.visit("/test/chat/test-conversation-id");
  });

  it("should integrate PA messages with user messages", () => {
    // Verify that PA messages and user messages can coexist
    cy.get('[data-testid="message-list"]').should("be.visible");
    cy.get('[data-testid="message-input"]').should("be.visible");
  });

  it("should handle mixed message types in conversation", () => {
    // Send a user message to trigger a PA response
    cy.get('[data-testid="message-input"]').should("be.visible").type("Mixed message test");
    cy.get('[data-testid="send-button"]').click();

    // Wait for the user message to appear first
    cy.get('[data-testid="message-list"]').should("contain", "Mixed message test");

    // Wait for the simulated PA response (1000ms delay + render time)
    cy.wait(2000);

    // Test conversation flow with both user and PA messages
    cy.get('[data-testid="message-list"]').should("exist");

    // Verify PA response appears
    cy.get('[data-testid="message-list"]').should(
      "contain",
      "Thank you for your message! I'm here to help.",
    );

    // Verify different message alignments
    cy.get(".justify-start").should("exist"); // PA messages (left-aligned)
    cy.get(".justify-end").should("exist"); // User messages (right-aligned)
  });
});
