/**
 * End-to-End Tests for Message Sending Flow
 *
 * This test suite covers the complete user journey for sending messages
 * in the A2A Platform chat interface, including:
 * - UI interactions
 * - Form validation
 * - Network requests
 * - Real-time updates
 * - Error handling
 */

describe("Send Message Flow", () => {
  beforeEach(() => {
    // Set environment variable to enable test routes
    cy.window().then(win => {
      // Enable test routes by setting the testing environment variable
      win.localStorage.setItem("VITE_TESTING", "true");
    });

    cy.intercept("POST", "/graphql", req => {
      // Capture for debugging without using Cypress commands
      console.log("=== GRAPHQL REQUEST DEBUG ===");
      console.log("Request body:", req.body);
      console.log("Body type:", typeof req.body);
      console.log("Body keys:", Object.keys(req.body || {}));

      if (req.body && req.body.variables) {
        console.log("Variables:", req.body.variables);
        console.log("Variables keys:", Object.keys(req.body.variables || {}));
        console.log("Content value:", req.body.variables.content);
      }

      if (req.body && req.body.query && req.body.query.includes("sendMessage")) {
        // Extract content using object destructuring - try multiple paths
        const { variables } = req.body;
        let content = "NOT_FOUND";

        const { content: directContent, input } = variables || {};
        if (directContent) {
          content = directContent;
        } else if (input?.content) {
          const { content: inputContent } = input;
          content = inputContent;
        } else {
          // Handle string body case without direct access to req.body type
          const bodyStr = req.body as string;
          if (typeof bodyStr === "string") {
            try {
              const parsed = JSON.parse(bodyStr);
              const { variables: parsedVars } = parsed;
              const { content: parsedContent } = parsedVars || {};
              content = parsedContent || "STRING_PARSE_FAILED";
            } catch (_e) {
              content = "JSON_PARSE_ERROR";
            }
          }
        }

        req.reply({
          statusCode: 200,
          body: {
            data: {
              sendMessage: {
                message: {
                  id: "test-message-id",
                  conversationId: "test-conversation-id",
                  senderRole: "user",
                  content: { text: content },
                  timestamp: new Date().toISOString(),
                  metadata: {},
                },
                success: true,
                errorMessage: null,
              },
            },
          },
        });
      }
    }).as("sendMessage");

    // Visit the test chat page (bypasses authentication)
    cy.visit("/test/chat/test-conversation-id");
  });

  describe("Basic Message Sending", () => {
    it("should send a simple text message successfully", () => {
      const testMessage = "Hello, this is a test message!";

      // Type message in input
      cy.get('[data-testid="message-input"]').should("be.visible").type(testMessage);

      // Verify character count updates
      cy.get('[data-testid="character-count"]').should(
        "contain",
        `${testMessage.length}/4000 characters`,
      );

      // Send button should be enabled
      cy.get('[data-testid="send-button"]').should("not.be.disabled");

      // Send the message
      cy.get('[data-testid="send-button"]').click();

      // Verify GraphQL request was made and check request structure
      cy.wait("@sendMessage").then(interception => {
        const { body } = interception.request;

        // Enhanced debugging - log the entire request structure
        console.log("=== FULL REQUEST DEBUG ===");
        console.log("Full body:", JSON.stringify(body, null, 2));
        console.log("Body type:", typeof body);
        console.log("Body keys:", Object.keys(body));

        if (body.variables) {
          console.log("Variables:", JSON.stringify(body.variables, null, 2));
          console.log("Variables keys:", Object.keys(body.variables));
          console.log("Variables type:", typeof body.variables);
        }

        // Also check if it's a different structure
        if (body.query) {
          console.log("Query:", body.query);
        }

        if (typeof body === "string") {
          console.log("Body is string, trying to parse:", body);
          try {
            const parsedBody = JSON.parse(body);
            console.log("Parsed body:", parsedBody);
          } catch (_e) {
            console.log("Could not parse body as JSON");
          }
        }

        console.log("=== END REQUEST DEBUG ===");

        // Test that variables exist and have expected structure
        expect(body).to.have.property("variables");
        const { variables } = body;
        expect(variables).to.have.property("conversationId", "test-conversation-id");

        // Test content - this should work now
        expect(variables).to.have.property("content");
        expect(variables.content).to.equal(testMessage);
      });

      // Verify input is cleared after sending
      cy.get('[data-testid="message-input"]').should("have.value", "");

      // Verify character count resets
      cy.get('[data-testid="character-count"]').should("contain", "0/4000 characters");

      // Verify message appears in chat
      cy.get('[data-testid="message-list"]').should("contain", testMessage);
    });

    it("should send message using Enter key", () => {
      const testMessage = "Message sent with Enter key";

      cy.get('[data-testid="message-input"]').type(testMessage).type("{enter}");

      cy.wait("@sendMessage");

      cy.get('[data-testid="message-list"]').should("contain", testMessage);
    });

    it("should not send message with Shift+Enter", () => {
      cy.get("@sendMessage.all").then(initialRequests => {
        const initialCount = initialRequests.length;
        const testMessage = "Line 1{shift+enter}Line 2";

        cy.get('[data-testid="message-input"]').type(testMessage);

        // Wait a moment to ensure no request is made
        cy.wait(1000);

        // Should not trigger send - check that no additional requests were made
        cy.get("@sendMessage.all").should("have.length", initialCount);

        // Input should contain both lines
        cy.get('[data-testid="message-input"]').should("contain.value", "Line 1\nLine 2");
      });
    });
  });

  describe("Input Validation", () => {
    it("should prevent sending empty messages", () => {
      // Get initial request count
      cy.get("@sendMessage.all").then(initialRequests => {
        const initialCount = initialRequests.length;
        console.log("=== EMPTY MESSAGE TEST ===");
        console.log("Initial request count:", initialCount);

        // Send button should be enabled but clicking should show validation error
        cy.get('[data-testid="send-button"]').should("not.be.disabled");

        // Ensure input is truly empty
        cy.get('[data-testid="message-input"]').should("have.value", "");

        console.log("About to click send button with empty message");

        // Try to submit empty form
        cy.get('[data-testid="send-button"]').click();

        // Should show validation error
        cy.get('[data-testid="error-message"]')
          .should("be.visible")
          .and("contain", "Message cannot be empty");

        console.log("Error message should be visible now");

        // Wait a moment then check no additional GraphQL request was made
        cy.wait(1000);
        cy.get("@sendMessage.all").then(finalRequests => {
          console.log("Final request count:", finalRequests.length);
          console.log("Expected:", initialCount, "Actual:", finalRequests.length);
          expect(finalRequests).to.have.length(initialCount);
        });
      });
    });

    it("should prevent sending whitespace-only messages", () => {
      cy.get("@sendMessage.all").then(initialRequests => {
        const initialCount = initialRequests.length;

        cy.get('[data-testid="message-input"]').type("   \t\n   ");

        cy.get('[data-testid="send-button"]').click();

        cy.get('[data-testid="error-message"]')
          .should("be.visible")
          .and("contain", "Message cannot be empty");

        // Wait a moment then check no additional GraphQL request was made
        cy.wait(1000);
        cy.get("@sendMessage.all").should("have.length", initialCount);
      });
    });

    it("should prevent sending messages exceeding character limit", () => {
      const longMessage = "a".repeat(4001);

      // Use native value setter to properly trigger React state updates
      cy.get('[data-testid="message-input"]').then($input => {
        const input = $input[0] as HTMLTextAreaElement;
        const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
          window.HTMLTextAreaElement.prototype,
          "value",
        )?.set;

        if (nativeInputValueSetter) {
          nativeInputValueSetter.call(input, longMessage);

          // Trigger React's onChange event
          const event = new Event("input", { bubbles: true });
          input.dispatchEvent(event);
        }
      });

      // Character count should show over limit
      cy.get('[data-testid="character-count"]')
        .should("contain", "4001/4000 characters")
        .and("have.class", "text-red-500");

      cy.get("@sendMessage.all").then(initialRequests => {
        const initialCount = initialRequests.length;

        cy.get('[data-testid="send-button"]').click();

        cy.get('[data-testid="error-message"]')
          .should("be.visible")
          .and("contain", "Message cannot exceed 4000 characters");

        // Wait a moment then check no additional GraphQL request was made
        cy.wait(1000);
        cy.get("@sendMessage.all").should("have.length", initialCount);
      });
    });
  });

  describe("Special Characters and Unicode", () => {
    it("should handle unicode characters correctly", () => {
      const unicodeMessage = "Hello 🚀 测试 العربية русский 🎉";

      cy.get('[data-testid="message-input"]').type(unicodeMessage);

      cy.get('[data-testid="character-count"]').should(
        "contain",
        `${unicodeMessage.length}/4000 characters`,
      );

      cy.get('[data-testid="send-button"]').click();

      cy.wait("@sendMessage");

      cy.wait("@sendMessage").then(interception => {
        const { body } = interception.request;
        const { variables } = body;
        expect(variables.content).to.equal(unicodeMessage);
      });

      cy.get('[data-testid="message-list"]').should("contain", unicodeMessage);
    });

    it("should handle special characters correctly", () => {
      const specialMessage = "Special: @#$%^&*()_+-=[]{}|;':\",./<>?";

      // Type the message to trigger React state updates
      cy.get('[data-testid="message-input"]').clear().type(specialMessage);

      cy.get('[data-testid="send-button"]').click();

      cy.wait("@sendMessage");

      cy.get('[data-testid="message-list"]').should("contain", specialMessage);
    });
  });

  describe("Error Handling", () => {
    it("should handle network errors gracefully", () => {
      // Override intercept to simulate network error
      cy.intercept("POST", "/graphql", {
        statusCode: 500,
        body: { error: "Internal Server Error" },
      }).as("sendMessageError");

      cy.get('[data-testid="message-input"]').type("This message will fail");

      cy.get('[data-testid="send-button"]').click();

      cy.wait("@sendMessageError");

      // Should show error message
      cy.get('[data-testid="error-message"]')
        .should("be.visible")
        .and("contain", "Failed to send message");

      // Message should remain in input for retry
      cy.get('[data-testid="message-input"]').should("have.value", "This message will fail");
    });

    it("should handle GraphQL errors gracefully", () => {
      // Override intercept to simulate GraphQL error
      cy.intercept("POST", "/graphql", {
        statusCode: 200,
        body: {
          data: {
            sendMessage: {
              message: null,
              success: false,
              errorMessage: "Conversation not found",
            },
          },
        },
      }).as("sendMessageGraphQLError");

      cy.get('[data-testid="message-input"]').type("This will return a GraphQL error");

      cy.get('[data-testid="send-button"]').click();

      cy.wait("@sendMessageGraphQLError");

      cy.get('[data-testid="error-message"]')
        .should("be.visible")
        .and("contain", "Conversation not found");
    });
  });

  describe("User Experience", () => {
    it("should disable send button during submission", () => {
      // Simulate slow network
      cy.intercept("POST", "/graphql", {
        delay: 1000,
        statusCode: 200,
        body: {
          data: {
            sendMessage: {
              message: {
                id: "test-message-id",
                conversationId: "test-conversation-id",
                senderRole: "user",
                content: { text: "Slow message" },
                timestamp: new Date().toISOString(),
                metadata: {},
              },
              success: true,
              errorMessage: null,
            },
          },
        },
      }).as("slowSendMessage");

      cy.get('[data-testid="message-input"]').type("Slow message");

      cy.get('[data-testid="send-button"]').click();

      // Button should be disabled during submission
      cy.get('[data-testid="send-button"]').should("be.disabled");

      // Should show loading state
      cy.get('[data-testid="send-button"]').should("contain", "Sending...");

      cy.wait("@slowSendMessage");

      // Button should be enabled again after completion
      cy.get('[data-testid="send-button"]').should("not.be.disabled").and("contain", "Send");
    });

    it("should focus input after sending message", () => {
      cy.get('[data-testid="message-input"]').type("Test focus behavior");

      cy.get('[data-testid="send-button"]').click();

      cy.wait("@sendMessage");

      // Wait for the requestAnimationFrame and check focus
      cy.get('[data-testid="message-input"]').should("be.focused");
    });

    it("should trim whitespace from message content", () => {
      const messageWithWhitespace = "   Hello World   ";
      const trimmedMessage = "Hello World";

      cy.get('[data-testid="message-input"]').type(messageWithWhitespace);

      cy.get('[data-testid="send-button"]').click();

      cy.wait("@sendMessage");

      cy.wait("@sendMessage").then(interception => {
        const { body } = interception.request;
        const { variables } = body;
        expect(variables.content).to.equal(trimmedMessage);
      });

      cy.get('[data-testid="message-list"]').should("contain", trimmedMessage);
    });
  });
});
