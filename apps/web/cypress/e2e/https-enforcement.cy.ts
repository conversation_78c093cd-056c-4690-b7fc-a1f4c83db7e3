/**
 * End-to-end tests for HTTPS enforcement.
 *
 * These tests validate that all client-backend communication uses HTTPS/WSS,
 * ensuring secure data transmission and preventing mixed content issues.
 */

describe("HTTPS Enforcement", () => {
  beforeEach(() => {
    // Visit the home page using HTTPS
    cy.visit("/");
  });

  it("should load page over HTTPS", () => {
    // Verify the current page URL uses HTTPS protocol
    cy.url().should("match", /^https:\/\//);

    // Check that the page loads successfully
    cy.get("body").should("be.visible");
  });

  it("should enforce HSTS header", () => {
    // Test the root path which should return the HSTS header from Vite dev server
    cy.request({
      url: "/",
      failOnStatusCode: false,
    }).then(response => {
      // Check for HSTS header from development server
      expect(response.headers["strict-transport-security"]).to.include("max-age=31536000");
      expect(response.headers["strict-transport-security"]).to.include("includeSubDomains");
      expect(response.headers["strict-transport-security"]).to.include("preload");
    });
  });

  it("should use HTTPS for all API requests", () => {
    // Intercept all network requests
    cy.intercept("**").as("allRequests");

    // Navigate through the app to trigger API requests
    cy.get("body").should("be.visible");

    // Wait a moment for any initial requests to complete
    cy.wait(1000);

    // Check that all requests use HTTPS (or are relative URLs which inherit the parent's protocol)
    cy.get("@allRequests.all").then(requests => {
      requests.forEach(request => {
        // Skip checking requests to Cypress itself or data URLs
        if (
          request.request.url.includes("cypress") ||
          request.request.url.startsWith("data:") ||
          request.request.url.includes("__cypress")
        ) {
          return;
        }

        // For absolute URLs, check they use HTTPS
        if (request.request.url.startsWith("http")) {
          expect(request.request.url).to.match(/^https:\/\//);
        }
      });
    });
  });
});
