// ***********************************************************
// This example support/e2e.js is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
// import './commands'

// Alternatively you can use CommonJS syntax:
// require('./commands')

// Add any global Cypress configuration here

// Global setup for Apollo Client in Cypress tests
beforeEach(() => {
  // Load Apollo Client error messages to prevent go.apollo.dev URLs in error messages
  cy.window().then(win => {
    // Only load if not already loaded
    if (!win.__APOLLO_DEV_MESSAGES_LOADED__) {
      // Set a flag to prevent multiple loads
      win.__APOLLO_DEV_MESSAGES_LOADED__ = true;

      // Load Apollo Client dev messages in the browser context
      cy.then(() => {
        return import("@apollo/client/dev")
          .then(({ loadErrorMessages, loadDevMessages }) => {
            loadDevMessages();
            loadErrorMessages();
            console.log("Apollo Client dev messages loaded in Cypress");
          })
          .catch(error => {
            console.warn("Failed to load Apollo Client dev messages in Cypress:", error);
          });
      });
    }
  });
});
