# GraphQL Schema Management

This document explains the hybrid approach for managing GraphQL schema in the A2A Platform frontend.

## Overview

We use a hybrid approach for GraphQL schema management:
- **Development**: Schema is fetched from the live backend
- **Testing/CI**: Schema is loaded from a committed file (`src/graphql/schema.graphql`)

This approach provides:
- ✅ No backend dependency for tests
- ✅ Faster test execution
- ✅ Deterministic test results
- ✅ CI/CD compatibility
- ✅ Offline development capability

## Configuration Files

### `codegen.yml` (Development)
- Fetches schema from live backend (`https://localhost:8000/graphql`)
- Used during development when backend is running
- Provides real-time schema updates

### `codegen.test.yml` (Testing/CI)
- Uses static schema file (`./src/graphql/schema.graphql`)
- Used for tests and CI builds
- No network calls required

### `codegen-introspection.yml` (Schema Generation)
- Used to generate the `schema.graphql` file from backend
- Only run when schema needs updating

## Scripts

### Update Schema
```bash
# Update from default endpoint (localhost)
bun run schema:update

# Update from specific endpoint
bun run schema:update https://staging.example.com/graphql

# Update using environment variable
VITE_GRAPHQL_API_URL=https://api.example.com/graphql bun run schema:update
```

### Generate Types
```bash
# Development (from live backend)
bun run codegen

# Testing (from schema file)
bun run codegen:test

# Watch mode (development only)
bun run codegen:watch
```

## CI/CD Usage

In CI environments, the schema file is used automatically:

```yaml
# Example GitHub Actions
- name: Generate GraphQL Types
  run: bun run codegen:test
  working-directory: apps/web
```

To update the schema in CI:

```bash
# Pass the endpoint as argument
./scripts/update-graphql-schema.sh "$GRAPHQL_ENDPOINT"
```

## When to Update Schema

Update the schema file when:
1. Backend GraphQL schema changes
2. New types/fields are added
3. Before releasing to ensure tests use latest schema

## Best Practices

1. **Commit schema changes**: Always commit `schema.graphql` when it changes
2. **Review schema diffs**: Carefully review schema changes in PRs
3. **Keep schema current**: Update before major releases
4. **Use test config in CI**: Always use `codegen:test` in CI/CD pipelines

## Troubleshooting

### Schema out of date
```bash
# Update the schema
bun run schema:update
```

### Tests failing due to schema mismatch
```bash
# Regenerate types with test config
bun run codegen:test
```

### Backend not accessible
```bash
# Use the test configuration
bun run codegen:test
```