require('@testing-library/jest-dom');

// Polyfill for TextEncoder/TextDecoder which is required by react-router-dom
if (typeof TextEncoder === 'undefined') {
  global.TextEncoder = require('util').TextEncoder;
  global.TextDecoder = require('util').TextDecoder;
}

// Configure React Testing Library
require('@testing-library/react');
const { configure } = require('@testing-library/react');

// Configure React Testing Library with proper settings
configure({
  // Automatically cleanup after each test
  asyncUtilTimeout: 5000,
  // Disable strict query suggestions for now
  throwSuggestions: false,
});

// Import React for act() configuration
const React = require('react');

// Configure React 18 act() behavior
// Suppress specific act() warnings that are not actionable
const originalConsoleError = console.error;
console.error = (...args) => {
  // Filter out specific React act() warnings that are not actionable
  if (
    typeof args[0] === 'string' &&
    (
      args[0].includes('Warning: An invalid form control') ||
      args[0].includes('Warning: You seem to have overlapping act() calls') ||
      args[0].includes('Warning: React does not recognize the')
    )
  ) {
    return;
  }
  
  originalConsoleError(...args);
};

// Global setup for proper jsdom environment
Object.defineProperty(global, 'IS_REACT_ACT_ENVIRONMENT', {
  writable: true,
  value: true,
});

// Setup jsdom environment globals
Object.defineProperty(window, 'ResizeObserver', {
  writable: true,
  value: jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
  })),
});

// Mock IntersectionObserver
Object.defineProperty(window, 'IntersectionObserver', {
  writable: true,
  value: jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
  })),
});

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock window.location for tests
Object.defineProperty(window, 'location', {
  value: {
    origin: 'https://localhost:5173',
    href: 'https://localhost:5173',
  },
  writable: true,
});

// Mock react-router-dom
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  BrowserRouter: ({ children }) => children,
  Link: ({ children, to }) => {
    const React = require('react');
    return React.createElement('a', { href: to }, children);
  },
}));
