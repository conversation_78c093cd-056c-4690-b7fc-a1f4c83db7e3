{"name": "web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"build-storybook": "bun storybook build", "build": "bun vite build", "codegen:watch": "graphql-codegen --watch", "codegen": "graphql-codegen", "codegen:test": "graphql-codegen --config codegen.test.yml", "schema:update": "./scripts/update-graphql-schema.sh", "dev:storybook": "bun storybook dev -p 6006", "dev": "bun vite", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx}\"", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx}\"", "format:all": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md,yaml,yml,html}\"", "format:all:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md,yaml,yml,html}\"", "lint:check": "eslint . --max-warnings 0", "lint:fix": "eslint . --fix", "code:fix": "bun run format && bun run lint:fix", "code:fix:all": "bun run format:all && bun run lint:fix", "code:check": "bun run format:check && bun run lint:check", "code:check:all": "bun run format:all:check && bun run lint:check", "preview": "bun vite preview", "start:prod": "serve -s dist -l 80 --no-clipboard -C", "test:e2e:open": "bun cypress open", "test:e2e": "bun cypress run", "test:e2e:jest": "bun jest --config=jest.e2e.config.js", "test:e2e:graphql": "bun jest --config=jest.e2e.config.js e2e/graphql-codegen", "test:watch": "bun jest --watch", "test": "bun jest", "type-check": "bun tsc --noEmit --project tsconfig.app.json"}, "dependencies": {"@apollo/client": "^3.13.8", "@clerk/clerk-react": "^5.31.6", "@graphql-typed-document-node/core": "^3.2.0", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/vite": "^4.1.7", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "graphql": "^16.11.0", "lucide-react": "^0.511.0", "postcss": "^8.5.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "6.26.2", "serve": "^14.2.3", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7", "zustand": "^5.0.5"}, "devDependencies": {"@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@chromatic-com/storybook": "^4.0.0", "@clerk/testing": "^1.7.3", "@eslint/js": "^9.25.0", "@graphql-codegen/cli": "^5.0.7", "@graphql-codegen/typed-document-node": "^5.1.1", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.1", "@mdx-js/mdx": "^3.1.0", "@mdx-js/react": "^3.1.0", "@storybook/addon-a11y": "^9.0.4", "@storybook/addon-docs": "^9.0.6", "@storybook/addon-storysource": "^8.6.14", "@storybook/addon-vitest": "^9.0.6", "@storybook/react-vite": "^9.0.6", "@tailwindcss/postcss": "^4.1.7", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/node": "^22.15.30", "@types/node-fetch": "^2.6.12", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.4.1", "@vitest/browser": "^3.1.4", "@vitest/coverage-v8": "^3.1.4", "babel-jest": "^29.7.0", "cypress": "^14.4.0", "dotenv": "^16.5.0", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-storybook": "^9.0.6", "globals": "^16.0.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "js-yaml": "^4.1.0", "node-fetch": "^3.3.2", "playwright": "^1.52.0", "prettier": "^3.4.2", "storybook": "9.0.6", "storybook-addon-apollo-client": "^8.1.2", "terser": "^5.40.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vitest": "^3.1.4"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}