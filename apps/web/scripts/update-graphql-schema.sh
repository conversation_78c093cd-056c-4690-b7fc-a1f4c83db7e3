#!/bin/bash
#
# Update GraphQL schema from backend
# This script fetches the latest schema from the backend and saves it to a file
#
# Usage:
#   ./scripts/update-graphql-schema.sh [GRAPHQL_ENDPOINT]
#
# If no endpoint is provided, it will use the VITE_GRAPHQL_API_URL environment variable
# or default to https://localhost:8000/graphql
#

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Determine GraphQL endpoint
if [ -n "$1" ]; then
  GRAPHQL_ENDPOINT="$1"
elif [ -n "$VITE_GRAPHQL_API_URL" ]; then
  GRAPHQL_ENDPOINT="$VITE_GRAPHQL_API_URL"
else
  GRAPHQL_ENDPOINT="https://localhost:8000/graphql"
fi

echo -e "${YELLOW}Updating GraphQL schema from: ${GRAPHQL_ENDPOINT}${NC}"

# Check if backend is running (skip SSL verification for local dev)
if ! curl -s -o /dev/null -w "%{http_code}" "$GRAPHQL_ENDPOINT" -k | grep -q "200\|405"; then
  echo -e "${RED}Error: Backend is not responding at ${GRAPHQL_ENDPOINT}${NC}"
  echo "Please ensure the backend is running and accessible."
  exit 1
fi

# Create temporary config with the dynamic endpoint
cat > codegen-introspection-temp.yml << EOF
overwrite: true
schema: 
  - '${GRAPHQL_ENDPOINT}':
      headers:
        'User-Agent': 'GraphQL-Codegen-Introspection'
generates:
  ./src/graphql/schema.graphql:
    plugins:
      - schema-ast
    config:
      commentDescriptions: true
      includeDirectives: true
EOF

# Run GraphQL codegen with introspection config
echo -e "${YELLOW}Running GraphQL introspection...${NC}"
NODE_TLS_REJECT_UNAUTHORIZED=0 bun run graphql-codegen --config codegen-introspection-temp.yml

if [ $? -eq 0 ]; then
  # Clean up temporary config
  rm -f codegen-introspection-temp.yml
  
  echo -e "${GREEN}✓ Schema updated successfully!${NC}"
  echo -e "${GREEN}  Location: src/graphql/schema.graphql${NC}"
  
  # Show schema stats
  SCHEMA_SIZE=$(wc -c < src/graphql/schema.graphql)
  SCHEMA_LINES=$(wc -l < src/graphql/schema.graphql)
  echo -e "${GREEN}  Size: ${SCHEMA_SIZE} bytes, ${SCHEMA_LINES} lines${NC}"
else
  # Clean up temporary config
  rm -f codegen-introspection-temp.yml
  
  echo -e "${RED}✗ Failed to update schema${NC}"
  exit 1
fi