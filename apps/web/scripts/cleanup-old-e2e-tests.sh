#!/bin/bash
#
# Cleanup old e2e tests that execute graphql-codegen
# These tests are no longer needed with the static schema approach
#

set -e

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${YELLOW}Cleaning up old GraphQL codegen e2e tests...${NC}"

# List of test files that execute codegen and are no longer needed
OLD_TESTS=(
  "network-failure-recovery.test.ts"
  "advanced-error-recovery.test.ts"  
  "performance-scalability.test.ts"
  "watch-mode.test.ts"
  "codegen-validation.test.ts"
  "cross-platform-compatibility.test.ts"
  "environment-variable-handling.test.ts"
  "cicd-integration.test.ts"
  "schema-regeneration.test.ts"
  "developer-experience.test.ts"
)

# Rename to .old so they don't run but are preserved
for test in "${OLD_TESTS[@]}"; do
  if [ -f "e2e/graphql-codegen/$test" ]; then
    mv "e2e/graphql-codegen/$test" "e2e/graphql-codegen/$test.old"
    echo -e "${GREEN}✓ Renamed $test to $test.old${NC}"
  fi
done

echo -e "${GREEN}✓ Cleanup complete!${NC}"
echo -e "${YELLOW}Old tests have been renamed with .old extension${NC}"
echo -e "${YELLOW}They can be restored if needed or deleted later${NC}"