FROM oven/bun:1.2.14-alpine

# Install dependencies for development and Cypress E2E testing
RUN apk add --no-cache \
    bash \
    curl \
    git \
    xvfb-run \
    gtk+3.0-dev \
    nss \
    alsa-lib

# Create non-root user FIRST (before copying files)
RUN addgroup --system --gid 1001 bunjs
RUN adduser --system --uid 1001 bunjs

WORKDIR /app

# Copy package files with correct ownership
COPY --chown=bunjs:bunjs package.json bun.lock ./

# Install dependencies with frozen lockfile
RUN bun install --frozen-lockfile

# Copy configuration files with correct ownership
COPY --chown=bunjs:bunjs tsconfig*.json vite.config.ts ./
COPY --chown=bunjs:bunjs postcss.config.js tailwind.config.js eslint.config.js ./

# Copy source code and assets with correct ownership
COPY --chown=bunjs:bunjs src/ ./src/
COPY --chown=bunjs:bunjs public/ ./public/
COPY --chown=bunjs:bunjs index.html ./

# Create ssl-certs directory with correct ownership
RUN mkdir -p /app/ssl-certs && chown bunjs:bunjs /app/ssl-certs

# Create cache directory with correct ownership for Storybook
RUN mkdir -p /app/node_modules/.cache && chown -R bunjs:bunjs /app/node_modules/.cache

# Copy entrypoint scripts with correct ownership
COPY --chown=bunjs:bunjs entrypoint.sh enhanced-entrypoint.sh enhanced-start-storybook.sh fix-permissions.sh ./
RUN chmod +x ./entrypoint.sh ./enhanced-entrypoint.sh ./enhanced-start-storybook.sh ./fix-permissions.sh

# Set development environment
ENV NODE_ENV=development

# Expose the Vite dev server port
EXPOSE 5173

# Rest of Dockerfile remains the same...
