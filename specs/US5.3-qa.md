## US5.3: Store Objectives Defined by User - QA Perspective

> As the Personal Assistant, I want to store objectives defined by the user via chat in the `assistant_objectives` table, so that these goals can be tracked and referenced.

-----

### High-Level Objective

  - Ensure the system can reliably store objectives defined by users via chat into the `assistant_objectives` table, making them available for tracking and future reference by the Personal Assistant and potentially the user.

-----

### Mid-Level Objectives

  - **Verify Record Creation:** Confirm that when a user defines an objective in chat (e.g., "Help me learn Spanish"), and the Personal Assistant (PA) identifies and confirms this as an objective, a new record is successfully created in the `assistant_objectives` table. This record must contain the accurate objective text and be correctly linked to the assistant (and by extension, the user).
  - **Test Objective Tracking:** Validate that the storage mechanism allows for the effective tracking of these objectives. This includes checking that necessary fields for tracking (e.g., `status`, `created_at`, `updated_at`, `assistant_id`) are present and correctly populated.
  - **Test Objective Referencing:** Confirm that stored objectives can be reliably referenced by the Personal Assistant. This involves testing scenarios where the PA needs to recall or use these objectives. Future testing may involve user referencing if that functionality is added.

-----

### Implementation Notes (QA Focus)

  - **Important technical details for Testing**:
      - **Data Accuracy**: Test cases must verify that the `objective_text` is stored exactly as defined by the user, including special characters, long strings, and various languages (if applicable).
      - **Correct Linking**: Ensure the `assistant_id` in the `assistant_objectives` table correctly links to the `assistants` table and, by extension, the correct user. Test with multiple users and assistants.
      - **Prerequisite US1.7**: The functionality of identifying and confirming an objective (US1.7) is a critical prerequisite. Test this integration point thoroughly. Failures in US1.7 will directly impact US5.3.
      - **Database Schema Conformance**: All database interactions must conform to the defined schema for `assistant_objectives`[cite: 479], including `id (UUID, PK)`, `assistant_id (UUID, FK to assistants)`, `objective_text (TEXT, NOT NULL)`, `status (TEXT, DEFAULT 'active')`, `created_at`, `updated_at`, `completed_at`, `metadata (JSONB)`.
  - **Dependencies and requirements from a QA perspective**:
      - **Stable `assistant_objectives` Table**: The `assistant_objectives` table schema must be finalized, migrated, and accessible in the test environment.
      - **Implemented US1.7**: US1.7 (User defines objective in chat) must be implemented and stable. Test cases for US5.3 will depend on the output/trigger from US1.7.
      - **Backend Logic Availability**: Backend logic for objective extraction and storage must be deployed to the test environment.
  - **Coding standards to follow (QA verification points)**:
      - **Python Backend Standards**: Verify adherence through code reviews or static analysis if part of QA scope.
      - **Secure and Efficient DB Interactions**: Test for potential SQL injection vulnerabilities (if applicable, though ORM reduces this risk). Performance test database interactions under load, especially objective creation.
  - **Other technical guidance (QA considerations)**:
      - **Future Needs Testing (Exploratory)**: While not in scope for this US, perform exploratory testing considering potential future needs like objective status changes (active, completed, cancelled) or linking objectives to tasks. Note any potential issues or limitations.
      - **Text Length Validation**: Test the `objective_text` field with various lengths, including very long strings, to ensure it handles them gracefully without truncation or errors. Define acceptable limits if any.
      - **Error Handling**: Test how the system handles errors during objective storage (e.g., database connection issues, invalid data). Ensure graceful error handling and appropriate logging.

-----

### Context (QA Environment)

**Beginning context**

  - Test environment has a deployed version of the backend.
  - The `assistant_objectives` table schema is defined and migrated in the test database[cite: 479].
  - Test data for users and assistants exists in the test database.
  - US1.7 functionality (objective identification in chat) is available and testable in the environment.
  - Access to backend logs and database for verification.

**Ending context**

  - `apps/backend/src/a2a_platform/db/models/assistant_objective.py`: SQLAlchemy model for `assistant_objectives` table is confirmed to match the schema.
  - `apps/backend/src/a2a_platform/schemas/objective_schemas.py`: Pydantic schemas for objective data are verified.
  - `apps/backend/src/a2a_platform/services/objective_service.py` (or updated `assistant_service.py`): Contains logic to create objective records, and this logic is covered by tests.
  - Backend logic successfully calls the objective service to store objectives identified via chat.
  - Unit and integration tests for objective storage functionality are written, passing, and provide adequate coverage. QA can review these tests for completeness.

-----

### Low-Level Tasks (QA Test Cases & Verification Plan)

1.  **Define/Verify `assistant_objectives` Table Model and Schema**

      * **Test Case QA1.1**: Verify `assistant_objectives` table schema in the test database matches the specification from "VEDAVIVI: MVP Backend Technical Plan v1.5" (Source 479).
          * **Steps**: Inspect the table structure in the test DB.
          * **Expected**: Columns `id (UUID, PK)`, `assistant_id (UUID, FK to assistants)`, `objective_text (TEXT, NOT NULL)`, `status (TEXT, DEFAULT 'active')`, `created_at`, `updated_at`, `completed_at`, `metadata (JSONB)` exist with correct types and constraints.
      * **Test Case QA1.2**: Verify the SQLAlchemy model (`AssistantObjective` in `assistant_objective.py`) accurately reflects the DB schema.
          * **Steps**: Review the model definition.
          * **Expected**: Model fields align with table columns and types.
      * **Test Case QA1.3**: Verify Pydantic schemas in `objective_schemas.py` for creating and reading objectives correctly include and validate fields like `assistant_id`, `objective_text`, `status`, and timestamps.
          * **Steps**: Review Pydantic schemas. Test with sample valid and invalid data.
          * **Expected**: Schemas correctly serialize and validate data.

2.  **Implement Objective Storage Service Logic**

      * **Test Case QA2.1 (Unit Test Review)**: Review unit tests for `add_assistant_objective` function in `objective_service.py`.
          * **Expected**: Tests cover successful objective creation, correct data mapping (`assistant_id`, `objective_text`), default status assignment (e.g., 'active'), and return of the created object.
      * **Test Case QA2.2 (Error Handling - Unit Test Review)**: Verify unit tests cover database error handling (e.g., connection errors, constraint violations if simulated).
          * **Expected**: Service appropriately handles and logs DB errors.
      * **Test Case QA2.3 (Functional)**: Manually trigger (if an admin interface exists) or use an API testing tool to call the service logic directly (if exposed) with valid `assistant_id` and `objective_text`.
          * **Steps**: Provide valid inputs.
          * **Expected**: A new record is created in `assistant_objectives` with correct data. The function returns the created objective.
      * **Test Case QA2.4 (Functional - Invalid Input)**: Call service logic with invalid `assistant_id` (e.g., non-existent).
          * **Steps**: Provide invalid `assistant_id`.
          * **Expected**: Appropriate error handling (e.g., exception raised, error logged, no record created).

3.  **Integrate Objective Storage into Chat Processing Logic**

      * **Test Case QA3.1 (Integration)**: Simulate a user defining an objective in chat that is identified and confirmed by US1.7 logic.
          * **Steps**: User A (linked to Assistant A) sends a chat message like "My goal is to read one book this month." Assume US1.7 confirms this.
          * **Expected**: The `add_assistant_objective` service function is called. A new record appears in `assistant_objectives` with `objective_text` = "My goal is to read one book this month." and `assistant_id` = Assistant A's ID.
      * **Test Case QA3.2 (Integration - Different User)**: Repeat QA3.1 with User B (linked to Assistant B).
          * **Steps**: User B sends a chat message "I want to save $500." Assume US1.7 confirms.
          * **Expected**: A new record in `assistant_objectives` linked to Assistant B, with the correct objective text. No impact on User A's objectives.
      * **Test Case QA3.3 (Integration - Objective Confirmation Failure)**: Simulate a scenario where US1.7 identifies a potential objective, but the confirmation step fails (e.g., user clarifies it's not an objective).
          * **Steps**: User sends "I might want to learn guitar." User then says "Actually, not right now."
          * **Expected**: The `add_assistant_objective` service function is NOT called. No new record is created.

4.  **Write Unit Tests for Objective Storage Service (QA Review)**

      * **Verification QA4.1**: Review the developer-written unit tests for `add_assistant_objective`.
          * **Checklist**:
              * Is the database session correctly mocked?
              * Is it verified that `session.add()` and `session.commit()` are called with the correct objective object?
              * Is the assignment of `assistant_id`, `objective_text`, and default `status` verified?
              * Is the structure and content of the returned objective object validated?
              * Are different `objective_text` contents tested (empty, long, special characters)?
              * Are database errors (e.g., `IntegrityError`) mocked and handled?
      * **Expected**: Tests are comprehensive and cover points in the checklist.

5.  **Write Integration Test for Objective Storage Flow (QA Review & Augmentation)**

      * **Verification QA5.1**: Review the developer-written integration test.
          * **Checklist**:
              * Does the test simulate US1.7 output correctly (e.g., providing `assistant_id` and `objective_text` to the integration point)?
              * Does it use a real test database?
              * Does it verify the actual database record's content (`objective_text`, `assistant_id`, `status`) after the flow?
              * Is data cleanup performed after the test?
      * **Test Case QA5.2 (QA-driven)**: If not covered by dev tests, QA to design and request/execute an integration test simulating a scenario where the chat processing logic fails *after* US1.7 identification but *before* calling `add_assistant_objective`.
          * **Expected**: No objective is stored; system handles the error gracefully.
      * **Test Case QA5.3 (QA-driven - Load)**: If feasible, run multiple objective creation events in rapid succession for the same or different users.
          * **Expected**: All objectives are stored correctly without race conditions or data corruption. Default status is consistently applied.

6.  **Documentation Update (If Necessary) (QA Review)**

      * **Verification QA6.1**: Review any updated backend documentation.
          * **Checklist**:
              * Is the data flow for storing objectives from chat clearly explained?
              * Is the `assistant_objectives` table and its relevant fields mentioned?
              * Are the key services/modules involved (`objective_service.py`, chat processing logic) identified?
      * **Expected**: Documentation is clear, accurate, and helpful for understanding the objective storage mechanism.
