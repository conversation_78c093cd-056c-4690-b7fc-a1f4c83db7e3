# IaC with Terraform and GitHub Actions
> Ingest the information from this file, implement the Low-Level Tasks, and generate the code that will satisfy the High and Mid-Level Objectives.

## High-Level Objective

Align with Vedavivi's MVP strategy of "simpler and cost-effective" while establishing a robust and scalable foundation.

## Mid-Level Objective

* Provision and manage defined GCP resources using Terraform.
* Support isolated dev, staging, and production environments.
* Automate staging deployments and Pull Request (PR) validation via GitHub Actions.
* Enable secure, manually approved deployments to production via GitHub Actions.
* Securely manage GCP credentials using Workload Identity Federation (WIF).
* Store and manage Terraform state securely and reliably in GCS.
* OIDC provider with local JWKs: Create OIDC JSON Web Key Sets (JWKS) to upload to the pool
* Terraform tests should only run if changes are detected
* Ensure all new and updated workflows are tested in all supported environments (dev, staging, production) for both fresh and pre-existing resources
* Deploy resources for postgres, redis, artifact registry, and compute for the containers.

## Implementation Notes

* **Environments**: `dev` (optional/ephemeral for feature branches), `staging`, `production`.
* **Team Structure**: 2 engineers.
* **Terraform State Management**: New setup, Google Cloud Storage (GCS) to be used.
* **Initial GCP Resource Complexity**: GCE instances, Cloud SQL (for PostgreSQL), GCS buckets, Cloud Memorystore (for Redis), Pub/Sub.
* **Drift Detection**: Moderately important for MVP.
* **Development Workflow**: Trunk-based development. Merges to `main` automatically deploy to `staging`. Production deployments require manual approval.
* **Cost-Effectiveness**: Minimize infrastructure and operational costs.
* **Simplicity & Manageability**: Solution should be straightforward for a 2-engineer team.
* **Scalability**: IaC structure must support future resource scaling.
* **Security**: Implement least privilege, secure credentials (WIF), and protect state.
* **Maintainability**: Terraform code to be modular, readable, and easy to update.
* **Auditability**: Deployment actions logged and traceable via GitHub Actions.
* **Reliability**: Ensure consistent resource provisioning.

## Context

### Beginning Context

-   `path/to/initial_infra_diagram.png`
-   `path/to/current_manual_deployment_process.md`

### Ending Context

-   `path/to/terraform_modules/`
-   `path/to/.github/workflows/terraform-gcp.yml`
-   `path/to/gcp_project_overview.md` (updated)

## Low-Level Tasks
> Ordered from start to finish

1.  **GCP Setup (Single Project for MVP)**:
    * Select/Create a GCP Project. Note its ID.
    * Create a GCS bucket for Terraform state (e.g., `vedavivi-tf-state-unique`). Enable Object Versioning.
    * Set up Workload Identity Federation.
    * Create a GCP Service Account.
    * Grant this SA necessary IAM roles.
    * Allow the WIF provider to impersonate this Service Account.
2.  **GitHub Repository Setup**:
    * Initialize the Git repository.
    * Add `GCP_PROJECT_ID` as a repository secret.
    * Configure GitHub Environments (`production`, optionally `staging` and `dev`).
3.  **Terraform Configuration**:
    * Structure Terraform code (`main.tf`, `variables.tf`, `outputs.tf`, `versions.tf`).
    * Configure backend in `main.tf` for GCS.
    * Create `staging.tfvars` and `production.tfvars` (and `dev.tfvars` if used).
    * Define resources using `terraform.workspace` for naming.
    * Develop reusable Terraform modules.
4.  **GitHub Actions Workflow Implementation**:
    * Create `.github/workflows/terraform-gcp.yml` with the provided YAML.
    * Replace placeholders with specific GCP and repository details.
5.  **Testing the End-to-End Flow**:
    * Test PR to `main` (plan for `staging`).
    * Test merge to `main` (apply to `staging`).
    * Test manual `dev` deploy (optional).
    * Test manual `production` deploy (plan and apply with approval).
6.  **Drift Detection Setup**:
    * Create a scheduled workflow for `terraform plan` against `staging` and `production`.
    * Implement notifications for detected drift.