## QA Test Specification: US1.1 Guided Initial Setup & Personal Assistant Creation

**User Story (Recap):** As a new user, I want to be guided through the initial setup of my Personal Assistant after my first login, culminating in the creation of my PA instance, so that I can quickly start using its core features.

---

### 1. Test Plan Summary & Objectives

**Overall Objective:** To verify that a new user can successfully complete the initial Personal Assistant (PA) setup wizard, resulting in the creation of a valid PA instance in the backend, and that the system handles various scenarios, including errors and edge cases, gracefully. This includes testing the new `Assistant` domain model creation, database schema changes, API endpoints, and the frontend user experience.

**Testing Approach:** A combination of manual and automated testing (where feasible) will be employed. Testing will cover:
* **Backend:** API testing (GraphQL mutation `createPersonalAssistant`), database interactions, model integrity, service logic.
* **Frontend:** UI/UX of the setup wizard, input validation, API integration, state management, and redirection.
* **Integration:** End-to-end flow from user login, wizard interaction, PA creation, to landing on the main PA interface.

---

### 2. Scope of Testing

**In Scope:**
* **`Assistant` Data Model & Database:**
    * Correct creation of the `assistants` table schema via Alembic migration.
    * Integrity of the `Assistant` SQLAlchemy model and Pydantic schemas.
    * Validation of field constraints (NOT NULL, UNIQUE on `user_id`, data types).
* **Backend API (`createPersonalAssistant` GraphQL Mutation):**
    * Authentication and authorization.
    * Input validation for all parameters.
    * Successful PA creation and association with the correct user.
    * Correct data persistence in the `assistants` table.
    * Error handling and appropriate error responses.
    * Idempotency considerations (e.g., preventing duplicate PA for the same user).
* **User Profile Updates:**
    * Successful update of user profile/model (e.g., `is_pa_setup_complete` flag or `pa_id` storage) post PA creation.
* **Frontend Setup Wizard:**
    * Correct display of the wizard for new users who haven't set up a PA.
    * Correct non-display of the wizard for users who have already completed setup.
    * UI elements, responsiveness (basic), and usability of the wizard.
    * Client-side input validation (e.g., for PA name).
    * Successful API call to the `createPersonalAssistant` mutation.
    * Handling of success and error responses from the API.
    * Redirection to the main PA interface upon successful setup.
* **End-to-End Flow:**
    * Complete user journey from first login -> PA setup wizard -> PA creation -> main PA interface.

**Out of Scope (for this specific User Story):**
* Full functionality of the main PA interface (chat, objectives, etc.) beyond initial access.
* Detailed testing of other unrelated User Stories (e.g., US2.1, US2.3 are prerequisites, not subjects of this test plan, though their correct functioning is assumed).
* Advanced performance, load, or stress testing beyond basic responsiveness checks.
* Functionality of the `avatar_file_id` beyond accepting a UUID (file upload/management is likely a separate story).

---

### 3. Test Environment & Setup

* **Environment:** Dedicated QA environment with frontend (`apps/web`) and backend (`apps/backend`) deployed, connected to a clean QA database.
* **Prerequisites:**
    * User authentication system (Clerk integration, as per US2.1, US2.3) must be functional.
    * Database migrations up to the point *before* the new `assistants` table migration are applied. The new migration will be tested as part of this plan.
* **Tools:**
    * Browser with developer tools.
    * API client (e.g., Postman, Insomnia, or GraphQL Playground) for direct API testing.
    * Database client to inspect table structures and data.
* **Test Accounts:**
    * Multiple new user accounts (created via Clerk signup flow).
    * At least one existing user account (simulated to have *already* completed PA setup, for testing wizard non-display).

---

### 4. Test Data Requirements

* **New User Data:** Credentials for newly registered users via Clerk who have not yet interacted with the PA section.
* **PA Configuration Data:**
    * Valid PA names (various lengths, alphanumeric, with spaces).
    * Invalid PA names (empty, excessively long, potentially harmful characters if not sanitized, names violating uniqueness if that were a PA-level constraint beyond user).
    * Valid and invalid `backstory` text (optional, different lengths).
    * Valid UUIDs for `avatar_file_id` (if testing this optional field).
* **Existing User Data (for negative tests):**
    * A user for whom an `Assistant` record already exists in the `assistants` table.
    * A user whose `User` model already has the `is_pa_setup_complete` flag set to true (or `pa_id` populated).

---

### 5. Test Types Coverage

* **Functional Testing:** Verifying all specified functionalities of the PA creation process.
* **API Testing:** Direct testing of the `createPersonalAssistant` GraphQL mutation.
* **Database Testing:** Verifying schema creation, data integrity, constraints.
* **UI/UX Testing:** Evaluating the frontend wizard's usability, clarity, and adherence to design.
* **Integration Testing:** Ensuring seamless interaction between frontend, backend API, services, and database.
* **Negative Testing & Error Handling:** Validating system behavior with invalid inputs, unexpected conditions, and errors.
* **Security Testing (Basic):**
    * Ensuring API endpoints are authenticated.
    * Verifying that a user can only create/see their own PA setup status.
* **Edge Case Testing:** Exploring less common scenarios and potential race conditions.

---

### 6. Detailed Test Scenarios & Cases

#### 6.1. Backend: `Assistant` Model & Database (Low-Level Task 1, 2)

| Test ID     | Description                                                                                                | Test Steps                                                                                                                                                                                                                            | Expected Results                                                                                                                                                                                                                                                                                                                                                                        | Test Type         |
| :---------- | :--------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------- |
| **DB-TC-001** | Verify `assistants` table schema after Alembic migration.                                                    | 1. Run the new Alembic migration script for `create_assistants_table`. <br> 2. Inspect the `assistants` table schema in the database.                                                                                                       | The `assistants` table is created with all specified columns (`id`, `user_id`, `name`, `backstory`, `avatar_file_id`, `configuration`, `created_at`, `updated_at`) and their correct data types, constraints (PK, FK to `users.id`, UNIQUE on `user_id`, NOT NULL for `name` and `backstory` as per Backend Tech Plan). | Database          |
| **DB-TC-002** | Verify Alembic migration downgrade.                                                                          | 1. After successful upgrade (DB-TC-001), run the downgrade script for the `assistants` table migration.                                                                                                                                     | The `assistants` table is successfully dropped.                                                                                                                                                                                                                                                                                                                                       | Database          |
| **DB-TC-003** | Verify `user_id` FK constraint and UNIQUE constraint.                                                        | 1. Attempt to insert a record into `assistants` with a `user_id` that does not exist in the `users` table. <br> 2. Create a PA for a user. <br> 3. Attempt to insert another record into `assistants` with the same `user_id`. | 1. Database error due to FK violation. <br> 2. PA created. <br> 3. Database error due to UNIQUE constraint violation on `user_id`.                                                                                                                                                                                                                                              | Database          |
| **DB-TC-004** | Verify `name` and `backstory` NOT NULL constraints (as per Backend Tech Plan). | 1. Attempt to insert a record into `assistants` with `name` as NULL. <br> 2. Attempt to insert a record with `backstory` as NULL.                                                                                                | 1. Database error due to NOT NULL violation on `name`. <br> 2. Database error due to NOT NULL violation on `backstory`.                                                                                                                                                                                                                                                               | Database          |
| **DB-TC-005** | Verify default values for `created_at`, `updated_at`.                                                      | 1. Insert a new record into `assistants` without specifying `created_at` or `updated_at`. <br> 2. Update the record.                                                                                                                     | 1. `created_at` and `updated_at` are automatically populated with the current timestamp. <br> 2. `updated_at` reflects the new timestamp of the update.                                                                                                                                                                                                                                 | Database          |

#### 6.2. Backend: Service Logic & `createPersonalAssistant` GraphQL API (Low-Level Task 3, 4, 5)

| Test ID     | Description                                                                                                   | Test Steps                                                                                                                                                                                                                                                                    | Expected Results                                                                                                                                                                                                                                                              | Test Type                  |
| :---------- | :------------------------------------------------------------------------------------------------------------ | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------- |
| **API-TC-001** | **Happy Path:** Create PA with valid auth and minimal required data (name).                                     | 1. Authenticate as a new user (User A). <br> 2. Send `createPersonalAssistant` mutation with a valid `name`. <br> 3. Verify DB.                                                                                                                              | 1. Mutation successful (e.g., 200 OK, no GraphQL errors). <br> 2. Response contains the created assistant's data (id, name, user_id matching User A). <br> 3. A new record exists in the `assistants` table for User A with the provided name. Default `backstory` is applied. | API, Functional, Integration |
| **API-TC-002** | **Authentication:** Attempt PA creation without authentication.                                               | 1. Send `createPersonalAssistant` mutation without a valid auth token.                                                                                                                                                                                      | Mutation fails with an authentication error (e.g., 401/403, or specific GraphQL auth error). No PA is created.                                                                                                                                                     | API, Security              |
| **API-TC-003** | **Authorization:** Attempt PA creation for another user (if API design were to allow `userId` input, which it shouldn't for this case). | 1. Authenticate as User A. <br> 2. (Hypothetical) Send `createPersonalAssistant` trying to specify User B's ID. (Note: The spec implies `user_id` is derived from context).                                                                                | API should use the authenticated user's ID. If it were possible to pass `user_id`, it should be ignored or result in an authorization error. No PA created for User B by User A.                                                                                        | API, Security              |
| **API-TC-004** | **Input Validation - Name (Required):** Empty name.                                                             | 1. Authenticate as a new user. <br> 2. Send `createPersonalAssistant` mutation with `name: ""`.                                                                                                                                                         | Mutation fails with a validation error indicating `name` is required or invalid. No PA created.                                                                                                                                                                       | API, Negative              |
| **API-TC-005** | **Input Validation - Name (Length):** Excessively long name.                                                    | 1. Authenticate as a new user. <br> 2. Send `createPersonalAssistant` mutation with a name exceeding defined limits (if any, otherwise test reasonable DB limits).                                                                                          | Mutation fails with a validation error if max length is enforced at API/Pydantic level, or potentially a DB error if it exceeds column limits. No PA created.                                                                                                      | API, Negative              |
| **API-TC-006** | **Input Validation - Backstory (Optional):** Provide valid backstory.                                       | 1. Authenticate as a new user. <br> 2. Send `createPersonalAssistant` mutation with valid `name` and `backstory`.                                                                                                                                   | Mutation successful. PA record in DB includes the provided `backstory`.                                                                                                                                                                                            | API, Functional            |
| **API-TC-007** | **Input Validation - Backstory (Optional):** Omit backstory.                                                  | 1. Authenticate as a new user. <br> 2. Send `createPersonalAssistant` mutation with valid `name` and no `backstory`.                                                                                                                                     | Mutation successful. PA record in DB has the default `backstory` as defined (e.g., from Backend Tech Plan "NOT NULL" implies a default needs to be set if not provided).                          | API, Functional            |
| **API-TC-008** | **Conflict - Duplicate PA:** Attempt to create PA for a user who already has one.                             | 1. Create a PA for User A. <br> 2. Authenticate as User A again. <br> 3. Send `createPersonalAssistant` mutation again.                                                                                                                                 | Mutation fails with a user-friendly error (e.g., "Personal Assistant already exists for this user"). No new PA is created. The existing PA remains unchanged. (DB UNIQUE constraint on `user_id` is the fallback).                                                 | API, Negative, Edge Case   |
| **API-TC-009** | **Service Logic:** Non-existent User ID (if `user_id` was not derived from auth context but passed, which is not the case here but good for service layer unit test). | 1. (Unit/Integration Test for Service) Call `assistant_service.create_personal_assistant` directly with a `user_id` that doesn't exist in `users` table.                                                                                             | Service function should raise an appropriate exception or handle it gracefully (e.g., due to FK violation if not pre-checked).                                                                                                                                       | Backend Service (Unit/Int) |
| **API-TC-010** | **User Profile Update:** Verify `is_pa_setup_complete` (or `pa_id`) is updated on the User model.              | 1. Authenticate as a new user. <br> 2. Send `createPersonalAssistant` mutation successfully. <br> 3. Fetch user profile data.                                                                                                                     | The user's profile data (e.g., from a `me` query or directly from `user_service.py`) should indicate that PA setup is complete (e.g., `is_pa_setup_complete: true` or `pa_id` is populated).                                                              | API, Integration           |

#### 6.3. Frontend: Setup Wizard UI/UX & Integration (Low-Level Task 6, 7)

| Test ID     | Description                                                                                                | Test Steps                                                                                                                                                                                                                         | Expected Results                                                                                                                                                                                                                         | Test Type                  |
| :---------- | :---------------------------------------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------- |
| **UI-TC-001** | **Wizard Display - New User:** Wizard appears for a new user on first access to PA section.                   | 1. Register a brand new user. <br> 2. Log in as the new user. <br> 3. Navigate to the Personal Assistant section (e.g., `/dashboard` as per `DashboardPage.tsx`). | The PA setup wizard (`SetupWizard.tsx`) is displayed.                                                                                                                                                                      | UI, Functional             |
| **UI-TC-002** | **Wizard Non-Display - Existing User with PA:** Wizard does not appear if PA setup is already complete.         | 1. Use a test user account for whom a PA is already created and `is_pa_setup_complete` is true. <br> 2. Log in as this user. <br> 3. Navigate to the PA section.                                                                 | The PA setup wizard is NOT displayed. The user is directed to the main PA interface.                                                                                                                                     | UI, Functional             |
| **UI-TC-003** | **Input - PA Name (Required):** Attempt to submit wizard without entering a PA name.                        | 1. Access the setup wizard. <br> 2. Leave the PA name field blank. <br> 3. Click the "Create" / "Next" button.                                                                                                                           | An inline validation error message appears indicating the name is required. The form is not submitted.                                                                                                                     | UI, Validation             |
| **UI-TC-004** | **Input - PA Name (Valid):** Enter a valid PA name.                                                         | 1. Access the setup wizard. <br> 2. Enter a valid name (e.g., "My Assistant") in the input field (`input.tsx` component).         | Input is accepted. No validation errors.                                                                                                                                                                                   | UI, Functional             |
| **UI-TC-005** | **Wizard Submission - Success:** Submit wizard with valid data.                                               | 1. Access the setup wizard as a new user. <br> 2. Enter a valid PA name. <br> 3. Click the "Create" button (`button.tsx` component).                                                                                                                                  | 1. A GraphQL mutation `createPersonalAssistant` is sent to the backend. <br> 2. On successful API response, the user is redirected to the main PA interface (e.g., chat view). <br> 3. A success message might be briefly shown. | UI, Functional, Integration |
| **UI-TC-006** | **Wizard Submission - API Error:** Wizard correctly handles and displays backend API errors.                | 1. Configure the backend to simulate an error for the `createPersonalAssistant` mutation (e.g., PA already exists, server error). <br> 2. Access wizard and submit valid data.                                                          | An appropriate error message is displayed to the user within the wizard UI. The user is not redirected and can potentially retry.                                                                                              | UI, Error Handling         |
| **UI-TC-007** | **Wizard Navigation (if multi-step):** Test navigation between wizard steps (if applicable).                | 1. If wizard has multiple steps, navigate forward and backward.                                                                                                                                                                   | Data entered in previous steps is preserved. Navigation is smooth.                                                                                                                                                                   | UI, Usability              |
| **UI-TC-008** | **Responsiveness (Basic):** Check wizard appearance on different viewport sizes (desktop, tablet-like).       | 1. View the wizard on a desktop browser. <br> 2. Resize browser window to simulate tablet width.                                                                                                                                    | Wizard remains usable and elements are reasonably laid out. No major visual breakage.                                                                                                                                              | UI, Responsiveness         |

#### 6.4. End-to-End Flow & Edge Cases

| Test ID       | Description                                                                                                                                  | Test Steps                                                                                                                                                                                                                                                                                                                                 | Expected Results                                                                                                                                                                                                                                                                                          | Test Type                           |
| :------------ | :------------------------------------------------------------------------------------------------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------- |
| **E2E-TC-001** | **Full Happy Path:** New user registration, login, PA setup wizard, PA creation, redirection to main PA interface.                              | 1. Sign up as a completely new user via Clerk flow (US2.1). <br> 2. Backend `user.created` webhook processed, minimal user record created (US2.3). <br> 3. Log in. <br> 4. Access PA section, complete wizard with a valid name. <br> 5. Verify PA created in DB and user profile updated. | User successfully creates PA and lands on the main PA interface. All data is consistent.                                                                                                                                                                                | End-to-End, Functional              |
| **E2E-TC-002** | **User Exists, No PA:** User logs in, has `User` record but no PA and `is_pa_setup_complete` is false/null.                                     | 1. Manually set up a user in DB who has a `users` record but no corresponding `assistants` record and `is_pa_setup_complete` is false. <br> 2. Log in as this user. <br> 3. Access PA section.                                                                                                                                               | Setup wizard is displayed. User can create a PA.                                                                                                                                                                                                                                                          | End-to-End, Edge Case             |
| **E2E-TC-003** | **Rapid Re-navigation/Refresh:** User refreshes page or navigates away and back during wizard.                                                  | 1. Start PA setup wizard. <br> 2. Enter some data (e.g., PA name). <br> 3. Refresh page / Navigate to another page and come back to PA setup.                                                                                                                                                                                                | For MVP, wizard likely resets. User starts over. (State persistence within wizard is a more advanced feature). If PA was already created in a previous attempt that was interrupted post-API call but pre-redirect, wizard should not show. | UI, Edge Case, Usability          |
| **E2E-TC-004** | **API Call Latency/Timeout:** Simulate slow API response during PA creation from wizard.                                                       | 1. Use browser dev tools to throttle network speed or simulate backend delay for `createPersonalAssistant` mutation. <br> 2. Submit wizard.                                                                                                                                                                                                     | Frontend shows appropriate loading state. If timeout occurs, a user-friendly error is displayed. System remains stable.                                                                                                                                                               | UI, Edge Case, Error Handling     |
| **E2E-TC-005** | **Clerk User Sync Delay:** PA setup wizard accessed before `user.created` webhook fully processes and local `User` record `is_pa_setup_complete` field (if new) exists. | 1. Sign up a new user. <br> 2. Immediately (try to) navigate to PA setup before backend worker (`clerk_event_handlers.py`) might have fully initialized any new user-specific flags. | The system should gracefully handle this. Ideally, the frontend queries for PA setup status based on a reliable user profile state. If user profile data isn't fully ready or indicates no PA, wizard shows. Creation relies on `user_id` from Clerk token which should be available. | Integration, Edge Case, Robustness |
| **E2E-TC-006** | **Concurrent PA Creation Attempt (Simulated):** Two browser sessions for the same new user trying to complete wizard simultaneously.         | 1. Log in as a new user in two separate browser windows/tabs. <br> 2. Try to proceed through the PA setup wizard in both sessions at roughly the same time, especially the final submission.                                                                                                                                                  | Only one PA should be created due to the UNIQUE constraint on `assistants.user_id`. The "losing" request should receive an error from the API (e.g., "PA already exists") which is then displayed in its wizard UI. The "winning" request proceeds normally.             | Integration, Edge Case, Robustness |

---

### 7. Test Deliverables

* This QA Test Specification document.
* Test Execution Log (with actual results, pass/fail status for each test case).
* Bug Reports (for any defects found, logged in the project's issue tracker).
* QA Sign-off Report (summarizing testing activities, results, and overall quality assessment).

---

### 8. Risks and Mitigation

| Risk                                                                  | Mitigation                                                                                                                               |
| :-------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------- |
| Schema definition for `Assistant` model is incomplete or incorrect.   | Thorough review of "VEDAVIVI: MVP Backend Technical Plan v1.5" and cross-reference with development. |
| Alembic migration script issues (errors, incomplete schema).        | Developer to test migration locally; QA to test on clean DB. Downgrade script also tested.                                                 |
| API (`createPersonalAssistant`) has security vulnerabilities.         | Ensure authentication is enforced. Code review for auth checks. Basic penetration testing if time allows.                                  |
| Frontend wizard state management issues leading to poor UX.           | Keep wizard simple for MVP. Test navigation and refresh scenarios.                                                                       |
| Inconsistent error handling between API and UI.                       | Define clear error codes/messages from API. Ensure UI maps these to user-friendly messages.                                              |
| Issues with Clerk user sync affecting PA setup eligibility checks.    | Test with freshly signed-up users. Understand the timing of `user.created` webhook processing. |

This detailed QA plan should provide a solid framework for testing the US1.1 feature thoroughly.
