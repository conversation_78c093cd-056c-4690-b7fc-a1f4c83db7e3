# 📋 User Story Specification: US7.1

**Story:** As a user, I want to send a text message to my Personal Assistant via a chat interface, so that I can communicate my requests and information.
**Steps:** Type message → Click send → Message transmitted via GraphQL → Persisted in database → Appears in chat history
**Epic:** 🎯 Core Conversational Interface & Interaction

## ✅ AC
- [ ] Given I am authenticated and in the chat interface with my PA, When I type a message (max 4000 chars) in the input field and click send, Then the message is transmitted to the backend via GraphQL mutation `sendMessage(conversationId: ID!, content: String!)` with <200ms response time [Source ADR-002 Sec 2.5, ADR-003 Sec 3.4]
- [ ] Given the backend receives the `sendMessage` mutation with valid JWT, When it validates user authorization for the conversation, Then it persists the message in `chat_messages` table with sender_role='user', JSONB content, and UTC timestamp [Source ADR-002 Sec 4.4]
- [ ] Given the message is successfully persisted, When the GraphQL mutation returns, Then the message appears in my chat history UI with proper timestamp, sender identification, and optimistic UI updates via Apollo Client cache
- [ ] Given invalid input (empty message, >4000 chars, missing conversationId, unauthorized conversation), When I attempt to send, Then GraphQL validation errors are returned and UI shows specific error messages without submission
- [ ] Given network failure during send, When the mutation fails, Then UI shows retry option and message remains in input field for resubmission

## 🔗 Deps
**Stories:** Basic chat UI - NOT STARTED | US7.3 (Real-time message display | IN PROGRESS) | **Ext:** 🌐 None | **Tech:** ⚙️ Strawberry GraphQL, Apollo Client, PostgreSQL, SQLAlchemy | **Data:** 🗄️ conversations table, chat_messages table schema | **Team:** 👥 None

## 💰 Value
**Biz:** 📈 Enables core user-PA communication, foundational for all conversational features | **UX:** ✨ Real-time messaging experience, immediate feedback on message delivery | **KPI:** 📊 Message delivery success rate >99%, response time <200ms | **Why:** 🎯 Essential MVP capability for user engagement and PA interaction

## 📐 ADRs
**Refs:** 📄 ADR-002 Sec 2.5 (Chat Interface Support), ADR-002 Sec 4 (Data Model), ADR-003 Sec 3.4 (Chat Interface) | **Limits:** ⚠️ GraphQL primary API, PostgreSQL for persistence, WebSocket for real-time | **Patterns:** 🏗️ Strawberry GraphQL mutations, SQLAlchemy ORM, Apollo Client state management

## 🛠️ Impl
**Arch:** 🏛️ React MessageInput → Apollo useMutation → Strawberry GraphQL resolver → SQLAlchemy ORM → PostgreSQL ACID transaction → Apollo cache update | **Data:** 🗄️ conversations(id UUID PK, user_id UUID FK, assistant_id UUID FK, created_at TIMESTAMPTZ, last_message_at TIMESTAMPTZ), chat_messages(id UUID PK, conversation_id UUID FK, sender_role TEXT CHECK('user','agent'), content JSONB, timestamp TIMESTAMPTZ, metadata JSONB) | **API:** 🔌 sendMessage(conversationId: ID!, content: String!) → {id, conversationId, senderRole, content, timestamp, metadata} | **Sec:** 🔒 Clerk JWT middleware validation, OBAC user-conversation ownership check, content XSS sanitization, 4000 char limit | **Perf:** ⚡ <200ms p95 response, B-tree indexes on (conversation_id, timestamp), (user_id, assistant_id) | **Int:** 🌐 None | **Err:** ❌ Strawberry validation errors, SQLAlchemy constraint violations, Apollo error boundaries | **Std:** 📏 ADR-002 Sec 2.5 GraphQL patterns, ADR-002 Sec 4.4 data model constraints

## 🧪 Test
**Unit:** 🔬 ChatMessage model, sendMessage resolver, message validation | **Int:** 🔗 GraphQL mutation end-to-end, database persistence, authentication flow | **Contract:** 📋 GraphQL schema compliance | **E2E:** 🎭 Complete user message sending journey | **Perf:** 🚀 Message throughput, concurrent user testing | **Sec:** 🛡️ Authorization bypass attempts, input injection testing

## 📏 NFRs
**Perf:** ⚡ <200ms sendMessage response time, support 1000 concurrent users | **A11y:** ♿ WCAG 2.1 AA keyboard navigation, screen reader support | **Sec:** 🔒 Clerk JWT authentication, OBAC authorization, XSS prevention | **Scale:** 📈 Handle 10k messages/hour per user | **Compat:** 🌍 Modern browsers, mobile responsive design

## ✅ DoD
- [ ] AC met | [ ] Tests pass | [ ] Security review | [ ] Perf benchmarks | [ ] Docs updated | [ ] Code review | [ ] QA complete

## ⚠️ Risk
**Tech:** 💻 GraphQL subscription complexity → Start with polling fallback | **Biz:** 📊 Message delivery failures → Implement retry mechanism | **Time:** ⏰ Database schema changes → Use Alembic migrations | **Deps:** 🔗 Frontend-backend coordination → API-first development

## 📊 Est
**Pts:** 🎯 8 points (complex data model + GraphQL + frontend integration) | **Hrs:** ⏱️ 16 dev + 8 test + 4 review | **Complex:** 🧩 New database tables, GraphQL schema changes, frontend state management | **Conf:** 📈 M (established patterns but new domain) | **Vars:** 🔄 Real-time requirements, message content structure

## 📦 Data
**API:** 🔌 sendMessage(conversationId: ID!, content: String!) → {id, conversationId, senderRole, content, timestamp} | **DB:** 🗄️ conversations, chat_messages with proper foreign keys and indexes | **UI:** 🖥️ Message input state, conversation history, loading states | **Config:** ⚙️ DATABASE_URL, GraphQL endpoint configuration

## 🎨 Visual
**Layout:** 📐
```
┌─────────────────────────────────────┐
│ Chat Header: [PA Name] [Status]     │
├─────────────────────────────────────┤
│ Message History:                    │
│ ┌─ User: Hello, can you help me?    │
│ └─ PA: Of course! How can I assist? │
│ ┌─ User: [New message being typed]  │
├─────────────────────────────────────┤
│ Input: [Type message...] [Send]     │
└─────────────────────────────────────┘
```
**Flow:** 🌊 User types → Validation → GraphQL mutation → Database → UI update | **States:** 🔄 Idle → Typing → Sending → Sent → Error | **Seq:** 📋 Frontend → GraphQL Gateway → Resolver → Database → Response

## 🤔 Assume
**Sys:** 🖥️ PostgreSQL available, GraphQL server running, Redis for caching | **User:** 👤 Authenticated via Clerk, has active conversation, modern browser | **Env:** 🌐 Stable network connection, WebSocket support available | **Data:** 📊 Text messages only (no rich media in MVP), UTF-8 encoding

## 🎬 Context
**Before:** Files:📁 apps/backend/src/a2a_platform/db/models/{user.py, assistant.py}, apps/backend/src/a2a_platform/api/graphql/__init__.py (basic schema), apps/web/src/components (basic UI) | Schema:🗄️ users, assistants tables exist, no conversations/chat_messages | Components:⚙️ Basic GraphQL setup, authentication middleware, basic React components | Config:⚙️ Database connection, Clerk integration
**After:** Files:📁 apps/backend/src/a2a_platform/db/models/{conversation.py, chat_message.py}, apps/backend/src/a2a_platform/api/graphql/schemas/chat_schemas.py, apps/backend/src/a2a_platform/api/graphql/resolvers/chat_resolvers.py, apps/web/src/components/chat/MessageInput.tsx, apps/web/src/graphql/mutations.tsx (updated) | Schema:🗄️ conversations and chat_messages tables with proper indexes and foreign keys | Components:⚙️ Functional message sending from UI to database with real-time updates | Config:⚙️ None

## 📝 Tasks
1. **Create Conversation SQLAlchemy Model** - Define conversation entity with user/assistant relationships
```coder_llm
**Do:** 🎯 Create SQLAlchemy model class Conversation in apps/backend/src/a2a_platform/db/models/conversation.py with __tablename__='conversations', id=UUID PK default gen_random_uuid(), user_id=UUID FK users.id CASCADE, assistant_id=UUID FK assistants.id CASCADE, created_at=TIMESTAMPTZ default now(), last_message_at=TIMESTAMPTZ nullable, relationships to User/Assistant models, unique constraint (user_id, assistant_id) | **File:** 📁 apps/backend/src/a2a_platform/db/models/conversation.py | **Target:** 🎯 Conversation class following user.py/assistant.py patterns with proper Mapped types, relationship back_populates, cascade options | **Check:** ✅ Model imports, relationships bidirectional, unique constraint enforced, follows existing UUID/timestamp patterns | **Needs:** 🔧 User and Assistant models exist with relationship definitions
```

2. **Create ChatMessage SQLAlchemy Model** - Define message entity with conversation relationship
```coder_llm
**Do:** 🎯 Create SQLAlchemy model class ChatMessage in apps/backend/src/a2a_platform/db/models/chat_message.py with __tablename__='chat_messages', id=UUID PK, conversation_id=UUID FK conversations.id CASCADE, sender_role=TEXT CHECK IN ('user','agent'), content=JSONB NOT NULL, timestamp=TIMESTAMPTZ default now(), metadata=JSONB default '{}', relationship to Conversation model, index on (conversation_id, timestamp) | **File:** 📁 apps/backend/src/a2a_platform/db/models/chat_message.py | **Target:** 🎯 ChatMessage class with proper JSONB handling, enum validation, timezone-aware timestamps, relationship back_populates | **Check:** ✅ Model imports, JSONB serialization works, sender_role constraint enforced, timestamp index created, relationship functional | **Needs:** 🔧 Conversation model exists
```

3. **Create Database Migrations** - Generate Alembic migrations for new tables
```coder_llm
**Do:** 🎯 Generate Alembic migrations for conversations and chat_messages tables with proper foreign key constraints, indexes on (user_id, assistant_id, last_message_at) for conversations and (conversation_id, timestamp) for chat_messages | **File:** 📁 apps/backend/alembic/versions/YYYYMMDD_add_chat_tables.py | **Target:** 🎯 Migration creates both tables with correct schema, constraints, and indexes | **Check:** ✅ Migration runs successfully, tables created with proper foreign keys and indexes | **Needs:** 🔧 Models defined
```

4. **Update Model Imports** - Register new models with SQLAlchemy
```coder_llm
**Do:** 🎯 Add Conversation and ChatMessage imports to apps/backend/src/a2a_platform/db/models/__init__.py following existing patterns for model discovery by Alembic | **File:** 📁 apps/backend/src/a2a_platform/db/models/__init__.py | **Target:** 🎯 Import statements and __all__ list updated | **Check:** ✅ Models are discoverable by Alembic, no import errors | **Needs:** 🔧 Model files created
```

5. **Create GraphQL Schemas** - Define Strawberry types for Conversation and ChatMessage
```coder_llm
**Do:** 🎯 Create Strawberry GraphQL types for Conversation, ChatMessage, and SendMessageInput following patterns from user_profile_schemas.py with proper field mappings and type annotations | **File:** 📁 apps/backend/src/a2a_platform/api/graphql/schemas/chat_schemas.py | **Target:** 🎯 Conversation, ChatMessage, and SendMessageInput types with proper serialization | **Check:** ✅ Types serialize correctly, relationships work, follows Strawberry patterns | **Needs:** 🔧 SQLAlchemy models exist
```

6. **Implement sendMessage GraphQL Mutation** - Backend API endpoint
```coder_llm
**Do:** 🎯 Create async resolve_send_message(info: Info, conversation_id: strawberry.ID, content: str) in apps/backend/src/a2a_platform/api/graphql/resolvers/chat_resolvers.py with: 1) Extract clerk_user_id from info.context, 2) Validate content length ≤4000 chars, 3) Query conversation ownership via user_id, 4) Create ChatMessage with sender_role='user' content={'text': content} timestamp=utcnow(), 5) Commit transaction, 6) Return ChatMessage GraphQL type, 7) Handle SQLAlchemy/validation exceptions | **File:** 📁 apps/backend/src/a2a_platform/api/graphql/resolvers/chat_resolvers.py | **Target:** 🎯 resolve_send_message function following assistant_resolvers.py patterns with proper async/await, db session handling, error logging | **Check:** ✅ Resolver authenticates, authorizes conversation access, validates input, persists message, returns proper type, handles errors gracefully | **Needs:** 🔧 ChatMessage GraphQL schema, database session context
```

7. **Update GraphQL Schema** - Add mutation to main schema
```coder_llm
**Do:** 🎯 Add sendMessage mutation to main GraphQL Mutation class in apps/backend/src/a2a_platform/api/graphql/__init__.py and import chat resolver following existing patterns | **File:** 📁 apps/backend/src/a2a_platform/api/graphql/__init__.py | **Target:** 🎯 Mutation class updated with sendMessage field from chat resolvers | **Check:** ✅ GraphQL schema includes new mutation, introspection works, proper imports | **Needs:** 🔧 Resolver implemented
```

8. **Create Frontend GraphQL Mutation** - Apollo Client mutation definition
```coder_llm
**Do:** 🎯 Add SEND_MESSAGE_MUTATION to apps/web/src/graphql/mutations.tsx with gql template literal: mutation SendMessage($conversationId: ID!, $content: String!) { sendMessage(conversationId: $conversationId, content: $content) { id conversationId senderRole content timestamp metadata } } and export const for component usage | **File:** 📁 apps/web/src/graphql/mutations.tsx | **Target:** 🎯 SEND_MESSAGE_MUTATION constant following existing mutation patterns with proper TypeScript types | **Check:** ✅ Mutation syntax valid, matches backend schema exactly, proper variable types, includes all needed return fields | **Needs:** 🔧 Backend GraphQL schema deployed with sendMessage mutation
```

9. **Create Message Input Component** - Frontend chat input interface
```coder_llm
**Do:** 🎯 Create MessageInput React FC in apps/web/src/components/chat/MessageInput.tsx with: useState for input value, useMutation hook for SEND_MESSAGE_MUTATION, handleSubmit with validation (trim, length check), loading/error states, textarea with placeholder "Type your message...", Send button disabled when loading/empty, error display, onSuccess callback to clear input, proper TypeScript props interface | **File:** 📁 apps/web/src/components/chat/MessageInput.tsx | **Target:** 🎯 Functional component with proper form handling, Apollo integration, accessibility (ARIA labels, keyboard shortcuts), responsive design | **Check:** ✅ Component renders correctly, handles input changes, validates before submit, shows loading states, displays errors, clears on success, accessible | **Needs:** 🔧 SEND_MESSAGE_MUTATION imported, conversation context available
```

10. **Update Chat Interface** - Integrate message input with existing chat UI
```coder_llm
**Do:** 🎯 Update existing chat interface components to include MessageInput component and handle new message display in chat history with proper state management | **File:** 📁 apps/web/src/components/chat/ChatInterface.tsx | **Target:** 🎯 Complete chat interface with message sending capability | **Check:** ✅ Messages appear in chat history after sending, proper UI updates | **Needs:** 🔧 MessageInput component created
```

## 🔧 Refactor
**Quality:** 📏 Modular GraphQL resolvers, reusable React components, clear separation of concerns | **Perf:** ⚡ Efficient database queries with proper indexing, optimized GraphQL field selection | **Reuse:** ♻️ Common message validation patterns, shared GraphQL types | **Debt:** 💳 None created - following established patterns | **Test:** 🧪 Comprehensive unit and integration test coverage | **Docs:** 📚 GraphQL schema documentation, component prop documentation
