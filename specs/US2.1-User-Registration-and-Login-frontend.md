## US2.1 User Registration and Login Frontend

> Ingest the information from this file, implement the Low-Level Tasks, and generate the code that will satisfy the High and Mid-Level Objectives.

### High-Level Objective

- Implement a user sign-up and login system allowing users to create accounts and authenticate using Google, GitHub, or email (magic link), providing a convenient and secure way to access VEDAVIVI.

### Mid-Level Objectives

- Develop a sign-up page UI (`/pa/sign-up`) offering options for Google, GitHub, and email-based sign-up.
- Integrate with Clerk.com for frontend handling of Google and GitHub OAuth flows.
- Implement the email and magic link sign-up flow using Clerk.com.
- Ensure new VEDAVIVI accounts are created and linked to the chosen authentication identity (Google, GitHub, or email) upon successful authentication.
- Automatically log users into VEDAVIVI after successful account creation.
- Implement robust error handling for existing accounts and failed authentication attempts, guiding users appropriately.
- Ensure the UI is responsive, accessible (WCAG AA), and intuitive.

### Implementation Notes

- **Core Technology Stack:**
    - ReactJS (v18+)
    - TypeScript
    - Tailwind CSS
    - ShadCN UI
    - GraphQL (Apollo Client) - *Initially for user profile data post-authentication, not directly for <PERSON> auth itself.*
    - Zustand - *May be used for managing global UI state related to auth (e.g., loading, error messages) if needed beyond Clerk's built-in capabilities.*
    - Vite
    - Jest & React Testing Library
    - Cypress
- **Dependencies and Requirements:**
    - Clerk.com Frontend SDK (`@clerk/clerk-react`).
    - Setup of Clerk.com application with Google, GitHub, and Email (magic link) authentication methods enabled.
    - Environment variables for Clerk publishable key (`VITE_CLERK_PUBLISHABLE_KEY`).
    - Backend readiness to handle user creation post-Clerk authentication (webhook or similar mechanism if VEDAVIVI backend needs to store user data beyond what Clerk manages). For MVP, we assume Clerk handles primary user identity.
- **Coding Standards to Follow:**
    - All filenames and variable names will use camelCase (e.g., `signUpPage.tsx`, `handleGoogleSignUp`).
    - Adhere to VEDAVIVI's established ESLint and Prettier configurations.
    - Components should be functional components using React Hooks.
    - TypeScript for all new code.
    - Utility-first CSS with Tailwind CSS.
    - Semantic HTML for accessibility.
    - Comprehensive unit, integration, and E2E tests.
- **Other Technical Guidance:**
    - Frontend routing for authentication pages will be under `/pa/` (e.g., `/pa/sign-up`, `/pa/sign-in`, `/pa/auth-callback`).
    - JWTs provided by Clerk will be used for authenticating with the VEDAVIVI backend (GraphQL API). Apollo Client middleware will handle attaching the JWT to requests.
    - Prioritize simplicity and core functionality for MVP.
    - Ensure all UI elements are keyboard navigable.
    - Use ARIA attributes where necessary to enhance accessibility.
    - Implement clear visual feedback for loading states and error messages.
    - Internationalization (i18n) is out of scope for this initial implementation but design with future i18n in mind (e.g., store text in constants).

### Context

**Beginning context:**
- `apps/web` directory does not exist.
- No authentication-related components or routes exist.

**Ending context:**
- `apps/web/src/pages/auth/SignUpPage.tsx` (or similar structure like `apps/web/src/features/auth/pages/SignUpPage.tsx`)
- `apps/web/src/components/auth/SignUpForm.tsx`
- `apps/web/src/routes/authRoutes.tsx` (defining `/pa/sign-up`, `/pa/sign-in`, etc.)
- `apps/web/src/App.tsx` (or main layout component) updated to include ClerkProvider and routing.
- `apps/web/src/lib/apolloClient.ts` configured to handle Clerk JWTs.
- `apps/web/src/main.tsx` (Vite entry point) updated.
- `.env.local` or `.env` with `VITE_CLERK_PUBLISHABLE_KEY`.
- Relevant test files (e.g., `SignUpPage.test.tsx`, `signUp.cy.ts`).

### Low-Level Tasks
> Ordered from start to finish

1.  **Initialize `apps/web` Project Structure & Base Setup**
    ```aider
    Initialize a new Vite React TypeScript project within the `apps/web` directory. Install core dependencies: react, react-dom, typescript, vite, @vitejs/plugin-react, tailwindcss, postcss, autoprefixer. Configure Tailwind CSS. Set up basic ESLint and Prettier configurations aligned with VEDAVIVI standards. Create a basic `App.tsx` and `main.tsx`.
    CREATE apps/web/vite.config.ts
    CREATE apps/web/tsconfig.json
    CREATE apps/web/tailwind.config.js
    CREATE apps/web/postcss.config.js
    CREATE apps/web/src/main.tsx
    CREATE apps/web/src/App.tsx
    CREATE apps/web/src/index.css (for Tailwind base, components, utilities)
    ```
2.  **Install and Configure Clerk.com SDK**
    ```aider
    Install the `@clerk/clerk-react` package. Create environment variable `VITE_CLERK_PUBLISHABLE_KEY` in an `.env.local` file (and provide a placeholder in `.env.example`). Wrap the root application component (`App.tsx`) with `<ClerkProvider>` using the publishable key. Configure Clerk sign-in and sign-up redirect URLs in the Clerk dashboard (e.g., `/pa/sign-in`, `/pa/sign-up`) and callback URLs.
    UPDATE apps/web/src/main.tsx
    CREATE apps/web/.env.local (developer to create manually with actual key)
    CREATE apps/web/.env.example (with VITE_CLERK_PUBLISHABLE_KEY="")
    ```
3.  **Create Basic Routing Structure for Authentication Pages**
    ```aider
    Install `react-router-dom`. Create a basic routing setup. Define routes for `/pa/sign-up` and a placeholder home page (`/`) for redirection after login.
    CREATE apps/web/src/routes/index.tsx
    UPDATE apps/web/src/App.tsx
    ```
4.  **Develop the Sign-Up Page UI (`SignUpPage.tsx`)**
    ```aider
    Create the `SignUpPage.tsx` component under `apps/web/src/pages/auth/`. This page will display options: "Sign up with Google", "Sign up with GitHub", and an email input field with a "Sign up with Email" button. Use ShadCN UI components (Button, Input, Card) and Tailwind CSS for styling. Ensure the page is responsive and adheres to basic accessibility principles (semantic HTML, labels).
    CREATE apps/web/src/pages/auth/SignUpPage.tsx
    UPDATE apps/web/src/routes/index.tsx (to render SignUpPage)
    ```
5.  **Implement Google Sign-Up Functionality**
    ```aider
    In `SignUpPage.tsx`, use Clerk's `<SignUpButton />` component or `clerk.redirectToSignUp({ strategy: 'oauth_google' })` method for the "Sign up with Google" button. This will redirect the user to Google's OAuth flow managed by Clerk. Ensure Clerk is configured for Google OAuth.
    UPDATE apps/web/src/pages/auth/SignUpPage.tsx
    ```
6.  **Implement GitHub Sign-Up Functionality**
    ```aider
    In `SignUpPage.tsx`, use Clerk's `<SignUpButton />` component or `clerk.redirectToSignUp({ strategy: 'oauth_github' })` method for the "Sign up with GitHub" button. This will redirect the user to GitHub's OAuth flow managed by Clerk. Ensure Clerk is configured for GitHub OAuth.
    UPDATE apps/web/src/pages/auth/SignUpPage.tsx
    ```
7.  **Implement Email (Magic Link) Sign-Up Functionality**
    ```aider
    In `SignUpPage.tsx`, for the "Sign up with Email" option:
    1. Create a controlled input field for the email address.
    2. On button click, use Clerk's `clerk.signUp.create({ emailAddress: email })` followed by `clerk.signUp.prepareEmailAddressVerification({ strategy: 'email_link', redirectUrl: window.location.href })` (or a specific post-verification page).
    3. Display a message to the user to check their email for the magic link.
    Ensure Clerk is configured for Magic Link authentication.
    UPDATE apps/web/src/pages/auth/SignUpPage.tsx
    ```
8.  **Implement Post-Authentication Handling and Redirection**
    ```aider
    Configure Clerk's `<SignedIn>` and `<SignedOut>` components in `App.tsx` or the router to manage content visibility based on authentication state. After successful sign-up and login via any method, Clerk should redirect the user. For MVP, redirect to a simple placeholder authenticated page (e.g., a dashboard stub). Clerk handles session management and JWT issuance automatically.
    UPDATE apps/web/src/App.tsx
    UPDATE apps/web/src/routes/index.tsx
    CREATE apps/web/src/pages/dashboard/DashboardPage.tsx (simple placeholder)
    ```
9.  **Implement Error Handling for Sign-Up Processes**
    ```aider
    Utilize Clerk's error handling mechanisms.
    - For "account already exists": Clerk typically handles this by redirecting to sign-in or showing a message. Ensure this flow is smooth. If custom UI is needed, use Clerk's state (e.g., `clerk.signUp.status` and `clerk.signUp.errors`). Display clear error messages to the user on the `SignUpPage.tsx` (e.g., "This email is already registered. Please log in.").
    - For "cancelled/failed auth": When OAuth flows are cancelled or fail, Clerk should redirect back to the sign-up page. Display appropriate error messages based on Clerk's feedback or URL parameters.
    UPDATE apps/web/src/pages/auth/SignUpPage.tsx
    ```
10. **Integrate Apollo Client with Clerk for Authenticated GraphQL Requests**
    ```aider
    Install `@apollo/client` and `graphql`. Configure Apollo Client to fetch the JWT from Clerk's `session.getToken()` and include it in the `Authorization` header for requests to the VEDAVIVI GraphQL backend. This setup will be used for subsequent features requiring authenticated API calls, not directly for the auth actions themselves.
    CREATE apps/web/src/lib/apolloClient.ts
    UPDATE apps/web/src/App.tsx (to wrap with ApolloProvider)
    ```
11. **Write Unit and Integration Tests for Sign-Up Components**
    ```aider
    Using Jest and React Testing Library:
    - Write unit tests for the `SignUpPage.tsx` component: testing rendering of buttons, email input.
    - Write integration tests to mock Clerk functions and verify that the correct Clerk methods are called upon user interactions (button clicks, form submission). Test error message display.
    CREATE apps/web/src/pages/auth/SignUpPage.test.tsx
    ```
12. **Write E2E Tests for Sign-Up Flows**
    ```aider
    Using Cypress:
    - Create E2E tests covering:
        - Navigating to the sign-up page.
        - Attempting email sign-up (mocking the email link click or interacting with a test email service if feasible).
        - Verifying redirection to the dashboard after successful sign-up.
        - Testing the display of an error message if sign-up fails (e.g., email already exists - may require mocking Clerk responses or specific backend setup for testing).
    - *Note: Full OAuth E2E tests are complex and may require separate strategies or be limited to testing up to the redirection to the OAuth provider.*
    CREATE apps/web/cypress/e2e/auth/signUp.cy.ts
    ```
13. **Accessibility Review and Enhancements**
    ```aider
    Manually review the sign-up page for WCAG AA compliance:
    - Full keyboard navigability for all interactive elements.
    - Correct ARIA attributes where necessary (e.g., for error messages, loading states).
    - Sufficient color contrast.
    - Semantic HTML structure.
    - Run automated accessibility checks (e.g., Axe DevTools browser extension).
    UPDATE apps/web/src/pages/auth/SignUpPage.tsx (and any related components based on findings)
    ```
