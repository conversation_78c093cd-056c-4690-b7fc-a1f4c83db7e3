# QA Test Plan for US5.1 Store and Retrieve User Preferences
> This document outlines the Quality Assurance plan for User Story US5.1. It details the testing strategy, objectives, and specific test scenarios to ensure the functionality of storing and retrieving user preferences meets the defined acceptance criteria and quality standards.

## High-Level QA Objective

- To verify that the system reliably and accurately stores and retrieves user-specific preferences (e.g., favorite color, timezone) as outlined in US5.1, ensuring data integrity, security, and correct functionality for personalization features.

## Mid-Level QA Objectives

- **MO1: Verify Preference Storage:** Confirm that explicitly stated user preferences (e.g., favorite color within a JSONB field, timezone in a dedicated column) are successfully saved to the `users` table and correctly associated with the user's `clerk_user_id`.
- **MO2: Verify Preference Retrieval:** Ensure that the Personal Assistant (PA) or any consuming system component can accurately retrieve all stored preferences (from both the JSONB `preferences` field and the specific `timezone` column) for a given user.
- **MO3: Validate Data Integrity & JSONB Merge Logic:** Confirm that updates to the JSONB `preferences` field correctly merge new or modified preferences while preserving existing, unrelated preference data. Validate that the `timezone` specific column updates correctly.
- **MO4: Test Data Type Handling:** Ensure the system correctly handles various valid data types and structures intended for storage within the JSONB `preferences` field (e.g., strings, numbers, booleans).
- **MO5: Validate Timezone Handling:** Test the storage and retrieval of valid timezone strings. Include tests for how invalid timezone formats are handled at the API/service layer (e.g., rejection, error message).
- **MO6: Verify Security and Authorization:** Confirm that only authenticated users can modify their own preferences and that appropriate error handling or denial occurs for unauthorized access attempts.
- **MO7: Assess Error Handling & Robustness:** Validate system responses to invalid inputs, malformed data, boundary conditions, and potential system failures during preference storage and retrieval operations.
- **MO8: Confirm Database Schema Changes:** Verify that the database schema modifications (addition of `preferences` JSONB and `timezone` string columns to the `users` table) are correctly implemented and do not adversely affect existing user data or functionality. Ensure default values are applied correctly.

## Test Strategy & Scope

- **Scope:** Testing will primarily focus on the backend implementation. This includes API-level testing of GraphQL mutations and queries, service-layer logic validation, and direct database verification. Interaction with the chat interface for preference extraction (US1.2) is considered upstream; this plan tests the storage/retrieval mechanism *after* a preference is identified.
- **Testing Types:**
    -   **Functional Testing:** Verifying C.R.U.D. (Create, Read, Update, Delete - though Delete of specific preferences within JSONB might be an update to an empty value or key removal) operations for preferences via the API.
    -   **API Testing (GraphQL):** Sending GraphQL requests to `updateUserProfilePreferences` mutation and `userProfile` query. Validating response payloads, status codes, error messages, and adherence to the GraphQL schema.
    -   **Database Testing:** Directly querying the PostgreSQL database to inspect the `users` table, verifying that `preferences` (JSONB) and `timezone` (VARCHAR) fields are populated, updated, and structured correctly per `clerk_user_id`. Checking default values and constraints.
    -   **Security Testing (API Level):**
        -   Authentication: Ensuring API endpoints require valid authentication.
        -   Authorization: Ensuring a user can only modify/view their own preferences.
    -   **Negative Testing:** Testing with invalid inputs (e.g., incorrect data types for JSONB values if not schema-enforced at GQL level, invalid timezone strings), malformed requests, and missing required parameters to ensure robust error handling.
    -   **Integration Testing (Backend):** Ensuring the preference service functions correctly interact with the database and that the GraphQL resolvers correctly call the service functions.
- **Test Environment:**
    -   A dedicated QA environment with network access to the deployed backend application and its PostgreSQL database.
    -   The QA database should be seeded with test users via Clerk.com or direct database manipulation for isolated testing scenarios.
    -   The environment must run the version of the application containing the US5.1 feature.
- **Tools:**
    -   GraphQL Client: (e.g., Apollo Sandbox, Insomnia, Postman) for manual and automated API tests.
    -   Database Client: (e.g., pgAdmin, DBeaver) for database verification.
    -   Test Automation Framework: Pytest (with appropriate HTTP client and GraphQL libraries) for writing and executing automated API and integration tests.
    -   Clerk.com dashboard/API: For managing test users and obtaining authentication tokens if necessary.
- **Test Data Requirements:**
    -   Multiple test user accounts (via Clerk) with unique `clerk_user_id`s.
    -   Test scenarios will require users with:
        -   No pre-existing preferences.
        -   Some pre-existing preferences in the JSONB field.
        -   A pre-existing timezone value.
        -   A null timezone value.
    -   A list of valid IANA timezone strings (e.g., "America/New_York", "Europe/London", "Asia/Tokyo", "UTC").
    -   A list of intentionally invalid/malformed timezone strings.
    -   Sample JSON preference data:
        -   Simple key-value: `{"favorite_color": "blue"}`
        -   Multiple keys: `{"theme": "dark", "notifications_enabled": true, "items_per_page": 10}`
        -   Updates: `{"favorite_color": "red", "new_setting": "alpha"}`
    -   Valid authentication tokens (JWTs) for test users.
- **QA Assumptions:**
    -   Backend changes for US5.1 (database model, migration, service logic, GraphQL API endpoints) are deployed to the designated QA environment.
    -   `clerk_user_id` for test users can be obtained.
    -   The GraphQL schema (including input types, mutations, queries, and response types related to user preferences) is documented and accessible.
    -   Upstream preference extraction logic (US1.2) is testable separately or will be mocked/simulated if needed for end-to-end style tests that are beyond the scope of this specific unit of work. This plan assumes the preference data *arrives* at the storage API correctly.

## Context (QA)

### Beginning context
-   The `users` table in the QA database does NOT contain `preferences` or `timezone` columns.
-   No API endpoints specifically for managing these granular preferences exist or are being targeted by tests.
-   Automated tests for US5.1 do not exist.

### Ending context
-   The `users` table in the QA database has been migrated to include a `preferences` JSONB column (defaulting to `'{}'`, nullable) and a `timezone` VARCHAR(64) column (nullable).
-   GraphQL mutation `updateUserProfilePreferences` and query `userProfile` (updated) are available and testable for preference management.
-   A suite of automated tests (e.g., in Pytest) for US5.1 functionality is created and passes in the QA environment.
-   Test execution results are documented, and any defects found are reported and tracked.

## Low-Level Tasks (Test Case Scenarios)

**Scenario 1: Verify Storage of New Preferences**
* **Test Case 1.1: New User - Set Initial Preferences (JSONB and Timezone)**
    * **Given:** A newly registered user (User_A) with `clerk_user_id_A` and no existing preferences or timezone in the database.
    * **When:** A GraphQL mutation `updateUserProfilePreferences` is executed for User_A with input: `preferences: {"color": "blue", "showTips": true}`, `timezone: "America/New_York"`.
    * **Then:** The API responds with a success status and returns the updated user profile data including `preferences: {"color": "blue", "showTips": true}` and `timezone: "America/New_York"`.
    * **And:** Database verification shows for `clerk_user_id_A`: `preferences` column contains `{"color": "blue", "showTips": true}` and `timezone` column contains "America/New_York".
* **Test Case 1.2: Existing User (No Prefs) - Set Only JSONB Preferences**
    * **Given:** An existing user (User_B) with `clerk_user_id_B`, `preferences` is `{}` (empty JSON), and `timezone` is NULL.
    * **When:** `updateUserProfilePreferences` is executed for User_B with input: `preferences: {"fontSize": 14}`.
    * **Then:** API responds successfully, showing `preferences: {"fontSize": 14}` and `timezone` as NULL.
    * **And:** Database for `clerk_user_id_B` shows `preferences` as `{"fontSize": 14}` and `timezone` remains NULL.
* **Test Case 1.3: Existing User (No Prefs) - Set Only Timezone**
    * **Given:** An existing user (User_C) with `clerk_user_id_C`, `preferences` is `{}` and `timezone` is NULL.
    * **When:** `updateUserProfilePreferences` is executed for User_C with input: `timezone: "Europe/Paris"`.
    * **Then:** API responds successfully, showing `preferences: {}` and `timezone: "Europe/Paris"`.
    * **And:** Database for `clerk_user_id_C` shows `preferences` as `{}` and `timezone` as "Europe/Paris".

**Scenario 2: Verify Update of Existing Preferences (Merge Logic & Overwrite)**
* **Test Case 2.1: Update Existing JSONB, Add New JSONB Key, Keep Timezone**
    * **Given:** User_D with `clerk_user_id_D` has `preferences: {"color": "green", "notifyByEmail": true}` and `timezone: "Asia/Tokyo"`.
    * **When:** `updateUserProfilePreferences` is executed for User_D with input: `preferences: {"color": "red", "notifyBySms": false}`.
    * **Then:** API responds successfully, showing `preferences: {"color": "red", "notifyByEmail": true, "notifyBySms": false}` and `timezone: "Asia/Tokyo"`.
    * **And:** Database for `clerk_user_id_D` reflects these merged preferences and unchanged timezone.
* **Test Case 2.2: Update Only Timezone, Keep Existing JSONB Preferences**
    * **Given:** User_E with `clerk_user_id_E` has `preferences: {"layout": "compact"}` and `timezone: "Australia/Sydney"`.
    * **When:** `updateUserProfilePreferences` is executed for User_E with input: `timezone: "Pacific/Auckland"`.
    * **Then:** API responds successfully, showing `preferences: {"layout": "compact"}` and `timezone: "Pacific/Auckland"`.
    * **And:** Database for `clerk_user_id_E` reflects unchanged preferences and updated timezone.
* **Test Case 2.3: Clear a JSONB Preference by Setting to Null/Empty (if supported by design, otherwise removing key)**
    * **Given:** User_F with `clerk_user_id_F` has `preferences: {"item": "test", "keep": "this"}`. (Assuming API supports removing/nullifying a key by passing `null` or specific instruction)
    * **When:** `updateUserProfilePreferences` is executed for User_F with input: `preferences: {"item": null}`. (Or appropriate syntax for key removal if `null` means "no change")
    * **Then:** API responds successfully, showing `preferences: {"keep": "this"}` (or `{"item": null, "keep": "this"}` if nulls are stored).
    * **And:** Database reflects the change. *Clarify expected behavior for "deleting" a key.*
* **Test Case 2.4: Update JSONB with Different Data Types**
    * **Given:** User_G with `clerk_user_id_G` has `preferences: {"count": 5}`.
    * **When:** `updateUserProfilePreferences` is executed for User_G with input: `preferences: {"count": 10, "isActive": false, "label": "Updated"}`.
    * **Then:** API responds successfully with `preferences: {"count": 10, "isActive": false, "label": "Updated"}`.
    * **And:** Database reflects these types.

**Scenario 3: Verify Retrieval of Preferences**
* **Test Case 3.1: Retrieve Full Preferences (JSONB and Timezone)**
    * **Given:** User_H with `clerk_user_id_H` has `preferences: {"mode": "expert", "sound": "on"}` and `timezone: "UTC"`.
    * **When:** GraphQL query `userProfile` is executed for User_H.
    * **Then:** API responds successfully, and the payload contains `preferences: {"mode": "expert", "sound": "on"}` and `timezone: "UTC"`.
* **Test Case 3.2: Retrieve Preferences - Null Timezone**
    * **Given:** User_I with `clerk_user_id_I` has `preferences: {"featureX": true}` and `timezone` is NULL in DB.
    * **When:** `userProfile` query is executed for User_I.
    * **Then:** API responds successfully, payload contains `preferences: {"featureX": true}` and `timezone` is `null` (or omitted if GQL schema indicates).
* **Test Case 3.3: Retrieve Preferences - Empty JSONB, Timezone Set**
    * **Given:** User_J with `clerk_user_id_J` has `preferences: {}` (empty JSON object) in DB and `timezone: "America/Chicago"`.
    * **When:** `userProfile` query is executed for User_J.
    * **Then:** API responds successfully, payload contains `preferences: {}` (or equivalent) and `timezone: "America/Chicago"`.
* **Test Case 3.4: Retrieve Preferences - User with No Preferences Set (Defaults)**
    * **Given:** User_K with `clerk_user_id_K` was just created, so DB has `preferences: {}` and `timezone: NULL`.
    * **When:** `userProfile` query is executed for User_K.
    * **Then:** API responds successfully, payload contains `preferences: {}` and `timezone: null`.

**Scenario 4: Negative Test Cases & Error Handling**
* **Test Case 4.1: Update with Invalid Timezone Format**
    * **Given:** User_L exists.
    * **When:** `updateUserProfilePreferences` is executed with `timezone: "Invalid/Timezone_Value"`.
    * **Then:** API responds with a GraphQL error indicating an invalid value for timezone (e.g., input validation error).
    * **And:** Database state for User_L's timezone and preferences remains unchanged.
* **Test Case 4.2: Update with Malformed JSON input for `preferences` (if GQL schema allows string, otherwise GQL layer handles)**
    * **Given:** User_M exists.
    * **When:** `updateUserProfilePreferences` is executed with `preferences` as a malformed JSON string (e.g., `"{key: value_no_quotes}"` if the API were to accept stringified JSON - less likely with GraphQLJSONObject).
    * **Then:** API responds with a GraphQL error (likely at parsing or validation stage).
* **Test Case 4.3: Update Preferences for a Non-Existent User**
    * **Given:** `clerk_user_id_non_existent` does not correspond to any user. (This test relies on `clerk_user_id` coming from auth context, so it's more about testing internal lookup robustness if an invalid ID somehow got into context).
    * **When:** `updateUserProfilePreferences` is invoked where the context's `clerk_user_id` is invalid/non-existent.
    * **Then:** API responds with an appropriate error (e.g., "User not found" or a generic auth error if Clerk validation happens first).
* **Test Case 4.4: Invalid Data Type within `preferences` JSON (e.g., expecting string, get number, if not schema-enforced by GQL types)**
    * **Given:** User_N exists. Assume a preference `myKey` is expected by some consumer to be a string.
    * **When:** `updateUserProfilePreferences` is executed with `preferences: {"myKey": 12345}`.
    * **Then:** API accepts it (as JSONB is flexible), but downstream consumers might fail. (This highlights the need for consumer contract awareness or more specific GQL input types if strictness is desired). *For this test, verify it's stored as a number.*

**Scenario 5: Security Test Cases (API Level)**
* **Test Case 5.1: Unauthenticated Request to Update/Get Preferences**
    * **Given:** No valid authentication token is provided.
    * **When:** `updateUserProfilePreferences` mutation or `userProfile` query is executed.
    * **Then:** API responds with an authentication error (e.g., 401/403 equivalent in GraphQL, "Authentication required").
* **Test Case 5.2: User A Tries to Update/Get User B's Preferences (Verify Contextual User ID is Enforced)**
    * **Given:** User_A is authenticated. User_B is another valid user. The API design implies `clerk_user_id` is taken from User_A's auth context.
    * **When:** (Simulate if possible, or confirm by code review) User_A makes an `updateUserProfilePreferences` call.
    * **Then:** The preferences are updated *only* for User_A. User_B's preferences remain unchanged.
    * **And:** User_A executing `userProfile` query only receives User_A's data.

**Scenario 6: Database Schema and Default Value Verification**
* **Test Case 6.1: Verify Column Creation and Types**
    * **Given:** Database migration for US5.1 has been applied.
    * **When:** The schema of the `users` table is inspected in the database.
    * **Then:** Column `preferences` exists, is of type JSONB, is nullable, and has a server default of `'{}'`.
    * **And:** Column `timezone` exists, is of type VARCHAR(64) (or as specified), and is nullable.
* **Test Case 6.2: Verify Default Values on New User Creation (Post-Migration)**
    * **Given:** The US5.1 migration is applied. A new user is created in the system (e.g., via Clerk webhook processing a new signup).
    * **When:** The new user's record is inspected in the `users` table.
    * **Then:** The `preferences` column for the new user defaults to an empty JSON object (`{}`).
    * **And:** The `timezone` column for the new user defaults to `NULL`.
