# 🧪 Frontend CDN Migration - QA Testing Specification

**Test Suite ID:** FRONT-001-QA  
**Related User Story:** FRONT-001  
**Test Environment:** Staging → Production  
**Estimated Testing Time:** 2 days  
**Created:** January 2025  

## 📋 Test Strategy Overview

### **Testing Approach**
- **Infrastructure Validation:** Verify CDN and storage configuration via Terraform
- **Performance Testing:** Measure load time improvements
- **Functional Testing:** Ensure feature parity with Docker deployment  
- **Security Testing:** Validate SSL/TLS and access controls
- **Rollback Testing:** Confirm disaster recovery procedures
- **Terraform Testing:** Validate Infrastructure as Code deployment

### **Test Environments**
- **Staging:** `https://staging.vedavivi.app`
- **Production:** `https://ai.vedavivi.app` 
- **CDN Testing:** Multiple global locations via WebPageTest
- **Cloudflare Dashboard:** Analytics and cache management interface
- **Terraform State:** Remote state validation and consistency

## 🏗️ Infrastructure & Configuration Tests

### **TC-001: Terraform Infrastructure Provisioning**
- **GIVEN** Terraform modules are developed for Cloudflare CDN and GCS hosting
- **WHEN** applying Terraform configuration to staging environment
- **THEN** all resources should be created successfully
- **AND** Terraform state should be consistent and stored remotely

**Test Steps:**
1. Initialize Terraform: `cd terraform/environments/staging && terraform init`
2. Plan deployment: `terraform plan -out=tfplan`
3. Apply configuration: `terraform apply tfplan`
4. Verify state: `terraform show` and `terraform state list`
5. Validate outputs: `terraform output`

**Expected Results:**
- ✅ Terraform init completes without errors
- ✅ Plan shows expected resource creation (no surprises)
- ✅ Apply completes successfully with all resources created
- ✅ State file stored in remote backend (GCS)
- ✅ Outputs match expected values (bucket name, CDN URL)

### **TC-002: GCS Bucket Configuration via Terraform**
- **GIVEN** GCS buckets are created via Terraform module
- **WHEN** uploading static assets via CI/CD pipeline
- **THEN** assets should be accessible with proper permissions
- **AND** bucket policies should allow public read access
- **AND** versioning should be enabled for rollback capability

**Test Steps:**
1. Verify Terraform-created bucket: `gsutil ls gs://vedavivi-web-assets-staging`
2. Check bucket configuration: `gsutil lifecycle get gs://vedavivi-web-assets-staging`
3. Upload test file: `gsutil cp test.html gs://vedavivi-web-assets-staging/`
4. Verify public access: `curl https://storage.googleapis.com/vedavivi-web-assets-staging/test.html`
5. Check IAM policies: `gsutil iam get gs://vedavivi-web-assets-staging`

**Expected Results:**
- ✅ Buckets accessible via gsutil
- ✅ Lifecycle rules configured for 30-day retention
- ✅ Files uploadable without authentication errors
- ✅ Public HTTP access returns 200 status
- ✅ IAM includes `allUsers:objectViewer` role
- ✅ Service account for CI/CD has proper permissions

### **TC-003: Cloudflare CDN Configuration via Terraform**
- **GIVEN** Cloudflare CDN is configured via Terraform module
- **WHEN** requesting static assets through Cloudflare
- **THEN** cache headers should be set correctly per asset type
- **AND** page rules should be active and properly configured

**Test Steps:**
1. Verify zone creation: Check Cloudflare dashboard for zone
2. Test DNS resolution: `nslookup staging.vedavivi.app`
3. Request HTML file: `curl -I https://staging.vedavivi.app/index.html`
4. Request hashed JS: `curl -I https://staging.vedavivi.app/assets/index-C06HsBvw.js`
5. Request API route: `curl -I https://staging.vedavivi.app/api/health`
6. Verify page rules in Cloudflare dashboard

**Expected Results:**
- ✅ Cloudflare zone exists and is active
- ✅ DNS resolves to Cloudflare IPs
- ✅ HTML: `Cache-Control: max-age=300` 
- ✅ Hashed assets: `Cache-Control: max-age=********`
- ✅ API routes: `Cache-Control: no-cache` + proxy to backend
- ✅ Page rules visible and active in dashboard
- ✅ Cloudflare edge response headers present (`CF-Cache-Status`, `CF-RAY`)

### **TC-004: DNS & SSL Configuration via Terraform**
- **GIVEN** DNS records and SSL settings are managed via Terraform
- **WHEN** accessing custom domain via HTTPS
- **THEN** SSL certificate should be valid and trusted
- **AND** HTTP requests should redirect to HTTPS

**Test Steps:**
1. DNS lookup: `nslookup ai.vedavivi.app`
2. SSL validation: `openssl s_client -connect ai.vedavivi.app:443`
3. HTTP redirect test: `curl -I http://ai.vedavivi.app`
4. Certificate chain verification
5. Verify SSL settings in Terraform state: `terraform show | grep ssl`

**Expected Results:**
- ✅ DNS resolves to CDN IP addresses
- ✅ SSL certificate valid and trusted (A+ rating)
- ✅ HTTP redirects to HTTPS (301/302 status)
- ✅ Certificate chain complete and unexpired
- ✅ SSL settings match Terraform configuration

### **TC-005: WebSocket Support via Spectrum**
- **GIVEN** WebSocket support is configured via Cloudflare Spectrum
- **WHEN** establishing WebSocket connections
- **THEN** connections should proxy successfully to backend
- **AND** real-time communication should work properly

**Test Steps:**
1. Verify Spectrum application in Terraform state
2. Test WebSocket connection: `wscat -c wss://staging.vedavivi.app/ws`
3. Send test messages and verify responses
4. Monitor connection stability over time
5. Check Spectrum analytics in Cloudflare dashboard

**Expected Results:**
- ✅ Spectrum application exists and is configured
- ✅ WebSocket connection establishes successfully
- ✅ Messages proxy correctly to backend
- ✅ No connection drops or timeouts
- ✅ Analytics show successful WebSocket traffic

### **TC-006: DNS & SSL Configuration**
- **GIVEN** DNS records point to CDN endpoint
- **WHEN** accessing custom domain via HTTPS
- **THEN** SSL certificate should be valid and trusted
- **AND** HTTP requests should redirect to HTTPS

**Test Steps:**
1. DNS lookup: `nslookup ai.vedavivi.app`
2. SSL validation: `openssl s_client -connect ai.vedavivi.app:443`
3. HTTP redirect test: `curl -I http://ai.vedavivi.app`
4. Certificate chain verification

**Expected Results:**
- ✅ DNS resolves to CDN IP addresses
- ✅ SSL certificate valid and trusted (A+ rating)
- ✅ HTTP redirects to HTTPS (301/302 status)
- ✅ Certificate chain complete and unexpired

## ⚡ Performance Testing

### **TC-007: Global Load Time Improvement**
- **GIVEN** baseline measurements from Docker deployment
- **WHEN** CDN deployment is active
- **THEN** global TTFB should improve by >60%
- **AND** total page load time should improve by >50%

**Test Locations:** New York, London, Tokyo, Sydney, São Paulo, Mumbai

**Test Steps:**
1. Baseline measurement (Docker): WebPageTest against Cloud Run
2. CDN measurement: WebPageTest against CDN endpoints
3. Compare TTFB and load complete times
4. Test from multiple global locations

**Expected Results:**
- ✅ TTFB: 200-800ms → 50-150ms (60-75% improvement)
- ✅ Load complete: >50% improvement across all locations
- ✅ Largest Contentful Paint (LCP) <2.5s globally
- ✅ First Input Delay (FID) <100ms

### **TC-008: Cache Performance Validation**
- **GIVEN** CDN cache is warmed up
- **WHEN** requesting static assets multiple times
- **THEN** subsequent requests should be served from cache
- **AND** cache hit ratio should be >95%

**Test Steps:**
1. First request: Measure response time and headers
2. Immediate second request: Verify cache hit
3. Monitor CDN analytics for hit ratio
4. Test cache invalidation workflow

**Expected Results:**
- ✅ First request: `X-Cache: MISS` or similar
- ✅ Second request: `X-Cache: HIT` with faster response
- ✅ Cache hit ratio >95% in CDN analytics
- ✅ Cache invalidation completes within 5 minutes globally

### **TC-009: Build and Deployment Speed**
- **GIVEN** updated CI/CD pipeline
- **WHEN** triggering deployment via GitHub Actions
- **THEN** build time should be reduced by >40%
- **AND** deployment should complete without errors

**Test Steps:**
1. Trigger deployment: Push change to staging branch
2. Monitor GitHub Actions workflow duration
3. Verify asset upload to GCS
4. Test cache invalidation completion

**Expected Results:**
- ✅ Total workflow time: 3-5min → 1-2min (50% improvement)
- ✅ Asset upload completes successfully
- ✅ Cache invalidation triggered automatically
- ✅ Zero deployment errors in logs

## 🔧 Functional Testing

### **TC-010: Frontend Application Functionality**
- **GIVEN** frontend is served via CDN
- **WHEN** users interact with the application
- **THEN** all features should work identically to Docker deployment
- **AND** API communication should be seamless

**Test Steps:**
1. User authentication flow (Clerk integration)
2. Dashboard loading and navigation
3. Real-time features (WebSocket connections)
4. Form submissions and data persistence
5. File uploads and downloads

**Expected Results:**
- ✅ Login/logout functionality works
- ✅ All dashboard components render correctly
- ✅ WebSocket connections establish successfully
- ✅ API calls receive proper responses
- ✅ No console errors or broken functionality

### **TC-011: Environment Variable Injection**
- **GIVEN** environment variables are injected at build time
- **WHEN** accessing staging and production environments
- **THEN** correct API endpoints should be configured
- **AND** environment-specific settings should be active

**Test Steps:**
1. Inspect staging build: Check `VITE_GRAPHQL_API_URL` in console
2. Inspect production build: Verify production API endpoint
3. Test API connectivity from each environment
4. Verify other environment-specific configurations

**Expected Results:**
- ✅ Staging points to staging API endpoint
- ✅ Production points to production API endpoint  
- ✅ API calls succeed from both environments
- ✅ No hardcoded URLs or incorrect configurations

### **TC-012: Error Handling and Fallbacks**
- **GIVEN** CDN or origin server issues
- **WHEN** errors occur in the delivery chain
- **THEN** appropriate error pages should be served
- **AND** graceful degradation should occur

**Test Steps:**
1. Simulate origin server outage
2. Test CDN edge server failure scenarios
3. Verify 404 page for missing assets
4. Test API proxy error handling

**Expected Results:**
- ✅ Custom 404 page served for missing assets
- ✅ CDN provides failover between edge locations
- ✅ API errors properly handled and displayed
- ✅ No white screen of death scenarios

## 🛡️ Security Testing

### **TC-013: Access Control Validation**
- **GIVEN** static assets are publicly accessible
- **WHEN** attempting to access backend resources
- **THEN** proper access controls should be enforced
- **AND** sensitive data should not be exposed

**Test Steps:**
1. Attempt direct GCS bucket access without authorization
2. Test API endpoint access through CDN
3. Verify source map and sensitive file exclusion
4. Check for exposed environment variables in client

**Expected Results:**
- ✅ GCS bucket prevents unauthorized write access
- ✅ API routes properly proxy to backend with auth
- ✅ Source maps excluded from production build
- ✅ No sensitive secrets exposed in client-side code

### **TC-014: SSL/TLS Security**
- **GIVEN** CDN serves content over HTTPS
- **WHEN** analyzing SSL configuration
- **THEN** security headers should be properly configured
- **AND** SSL rating should be A+ grade

**Test Steps:**
1. SSL Labs test: `https://www.ssllabs.com/ssltest/`
2. Security headers check: `curl -I https://ai.vedavivi.app`
3. HSTS validation
4. Content Security Policy verification

**Expected Results:**
- ✅ SSL Labs grade: A+ rating
- ✅ HSTS header present and properly configured
- ✅ CSP headers prevent XSS attacks
- ✅ No mixed content warnings in browser

## 🔄 Migration & Rollback Testing

### **TC-015: Parallel Deployment Validation**
- **GIVEN** both Docker and CDN deployments are active
- **WHEN** traffic is split between deployments
- **THEN** both should serve identical content
- **AND** traffic splitting should work smoothly

**Test Steps:**
1. Deploy to both Docker and CDN simultaneously
2. Configure traffic splitting (50/50)
3. Monitor response consistency between versions
4. Verify session persistence across deployments

**Expected Results:**
- ✅ Identical content served from both deployments
- ✅ Traffic splitting operates without errors
- ✅ User sessions maintain consistency
- ✅ No functionality differences detected

### **TC-016: Rollback Procedure Validation**
- **GIVEN** CDN deployment is active
- **WHEN** rollback to Docker deployment is triggered
- **THEN** DNS switch should occur within 5 minutes
- **AND** application should remain accessible throughout

**Test Steps:**
1. Document current CDN deployment state
2. Execute rollback procedure via DNS change
3. Monitor DNS propagation and accessibility
4. Verify full functionality on Docker deployment
5. Test forward migration back to CDN

**Expected Results:**
- ✅ DNS propagation completes within 5 minutes
- ✅ Zero downtime during rollback process
- ✅ Application fully functional on Docker
- ✅ Forward migration successful

### **TC-017: Disaster Recovery Testing**
- **GIVEN** various failure scenarios
- **WHEN** catastrophic failures occur
- **THEN** recovery procedures should restore service
- **AND** data integrity should be maintained

**Test Steps:**
1. Simulate GCS bucket deletion
2. Test CDN provider outage scenario
3. Verify backup and restore procedures
4. Test cross-region failover capabilities

**Expected Results:**
- ✅ Backup assets restore successfully
- ✅ Alternative CDN providers can be activated
- ✅ Recovery time objective <30 minutes
- ✅ No data loss during recovery

## 📊 Cost Validation Testing

### **TC-018: Cost Monitoring and Validation**
- **GIVEN** CDN deployment is running for 30 days
- **WHEN** analyzing monthly costs
- **THEN** total costs should be 60-70% lower than Docker
- **AND** cost breakdown should match projections

**Test Steps:**
1. Monitor GCS storage costs daily
2. Track CDN egress and cache fill costs
3. Compare with previous Docker deployment costs
4. Analyze cost per user and per GB served

**Expected Results:**
- ✅ Total monthly costs: $8-20 (vs $59-100 Docker, leveraging Cloudflare free tier)
- ✅ Cost reduction: 70-80% as projected
- ✅ No unexpected cost spikes or overruns
- ✅ Cost per user improved significantly

## 📈 Monitoring and Alerting

### **TC-019: Observability Validation**
- **GIVEN** CDN monitoring is configured
- **WHEN** the system is under normal and high load
- **THEN** metrics should be collected and alerts triggered appropriately
- **AND** dashboards should provide clear visibility

**Test Steps:**
1. Configure CDN performance monitoring
2. Set up alerts for availability and performance degradation
3. Test alert triggers with simulated issues
4. Verify dashboard accuracy and completeness

**Expected Results:**
- ✅ Real-time performance metrics available
- ✅ Alerts trigger within 2 minutes of issues
- ✅ Dashboard shows accurate CDN statistics
- ✅ Historical data available for trend analysis

## ✅ Test Completion Criteria

### **Staging Environment (Must Pass)**
- [ ] All infrastructure tests (TC-001 to TC-003) pass
- [ ] Performance improvements validated (TC-004 to TC-006)
- [ ] Functional parity confirmed (TC-007 to TC-009)
- [ ] Security requirements met (TC-010 to TC-011)
- [ ] Rollback procedure validated (TC-012 to TC-013)

### **Production Environment (Must Pass)**
- [ ] All staging tests repeated and passed
- [ ] Cost validation confirmed over 7-day period
- [ ] Monitoring and alerting operational
- [ ] Zero critical issues during 48-hour monitoring period
- [ ] Performance improvements sustained under production load

### **Success Metrics Achievement**
- [ ] Page load time improvement >50% ✅
- [ ] Cost reduction >60% ✅
- [ ] Availability improvement to 99.99% ✅
- [ ] Build time improvement >40% ✅
- [ ] Zero downtime during migration ✅

**Sign-off Required From:**
- DevOps Engineer (Infrastructure)
- Frontend Developer (Functionality)  
- QA Engineer (Testing)
- Product Owner (Business Value)