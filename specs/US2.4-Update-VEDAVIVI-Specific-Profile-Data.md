## US2.4 Update VEDAVIVI-Specific Profile Data
> As a user, I want to be able to update my VEDAVIVI-specific profile data, such as my timezone, via a GraphQL API, so that my PA can use accurate information about me.

### High-Level Objective

  - Enable users to update their VEDAVIVI-specific profile information (e.g., timezone) through a GraphQL API so that their Personal Assistant (PA) can utilize accurate and current user data.

### Mid-Level Objectives

  - Implement a GraphQL mutation that allows an authenticated user to update their VEDAVIVI-specific profile data (specifically timezone for this story).
  - Ensure that the updated profile data (timezone) is persisted in the VEDAVIVI `users` table.
  - Make the stored VEDAVIVI-specific profile data (timezone) accessible to the user's PA for relevant tasks.

### Implementation Notes

  - **Important technical details**:
      * The primary focus is on updating the `timezone` field in the `users` table.
      * The update mechanism must be a GraphQL mutation.
      * Authentication is a prerequisite; only the authenticated user can update their own profile.
      * The PA's access to this information is a key outcome, though the PA's implementation details are beyond this specific user story's backend scope.
  - **Dependencies and requirements**:
      * `users` table schema must have a `timezone` field (or a suitable JSONB field like `preferences` or `settings` to store this) \[Source 1301, Source 469]. The `users` table schema provided in "VEDAVIVI: MVP Backend Technical Plan v1.5" (Source 469) includes `timezone TEXT NULL` and `preferences JSONB NULL, DEFAULT '{}'::jsonb` and `settings JSONB NULL, DEFAULT '{}'::jsonb`. The user story example uses `timezone`, so we'll assume the direct `timezone` field.
      * A GraphQL mutation, (e.g., `updateMyProfile` or a more specific one like `updateMyVedaviviProfile`) and its corresponding resolver must be created or updated \[Source 1231, 1233]. The "VEDAVIVI: MVP Backend Technical Plan v1.5" lists `updateMyA2AProfile` as a key mutation (Source 466), which seems related but might be for A2A specific profile data rather than general VEDAVIVI profile data. Given the user story refers to "VEDAVIVI-specific profile data", a mutation like `updateMyProfile` or `updateMyVedaviviProfile` seems more appropriate. Let's assume we'll define a new one if `updateMyA2AProfile` is not suitable.
      * Frontend UI will be responsible for triggering this mutation (dependency, but not part of backend implementation for this story).
  - **Coding standards to follow**:
      * Adhere to Python backend coding standards.
      * Ensure GraphQL schema best practices are followed for the mutation design.
      * Secure the mutation to ensure users can only update their own profiles.
  - **Other technical guidance**:
      * The mutation should ideally return the updated user profile data.
      * Validation should be in place for the input data (e.g., valid timezone string format).
      * Consider how this fits into the broader User Profile Management (Source 398) and User Authentication and Authorization (Source 405) functional areas.

### Context

**Beginning context**

  - `apps/backend/src/a2a_platform/db/models/user.py`: Contains the SQLAlchemy model for the `users` table, which should include a `timezone` field or a JSONB field for such settings. (Source 469 shows `timezone: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)`).
  - `apps/backend/src/a2a_platform/api/graphql/__init__.py`: Base GraphQL schema setup exists.
  - `apps/backend/src/a2a_platform/auth/clerk.py` and `apps/backend/src/a2a_platform/api/graphql/middleware/auth_middleware.py`: Authentication middleware is in place to identify the authenticated user (e.g., via `clerk_user_id` in the GraphQL context).
  - `apps/backend/src/a2a_platform/services/user_service.py`: May contain functions to interact with user data; new functions might be needed here.

**Ending context**

  - `apps/backend/src/a2a_platform/api/graphql/resolvers/user_profile_resolvers.py` (or similar): Contains the resolver for the new/updated GraphQL mutation.
  - `apps/backend/src/a2a_platform/api/graphql/schemas/user_profile_schemas.py` (or similar): Contains the GraphQL input and type definitions for profile updates.
  - `apps/backend/src/a2a_platform/db/models/user.py`: The `users` table model correctly reflects the `timezone` field being updated.
  - `apps/backend/src/a2a_platform/services/user_service.py`: Contains logic to update the user's timezone in the database.
  - `apps/backend/tests/integration/test_graphql_user_profile.py` (or similar): Contains integration tests for the profile update mutation.
  - `apps/backend/tests/unit/services/test_user_service_profile.py` (or similar): Contains unit tests for the service layer logic.

### Low-Level Tasks

> Ordered from start to finish

1.  **Define GraphQL Mutation and Types for Profile Update**
    ```aider
    Prompt: "Define the GraphQL mutation `updateMyProfile(timezone: String!): User` and any necessary input types or updates to the User type in the GraphQL schema. The mutation should accept `timezone` as an argument and return the updated User object."
    File to CREATE or UPDATE: `apps/backend/src/a2a_platform/api/graphql/schemas/user_profile_schemas.py` (if creating new schema files) or update existing user schema files.
    File to UPDATE: `apps/backend/src/a2a_platform/api/graphql/__init__.py` (to include the new mutation in the main schema).
    Details: "The User type returned should reflect the fields available in the VEDAVIVI user profile, including the timezone."
    ```
2.  **Implement User Profile Update Service Logic**
    ```aider
    Prompt: "Create a function in `user_service.py` called `update_user_profile` that takes `clerk_user_id` and `timezone` as input. This function should fetch the user by `clerk_user_id`, update their `timezone` field in the `users` table, commit the change, and return the updated User SQLAlchemy model object. Handle cases where the user is not found."
    File to CREATE or UPDATE: `apps/backend/src/a2a_platform/services/user_service.py`
    Function to CREATE or UPDATE: `update_user_profile`
    Details: "Ensure the function uses an async session if the rest of the DB access is async. Validate the timezone format if possible, or rely on database constraints/application-level validation."
    ```
3.  **Implement GraphQL Resolver for `updateMyProfile` Mutation**
    ```aider
    Prompt: "Create the GraphQL resolver function for the `updateMyProfile` mutation. This resolver should:
    1. Ensure the user is authenticated (extract `clerk_user_id` from `info.context`).
    2. Call the `update_user_profile` service function with the `clerk_user_id` and the `timezone` input.
    3. Return the updated user information in the GraphQL User type format."
    File to CREATE or UPDATE: `apps/backend/src/a2a_platform/api/graphql/resolvers/user_profile_resolvers.py` (or update existing user resolvers).
    Function to CREATE: `resolve_update_my_profile`
    Details: "Handle potential errors from the service layer, such as user not found, and translate them into appropriate GraphQL errors."
    ```
4.  **Write Unit Tests for User Profile Update Service**
    ```aider
    Prompt: "Write unit tests for the `update_user_profile` function in `user_service.py`. Mock the database session and verify:
    1. The correct user is fetched.
    2. The timezone field is updated.
    3. The session is committed.
    4. The updated user object is returned.
    5. Test behavior when the user is not found."
    File to CREATE or UPDATE: `apps/backend/tests/unit/services/test_user_service_profile.py` (or add to `test_user_service.py`)
    ```
5.  **Write Integration Test for `updateMyProfile` GraphQL Mutation**
    ```aider
    Prompt: "Write an integration test for the `updateMyProfile` GraphQL mutation. This test should:
    1. Simulate an authenticated user.
    2. Send a GraphQL request to the `updateMyProfile` mutation with a new timezone.
    3. Verify the mutation returns a successful response with the updated user data.
    4. Query the test database directly to confirm the `timezone` field for the user was updated.
    5. Test the mutation with an unauthenticated user to ensure it fails."
    File to CREATE or UPDATE: `apps/backend/tests/integration/test_graphql_user_profile.py` (or add to existing GraphQL integration tests)
    ```
6.  **Verify PA Accessibility (Conceptual/Cross-Team Check)**
    ```aider
    # This is not a direct code generation task but a verification step.
    Prompt: "Confirm with the team responsible for the Personal Assistant that the updated timezone information, once stored in the users table, will be accessible and utilized by the PA for relevant tasks as per existing or planned data access patterns for the PA."
    # No specific file. This is a check on system-level data flow.
    ```
