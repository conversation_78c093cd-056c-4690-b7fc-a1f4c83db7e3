# Register Specialized Agents
> Ingest the information from this file, implement the Low-Level Tasks, and generate the code that will satisfy the High and Mid-Level Objectives.

# SYSTEM
<PERSON> are a senior backend engineer. Prioritize strong typing, modular design, and scalability. All code should be efficient and easy to maintain. After completing any code changes, always ensure there are corresponding unit tests. If the tests already exist, run them. If they do not exist, create them. Confirm that all tests pass before considering the work complete.

## High-Level Objective

- As a VEDAVIVI backend administrator/developer, I want to be able to register internal Specialized Agents (SAs) in the registered_agents table, including their definition ID, name, endpoint, and capabilities, so that they become discoverable and usable by Personal Assistants within the system for MVP.

## Mid-Level Objective

- Given I have the details of an internal SA, When I insert a new record into the registered_agents table, Then the SA's agent_definition_id, name, description, version, endpoint_url (if sync), async_queue_name (if async), capabilities, and skills are stored.
- Given an SA is registered, Then PAs can look up its details for A2A communication.
- Create an install script fror the backend
- Test the code.
- Add testing instructions to the readme.
- Update the Low-Level Task with items.


## Implmentation Notes

## Context

### Beginning context

- `/README.md`
- `/adrs/ADR-001-Architecture-Overview.md`
- `/adrs/ADR-002-Backend-Overview.md`
- `/adrs/ADR-003-Frontend-Overview.md`
- `/adrs/ADR-004-Deployment-Overview.md`
- `/specs/register-specialized-agents-v1.md`

### Ending context

- `/README.md`
- `/adrs/ADR-001-Architecture-Overview.md`
- `/adrs/ADR-002-Backend-Overview.md`
- `/adrs/ADR-003-Frontend-Overview.md`
- `/adrs/ADR-004-Deployment-Overview.md`
- `/specs/register-specialized-agents-v1.md` (updated)
- `/apps/backend/requirements.txt`
- `/apps/backend/README.md`

## Implementation Notes

### Project Structure

apps/backend/
├── alembic/               # Database migrations
│   └── versions/         # Migration scripts
├── src/
│   └── a2a_platform/
│       ├── api/          # API layer
│       │   ├── graphql/  # GraphQL API
│       │   │   ├── resolvers/
│       │   │   └── schema/
│       │   └── rest/     # REST API
│       │       ├── controllers/
│       │       └── routes/
│       ├── auth/         # Authentication & authorization
│       ├── config/       # Configuration management
│       ├── db/           # Database models and config
│       │   └── models/   # SQLAlchemy models
│       ├── messaging/    # Messaging system
│       │   ├── pub_sub/ # Pub/Sub implementation
│       │   └── queue/   # Message queue handlers
│       ├── schemas/      # Pydantic models
│       ├── services/     # Business logic
│       ├── storage/      # File storage & CDN
│       ├── utils/        # Shared utilities
│       ├── vector_db/    # Vector database integration
│       └── workers/      # Background workers
└── tests/                # Test suite
    ├── e2e/             # End-to-end tests
    ├── integration/     # Integration tests
    └── unit/           # Unit tests

## Low-Level Tasks

1.  **Define Data Structure/Model for `RegisteredAgent`:**
    *   Ensure the model accurately reflects all fields specified: `agent_definition_id` (Primary Key), `name`, `description`, `version`, `endpoint_url` (nullable), `async_queue_name` (nullable), `capabilities` (JSONB), `skills` (JSONB), `authentication_info` (JSONB), `status`, `created_at`, `updated_at`. (Refer to `ADR-002-Backend-Overview.md` for the `registered_agents` table schema).
    *   Consider constraints: `agent_definition_id` must be unique. `name`, `version`, `status` are NOT NULL. `status` should be one of ('active', 'inactive').
2.  **Implement Registration Logic (Service Layer):**
    *   Create a function/method, e.g., `register_specialized_agent(agent_data: ValidatedRegisteredAgentInput) -> RegisteredAgentModel`.
    *   Input data should be validated (e.g., using Pydantic or similar for strong typing and business rules) before hitting this core logic.
    *   Handle potential database conflicts gracefully (e.g., duplicate `agent_definition_id` should raise a specific, catchable error).
    *   Persist the valid agent data to the `registered_agents` table.
    *   Return the created `RegisteredAgentModel` object (or relevant representation).
3.  **Develop Unit Tests for Registration Logic:**
    *   Test successful registration of a new SA with only a synchronous `endpoint_url`.
    *   Test successful registration of a new SA with only an `async_queue_name`.
    *   Test successful registration of a new SA with both `endpoint_url` and `async_queue_name`.
    *   Test successful registration with minimal required fields and default values applied correctly (e.g., for `capabilities`, `skills`, `status`).
    *   Test registration failure due to missing required fields (e.g., `agent_definition_id`, `name`, `version`). (This might be caught by a validation layer before the service logic).
    *   Test registration failure due to duplicate `agent_definition_id` (ensure database integrity error is handled and surfaces as a clean application error).
    *   Test registration failure due to invalid `status` value (e.g., "pending" if only "active" or "inactive" are allowed). (This might be caught by a validation layer).
    *   Test that `created_at` and `updated_at` timestamps are set correctly upon creation.
    *   Test that `updated_at` timestamp is updated if an update mechanism is implemented (though not in scope for initial registration).
4.  **Design and Implement MVP Mechanism for Admin Registration:**
    *   **Option A (Script-based - preferred for initial MVP):**
        *   Develop a Python script (e.g., `scripts/register_agent.py`) that accepts agent details (e.g., from a JSON file or command-line arguments).
        *   The script will utilize the registration logic developed in step 2.
        *   Ensure the script provides clear success/failure feedback for each agent registration attempt.
    *   **Option B (Internal API Endpoint - for future extensibility):**
        *   Define a simple, secured internal API endpoint (e.g., `POST /internal/admin/agents/register`).
        *   This endpoint would take the agent details as a request body and use the registration logic.
        *   Authentication/Authorization for this internal endpoint must be considered.
5.  **Document the Agent Registration Process:**
    *   Create a `README.md` or a document in a `docs/administration` folder.
    *   Detail the structure of the input data (e.g., JSON schema for the agent details if using a script with file input).
    *   Explain how to use the chosen MVP mechanism (e.g., command to run the script, example JSON).
    *   List all fields for an SA, their purpose, data types, and any constraints or example values.
6.  **Integrate with Database:**
    *   Ensure database migrations (if using a migration tool like Alembic for SQLAlchemy) are created or updated to reflect the `registered_agents` table schema from `ADR-002-Backend-Overview.md`.
    *   Confirm the application has the necessary database connection and permissions.
7.  **Define Agent Lookup Logic (Service Layer):**
    *   Implement function(s)/method(s) for PAs to query `registered_agents` data.
    *   `get_specialized_agent_by_id(agent_definition_id: str) -> Optional[RegisteredAgentModel]`
        *   Retrieves a single SA by its unique `agent_definition_id`.
        *   Returns the agent model or `None` (or raises a specific "NotFound" error) if not found.
    *   `list_specialized_agents(status: Optional[str] = 'active', capabilities_filter: Optional[List[str]] = None, skills_filter: Optional[List[str]] = None) -> List[RegisteredAgentModel]`
        *   Retrieves a list of SAs, typically filtering by `status='active'` by default.
        *   Allows optional filtering by `capabilities` (e.g., SA must have ALL specified capabilities) and/or `skills`. The exact filtering logic for JSONB fields needs careful consideration (e.g., `jsonb_path_exists`, `?&` operator in PostgreSQL for checking existence of all keys in an array).
8.  **Develop Unit Tests for Agent Lookup Logic:**
    *   Test successful retrieval of an existing SA by `agent_definition_id`.
    *   Test retrieval by `agent_definition_id` when the agent does not exist (ensure it returns `None` or the agreed-upon not-found behavior).
    *   Test listing active SAs with no filters (should return all agents with `status='active'`).
    *   Test listing SAs with `status='inactive'`.
    *   Test listing SAs filtered by one or more `capabilities`.
    *   Test listing SAs filtered by one or more `skills`.
    *   Test listing SAs with combined `capabilities` and `skills` filters.
    *   Test listing SAs when no agents match the filter criteria (should return an empty list).
    *   Test listing SAs when the `registered_agents` table is empty.
9.  **(For PAs) Define and Implement Internal API Endpoint(s) for Agent Lookup:**
    *   Expose the lookup logic via secured internal API endpoint(s) that PAs can call, aligning with `ADR-002-Backend-Overview.md` which specifies OpenAPI for internal synchronous HTTP APIs.
    *   Example Endpoints:
        *   `GET /internal/agents/{agent_definition_id}`: Retrieves a specific agent by its definition ID.
        *   `GET /internal/agents/`: Lists agents, supporting query parameters for `status`, `capabilities` (e.g., `capability=summary&capability=translation`), `skills` (e.g., `skill=data_analysis`).
    *   Ensure these endpoints use the internal service-to-service authentication mechanism defined in the architecture.
10. **Document Agent Lookup API for PA Developers:**
    *   Provide clear documentation for PA developers on how to use the internal agent lookup API.
    *   Include endpoint URLs, precise request parameters (path, query strings, data types), expected response formats (JSON schemas), example requests/responses, and authentication requirements.
    *   This documentation should be easily accessible to teams developing Personal Assistants.