# User Story US5.5: Internal Task Management for Personal Assistant

**User Story:** As the Personal Assistant, I want to manage internal tasks associated with user objectives or my own operational needs in the `tasks` table. This system should allow me to break down work into manageable units, including sub-tasks, track progress internally, ensure resilient execution through mechanisms like timeouts, retries, and leasing, effectively handle situations requiring human input or escalation, and ensure that task creation is idempotent.

## 1. High-Level Objective
Enable the Personal Assistant (PA) to autonomously create, manage, track, and ensure the resilient, interactive, and hierarchical execution of internal tasks within the `tasks` table. These tasks, which can include sub-tasks, are essential for the PA to break down complex work, manage dependencies, handle execution issues robustly (including escalation), incorporate human input when necessary, and ensure idempotent creation.

## 2. Mid-Level Objectives
* **Hierarchical Task Creation & Idempotency:** Given the PA identifies a need for a distinct piece of work, Then the PA can create a new task record (or sub-task linked to a parent task) in the `tasks` table. Task creation must be idempotent (e.g., based on a unique PA-generated idempotency key provided at creation). Records include description, initial status, linkage to the assistant and parent task (if any), and metadata for execution, monitoring, and human interaction.
* **Task Status & Lifecycle Management:** Given an internal task exists, Then its status can be updated by the PA's internal logic or the underlying system to reflect its lifecycle (e.g., 'todo', 'in_progress', 'done', 'retrying', 'pending_dependency', 'pending_human_input', 'escalated').
* **Objective Linking:** Given an internal task supports a user objective, Then it can be optionally linked to the relevant `assistant_objective` record.
* **Stuck/Failed Task Detection & Recovery:** The system must detect unresponsive or failing tasks and support automated retries with backoff.
* **Task Quarantining & Escalation:** Persistently failing tasks must be transitioned to a terminal failure/quarantined state or escalated if they require higher-level intervention.
* **Human Input Workflow Support:**
    * **Identification & Pause:** Given the PA determines a task requires human input, Then the task can be transitioned to a 'pending_human_input' status, storing context for the required input.
    * **Input Ingestion & Resumption:** Given human input is provided, Then the input is recorded, and the task can resume processing.
* **Task Dependency Management (Simple Sequential):** The system must support defining a task as dependent on the completion of another task, preventing its execution until the prerequisite is met.
* **Task Leasing (for distributed execution):** If tasks are processed by distributed workers, a leasing mechanism should ensure tasks are exclusively processed and can be recovered if a worker fails.

## 3. Implementation Notes
* **Nature of Internal Tasks:** PA-managed tasks for operational efficiency or supporting user objectives, distinct from user-facing task features.
* **Sub-tasks & Dependencies:** Tasks can have a `parent_task_id` for hierarchical breakdown. A `depends_on_task_id` can model simple sequential dependencies. The PA's AI/LLM logic determines this structure.
* **Idempotent Creation:** The `POST /internal/assistants/my/tasks` API will accept an optional idempotency key. If a key for a task from a specific assistant has been seen recently for a task not yet in a terminal state, the existing task details are returned without creating a new one. This prevents duplicate tasks from rapid PA decisions or retries at the PA logic level.
* **Task-Objective Relationship:** `objective_id` in `tasks` links internal actions to `assistant_objectives`.
* **Backend Interaction:** Via internal APIs (`POST /internal/assistants/my/tasks`, `PUT /internal/tasks/{task_id}`, `DELETE /internal/tasks/{task_id}`).
* **Handling Long-Running/Stuck Tasks:**
    * **Task Workers & Leasing:** Processed by asynchronous workers. Leasing via `lease_owner_id`, `lease_expires_at` if distributed.
    * **Heartbeating & Timeouts:** Tasks update `last_progress_at`; `expected_duration_seconds` (soft timeout) and system hard timeouts.
    * **Watchdog:** A process may monitor for stale tasks and trigger status changes (e.g., to 'failed_timeout' or 'escalated').
* **Handling Tasks Requiring Human Input:**
    * **PA Identifies Need:** AI/LLM logic determines when human input is needed.
    * **State & Context:** Task status becomes `pending_human_input`; `metadata` stores the request details.
    * **Notification & Input:** PA notifies human (user via chat; admin via alerts). Input is received, stored in `metadata`, and task resumes.
    * **Timeouts & Escalation for Human Input:** If input isn't timely, task status may change to `failed_human_timeout` or `escalated`.
* **Task Escalation:** Tasks that are critically stuck, repeatedly fail, or where human input times out critically, can be moved to an `escalated` status, signaling a need for higher-level review (potentially by an admin or a more sophisticated PA recovery process).
* **Metadata Implementation Details:**
    * **Database Storage:** To avoid conflicts with SQLAlchemy's reserved attributes, the `metadata` field is stored as `metadata_json` in the Task model.
    * **API Schema Mapping:** The API schemas consistently map between `metadata_json` in the database model and `metadata` in the Pydantic schemas to maintain a clean API contract.
    * **Field Conversion:** All API endpoints implement consistent field conversion between the ORM model and API schemas to ensure proper handling of metadata.

## 4. Dependencies
* D1: `tasks` table schema (see Section 11).
* D2: Internal task management APIs.
* D3: Core AI/LLM for task decomposition, dependency identification, human input needs, and processing responses.
* D4: Asynchronous task processing system.
* D5: Notification mechanisms for human input or escalations.
* D6: Interfaces for humans to provide input.

## 5. Assumptions
* A1: PA's AI/LLM can determine task creation, updates, dependencies, human input needs, and process responses.
* A2: `tasks` and `assistant_objectives` tables are accessible.
* A3: Tasks link to a specific assistant.
* A4: Asynchronous task processing and notification infrastructures are in place.
* A5: Mechanisms exist for human input provision.
* A6: The system can support atomic operations for task leasing and idempotent creation checks.

## 6. Scope
#### In Scope
* Backend logic for PA to create (idempotently) and update hierarchical tasks (parent/child) and tasks with simple sequential dependencies.
* Status management for lifecycle, retries, failures, pending dependencies, pending human input, and escalations.
* Storing context for human input requests/responses and dependency information in `metadata` or dedicated fields.
* Task management system support for: retries, heartbeating, basic timeout detection, task quarantining/escalation, lease management (if applicable).

#### Out of Scope
* UI for direct user management of internal tasks.
* Complex dependency graph management (beyond simple `depends_on_task_id`).
* Dynamic task prioritization beyond inherent ordering (e.g., by `created_at` or processing by available workers). The `priority` field from the previous spec version is removed based on the new recommendations, simplifying this aspect for MVP.
* Detailed implementation of AI/LLM logic (D3).
* Detailed UI/Notification systems (D5, D6).
* Watchdog process implementation.

## 7. Acceptance Criteria
* AC1: Given the PA creates an internal task (optionally with `parent_task_id`, `depends_on_task_id`, or `idempotency_key`), Then a new record appears in `tasks` with appropriate initial values (or existing task is returned if idempotency key matches an active task).
* AC2: Given an internal task's status changes, Then `status`, relevant timestamps, and `metadata` in `tasks` table are updated.
* AC3: Given a task fails transiently, Then `retry_count` increments, status becomes 'retrying', and it's scheduled for a future attempt.
* AC4: Given a task fails max retries, Then its status becomes 'failed_max_retries' or 'quarantined'.
* AC5: Given a long-running task reports progress, Then `last_progress_at` is updated.
* AC6: Given a task is stuck (exceeds duration without progress, lease expires), Then it's subject to intervention (e.g., lease re-acquisition, status change to 'failed_timeout' or 'escalated').
* AC7: (If leasing) Given a task is leased, Then `lease_owner_id` and `lease_expires_at` are set.
* AC8: Given PA needs human input, Then task status changes to 'pending_human_input' (or specific variant), `metadata` stores request details.
* AC9: Given human input is provided, Then task `metadata` stores the response, status changes to an actionable state.
* AC10: Given human input times out, Then task status changes to 'failed_human_timeout' or 'escalated'.
* AC11: Given a task has a `depends_on_task_id`, When its prerequisite task is not 'done', Then its status is 'pending_dependency' and it is not processed.
* AC12: Given a prerequisite task (linked via `depends_on_task_id`) completes, Then the dependent task's status transitions from 'pending_dependency' to 'todo'.
* AC13: Given a task with `parent_task_id` completes, Then this event is available for the parent task's logic to determine its own status progression.
* AC14: Given a task is critically stuck or failed and cannot be resolved by automated retries or user input, Then its status can be changed to 'escalated' for administrative review.
* AC15: Given the PA attempts to create/update/delete a task with invalid data, Then the operation fails gracefully.
* AC16: Given a task is cancelled (via `DELETE /internal/tasks/{task_id}`), Then its status becomes 'cancelled'.

## 8. Non-Functional Requirements
* **NFR1 (Idempotency):** Task creation via API must support idempotency. Task execution logic must be idempotent for retries.
* **NFR2 (Data Integrity):** Enforced constraints and accurate state transitions.
* **NFR3 (Robustness):** Graceful handling of operational errors.
* **NFR4 (Stuck Task Detection & Escalation):** Reliable detection and defined escalation paths.
* **NFR5 (Resource Management):** Prevention of stuck tasks from indefinite excessive resource consumption.
* **NFR6 (Observability):** Availability of key metrics for monitoring.
* **NFR7 (Clarity of Human Input Requests):** Clear context for human input.
* **NFR8 (Hierarchical Integrity):** Consistency in parent-child task relationships.

## 9. Risks and Mitigations
* **R1: AI Task Proliferation/Poor Human Input/Dependency Loops:** AI creates excessive/cyclic tasks or unclear human requests.
    * Mitigation: Robust AI testing, rate limiting, cycle detection in dependency logic, clear guidelines for AI on human input.
* **R2: Flawed Stuck/Pending/Escalated Task Detection:** Incorrectly flagging or missing problematic tasks.
    * Mitigation: Tunable parameters, thorough testing, phased rollout.
* **R3: Retry Storms:**
    * Mitigation: Exponential backoff, jitter, max retry limits, circuit breakers.
* **R4: Lease Contention/Clock Skew (if leasing):**
    * Mitigation: NTP, atomic lease acquisition, graceful handling of failures.
* **R5: Cascading Failures in Dependencies:** Failure of one task blocks many others.
    * Mitigation: Design for fault tolerance in dependent task logic; clear error propagation; potential for PA to re-plan if a critical dependency fails.
* **R6: Human Input Bottleneck/Delay/Escalation Overload:**
    * Mitigation: Clear notification systems, defined SLAs for responses, clear escalation criteria and capacity planning for handling escalated tasks.

## 10. API Specification (Internal)
(Leveraging and extending endpoints from "VEDAVIVI: MVP Backend Technical Plan v1.5")

#### Create Task
* **Endpoint:** `POST /internal/assistants/my/tasks`
* **Headers:**
    * `X-Idempotency-Key: <uuid>` (Optional: for idempotent creation)
* **Request Body:**
    ```json
    {
      "description": "string (NOT NULL)",
      "objective_id": "uuid (optional, FK to assistant_objectives)",
      "parent_task_id": "uuid (optional, FK to tasks, for sub-tasks)",
      "depends_on_task_id": "uuid (optional, FK to tasks, for simple sequence)",
      "initial_status": "string (optional, e.g., 'todo', 'pending_dependency', 'pending_human_input')",
      "metadata": {
        "expected_duration_seconds": "integer (optional)",
        "max_retries": "integer (optional)",
        "human_input_request": { /* ... details ... */ }
        // other custom metadata
      }
    }
    ```
* **Response Body (Success 201 Created / 200 OK if idempotency key reused for active task):** Full task object.

#### Update Task (Status, Progress, Human Input, etc.)
* **Endpoint:** `PUT /internal/tasks/{task_id}`
* **Path Parameters:** `task_id` (uuid)
* **Request Body:**
    ```json
    {
      "status": "string (optional)", // e.g., 'in_progress', 'pending_human_input', 'done', 'escalated' etc.
      "description": "string (optional)",
      "last_progress_at": "datetime (optional, for heartbeating)",
      "metadata": { /* ... updated metadata, including human_input_response ... */ }
      // If leasing is managed via API: worker_id, lease_expires_at could be here
    }
    ```
* **Response Body (Success 200 OK):** Full updated task object.

#### Delete/Cancel Task
* **Endpoint:** `DELETE /internal/tasks/{task_id}`
* **Behavior:** Sets task status to 'cancelled'.
* **Response Body (Success 200 OK or 204 No Content):**

## 11. Data Model (`tasks` table)
(Based on "VEDAVIVI: MVP Backend Technical Plan v1.5" with resilience, hierarchy, dependency, and human input enhancements. `priority` field removed.)

| Column Name                 | Data Type     | Constraints                                                                                                                                                                                                      | Notes                                                                                                                              |
| :-------------------------- | :------------ | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------- |
| `id`                        | UUID          | `PRIMARY KEY, DEFAULT gen_random_uuid()`                                                                                                                                                                         | Unique identifier.                                                                                                                 |
| `assistant_id`              | UUID          | `NOT NULL, REFERENCES assistants(id) ON DELETE CASCADE`                                                                                                                                                          | Owning assistant.                                                                                                                  |
| `objective_id`              | UUID          | `NULL, REFERENCES assistant_objectives(id) ON DELETE SET NULL`                                                                                                                                                   | Optional linked objective.                                                                                                         |
| `parent_task_id`            | UUID          | `NULL, REFERENCES tasks(id) ON DELETE CASCADE`                                                                                                                                                                   | For hierarchical sub-tasks.                                                                                                        |
| `depends_on_task_id`        | UUID          | `NULL, REFERENCES tasks(id) ON DELETE SET NULL`                                                                                                                                                                  | For simple sequential dependency (task that must complete before this one).                                                        |
| `idempotency_key`           | TEXT          | `NULL, UNIQUE (assistant_id, idempotency_key)`                                                                                                                                                                 | Optional key provided by PA for idempotent creation. Constraint ensures uniqueness per assistant.                                  |
| `description`               | TEXT          | `NOT NULL`                                                                                                                                                                                                       | Task description.                                                                                                                  |
| `status`                    | TEXT          | `NOT NULL, DEFAULT 'todo', CHECK (status IN ('todo', 'leased', 'in_progress', 'pending_dependency', 'pending_human_input', 'pending_user_clarification', 'pending_admin_review', 'done', 'cancelled', 'retrying', 'failed_timeout', 'failed_max_retries', 'failed_human_timeout', 'quarantined', 'escalated'))` | Current task status.                                                                                                               |
| `due_date`                  | TIMESTAMPTZ   | `NULL`                                                                                                                                                                                                           | Optional due date.                                                                                                                 |
| `completed_at`              | TIMESTAMPTZ   | `NULL`                                                                                                                                                                                                           | Timestamp of completion.                                                                                                           |
| `created_at`                | TIMESTAMPTZ   | `NOT NULL, DEFAULT now()`                                                                                                                                                                                        | Creation timestamp.                                                                                                                |
| `updated_at`                | TIMESTAMPTZ   | `NOT NULL, DEFAULT now()`                                                                                                                                                                                        | Last update timestamp.                                                                                                             |
| `retry_count`               | INTEGER       | `NOT NULL, DEFAULT 0`                                                                                                                                                                                            | Number of execution attempts.                                                                                                      |
| `last_progress_at`          | TIMESTAMPTZ   | `NULL`                                                                                                                                                                                                           | Last heartbeat/progress report.                                                                                                    |
| `lease_owner_id`            | TEXT          | `NULL`                                                                                                                                                                                                           | Worker ID holding the lease (if leasing).                                                                                            |
| `lease_acquired_at`         | TIMESTAMPTZ   | `NULL`                                                                                                                                                                                                           | Lease acquisition time (if leasing).                                                                                                 |
| `lease_expires_at`          | TIMESTAMPTZ   | `NULL`                                                                                                                                                                                                           | Lease expiry time (if leasing).                                                                                                      |
| `metadata`                  | JSONB         | `NULL, DEFAULT '{}'::jsonb`                                                                                                                                                                                      | Flexible storage: `expected_duration_seconds`, `max_retries`, error details, `human_input_request`, `human_input_response`, etc. |
| **Indexes:** |               | `(assistant_id, status)`, `(objective_id)`, `(parent_task_id)`, `(depends_on_task_id)`, `(status, lease_expires_at)`, `(status, updated_at)`, `(idempotency_key)` | For efficient querying.                                                                                                            |

*(Note: For more complex many-to-many dependencies, a separate `task_dependencies` table (`task_id`, `depends_on_task_id`) would be considered as a future enhancement if simple `depends_on_task_id` becomes insufficient.)*

## 12. Release Plan (Conceptual)
* **Phase 1 (Core & Basic Resilience):** Task CRUD, status management (including retry, basic failure states), `parent_task_id` for sub-tasks, simple `depends_on_task_id`, and idempotent creation.
* **Phase 2 (Enhanced Resilience & Human Input):** Leasing (if needed for distributed workers), detailed heartbeating, more granular timeout handling, full human input workflow states, and task escalation mechanisms.

## 13. Future Considerations
* Develop a dedicated admin UI for operational task monitoring, including views for escalated tasks, stuck tasks, tasks awaiting human input, and manual intervention capabilities.
* Implement more sophisticated watchdog services for proactive detection and automated recovery of complex failure scenarios, including analysis of dependency chains.
* Introduce dynamic adjustment of task parameters (e.g., `expected_duration_seconds`, `max_retries`) based on observed performance or task type.
* Allow PA to learn from persistently failing or escalated tasks to improve its own task decomposition, dependency planning, or execution strategies.
* Provide more granular insight into internal task progress (including sub-tasks and dependencies) if/when users query about the status of a larger objective.
* Support for more complex dependency graphs (e.g., a task depending on multiple other tasks) via a dedicated `task_dependencies` table.
* Conditional task execution paths (workflows beyond simple sequential dependencies).
* Recurring task templates and scheduling.

## 14. Epic
* **Personal Assistant Capabilities & Operational Intelligence**
    * *Clarification*: This user story is foundational to the Personal Assistant's intelligence, autonomy, and reliability. It enables the PA to manage its internal workload effectively using hierarchical and dependent tasks, recover from operational issues, intelligently engage with humans when its autonomous capabilities reach their limits, and handle escalations. These capabilities are critical for achieving user objectives and maintaining system health.

---
## Appendix: Forward-Looking Recommendations for Task Domain Evolution

The internal task management system, as defined in US5.5, serves as a critical building block for the Personal Assistant's operational capabilities. As VEDAVIVI evolves, this Task domain can be expanded to support even more sophisticated agent behaviors and platform features.

### Short-Term (Next 6-12 Months)

1.  **Enhanced Dependency Management:**
    * **Many-to-Many Dependencies:** Introduce a `task_dependencies` junction table (`task_id`, `depends_on_task_id`, `dependency_type (e.g., 'finish-to-start', 'start-to-start')`) to allow a task to depend on multiple prerequisites, and multiple tasks to depend on a single one. This will enable more complex workflow construction by the PA.
    * **Circular Dependency Detection:** Implement mechanisms at the API level or as a background process to detect and prevent or flag circular dependencies when tasks are created or updated.
2.  **Advanced Watchdog & Automated Recovery:**
    * Develop a more sophisticated watchdog service that not only detects stuck/stale tasks but can also trigger predefined automated recovery scripts or more nuanced escalation paths based on task type or error patterns.
    * Integrate watchdog insights with PA's learning loop (e.g., if a certain type of task frequently times out, the PA might learn to allocate more `expected_duration_seconds` or break it down further).
3.  **Granular Task Output Management:**
    * Formalize a structure within `tasks.metadata` (or a new related table like `task_outputs`) for tasks to store their results in a typed, queryable manner. This allows subsequent tasks to consume these outputs reliably as inputs.
4.  **Basic Admin UI for Task Oversight:**
    * Develop a simple internal dashboard for administrators/developers to view tasks by status (especially 'failed_max_retries', 'quarantined', 'escalated', 'pending_admin_review'), inspect their metadata, and manually trigger actions like retry, cancel, or reassign (if a concept of ownership/assignee is introduced beyond leasing).

### Mid-Term (12-24 Months)

1.  **Conditional Task Execution & Workflow Engine Lite:**
    * Introduce capabilities for defining conditional paths (e.g., "if Task A succeeds with result X, then execute Task B, else execute Task C"). This could involve storing conditions in `metadata` and having the task completion logic (or a small workflow engine component) evaluate these to trigger next tasks.
    * This allows the PA to build more dynamic and adaptive plans.
2.  **Recurring Task Engine:**
    * Implement a system for defining and scheduling recurring internal tasks (e.g., daily data synchronization for the PA, weekly report generation for its own metrics). This would involve task templates and a robust scheduling component.
3.  **Task Prioritization & Resource Management Enhancement:**
    * While the simple `priority` field was removed for MVP simplicity, re-evaluate the need for more dynamic prioritization, potentially including preemption if system resources become constrained and high-priority PA operations are blocked.
    * Consider basic resource quotas or swimlanes for different types of PA tasks if contention becomes an issue.
4.  **Integration with User-Facing Task/Objective Visibility:**
    * Explore ways to (optionally and with user consent) expose a high-level summary or status of relevant internal task groups to the user, especially when they directly contribute to a user-defined objective. This enhances transparency. For example, "Your PA is currently performing 3 sub-tasks to research your travel options."

### Long-Term (24+ Months)

1.  **Full-Fledged Internal Workflow Orchestration:**
    * Consider adopting or building a more comprehensive internal workflow orchestration engine (e.g., leveraging technologies like Temporal, Cadence, or Apache Airflow if the complexity warrants it) for managing highly complex, long-running, and fault-tolerant internal PA processes composed of many tasks.
2.  **Saga Pattern for Distributed Transactions:**
    * For complex PA operations that involve multiple internal tasks and potentially interactions with external systems, implement the Saga pattern to ensure data consistency across services through compensating transactions.
3.  **AI-Driven Task Optimization & Self-Healing:**
    * Enable the PA to analyze historical task performance (durations, failure rates, resource consumption, human intervention patterns) to optimize its own task decomposition strategies, predict potential issues, and even attempt automated self-healing for common problems.
4.  **Cross-Assistant Task Collaboration (Internal):**
    * If the platform evolves to have multiple internal specialized system agents beyond the user's PA, establish patterns for these system agents to assign and track tasks amongst themselves for platform operations.

These recommendations aim to evolve the internal task management system from a foundational component into a powerful enabler of increasingly sophisticated, reliable, and intelligent Personal Assistant behaviors. The key is an iterative approach, building complexity only as genuinely required by the PA's evolving capabilities and the platform's needs.
