# US2.2: User Login with Existing Method
> As an existing user, I want to be able to log in to VEDAVIVI using my previously chosen authentication method (Google, GitHub, or email), so that I can access my Personal Assistant and data.

## High-Level Objective

- Enable an existing user to securely log in to the VEDAVIVI platform using their previously established authentication method (Google, GitHub, or email via Clerk.com) to access their Personal Assistant and associated data.

## Mid-Level Objectives

- Provide a clear and intuitive user interface on the VEDAVIVI login page that allows users to select their chosen existing authentication method (Google, GitHub, or Email).
- Integrate seamlessly with Clerk.com's authentication services to handle the respective authentication flow for the selected method.
- Upon successful authentication by Clerk.com, establish a secure user session within the VEDAVIVI application by validating the JWT issued by <PERSON>.
- Redirect the successfully authenticated user to their personalized VEDAVIVI dashboard or the appropriate post-login landing page.
- Implement comprehensive error handling to provide informative feedback to the user in case of authentication failures (e.g., incorrect credentials, provider errors, account not found with that method).

## Implementation Notes

-   **Important Technical Details:**
    * **Frontend:**
        * Utilize Clerk.com's frontend SDK (e.g., `@clerk/clerk-react` or equivalent for the tech stack detailed in `VEDAVIVI: MVP Frontend Technical Plan` [Source: 116, 67]).
        * Implement UI components for presenting login options: Google, GitHub, and Email. These will trigger the corresponding Clerk authentication flows.
        * Handle authentication callbacks/redirects from Clerk and securely manage the JWT session on the client-side [Source: 116].
        * Update application state (e.g., via Zustand [Source: 110, 139]) upon successful login to reflect authenticated status and user information.
    * **Backend:**
        * Utilize Clerk.com's backend SDK for the chosen language (Python, as per `VEDAVIVI: MVP Backend Technical Plan v1.5` [Source: 390]) to validate JWTs sent from the frontend in API requests (GraphQL API [Source: 391, 406]).
        * Authenticated user identity will be based on the `clerk_user_id` extracted from the validated JWT [Source: 408]. This ID is used to fetch VEDAVIVI-specific user data from the `users` table [Source: 398, 468].
        * All protected backend resources must enforce authentication by validating the Clerk JWT.
    * **Authentication Methods:**
        * **Google & GitHub:** OAuth 2.0 flow managed by Clerk.com.
        * **Email:** Passwordless login (e.g., magic link or OTP) managed by Clerk.com [Source: 405].

-   **Dependencies and Requirements:**
    * Full integration of Clerk.com for both frontend and backend [User Story Dependency].
    * Clerk.com application configured with Google, GitHub, and Email (passwordless) providers enabled.
    * Frontend environment set up with Clerk SDK and necessary UI components for login.
    * Backend environment set up with Clerk SDK for token validation.
    * The `users` table in the PostgreSQL database must have a `clerk_user_id` column to link VEDAVIVI users to their Clerk identity [Source: 468].

-   **Coding Standards to Follow:**
    * Adhere to security best practices for web authentication, including secure handling of JWTs and protection against common vulnerabilities (e.g., XSS, CSRF).
    * Follow established VEDAVIVI coding guidelines and style guides for both frontend and backend development.
    * Ensure all code is well-documented and includes unit/integration tests.
    * Error handling must be robust, providing clear and non-exploitable messages to users and appropriate logging for developers.

-   **Other Technical Guidance:**
    * The login page UI/UX should be consistent with the overall VEDAVIVI design language.
    * The authentication flow should feel seamless to the user, even when interacting with Clerk-provided interfaces or redirects.
    * Refer to `VEDAVIVI: MVP Backend Technical Plan v1.5` (Section 2.2: User Authentication and Authorization [Source: 404-411]) and `VEDAVIVI: MVP Frontend Technical Plan` (Section 3.1: User Authentication and Profile Management [Source: 115-119]) for detailed architectural decisions.

## Context

-   **Beginning Context:**
    * Existing VEDAVIVI frontend and backend applications.
    * Clerk.com SDKs are assumed to be installed/available in both frontend and backend projects.
    * A basic login page structure might exist but requires integration with Clerk's specific authentication methods.
    * User data, including `clerk_user_id`, is stored in the backend `users` table for existing users who have previously signed up.

-   **Ending Context:**
    * A fully functional login system allowing existing users to authenticate via Google, GitHub, or Email using Clerk.com.
    * Authenticated users have an active session and can access protected routes and their data within VEDAVIVI.
    * Relevant frontend components and backend middleware/services for handling these authentication flows are implemented and tested.

## Low-Level Tasks
> Ordered from start to finish

1.  **Frontend: Design Login Page UI**
    * Task: Create or update the login page UI to include distinct selection elements (buttons or links) for "Sign in with Google," "Sign in with GitHub," and "Sign in with Email."
    ```aider
    # This is a conceptual task for UI/UX design and component creation.
    # Actual prompt would depend on the specific UI framework and component library (React, ShadCN UI, Tailwind CSS [Source: 103, 106]).
    # Example for a conceptual discussion:
    # "Design a login page component in React using Tailwind CSS and ShadCN UI components.
    # It should feature three distinct buttons: 'Sign in with Google', 'Sign in with GitHub', and 'Sign in with Email'.
    # The page should have a professional and trustworthy appearance, aligning with VEDAVIVI's brand.
    # Ensure the layout is responsive for various screen sizes."
    # UPDATE: src/pages/LoginPage.tsx (or equivalent)
    # UPDATE: src/components/auth/GoogleLoginButton.tsx, GithubLoginButton.tsx, EmailLoginButton.tsx (or equivalent)
    ```

2.  **Frontend: Implement Clerk Authentication Triggers**
    * Task: Wire up the UI elements from Task 1 to trigger the respective Clerk.com authentication flows using the Clerk frontend SDK.
    ```aider
    # Example for React using @clerk/clerk-react:
    # "In LoginPage.tsx, import useClerk from '@clerk/clerk-react'.
    # For the GoogleLoginButton, implement an onClick handler that calls clerk.authenticateWithRedirect({ strategy: 'oauth_google', redirectUrl: '/auth/callback', redirectUrlComplete: '/' }).
    # For the GitHubLoginButton, implement an onClick handler that calls clerk.authenticateWithRedirect({ strategy: 'oauth_github', redirectUrl: '/auth/callback', redirectUrlComplete: '/' }).
    # For the EmailLoginButton, implement a flow that either redirects to a Clerk-hosted email link page or uses Clerk components to handle email input and magic link/OTP flow.
    # Ensure appropriate error handling is in place for SDK method calls."
    # UPDATE: src/pages/LoginPage.tsx (or equivalent components)
    # FUNCTION: handleGoogleLogin, handleGitHubLogin, handleEmailLogin
    ```

3.  **Frontend: Handle Authentication Callbacks**
    * Task: Implement a callback route and logic to handle the user's return to the application after successful or failed authentication attempts with Clerk (especially for OAuth providers).
    ```aider
    # "Create a new route, for example '/auth/callback', in React Router.
    # This component should use the Clerk SDK to handle the authentication result (e.g., clerk.handleRedirectCallback()).
    # Upon successful authentication, securely store the session (Clerk SDK typically handles this) and redirect the user to their dashboard (e.g., '/pa/dashboard').
    # If authentication fails, display an appropriate error message or redirect to the login page with an error query parameter.
    # Update global application state (Zustand store) with user authentication status and details."
    # CREATE: src/pages/AuthCallbackPage.tsx (or equivalent)
    # UPDATE: src/App.tsx (to include the new route)
    # UPDATE: src/store/authStore.ts (Zustand store)
    ```

4.  **Backend: Implement/Verify JWT Validation Middleware**
    * Task: Ensure that backend GraphQL resolvers or API endpoints that require authentication have middleware to validate the JWT sent from the frontend. This JWT is issued by Clerk.com.
    ```aider
    # For a Python backend (e.g., FastAPI/Strawberry for GraphQL):
    # "Create or verify existing authentication middleware for GraphQL resolvers.
    # This middleware should extract the JWT from the 'Authorization' header.
    # Use the Clerk Python SDK (clerk-sdk-python) to verify the JWT.
    # If valid, extract the clerk_user_id and other necessary claims, and attach them to the request context.
    # If invalid, return an appropriate GraphQL authentication error.
    # Apply this middleware to all resolvers that require an authenticated user."
    # UPDATE: app/graphql/middleware.py (or equivalent auth handling module)
    # FUNCTION: AuthenticatedUserMiddleware (or similar)
    # DETAILS: Ensure it integrates with the Clerk SDK's verify_token() or similar method.
    ```

5.  **Backend: User Retrieval Logic**
    * Task: Ensure that once a `clerk_user_id` is obtained from a validated JWT, the backend can correctly fetch the corresponding VEDAVIVI user's data from the `users` table.
    ```aider
    # "In the services or data access layer of the Python backend:
    # Implement or verify a function `get_user_by_clerk_id(clerk_id: str) -> User`.
    # This function should query the 'users' PostgreSQL table for a record where 'clerk_user_id' matches the provided clerk_id.
    # This function will be used by authenticated resolvers to fetch VEDAVIVI-specific user details."
    # UPDATE: app/services/user_service.py (or equivalent)
    # FUNCTION: get_user_by_clerk_id
    ```

6.  **End-to-End Testing**
    * Task: Perform end-to-end testing for each authentication method (Google, GitHub, Email).
    ```aider
    # This is a manual or automated (e.g., Cypress [Source: 109]) testing task.
    # "Develop Cypress end-to-end tests for the user login flow:
    # 1. Test successful login with Google: User clicks Google button, completes Google auth, is redirected to dashboard.
    # 2. Test successful login with GitHub: User clicks GitHub button, completes GitHub auth, is redirected to dashboard.
    # 3. Test successful login with Email (magic link/OTP): User enters email, receives and uses link/OTP, is redirected to dashboard.
    # 4. Test failed login attempts for each method (e.g., incorrect password if applicable with Clerk, cancelled OAuth).
    # Verify that protected pages are inaccessible before login and accessible after.
    # Verify user information is correctly displayed after login."
    # CREATE: cypress/integration/login_spec.ts (or equivalent)
    ```
