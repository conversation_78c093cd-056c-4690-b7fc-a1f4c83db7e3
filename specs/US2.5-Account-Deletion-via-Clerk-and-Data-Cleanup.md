## US2.5 Clerk User Lifecycle Webhook Handling (`user.created`, `user.deleted`)
> As VEDAVIVI, when <PERSON> sends user lifecycle webhooks (e.g., `user.created`, `user.deleted`), I want to process these events securely and reliably using a standardized asynchronous pattern, so that user data in VEDAVIVI's systems is accurately synchronized with Clerk.

### High-Level Objective

- Implement a robust, secure, and scalable backend system to process `user.created` and `user.deleted` webhook events from Clerk.com. This system will ensure:
    - For `user.created`: A corresponding minimal user record is created in VEDAVIVI's database, linked to the Clerk User ID. (Addresses parts of original US2.3)
    - For `user.deleted`: All associated user data is completely and asynchronously removed from VEDAVIVI's systems. (Original US2.5 objective)

### Mid-Level Objectives

-   **Standardized Webhook Reception:** VEDAVIVI's backend must reliably receive and authenticate all incoming webhooks from <PERSON> using signature verification.
-   **Event-Specific Payload Validation:** Implement strict payload validation using Pydantic models for `user.created` and `user.deleted` event types.
-   **Asynchronous Event Processing:** All webhook-triggered business logic (user creation, data deletion) must be performed asynchronously to ensure webhook endpoints respond quickly and reliably.
-   **Idempotent Task Execution:** Asynchronous tasks for user creation and deletion must be designed to be idempotent, preventing duplicate operations if Clerk resends an event.
-   **User Creation on `user.created`:** Upon receiving a validated `user.created` event, an asynchronous job creates a new user record in VEDAVIVI's `users` table, populating it with essential information from Clerk (e.g., `clerk_user_id`, `email`, `first_name`, `last_name`).
-   **Comprehensive Data Removal on `user.deleted`:** Upon receiving a validated `user.deleted` event, an asynchronous cleanup job is triggered to remove all data associated with the user from all relevant VEDAVIVI database tables (e.g., `users`, `assistants`, `chat_messages`, `file_metadata`, `assistant_objectives`, `tasks`, `external_credentials`, `cli_tokens`, `webhook_registrations`).
-   **Robust Error Handling & Logging:** Implement comprehensive error handling, retries for transient issues, and detailed logging for all stages of webhook processing.

### Implementation Notes

-   **Webhook Handling Pattern:**
    -   **Endpoint:** A single, dedicated FastAPI route (e.g., `/api/v1/webhooks/clerk`) will handle all Clerk webhooks.
    -   **Security:** Mandatory Svix signature verification for all incoming requests using the Clerk signing secret.
    -   **Dispatch:** The endpoint will inspect the `event.type` to route to specific validation and task enqueueing logic.
    -   **Validation:** Pydantic models (e.g., `UserCreatedData`, `UserDeletedData` in `a2a_platform.schemas.clerk_events`) for strict payload validation.
    -   **Asynchronicity:** Immediate `2xx` response to Clerk after validation and task enqueueing. Business logic executed by background workers (e.g., Celery, ARQ).
    -   **Idempotency:** Workers use Clerk's event ID (`evt_id`) to ensure tasks are processed only once (e.g., via Redis lock or check in database).
-   **Important Technical Details:**
    -   The `clerk_user_id` is the primary key for identifying users across Clerk and VEDAVIVI.
    -   Asynchronous task system (e.g., message queues like SQS/Pub/Sub and worker services as per `VEDAVIVI: MVP Backend Technical Plan v1.5` [Source 393, 433, 504]) is essential.
    -   Order of operations for data deletion must respect foreign key constraints or leverage cascading deletes.
    -   Deletion should also encompass any user-associated files in object storage, triggered by `file_metadata` cleanup. [Source 420, 476]
-   **Dependencies and Requirements:**
    -   Clerk.com webhook configuration for `user.created` and `user.deleted` events. [Source 1233, 1235 for `user.deleted`]
    -   Secure storage and access to Clerk webhook signing secret.
    -   Implemented asynchronous task processing system.
    -   Defined Pydantic schemas for `user.created` and `user.deleted` event payloads.
    -   Clearly defined database schema and relationships for all user-related data. [Source 468-489]
-   **Coding Standards to Follow:**
    -   Adhere to VEDAVIVI's established Python backend coding standards.
    -   Modular design for webhook handlers, Pydantic models, service functions, and worker tasks.
    -   Comprehensive unit and integration tests.
-   **Other Technical Guidance:**
    -   Ensure logging captures `clerk_user_id` and Clerk `event_id` for traceability.
    -   Establish monitoring for webhook success/failure rates and async queue health.

### Context

**Beginning Context:**
-   `apps/backend/src/a2a_platform/api/rest/routes/webhooks.py`: May exist with basic structure, but needs to implement the full pattern for Clerk events.
-   `apps/backend/src/a2a_platform/auth/clerk.py`: May contain JWT validation; needs robust Svix signature verification for webhooks.
-   `apps/backend/src/a2a_platform/services/user_service.py`: Contains user-related logic; will need functions for `create_user_from_clerk_event` and `delete_user_data_by_clerk_id`.
-   `apps/backend/src/a2a_platform/db/models/user.py`: Defines the `User` model, which will be created/deleted.
-   Other models storing user-related data exist (e.g., `assistants`, `chat_messages`, etc.).
-   An asynchronous task processing system might be conceptualized or partially implemented. [Source 393]

**Ending Context:**
-   `apps/backend/src/a2a_platform/api/rest/routes/webhooks.py`: Contains a robust Clerk webhook handler applying the full pattern: signature verification, event dispatching, Pydantic validation, and async task enqueueing for `user.created` and `user.deleted`.
-   `apps/backend/src/a2a_platform/auth/clerk.py`: Contains a reusable function for Clerk webhook signature verification.
-   `apps/backend/src/a2a_platform/schemas/clerk_events.py`: New file containing Pydantic models like `UserCreatedEventData` and `UserDeletedEventData`.
-   `apps/backend/src/a2a_platform/services/user_service.py`: Contains `create_user_from_clerk_event(clerk_user_data: UserCreatedEventData)` and `delete_all_user_data(clerk_user_id: str)`.
-   `apps/backend/src/a2a_platform/workers/clerk_event_handlers.py` (or similar): New file containing worker functions like `process_user_created_task` and `process_user_deleted_task`, which call the respective service functions and handle idempotency.
-   `apps/backend/tests/integration/test_clerk_webhooks.py`: New or updated integration tests covering `user.created` and `user.deleted` webhook flows.
-   `apps/backend/tests/unit/services/test_user_service_lifecycle.py`: New unit tests for user creation and deletion logic within `user_service.py`.
-   `apps/backend/tests/unit/schemas/test_clerk_event_schemas.py`: New unit tests for Pydantic models.

### Low-Level Tasks

> Ordered from start to finish, implementing the common pattern first, then event-specific logic.

1.  **Establish Clerk Webhook Signature Verification**
    ```aider
    Prompt: "In `apps/backend/src/a2a_platform/auth/clerk.py`, create or ensure a robust function `verify_clerk_webhook(request: Request, secret: str) -> dict` that uses the `svix` library to verify 'Svix-Id', 'Svix-Timestamp', and 'Svix-Signature' headers. It should return the verified payload as a dict or raise an HTTPException on failure. Add necessary configurations for the webhook secret in `settings.py`."
    File to UPDATE: `apps/backend/src/a2a_platform/auth/clerk.py`
    File to UPDATE: `apps/backend/src/a2a_platform/config/settings.py`
    Function to CREATE/UPDATE: `verify_clerk_webhook` in `clerk.py`
    Details: "Log verification success and failures. Ensure the secret is handled securely."
    ```
2.  **Implement Generic Clerk Webhook Endpoint Shell**
    ```aider
    Prompt: "In `apps/backend/src/a2a_platform/api/rest/routes/webhooks.py`, create a FastAPI POST endpoint `/api/v1/webhooks/clerk`. Use a dependency to inject the verified payload from `verify_clerk_webhook`. Initially, log the event type and payload, then return a 200 OK. Set up basic logging."
    File to CREATE/UPDATE: `apps/backend/src/a2a_platform/api/rest/routes/webhooks.py`
    Function to CREATE: `handle_clerk_webhook(request: Request, verified_payload: dict = Depends(verify_clerk_webhook))`
    ```
3.  **Define Pydantic Schemas for Clerk Events**
    ```aider
    Prompt: "Create `apps/backend/src/a2a_platform/schemas/clerk_events.py`. Define Pydantic models: `ClerkEventBase(type: str, object: str)`, `UserEmailInfo(email_address: EmailStr, verified: bool, verification: Optional[Dict])`, `UserCreatedData(id: str, email_addresses: List[UserEmailInfo], first_name: Optional[str] = None, last_name: Optional[str] = None, public_metadata: Dict = {}, created_at: int, updated_at: int)`, and `UserDeletedData(id: str, deleted: bool)`. Ensure these models reflect the actual structure of Clerk's webhook payloads for `user.created` and `user.deleted`."
    File to CREATE: `apps/backend/src/a2a_platform/schemas/clerk_events.py`
    ```
4.  **Implement Event Dispatching and Asynchronous Task Enqueueing in Webhook Endpoint**
    ```aider
    Prompt: "Update `handle_clerk_webhook` in `webhooks.py`. After getting the `verified_payload`:
    1. Parse `verified_payload['type']`.
    2. If `type == 'user.created'`, validate `verified_payload['data']` against `UserCreatedData`. If valid, enqueue an async task named 'process_user_created_task' with the validated data and Clerk event ID (from Svix headers, passed through `verified_payload` or `request.state`). Log success/failure of validation & enqueueing.
    3. If `type == 'user.deleted'`, validate `verified_payload['data']` against `UserDeletedData`. If valid, enqueue an async task named 'process_user_deleted_task' with the validated data and Clerk event ID. Log success/failure.
    4. For other event types, log them as unhandled for now.
    5. Return `200 OK` immediately after successful validation and enqueueing (or `400 Bad Request` on validation failure)."
    File to UPDATE: `apps/backend/src/a2a_platform/api/rest/routes/webhooks.py`
    Function to UPDATE: `handle_clerk_webhook`
    Details: "Use a placeholder for the actual async task enqueueing mechanism if not yet implemented (e.g., just log 'Task enqueued: process_user_created_task with data ...'). Assume Clerk event ID is available (e.g., via `request.state.clerk_event_id`)."
    ```
5.  **Backend: Implement `user.created` Service Logic**
    ```aider
    Prompt: "In `apps/backend/src/a2a_platform/services/user_service.py`, create an async function `create_user_from_clerk_event(db_session: AsyncSession, clerk_user_data: UserCreatedData) -> User`. This function should:
    1. Check if a user with `clerk_user_data.id` already exists. If so, log and return the existing user (idempotency at service level if worker check fails).
    2. Extract primary email (verified first, then any).
    3. Create a new VEDAVIVI `User` record using `clerk_user_id`, email, first name, last name.
    4. Save and return the new user. Log creation."
    File to UPDATE: `apps/backend/src/a2a_platform/services/user_service.py`
    Function to CREATE: `create_user_from_clerk_event`
    ```
6.  **Backend: Implement Asynchronous Worker for `user.created` Event**
    ```aider
    Prompt: "Create `apps/backend/src/a2a_platform/workers/clerk_event_handlers.py`. Define an async worker task `process_user_created_task(ctx, clerk_event_id: str, event_data_dict: dict)`. This worker should:
    1. Implement idempotency check using `clerk_event_id` (e.g., using Redis or a db table - log if already processed).
    2. Deserialize `event_data_dict` into `UserCreatedData`.
    3. Obtain a database session.
    4. Call `user_service.create_user_from_clerk_event` with the session and deserialized data.
    5. Implement error handling, logging, and appropriate retries for transient issues.
    6. Mark `clerk_event_id` as processed on success."
    File to CREATE: `apps/backend/src/a2a_platform/workers/clerk_event_handlers.py`
    Function to CREATE: `process_user_created_task`
    ```
7.  **Backend: Implement `user.deleted` Service Logic (Data Deletion)**
    ```aider
    Prompt: "In `apps/backend/src/a2a_platform/services/user_service.py`, create an async function `delete_all_user_data(db_session: AsyncSession, clerk_user_id: str)`. This function should:
    1. Delete all records associated with the `clerk_user_id` from relevant tables (`users`, `assistants`, `chat_messages`, `file_metadata`, etc.).
    2. Ensure correct deletion order or use cascading deletes.
    3. Log actions for each table and overall success/failure.
    4. If user not found, log and return gracefully."
    File to UPDATE: `apps/backend/src/a2a_platform/services/user_service.py`
    Function to CREATE: `delete_all_user_data`
    ```
8.  **Backend: Implement Asynchronous Worker for `user.deleted` Event**
    ```aider
    Prompt: "In `apps/backend/src/a2a_platform/workers/clerk_event_handlers.py`, define an async worker task `process_user_deleted_task(ctx, clerk_event_id: str, event_data_dict: dict)`. This worker should:
    1. Implement idempotency check using `clerk_event_id`.
    2. Deserialize `event_data_dict` into `UserDeletedData`.
    3. Extract `clerk_user_id` from `UserDeletedData.id`.
    4. Obtain a database session.
    5. Call `user_service.delete_all_user_data` with the session and `clerk_user_id`.
    6. Implement error handling, logging, and retries.
    7. Mark `clerk_event_id` as processed on success."
    File to UPDATE: `apps/backend/src/a2a_platform/workers/clerk_event_handlers.py`
    Function to CREATE: `process_user_deleted_task`
    ```
9.  **Testing: Unit Tests for Pydantic Schemas, Services, and Workers**
    ```aider
    Prompt: "Create unit tests for:
    1. `clerk_events.py`: Test Pydantic model validation (valid and invalid data) for `UserCreatedData` and `UserDeletedData`. (`apps/backend/tests/unit/schemas/test_clerk_event_schemas.py`)
    2. `user_service.py`: Test `create_user_from_clerk_event` (user creation, idempotency if user exists) and `delete_all_user_data` (data removal from mocked tables). (`apps/backend/tests/unit/services/test_user_service_lifecycle.py`)
    3. `clerk_event_handlers.py`: Test worker logic: idempotency checks, deserialization, service call, error handling (mock dependencies like DB session, services, idempotency store). (`apps/backend/tests/unit/workers/test_clerk_event_handlers.py`)"
    Files to CREATE: (as listed in prompt)
    ```
10. **Testing: Integration Tests for Webhook Flows**
    ```aider
    Prompt: "In `apps/backend/tests/integration/test_clerk_webhooks.py`, write integration tests for the `/api/v1/webhooks/clerk` endpoint:
    1. Test with valid signed `user.created` event: Verify 200 OK, and an async task for user creation is enqueued (mock the task queue publish call or check DB if testing end-to-end with a test worker).
    2. Test with valid signed `user.deleted` event: Verify 200 OK, and an async task for user deletion is enqueued.
    3. Test with invalid signature: Verify 401/403 error.
    4. Test with invalid payload for known event types: Verify 400 error.
    5. Test with unknown event type: Verify 200 OK (as per current spec: log and acknowledge)."
    File to CREATE/UPDATE: `apps/backend/tests/integration/test_clerk_webhooks.py`
    ```
11. **Documentation (Internal)**
    ```aider
    Prompt: "Update or create internal documentation (e.g., in `apps/backend/docs/clerk_integration.md` or a new `clerk_webhooks.md`) to describe the implemented Clerk webhook handling pattern for `user.created` and `user.deleted` events, including signature verification, Pydantic models, async task processing, idempotency, and relevant file locations."
    File to CREATE/UPDATE: `apps/backend/docs/clerk_integration.md` (or new file)
    ```
