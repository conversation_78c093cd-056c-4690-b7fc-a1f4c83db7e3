# 🧪 QA Testing Specification: US7.2

**Goal:** 🎯 Validate PA message generation, persistence, and real-time display in chat interface with comprehensive functional, performance, and security testing

## 🎯 Objectives
**Functional:** ⚙️ PA message creation via ChatService, GraphQL mutation operations, UI message display with proper attribution | **NFR:** 📏 Message persistence performance <100ms, subscription delivery <200ms, UI accessibility compliance | **Edge:** ⚠️ Database errors, network failures, malformed PA responses, conversation access violations | **Security:** 🔒 PA authentication validation, conversation ownership checks, message content sanitization | **DB:** 🗄️ chat_messages table integrity, sender_role validation, JSONB content structure | **Integration:** 🔗 GraphQL subscription system, AI/LLM service mocking, frontend-backend message flow

## 📋 Strategy
**Scope:** 🎯 In: PA message CRUD operations, GraphQL subscriptions, UI rendering, error handling / Out: AI/LLM service implementation details, advanced message formatting | **Env:** 🐳 Docker(default)/.env.test | CI(--ci flag) | **Backend:** 🖥️ apps/backend + ./scripts/run-backend-tests.sh + pytest + postgresql+asyncpg + DATABASE_URL(required) | **Frontend:** 🌐 bun + ./scripts/run-frontend-tests.sh + port:5173 + --e2e(Cypress) | **DB:** 🗄️ ./scripts/db-migrate.sh + upgrade/downgrade | **Tools:** 🛠️ pytest/bun/Docker/Alembic/Cypress | **Deps:** 🔗 Test database with chat_messages schema, Redis for subscriptions, mock AI service | **Data:** 📊 Test conversations, mock PA responses, user authentication tokens | **Risks:** ⚠️ GraphQL subscription timing, database transaction rollbacks, authentication context

## 🎬 Context
**Before:** Features:⚙️ User message sending, basic chat UI, GraphQL schema definitions | Env:🔧 Test database setup, Redis configuration, mock authentication | Data:📊 Test users, conversations, existing chat messages
**After:** Deliverables:📦 PA message test suite, performance benchmarks, security validation reports | Reports:📄 Test coverage analysis, performance metrics documentation | Defects:🐛 Issue tracking for subscription timing, error handling gaps

## 📝 Tasks
1. **Setup PA Message Test Environment** - Configure test infrastructure for PA message testing
```test_execution
**Do:** 🎯 Setup test database with chat_messages schema, configure Redis for subscription testing | **Type:** 🧪 Infrastructure | **Cmd:** 💻 ./scripts/run-backend-tests.sh --setup, docker-compose -f docker-compose.test.yml up | **Assert:** ✅ Database schema migrated, Redis connection established | **Data:** 📊 Clean test environment | **Result:** 🎯 Ready test infrastructure
```

2. **Unit Test ChatService PA Message Creation** - Test PA message service layer functionality
```test_execution
**Do:** 🎯 Test ChatService.send_pa_message() method with various content types and error scenarios | **Type:** 🧪 Unit | **Cmd:** 💻 pytest apps/backend/tests/unit/test_chat_service_pa.py -v | **Assert:** ✅ PA messages created with sender_role='agent', proper timestamps, JSONB content validation | **Data:** 📊 Mock conversation IDs, PA response content | **Result:** 🎯 Validated PA message creation logic
```

3. **Integration Test GraphQL PA Message Mutations** - Test GraphQL layer PA message operations
```test_execution
**Do:** 🎯 Test sendMessageFromPA GraphQL mutation with authentication, error handling, and response validation | **Type:** 🧪 Integration | **Cmd:** 💻 pytest apps/backend/tests/integration/test_pa_message_graphql.py -v --setup-clean | **Assert:** ✅ Mutation creates database records, returns proper payloads, handles authentication failures | **Data:** 📊 GraphQL mutation requests, PA authentication tokens | **Result:** 🎯 Validated GraphQL PA message API
```

4. **End-to-End PA Message Flow Testing** - Test complete PA message workflow
```test_execution
**Do:** 🎯 Test PA message creation through UI display including subscription notifications | **Type:** 🧪 E2E | **Cmd:** 💻 ./scripts/run-frontend-tests.sh --e2e | **Assert:** ✅ PA messages appear in chat UI, real-time updates work, proper attribution displayed | **Data:** 📊 Test user sessions, conversation contexts | **Result:** 🎯 Validated complete PA message user experience
```

## 📋 Test Cases

### 🧪 Functional: PA Message Creation and Persistence
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.2-TC-01 | PA message creation via ChatService | call send_pa_message(conv_id, content) → verify database record | ChatMessage with sender_role='agent' persisted | API/DB |
| US7.2-TC-02 | PA message GraphQL mutation | sendMessageFromPA mutation → validate response payload | SendMessagePayload with success=true, message data | API/Integration |
| US7.2-TC-03 | PA message content JSONB structure | create message with structured content → verify JSONB format | Content stored as {"parts": [{"type": "text", "content": "..."}]} | DB |
| US7.2-TC-04 | PA message timestamp accuracy | create PA message → check timestamp | Timestamp within 1 second of creation time | DB |
| US7.2-TC-05 | PA message conversation linking | create PA message in conversation → verify FK relationship | Message linked to correct conversation_id | DB |

### 🧪 UI: Frontend PA Message Display
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.2-TC-06 | PA message UI rendering | PA message in conversation → load chat UI | Message displays with PA attribution and styling | UI |
| US7.2-TC-07 | PA message chronological ordering | multiple PA messages → display in chat | Messages appear in timestamp order | UI |
| US7.2-TC-08 | PA message real-time updates | PA sends message → GraphQL subscription → UI update | Message appears without page refresh | UI/Integration |
| US7.2-TC-09 | PA message accessibility | PA message displayed → screen reader test | Message content readable by assistive technology | UI/A11y |
| US7.2-TC-10 | PA message mobile responsiveness | PA message on mobile viewport → verify display | Message layout adapts to mobile screen size | UI |

### ❌ Negative & Error
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.2-ERR-01 | Invalid conversation ID | send_pa_message with non-existent conv_id → handle error | Error thrown with descriptive message | Negative |
| US7.2-ERR-02 | Database connection failure | PA message creation during DB outage → error handling | Graceful error handling, no data corruption | Negative |
| US7.2-ERR-03 | Malformed PA content | send PA message with invalid JSON content → validate | Content validation error with specific feedback | Negative |
| US7.2-ERR-04 | Unauthorized PA message creation | sendMessageFromPA without proper auth → reject | Authentication error returned | Security |
| US7.2-ERR-05 | GraphQL subscription connection failure | PA message sent during subscription outage → fallback | Message persisted, fallback notification mechanism | Negative |

### 📋 Contract & External
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.2-CT-01 | AI/LLM service response format | Mock AI service response → PA message creation | PA message created with expected content structure | Contract |
| US7.2-CT-02 | GraphQL subscription schema compliance | PA message subscription event → validate schema | Event matches GraphQL subscription schema | Contract |
| US7.2-CT-03 | Database schema compatibility | PA message insertion → verify schema compliance | Message fits chat_messages table constraints | Contract |

### ⚙️ Environment & Config
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.2-ENV-01 | Test database PA message handling | PA message operations in test env → verify isolation | Test messages don't affect other test data | Environment |
| US7.2-ENV-02 | Redis subscription configuration | PA message → Redis pub/sub → subscription delivery | Subscription events properly configured and delivered | Environment |
| US7.2-ENV-03 | Docker environment PA message flow | Complete PA message flow in Docker → validate | All components work correctly in containerized environment | Environment |

### 🚀 Performance & Load
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.2-PERF-01 | PA message persistence latency | Create 100 PA messages → measure DB write time | Average persistence time <100ms | Performance |
| US7.2-PERF-02 | GraphQL subscription delivery latency | PA message → subscription notification → measure delay | Subscription delivery <200ms | Performance |
| US7.2-PERF-03 | UI rendering performance | PA message display → measure render time | UI update renders <50ms after data received | Performance |
| US7.2-PERF-04 | Concurrent PA message handling | 50 simultaneous PA messages → system response | System handles concurrent messages without degradation | Performance |

### 🔒 Security
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| US7.2-SEC-01 | PA authentication validation | PA message creation without valid auth → block | Authentication failure prevents message creation | Security |
| US7.2-SEC-02 | Conversation ownership verification | PA message to unauthorized conversation → reject | Access control prevents unauthorized message sending | Security |
| US7.2-SEC-03 | PA message content sanitization | PA message with potentially malicious content → sanitize | Content sanitized for safe display | Security |
| US7.2-SEC-04 | Message attribution integrity | PA message creation → verify sender_role immutable | sender_role cannot be modified after creation | Security |

## 📊 Coverage
**Functional:** ⚙️ PA message CRUD operations (100%), GraphQL mutations and subscriptions (100%), UI display and attribution (100%) | **NFR:** 📏 Performance benchmarks for persistence/delivery/rendering, accessibility compliance for PA messages, security validation for authentication and authorization | **Edge:** ⚠️ Database errors, network failures, malformed content, authentication failures, subscription outages

## 🔧 Setup
**Backend:** 🖥️ apps/backend + ./scripts/run-backend-tests.sh + pytest + postgresql+asyncpg + DATABASE_URL(required) + REDIS_URL + PA_AUTH_TOKEN | **Frontend:** 🌐 bun + ./scripts/run-frontend-tests.sh + port:5173 + dev mode + GraphQL endpoint configuration | **DB:** 🗄️ ./scripts/db-migrate.sh + Docker Compose env vars + upgrade/downgrade + test data seeding

## 📦 Data
**Env Vars:** ⚙️ DATABASE_URL + REDIS_URL + POSTGRES_* + CLERK_JWT_PUBLIC_KEY + PA_AUTH_TOKEN + AI_SERVICE_URL + .env/.env.test | **Config:** 🔧 .env.test + Docker + --ci flags + GraphQL endpoint URLs + WebSocket connection settings

## 🎯 Success Criteria
**Functional:** ⚙️ All PA message operations work correctly, UI displays messages properly, real-time updates function | **Performance:** ⚡ All latency requirements met (<100ms persistence, <200ms subscription, <50ms UI render) | **Security:** 🔒 Authentication and authorization controls validated, no security vulnerabilities identified | **Coverage:** 📊 >90% code coverage for PA message functionality, all acceptance criteria validated
