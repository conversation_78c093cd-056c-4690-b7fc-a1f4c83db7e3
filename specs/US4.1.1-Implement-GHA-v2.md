# Summary of Changes to US4.1.1-Impement-GHA.md:

## High-Level Objective:

Modified to include the web frontend in the automated build, test, and deployment process.

## Mid-Level Objective (CI/CD):

- Restructure deployment workflows to use composite actions for common sequences of steps (e.g., GCP authentication, Docker build/push, Cloud Run deployment).
- Individual service deployment workflows (`deploy-api.yml`, `deploy-workers.yml`, `deploy-messaging.yml`, `deploy-web.yml`) are now self-contained, directly implementing all steps for staging and production deployments.
- Each service-specific workflow will still be triggered by changes to its respective paths.
- Database migrations (Alembic) will be handled within the `deploy-api.yml` workflow, triggered conditionally for both staging and production.
- Use `act` (see `LOCAL_ACTIONS_TESTING.md`) to test the workflows locally. Each of the four deployment workflows (`deploy-api.yml`, `deploy-workers.yml`, `deploy-messaging.yml`, `deploy-web.yml`) will be tested using the `scripts/run-act.sh` script to ensure composite actions and the overall workflow logic are functioning correctly.
- Remove the suffixes `_STAGING` and `_PRODUCTION` from workflow secret names

## Implementation Notes:

No major changes, but the principle of using the exact same Docker images for production that were deployed to staging will now apply to each service's respective workflow and image.
- A new directory `.github/actions/` will be created to house composite actions.

## Context:

### Beginning Context:

- Added web directory and Dockerfile (to be created).

### Ending Context:

- Updated workflow file list to reflect the new structure:
    - `.github/workflows/deploy-api.yml` (self-contained)
    - `.github/workflows/deploy-workers.yml` (self-contained)
    - `.github/workflows/deploy-messaging.yml` (self-contained)
    - `.github/workflows/deploy-web.yml` (self-contained)
    - `.github/actions/gcp-auth-setup/action.yml` (composite action)
    - `.github/actions/docker-build-push/action.yml` (composite action)
    - `.github/actions/gcloud-run-deploy/action.yml` (composite action)
- Removed old consolidated workflow file: `.github/workflows/deploy.yml`.
- Added Dockerfile (created and functional).
- Updated Google Cloud Run service list to include web-staging and web-production.

## Low-Level Tasks (Section B - CI/CD Deployment Workflows Setup):

- B.1. General Deployment Configuration:
    - Emphasized that manual approval for production will be configured within the reusable deployment workflow and triggered by the calling service workflows.
    - Cloud Run services list updated to include web.
    - Create a new directory `.github/actions/` for storing composite action definitions.

- B.2. Create Web Frontend Dockerfile (Dockerfile):
    - (No changes to this task itself, but its usage will be via the reusable workflow)
    - Added a new task to create a Dockerfile for the web frontend. The Dockerfile will set up a Node.js environment, install Bun, install dependencies, and run the Vite development server using `bun run dev -- --host 0.0.0.0`.
    - The production stage of this Dockerfile will be updated to remove Nginx. An alternative HTTP server (e.g., Bun's native server, or a package like 'serve') will be used to serve static assets from the build output and handle SPA client-side routing fallbacks.
    - Noted that Dockerfile COPY commands need to be relative to the build context.

- B.3. Create Composite Action: GCP Auth & Setup (`.github/actions/gcp-auth-setup/action.yml`):
    - Define a composite action to handle GCP authentication using Workload Identity Federation and gcloud SDK setup.
    - Inputs: `workload_identity_provider`, `service_account`.
    - Steps: `google-github-actions/auth`, `google-github-actions/setup-gcloud`, `gcloud auth configure-docker`.

- B.4. Create Composite Action: Docker Build & Push (`.github/actions/docker-build-push/action.yml`):
    - Define a composite action to build a Docker image and push it to Google Artifact Registry.
    - Inputs: `image_name`, `dockerfile_path`, `build_context`, `artifact_registry_host`, `gcp_project_id`, `repository_name`, `github_sha`.
    - Outputs: `image_tag`.
    - Steps: `docker build`, `docker push`.

- B.5. Create Composite Action: Cloud Run Deploy (`.github/actions/gcloud-run-deploy/action.yml`):
    - Define a composite action to deploy an image to Google Cloud Run.
    - Inputs: `service_name_suffix` (e.g., "-staging", "-production"), `base_service_name` (e.g., "api"), `image_tag`, `gcp_project_id`, `cloud_run_region`, `vpc_connector`, `env_vars_json` (a JSON string of all environment variables to be set), `allow_unauthenticated` (boolean), `port`.
    - Steps: `gcloud run deploy` command construction and execution. This action is responsible for taking the `env_vars_json` input (which should be a flat JSON object string like `{"KEY1": "VALUE1", "KEY2": "VALUE2"}`) and converting it into the comma-separated `KEY1=VALUE1,KEY2=VALUE2` format required by the `gcloud run deploy --set-env-vars` flag.

- B.6. Standardized Deployment Job Structure (within each `deploy-*.yml`):
    - Each service deployment workflow (`deploy-api.yml`, `deploy-workers.yml`, `deploy-messaging.yml`, `deploy-web.yml`) follows a standardized structure.
    - It contains two main jobs: one for deploying to `staging` and one for deploying to `production`.
    - The `production` deployment job typically depends on the successful completion of the `staging` job and may include a manual approval step.
    - Each job performs the following sequence of operations:
        - Authenticates to Google Cloud using the `gcp-auth-setup` composite action.
        - Builds and pushes the Docker image to Google Artifact Registry using the `docker-build-push` composite action. This step includes environment-specific image tagging.
        - **Environment Variable Preparation**: A dedicated `run` step constructs a comprehensive JSON string of all environment variables required by the service at runtime. This script uses `jq` to merge:
            - Basic context variables (e.g., `{"ENVIRONMENT": "staging"}`).
            - Service-specific variables.
            - Secrets fetched from the GitHub secrets context (e.g., `DATABASE_URL`, `REDIS_URL`, `CLERK_API_KEY`, `VITE_GRAPHQL_API_URL`, etc.).
            The resulting flat JSON object string is made available as an output for the deployment step.
        - Deploys the service to Google Cloud Run using the `gcloud-run-deploy` composite action, passing the prepared `env_vars_json` and other service/environment-specific parameters.
        - For the `deploy-api.yml` workflow, the deployment jobs also include steps to run Alembic database migrations conditionally.

- B.7. Backend API Deployment Workflow (`.github/workflows/deploy-api.yml`):
    - This workflow is self-contained and directly implements the deployment logic for the API service to staging and production environments.
    - It will include a `permissions` block with `id-token: write` and `contents: read` at the top level.
    - It will be triggered by the successful completion of the "CI Pipeline (Static Analysis & Tests)" workflow on the `main` branch using the `workflow_run` event. It will also allow manual triggering via `workflow_dispatch`.
    - The first job in this workflow (`deploy_api_staging`) has an `if` condition: `(github.event_name == 'workflow_run' && github.event.workflow_run.conclusion == 'success') || github.event_name == 'workflow_dispatch'`.
    - The production deployment job (`deploy_api_production`) will have an `if` condition like `needs.deploy_api_staging.result == 'success'` and include a manual approval step.
    - It defines jobs for staging and production, each including steps for GCP authentication, Docker build/push, environment variable preparation (including `DATABASE_URL`, `ALEMBIC_DB_URL`, `DB_INSTANCE_NAME` and other API-specific variables derived from secrets), Alembic database migrations, and Cloud Run deployment using composite actions.
    - Secrets are accessed directly within the deployment jobs.

- B.8. Backend Workers Deployment Workflow (`.github/workflows/deploy-workers.yml`):
    - This workflow is self-contained and directly implements the deployment logic for the Workers service.
    - It will include a `permissions` block with `id-token: write` and `contents: read` at the top level.
    - Trigger conditions and job structure (`deploy_workers_staging`, `deploy_workers_production`) are similar to `deploy-api.yml`.
    - It defines jobs for staging and production, each including steps for GCP authentication, Docker build/push, environment variable preparation (including worker-specific variables derived from secrets), and Cloud Run deployment using composite actions.
    - Secrets are accessed directly within the deployment jobs.

- B.9. Backend Messaging Deployment Workflow (`.github/workflows/deploy-messaging.yml`):
    - **REMOVED**: The messaging service has been decommissioned to simplify infrastructure.
    - Messaging functionality is now handled by the main API service and RQ workers.
    - This workflow is no longer needed and has been removed from the codebase.

- B.10. Web Frontend Deployment Workflow (`.github/workflows/deploy-web.yml`):
    - This workflow is self-contained and directly implements the deployment logic for the Web service.
    - It will include a `permissions` block with `id-token: write` and `contents: read` at the top level.
    - Trigger conditions and job structure (`deploy_web_staging`, `deploy_web_production`) are similar to `deploy-api.yml`.
    - It defines jobs for staging and production, each including steps for GCP authentication, Docker build/push (using `.` as build context), environment variable preparation (including `NODE_ENV`, `VITE_GRAPHQL_API_URL`, `CDN_URL`, `STORAGE_BUCKET` derived from environment-specific secrets), and Cloud Run deployment using composite actions. The `port` will be set to `80`.
    - Secrets, including environment-specific ones like `VITE_GRAPHQL_API_URL_STAGING` and `VITE_GRAPHQL_API_URL_PRODUCTION` (which are mapped to a common `VITE_GRAPHQL_API_URL` in the workflow via GitHub environments), are accessed directly.

## C. Unit Tests & Verification (Conceptual):

- Updated to reflect that merging a PR to main will trigger all four new deployment workflows.

These changes restructure the CI/CD part of the specification to use individual deployment workflows per service, including the new web frontend, while keeping the CI checks workflow (ci-checks.yml) as a common prerequisite.

##### B.6. Reusable Workflow (`reusable-deploy-service.yml`)
This section is removed as the reusable workflow has been eliminated. Each service deployment workflow (`deploy-*.yml`) is now self-contained. The common structure is described in section B.6 above (Standardized Deployment Job Structure). Any previous content under this heading referring to `reusable-deploy-service.yml` is now obsolete.

##### B.10. Environment Variable and Secret Management Strategy

The strategy for managing environment variables and secrets across the deployment workflows is as follows:

1.  **Service-Specific Deployment Workflows (`deploy-*.yml`)**: These workflows are responsible for initiating and executing the deployment for a specific service to both staging and production environments.
    *   Each workflow contains two primary jobs: `deploy_<service>_staging` and `deploy_<service>_production`.
    *   **Secrets Access**: Secrets are accessed directly within each job (staging/production) using the `secrets` context. For variables that differ between environments (e.g., `VITE_GRAPHQL_API_URL`), GitHub's environment-specific secrets are utilized, allowing the workflow to reference a single secret name (e.g., `secrets.VITE_GRAPHQL_API_URL`) whose value is determined by the target GitHub environment.
    *   **Build Arguments**: If Docker build-time arguments are needed, they are passed directly to the `docker-build-push` composite action within the relevant job using the `build-args-json` input (e.g., `build-args-json: '{"ARG_NAME1": "value1"}'`).
    *   **Runtime Environment Variable Preparation**: Within each deployment job, a dedicated `run` step named "Prepare Environment Variables JSON" uses `jq` to:
        *   Initialize a JSON object, often including a base variable like `{"ENVIRONMENT": "staging"}` or `{"NODE_ENV": "production"}`.
        *   Dynamically merge all necessary runtime environment variables for the service. These are typically derived from the `secrets` context (e.g., `DATABASE_URL: ${{ secrets.DB_APP_PROXY_URL }}`, `REDIS_URL: ${{ secrets.REDIS_URL }}`, `CLERK_JWT_PUBLIC_KEY: ${{ secrets.CLERK_JWT_PUBLIC_KEY }}`).
        *   Service-specific variables are also included here (e.g., `ALEMBIC_DB_URL` for the API service, or `VITE_GRAPHQL_API_URL` for the web service, using the environment-scoped secret).
        *   The output of this step is a single JSON string (e.g., `env_vars_json`) containing all consolidated environment variables for the service in that specific environment.
    *   This `env_vars_json` string is then passed to the `gcloud-run-deploy` composite action.

2.  **Composite Action (`gcloud-run-deploy/action.yml`)**:
    *   Receives the `env_vars_json` string as an input.
    *   Its internal script uses `jq` to transform this JSON string into the `KEY1=VALUE1,KEY2=VALUE2,...` format required by the `gcloud run deploy --set-env-vars` flag.
    *   This ensures that secrets are securely passed through the workflow and correctly formatted for the `gcloud` command.

This approach makes each service deployment workflow self-contained and explicit about its dependencies on secrets and environment variables. It centralizes the construction of the environment variable set within each job, ensuring clarity and consistency for deploying to different environments.