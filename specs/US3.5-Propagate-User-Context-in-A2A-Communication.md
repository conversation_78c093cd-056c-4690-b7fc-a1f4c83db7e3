# US3.5 Propagate User Context in A2A Communication
> As the VEDAVIVI system, I want to ensure that internal Agent-to-Agent communication propagates necessary user context (like user_id) and agent identity, so that Specialized Agents can operate within the correct user context and with appropriate permissions, while designing the context schema in a way that considers future interoperability.

## High-Level Objective

- Ensure that when a Personal Agent (PA) communicates with a Specialized Agent (SA) within the VEDAVIVI system, critical user context (specifically `user_id`) and the initiating agent's identity are securely and reliably passed along. This enables SAs to perform actions within the correct user's data scope and apply appropriate permissions based on the user's session with the PA.
- The schema for this propagated context should be designed with consideration for potential future alignment with broader, external A2A communication standards.

## Mid-Level Objectives

-   When a PA initiates either synchronous or asynchronous communication with an SA, the `user_id` of the end-user must be included in the A2A payload.
-   Specialized Agents must be able to reliably extract the `user_id` from the incoming A2A request/message payload.
-   Actions performed by SAs on behalf of a user must be implicitly authorized by the user's existing session with the PA that initiated the A2A communication.
-   Define and utilize a clear schema (`a2a-schema.json`) for structuring the propagated context within A2A payloads, ensuring this definition is mindful of potential future external interoperability needs.

## Implementation Notes

-   **Important technical details**:
    * The `user_id` (likely the `clerk_user_id` or the internal VEDAVIVI `user.id` linked to it) is the primary piece of user context to be propagated.
    * Agent identity (e.g., `agent_id` of the PA) should also be part of the propagated context, allowing SAs to know which agent initiated the request.
    * The propagation mechanism must support both synchronous (e.g., internal HTTP calls) and asynchronous (e.g., message queue) A2A communication patterns as outlined in ADR-002 Backend Overview and the VEDAVIVI: MVP Backend Technical Plan v1.5 (Source 433, 435, 436).
    * The `a2a-schema.json` will be the source of truth for the structure of the context block within the A2A payload. This context block should be a dedicated part of the message/request.
    * Authorization for SA actions is *implicit*. The PA is responsible for ensuring the user is authorized for the high-level objective. The SA operates under the assumption that the PA has validated this. The `user_id` is used for data scoping and potentially for fetching user-specific configurations or permissions relevant to the SA's task, but not for re-authenticating the user at the SA level.
-   **Dependencies and requirements**:
    * Existence and definition of `a2a-schema.json` which will define the structure for propagating context. This schema needs to accommodate `user_id` and `initiating_agent_id`.
    * Established synchronous and asynchronous A2A communication channels (as per ADR-002).
    * A mechanism for PAs to access the end-user's `user_id` from their session/context.
    * SAs need to be designed to expect and parse this context from incoming A2A messages.
-   **Coding standards to follow**:
    * Adhere to Python backend coding standards.
    * Ensure the context propagation is implemented consistently across all internal A2A interactions.
    * Payloads should be validated against the `a2a-schema.json` where feasible.
-   **Other technical guidance**:
    * **Forward-Looking Schema Design**: While the immediate scope of US3.5 is internal A2A communication for the MVP, the design of the `user_context` portion of `a2a-schema.json` should be forward-looking. Consider common patterns and potential future alignment with broader A2A specifications (e.g., the structure of metadata or parameters in protocols like Google's A2A proposal) to facilitate easier integration or mapping if VEDAVIVI later adopts such standards for external agent communication. This is about thoughtful internal design to prevent future roadblocks, not about implementing external specs during the MVP. The goal is to make our internal context block easily embeddable or translatable into more comprehensive external protocols later.
    * Security: Ensure that only trusted internal agents can inject or modify this context. The propagation should happen within the trusted boundaries of the VEDAVIVI system.
    * Logging: Add logs at the PA side when context is added and at the SA side when context is extracted and used.
    * This feature is foundational for enabling SAs to perform user-specific tasks correctly and securely.

## Context

**Beginning context**
-   `apps/backend/src/a2a_platform/schemas/a2a-schema.json` (May not exist or may not have a defined structure for user context propagation, or the existing structure may need revision for forward-compatibility considerations).
-   Basic A2A communication mechanisms (sync/async) might be in place but lack standardized user context propagation that is also designed with future interoperability in mind (Source 433, 435, 436).
-   SAs might not be expecting or equipped to handle `user_id` from PAs in a way that considers future external schema alignment.
-   `ADR-002-Backend-Overview.md` (Source 435) mentions propagating `user_id` and `agent_id`; this user story refines the "how" for the schema with an eye on the future.

**Ending context**
-   `apps/backend/src/a2a_platform/schemas/a2a-schema.json` updated or created with a clear definition for how `user_id` and `agent_id` are structured within an internal A2A payload, with considerations for future mappability to external standards.
-   All PA-initiated internal A2A communication (sync and async) includes the `user_id` and `initiating_agent_id` in the payload according to this schema.
-   SAs are capable of extracting `user_id` from the A2A payload.
-   Documentation updated to reflect how user context is propagated internally and the design considerations for future interoperability.
-   Relevant tests for context propagation are implemented.

## Low-Level Tasks
> Ordered from start to finish

1.  **Define/Update `a2a-schema.json` for User Context (with Future Interoperability in Mind)**
    ```aider
    Prompt: "Update or create the `a2a-schema.json` file. Define a clear structure within the A2A payload for propagating user context for internal communication. This structure should include at least `user_id` (string, required) and `initiating_agent_id` (string, required). Consider adding a timestamp for context generation if useful. When defining the structure and field names for this internal `user_context` block, consider how it might be embedded or mapped into broader A2A protocols (like Google's A2A) in the future. Aim for clarity and use field names that are either standard or easily mappable, without adding complexity that is not needed for current internal MVP requirements. The goal is to facilitate future integration, not to implement external specs now."
    File to CREATE or UPDATE: `apps/backend/src/a2a_platform/schemas/a2a-schema.json`
    Details: "The schema should define an object, perhaps named `vedavivi_internal_context` or similar, to clearly namespace it if it's part of a larger, eventually more standardized message.
    Example structure for the context block:
    {
      \"type\": \"object\",
      \"properties\": {
        \"vedavivi_user_context\": { // Or a more generic name if preferred, but namespace for clarity
          \"type\": \"object\",
          \"description\": \"VEDAVIVI-specific user and initiating agent context for internal A2A communication. Designed to be potentially embeddable in broader A2A protocols in the future.\",
          \"properties\": {
            \"user_id\": { \"type\": \"string\", \"description\": \"The unique identifier of the end-user.\" },
            \"initiating_agent_id\": { \"type\": \"string\", \"description\": \"The identifier of the VEDAVIVI agent initiating the request.\" },
            \"request_timestamp\": { \"type\": \"string\", \"format\": \"date-time\", \"description\": \"Timestamp of when the context was generated for the request.\" }
          },
          \"required\": [\"user_id\", \"initiating_agent_id\", \"request_timestamp\"]
        }
        // ... other potential top-level A2A payload properties for internal use
      },
      \"required\": [\"vedavivi_user_context\"]
    }
    Ensure this schema clearly delineates what is for VEDAVIVI's internal context propagation."
    ```
2.  **Implement Context Injection in PA for Synchronous A2A Calls**
    ```aider
    Prompt: "Modify the PA logic that makes synchronous internal A2A calls (e.g., internal HTTP requests to SAs). When forming the request payload, ensure it includes the `vedavivi_user_context` block (or chosen name) with the current end-user's `user_id`, the PA's `agent_id` (as `initiating_agent_id`), and a `request_timestamp`, conforming to the updated `a2a-schema.json`."
    File to UPDATE: (Relevant PA service files making HTTP calls, e.g., `pa_service.py` or specific agent interaction modules)
    Function to UPDATE: (Functions responsible for making outgoing synchronous internal A2A requests)
    Details: "The PA must have access to the `user_id` from the user's session. This might involve fetching it from the GraphQL context or a similar mechanism. Ensure the `initiating_agent_id` is correctly set."
    ```
3.  **Implement Context Injection in PA for Asynchronous A2A Messages**
    ```aider
    Prompt: "Modify the PA logic that sends asynchronous internal A2A messages (e.g., publishing to a message queue for SAs). When forming the message payload, ensure it includes the `vedavivi_user_context` block (or chosen name) with the current end-user's `user_id`, the PA's `agent_id` (as `initiating_agent_id`), and a `request_timestamp`, conforming to the updated `a2a-schema.json`."
    File to UPDATE: (Relevant PA service files interacting with message queues, e.g., `pa_event_publisher.py`)
    Function to UPDATE: (Functions responsible for publishing internal A2A messages)
    Details: "Ensure the message structure adheres to the defined schema for asynchronous internal communication."
    ```
4.  **Implement Context Extraction in SA for Synchronous A2A Calls**
    ```aider
    Prompt: "Modify SA logic that handles incoming synchronous internal A2A calls (e.g., internal HTTP request handlers). SAs must extract the `user_id`, `initiating_agent_id`, and `request_timestamp` from the `vedavivi_user_context` block of the request payload. Make these details available for subsequent operations within the SA."
    File to UPDATE: (Relevant SA service files handling HTTP requests, e.g., `specialized_agent_api.py`)
    Function to UPDATE: (Functions responsible for handling incoming synchronous internal A2A requests)
    Details: "Implement validation to ensure the `vedavivi_user_context` and its required fields are present and valid as per `a2a-schema.json`. Log errors if context is missing or malformed."
    ```
5.  **Implement Context Extraction in SA for Asynchronous A2A Messages**
    ```aider
    Prompt: "Modify SA logic that consumes asynchronous internal A2A messages (e.g., from a message queue). SAs must extract the `user_id`, `initiating_agent_id`, and `request_timestamp` from the `vedavivi_user_context` block of the message payload. Make these details available for subsequent operations."
    File to UPDATE: (Relevant SA worker/consumer files processing messages, e.g., `specialized_agent_worker.py`)
    Function to UPDATE: (Functions responsible for processing incoming internal A2A messages)
    Details: "Implement validation for the presence and structure of `vedavivi_user_context` in messages. Log errors if context is missing or malformed."
    ```
6.  **Update Authorization Logic (Implicit)**
    ```aider
    Prompt: "Review and confirm that SA authorization logic relies on the propagated `user_id` primarily for data scoping. Explicit re-authentication of the user at the SA level is not required for this story as authorization is implicit based on the PA's authenticated session. Ensure SA operations use the `user_id` to access/modify data within that user's scope."
    File to UPDATE: (Relevant SA service files performing data operations)
    Details: "This is more of a confirmation task. Ensure no SAs attempt to re-authenticate the user based on the propagated context alone for authorization of the SA's action itself. The `user_id` is for scoping and context, not for primary authZ of the SA's right to act. The `initiating_agent_id` can be used for logging or fine-grained internal checks if necessary."
    ```
7.  **Write Unit and Integration Tests**
    ```aider
    Prompt: "Develop unit tests for:
    1. PA context injection logic (sync and async), verifying the `vedavivi_user_context` block.
    2. SA context extraction logic (sync and async), verifying the extracted fields.
    3. Validation against `a2a-schema.json` for the `vedavivi_user_context` block (if a validation library is used).
    Develop integration tests for:
    1. An end-to-end PA -> SA (sync) internal flow demonstrating `user_id`, `initiating_agent_id`, and `request_timestamp` propagation and use.
    2. An end-to-end PA -> SA (async) internal flow demonstrating `user_id`, `initiating_agent_id`, and `request_timestamp` propagation and use."
    File to CREATE or UPDATE: (Relevant test files for PAs and SAs, e.g., `tests/a2a/test_context_propagation.py`)
    ```
8.  **Documentation**
    ```aider
    Prompt: "Update any relevant internal A2A communication documentation or ADRs to reflect the standardized method of user context propagation, referencing the `a2a-schema.json` and specifically the `vedavivi_user_context` block. Briefly note the design consideration for future interoperability without elaborating on external specs themselves."
    File to UPDATE: (e.g., `ADR-002-Backend-Overview.md`, developer documentation for internal A2A interactions)
    ```
