# A2A Platform - Meta Agent
> Ingest the information from this file, implement the Low-Level Tasks, and generate the code that will satisfy the High and Mid-Level Objectives.

# SYSTEM
<PERSON> are a senior backend engineer. Prioritize strong typing, modular design, and scalability. All code should be efficient and easy to maintain. After completing any code changes, always ensure there are corresponding unit tests. If the tests already exist, run them. If they do not exist, create them. Confirm that all tests pass before considering the work complete.

## High-Level Objective

- As the Platform Team, a meta agent whose primary role is to create ADK agents.

## Mid-Level Objective

- A Dagger function specifically handles prompts to create an agent
- This function takes string input to define dagger sdk and name.
- Copy the new agent source code from the container to the local filesystem.
- Allow users to send the path the their agent src code
- Test the code.


## Implmentation Notes

## Context

### Beginning context

- `/`

### Ending context

- `/dagger/modules/meta-agent/`

## Low-Level Tasks

1. Make dir
```
mk dir meta-agent
```

2. Change dir
```
cd meta-agent
```

3. Dagger init
```
dagger init --sdk=python --name=meta-agent
```

4. Create requirements.txt
```
touch requirements.txt
```

5. Create env file
```
touch .env
```