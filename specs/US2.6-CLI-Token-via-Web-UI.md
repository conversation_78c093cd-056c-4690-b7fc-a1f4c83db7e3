# Generate VEDAVIVI CLI Token via Web UI
> Ingest the information from this file, implement the Low-Level Tasks, and generate the code that will satisfy the High and Mid-Level Objectives.

## High-Level Objective

Enable terminal client users to securely authenticate their CLI interactions with the VEDAVIVI platform by generating a CLI Token through the web UI.

## Mid-Level Objective

- Implement a user interface within the VEDAVIVI web application for managing CLI tokens.
- Develop a backend mechanism to generate unique, secure CLI tokens.
- Securely store a hashed version of the generated token along with its metadata.
- Ensure the generated token is displayed to the user once for them to copy.
- Document low level tasks as they are identified

## Implementation Notes

- **Token Generation:**
    - The CLI token MUST be cryptographically secure and unique.
    - A prefix should be part of the token to help identify it (e.g., `vv_cli_`).
    - The full token is displayed to the user only once upon generation.
- **Token Storage:**
    - The `cli_tokens` table will store a hashed version of the token (e.g., using SHA-256 or a stronger algorithm). Salting should be employed.
    - Metadata to be stored:
        - `user_id`: Foreign key referencing the user who generated the token.
        - `token_prefix`: The non-secret prefix of the token.
        - `hashed_token`: The hashed suffix of the token.
        - `description`: User-provided description for the token.
        - `created_at`: Timestamp of token creation.
        - `last_used_at`: Timestamp of last token usage (initially null).
        - `expires_at`: Timestamp for token expiry (optional, consider for future implementation).
- **Security:**
    - Ensure the endpoint for token creation is authenticated and authorized.
    - The raw token should not be retrievable after the initial display.
- **GraphQL:**
    - The `createCliToken` mutation will accept a `description` string.
    - The resolver for `createCliToken` will handle the token generation logic, hashing, and database insertion.
    - The mutation should return the generated raw token and its prefix for display, or an error if generation fails.
- **Frontend:**
    - A dedicated section in the user settings or profile area for CLI token management.
    - A button to initiate the "Create new token" flow.
    - A modal or form to input the token description.
    - Clear display of the generated token with a copy-to-clipboard functionality.
    - A warning that the token will not be shown again.

## Context

### Beginning Context

- `graphql/schema/mutations.graphql` (contains `createCliToken` definition)
- `api/resolvers/cli_token_resolver.py` (contains logic for `createCliToken`)
- `database/models/cli_token.py` (defines `cli_tokens` table schema)
- `frontend/src/components/CliTokenManager.tsx` (UI component for token management)
- `frontend/src/services/api.tsx` (frontend service to call GraphQL mutations)

### Ending Context

- `graphql/schema/mutations.graphql` (updated, if necessary)
- `api/resolvers/cli_token_resolver.py` (updated with token generation, hashing, and storage logic)
- `database/models/cli_token.py` (schema confirmed or updated if necessary)
- `frontend/src/components/CliTokenManager.tsx` (updated with UI for token creation and display)
- `frontend/src/services/api.tsx` (updated to integrate with `createCliToken` mutation)
- `tests/api/resolvers/test_cli_token_resolver.py` (new or updated unit tests for the resolver)

## Low-Level Tasks

*   **Backend:**
    *   Define `CliToken` SQLAlchemy model in `apps/backend/src/a2a_platform/db/models/cli_token.py`. ✅
    *   Define GraphQL schema types (`CliTokenGraphQLType`, `CreateCliTokenInput`, `CreateCliTokenPayload`) in `apps/backend/src/a2a_platform/api/graphql/schemas/user_profile_schemas.py`. ✅
    *   Implement `resolve_create_cli_token` resolver in `apps/backend/src/a2a_platform/api/graphql/resolvers/user_profile_resolvers.py` with logic for: ✅
        *   Authentication check.
        *   Secure token and salt generation.
        *   Token hashing (PBKDF2-SHA256).
        *   Database insertion of `CliTokenModel`.
        *   Returning raw token and `CliTokenGraphQLType`.
    *   Register `create_cli_token` mutation in the main GraphQL schema (`apps/backend/src/a2a_platform/api/graphql/__init__.py`). ✅
    *   Write integration tests for the `create_cli_token` resolver (`apps/backend/tests/integration/test_graphql_user_profile.py`), covering authenticated (direct) and unauthenticated (HTTP) scenarios. ✅ *(Note: HTTP authenticated test is currently skipped due to async issues).*

*   **Frontend (`apps/web/`):**
    *   **Apollo Client Setup:** ✅
        *   Create `apps/web/src/lib/apolloClient.tsx` to configure `ApolloClient`. ✅
        *   Implement `createApolloClient` function that accepts a `getToken` callback. ✅
        *   Use `setContext` to create an `authLink` that injects the Clerk authentication token into GraphQL request headers. ✅
        *   Add environment variable handling for getting the GraphQL API URL. ✅
        *   Implement runtime configuration to handle Docker environment variables. ✅
    *   **GraphQL Operations:** ✅
        *   Create `apps/web/src/graphql/mutations.tsx` to define GraphQL mutations. ✅
        *   Define `CREATE_CLI_TOKEN_MUTATION` using `gql` for the `createCliToken` operation. ✅
    *   **CLI Token Management UI (`CliTokenManager.tsx`):** ✅
        *   Create the `apps/web/src/components/CliTokenManager.tsx` React component. ✅
        *   Implement a button to trigger the token creation process (e.g., open a modal). ✅
        *   Implement a modal form for the user to input a `description` for the new token. ✅
        *   Use the `useMutation` hook from `@apollo/client` with `CREATE_CLI_TOKEN_MUTATION` to call the backend. ✅
        *   Handle loading and error states from the mutation. ✅
        *   On successful token creation: ✅
            *   Display the raw generated token to the user clearly. ✅
            *   Include a prominent warning that the token will not be shown again. ✅
            *   Provide a "Copy to Clipboard" button for the generated token (`navigator.clipboard`). ✅
            *   Close the modal. ✅
        *   Style the component and modal for usability with Tailwind CSS. ✅
    *   **Docker & Environment Configuration:** ✅
        *   Update Docker configuration files to include `VITE_GRAPHQL_API_URL`. ✅
        *   Configure both `docker-compose.yml` and `docker-compose.dev.yml`. ✅
        *   Add fallbacks in code for when environment variables aren't set. ✅
        *   Create sample environment file for local development. ✅
        *   Add runtime environment variable substitution via HTML template. ✅
    *   **Integration:**
        *   Create user settings page to display the CliTokenManager. (Pending)
        *   Add route for the settings page in the application router. (Pending)
    *   **Utility Functions:** ✅
        *   Created `utils.ts` with the `cn` utility function for Tailwind class merging. ✅