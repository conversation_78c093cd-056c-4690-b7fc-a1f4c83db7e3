## US2.1: New User Sign-Up & Account Creation (Backend)

> Ingest the information from this file, implement the Low-Level Tasks, and generate the code that will satisfy the High and Mid-Level Objectives for a new user signing up, acknowledging prior frontend implementation.

### High-Level Objective

  - As a new user, I want to be able to sign up for VEDAVIVI using my Google account, GitHub account, or email and a magic link, so that I have a convenient and secure way to create an account.

### Mid-Level Objectives

  - Implement backend integration with Clerk.com to specifically handle the event of a *new user* completing the sign-up flow initiated by the frontend.
  - Develop a secure webhook handler to listen for and process the Clerk.com `user.created` event, which signifies a new user has successfully signed up and authenticated via Clerk.
  - Upon receiving the `user.created` event from <PERSON> for a new sign-up, ensure a corresponding VEDAVIVI `users` table record is atomically created, linking the `clerk_user_id` and populating essential VEDAVIVI-specific default attributes.
  - Ensure VEDAVIVI's GraphQL API endpoints (that would be accessed immediately post-sign-up by a new user) are secured by validating Clerk-issued JWTs.

### Implementation Notes

  - **Important technical details**:
      - This spec focuses exclusively on the backend processes triggered when a *new user successfully completes a sign-up* via Google, GitHub, or Email Magic Link through the Clerk-enabled frontend.
      - The primary mechanism for the backend to learn about a new user sign-up is the Clerk `user.created` webhook.
      - The VEDAVIVI `users` table must include `clerk_user_id` (UNIQUE, NOT NULL) to link to the Clerk identity. Default values for VEDAVIVI-specific fields (e.g., `timezone`, `preferences`) should be set upon creation.
      - The backend GraphQL API must validate Clerk JWTs to authenticate requests from the newly signed-up and logged-in user.
      - Webhook security: The `user.created` webhook handler must validate incoming Clerk requests using Clerk's signing secret.
  - **Dependencies and requirements**:
      - Clerk.com account fully configured for OAuth (Google, GitHub), Email Magic Link, and the `user.created` webhook pointing to the VEDAVIVI backend.
      - Implemented frontend for US2.1 enabling user sign-up flows via Clerk.
      - VEDAVIVI backend environment (Python, PostgreSQL).
      - Definition of the VEDAVIVI `users` table schema.
  - **Coding standards to follow**:
      - Python backend coding standards.
  - **Other technical guidance**:
      - The creation of the VEDAVIVI `users` record upon a `user.created` event should be idempotent, although `user.created` is typically fired only once per user.
      - Logging for `user.created` event processing is crucial.

### Context

**Beginning context**

  - The frontend for US2.1 sign-up (Google, GitHub, Email Magic Link) is implemented and uses Clerk.js.
  - Clerk.com application is configured, and the `user.created` webhook is set up with its signing secret known to the backend deployment environment.
  - VEDAVIVI PostgreSQL database is running, but the `users` table logic for Clerk integration might not be fully implemented.
  - Basic backend API structure exists but may not yet have JWT validation tied to Clerk for newly signed-up users.

**Ending context**

  - Backend webhook endpoint successfully receives and processes `user.created` events from Clerk for new sign-ups.
  - A new VEDAVIVI `users` record is created in the PostgreSQL database when a new user signs up via Clerk, correctly linked via `clerk_user_id`.
  - The VEDAVIVI GraphQL API can validate the Clerk JWT for the newly signed-up user, allowing them to access protected resources appropriate for a new user.
  - The act of a new user signing up (US2.1) successfully results in a VEDAVIVI account being created and the user being in a logged-in state, recognized by the backend.

### Low-Level Tasks

> Ordered from start to finish

1.  **Backend: Configure Clerk Backend SDK for JWT Verification & Webhook Handling**
    ```aider
    Prompt: Ensure the Clerk Backend SDK for Python is configured in the VEDAVIVI backend. Set up environment variables for Clerk API Key and Webhook Signing Secret. Initialize the Clerk client.
    File: src/core/clerk_config.py (or similar central configuration)
    Details: This setup is foundational for both JWT validation and webhook verification.
    ```
2.  **Database: Finalize `users` Table Schema for New User Creation**
    ```aider
    Prompt: Define/confirm the PostgreSQL `users` table schema to support new user creation via Clerk. Ensure it includes `id (UUID, PK)`, `clerk_user_id (TEXT, UNIQUE, NOT NULL)`, `timezone (TEXT, NULL)`, `preferences (JSONB, NULL, DEFAULT '{}')`, `created_at (TIMESTAMPTZ, DEFAULT now())`, `updated_at (TIMESTAMPTZ, DEFAULT now())`. Create a migration if necessary.
    File: database/migrations/YYYYMMDDHHMMSS_create_users_table_for_clerk.sql
    Details: `clerk_user_id` must be unique. Consider default values for VEDAVIVI-specific fields.
    ```
3.  **Backend: Implement Secure Webhook Handler for `user.created` Events**
    ```aider
    Prompt: Develop the API endpoint (e.g., `/api/v1/webhooks/clerk`) to receive `user.created` events from Clerk. Implement robust signature verification using the Clerk Backend SDK and the webhook signing secret.
    File: src/webhooks/clerk_webhook_router.py
    Function: handle_clerk_event (or specifically handle_user_created_event)
    Details: Reject requests with invalid signatures. Log receipt of events.
    ```
4.  **Backend: Implement Logic to Create VEDAVIVI User Record from `user.created` Event**
    ```aider
    Prompt: Inside the webhook handler, for verified `user.created` events, extract user details from the payload (Clerk user ID, email, name if available). Insert a new record into the VEDAVIVI `users` table, linking it with `clerk_user_id` and setting default values for VEDAVIVI-specific fields.
    File: src/services/user_service.py (or directly in webhook processor)
    Function: create_a2a_user_from_clerk_event
    Details: Handle potential database errors (e.g., unique constraint violation on `clerk_user_id` if, for some reason, the event is replayed, though `user.created` should be once).
    ```
5.  **Backend: Implement/Confirm JWT Validation Middleware for GraphQL API**
    ```aider
    Prompt: Ensure that the GraphQL API has middleware that extracts the JWT from the `Authorization` header of incoming requests. Use the Clerk Backend SDK to verify the token. If valid, make user information (especially `clerk_user_id`) available to resolvers. If invalid, return an authentication error. This is critical for the newly signed-up user's subsequent API calls.
    File: src/graphql_api/middleware/auth_middleware.py
    Details: This middleware protects resources and identifies the user for API operations.
    ```
6.  **Testing: Backend Unit Test for `user.created` Event Processing**
    ```aider
    Prompt: Write unit tests for the logic that processes a `user.created` event payload and creates a VEDAVIVI user record. Mock Clerk SDK calls (for verification if used directly in processing) and database interactions.
    File: tests/services/test_user_service.py (or tests/webhooks/test_clerk_user_created_processor.py)
    Details: Verify correct data mapping and database record creation.
    ```
7.  **Testing: Backend Integration Test for `user.created` Webhook Endpoint**
    ```aider
    Prompt: Write an integration test for the webhook endpoint. Simulate a valid, signed `user.created` event from Clerk. Verify the endpoint returns a 2xx status and that a corresponding record is created in the test database.
    File: tests/integration/test_clerk_user_created_webhook.py
    Details: Ensure the test includes signature generation and verification.
    ```
8.  **Testing: Backend Integration Test for JWT Authentication on a Sample GraphQL Query**
    ```aider
    Prompt: Test a sample GraphQL query that requires authentication. Verify it fails with no token, fails with an invalid token, and succeeds with a valid mock Clerk JWT, correctly identifying the user context.
    File: tests/integration/test_graphql_auth_for_new_user.py
    Details: This confirms that a newly signed-up user's token will be accepted.
    ```
9.  **End-to-End (E2E) Scenario Test: New User Sign-Up**
    ```aider
    Prompt: Manually execute the new user sign-up flow using one of the methods (e.g., Google) via the implemented frontend.
    1. Confirm successful sign-up on the frontend.
    2. Verify in the backend logs that the `user.created` webhook was received and processed.
    3. Verify in the VEDAVIVI database that a new `users` record exists with the correct `clerk_user_id` and default VEDAVIVI fields.
    4. Attempt a simple authenticated GraphQL query from the frontend (or a tool like Postman/Insomnia using the token obtained by the frontend from Clerk) to ensure the backend recognizes the new user's session.
    Details: This is the ultimate validation that US2.1 is fully delivered.
    ```
