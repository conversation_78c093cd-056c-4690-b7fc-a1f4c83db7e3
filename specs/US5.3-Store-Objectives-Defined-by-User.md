## US5.3 Store Objectives Defined by User

> As the Personal Assistant, I want to store objectives defined by the user via chat in the assistant\_objectives table, so that these goals can be tracked and referenced.

### High-Level Objective

  - As a Personal Assistant, I want to store objectives defined by the user via chat in the `assistant_objectives` table, so that these goals can be tracked and referenced.

### Mid-Level Objectives

  - Given a user defines an objective in chat (e.g., "Help me learn Spanish"), When the PA identifies and confirms this as an objective, Then a new record is created in the `assistant_objectives` table with the objective text and linked to the assistant.
  - Ensure the storage mechanism allows for tracking of these objectives.
  - Ensure the stored objectives can be referenced by the Personal Assistant and potentially the user.

### Implementation Notes

  - **Important technical details**:
      - The objective text needs to be stored accurately as defined by the user.
      - The new record in `assistant_objectives` must be linked to the correct assistant (and thereby the user).
      - The process of identifying and confirming an objective in chat is a prerequisite.
  - **Dependencies and requirements**:
      - `assistant_objectives` table schema must be defined and accessible.
      - US1.7 (User defines objective in chat) must be implemented to provide the trigger for this user story.
      - Backend logic for objective extraction from chat and subsequent storage processing is required.
  - **Coding standards to follow**:
      - Adhere to Python backend coding standards.
      - Ensure database interactions are secure and efficient.
  - **Other technical guidance**:
      - Consider potential future needs like objective status tracking (e.g., active, completed, cancelled) or linking objectives to specific tasks, although not in scope for this US.
      - Ensure the `objective_text` field can handle a reasonable length of text.

### Context

**Beginning context**

  - `apps/backend/src/a2a_platform/db/models/assistant_objective.py` (or similar, may not exist yet but `assistant_objectives` table schema is a dependency)
  - `apps/backend/src/a2a_platform/services/assistant_service.py` (or a new `objective_service.py`) - may need new functions.
  - Backend logic for identifying objectives in chat (dependency from US1.7).
  - `assistant_objectives` table schema is defined.

**Ending context**

  - `apps/backend/src/a2a_platform/db/models/assistant_objective.py` (if new, or updated if exists) - SQLAlchemy model for `assistant_objectives` table.
  - `apps/backend/src/a2a_platform/schemas/objective_schemas.py` (or similar) - Pydantic schemas for objective data.
  - `apps/backend/src/a2a_platform/services/objective_service.py` (or updated in `assistant_service.py`) - Contains logic to create objective records.
  - Backend logic (potentially in a chat processing service or assistant core logic) calls the objective service to store the objective.
  - Unit and integration tests for the objective storage functionality.

### Low-Level Tasks

> Ordered from start to finish

1.  **Define/Verify `assistant_objectives` Table Model and Schema**
    ```aider
    Prompt: "Define or verify the SQLAlchemy model for the `assistant_objectives` table based on its schema (Source 1311). Create Pydantic schemas for creating and reading objectives. Ensure fields like `assistant_id` (linking to the assistant), `objective_text`, and `status` (e.g., default to 'active') are included. Also include `created_at` and `updated_at` timestamps."
    File to CREATE or UPDATE: `apps/backend/src/a2a_platform/db/models/assistant_objective.py` (model might be `AssistantObjective`)
    File to CREATE or UPDATE: `apps/backend/src/a2a_platform/schemas/objective_schemas.py`
    Details: "The schema from the VEDAVIVI: MVP Backend Technical Plan v1.5 (Source 479) for `assistant_objectives` includes: `id (UUID, PK)`, `assistant_id (UUID, FK to assistants)`, `objective_text (TEXT, NOT NULL)`, `status (TEXT, DEFAULT 'active')`, `created_at`, `updated_at`, `completed_at`, `metadata (JSONB)`. Ensure the model and Pydantic schemas align."
    ```
2.  **Implement Objective Storage Service Logic**
    ```aider
    Prompt: "Create a function in an `objective_service.py` (or update `assistant_service.py`) called `add_assistant_objective`. This function should take `assistant_id` and `objective_text` as input. It should create a new record in the `assistant_objectives` table, linking it to the `assistant_id`, storing the `objective_text`, and setting a default status (e.g., 'active'). The function should return the created objective object."
    File to CREATE or UPDATE: `apps/backend/src/a2a_platform/services/objective_service.py`
    Function to CREATE or UPDATE: `add_assistant_objective`
    Details: "Handle potential database errors. Use the Pydantic schema for input validation if appropriate at the service boundary."
    ```
3.  **Integrate Objective Storage into Chat Processing Logic**
    ```aider
    Prompt: "Modify the backend logic that processes chat messages and identifies user objectives (from US1.7). After an objective is identified and confirmed, call the `add_assistant_objective` service function to store it. Ensure the correct `assistant_id` is passed."
    File to UPDATE: (Relevant file from US1.7 implementation, e.g., `apps/backend/src/a2a_platform/services/chat_service.py` or assistant core logic module)
    Function to UPDATE: (The function that handles objective identification and confirmation)
    Details: "This task links the identification of an objective with its storage."
    ```
4.  **Write Unit Tests for Objective Storage Service**
    ```aider
    Prompt: "Write unit tests for the `add_assistant_objective` function. Mock the database session and verify that:
    1. A new objective record is correctly prepared for insertion.
    2. The `assistant_id` and `objective_text` are correctly assigned.
    3. The default status is set.
    4. The database session's `add` and `commit` methods are called.
    5. The created objective object is returned."
    File to CREATE or UPDATE: `apps/backend/tests/unit/services/test_objective_service.py`
    ```
5.  **Write Integration Test for Objective Storage Flow**
    ```aider
    Prompt: "Write an integration test that simulates a user defining an objective in chat (mocking the output of US1.7's identification part). Verify that this triggers the backend logic to store the objective in the `assistant_objectives` table in a test database. Confirm the record's `objective_text` and its link to the correct assistant."
    File to CREATE or UPDATE: `apps/backend/tests/integration/test_objective_management.py`
    ```
6.  **Documentation Update (If Necessary)**
    ```aider
    Prompt: "Review existing backend documentation (e.g., READMEs, service descriptions). If necessary, update it to include details about how objectives defined in chat are stored, referencing the `assistant_objectives` table and the relevant services."
    File to UPDATE: (Relevant documentation files, e.g., `apps/backend/README.md` or specific service/module READMEs)
    Details: "Focus on explaining the data flow for storing objectives."
    ```
