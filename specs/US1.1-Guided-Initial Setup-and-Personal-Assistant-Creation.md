## US1.1 Guided Initial Setup & Personal Assistant Creation
> As a new user, I want to be guided through the initial setup of my Personal Assistant after my first login, culminating in the creation of my PA instance, so that I can quickly start using its core features.

### High-Level Objective

- Guide new users through an initial setup wizard after their first login, which includes naming their Personal Assistant (PA) and triggering the creation of the PA's backend infrastructure (database model, API endpoint) and instance, enabling quick adoption of core features.

### Mid-Level Objective

-   **Define and implement the `Assistant` data model** in the backend, including its database table and necessary ORM/schema definitions.
-   **Implement a backend API endpoint** (GraphQL mutation) for creating a new `Assistant` instance, associating it with the logged-in user.
-   Present an initial setup wizard/guided flow in the frontend when a first-time logged-in user accesses the Personal Assistant section.
-   Allow the user to perform mandatory setup steps within the wizard (e.g., naming their assistant).
-   Upon wizard completion, call the new backend API to create and associate the `Assistant` instance with the user.
-   Direct the user to the main PA interface (e.g., chat) after successful PA creation and setup.
-   Update user profile to indicate PA setup completion.

### Implementation Notes

-   **`Assistant` Model & Database Table (`assistants`):**
    * To be created based on "VEDAVIVI: MVP Backend Technical Plan v1.5" (Section 4, Data Model).
    * **SQLAlchemy Model:** Create in `apps/backend/src/a2a_platform/db/models/assistant.py`.
    * **Pydantic Schema:** Define in `apps/backend/src/a2a_platform/schemas/assistant_schemas.py`.
    * **Key Fields** (from Backend Tech Plan):
        * `id` (UUID, PK)
        * `user_id` (UUID, FK to `users` table, UNIQUE to enforce one PA per user)
        * `name` (TEXT, NOT NULL)
        * `backstory` (TEXT, NOT NULL - for MVP, can be a default or simplified input)
        * `avatar_file_id` (UUID, FK, optional)
        * `configuration` (JSONB, for future extensibility, can be minimal for MVP)
        * `created_at` (TIMESTAMP WITH TIMEZONE, default now())
        * `updated_at` (TIMESTAMP WITH TIMEZONE, default now(), onupdate now())
    * **Alembic Migration:** A new migration script is required to create the `assistants` table.
-   **Backend API for PA Creation:**
    * **GraphQL Mutation:** `createPersonalAssistant(name: String!, backstory: String, avatar_file_id: UUID): Assistant` (or similar, input fields can be refined for MVP). This aligns with `createAssistant` mentioned in the Backend Tech Plan.
    * **Service Layer:** Implement creation logic in `apps/backend/src/a2a_platform/services/agent_service.py` or a new `apps/backend/src/a2a_platform/services/assistant_service.py`. This service will handle interaction with the `Assistant` model.
    * **Resolver:** Implement in a new `apps/backend/src/a2a_platform/api/graphql/resolvers/assistant_resolvers.py` or integrate into an existing relevant resolver.
-   **Frontend Wizard:**
    * Develop a multi-step wizard UI (e.g., using React components in `apps/web/src/components/pa_setup_wizard/`).
    * The wizard should collect necessary information (at least the PA's name).
-   **User Profile Update:**
    * A flag like `is_pa_setup_complete` or `pa_id` on the `User` model (`apps/backend/src/a2a_platform/db/models/user.py`) should be updated upon successful PA creation. This might require an Alembic migration if the field doesn't exist.
-   **Coding standards:** Follow existing project coding standards (React/TypeScript for frontend, Python/FastAPI/SQLAlchemy for backend).

### Dependencies

-   US2.1 (User Registration and Login - frontend & backend): A user must be authenticated to create a PA.
-   US2.3 (Create Minimal User Record on First Authentication): Ensures a `user` record and `user_id` exist to associate with the PA.
-   Potentially US2.4 (Update VEDAVIVI-Specific Profile Data) if the `is_pa_setup_complete` flag is managed via general profile updates.

### Context

**Beginning context**
-   `apps/backend/src/a2a_platform/db/models/user.py` exists.
-   User authentication (US2.1, US2.3) is functional.
-   **No** `assistants` table in the database.
-   **No** `Assistant` SQLAlchemy model or Pydantic schema.
-   **No** backend API endpoint (GraphQL mutation) specifically for creating a Personal Assistant.
-   Frontend components for PA setup wizard may not exist.

**Ending context**
-   **New Backend Files/Entities:**
    * `apps/backend/src/a2a_platform/db/models/assistant.py` (SQLAlchemy model for `Assistant`).
    * New Alembic migration script in `apps/backend/alembic/versions/` to create the `assistants` table.
    * `apps/backend/src/a2a_platform/schemas/assistant_schemas.py` (Pydantic schemas for `Assistant`).
    * The `assistants` table is created in the database.
    * Logic in `apps/backend/src/a2a_platform/services/agent_service.py` (or a new `assistant_service.py`) to handle PA creation.
    * New GraphQL mutation (`createPersonalAssistant`) and its resolver, likely in `apps/backend/src/a2a_platform/api/graphql/resolvers/assistant_resolvers.py`.
-   **Updated Backend Files:**
    * `apps/backend/src/a2a_platform/db/models/user.py` and `apps/backend/src/a2a_platform/services/user_service.py` might be updated if a flag like `is_pa_setup_complete` or `pa_id` is added to the User model/service.
    * Relevant `__init__.py` files and GraphQL schema type definitions.
-   **New Frontend Components:**
    * `apps/web/src/components/pa_setup_wizard/SetupWizard.tsx`
    * `apps/web/src/components/pa_setup_wizard/StepNameAssistant.tsx` (and any other wizard steps)
-   **Updated Frontend Components:**
    * The main Personal Assistant access point (e.g., `apps/web/src/pages/dashboard/DashboardPage.tsx`) updated to launch the wizard and then the main PA interface.

### Low-Level Tasks
> Ordered from start to finish

1.  **Define `Assistant` SQLAlchemy Model and Pydantic Schemas**
    ```aider
    Define the SQLAlchemy model for `Assistant` in a new file `apps/backend/src/a2a_platform/db/models/assistant.py`. Define corresponding Pydantic schemas in a new file `apps/backend/src/a2a_platform/schemas/assistant_schemas.py`. Include fields like `id`, `user_id` (FK to User), `name`, `backstory`, `configuration`, `created_at`, `updated_at` as per the Backend Technical Plan.
    CREATE /apps/backend/src/a2a_platform/db/models/assistant.py
    CREATE /apps/backend/src/a2a_platform/schemas/assistant_schemas.py
    Ensure `user_id` has a UNIQUE constraint to enforce one PA per user. The `backstory` can have a default value for MVP.
    ```
2.  **Create Alembic Migration for `assistants` Table**
    ```aider
    Generate an Alembic migration script to create the `assistants` table in the database based on the new `Assistant` SQLAlchemy model.
    RUN `alembic revision -m "create_assistants_table"` in `apps/backend/` and then implement the `upgrade` and `downgrade` functions in the generated migration file.
    CREATE /apps/backend/alembic/versions/<new_migration_file_for_assistants_table>.py
    The migration should define the table schema including primary keys, foreign keys, constraints, and indexes.
    ```
3.  **(Optional) Update User Model for PA Link/Status**
    ```aider
    If a direct link or status flag (e.g., `pa_id` or `is_pa_setup_complete`) is desired on the `User` model, update `apps/backend/src/a2a_platform/db/models/user.py`. Generate and implement an Alembic migration if schema changes are made.
    /apps/backend/src/a2a_platform/db/models/user.py
    CREATE /apps/backend/alembic/versions/<new_migration_file_for_user_table_update>.py (if schema changes)
    This helps quickly identify if a user has completed PA setup.
    ```
4.  **Implement Backend Service Logic for PA Creation**
    ```aider
    In `apps/backend/src/a2a_platform/services/agent_service.py` (or a new `assistant_service.py`), implement a function `create_personal_assistant(db: Session, user_id: UUID, name: str, backstory: Optional[str] = None, ...) -> Assistant`. This function will create and save a new `Assistant` instance. If `is_pa_setup_complete` is added to user, update it here.
    /apps/backend/src/a2a_platform/services/agent_service.py
    (or CREATE /apps/backend/src/a2a_platform/services/assistant_service.py)
    Function `create_personal_assistant`
    The service should handle database session management and commit the new assistant.
    ```
5.  **Implement GraphQL Mutation for PA Creation**
    ```aider
    Define and implement the `createPersonalAssistant` GraphQL mutation. This includes defining the input type and return type in the GraphQL schema, and creating a resolver function in `apps/backend/src/a2a_platform/api/graphql/resolvers/assistant_resolvers.py` (new file, or integrate into existing). The resolver will call the service function created in the previous step.
    CREATE /apps/backend/src/a2a_platform/api/graphql/resolvers/assistant_resolvers.py
    (and update GraphQL schema type definitions)
    Mutation `createPersonalAssistant(name: String!, ...)`
    Ensure the mutation is authenticated and uses the logged-in user's ID.
    ```
6.  **Design and Implement Frontend UI for PA Setup Wizard**
    ```aider
    Create React components for a multi-step PA setup wizard (e.g., for naming the assistant). Store these in `apps/web/src/components/pa_setup_wizard/`.
    CREATE /apps/web/src/components/pa_setup_wizard/SetupWizard.tsx
    CREATE /apps/web/src/components/pa_setup_wizard/StepNameAssistant.tsx
    The wizard should take user input (e.g., PA name) and, on final submission, call the `createPersonalAssistant` GraphQL mutation.
    ```
7.  **Implement Frontend Logic to Launch Wizard & Handle Creation**
    ```aider
    In the main PA access point (e.g., `apps/web/src/pages/dashboard/DashboardPage.tsx`), check if the user needs to go through PA setup (e.g., by checking the `is_pa_setup_complete` flag or if their `pa_id` is null from their user profile data). If setup is needed, display the `SetupWizard.tsx`. Upon successful PA creation via the wizard, redirect to the main PA interface.
    /apps/web/src/pages/dashboard/DashboardPage.tsx
    (or a dedicated PA page component)
    Update the component to fetch user PA status, conditionally render wizard or PA interface, and handle post-creation navigation.
    ```
