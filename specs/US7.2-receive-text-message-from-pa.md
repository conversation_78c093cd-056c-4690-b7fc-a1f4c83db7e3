# 📋 User Story Specification: US7.2

**Story:** As a user, I want to receive text messages from my Personal Assistant in the chat interface, so that I can see its responses and information.
**Steps:** PA generates response → Message persisted to database → Message displayed in UI → Real-time update via GraphQL subscription
**Epic:** 🎯 Core Conversational Interface & Interaction

## ✅ AC
- [ ] Given my PA has a response or information to send me, When the PA generates a message, Then the message is persisted in the chat_messages table with sender_role='agent' and appears in the conversation history [US7.2 acceptance criteria]
- [ ] Given a new message from the PA is available in the database, When the frontend queries for conversation messages, Then the PA message appears in my chat history in the UI, attributed to the PA with proper styling and timestamp [US7.2 acceptance criteria]
- [ ] Given the PA sends a message, When the message is persisted, Then a GraphQL subscription notification is triggered for real-time display without requiring page refresh [Dependent on US7.3]
- [ ] Given PA message creation fails due to database error, When the error occurs, Then appropriate error handling prevents data corruption and logs the failure with conversation context [Error handling requirements]
- [ ] Given the PA message content contains structured data (text, metadata), When displayed in the UI, Then the message renders correctly with proper formatting and attribution [Chat interface requirements]
- [ ] Given multiple PA messages are sent in sequence, When displayed in the chat interface, Then messages appear in chronological order with proper conversation flow [Chat interface requirements]

## 🔗 Deps
**Stories:** US7.3 (PENDING - Real-time message display via GraphQL Subscriptions) | **Ext:** 🌐 AI/LLM service for PA response generation | **Tech:** ⚙️ Strawberry GraphQL, SQLAlchemy ChatMessage model, React chat components | **Data:** 🗄️ chat_messages table, conversations table | **Team:** 👥 Frontend chat UI implementation

## 💰 Value
**Biz:** 📈 Enables core conversational AI functionality, foundation for user engagement and retention | **UX:** ✨ Real-time PA communication creates seamless AI assistant experience | **KPI:** 📊 Message delivery success rate, response time latency, user engagement metrics | **Why:** 🎯 Essential for MVP conversational interface - users need to receive PA responses to have meaningful interactions

## 📐 ADRs
**Refs:** 📄 ADR-002 Sec 2.4 (GraphQL Subscriptions for real-time chat), ADR-002 Sec 3.2 (chat_messages table structure), ADR-002 Sec 2.3 (WebSocket connections via Redis) | **Limits:** ⚠️ Message size constraints (JSONB content field), subscription scalability limits | **Patterns:** 🏗️ GraphQL subscription pattern, sender_role enum validation, JSONB content structure

## 🛠️ Impl
**Arch:** 🏛️ ChatService.send_message() method for PA message creation, GraphQL mutation for message persistence, subscription resolver for real-time updates | **Data:** 🗄️ chat_messages table (id, conversation_id, sender_role='agent', content JSONB, timestamp, metadata JSONB) | **API:** 🔌 GraphQL mutation sendMessageFromPA(conversationId, content), newMessages subscription | **Sec:** 🔒 PA service authentication via internal API tokens, conversation ownership validation through user_id FK, message attribution integrity | **Perf:** ⚡ Database indexing on (conversation_id, timestamp), Redis pub/sub for subscriptions, query optimization with conversation message pagination | **Int:** 🌐 AI/LLM service integration for PA response generation | **Err:** ❌ Message persistence failure handling, subscription connection error recovery | **Std:** 📏 JSONB content format: {"parts": [{"type": "text", "content": "message"}]}, sender_role validation

## 🧪 Test
**Unit:** 🔬 ChatService.send_message() for PA messages, ChatMessage model validation, GraphQL resolver methods | **Int:** 🔗 Database persistence tests, GraphQL subscription integration tests | **Contract:** 📋 AI/LLM service response format validation | **E2E:** 🎭 Complete PA message flow from generation to UI display | **Perf:** 🚀 Message throughput testing, subscription latency benchmarks | **Sec:** 🛡️ Authorization testing for PA message creation, conversation access validation

## 📏 NFRs
**Perf:** ⚡ Message persistence <100ms, subscription delivery <200ms, UI rendering <50ms | **A11y:** ♿ Screen reader support for PA messages, keyboard navigation | **Sec:** 🔒 PA authentication context, conversation access control | **Scale:** 📈 Support 1000+ concurrent subscriptions, message throughput 100/sec | **Compat:** 🌍 WebSocket support across browsers, mobile responsiveness

## ✅ DoD
- [ ] AC met | [ ] Tests pass | [ ] Security review | [ ] Perf benchmarks | [ ] Docs updated | [ ] Code review | [ ] QA complete

## ⚠️ Risk
**Tech:** 💻 GraphQL subscription complexity → Implement incremental testing and fallback polling | **Biz:** 📊 PA response quality → Establish content validation and error handling | **Time:** ⏰ AI/LLM integration dependencies → Mock PA responses for development | **Deps:** 🔗 US7.3 subscription dependency → Implement basic persistence first, add real-time later

## 📊 Est
**Pts:** 🎯 5 points (medium complexity - database integration + GraphQL patterns) | **Hrs:** ⏱️ 8 dev + 4 test + 2 review = 14 hours | **Complex:** 🧩 GraphQL subscription patterns, PA authentication context, real-time messaging | **Conf:** 📈 Medium confidence - established patterns but subscription complexity | **Vars:** 🔄 AI/LLM service integration timeline, US7.3 completion

## 📦 Data
**API:** 🔌 sendMessageFromPA(conversationId: UUID!, content: JSON!): SendMessagePayload, newMessages(conversationId: UUID!): ChatMessage | **DB:** 🗄️ chat_messages record with sender_role='agent', content JSONB, conversation_id FK | **UI:** 🖥️ Chat message state with PA attribution, timestamp, content rendering | **Config:** ⚙️ AI_SERVICE_URL, PA_AUTH_TOKEN environment variables

## 🎨 Visual
**Layout:** 📐 PA messages aligned left with assistant avatar, distinct styling from user messages, timestamp display | **Flow:** 🌊 PA response → Database → GraphQL subscription → Real-time UI update | **States:** 🔄 Message sending, persisted, delivered, displayed | **Seq:** 📋 PA generates → ChatService.send_message() → Database persist → Subscription notify → UI render

## 🤔 Assume
**Sys:** 🖥️ PostgreSQL database available, Redis for subscriptions, GraphQL server running | **User:** 👤 Active conversation exists, user has WebSocket connection | **Env:** 🌐 Stable network connection, browser WebSocket support | **Data:** 📊 PA responses in structured JSON format, conversation ID validity

## 🎬 Context
**Before:** Files:📁 ChatService.send_message() exists for user messages, ChatMessage model supports sender_role enum, GraphQL schemas defined | Schema:🗄️ chat_messages table with sender_role constraint, conversations table | Components:⚙️ React chat UI components, GraphQL resolvers | Config:⚙️ Database connection, GraphQL server setup
**After:** Files:📁 ChatService enhanced for PA messages, GraphQL resolver for PA message sending, frontend PA message rendering | Schema:🗄️ PA messages in chat_messages table | Components:⚙️ PA message display components, subscription handling | Config:⚙️ AI service integration variables

## 📝 Tasks
1. **Enhance ChatService for PA Messages** - Add method to create PA messages with proper attribution
```coder_llm
**Do:** 🎯 Add send_pa_message(conversation_id, content, metadata) method to ChatService class | **File:** 📁 apps/backend/src/a2a_platform/services/chat_service.py | **Target:** 🎯 ChatService class | **Check:** ✅ Method creates ChatMessage with sender_role='agent', returns persisted message | **Needs:** 🔧 Existing ChatService.send_message() pattern
```

2. **Create GraphQL Mutation for PA Messages** - Implement GraphQL mutation for PA message creation
```coder_llm
**Do:** 🎯 Add sendMessageFromPA mutation to chat resolvers with authentication validation | **File:** 📁 apps/backend/src/a2a_platform/api/graphql/resolvers/chat_resolvers.py | **Target:** 🎯 Chat mutation resolvers | **Check:** ✅ Mutation accepts conversationId and content, returns SendMessagePayload | **Needs:** 🔧 ChatService.send_pa_message() method, PA authentication context
```

3. **Update GraphQL Schemas** - Add PA message mutation schema
```coder_llm
**Do:** 🎯 Add SendMessageFromPAInput and update mutation types in GraphQL schemas | **File:** 📁 apps/backend/src/a2a_platform/api/graphql/schemas/chat_schemas.py | **Target:** 🎯 Input types and mutation definitions | **Check:** ✅ Schema supports PA message creation with proper typing | **Needs:** 🔧 Existing ChatMessage and SendMessagePayload types
```

4. **Implement Frontend PA Message Display** - Create UI components for PA message rendering
```coder_llm
**Do:** 🎯 Create PAMessage component with distinct styling and attribution | **File:** 📁 apps/web/src/components/chat/PAMessage.tsx | **Target:** 🎯 React component for PA messages | **Check:** ✅ Component renders PA messages with proper styling, avatar, timestamp | **Needs:** 🔧 Existing chat UI components and styling system
```

5. **Add PA Message Integration Tests** - Test complete PA message flow
```coder_llm
**Do:** 🎯 Create integration tests for PA message creation, persistence, and GraphQL operations | **File:** 📁 apps/backend/tests/integration/test_pa_messages.py | **Target:** 🎯 Integration test suite | **Check:** ✅ Tests cover PA message creation, GraphQL mutations, database persistence | **Needs:** 🔧 Test database setup, GraphQL test client
```

## 🔧 Refactor
**Quality:** 📏 Separate PA message logic from user message logic in ChatService, consistent error handling patterns | **Perf:** ⚡ Optimize message queries with proper indexing, subscription efficiency | **Reuse:** ♻️ Extract common message validation patterns, shared UI components | **Debt:** 💳 Address message content structure standardization | **Test:** 🧪 Comprehensive unit and integration test coverage | **Docs:** 📚 Document PA message flow and API patterns

---

## Usage
**Symbols:** {text}=required | [text]=optional | {text}|[default]=required+fallback
**Check:** [x] {} replaced | [x] [] evaluated | [x] ADRs ref'd | [x] Tasks actionable | [x] AC=G-W-T | [x] Deps ID'd
