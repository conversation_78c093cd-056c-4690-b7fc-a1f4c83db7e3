# Specification: Implement GitHub Actions for Python Backend CI/CD to GCP and CI Checks

> Ingest the information from this file, implement the Low-Level Tasks, and generate the code that will satisfy the High and Mid-Level Objectives.

## High-Level Objective

1.  Automate the build, test, and deployment process for the Python backend services (`api`, `workers`, `messaging`) to Google Cloud Run for `staging` and `production` environments using GitHub Actions. This includes Docker containerization, database migrations, and secure management of credentials and environment-specific configurations. The deployment to `main` (triggering staging deployment) must only occur if the code has passed all linting and unit test checks.
2.  Implement a continuous integration (CI) process that automatically runs linters and unit tests on every push to any branch (including `main`) and on pull requests targeting `main` to ensure code quality and catch issues early.

## Mid-Level Objective

**For CI Checks (Lint and Test Workflow):**
1.  Develop a separate GitHub Actions workflow (e.g., `ci-checks.yml`) that triggers on pushes to any branch (including `main`) and on pull requests targeting `main`.
2.  Sets up the Python environment and installs dependencies for the backend services.
3.  Runs configured linting tools (e.g., <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>) across the Python codebase.
4.  Executes unit tests (e.g., using `pytest`) for the `api`, `workers`, and `messaging` services.
5.  Reports the status (success/failure) of linting and tests back to GitHub, making these checks available as required status checks for branch protection.

**For CI/CD (Deployment Workflow):**
1.  Develop a GitHub Actions workflow (`deploy.yml`) that triggers on pushes to the `main` branch.
2.  **Pre-condition:** This workflow implicitly depends on the commit having passed all checks defined in `ci-checks.yml`, enforced via GitHub branch protection rules on the `main` branch.
3.  Builds Docker images for `api`, `workers`, `messaging` services once per workflow run.
4.  Pushes built images to a `staging`-specific Google Artifact Registry.
5.  Executes Alembic database migrations against the `staging` database.
6.  Deploys the services to distinct `staging` Google Cloud Run instances.
7.  Requires manual approval in GitHub Actions to promote the same build to the `production` environment.
8.  Upon approval, promotes/copies/retags Docker images to a `production`-specific Google Artifact Registry.
9.  Executes Alembic database migrations against the `production` database.
10. Deploys the services to distinct `production` Google Cloud Run instances.
11. Securely manages GCP credentials and environment-specific variables.

## Implementation Notes

**General:**
* Utilize GitHub Environments for `staging` and `production` to manage approvals and environment-specific secrets/variables for the deployment workflow.
* Employ separate GCP Service Accounts for `staging` and `production` environments to adhere to the principle of least privilege. Store their keys as GitHub Secrets.
* Ensure Docker images are tagged with the Git commit SHA to ensure traceability and allow for easy rollback if necessary.
* Database migration scripts should be idempotent and backward compatible where possible to prevent issues during deployment or rollback.
* Workflows should be designed for clarity, maintainability, and scalability.
* Consider using reusable GitHub Actions workflows if parts of the logic are common.
* Ensure robust error handling and logging throughout the workflows.
* The `production` deployment step must use the *exact same* Docker images that were successfully deployed to `staging`.

**Branch Protection and Workflow Dependencies:**
* The `main` branch **must** be protected by branch protection rules.
* These rules **must** require the successful completion of the jobs in `ci-checks.yml` (e.g., `lint_and_test` job) before a pull request can be merged into `main`, and for any direct pushes to `main` (if allowed).
* This ensures that the `deploy.yml` workflow, which triggers on pushes to `main`, only runs on code that has already passed all necessary quality checks.

**For CI Checks Workflow (`ci-checks.yml`):**
* This workflow should not require GCP credentials.
* Ensure it runs efficiently and provides quick feedback to developers.
* Consider caching dependencies to speed up execution times.
* If using multiple linting/testing tools, ensure they can run in parallel if appropriate or sequentially if necessary.
* The job names within this workflow (e.g., `lint_and_test`) will be referenced in the branch protection rules.

## Context

### Beginning Context

-   `apps/backend/src/a2a_platform/api/Dockerfile` (potentially non-existent or to be created/updated)
-   `apps/backend/src/a2a_platform/workers/Dockerfile` (potentially non-existent or to be created/updated)
-   `apps/backend/src/a2a_platform/messaging/Dockerfile` (potentially non-existent or to be created/updated)
-   `apps/backend/alembic/` (directory containing Alembic migration scripts and configuration)
-   `pyproject.toml` or `requirements.txt` (defining Python dependencies, linting tools, and test runner like pytest)
-   Unit tests exist within the service directories (e.g., `apps/backend/src/a2a_platform/api/tests/`)
-   `.github/workflows/` (directory where the new workflow files will be created)
-   GitHub repository settings for branch protection on `main` (to be configured).

### Ending Context

-   `.github/workflows/deploy.yml` (created for CD)
-   `.github/workflows/ci-checks.yml` (created for CI linting and testing)
-   `apps/backend/src/a2a_platform/api/Dockerfile` (exists and is functional)
-   `apps/backend/src/a2a_platform/workers/Dockerfile` (exists and is functional)
-   `apps/backend/src/a2a_platform/messaging/Dockerfile` (exists and is functional)
-   GitHub Secrets configured for GCP credentials and other sensitive environment variables (for `deploy.yml`).
-   GitHub Environments (`staging`, `production`) configured with protection rules (manual approval for `production`).
-   **GitHub branch protection rules for `main` are configured to require status checks from `ci-checks.yml` to pass.**
-   Google Artifact Registries created for `staging` and `production`.
-   Google Cloud Run services configured for `api-staging`, `workers-staging`, `messaging-staging`, `api-production`, `workers-production`, `messaging-production`.
-   Linting and testing tools are configured and runnable from the command line.

## Low-Level Tasks
> Ordered from start to finish

### A. CI Checks Workflow Setup (`.github/workflows/ci-checks.yml`)

1.  **Create CI Workflow File (`.github/workflows/ci-checks.yml`):**
    * Define workflow name (e.g., `CI Checks`).
    * Define triggers:
        ```yaml
        name: CI Checks
        on:
          push:
            branches:
              - main # Run on pushes to main to update commit status
              - '**' # Run on pushes to all branches
          pull_request:
            branches:
              - main # Run on PRs targeting main
        ```
    * Define a job, e.g., `lint_and_test`.

2.  **Job: `lint_and_test`:**
    * Runs on `ubuntu-latest`.
    * **Checkout Code:** Use `actions/checkout@vX`.
    * **Setup Python:** Use `actions/setup-python@vX` with the project's Python version.
    * **Install Dependencies:**
        * `pip install .[dev]`
        * Consider `actions/cache@vX` for dependencies.
    * **Run Linters:**
        * Example: `flake8 apps/backend/src/a2a_platform/`
        * Example: `black --check apps/backend/src/a2a_platform/`
        * Example: `ruff check apps/backend/src/a2a_platform/`
        * Fail job if linters find issues.
    * **Run Unit Tests:**
        * Example: `pytest apps/backend/src/a2a_platform/` (ensure pytest is configured to find tests in `api`, `workers`, `messaging` subdirectories).
        * Fail job if tests fail.

### B. CI/CD Deployment Workflow Setup (`.github/workflows/deploy.yml`)

1.  **Project Setup & Configuration (Repository and Deployment Focused):**
    * **Configure Branch Protection Rules for `main`:**
        * In GitHub repository settings, protect the `main` branch.
        * Require status checks to pass before merging. Add the `lint_and_test` job (or whatever name is used in `ci-checks.yml`) from the "CI Checks" workflow as a required status check.
        * Consider other protections like requiring PR reviews, disallowing force pushes, etc.
    * Define environment GitHub Secrets for `GCP_SA_KEY`, `GCP_PROJECT_ID`, `DB_PROXY_URL`, `DB_APP_PROXY_URL`, etc.
    * Set up `staging` and `production` environments in GitHub repository settings, including protection rules for `production` requiring manual approval.
    * Ensure Google Artifact Registries exist for both `staging` and `production` GCP projects.
    * Ensure distinct Google Cloud Run services are pre-configured (or the workflow has permissions to create them) for `api`, `workers`, and `messaging` in both `staging` and `production` environments.
    * Verify/Create `Dockerfile` for the `api` service (`apps/backend/src/a2a_platform/api/Dockerfile`).
    * Verify/Create `Dockerfile` for the `workers` service (`apps/backend/src/a2a_platform/workers/Dockerfile`).
    * Verify/Create `Dockerfile` for the `messaging` service (`apps/backend/src/a2a_platform/messaging/Dockerfile`).

2.  **Create Deployment Workflow File (`.github/workflows/deploy.yml`):**
    * Define workflow name (e.g., `Deploy to GCP`).
    * Define the workflow trigger:
      ```yaml
      name: Deploy to GCP
      on:
        push:
          branches:
            - main # Triggers only after merge to main (which would have passed CI checks)
      ```
    * Define jobs: `build_and_deploy_staging` and `deploy_production`.

3.  **Job: `build_and_deploy_staging`:**
    * Runs on `ubuntu-latest`.
    * Note: Execution of this job implies that the commit on `main` has already passed the required CI checks (`lint_and_test`) due to branch protection rules.
    * Define environment variables for `staging` (e.g., `GCP_PROJECT_ID`, `GCP_SA_KEY_SECRET_NAME`, `ARTIFACT_REGISTRY_URL_STAGING`, `CLOUD_RUN_REGION`, etc.).
    * **Checkout Code:** Use `actions/checkout@vX`.
    * **Authenticate to GCP (Staging):** Use `google-github-actions/auth@vX` with the staging service account.
    * **Setup gcloud CLI:** Use `google-github-actions/setup-gcloud@vX`.
    * **Configure Docker for Artifact Registry (Staging):** `gcloud auth configure-docker <staging_artifact_registry_host>`.
    * **Build Docker Images:** (details as previously specified)
    * **Push Docker Images to Staging Artifact Registry:** (details as previously specified)
    * **Run Database Migrations (Staging):** (details as previously specified)
    * **Deploy to Google Cloud Run (Staging):** (details as previously specified)
    * **Upload Build Artifacts (Image Tags):** (details as previously specified)

4.  **Job: `deploy_production`:**
    * Needs `build_and_deploy_staging` to complete successfully (`needs: build_and_deploy_staging`).
    * Set up environment: `environment: name: production url: <production_app_url>`.
    * (Rest of the details for `deploy_production` remain as previously specified)

### C. Unit Tests & Verification (Conceptual)

* **CI Checks Workflow Tests (`ci-checks.yml`):**
    * Push to a feature branch; verify `ci-checks.yml` runs and its status is reported.
    * Create a PR to `main` with linting errors; verify `ci-checks.yml` fails and merging is blocked (if branch protection is active).
    * Create a PR to `main` with test failures; verify `ci-checks.yml` fails and merging is blocked.
    * Create a PR to `main` with passing code; verify `ci-checks.yml` passes and merging is allowed (pending other PR requirements).
    * Push directly to `main` (if allowed by temporary rule suspension for testing); verify `ci-checks.yml` runs.
* **Deployment Workflow Tests (`deploy.yml`):**
    * **Prerequisite Check:** Attempt to merge a PR to `main` that has *not* passed `ci-checks.yml`; verify merge is blocked by branch protection.
    * Merge a PR to `main` that *has* passed `ci-checks.yml`; verify `deploy.yml` triggers.
    * (Rest of the deployment workflow tests remain as previously defined: staging deployment, manual approval, production deployment, failure handling, secret handling).

This updated specification now clearly outlines the dependency through branch protection rules, ensuring that code deployed to `main` (and subsequently to staging/production) has met the quality gates defined in the `ci-checks.yml` workflow.