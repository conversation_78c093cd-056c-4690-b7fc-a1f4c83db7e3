# QA Test Specification: US5.5 - Internal Task Management for Personal Assistant

**User Story:** As the Personal Assistant, I want to manage internal tasks associated with user objectives or my own operational needs in the `tasks` table. This system should allow me to break down work into manageable units, including sub-tasks, track progress internally, ensure resilient execution through mechanisms like timeouts, retries, and leasing, effectively handle situations requiring human input or escalation, and ensure that task creation is idempotent.

## 1. Test Objectives (Derived from High & Mid-Level Objectives of US5.5)

* **Overall Test Objective:** To verify that the Personal Assistant (PA) can autonomously create, manage, track, and ensure the resilient, interactive, and hierarchical execution of internal tasks within the `tasks` table, meeting all functional and non-functional requirements outlined in US5.5.
* **Specific Test Objectives:**
    * Validate idempotent and hierarchical task (and sub-task) creation, including correct record details (description, status, assistant linkage, parent task, idempotency key).
    * Verify accurate task status lifecycle management, ensuring tasks transition through all defined states correctly (e.g., 'todo', 'in_progress', 'done', 'retrying', 'pending_dependency', 'pending_human_input', 'escalated', 'cancelled', 'failed_*').
    * Confirm that internal tasks can be optionally and correctly linked to `assistant_objective` records.
    * Ensure the system can detect unresponsive or failing tasks and effectively supports automated retries with backoff.
    * Validate that persistently failing tasks are correctly transitioned to terminal failure/quarantined states or escalated as appropriate.
    * Test the complete human input workflow:
        * Task transition to 'pending_human_input' with correct context storage.
        * Correct ingestion and recording of human input.
        * Successful resumption of task processing after input.
        * Correct handling of timeouts and escalations for human input.
    * Verify simple sequential task dependency management (task execution blocked until prerequisite is 'done').
    * If applicable, test task leasing mechanisms for exclusive processing and recovery from worker failure.
    * Ensure all API interactions (`POST /internal/assistants/my/tasks`, `PUT /internal/tasks/{task_id}`, `DELETE /internal/tasks/{task_id}`) function as specified, including request/response schemas and status codes.
    * Verify correct handling and storage of `metadata` (as `metadata_json` in DB) across all operations, including field conversions.
    * Validate data integrity in the `tasks` table according to the defined schema and constraints.
    * Test NFRs: Idempotency, Data Integrity, Robustness, Stuck Task Detection & Escalation, Resource Management, Observability, Clarity of Human Input Requests, Hierarchical Integrity.

## 2. Test Scope

### 2.1. In Scope:

* **API Testing:** All CRUD operations for internal tasks via specified internal APIs:
    * `POST /internal/assistants/my/tasks` (Create)
    * `PUT /internal/tasks/{task_id}` (Update)
    * `DELETE /internal/tasks/{task_id}` (Cancel)
* **Functional Testing:**
    * Idempotent task creation (with and without `X-Idempotency-Key`).
    * Hierarchical task creation (`parent_task_id`).
    * Simple sequential dependencies (`depends_on_task_id`).
    * Task linking to objectives (`objective_id`).
    * Comprehensive task status lifecycle transitions (all specified statuses).
    * Retry mechanisms (incrementing `retry_count`, status changes, backoff if observable).
    * Maximum retry handling and transition to 'failed_max_retries' or 'quarantined'.
    * Task heartbeating (`last_progress_at` updates) and timeout detection (soft timeouts via `expected_duration_seconds` leading to 'failed_timeout' or 'escalated' if no progress).
    * Human input workflow:
        * Identification and pause ('pending_human_input', context storage in `metadata`).
        * Input ingestion and task resumption.
        * Timeouts for human input ('failed_human_timeout', 'escalated').
    * Task escalation mechanism ('escalated' status).
    * Task cancellation (status to 'cancelled').
    * Metadata handling (`metadata_json` storage, API schema mapping for `metadata`).
* **Data Validation:**
    * Verification of `tasks` table schema constraints (NOT NULL, FKs, CHECK constraints, UNIQUE constraints like `(assistant_id, idempotency_key)`).
    * Correct population of all relevant fields (`id`, `assistant_id`, `objective_id`, `parent_task_id`, `depends_on_task_id`, `idempotency_key`, `description`, `status`, timestamps, `retry_count`, `last_progress_at`, `lease_*` fields, `metadata`).
* **Resilience Testing (Conceptual - may require specific test harnesses):**
    * Task leasing behavior (if distributed workers): lease acquisition, exclusivity, expiry, and recovery (simulating worker failures).
    * Behavior during simulated transient failures (e.g., database connection drops during an update, then recovers).
* **Negative Testing:**
    * Invalid API requests (missing required fields, incorrect data types, invalid UUIDs for FKs).
    * Attempts to create tasks with non-existent `assistant_id`, `objective_id`, `parent_task_id`, `depends_on_task_id`.
    * Violating idempotency rules (e.g., trying to reuse an idempotency key for a different task payload if the first is still active).
    * Invalid status transitions.
* **Edge Case Testing:**
    * Very long descriptions or metadata content.
    * Tasks with `expected_duration_seconds` set to 0 or a very small/large value.
    * Maxing out `retry_count`.
    * Circular dependencies (if not explicitly prevented by design, test system behavior).
    * Rapid, concurrent API calls (especially for creation with idempotency keys and updates).
    * Lease expiration exactly at the moment of a heartbeat.
    * Human input provided just before/after timeout.
    * Tasks created with `initial_status` directly as 'pending_dependency' or 'pending_human_input'.

### 2.2. Out of Scope (for this specific test plan, though related to the feature):

* UI for direct user management of internal tasks.
* Complex dependency graph management (beyond simple `depends_on_task_id`).
* Dynamic task prioritization logic (beyond creation order or worker availability).
* Detailed implementation of the PA's AI/LLM logic for task decomposition, dependency identification, human input needs, and processing responses (we test the *system's* response to these *decisions* via API calls).
* Detailed UI/Notification systems for human input or escalations (we test the *task status changes* and *metadata* updates that would *trigger* such systems).
* Watchdog process implementation (we test the *effects* a watchdog *would have*, e.g., changing status on timeout, but not the watchdog itself unless its actions are exposed via API or DB changes).
* Performance/Load testing (unless specified as a separate NFR test cycle).
* Security testing beyond basic API input validation (e.g., penetration testing, authZ testing beyond PA's own tasks).

## 3. Test Strategy

* **Primary Approach:** API-level testing using tools like Postman, pytest with `requests`, or a similar HTTP client library.
* **Data-Driven Testing:** Utilize varied datasets to cover different scenarios, especially for status transitions and metadata.
* **State Transition Testing:** Map out all valid and invalid task status transitions and design tests to cover them.
* **Scenario-Based Testing:** Combine multiple functionalities into end-to-end scenarios (e.g., create task -> it becomes dependent -> prerequisite completes -> task runs -> needs human input -> input provided -> task completes).
* **Database Validation:** Directly query the `tasks` table to verify data integrity, record creation, updates, and deletions (soft delete to 'cancelled').
* **Collaboration:** Work with developers to understand potential failure points and to simulate specific conditions (e.g., worker failure for leasing tests, AI/LLM decision simulation).

## 4. Test Environment & Setup

* **Backend API:** Deployed instance of the VEDAVIVI backend with the internal task management features.
* **Database:** Accessible PostgreSQL instance with the `tasks` table and related tables (`assistants`, `assistant_objectives`).
* **Authentication:** Mechanism to obtain valid authentication tokens for the PA to make internal API calls.
* **Test Data:**
    * Pre-existing `assistant` record(s).
    * Pre-existing `assistant_objective` record(s) for linking.
    * UUIDs for `X-Idempotency-Key` generation.
* **Tools:**
    * API testing tool (e.g., Postman, Insomnia).
    * Scripting environment for automated API tests (e.g., Python with `pytest` and `requests`).
    * Database client (e.g., pgAdmin, DBeaver, psql).

## 5. Test Cases & Scenarios

*(Note: This is a representative list. Each AC and many implementation details would spawn multiple specific test cases: positive, negative, edge. `TC_US5.5_XXX` denotes Test Case ID.)*

### 5.1. Hierarchical Task Creation & Idempotency (AC1, NFR1)

* **TC_US5.5_001 (Positive - Simple Create):**
    * Action: `POST /internal/assistants/my/tasks` with valid `description`.
    * Expected: 201 Created. New task record in `tasks` with `status='todo'`, correct `assistant_id`, `description`, `created_at`, `updated_at`. `idempotency_key` is NULL.
* **TC_US5.5_002 (Positive - Create with Parent):**
    * Setup: Create a parent task.
    * Action: `POST /internal/assistants/my/tasks` with `description` and valid `parent_task_id`.
    * Expected: 201 Created. New task linked to `parent_task_id`.
* **TC_US5.5_003 (Positive - Create with Objective):**
    * Setup: Create an objective.
    * Action: `POST /internal/assistants/my/tasks` with `description` and valid `objective_id`.
    * Expected: 201 Created. New task linked to `objective_id`.
* **TC_US5.5_004 (Positive - Idempotent Create - First Call):**
    * Action: `POST /internal/assistants/my/tasks` with `description` and a new `X-Idempotency-Key`.
    * Expected: 201 Created. New task record, `idempotency_key` field populated.
* **TC_US5.5_005 (Positive - Idempotent Create - Second Call, Same Key, Active Task):**
    * Setup: TC_US5.5_004 executed.
    * Action: Repeat `POST /internal/assistants/my/tasks` with the *exact same payload and `X-Idempotency-Key`* while the task is in a non-terminal state (e.g. 'todo', 'in_progress').
    * Expected: 200 OK. Returns details of the *existing* task created in TC_US5.5_004. No new task created.
* **TC_US5.5_006 (Negative - Idempotent Create - Second Call, Same Key, Different Payload, Active Task):**
    * Setup: TC_US5.5_004 executed.
    * Action: Repeat `POST /internal/assistants/my/tasks` with the same `X-Idempotency-Key` but *different* description (or other fields) while the task is active.
    * Expected: Behavior should be clearly defined. Typically, this might be a 4xx error (e.g., 409 Conflict if the key is tied to the original payload, or it might still return the original task ignoring payload differences as per spec "existing task details are returned"). Verify against spec. *Spec says "existing task details are returned", so expect 200 OK and original task.*
* **TC_US5.5_007 (Positive/Edge - Idempotent Create - Key for Terminal Task):**
    * Setup: Create a task with an idempotency key, then update its status to 'done' or 'cancelled'.
    * Action: `POST /internal/assistants/my/tasks` using the *same* `X-Idempotency-Key` again.
    * Expected: *As per spec: "If a key for a task ... has been seen recently for a task not yet in a terminal state...". This implies if the task IS in a terminal state, a new task MIGHT be created. This needs clarification or specific testing of the "recently" and "terminal state" logic. If not clearly defined, this is an edge case to explore. Assuming it should create a new task if the previous one is terminal.* Expected: 201 Created, new task.
* **TC_US5.5_008 (Negative - Create - Missing Description):**
    * Action: `POST /internal/assistants/my/tasks` without `description`.
    * Expected: 422 Unprocessable Entity (or similar validation error).
* **TC_US5.5_009 (Negative - Create - Invalid `parent_task_id`):**
    * Action: `POST /internal/assistants/my/tasks` with a non-existent UUID for `parent_task_id`.
    * Expected: 4xx error (e.g., 404 if checked, or 422 due to FK constraint if DB catches it, or specific business logic error).
* **TC_US5.5_010 (Positive - Create with Initial Status):**
    * Action: `POST /internal/assistants/my/tasks` with `description` and `initial_status: 'pending_dependency'`.
    * Expected: 201 Created. Task status is 'pending_dependency'.
* **TC_US5.5_011 (Negative - Create - Invalid Initial Status):**
    * Action: `POST /internal/assistants/my/tasks` with `initial_status: 'invalid_status_value'`.
    * Expected: 422 Unprocessable Entity.

### 5.2. Task Status & Lifecycle Management (AC2)

* **TC_US5.5_020 (Positive - Update Status):**
    * Setup: Create a task.
    * Action: `PUT /internal/tasks/{task_id}` with `status: 'in_progress'`.
    * Expected: 200 OK. Task `status` and `updated_at` updated in DB. `last_progress_at` might also be updated.
* **TC_US5.5_021 (Positive - Update Description):**
    * Setup: Create a task.
    * Action: `PUT /internal/tasks/{task_id}` with a new `description`.
    * Expected: 200 OK. Task `description` and `updated_at` updated.
* **TC_US5.5_022 (Positive - Update Metadata):**
    * Setup: Create a task.
    * Action: `PUT /internal/tasks/{task_id}` with updated `metadata` (e.g., adding `{"custom_key": "value"}`).
    * Expected: 200 OK. `metadata` field in DB is updated. Verify `metadata_json` to `metadata` mapping.
* **TC_US5.5_023 (Negative - Update - Invalid Status):**
    * Setup: Create a task.
    * Action: `PUT /internal/tasks/{task_id}` with `status: 'invalid_status_here'`.
    * Expected: 422 Unprocessable Entity (due to CHECK constraint or Pydantic validation).
* **TC_US5.5_024 (Negative - Update - Non-existent Task ID):**
    * Action: `PUT /internal/tasks/{non_existent_uuid}` with valid payload.
    * Expected: 404 Not Found.
* **TC_US5.5_025 (Positive - Cancel Task - DELETE):**
    * Setup: Create a task.
    * Action: `DELETE /internal/tasks/{task_id}`.
    * Expected: 200 OK or 204 No Content. Task status in DB is 'cancelled', `updated_at` changed. (AC16)

### 5.3. Stuck/Failed Task Detection & Recovery (AC3, AC4, AC6, NFR4)

* **TC_US5.5_030 (Positive - Retry):**
    * Setup: Create a task. Simulate a scenario where the PA logic decides to retry.
    * Action: `PUT /internal/tasks/{task_id}` with `status: 'retrying'`. (The system should ideally auto-set this, but API allows manual setting for testing).
    * Expected: 200 OK. Task status is 'retrying'. `retry_count` should be incremented by the worker logic (this test focuses on API setting status, worker behavior is implicit). Verify `updated_at`.
    * Follow-up: If a worker picks it up, it might transition to `in_progress`.
* **TC_US5.5_031 (Positive - Max Retries):**
    * Setup: Create a task. Manually update `retry_count` (via DB or API if possible, otherwise simulate worker behavior) to `metadata.max_retries` (or default if not set). Simulate another failure.
    * Action: Worker/system transitions task.
    * Expected: Task status becomes 'failed_max_retries' or 'quarantined'. (AC4)
* **TC_US5.5_032 (Positive - Heartbeat/Progress Update):**
    * Setup: Create a task, set status to 'in_progress'.
    * Action: `PUT /internal/tasks/{task_id}` with `last_progress_at: (current_timestamp)`.
    * Expected: 200 OK. `last_progress_at` and `updated_at` are updated. (AC5)
* **TC_US5.5_033 (Conceptual - Timeout):**
    * Setup: Create a task with `metadata: {"expected_duration_seconds": 5}`. Set status to `in_progress`. Do NOT update `last_progress_at`.
    * Action: Wait for > 5 seconds. (This requires a watchdog or similar mechanism as per spec).
    * Expected: Task status eventually changes to 'failed_timeout' or 'escalated'. (AC6) This may need manual verification or a test harness.
* **TC_US5.5_034 (Positive - Escalation):**
    * Setup: Create a task. Simulate a condition for escalation (e.g., critical failure).
    * Action: `PUT /internal/tasks/{task_id}` with `status: 'escalated'`.
    * Expected: 200 OK. Task status is 'escalated'. (AC14)

### 5.4. Human Input Workflow Support (AC8, AC9, AC10, NFR7)

* **TC_US5.5_040 (Positive - Request Human Input):**
    * Setup: Create a task.
    * Action: `PUT /internal/tasks/{task_id}` with `status: 'pending_human_input'` and `metadata: {"human_input_request": {"query": "Need clarification on X"}}`.
    * Expected: 200 OK. Status is 'pending_human_input'. `metadata` stores the request details. (AC8)
* **TC_US5.5_041 (Positive - Provide Human Input):**
    * Setup: TC_US5.5_040 executed.
    * Action: `PUT /internal/tasks/{task_id}` with `status: 'todo'` (or 'in_progress') and `metadata: {"human_input_request": {"query": "Need clarification on X"}, "human_input_response": {"answer": "Clarification Y"}}`.
    * Expected: 200 OK. Status updated (e.g., to 'todo' to be re-picked). `metadata` stores the response. (AC9)
* **TC_US5.5_042 (Conceptual - Human Input Timeout):**
    * Setup: Task in 'pending_human_input'. `metadata` contains `human_input_request`. No input provided for a configured timeout period.
    * Action: Watchdog/system acts.
    * Expected: Task status changes to 'failed_human_timeout' or 'escalated'. (AC10) This may need manual verification or a test harness.
* **TC_US5.5_043 (Edge - Human Input Metadata Structure):**
    * Test with various valid and invalid structures within `human_input_request` and `human_input_response` in `metadata`.
    * Expected: Graceful handling of valid structures, validation errors for malformed ones.

### 5.5. Task Dependency Management (Simple Sequential) (AC11, AC12)

* **TC_US5.5_050 (Positive - Dependent Task Creation):**
    * Setup: Create prerequisite_task_1.
    * Action: `POST /internal/assistants/my/tasks` for dependent_task_2 with `depends_on_task_id: prerequisite_task_1.id` and `initial_status: 'pending_dependency'`.
    * Expected: 201 Created. dependent_task_2 status is 'pending_dependency'.
* **TC_US5.5_051 (Positive - Dependent Task Waits):**
    * Setup: TC_US5.5_050. prerequisite_task_1 is in 'todo' or 'in_progress'.
    * Action: Observe dependent_task_2. (Simulate worker trying to pick it up).
    * Expected: dependent_task_2 remains in 'pending_dependency' and is not processed. (AC11)
* **TC_US5.5_052 (Positive - Prerequisite Completes, Dependent Unblocks):**
    * Setup: TC_US5.5_050.
    * Action: Update prerequisite_task_1 status to 'done' via `PUT /internal/tasks/{prerequisite_task_1.id}`. (A system mechanism should then update the dependent task).
    * Expected: dependent_task_2 status transitions from 'pending_dependency' to 'todo' (or as per system design). `updated_at` changes. (AC12)
* **TC_US5.5_053 (Edge - Prerequisite Fails/Cancelled):**
    * Setup: TC_US5.5_050.
    * Action: Update prerequisite_task_1 status to 'failed_max_retries' or 'cancelled'.
    * Expected: Behavior of dependent_task_2 should be defined. Does it also fail? Stay pending indefinitely? Escalate? Test this. (If not defined, flag as ambiguity). *Based on `ON DELETE SET NULL` for `depends_on_task_id`, if the prerequisite is deleted, the FK becomes NULL. If it's just failed, the dependent task would remain 'pending_dependency' unless logic handles this.*

### 5.6. Task Leasing (AC7) - *If leasing is actively used and observable via API/DB*

* **TC_US5.5_060 (Positive - Lease Acquired):**
    * Setup: Create a task. Simulate a worker acquiring a lease.
    * Action: (If API allows) `PUT /internal/tasks/{task_id}` with `status: 'leased'`, `lease_owner_id: 'worker_1'`, `lease_expires_at: (future_time)`.
    * (If not API driven) Worker action.
    * Expected: Task record in DB shows `status='leased'`, `lease_owner_id`, `lease_acquired_at`, `lease_expires_at` populated. (AC7)
* **TC_US5.5_061 (Conceptual - Lease Expiry & Recovery):**
    * Setup: TC_US5.5_060. Worker 'worker_1' does not heartbeat or complete. `lease_expires_at` passes.
    * Action: Watchdog/system or another worker attempts to acquire the lease.
    * Expected: Task lease becomes available. Another worker can acquire it, or status changes (e.g., to 'retrying' or 'failed_timeout', then 'escalated'). (AC6)
* **TC_US5.5_062 (Conceptual - Lease Exclusivity):**
    * Setup: Task is leased by 'worker_1'.
    * Action: Simulate 'worker_2' attempting to acquire the lease *before* it expires.
    * Expected: 'worker_2' fails to acquire the lease.

### 5.7. Parent Task Logic (AC13)

* **TC_US5.5_070 (Positive - Sub-task Completion Event):**
    * Setup: Create parent_task. Create child_task with `parent_task_id: parent_task.id`.
    * Action: Update child_task status to 'done'.
    * Expected: This event is "available for the parent task's logic". For testing, this might mean parent_task's `updated_at` changes if it polls, or a specific field in parent_task `metadata` gets updated, or a separate event is logged. *This AC is about the *availability* of the event, not necessarily an automatic parent status change. Clarify how this availability is observed for testing.*

### 5.8. Invalid Data and Graceful Failure (AC15)

* **TC_US5.5_080 (Negative - Create - Invalid `objective_id` format):**
    * Action: `POST /internal/assistants/my/tasks` with `objective_id: 'not-a-uuid'`.
    * Expected: 422 Unprocessable Entity.
* **TC_US5.5_081 (Negative - Update - Invalid `metadata` structure):**
    * Action: `PUT /internal/tasks/{task_id}` with `metadata: "not-an-object"`.
    * Expected: 422 Unprocessable Entity.
* **TC_US5.5_082 (Negative - Update - Attempt to set invalid `status` value):**
    * Action: `PUT /internal/tasks/{task_id}` with `status: 'super_done_custom_status'`.
    * Expected: 422 Unprocessable Entity (due to CHECK constraint on `status` column).
* **TC_US5.5_083 (Negative - Create - `idempotency_key` uniqueness violation):**
    * Setup: Create task_A by assistant_1 with `idempotency_key: 'key123'`.
    * Action: Attempt to create task_B by *the same* assistant_1 with the *same* `idempotency_key: 'key123'` but *different description*, while task_A is active. (This differs from TC_US5.5_006 by intent to create a *new distinct* task with a colliding active key).
    * Expected: As per spec, should return 200 OK and the *original* task_A. If a 409 or other error is expected due to payload mismatch, clarify from dev. *Spec: "If a key for a task from a specific assistant has been seen recently for a task not yet in a terminal state, the existing task details are returned without creating a new one."* So, 200 OK and task_A details.
* **TC_US5.5_084 (Positive - `idempotency_key` uniqueness with different assistant):**
    * Setup: Create task_A by assistant_1 with `idempotency_key: 'key123'`.
    * Action: Attempt to create task_C by *different* assistant_2 with the *same* `idempotency_key: 'key123'`.
    * Expected: 201 Created. New task_C created for assistant_2. The `UNIQUE (assistant_id, idempotency_key)` constraint allows this.

### 5.9. Non-Functional Requirements Testing (NFR1-NFR8)

* **NFR1 (Idempotency):** Covered by TC_US5.5_004, 005, 006, 007, 083, 084. Also, task execution logic should be tested for idempotency if observable (e.g., running a 'done' task again has no side effects).
* **NFR2 (Data Integrity):**
    * Test all `NOT NULL` constraints by attempting to create/update with NULL values.
    * Test FK constraints by using non-existent foreign keys.
    * Test `CHECK (status IN (...))` by trying invalid status values.
    * Test `DEFAULT` values by creating records without specifying those fields.
* **NFR3 (Robustness):**
    * Send malformed JSON in request bodies.
    * Send oversized payloads (e.g., very long descriptions or metadata).
    * Simulate network interruptions during API calls (if possible with test tools).
* **NFR4 (Stuck Task Detection & Escalation):** Covered by TC_US5.5_033, 034, 042.
* **NFR5 (Resource Management):** Primarily observational during other tests – ensure no runaway processes or excessive DB connections from stuck tasks. May require specific monitoring.
* **NFR6 (Observability):** Verify that key metrics (if defined and accessible, e.g., number of tasks in each status) are available/updated. This might be via admin endpoints or DB queries.
* **NFR7 (Clarity of Human Input Requests):** When a task moves to `pending_human_input`, ensure `metadata.human_input_request` contains all necessary context as defined by PA logic (this is more about what the PA *puts* there, QA verifies it's stored and retrieved).
* **NFR8 (Hierarchical Integrity):**
    * Attempt to create a task making itself its own parent (`parent_task_id = id`). This should be prevented by DB constraints or business logic.
    * If a parent task is deleted, verify `ON DELETE CASCADE` behavior for child tasks (they should also be deleted).
    * If a task a task `depends_on_task_id` is deleted, verify `ON DELETE SET NULL` (the `depends_on_task_id` field becomes NULL).

## 6. Test Data Requirements

* Valid `assistant_id`(s) from `assistants` table.
* Valid `objective_id`(s) from `assistant_objectives` table.
* Valid `task_id`(s) for `parent_task_id` and `depends_on_task_id` references.
* Sets of data for creating tasks:
    * Minimal valid data.
    * Fully populated valid data.
    * Data with various `initial_status` values.
    * Data for idempotency tests (reusable keys).
* Data for updates:
    * All valid status transitions.
    * Varied `metadata` content (simple, complex, empty, with human input requests/responses).
* Invalid data:
    * NULLs for NOT NULL fields.
    * Non-existent FKs.
    * Invalid data types (e.g., string for integer).
    * Out-of-range values (if applicable).
    * Invalid `status` enum values.

## 7. Entry Criteria

* Backend code for US5.5 deployed to the test environment.
* All dependencies (D1-D6 from spec) are available and functional in the test environment (e.g., `tasks` table created, auth works, dependent APIs/services are up).
* Test environment is stable and accessible.
* This QA Test Specification is reviewed and approved.
* Test data is prepared and available.

## 8. Exit Criteria

* All defined test cases executed.
* A defined percentage of test cases passed (e.g., 100% of critical path, 95% of major functionality).
* No outstanding critical or high-severity defects related to US5.5.
* All found defects are logged, tracked, and either fixed and retested, or deferred with justification.
* Test execution report and summary report are generated and shared.

## 9. Risks & Contingencies (for Testing Process)

* **Risk 1:** Test environment instability or unavailability.
    * **Contingency:** Communicate with DevOps/Dev team. Schedule testing around maintenance. Have a backup plan for local testing if partial.
* **Risk 2:** Dependencies (AI/LLM logic, notification systems) not fully implemented or testable, impacting end-to-end scenarios.
    * **Contingency:** Use stubs or mocks for external/AI dependencies where possible. Focus on testing the contract and the system's reaction to simulated inputs from these dependencies.
* **Risk 3:** Ambiguity in specifications leading to incorrect test case design.
    * **Contingency:** Regular clarification sessions with Product Owner/Developers. Document all assumptions.
* **Risk 4:** Insufficient time for thorough testing of all edge cases.
    * **Contingency:** Prioritize test cases based on risk and impact. Communicate limitations early.
* **Risk 5:** Difficulty in simulating specific scenarios (e.g., distributed worker failure, exact lease expiry race conditions).
    * **Contingency:** Work with developers to create test hooks or specific debug endpoints to force these states if crucial. Otherwise, rely on code reviews and conceptual validation for these.

## 10. Test Deliverables

* This QA Test Specification document.
* Test Case Suite (e.g., in a test management tool or spreadsheet).
* Defect Reports.
* Test Execution Logs.
* Test Summary Report.

---
