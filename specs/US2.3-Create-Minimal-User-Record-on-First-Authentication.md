# US2.3 Create Minimal User Record on First Authentication
> Ingest the information from this file, implement the Low-Level Tasks, and generate the code that will satisfy the High and Mid-Level Objectives.

## High-Level Objective

- Automatically create a new, minimal user record in the VEDAVIVI database when a new user authenticates successfully via Clerk for the first time, triggered by a Clerk webhook. This allows VEDAVIVI-specific data to be associated with the user's identity.

## Mid-Level Objectives

- Implement a backend webhook handler to securely receive and validate `user.created` events from Clerk.com.
- Develop backend logic to parse the webhook payload and extract necessary information, primarily the `clerk_user_id`.
- Ensure a new record is created in the VEDAVIVI `users` table in the PostgreSQL database, linking to the extracted `clerk_user_id`.
- The user record created should be minimal, containing only essential linking information (like `clerk_user_id`) and default values for other VEDAVIVI-specific fields as defined in the `users` table schema.
- Implement error handling and logging for the webhook ingestion and user creation process.

## Implementation Notes

- **Important Technical Details:**
    - The webhook handler endpoint for Clerk events is specified as `POST /api/v1/webhooks/clerk` (Source 498).
    - The backend must validate incoming Clerk webhooks, potentially using Clerk signatures if available, to ensure authenticity and integrity (Source 401, 498).
    - The `users` table in the VEDAVIVI PostgreSQL database is the target for the new record. The schema includes `id (UUID, PK)`, `clerk_user_id (TEXT, UNIQUE, NOT NULL)`, `timezone (TEXT, NULL)`, `preferences (JSONB, NULL, DEFAULT '{}')`, `created_at (TIMESTAMPTZ, NOT NULL, DEFAULT now())`, and `updated_at (TIMESTAMPTZ, NOT NULL, DEFAULT now())` (Source 468).
    - For a "minimal" user record, the `clerk_user_id` must be populated from the webhook. `timezone` can be initially NULL or set to a default. `preferences` should default to an empty JSONB object (`{}`) as per the schema (Source 468). `id`, `created_at`, and `updated_at` will be handled by the database defaults.
    - The system should handle potential errors gracefully, such as invalid webhook signatures, missing data in the payload, or database insertion failures (e.g., constraint violations if a `clerk_user_id` somehow already exists). Consider idempotency mechanisms, although `user.created` is typically a one-time event (general webhook idempotency mentioned in Source 439, 490).
    - The primary backend language is Python (Source 390).
- **Dependencies and Requirements:**
    - Active Clerk.com account with a configured application.
    - `user.created` webhook must be configured in the Clerk.com dashboard to point to the VEDAVIVI backend endpoint.
    - The VEDAVIVI PostgreSQL database with the `users` table (schema defined in Source 468) must be accessible by the backend application.
    - Backend infrastructure capable of receiving HTTP POST requests for the webhook.
- **Coding Standards to Follow:**
    - Adhere to general backend development best practices and Python-specific coding conventions.
    - Ensure code is modular, with clear separation of concerns (e.g., webhook validation, data parsing, database interaction).
    - Include comprehensive inline documentation and comments where necessary.
- **Other Technical Guidance:**
    - This functionality is a core part of "User Profile Management" (Source 397).
    - The creation of the VEDAVIVI user record is explicitly triggered by the `user.created` webhook from Clerk (Source 399).

## Context

**Beginning Context:**
- VEDAVIVI PostgreSQL database exists with the `users` table schema defined as per Source 468.
- Clerk.com application is set up and configured for user authentication.
- The VEDAVIVI backend application (Python) project structure is in place.
- No specific webhook handler for Clerk `user.created` events exists or is fully implemented in the backend.

**Ending Context:**
- A new backend API endpoint `POST /api/v1/webhooks/clerk` is implemented, deployed, and operational (Source 498).
- This endpoint is configured in the Clerk.com dashboard as the destination for `user.created` webhooks.
- Upon successful first-time authentication of a user via Clerk, the `user.created` webhook triggers the new backend logic.
- The backend logic successfully validates the webhook, extracts the `clerk_user_id`, and creates a corresponding minimal record in the VEDAVIVI `users` table.
- The system includes logging for successful user creations and any errors encountered during the process.

## Low-Level Tasks
> Ordered from start to finish

1.  **Verify `users` Table Schema:**
    * Confirm that the `users` table in the PostgreSQL database matches the schema defined in the "VEDAVIVI: MVP Backend Technical Plan v1.5" (Source 468), specifically ensuring `clerk_user_id` is `TEXT, UNIQUE, NOT NULL`, and `preferences` defaults to `'{}'::jsonb`.
    ```aider
    # No code generation for verification, this is a manual/DB check or migration check.
    # Prompt: "Review the current migration file for the users table and ensure it matches the specification in Source 468, paying attention to clerk_user_id constraints and the default for preferences."
    ```
2.  **Create Clerk Webhook Handler Endpoint:**
    * Implement an HTTP POST endpoint at `/api/v1/webhooks/clerk` in the Python backend.
    * Add basic structure for receiving the request.
    ```aider
    # Assuming a Python framework like FastAPI or Flask
    # Prompt: "Create a Python FastAPI endpoint for POST /api/v1/webhooks/clerk that accepts a request body. Include basic logging for received requests."
    # File to CREATE or UPDATE: (e.g., backend/routers/webhooks.py or backend/main.py)
    # Function to CREATE or UPDATE: (e.g., handle_clerk_webhook)
    ```
3.  **Implement Webhook Signature Validation:**
    * Integrate Clerk's recommended method for webhook signature validation within the endpoint created in Task 2. This is crucial for security.
    * The handler should reject requests with invalid signatures.
    ```aider
    # Prompt: "Implement Clerk webhook signature validation for the /api/v1/webhooks/clerk endpoint. Use the Clerk Python SDK or follow Clerk's documentation for manual signature verification. Log and return an error for invalid signatures."
    # File to UPDATE: (e.g., backend/routers/webhooks.py)
    # Function to UPDATE: (e.g., handle_clerk_webhook)
    # Details: "Ensure to fetch the Clerk signing secret securely from environment variables."
    ```
4.  **Implement Webhook Payload Processing Logic:**
    * Inside the validated webhook handler, parse the JSON payload.
    * Specifically extract the `type` of the event and ensure it is `user.created`.
    * Extract the `data.id` which corresponds to the `clerk_user_id`.
    ```aider
    # Prompt: "In the handle_clerk_webhook function, after signature validation, parse the JSON payload. Check if the event type is 'user.created'. If it is, extract the user ID (clerk_user_id) from the 'data.id' field. Log the extracted clerk_user_id."
    # File to UPDATE: (e.g., backend/routers/webhooks.py)
    # Function to UPDATE: (e.g., handle_clerk_webhook)
    ```
5.  **Implement Database Interaction Logic for User Creation:**
    * Create a new function/service method to handle the insertion of a new user record into the `users` table.
    * This function should take `clerk_user_id` as input.
    * It should insert a new row populating `clerk_user_id`. `timezone` can be set to NULL or a system default. `preferences` should use its database default (`{}`).
    * Handle potential database errors, especially unique constraint violations on `clerk_user_id` (though `user.created` should be unique, defensive coding is good).
    ```aider
    # Prompt: "Create a Python function `create_a2a_user(db_session, clerk_user_id: str)` that inserts a new record into the 'users' table with the given clerk_user_id. Set timezone to NULL initially. Log success or database errors. Call this function from the webhook handler if the event is 'user.created'."
    # File to CREATE or UPDATE: (e.g., backend/services/user_service.py and update backend/routers/webhooks.py)
    # Function to CREATE: create_a2a_user (in user_service.py)
    # Function to UPDATE: handle_clerk_webhook (in webhooks.py to call the new service)
    # Details: "Use SQLAlchemy or preferred ORM/DB library for interaction with PostgreSQL. Ensure created_at and updated_at are handled by DB defaults."
    ```
6.  **Add Comprehensive Error Handling and Logging:**
    * Ensure robust error handling is in place for all stages: signature validation, payload parsing, missing expected data, database operations.
    * Implement detailed logging for successful user creation events and any errors encountered, including relevant details for debugging.
    ```aider
    # Prompt: "Review the handle_clerk_webhook and create_a2a_user functions. Enhance error handling using try-except blocks for specific exceptions (e.g., JSONDecodeError, DB exceptions like IntegrityError). Add detailed logging for both success and failure scenarios, including event type, clerk_user_id (if available), and error messages."
    # File to UPDATE: (e.g., backend/routers/webhooks.py, backend/services/user_service.py)
    # Function to UPDATE: handle_clerk_webhook, create_a2a_user
    ```
7.  **Write Unit and Integration Tests:**
    * Develop unit tests for:
        * Webhook signature validation (mocking Clerk's verification).
        * Payload parsing logic for `user.created` events (valid and malformed).
        * The `create_a2a_user` service function (mocking the database session).
    * Develop integration tests that:
        * Simulate a valid `user.created` webhook call to the endpoint and verify a new user is created in a test database.
        * Test failure scenarios like invalid signature or duplicate `clerk_user_id` insertion attempt.
    ```aider
    # Prompt: "Write unit tests for the Clerk webhook signature validation logic, payload parsing, and the user creation service function using pytest and appropriate mocking. Then, write integration tests for the /api/v1/webhooks/clerk endpoint simulating a 'user.created' event and verifying database changes."
    # File to CREATE or UPDATE: (e.g., backend/tests/test_webhooks.py, backend/tests/test_user_service.py)
    # Function to CREATE or UPDATE: Multiple test functions.
    ```
8.  **Documentation and Clerk Configuration (Manual Steps):**
    * Document the new webhook endpoint and its purpose internally.
    * Manually configure the `user.created` webhook in the Clerk.com dashboard, pointing it to the deployed VEDAVIVI backend endpoint (`https://<your-backend-domain>/api/v1/webhooks/clerk`). Ensure the correct signing secret from Clerk is configured in the backend environment.
    ```aider
    # This is a documentation and manual configuration step, no direct code generation by aider.
    # Prompt: "N/A for code generation. This task involves updating API documentation and performing manual configuration in the Clerk dashboard."
    ```
