# US5.1 Store and Retrieve User Preferences
> Ingest the information from this file, implement the Low-Level Tasks, and generate the code that will satisfy the High and Mid-Level Objectives.
## High-Level Objective

- Enable the Personal Assistant (PA) to store and retrieve user-specific preferences (e.g., favorite color, timezone) to allow for more personalized interactions with the user.

## Mid-Level Objective

- Develo<PERSON> backend logic to identify and extract preferences stated by the user in chat (this part is largely dependent on US1.2 and related preference extraction logic, this spec focuses on the storage/retrieval mechanism once a preference is identified).
- Implement functionality to save general, key-value pair preferences (e.g., "favorite_color": "blue") into a JSONB `preferences` field in the `users` table, associated with the `clerk_user_id`.
- Implement functionality to save specific, commonly accessed, or frequently filtered preferences (e.g., timezone) into dedicated columns (e.g., `timezone` column) in the `users` table, associated with the `clerk_user_id`.
- Develop backend logic for the Personal Assistant to retrieve all stored preferences (both from the JSONB field and specific columns) for a given user when needed for personalization.
- Ensure the `users` table schema in PostgreSQL is updated to support a JSONB `preferences` field and a `timezone` field (e.g., `VARCHAR`).

## Implementation Notes
- **Data Storage:**
    - User identification will be via `clerk_user_id`.
    - The `preferences` field in the `users` table will be of type `JSONB` in PostgreSQL. This allows for flexible storage of arbitrary key-value user preferences. It should default to an empty JSON object `{}`.
    - A dedicated `timezone` column (e.g., `VARCHAR(64)`) will be added to the `users` table for storing the user's preferred timezone. This field can be nullable.
- **Data Integrity & Access:**
    - Input validation for timezone formats (e.g., IANA timezone database names like "America/New_York") should be considered for the `timezone` column.
    - When updating the JSONB `preferences` field, the new data should be merged with existing preferences. For example, if `preferences` is `{"color": "blue", "food": "pizza"}` and the update is `{"food": "sushi", "drink": "tea"}`, the result should be `{"color": "blue", "food": "sushi", "drink": "tea"}`.
- **API Design:**
    - API endpoints (GraphQL mutations/queries are preferred as per the Backend Technical Plan v1.5) will be needed for an authenticated user to update their preferences and for the PA to retrieve them.
    - The `clerk_user_id` will be used to identify the user for these operations, typically derived from the authentication context.
- **Dependencies and Requirements:**
    - Existing `users` table and its SQLAlchemy model.
    - Alembic for database migrations.
    - US1.2 (User states preference in chat) for the upstream preference identification.
    - Backend service/module for processing chat messages and identifying preferences (which will then call the storage logic defined here).
    - Authentication mechanism (Clerk.com) to secure endpoints and identify the `clerk_user_id`.
- **Coding Standards to Follow:**
    - Adhere to existing project coding standards for Python, FastAPI, SQLAlchemy.
    - Code must be clean, modular, well-documented, and include type hints.
    - Implement comprehensive error handling for database operations and preference processing.
    - Write unit tests for service-layer logic and integration tests for API endpoints.
- **Other Technical Guidance:**
    - **Security:** Ensure that preference update mechanisms are only accessible by the authenticated user for their own profile, or by authorized system processes.
    - **Scalability:** The JSONB approach is generally scalable for a moderate number of preferences per user. If specific preferences within the JSONB need to be queried frequently across many users, consider promoting them to dedicated columns in the future.
    - **Structure within JSONB:** For general preferences, a flat key-value structure (e.g., `{"key1": "value1", "key2": "value2"}`) is recommended for simplicity. Avoid deeply nested structures initially unless there's a clear, immediate need.
    - **Atomicity:** Database operations for updating preferences should be atomic.

## Context

### Beginning context
- `apps/backend/src/a2a_platform/db/models/user.py` (Defines the `User` SQLAlchemy model)
- `apps/backend/alembic/versions/` (Contains existing database migration scripts)
- `apps/backend/src/a2a_platform/services/user_service.py` (Contains user-related service logic; to be updated or new preference service created)
- `apps/backend/src/a2a_platform/api/graphql/resolvers/user_profile_resolvers.py` (Contains GraphQL resolvers for user profile; to be updated)
- `apps/backend/src/a2a_platform/api/graphql/schemas/user_profile_schemas.py` (Contains GraphQL schemas for user profile; to be updated)

### Ending context
- Updated `apps/backend/src/a2a_platform/db/models/user.py` (with new `preferences` JSONB and `timezone` String fields in the `User` model).
- A new Alembic migration script in `apps/backend/alembic/versions/` to apply schema changes to the `users` table.
- Updated or new functions in `apps/backend/src/a2a_platform/services/user_service.py` (or a dedicated `preference_service.py`) for creating, reading, and updating user preferences.
- Updated `apps/backend/src/a2a_platform/api/graphql/resolvers/user_profile_resolvers.py` and `apps/backend/src/a2a_platform/api/graphql/schemas/user_profile_schemas.py` to expose preference management via GraphQL.
- New/updated test files (e.g., `apps/backend/tests/unit/services/test_user_service.py`, `apps/backend/tests/integration/test_graphql_user_profile.py`) for the preference functionalities.

## Low-Level Tasks
> Ordered from start to finish

1. **Define/Update the `User` SQLAlchemy model**
    ```aider
    Update the User model in `apps/backend/src/a2a_platform/db/models/user.py` to include a 'preferences' field of type `sqlalchemy.dialects.postgresql.JSONB`, nullable, with a server default of an empty JSON object `'{}'`, and a 'timezone' field of type `sqlalchemy.String(64)`, nullable.
    File: apps/backend/src/a2a_platform/db/models/user.py
    Class: User
    Details: Import JSONB from `sqlalchemy.dialects.postgresql`. Ensure `preferences` uses `server_default=text("'{}'")` and `nullable=True`. `timezone` should be `nullable=True`.
    ```

2. **Create an Alembic migration script**
    ```aider
    Generate an Alembic revision to add the 'preferences' JSONB column (server_default '{}', nullable=True) and a nullable 'timezone' VARCHAR(64) column to the 'users' table.
    File: apps/backend/alembic/versions/<new_migration_file>.py
    Details: The script should use `op.add_column` for both fields. For 'preferences', specify `sa.dialects.postgresql.JSONB(astext_fallback=True)`, `server_default=sa.text("'{}'")`, and `nullable=True`. For 'timezone', use `sa.String(64)` and `nullable=True`.
    ```

3. **Implement service-layer functions to update and retrieve user preferences**
    ```aider
    In `apps/backend/src/a2a_platform/services/user_service.py`, create/update functions:
    1. `update_user_preferences(db: Session, *, clerk_user_id: str, preferences_update: dict[str, Any], timezone_update: Optional[str] = None) -> User`: This function should retrieve the user by `clerk_user_id`. It should merge the `preferences_update` dictionary into the existing `preferences` JSONB field. If `preferences` is None, it should be initialized as an empty dict before merging. If `timezone_update` is provided, it should update the `timezone` column. The function should persist changes and return the updated `User` object.
    2. `get_user_preferences(db: Session, *, clerk_user_id: str) -> Optional[dict[str, Any]]`: This function retrieves the user by `clerk_user_id` and returns a dictionary containing their 'preferences' (JSONB content) and 'timezone'. If the user is not found, return None. If preferences or timezone are null, they should be represented appropriately (e.g., preferences as an empty dict, timezone as None).
    File: apps/backend/src/a2a_platform/services/user_service.py
    Details: For merging JSONB in SQLAlchemy with PostgreSQL, you can load the existing JSON, merge in Python, and then save, or use database-specific functions like `user.preferences.op('||')(preferences_update)` if appropriate (ensuring `user.preferences` is not None first, or coalescing it with an empty JSON object: `func.coalesce(User.preferences, text("'{}'::jsonb"))`). Handle `None` values for `preferences` field before attempting a merge.
    ```

4. **Expose preference update and retrieval via GraphQL API**
    ```aider
    In `apps/backend/src/a2a_platform/api/graphql/schemas/user_profile_schemas.py`:
    1. Define an input type `UserProfilePreferencesInput` with optional fields: `preferences: Optional[GraphQLJSONObject]` and `timezone: Optional[str]`.
    2. Update the `UserProfileGQLType` to include `preferences: Optional[GraphQLJSONObject]` and `timezone: Optional[str]`.

    In `apps/backend/src/a2a_platform/api/graphql/resolvers/user_profile_resolvers.py`:
    1. Create a GraphQL mutation `updateUserProfilePreferences(info: Info, input: UserProfilePreferencesInput) -> UserProfileGQLType`: This mutation will use the `clerk_user_id` from `info.context["current_user"]`. It will call the `user_service.update_user_preferences` function with the `preferences` and `timezone` from the input. It should then return the updated user profile.
    2. Modify the existing `userProfile(info: Info) -> UserProfileGQLType` query resolver: Ensure it calls `user_service.get_user_preferences` (or fetches the user object which now includes these fields) and populates the `preferences` and `timezone` fields in the returned `UserProfileGQLType`.
    Files:
    - apps/backend/src/a2a_platform/api/graphql/schemas/user_profile_schemas.py
    - apps/backend/src/a2a_platform/api/graphql/resolvers/user_profile_resolvers.py
    Details: Use `strawberry.scalars.JSON` for `GraphQLJSONObject`. Ensure authentication checks are implicitly handled by the existing middleware or explicitly added if necessary for the new mutation. The resolver should gracefully handle cases where preferences might be null.
    ```

5. **Write tests for the new service functions and API endpoints**
    ```aider
    Create/Update test files:
    1. In `apps/backend/tests/unit/services/test_user_service.py`: Add unit tests for `update_user_preferences` (testing merging logic, timezone update, handling of initial null preferences) and `get_user_preferences` (testing retrieval of preferences and timezone).
    2. In `apps/backend/tests/integration/test_graphql_user_profile.py`: Add integration tests for the `updateUserProfilePreferences` mutation (verifying that preferences and timezone are correctly updated and returned) and update tests for the `userProfile` query to verify it returns the new `preferences` and `timezone` fields.
    Files:
    - apps/backend/tests/unit/services/test_user_service.py
    - apps/backend/tests/integration/test_graphql_user_profile.py
    Details: Mock database sessions and Clerk authentication where necessary for unit tests. Use test client and actual database (test instance) for integration tests.
    ```
