---
description: Handle database migrations
---

# Database Migration Workflow

This workflow helps you manage database migrations for the A2A Platform.

## Steps

1. Navigate to the backend directory
   ```bash
   cd /home/<USER>/workbook/a2a-platform/apps/backend
   ```

2. Create a new migration (when you've made model changes)
   ```bash
   alembic revision --autogenerate -m "Description of changes"
   ```

3. Apply migrations to update your database
   ```bash
   alembic upgrade head
   ```

4. Check migration status
   ```bash
   alembic current
   ```

## Important Notes

- Database URLs use `postgresql+asyncpg://` for async connections and `postgresql://` for sync connections
- Always test migrations in a development environment before applying to production
- Use Docker for standing up the backend service with proper database configuration
- Migration files should be committed to version control
