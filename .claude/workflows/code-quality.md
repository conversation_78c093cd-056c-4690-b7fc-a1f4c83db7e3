# Code Quality Workflow

Based on your ESLint/Prettier integration and formatting focus:

## Before Committing
1. **Format Code**
   ```bash
   ./scripts/precommit-check.sh --skip-tests
   ```

2. **Type Check**
   ```bash
   cd apps/web && bun run typecheck
   cd ../backend && mypy .
   ```

3. **Security Check**
   ```bash
   ./scripts/precommit-cdn-security-check.sh
   ```

## PR Preparation
1. **Full Check**
   ```bash
   ./scripts/run-full-check.sh
   ```

2. **Performance Benchmark**
   ```bash
   ./scripts/test-performance-benchmark.sh
   ```

3. **Generate Changelog**
   Use `/changelog` command to create entry

## Common Fixes
- ESLint errors: Often double vs single quotes
- Import sorting: Use isort for Python
- Type errors: Check GraphQL generated types match backend