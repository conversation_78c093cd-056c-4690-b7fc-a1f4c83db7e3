---
description: Clean development environment setup from scratch
---

# Fresh Start Workflow

This workflow sets up a clean development environment from scratch, useful when you need to reset everything.

## Steps

1. **Stop all running services**
   ```bash
   ./scripts/dev.sh stop
   ```

2. **Clean up Docker containers and volumes**
   ```bash
   docker compose down -v
   ```

3. **Remove any cached data**
   ```bash
   docker system prune -f
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Build all services fresh**
   ```bash
   docker compose build --no-cache
   ```

6. **Start services**
   ```bash
   ./scripts/dev.sh start --wait
   ```

7. **Run database migrations**
   ```bash
   ./scripts/db-migrate.sh upgrade
   ```

8. **Verify everything is working**
   ```bash
   docker compose ps
   # All services should be "Up"
   ```

## Post-Setup

- Backend API: https://localhost:8000
- Frontend: https://localhost:5173
- GraphQL Playground: https://localhost:8000/graphql
- Storybook: https://localhost:6006 (if started with --with-storybook)

## Troubleshooting

- If database connection fails, wait a few seconds and retry migrations
- Check logs with `docker compose logs -f [service]` if any service fails
