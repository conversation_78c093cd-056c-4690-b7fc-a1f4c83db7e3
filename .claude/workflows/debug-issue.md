---
description: Debug a specific issue in the application
---

# Debug Issue Workflow

This workflow helps you systematically debug issues in the application.

## Steps

1. **Check service status**
   ```bash
   docker compose ps
   ```

2. **View logs for the problematic service**
   ```bash
   # Backend logs
   docker compose logs -f backend --tail 100
   
   # Frontend logs
   docker compose logs -f frontend --tail 100
   
   # Database logs
   docker compose logs -f db --tail 100
   ```

3. **Access the service shell for debugging**
   ```bash
   # Backend shell
   docker compose exec backend bash
   
   # Frontend shell
   docker compose exec frontend sh
   ```

4. **Run specific tests related to the issue**
   ```bash
   # Run a specific test file
   ./scripts/run-backend-tests.sh tests/path/to/test_file.py
   
   # Run a specific test function
   ./scripts/run-backend-tests.sh tests/path/to/test_file.py::test_function_name
   ```

5. **Check database state**
   ```bash
   # Access PostgreSQL
   docker compose exec db psql -U postgres -d a2a_platform
   
   # Common queries:
   \dt                    # List all tables
   \d table_name         # Describe table structure
   SELECT * FROM users;  # Query data
   ```

6. **Enable debug logging**
   ```bash
   # Set DEBUG environment variable
   export DEBUG=true
   docker compose up -d backend
   ```

## Common Issues

### Database Connection Failed
```bash
# Check if database is running
docker compose ps db

# Check database logs
docker compose logs db --tail 50

# Test connection
docker compose exec db pg_isready -U postgres
```

### Frontend Build Issues
```bash
# Clear cache and rebuild
cd apps/web
rm -rf node_modules
bun install
bun run build
```

### Backend Import Errors
```bash
# Reinstall dependencies
docker compose exec backend pip install -e '.[dev]'

# Check Python path
docker compose exec backend python -c "import sys; print('\n'.join(sys.path))"
```

## Advanced Debugging

### Enable Python Debugger
```python
# Add in your code
import pdb; pdb.set_trace()
```

### Enable Frontend Source Maps
```bash
# In apps/web/vite.config.ts
build: {
  sourcemap: true
}
```

### Profile Performance
```bash
# Backend performance
./scripts/test-performance-benchmark.sh

# Frontend bundle analysis
cd apps/web && bun run build -- --report
```