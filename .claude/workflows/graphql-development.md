# GraphQL Development Workflow

Based on your recent GraphQL commits (code generation, type safety, enum fixes), here's an optimized workflow:

## Steps

1. **Update GraphQL Schema**
   ```bash
   # Edit schema files in apps/backend/src/a2a_platform/api/graphql/schemas/
   ```

2. **Generate Types & Client Code**
   ```bash
   cd apps/web && bun run codegen
   ```

3. **Fix Any Type Errors**
   ```bash
   cd apps/web && bun run typecheck
   ```

4. **Test GraphQL Endpoints**
   ```bash
   cd apps/backend && ./scripts/run-backend-tests.sh tests/integration/graphql/
   ```

5. **Format & Lint**
   ```bash
   ./scripts/precommit-check.sh --skip-tests
   ```

## Common Issues
- Enum type mismatches: Check that GraphQL enums match Python enums exactly
- Multiline string errors: Use the codegen:test variant if needed
- Auth errors in tests: Ensure proper test user setup