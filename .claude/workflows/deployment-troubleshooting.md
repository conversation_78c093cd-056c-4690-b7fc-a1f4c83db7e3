# Deployment Troubleshooting Workflow

Based on your deployment error diagnosis patterns:

## Quick Diagnosis
1. **Check Deployment Errors**
   ```bash
   ./scripts/diagnose-deploy-errors.sh
   ```

2. **Validate HTTPS**
   ```bash
   ./scripts/validate-https-implementation.sh
   ```

3. **Check Security Headers**
   ```bash
   ./scripts/check-security-headers.sh
   ```

## CDN Issues
1. **Validate CDN Performance**
   ```bash
   ./scripts/validate-cdn-performance.sh
   ```

2. **Check Domain Resolution**
   ```bash
   ./scripts/validate-domain-resolution.sh
   ```

## Rollback Procedures
1. **CDN Rollback**
   ```bash
   ./scripts/test-cdn-rollback.sh
   ```

2. **Database Rollback**
   ```bash
   ./scripts/db-migrate.sh rollback
   ```

## Common Issues
- SSL certificate problems: Check ./scripts/monitor-ssl-certificates.sh
- Container permissions: Use ./scripts/fix-web-permissions.sh
- GitHub Actions failures: Check workflow consolidation