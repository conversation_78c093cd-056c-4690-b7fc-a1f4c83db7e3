---
description: Deploy changes to production environment
---

# Production Deployment Workflow

This workflow guides you through deploying changes to production safely.

## Pre-Deployment Checklist

1. **Ensure all tests pass**
   ```bash
   ./scripts/run-full-check.sh
   ```

2. **Run security review**
   ```bash
   ./scripts/run-security-review.sh
   ```

3. **Check for database migrations**
   ```bash
   ./scripts/db-migrate.sh current
   ./scripts/db-migrate.sh history
   ```

4. **Review environment variables**
   - Ensure all required vars are set in production
   - No development/test values in production config

## Deployment Steps

1. **Create release branch**
   ```bash
   git checkout -b release/v1.x.x
   ```

2. **Update version numbers**
   - `apps/backend/pyproject.toml`
   - `apps/web/package.json`

3. **Build production images**
   ```bash
   # Backend
   docker build -f apps/backend/Dockerfile -t a2a-backend:latest apps/backend
   
   # Frontend
   docker build -f apps/web/Dockerfile -t a2a-frontend:latest apps/web
   ```

4. **Run production tests**
   ```bash
   # Test with production config
   ./scripts/run-backend-tests.sh --ci
   ./scripts/run-frontend-tests.sh --ci
   ```

5. **Deploy infrastructure changes (if any)**
   ```bash
   cd terraform/environments/production
   terraform plan
   terraform apply
   ```

6. **Deploy backend**
   - Push to container registry
   - Update Cloud Run service
   - Run database migrations

7. **Deploy frontend**
   - Build production bundle
   - Upload to CDN
   - Invalidate cache

8. **Post-deployment verification**
   ```bash
   # Check service health
   curl https://api.your-domain.com/health
   
   # Run smoke tests
   ./scripts/run-smoke-tests.sh --production
   ```

## Rollback Procedure

1. **Quick rollback (< 5 minutes)**
   ```bash
   # Revert to previous image
   gcloud run deploy backend --image=previous-image
   ```

2. **Database rollback**
   ```bash
   # Downgrade migration
   ./scripts/db-migrate.sh downgrade -1
   ```

3. **Full rollback**
   - Revert infrastructure changes
   - Restore database backup
   - Deploy previous version

## Monitoring

- Check error rates in logs
- Monitor performance metrics
- Watch for unusual patterns
- Set up alerts for critical issues

## Production Debugging

1. **View logs**
   ```bash
   gcloud logging read "resource.type=cloud_run_revision"
   ```

2. **Connect to production database (carefully!)**
   ```bash
   gcloud sql connect production-db --user=postgres
   ```

3. **Check service status**
   ```bash
   gcloud run services describe backend --region=us-central1
   ```

## Important Notes

- **Always backup database** before migrations
- **Test migrations** on staging first
- **Monitor closely** for 30 minutes after deploy
- **Have rollback plan** ready
- **Communicate** with team about deployment