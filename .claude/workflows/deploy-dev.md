---
description: Deployment to development environment
---

# Development Deployment Workflow

This workflow helps you deploy the A2A Platform to a development environment.

## Steps

1. Navigate to the project root
   ```bash
   cd /home/<USER>/workbook/a2a-platform
   ```

2. Make sure all tests pass
   ```bash
   ./scripts/run-backend-tests.sh
   ./scripts/run-frontend-tests.sh
   ```

3. Make sure all linting checks pass
   ```bash
   ./scripts/precommit-check.sh
   ```

4. Build and deploy the application
   ```bash
   docker compose build
   docker compose up -d
   ```

## Important Notes

- The frontend Docker container should run in development mode with 'bun run dev -- --host 0.0.0.0' for hot-reloading
- Consolidate environment variables in a .env file at the project root
- Docker Compose files should reference these variables instead of hardcoding them
- Prefer Docker Compose environment variables as the single source of truth for configuration management
