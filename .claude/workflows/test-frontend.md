---
description: Run frontend tests with proper configuration
---

# Frontend Testing Workflow

This workflow helps you run frontend tests efficiently with the proper configuration.

## Steps

1. Navigate to the project root
   ```bash
   cd /home/<USER>/workbook/a2a-platform
   ```

2. Run the frontend tests using the provided script:
   ```bash
   ./scripts/run-frontend-tests.sh
   ```
   
   You can specify test types with flags:
   - `--unit` - Run only unit tests
   - `--e2e` - Run end-to-end tests
   - `--watch` - Run tests in watch mode for development
   
## Important Notes

- Frontend tests use bun for faster execution
- The frontend development server runs on port 5173 for e2e testing
- When working on the frontend, use `cd apps/web && bun run dev` for local development
