---
description: Update GraphQL schema and regenerate types
---

# GraphQL Schema Update Workflow

This workflow guides you through updating GraphQL schemas and regenerating TypeScript types.

## Prerequisites
- GraphQL schema file exists
- Code generation is configured
- Frontend is using generated types

## Steps

1. **Update the GraphQL schema**
   ```bash
   # Edit the schema file
   apps/backend/src/a2a_platform/api/graphql/schema.graphql
   ```

2. **Validate schema syntax**
   - Ensure all types are properly defined
   - Check field types and nullability
   - Verify enum values

3. **Generate TypeScript types**
   ```bash
   cd apps/web && bun run codegen
   ```

4. **Fix any generation errors**
   - If multiline strings cause issues: `bun run codegen:test`
   - Check for syntax errors in generated files
   - Verify imports are correct

5. **Update affected components**
   - Find components using the changed types
   - Update TypeScript interfaces
   - Fix any type errors

6. **Test the changes**
   ```bash
   # Run frontend tests
   ./scripts/run-frontend-tests.sh
   
   # Run backend GraphQL tests
   ./scripts/run-backend-tests.sh tests/integration/graphql/
   ```

## Common Issues

### Syntax Errors in Generated Files
- Run `bun run codegen:test` for test schema
- Check for special characters in descriptions
- Ensure proper escaping of quotes

### Type Mismatches
- Verify schema matches backend implementation
- Check nullable fields match database
- Ensure enums are consistent

### Import Errors
- Regenerate if imports are broken
- Check codegen configuration
- Verify output paths are correct

## Best Practices

1. **Schema Design**
   - Use clear, descriptive names
   - Document fields with descriptions
   - Consider backward compatibility

2. **Type Safety**
   - Make fields non-nullable when possible
   - Use enums for fixed values
   - Add input validation

3. **Testing**
   - Test both queries and mutations
   - Verify subscriptions work
   - Check error handling