---
description: Run comprehensive test suite with coverage
---

# Full Test Suite Workflow

This workflow runs all tests with coverage reporting to ensure code quality.

## Steps

1. **Navigate to project root**
   ```bash
   cd /home/<USER>/workbook/a2a-platform
   ```

2. **Run backend tests with coverage**
   ```bash
   ./scripts/run-backend-tests.sh --coverage
   ```

3. **Run frontend tests with coverage**
   ```bash
   ./scripts/run-frontend-tests.sh --coverage
   ```

4. **Run E2E tests**
   ```bash
   cd apps/web && bun run test:e2e
   ```

5. **View coverage reports**
   - Backend: `apps/backend/htmlcov/index.html`
   - Frontend: `apps/web/coverage/lcov-report/index.html`

## Tips

- Use `--verbose` flag for detailed test output
- Run specific test categories with `--unit`, `--integration`, or `--e2e`
- Coverage reports are generated in HTML format for easy viewing