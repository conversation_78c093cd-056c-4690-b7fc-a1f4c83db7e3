---
description: Step-by-step guide for implementing new features
---

# Feature Development Workflow

A comprehensive workflow for implementing new features in the A2A Platform.

## Overview

This workflow ensures consistent, high-quality feature implementation following TDD principles and project standards.

## Pre-Development

1. **Understand Requirements**
   - Review user story/specification
   - Identify affected components
   - Plan the implementation approach

2. **Set Up Task List**
   ```
   /plan
   - [ ] Create database models
   - [ ] Write and run migrations
   - [ ] Define GraphQL schema
   - [ ] Generate TypeScript types
   - [ ] Implement backend logic
   - [ ] Create frontend components
   - [ ] Write tests
   - [ ] Update documentation
   ```

3. **Explore Existing Code**
   ```
   /explore
   /patterns
   ```

## Backend Development

1. **Create Database Models**
   ```python
   # Use SQLAlchemy model template
   # apps/backend/src/a2a_platform/db/models/new_model.py
   ```

2. **Generate Migration**
   ```bash
   cd apps/backend
   alembic revision -m "Add new feature models"
   ```

3. **Run Migration**
   ```bash
   ./scripts/db-migrate.sh
   ```

4. **Create Pydantic Schemas**
   ```python
   # apps/backend/src/a2a_platform/schemas/new_feature.py
   ```

5. **Implement Service Layer**
   ```python
   # apps/backend/src/a2a_platform/services/new_feature_service.py
   ```

6. **Add GraphQL Schema**
   ```graphql
   # Update apps/backend/src/a2a_platform/api/graphql/schema.graphql
   ```

7. **Implement Resolvers**
   ```python
   # apps/backend/src/a2a_platform/api/graphql/resolvers/new_feature.py
   ```

## Frontend Development

1. **Generate TypeScript Types**
   ```bash
   cd apps/web && bun run codegen
   ```

2. **Create React Components**
   ```tsx
   # apps/web/src/components/new-feature/
   ```

3. **Add GraphQL Operations**
   ```tsx
   # apps/web/src/graphql/operations.graphql
   ```

4. **Implement Hooks**
   ```tsx
   # apps/web/src/hooks/useNewFeature.ts
   ```

5. **Update Routes**
   ```tsx
   # apps/web/src/routes/index.tsx
   ```

## Testing

1. **Backend Unit Tests**
   ```bash
   ./scripts/run-backend-tests.sh --unit tests/unit/services/test_new_feature.py
   ```

2. **Backend Integration Tests**
   ```bash
   ./scripts/run-backend-tests.sh --integration tests/integration/test_new_feature.py
   ```

3. **Frontend Unit Tests**
   ```bash
   ./scripts/run-frontend-tests.sh --unit
   ```

4. **E2E Tests**
   ```bash
   ./scripts/run-frontend-tests.sh --e2e
   ```

## Quality Assurance

1. **Run All Checks**
   ```bash
   /check
   ```

2. **Security Review**
   ```bash
   /security
   ```

3. **Performance Check**
   ```bash
   /performance
   ```

4. **Test Coverage**
   ```
   /test-coverage
   ```

## Documentation

1. **Update API Documentation**
   ```
   /api-docs
   ```

2. **Add Usage Examples**
   - Create example code snippets
   - Document edge cases
   - Add to relevant README files

3. **Update CHANGELOG**
   ```
   /changelog
   ```

## Final Steps

1. **Review All Changes**
   ```
   /summary
   ```

2. **Run Full Test Suite**
   ```bash
   ./scripts/run-full-check.sh
   ```

3. **Prepare for PR**
   - Ensure all tests pass
   - Code follows style guidelines
   - Documentation is complete
   - No security issues

## Checklist

- [ ] Database models created
- [ ] Migrations run successfully
- [ ] Backend services implemented
- [ ] GraphQL schema updated
- [ ] Frontend components created
- [ ] All tests passing
- [ ] Documentation updated
- [ ] Security review passed
- [ ] Performance acceptable
- [ ] Code review ready