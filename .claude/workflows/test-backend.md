---
description: Run backend tests with proper configuration
---

# Backend Testing Workflow

This workflow helps you run backend tests efficiently with the proper configuration.

## Steps

1. Navigate to the project root
   ```bash
   cd /home/<USER>/workbook/a2a-platform
   ```

2. Run the backend tests using the provided script:
   ```bash
   ./scripts/run-backend-tests.sh
   ```
   
   You can specify test types with flags:
   - `--unit` - Run only unit tests
   - `--integration` - Run only integration tests
   - `--e2e` - Run only end-to-end tests
   
   Or run a specific test:
   ```bash
   ./scripts/run-backend-tests.sh tests/path/to/test_file.py::test_function
   ```

## Important Notes

- Backend tests should always be run from the correct environment
- Tests use Docker PostgreSQL by default
- When running specific test functions, use the `::` notation to specify the function name
