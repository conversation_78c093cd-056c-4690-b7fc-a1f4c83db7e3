---
description: Run linting tools without tests
---

# Linting Workflow

This workflow helps you run linting checks on the codebase without running the tests.

## Steps

1. Navigate to the project root
   ```bash
   cd /home/<USER>/workbook/a2a-platform
   ```

2. Run the precommit check script with the skip-tests flag:
   ```bash
   ./scripts/precommit-check.sh --skip-tests
   ```

## Important Notes

- **NO TRAILING SPACES**: YAML files must not have trailing spaces - yam<PERSON><PERSON> will fail with "trailing spaces" error if empty lines contain spaces
- **Empty lines must be completely empty**: No spaces or tabs on blank lines
- **Comments need proper spacing**: YAML comments must have at least two spaces before the comment character (#)
- Python code follows Black (88 chars), isort, mypy (strict), ruff/flake8 (120 chars max line) standards
- TypeScript code uses double quotes, ESLint, and Prettier
