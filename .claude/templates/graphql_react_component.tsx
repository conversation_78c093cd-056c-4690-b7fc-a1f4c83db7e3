import { FC, useState } from "react";
import { gql, useQuery, useMutation } from "@apollo/client";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";

// GraphQL query definition - replace with actual query
const FETCH_DATA_QUERY = gql`
  query FetchData($id: ID!) {
    someData(id: $id) {
      id
      name
      description
      createdAt
    }
  }
`;

// GraphQL mutation definition - replace with actual mutation
const UPDATE_DATA_MUTATION = gql`
  mutation UpdateData($input: UpdateDataInput!) {
    updateData(input: $input) {
      id
      name
      description
      updatedAt
    }
  }
`;

// TypeScript types for the GraphQL responses
interface FetchDataResponse {
  someData: {
    id: string;
    name: string;
    description: string | null;
    createdAt: string;
  };
}

interface UpdateDataResponse {
  updateData: {
    id: string;
    name: string;
    description: string | null;
    updatedAt: string;
  };
}

interface UpdateDataInput {
  id: string;
  name?: string;
  description?: string;
}

interface GraphQLComponentProps {
  /** ID of the item to fetch and update */
  itemId: string;
  /** Additional CSS class names */
  className?: string;
}

/**
 * Component that demonstrates GraphQL integration with React
 * Uses Apollo Client for queries and mutations
 * 
 * @example
 * ```tsx
 * <GraphQLComponent itemId="123" />
 * ```
 */
export const GraphQLComponent: FC<GraphQLComponentProps> = ({
  itemId,
  className,
}) => {
  const [editMode, setEditMode] = useState(false);
  const [formData, setFormData] = useState<{
    name: string;
    description: string;
  }>({
    name: "",
    description: "",
  });

  // Query to fetch data
  const { loading, error, data, refetch } = useQuery<FetchDataResponse>(
    FETCH_DATA_QUERY,
    {
      variables: { id: itemId },
      onCompleted: (data) => {
        // Initialize form data with current values
        setFormData({
          name: data.someData.name,
          description: data.someData.description || "",
        });
      },
    }
  );

  // Mutation to update data
  const [updateData, { loading: updating }] = useMutation<
    UpdateDataResponse,
    { input: UpdateDataInput }
  >(UPDATE_DATA_MUTATION, {
    onCompleted: () => {
      toast({
        title: "Success",
        description: "Data updated successfully",
      });
      setEditMode(false);
      refetch(); // Refresh data after update
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateData({
      variables: {
        input: {
          id: itemId,
          name: formData.name,
          description: formData.description,
        },
      },
    });
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-destructive/15 text-destructive p-4 rounded-md">
        <h3 className="font-semibold">Error</h3>
        <p>{error.message}</p>
      </div>
    );
  }

  if (!data?.someData) {
    return <p className="text-muted-foreground">No data found</p>;
  }

  return (
    <div className={cn("space-y-4", className)}>
      {editMode ? (
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="name" className="text-sm font-medium">
              Name
            </label>
            <input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-input rounded-md"
              required
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="description" className="text-sm font-medium">
              Description
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows={3}
              className="w-full px-3 py-2 border border-input rounded-md"
            />
          </div>

          <div className="flex gap-2 justify-end">
            <Button
              type="button"
              variant="outline"
              onClick={() => setEditMode(false)}
              disabled={updating}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={updating}>
              {updating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                "Save Changes"
              )}
            </Button>
          </div>
        </form>
      ) : (
        <>
          <div>
            <h2 className="text-xl font-semibold">{data.someData.name}</h2>
            <p className="text-sm text-muted-foreground">
              Created: {new Date(data.someData.createdAt).toLocaleDateString()}
            </p>
            {data.someData.description && <p className="mt-2">{data.someData.description}</p>}
          </div>

          <Button onClick={() => setEditMode(true)}>Edit</Button>
        </>
      )}
    </div>
  );
};

export default GraphQLComponent;
