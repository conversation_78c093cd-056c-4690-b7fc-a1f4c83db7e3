"""
GraphQL Resolver Template for A2A Platform

This template demonstrates the standard pattern for creating GraphQL resolvers
in the A2A Platform backend.
"""
from typing import Any, List, Optional

from sqlalchemy.ext.asyncio import AsyncSession

from apps.backend.api.deps import get_current_user, get_db
from apps.backend.models import SomeModel
from apps.backend.schemas import SomeSchema, SomeCreateSchema, SomeUpdateSchema


# Query Resolvers
async def resolve_some_items(
    db: AsyncSession,
    info,
    limit: Optional[int] = 50,
    offset: Optional[int] = 0,
) -> List[SomeModel]:
    """
    Resolve a list of items with pagination.
    
    Args:
        db: Database session from context
        info: GraphQL resolver info
        limit: Maximum number of items to return
        offset: Number of items to skip
        
    Returns:
        List of model instances
    """
    # Get current user from request context
    current_user = await get_current_user(info.context)
    
    # Query filtering by user
    query = (
        db.query(SomeModel)
        .filter(SomeModel.user_id == current_user.id)
        .order_by(SomeModel.created_at.desc())
        .limit(limit)
        .offset(offset)
    )
    
    return await query.all()


async def resolve_some_item(
    db: AsyncSession,
    info,
    item_id: str,
) -> Optional[SomeModel]:
    """
    Resolve a single item by ID.
    
    Args:
        db: Database session from context
        info: GraphQL resolver info
        item_id: ID of the item to fetch
        
    Returns:
        Model instance if found, None otherwise
    """
    # Get current user from request context
    current_user = await get_current_user(info.context)
    
    # Query filtering by ID and user
    return await (
        db.query(SomeModel)
        .filter(
            SomeModel.id == item_id,
            SomeModel.user_id == current_user.id
        )
        .first()
    )


# Mutation Resolvers
async def resolve_create_some_item(
    db: AsyncSession,
    info,
    input_data: dict,
) -> SomeModel:
    """
    Create a new item.
    
    Args:
        db: Database session from context
        info: GraphQL resolver info
        input_data: Input data for creating the item
        
    Returns:
        Created model instance
    """
    # Get current user from request context
    current_user = await get_current_user(info.context)
    
    # Create schema from input data
    create_schema = SomeCreateSchema(**input_data)
    
    # Create model instance
    db_item = SomeModel(
        **create_schema.dict(),
        user_id=current_user.id,
    )
    
    # Add and commit to database
    db.add(db_item)
    await db.commit()
    await db.refresh(db_item)
    
    return db_item


async def resolve_update_some_item(
    db: AsyncSession,
    info,
    input_data: dict,
) -> Optional[SomeModel]:
    """
    Update an existing item.
    
    Args:
        db: Database session from context
        info: GraphQL resolver info
        input_data: Input data for updating the item
        
    Returns:
        Updated model instance if found, None otherwise
    """
    # Get current user from request context
    current_user = await get_current_user(info.context)
    
    # Get item ID from input
    item_id = input_data.get("id")
    if not item_id:
        return None
    
    # Fetch existing item
    db_item = await (
        db.query(SomeModel)
        .filter(
            SomeModel.id == item_id,
            SomeModel.user_id == current_user.id
        )
        .first()
    )
    
    if not db_item:
        return None
    
    # Update schema from input data
    update_schema = SomeUpdateSchema(**input_data)
    
    # Update model with new values, excluding None values
    update_data = update_schema.dict(exclude_unset=True, exclude={"id"})
    for key, value in update_data.items():
        setattr(db_item, key, value)
    
    # Commit changes
    await db.commit()
    await db.refresh(db_item)
    
    return db_item


async def resolve_delete_some_item(
    db: AsyncSession,
    info,
    item_id: str,
) -> bool:
    """
    Delete an item by ID.
    
    Args:
        db: Database session from context
        info: GraphQL resolver info
        item_id: ID of the item to delete
        
    Returns:
        True if deleted, False otherwise
    """
    # Get current user from request context
    current_user = await get_current_user(info.context)
    
    # Fetch existing item
    db_item = await (
        db.query(SomeModel)
        .filter(
            SomeModel.id == item_id,
            SomeModel.user_id == current_user.id
        )
        .first()
    )
    
    if not db_item:
        return False
    
    # Delete item
    await db.delete(db_item)
    await db.commit()
    
    return True
