# GraphQL Schema Template for A2A Platform
# Based on requirements from ADRs

# User Management Types
type User {
  id: ID!
  clerkUserId: String!
  timezone: String
  preferences: UserPreferences
  createdAt: DateTime!
  updatedAt: DateTime!
}

type UserPreferences {
  themeMode: ThemeMode
  notifications: NotificationPreferences
}

enum ThemeMode {
  LIGHT
  DARK
  SYSTEM
}

type NotificationPreferences {
  emailEnabled: Boolean!
  pushEnabled: Boolean!
}

# Assistant Management Types
type Assistant {
  id: ID!
  userId: ID!
  name: String!
  description: String
  avatarUrl: String
  createdAt: DateTime!
  updatedAt: DateTime!
}

# File Management Types
type FileMetadata {
  id: ID!
  userId: ID!
  filename: String!
  mimetype: String!
  size: Int!
  url: String!
  createdAt: DateTime!
}

# Conversation and Chat Types
type Conversation {
  id: ID!
  userId: ID!
  assistantId: ID!
  title: String
  createdAt: DateTime!
  updatedAt: DateTime!
  messages: [ChatMessage!]!
}

type ChatMessage {
  id: ID!
  conversationId: ID!
  sender: MessageSender!
  content: String!
  createdAt: DateTime!
}

enum MessageSender {
  USER
  ASSISTANT
  SYSTEM
}

# CLI Token Management
type CliToken {
  id: ID!
  userId: ID!
  name: String!
  lastUsed: DateTime
  createdAt: DateTime!
  expiresAt: DateTime
}

# Scalars
scalar DateTime

# Queries
type Query {
  # User related
  me: User!
  myDataOverview: DataOverview!
  
  # Assistant related
  myAssistant: Assistant
  
  # Conversation related
  conversations(limit: Int, offset: Int): [Conversation!]!
  conversation(id: ID!): Conversation
  
  # CLI Tokens
  cliTokens: [CliToken!]!
  
  # File Management
  fileMetadata(id: ID!): FileMetadata
  myFiles(limit: Int, offset: Int): [FileMetadata!]!
}

type DataOverview {
  user: User!
  assistant: Assistant
  recentConversations: [Conversation!]!
  unreadMessages: Int!
}

# Mutations
type Mutation {
  # User related
  updateUserPreferences(input: UpdateUserPreferencesInput!): User!
  
  # Assistant related
  createAssistant(input: CreateAssistantInput!): Assistant!
  updateAssistant(input: UpdateAssistantInput!): Assistant!
  deleteAssistant(id: ID!): Boolean!
  
  # Conversation related
  sendMessage(input: SendMessageInput!): ChatMessage!
  createConversation(input: CreateConversationInput!): Conversation!
  
  # File Management
  requestFileUploadUrl(input: RequestFileUploadInput!): FileUploadUrl!
  completeFileUpload(id: ID!): FileMetadata!
  
  # CLI Token Management
  createCliToken(input: CreateCliTokenInput!): CliToken!
  revokeCliToken(id: ID!): Boolean!
}

# Subscriptions
type Subscription {
  newMessage(conversationId: ID!): ChatMessage!
}

# Input Types
input UpdateUserPreferencesInput {
  timezone: String
  themeMode: ThemeMode
}

input CreateAssistantInput {
  name: String!
  description: String
  avatarUrl: String
}

input UpdateAssistantInput {
  id: ID!
  name: String
  description: String
  avatarUrl: String
}

input SendMessageInput {
  conversationId: ID!
  content: String!
}

input CreateConversationInput {
  assistantId: ID!
  title: String
}

input RequestFileUploadInput {
  filename: String!
  mimetype: String!
  size: Int!
}

type FileUploadUrl {
  id: ID!
  uploadUrl: String!
  fileMetadata: FileMetadata!
}

input CreateCliTokenInput {
  name: String!
  expiresInDays: Int
}
