import { FC, useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON>Footer, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";

/**
 * Props for the StyledComponent
 */
interface StyledComponentProps {
  /** Main title text for the component */
  title: string;
  /** Optional description text */
  description?: string;
  /** Optional loading state */
  isLoading?: boolean;
  /** Optional error message */
  error?: string;
  /** Callback for primary action */
  onAction?: () => void;
  /** Text for action button */
  actionText?: string;
  /** Additional CSS class names */
  className?: string;
  /** Child content */
  children?: React.ReactNode;
}

/**
 * A styled card component with common UI patterns
 * 
 * @example
 * ```tsx
 * <StyledComponent 
 *   title="User Profile" 
 *   description="Manage your personal information"
 *   actionText="Save Changes"
 *   onAction={handleSave}
 * >
 *   <ProfileForm user={currentUser} />
 * </StyledComponent>
 * ```
 */
export const StyledComponent: FC<StyledComponentProps> = ({
  title,
  description,
  isLoading = false,
  error,
  onAction,
  actionText = "Submit",
  className,
  children,
}) => {
  const [showError, setShowError] = useState<boolean>(!!error);

  useEffect(() => {
    // Show error message when error prop changes
    if (error) {
      setShowError(true);
      // Hide error after 5 seconds
      const timer = setTimeout(() => {
        setShowError(false);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
      </CardHeader>
      
      <CardContent>
        {showError && (
          <div className="bg-destructive/15 text-destructive px-4 py-2 rounded-md mb-4">
            <p className="text-sm">{error}</p>
          </div>
        )}
        
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          </div>
        ) : (
          children
        )}
      </CardContent>
      
      {onAction && (
        <CardFooter className="flex justify-end">
          <Button 
            onClick={onAction}
            disabled={isLoading}
          >
            {actionText}
          </Button>
        </CardFooter>
      )}
    </Card>
  );
};

export default StyledComponent;
