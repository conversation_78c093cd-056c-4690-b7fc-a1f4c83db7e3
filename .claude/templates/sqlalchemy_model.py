"""
SQLAlchemy Model Template for A2A Platform

This template demonstrates the standard pattern for creating SQLAlchemy models
with proper typing, relationships, and timestamps.
"""
from datetime import datetime
from typing import TYPE_CHECKING, List, Optional

from sqlalchemy import Column, DateTime, Foreign<PERSON>ey, Integer, String, Text, Boolean
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from apps.backend.db.base_class import Base

if TYPE_CHECKING:
    # Import for type checking only to avoid circular imports
    from .related_model import RelatedModel


class ModelName(Base):
    """
    Model description - replace with actual description
    
    This model follows the A2A Platform database conventions:
    - UUID primary keys
    - Created/updated timestamps
    - Proper foreign keys with relationship definitions
    - User association for authorization
    """
    __tablename__ = "model_names"  # Use plural snake_case for table names
    
    # Primary key - always use UUID
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        index=True,
    )
    
    # Standard timestamp fields
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )
    
    # User association - always include for authorization
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    user = relationship("User", back_populates="model_names")
    
    # Example fields - replace with actual fields
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Example foreign key relationship
    # related_id = Column(
    #     UUID(as_uuid=True),
    #     ForeignKey("related_models.id", ondelete="CASCADE"),
    #     nullable=True,
    #     index=True,
    # )
    # related = relationship("RelatedModel", back_populates="model_names")
    
    # Example one-to-many relationship
    # child_items = relationship(
    #     "ChildModel",
    #     back_populates="parent",
    #     cascade="all, delete-orphan",
    # )
    
    def __repr__(self) -> str:
        """String representation of the model"""
        return f"<{self.__class__.__name__} {self.id}: {self.name}>"
