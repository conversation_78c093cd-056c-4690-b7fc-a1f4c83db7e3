from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from apps.backend.api.deps import get_db
from apps.backend.schemas import SomeResponseSchema, SomeRequestSchema

router = APIRouter(
    prefix="/some-resource",
    tags=["resource-tag"],
)


@router.get("/", response_model=list[SomeResponseSchema])
async def get_items(
    db: AsyncSession = Depends(get_db),
):
    """
    Get all items.
    
    Args:
        db: Database session dependency
        
    Returns:
        List of items
    """
    # Implementation here
    pass


@router.post("/", response_model=SomeResponseSchema, status_code=status.HTTP_201_CREATED)
async def create_item(
    item: SomeRequestSchema,
    db: AsyncSession = Depends(get_db),
):
    """
    Create a new item.
    
    Args:
        item: Item data
        db: Database session dependency
        
    Returns:
        Created item
    """
    # Implementation here
    pass


@router.get("/{item_id}", response_model=SomeResponseSchema)
async def get_item(
    item_id: int,
    db: AsyncSession = Depends(get_db),
):
    """
    Get a specific item by ID.
    
    Args:
        item_id: ID of the item
        db: Database session dependency
        
    Returns:
        Item if found
        
    Raises:
        HTTPException: If item not found
    """
    # Get item from database
    # item = await some_service.get_by_id(db, item_id)
    
    # if not item:
    #     raise HTTPException(
    #         status_code=status.HTTP_404_NOT_FOUND,
    #         detail=f"Item with id {item_id} not found",
    #     )
    
    # return item
    pass
