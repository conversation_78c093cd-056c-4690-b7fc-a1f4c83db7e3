{"shortcuts": {"t": "test", "tb": "test-backend", "tf": "test-frontend", "l": "lint", "m": "migrate", "r": "restart", "f": "fresh", "c": "check", "u": "up", "d": "down", "g": "graphql", "s": "shell", "db": "db", "lg": "logs", "p": "performance", "sec": "security", "dbg": "debug"}, "combinations": {"restart": "down && up", "refresh": "down && up && migrate", "rebuild": "down && build && up", "clean-start": "down -v && up && migrate && seed", "full-check": "lint && test-backend && test-frontend", "quick-check": "lint && test-backend --unit"}}