# Claude Code Development Patterns

## Codebase Exploration Patterns

### Initial Project Understanding
When starting with a new codebase:
1. Use `/explore` to map the structure
2. Run `/dependencies` to understand the tech stack
3. Use `/patterns` to identify coding conventions
4. Run `/architecture` to understand component relationships

### File Navigation
- Use `Glob` for finding files by pattern (e.g., "**/*.test.ts")
- Use `Grep` for searching content across files
- Use `Task` for complex multi-step searches
- Always batch multiple `Read` operations in parallel

## Context Management Patterns

### Storing Project Knowledge
```
/remember entities: [
  {
    name: "ProjectArchitecture",
    entityType: "Documentation",
    observations: ["Uses Docker Compose", "GraphQL API", "React frontend"]
  }
]
```

### Retrieving Context
- Use `/recall <query>` to search stored knowledge
- Use `/context` to view the full knowledge graph
- Use `/think` for complex reasoning about the codebase

## Code Generation Patterns

### Using Templates
1. Check for existing templates in `.claude/templates/`
2. Adapt templates to match project conventions
3. Always follow existing code style in the codebase

### Multi-File Operations
When creating features that span multiple files:
1. Use `/plan` to create a task list
2. Update task status as you progress
3. Use `MultiEdit` for multiple changes to the same file
4. Batch `Read` operations before making changes

## Testing Patterns

### Test-Driven Development
1. Read existing tests to understand patterns
2. Write tests before implementation
3. Use project-specific test runners
4. Always run tests after changes

### Test Organization
- Unit tests: Mock external dependencies
- Integration tests: Use test database
- E2E tests: Full user flows
- Performance tests: Benchmark critical paths

## Debugging Patterns

### Systematic Debugging
1. Use `/diagnose` to check for IDE diagnostics
2. Read error messages carefully
3. Check logs with appropriate commands
4. Use `/shell` to debug inside containers
5. Use `/think` to reason through complex issues

### Common Issues
- Permission errors: Check file ownership
- Import errors: Verify module paths
- Type errors: Check generated types
- Test failures: Compare local vs CI environment

## Performance Patterns

### Code Optimization
1. Use `/performance` to benchmark
2. Profile before optimizing
3. Focus on algorithmic improvements
4. Consider caching strategies

### Database Optimization
- Add appropriate indexes
- Use query builders properly
- Implement connection pooling
- Monitor query performance

## Security Patterns

### Security Checks
1. Run `/security-scan` regularly
2. Never hardcode secrets
3. Use environment variables
4. Validate all inputs
5. Follow OWASP guidelines

### Authentication Patterns
- Use established auth libraries
- Implement proper CORS
- Secure API endpoints
- Handle tokens safely

## Documentation Patterns

### Code Documentation
- Document complex logic
- Add type annotations
- Include usage examples
- Keep documentation updated

### API Documentation
1. Use `/api-docs` to generate documentation
2. Include request/response examples
3. Document error cases
4. Version your APIs

## Collaboration Patterns

### Working with Git
1. Use meaningful commit messages
2. Create focused pull requests
3. Run pre-commit checks
4. Use `/changelog` for release notes

### Code Review Preparation
1. Run `/summary` to review changes
2. Ensure tests pass
3. Check code coverage
4. Document breaking changes

## Common Workflows

### Feature Implementation
```
1. /plan - Create task list
2. /explore - Understand existing code
3. /patterns - Follow conventions
4. Implement feature
5. /test-coverage - Ensure testing
6. /document - Add documentation
7. /summary - Review changes
```

### Bug Fixing
```
1. /diagnose - Check for issues
2. /recall - Search for similar fixes
3. Debug systematically
4. Fix the issue
5. Add regression test
6. /changelog - Document fix
```

### Refactoring
```
1. /refactor - Identify improvements
2. /test-coverage - Ensure safety net
3. Make incremental changes
4. Run tests frequently
5. /optimize - Check performance
6. /document - Update docs
```

## Best Practices

### Code Quality
- Always run linters before committing
- Follow project style guides
- Write self-documenting code
- Keep functions focused

### Error Handling
- Use structured error types
- Log errors appropriately
- Provide helpful error messages
- Handle edge cases

### Performance
- Measure before optimizing
- Use appropriate data structures
- Implement caching wisely
- Monitor resource usage

### Security
- Validate all inputs
- Use parameterized queries
- Implement rate limiting
- Keep dependencies updated