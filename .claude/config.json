{"version": "1.0", "project": "a2a-platform", "preferences": {"codingStyle": {"python": {"formatter": "black", "lineLength": 88, "importSorter": "isort", "typeChecker": "mypy", "linter": "ruff"}, "typescript": {"quotes": "double", "linter": "eslint", "formatter": "prettier"}}, "database": {"orm": "SQLAlchemy", "asyncConnection": "postgresql+asyncpg://", "syncConnection": "postgresql://"}, "graphql": {"casing": "camelCase", "dataStructures": "pydantic"}, "testing": {"backend": "./scripts/run-backend-tests.sh", "frontend": "./scripts/run-frontend-tests.sh", "linting": "./scripts/precommit-check.sh"}}, "allowedTools": ["run_command", "find_by_name", "view_file_outline", "view_line_range", "grep_search", "write_to_file", "replace_file_content"]}