Map relationships between code components for $ARGUMENTS. Follow these steps:

1. Create entities for components, services, or modules:
```
mcp__think-tank__upsert_entities
```

2. Define relationships (depends_on, implements, extends, calls):
```
mcp__think-tank__create_relations
```

3. View the complete knowledge graph:
```
mcp__think-tank__read_graph
```

Creates a visual map of how system components interact and depend on each other.
**Security Note:** This command passes arguments to a script that handles its own validation and security checks.
