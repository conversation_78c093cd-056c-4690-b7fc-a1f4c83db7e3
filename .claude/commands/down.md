Stop Docker containers. Execute:
```bash
# Smart environment detection
if [[ "$1" == "dev" ]]; then
    shift
    docker compose -f docker-compose.dev.yml down "$@"
elif [[ "$1" == "test" ]]; then
    shift
    docker compose -f docker-compose.test.yml down "$@"
else
    docker compose down $ARGUMENTS
fi
```
Stops and removes all containers. Use `down dev` for development environment or `down test` for test environment. Add `--volumes` to also remove volumes (e.g., `down test --volumes`).
**Security Note:** This command passes arguments to docker compose which handles its own validation.
