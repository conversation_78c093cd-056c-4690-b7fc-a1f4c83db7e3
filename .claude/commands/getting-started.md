Help new developers get productive with Claude Code quickly. Execute:
```bash
# Smart getting started guide
case "$1" in
  "commands"|"c")
    echo "🚀 Top 10 Essential Commands"
    echo ""
    echo "1️⃣  /test              - Run tests (backend unit by default)"
    echo "    /test unit        - ALL unit tests (frontend + backend)"
    echo "    /test all         - Complete test suite"
    echo ""
    echo "2️⃣  /db                - Database console"
    echo "    /db migrate       - Run migrations"
    echo "    /db seed          - Add test data"
    echo ""
    echo "3️⃣  /check             - Run all quality checks"
    echo "    /check fix        - Auto-fix issues"
    echo ""
    echo "4️⃣  /up dev            - Start development environment"
    echo "    /down dev         - Stop development environment"
    echo ""
    echo "5️⃣  /explore [path]    - Analyze codebase area"
    echo ""
    echo "6️⃣  /graphql           - Generate TypeScript types"
    echo "    /graphql update   - Update schema from backend"
    echo ""
    echo "7️⃣  /precommit         - Run all pre-commit checks"
    echo ""
    echo "8️⃣  /debug             - Diagnose issues"
    echo ""
    echo "9️⃣  /remember [fact]   - Store project knowledge"
    echo "    /recall [topic]   - Retrieve stored knowledge"
    echo ""
    echo "🔟  /help             - Get help with any command"
    echo ""
    echo "💡 Try: /getting-started workflows"
    ;;
    
  "workflows"|"w")
    echo "🔄 Common Development Workflows"
    echo ""
    echo "📝 Starting Your Day:"
    echo "  1. /up dev                    # Start environment"
    echo "  2. /db migrate               # Ensure DB is current"
    echo "  3. /test                     # Verify everything works"
    echo ""
    echo "🐛 Fixing a Bug:"
    echo "  1. /explore [bug-area]       # Understand the code"
    echo "  2. Write a failing test      # Reproduce the bug"
    echo "  3. /test                     # Verify test fails"
    echo "  4. Fix the bug              # Make test pass"
    echo "  5. /test unit               # Run all unit tests"
    echo "  6. /precommit               # Final checks"
    echo ""
    echo "✨ Adding a Feature:"
    echo "  1. /explore [feature-area]   # Study existing patterns"
    echo "  2. /db migrate              # Update schema if needed"
    echo "  3. Write tests first        # TDD approach"
    echo "  4. Implement feature        # Make tests pass"
    echo "  5. /test all                # Full validation"
    echo "  6. /check all               # Quality checks"
    echo ""
    echo "🚀 Before Committing:"
    echo "  1. /test unit               # All unit tests"
    echo "  2. /check fix               # Auto-fix formatting"
    echo "  3. /precommit               # Final validation"
    echo ""
    echo "💡 Try: /getting-started interactive"
    ;;
    
  "interactive"|"i")
    echo "🎯 Interactive Claude Code Tour"
    echo ""
    echo "Welcome! Let's explore Claude Code together."
    echo ""
    echo "❓ What would you like to do?"
    echo ""
    echo "1. 🧪 Learn about testing"
    echo "2. 🗄️  Work with the database"
    echo "3. 🔍 Explore the codebase"
    echo "4. 🛠️  Fix issues and check quality"
    echo "5. 🎨 Work on frontend features"
    echo "6. 🔧 Backend development"
    echo ""
    echo "Reply with a number (1-6) to explore that area."
    echo ""
    echo "For example:"
    echo '  User: "1"'
    echo '  Claude: "Great! Let'\''s explore testing..."'
    echo ""
    echo "Or describe what you want to do:"
    echo '  User: "I need to add a new API endpoint"'
    echo '  Claude: "I'\''ll guide you through backend development..."'
    ;;
    
  "tips"|"t")
    echo "💡 Pro Tips for Claude Code"
    echo ""
    echo "🎯 Communication:"
    echo "  • Be specific: Include file paths and error messages"
    echo "  • Course-correct early if Claude goes wrong direction"
    echo "  • Use screenshots for UI work"
    echo ""
    echo "🚀 Productivity:"
    echo "  • Always /explore before implementing"
    echo "  • Write tests before code (TDD)"
    echo "  • Use /check fix to auto-fix formatting"
    echo ""
    echo "🧠 Claude's Memory:"
    echo "  • /remember important project facts"
    echo "  • /recall to retrieve later"
    echo "  • /wrap-it-up to save session context"
    echo ""
    echo "⚡ Quick Shortcuts:"
    echo "  • /test → Quick backend unit tests"
    echo "  • /db → Open database console"
    echo "  • /dc → Direct docker compose access"
    echo ""
    echo "📚 Learning More:"
    echo "  • Read CLAUDE.md for workflow patterns"
    echo "  • Check .claude/README.md for all commands"
    echo "  • Use /help [command] for details"
    ;;
    
  "help"|"--help"|"-h")
    echo "Usage: getting-started [topic]"
    echo ""
    echo "Topics:"
    echo "  commands, c      Top 10 essential commands"
    echo "  workflows, w     Common development workflows"
    echo "  interactive, i   Interactive guided tour"
    echo "  tips, t          Pro tips and shortcuts"
    echo ""
    echo "Examples:"
    echo "  /getting-started              # Overview"
    echo "  /getting-started commands     # Essential commands"
    echo "  /getting-started workflows    # Common patterns"
    echo "  /getting-started interactive  # Guided tour"
    ;;
    
  *)
    echo "🚀 Welcome to Claude Code!"
    echo ""
    echo "Claude Code provides 60+ commands to streamline your development."
    echo ""
    echo "🎯 Quick Start Options:"
    echo ""
    echo "  /getting-started commands     📋 Top 10 essential commands"
    echo "  /getting-started workflows    🔄 Common development patterns"
    echo "  /getting-started interactive  🎮 Interactive guided tour"
    echo "  /getting-started tips        💡 Pro tips and shortcuts"
    echo ""
    echo "📚 Key Resources:"
    echo "  • CLAUDE.md - Workflow patterns and best practices"
    echo "  • .claude/README.md - Complete command reference"
    echo "  • /help [command] - Get help with any command"
    echo ""
    echo "🏃 Ready to start?"
    echo "  1. Try: /up dev      (Start development environment)"
    echo "  2. Then: /test       (Run quick tests)"
    echo "  3. Explore: /explore apps/web  (Understand the codebase)"
    echo ""
    echo "💬 Remember: Be specific, explore first, test always!"
    ;;
esac
```

Interactive onboarding guide for new developers. Shows essential commands, workflows, and tips.
**Security Note:** This command provides guidance only and does not execute other commands.