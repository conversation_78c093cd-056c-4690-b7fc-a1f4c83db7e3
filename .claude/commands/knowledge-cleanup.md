Clean up and organize project knowledge base around $ARGUMENTS. Options:

1. **Remove outdated information**:
```
mcp__think-tank__delete_entities
mcp__think-tank__delete_observations
```

2. **Update existing relationships**:
```
mcp__think-tank__update_relations
```

3. **View memory storage path**:
```
mcp__think-tank__show_memory_path
```

Helps maintain a clean, organized knowledge base by removing obsolete information and updating relationships.
**Security Note:** This command passes arguments to a script that handles its own validation and security checks.
