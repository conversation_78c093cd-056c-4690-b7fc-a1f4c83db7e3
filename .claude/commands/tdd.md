Test-Driven Development workflow with enforcement and guidance. Execute:
```bash
# TDD Workflow Management
ACTION="${1:-help}"
FEATURE_NAME="${2:-}"
TDD_STATE_FILE=".tdd-state"

case "$ACTION" in
    "start"|"begin")
        if [[ -z "$FEATURE_NAME" ]]; then
            echo "❌ Feature name required"
            echo "Usage: tdd start [feature-name]"
            exit 1
        fi
        
        echo "🚀 Starting TDD for: $FEATURE_NAME"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        
        # Save TDD state
        cat > "$TDD_STATE_FILE" << EOF
FEATURE_NAME=$FEATURE_NAME
TDD_PHASE=test_creation
START_TIME=$(date +%s)
TESTS_WRITTEN=false
TESTS_PASSING=false
IMPLEMENTATION_DONE=false
EOF
        
        echo ""
        echo "📋 TDD Phase 1: Write Failing Test"
        echo ""
        echo "1. Create test file for: $FEATURE_NAME"
        echo "2. Write test that describes expected behavior"
        echo "3. Run test to verify it fails"
        echo ""
        echo "💡 Test should be:"
        echo "  • Specific and focused"
        echo "  • Test one behavior"
        echo "  • Have clear assertions"
        echo "  • Use descriptive names"
        echo ""
        echo "Example test structure:"
        echo "  describe('$FEATURE_NAME', () => {"
        echo "    it('should [expected behavior]', () => {"
        echo "      // Arrange"
        echo "      // Act"
        echo "      // Assert"
        echo "    })"
        echo "  })"
        echo ""
        echo "When your test is ready, run: /tdd verify-test"
        ;;
        
    "verify-test"|"verify")
        if [[ ! -f "$TDD_STATE_FILE" ]]; then
            echo "❌ No active TDD session. Start with: /tdd start [feature]"
            exit 1
        fi
        
        source "$TDD_STATE_FILE"
        
        echo "🧪 Verifying Test Phase"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Feature: $FEATURE_NAME"
        echo ""
        echo "Running tests to verify they fail..."
        echo ""
        
        # Detect test type and run appropriate tests
        if [[ -d "apps/web/src" ]]; then
            echo "📦 Running frontend tests..."
            cd apps/web && bun test || FRONTEND_FAILED=1
        fi
        
        if [[ -d "apps/backend/tests" ]]; then
            echo "🐍 Running backend tests..."
            ./scripts/run-backend-tests.sh --unit || BACKEND_FAILED=1
        fi
        
        echo ""
        if [[ "$FRONTEND_FAILED" == "1" ]] || [[ "$BACKEND_FAILED" == "1" ]]; then
            echo "✅ Tests are failing as expected!"
            echo ""
            echo "📋 TDD Phase 2: Implement Solution"
            echo ""
            echo "Now implement just enough code to make the test pass:"
            echo "  • Write minimal implementation"
            echo "  • Focus only on making test pass"
            echo "  • Don't add extra features"
            echo "  • Keep it simple"
            echo ""
            echo "When implementation is ready, run: /tdd implement"
            
            # Update state
            sed -i.bak 's/TESTS_WRITTEN=false/TESTS_WRITTEN=true/' "$TDD_STATE_FILE" && rm -f "$TDD_STATE_FILE.bak"
            sed -i.bak 's/TDD_PHASE=test_creation/TDD_PHASE=implementation/' "$TDD_STATE_FILE" && rm -f "$TDD_STATE_FILE.bak"
        else
            echo "❌ Tests are not failing!"
            echo ""
            echo "Tests must fail first in TDD. Check:"
            echo "  • Did you write a test for new functionality?"
            echo "  • Is the test actually testing the right thing?"
            echo "  • Is the implementation already present?"
            echo ""
            echo "Fix the test and run /tdd verify-test again"
        fi
        ;;
        
    "implement"|"code")
        if [[ ! -f "$TDD_STATE_FILE" ]]; then
            echo "❌ No active TDD session. Start with: /tdd start [feature]"
            exit 1
        fi
        
        source "$TDD_STATE_FILE"
        
        if [[ "$TDD_PHASE" != "implementation" ]]; then
            echo "❌ Wrong TDD phase. Current phase: $TDD_PHASE"
            echo "Run /tdd verify-test first"
            exit 1
        fi
        
        echo "🔧 Implementation Phase"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Feature: $FEATURE_NAME"
        echo ""
        echo "✅ Tests failing (as expected)"
        echo "🎯 Now implement the solution"
        echo ""
        echo "Implementation Guidelines:"
        echo "  • Write minimal code to pass tests"
        echo "  • Don't over-engineer"
        echo "  • Focus on making tests green"
        echo "  • Add error handling as needed"
        echo ""
        echo "After implementation, run: /tdd validate"
        
        # Update state
        sed -i.bak 's/TDD_PHASE=implementation/TDD_PHASE=validation/' "$TDD_STATE_FILE" && rm -f "$TDD_STATE_FILE.bak"
        sed -i.bak 's/IMPLEMENTATION_DONE=false/IMPLEMENTATION_DONE=true/' "$TDD_STATE_FILE" && rm -f "$TDD_STATE_FILE.bak"
        ;;
        
    "validate"|"check")
        if [[ ! -f "$TDD_STATE_FILE" ]]; then
            echo "❌ No active TDD session. Start with: /tdd start [feature]"
            exit 1
        fi
        
        source "$TDD_STATE_FILE"
        
        if [[ "$TDD_PHASE" != "validation" ]]; then
            echo "❌ Wrong TDD phase. Current phase: $TDD_PHASE"
            echo "Complete implementation first with /tdd implement"
            exit 1
        fi
        
        echo "✅ Validation Phase"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Feature: $FEATURE_NAME"
        echo ""
        echo "Running comprehensive tests..."
        
        # Run all tests
        VALIDATION_FAILED=0
        
        echo ""
        echo "🧪 Unit Tests:"
        if ! ./scripts/run-backend-tests.sh --unit; then
            VALIDATION_FAILED=1
        fi
        
        echo ""
        echo "🔗 Integration Tests:"
        if ! ./scripts/run-backend-tests.sh --integration; then
            VALIDATION_FAILED=1
        fi
        
        echo ""
        echo "📦 Frontend Tests:"
        if ! ./scripts/run-frontend-tests.sh; then
            VALIDATION_FAILED=1
        fi
        
        echo ""
        echo "🔍 Code Quality:"
        if ! ./scripts/precommit-check.sh; then
            echo "⚠️  Code quality issues found"
            VALIDATION_FAILED=1
        fi
        
        if [[ $VALIDATION_FAILED -eq 0 ]]; then
            echo ""
            echo "🎉 TDD Cycle Complete!"
            echo ""
            echo "✅ Tests written and failing initially"
            echo "✅ Implementation makes tests pass"
            echo "✅ All tests passing"
            echo "✅ Code quality checks pass"
            echo ""
            echo "📊 TDD Summary for $FEATURE_NAME:"
            START_TIME=${START_TIME:-$(date +%s)}
            DURATION=$(($(date +%s) - START_TIME))
            echo "  Duration: ${DURATION}s"
            echo "  Phase: Complete"
            echo ""
            echo "🚀 Next Steps:"
            echo "  • Run /tdd refactor if code can be improved"
            echo "  • Start next feature with /tdd start [next-feature]"
            echo "  • Commit your changes"
            
            # Update state
            sed -i.bak 's/TDD_PHASE=validation/TDD_PHASE=complete/' "$TDD_STATE_FILE" && rm -f "$TDD_STATE_FILE.bak"
            sed -i.bak 's/TESTS_PASSING=false/TESTS_PASSING=true/' "$TDD_STATE_FILE" && rm -f "$TDD_STATE_FILE.bak"
        else
            echo ""
            echo "❌ Validation failed"
            echo ""
            echo "Fix the issues and run /tdd validate again"
        fi
        ;;
        
    "refactor"|"improve")
        if [[ ! -f "$TDD_STATE_FILE" ]]; then
            echo "❌ No active TDD session. Start with: /tdd start [feature]"
            exit 1
        fi
        
        source "$TDD_STATE_FILE"
        
        if [[ "$TDD_PHASE" != "complete" ]]; then
            echo "❌ Complete TDD cycle first"
            exit 1
        fi
        
        echo "🔧 Refactoring Phase"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Feature: $FEATURE_NAME"
        echo ""
        echo "✅ All tests passing"
        echo "🎯 Now improve the code"
        echo ""
        echo "Refactoring Guidelines:"
        echo "  • Keep all tests green"
        echo "  • Improve code structure"
        echo "  • Remove duplication"
        echo "  • Clarify intent"
        echo "  • Run tests after each change"
        echo ""
        echo "Safe refactoring steps:"
        echo "  1. Run tests (should pass)"
        echo "  2. Make small improvement"
        echo "  3. Run tests again"
        echo "  4. Repeat"
        echo ""
        echo "When done, run: /tdd complete"
        ;;
        
    "complete"|"done"|"finish")
        if [[ ! -f "$TDD_STATE_FILE" ]]; then
            echo "❌ No active TDD session"
            exit 1
        fi
        
        source "$TDD_STATE_FILE"
        
        echo "🏁 TDD Session Complete"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Feature: $FEATURE_NAME"
        echo "Duration: $(($(date +%s) - START_TIME))s"
        echo ""
        echo "✅ Red-Green-Refactor cycle completed"
        echo ""
        echo "📚 What you accomplished:"
        echo "  • Wrote failing tests first"
        echo "  • Implemented minimal solution"
        echo "  • Verified all tests pass"
        echo "  • Maintained code quality"
        echo ""
        
        # Archive TDD state
        mv "$TDD_STATE_FILE" ".tdd-complete-$(date +%Y%m%d-%H%M%S)"
        
        echo "Ready for next TDD cycle!"
        ;;
        
    "status"|"state")
        if [[ ! -f "$TDD_STATE_FILE" ]]; then
            echo "❌ No active TDD session"
            echo "Start with: /tdd start [feature-name]"
            exit 1
        fi
        
        source "$TDD_STATE_FILE"
        
        echo "📊 TDD Status"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Feature: $FEATURE_NAME"
        echo "Phase: $TDD_PHASE"
        echo "Duration: $(($(date +%s) - START_TIME))s"
        echo ""
        echo "Progress:"
        [[ "$TESTS_WRITTEN" == "true" ]] && echo "  ✅ Tests written" || echo "  ⏳ Tests pending"
        [[ "$IMPLEMENTATION_DONE" == "true" ]] && echo "  ✅ Implementation done" || echo "  ⏳ Implementation pending"
        [[ "$TESTS_PASSING" == "true" ]] && echo "  ✅ All tests passing" || echo "  ⏳ Tests not passing"
        echo ""
        case "$TDD_PHASE" in
            "test_creation") echo "Next: Write failing test, then /tdd verify-test" ;;
            "implementation") echo "Next: Implement solution, then /tdd validate" ;;
            "validation") echo "Next: Run /tdd validate to check all tests" ;;
            "complete") echo "Next: Optional /tdd refactor or /tdd complete" ;;
        esac
        ;;
        
    "help"|"--help"|"-h"|*)
        echo "🧪 Test-Driven Development Commands"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Usage: tdd [action] [arguments]"
        echo ""
        echo "TDD Workflow:"
        echo "  start [feature]     Begin TDD for new feature"
        echo "  verify-test         Verify test fails as expected"
        echo "  implement           Move to implementation phase"
        echo "  validate            Run all tests and quality checks"
        echo "  refactor            Improve code while keeping tests green"
        echo "  complete            Finish TDD session"
        echo ""
        echo "Status & Help:"
        echo "  status              Show current TDD state"
        echo "  help                Show this help"
        echo ""
        echo "Example Workflow:"
        echo "  /tdd start user-authentication"
        echo "  # Write failing test"
        echo "  /tdd verify-test"
        echo "  # Implement solution"
        echo "  /tdd validate"
        echo "  # Optional refactoring"
        echo "  /tdd complete"
        echo ""
        echo "💡 TDD Benefits:"
        echo "  • Better design through tests"
        echo "  • Higher code confidence"
        echo "  • Fewer bugs in production"
        echo "  • Living documentation"
        ;;
esac
```

Comprehensive Test-Driven Development workflow with enforcement and guidance through Red-Green-Refactor cycle.
**Security Note:** This command manages TDD workflow state and executes test commands with proper validation.