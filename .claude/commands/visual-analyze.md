Analyze and compare visual components for design consistency and improvements. Execute:
```bash
# Visual analysis and design system tools
ACTION="${1:-help}"
TARGET="${2:-}"

case "$ACTION" in
    "component"|"comp")
        if [[ -z "$TARGET" ]]; then
            echo "❌ Component name required"
            echo "Usage: visual-analyze component [component-name]"
            exit 1
        fi
        
        echo "🔍 Visual Component Analysis: $TARGET"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        # Find component files
        COMPONENT_FILE="apps/web/src/components/$TARGET.tsx"
        STORY_FILE="apps/web/src/components/$TARGET.stories.tsx"
        TEST_FILE="apps/web/src/components/__tests__/$TARGET.test.tsx"
        
        if [[ ! -f "$COMPONENT_FILE" ]]; then
            echo "❌ Component not found: $COMPONENT_FILE"
            echo ""
            echo "Available components:"
            find apps/web/src/components -name "*.tsx" -not -path "*/.*" | grep -v stories | grep -v test | head -10
            exit 1
        fi
        
        echo "📁 Component Files:"
        [[ -f "$COMPONENT_FILE" ]] && echo "  ✅ Component: $COMPONENT_FILE"
        [[ -f "$STORY_FILE" ]] && echo "  ✅ Story: $STORY_FILE" || echo "  ❌ Story: Missing"
        [[ -f "$TEST_FILE" ]] && echo "  ✅ Test: $TEST_FILE" || echo "  ❌ Test: Missing"
        echo ""
        
        # Analyze component structure
        echo "🏗️ Component Structure:"
        
        # Count props
        PROP_COUNT=$(grep -c "interface.*Props\|type.*Props" "$COMPONENT_FILE" 2>/dev/null || echo "0")
        echo "  • Props interfaces: $PROP_COUNT"
        
        # Check for TypeScript
        if grep -q "React.FC\|FunctionComponent" "$COMPONENT_FILE" 2>/dev/null; then
            echo "  • Type: Functional Component (TypeScript)"
        elif grep -q "function.*{" "$COMPONENT_FILE" 2>/dev/null; then
            echo "  • Type: Function Declaration"
        else
            echo "  • Type: Unknown"
        fi
        
        # Check for hooks
        HOOK_USAGE=$(grep -o "use[A-Z][a-zA-Z]*" "$COMPONENT_FILE" 2>/dev/null | sort | uniq | wc -l)
        echo "  • React hooks used: $HOOK_USAGE"
        
        # Check for styling approach
        if grep -q "className.*{" "$COMPONENT_FILE" 2>/dev/null; then
            echo "  • Styling: CSS-in-JS/Modules"
        elif grep -q "tailwind\|tw-" "$COMPONENT_FILE" 2>/dev/null; then
            echo "  • Styling: Tailwind CSS"
        elif grep -q "styled-components\|emotion" "$COMPONENT_FILE" 2>/dev/null; then
            echo "  • Styling: Styled Components"
        else
            echo "  • Styling: Standard CSS"
        fi
        
        echo ""
        
        # Analyze visual patterns
        echo "🎨 Visual Patterns:"
        
        # Color usage
        COLOR_COUNT=$(grep -o "#[0-9a-fA-F]\{6\}\|rgb\|hsl\|var(--" "$COMPONENT_FILE" 2>/dev/null | wc -l)
        echo "  • Color references: $COLOR_COUNT"
        
        # Spacing patterns
        SPACING_COUNT=$(grep -o "padding\|margin\|gap\|space" "$COMPONENT_FILE" 2>/dev/null | wc -l)
        echo "  • Spacing properties: $SPACING_COUNT"
        
        # Typography
        FONT_COUNT=$(grep -o "font-\|text-\|leading-" "$COMPONENT_FILE" 2>/dev/null | wc -l)
        echo "  • Typography classes: $FONT_COUNT"
        
        echo ""
        
        # Story analysis
        if [[ -f "$STORY_FILE" ]]; then
            echo "📚 Storybook Coverage:"
            STORY_COUNT=$(grep -c "export const" "$STORY_FILE" 2>/dev/null || echo "0")
            echo "  • Stories defined: $STORY_COUNT"
            
            # Check for different states
            if grep -q "disabled\|loading\|error" "$STORY_FILE" 2>/dev/null; then
                echo "  • State variations: ✅ Found"
            else
                echo "  • State variations: ❌ Missing"
            fi
            
            # Check for responsive stories
            if grep -q "mobile\|tablet\|desktop" "$STORY_FILE" 2>/dev/null; then
                echo "  • Responsive variants: ✅ Found"
            else
                echo "  • Responsive variants: ❌ Missing"
            fi
        fi
        
        echo ""
        
        # Recommendations
        echo "💡 Recommendations:"
        
        if [[ ! -f "$STORY_FILE" ]]; then
            echo "  • Create Storybook story for visual testing"
        fi
        
        if [[ ! -f "$TEST_FILE" ]]; then
            echo "  • Add unit tests for component behavior"
        fi
        
        if [[ $COLOR_COUNT -gt 5 ]]; then
            echo "  • Consider using design tokens for colors"
        fi
        
        if [[ $STORY_COUNT -lt 3 ]] && [[ -f "$STORY_FILE" ]]; then
            echo "  • Add more story variants (states, sizes, etc.)"
        fi
        
        echo "  • Use /visual-iterate start $TARGET for rapid development"
        ;;
        
    "design-system"|"tokens"|"system")
        echo "🎨 Design System Analysis"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        # Check for design system files
        DESIGN_FILES=(
            "apps/web/src/styles/tokens.css"
            "apps/web/src/styles/globals.css"
            "apps/web/tailwind.config.js"
            "apps/web/src/components/ui"
        )
        
        echo "🏗️ Design System Structure:"
        for file in "${DESIGN_FILES[@]}"; do
            if [[ -f "$file" ]] || [[ -d "$file" ]]; then
                echo "  ✅ $file"
            else
                echo "  ❌ $file (missing)"
            fi
        done
        echo ""
        
        # Analyze color consistency
        echo "🌈 Color Analysis:"
        
        # Find all color references
        COLOR_FILES=$(find apps/web/src -name "*.tsx" -o -name "*.ts" -o -name "*.css" | head -20)
        
        echo "  Scanning for color usage..."
        
        # Extract unique colors
        UNIQUE_COLORS=$(grep -hoE "#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}" $COLOR_FILES 2>/dev/null | sort | uniq | wc -l)
        echo "  • Unique hex colors: $UNIQUE_COLORS"
        
        # Check for CSS variables
        CSS_VARS=$(grep -hoE "var\(--[^)]*\)" $COLOR_FILES 2>/dev/null | sort | uniq | wc -l)
        echo "  • CSS variables: $CSS_VARS"
        
        # Tailwind colors
        if [[ -f "apps/web/tailwind.config.js" ]]; then
            TAILWIND_COLORS=$(grep -c "colors\|color" apps/web/tailwind.config.js 2>/dev/null || echo "0")
            echo "  • Tailwind color config: $TAILWIND_COLORS references"
        fi
        
        echo ""
        
        # Typography analysis
        echo "📝 Typography Analysis:"
        
        FONT_FAMILIES=$(grep -hoE "font-family\|fontFamily" $COLOR_FILES 2>/dev/null | sort | uniq | wc -l)
        echo "  • Font family declarations: $FONT_FAMILIES"
        
        FONT_SIZES=$(grep -hoE "text-[a-z0-9]+\|font-size" $COLOR_FILES 2>/dev/null | sort | uniq | wc -l)
        echo "  • Font size classes: $FONT_SIZES"
        
        echo ""
        
        # Component consistency
        echo "🧩 Component Consistency:"
        
        # Count UI components
        UI_COMPONENTS=$(find apps/web/src/components/ui -name "*.tsx" 2>/dev/null | wc -l)
        echo "  • UI components: $UI_COMPONENTS"
        
        # Button variants
        BUTTON_VARIANTS=$(grep -c "variant\|size" apps/web/src/components/ui/button.tsx 2>/dev/null || echo "0")
        echo "  • Button variants: $BUTTON_VARIANTS"
        
        echo ""
        
        # Recommendations
        echo "💡 Design System Recommendations:"
        
        if [[ $UNIQUE_COLORS -gt 20 ]]; then
            echo "  • Too many unique colors ($UNIQUE_COLORS) - consider design tokens"
        fi
        
        if [[ $CSS_VARS -lt 10 ]]; then
            echo "  • Use more CSS variables for consistent theming"
        fi
        
        if [[ $UI_COMPONENTS -lt 5 ]]; then
            echo "  • Build more reusable UI components"
        fi
        
        echo "  • Run regular design audits with this command"
        echo "  • Consider using a design token system"
        echo "  • Document component usage guidelines"
        ;;
        
    "accessibility"|"a11y")
        if [[ -z "$TARGET" ]]; then
            echo "❌ Component name required"
            echo "Usage: visual-analyze accessibility [component-name]"
            exit 1
        fi
        
        echo "♿ Accessibility Analysis: $TARGET"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        COMPONENT_FILE="apps/web/src/components/$TARGET.tsx"
        
        if [[ ! -f "$COMPONENT_FILE" ]]; then
            echo "❌ Component not found: $COMPONENT_FILE"
            exit 1
        fi
        
        echo "🔍 Scanning: $COMPONENT_FILE"
        echo ""
        
        # ARIA analysis
        echo "🏷️ ARIA Attributes:"
        ARIA_COUNT=$(grep -c "aria-" "$COMPONENT_FILE" 2>/dev/null || echo "0")
        echo "  • ARIA attributes: $ARIA_COUNT"
        
        # Specific ARIA checks
        grep -q "aria-label" "$COMPONENT_FILE" 2>/dev/null && echo "  ✅ aria-label found"
        grep -q "aria-describedby" "$COMPONENT_FILE" 2>/dev/null && echo "  ✅ aria-describedby found"
        grep -q "role=" "$COMPONENT_FILE" 2>/dev/null && echo "  ✅ role attribute found"
        
        echo ""
        
        # Semantic HTML
        echo "🏗️ Semantic HTML:"
        SEMANTIC_TAGS=$(grep -oE "<(header|main|nav|section|article|aside|footer)" "$COMPONENT_FILE" 2>/dev/null | wc -l)
        echo "  • Semantic elements: $SEMANTIC_TAGS"
        
        # Button accessibility
        if grep -q "<button\|Button" "$COMPONENT_FILE" 2>/dev/null; then
            echo "  ✅ Button elements found"
            if ! grep -q "aria-label\|aria-describedby" "$COMPONENT_FILE" 2>/dev/null; then
                echo "  ⚠️  Consider adding ARIA labels to buttons"
            fi
        fi
        
        # Form accessibility
        if grep -q "<input\|<select\|<textarea" "$COMPONENT_FILE" 2>/dev/null; then
            echo "  ✅ Form elements found"
            if ! grep -q "htmlFor\|id=" "$COMPONENT_FILE" 2>/dev/null; then
                echo "  ⚠️  Ensure form labels are properly associated"
            fi
        fi
        
        echo ""
        
        # Keyboard navigation
        echo "⌨️ Keyboard Navigation:"
        TAB_INDEX=$(grep -c "tabIndex" "$COMPONENT_FILE" 2>/dev/null || echo "0")
        echo "  • TabIndex usage: $TAB_INDEX"
        
        FOCUS_HANDLERS=$(grep -c "onFocus\|onBlur\|onKeyDown" "$COMPONENT_FILE" 2>/dev/null || echo "0")
        echo "  • Focus event handlers: $FOCUS_HANDLERS"
        
        echo ""
        
        # Color and contrast
        echo "🌈 Visual Accessibility:"
        
        # Check for color-only information
        if grep -q "color:\|text-red\|text-green\|bg-red\|bg-green" "$COMPONENT_FILE" 2>/dev/null; then
            echo "  ⚠️  Color usage detected - ensure not color-only information"
        fi
        
        # Check for focus indicators
        if grep -q "focus:\|focus-" "$COMPONENT_FILE" 2>/dev/null; then
            echo "  ✅ Focus styles found"
        else
            echo "  ⚠️  Consider adding focus indicators"
        fi
        
        echo ""
        
        # Recommendations
        echo "💡 Accessibility Improvements:"
        
        if [[ $ARIA_COUNT -eq 0 ]]; then
            echo "  • Add appropriate ARIA attributes"
        fi
        
        if [[ $SEMANTIC_TAGS -eq 0 ]]; then
            echo "  • Use semantic HTML elements"
        fi
        
        if [[ $FOCUS_HANDLERS -eq 0 ]]; then
            echo "  • Add keyboard navigation support"
        fi
        
        echo "  • Test with screen reader"
        echo "  • Verify color contrast ratios"
        echo "  • Ensure all interactive elements are focusable"
        echo "  • Use /test frontend --a11y for automated checks"
        ;;
        
    "performance"|"perf")
        if [[ -z "$TARGET" ]]; then
            echo "❌ Component name required"
            echo "Usage: visual-analyze performance [component-name]"
            exit 1
        fi
        
        echo "⚡ Performance Analysis: $TARGET"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        COMPONENT_FILE="apps/web/src/components/$TARGET.tsx"
        
        if [[ ! -f "$COMPONENT_FILE" ]]; then
            echo "❌ Component not found: $COMPONENT_FILE"
            exit 1
        fi
        
        echo "🔍 Analyzing: $COMPONENT_FILE"
        echo ""
        
        # React performance patterns
        echo "⚛️ React Performance:"
        
        # Memoization
        MEMO_USAGE=$(grep -c "React.memo\|useMemo\|useCallback" "$COMPONENT_FILE" 2>/dev/null || echo "0")
        echo "  • Memoization hooks: $MEMO_USAGE"
        
        # State management
        STATE_HOOKS=$(grep -c "useState\|useReducer" "$COMPONENT_FILE" 2>/dev/null || echo "0")
        echo "  • State hooks: $STATE_HOOKS"
        
        # Effect hooks
        EFFECT_HOOKS=$(grep -c "useEffect\|useLayoutEffect" "$COMPONENT_FILE" 2>/dev/null || echo "0")
        echo "  • Effect hooks: $EFFECT_HOOKS"
        
        # Re-render analysis
        if grep -q "console.log\|console.debug" "$COMPONENT_FILE" 2>/dev/null; then
            echo "  ⚠️  Debug logs found (remove for production)"
        fi
        
        echo ""
        
        # Bundle size impact
        echo "📦 Bundle Impact:"
        
        # Import analysis
        IMPORT_COUNT=$(grep -c "^import" "$COMPONENT_FILE" 2>/dev/null || echo "0")
        echo "  • Import statements: $IMPORT_COUNT"
        
        # Heavy imports
        if grep -q "lodash\|moment\|date-fns" "$COMPONENT_FILE" 2>/dev/null; then
            echo "  ⚠️  Heavy dependencies detected"
        fi
        
        # Dynamic imports
        DYNAMIC_IMPORTS=$(grep -c "import(.*)" "$COMPONENT_FILE" 2>/dev/null || echo "0")
        echo "  • Dynamic imports: $DYNAMIC_IMPORTS"
        
        echo ""
        
        # Rendering patterns
        echo "🎨 Rendering Patterns:"
        
        # List rendering
        if grep -q "\.map(" "$COMPONENT_FILE" 2>/dev/null; then
            echo "  ✅ List rendering found"
            if ! grep -q "key=" "$COMPONENT_FILE" 2>/dev/null; then
                echo "  ⚠️  Ensure proper key props for list items"
            fi
        fi
        
        # Conditional rendering
        CONDITIONALS=$(grep -c "&&\|?.*:" "$COMPONENT_FILE" 2>/dev/null || echo "0")
        echo "  • Conditional renders: $CONDITIONALS"
        
        echo ""
        
        # File size
        FILE_SIZE=$(wc -c < "$COMPONENT_FILE" 2>/dev/null || echo "0")
        echo "📊 Component Metrics:"
        echo "  • File size: $FILE_SIZE bytes"
        
        LINE_COUNT=$(wc -l < "$COMPONENT_FILE" 2>/dev/null || echo "0")
        echo "  • Lines of code: $LINE_COUNT"
        
        echo ""
        
        # Recommendations
        echo "💡 Performance Recommendations:"
        
        if [[ $FILE_SIZE -gt 5000 ]]; then
            echo "  • Consider splitting large component ($FILE_SIZE bytes)"
        fi
        
        if [[ $MEMO_USAGE -eq 0 ]] && [[ $STATE_HOOKS -gt 0 ]]; then
            echo "  • Consider React.memo or useMemo for optimization"
        fi
        
        if [[ $EFFECT_HOOKS -gt 3 ]]; then
            echo "  • Multiple useEffect hooks - consider consolidation"
        fi
        
        echo "  • Use React DevTools Profiler for detailed analysis"
        echo "  • Test with React.StrictMode enabled"
        echo "  • Monitor bundle size impact"
        ;;
        
    "compare"|"diff")
        COMPONENT1="${2:-}"
        COMPONENT2="${3:-}"
        
        if [[ -z "$COMPONENT1" ]] || [[ -z "$COMPONENT2" ]]; then
            echo "❌ Two component names required"
            echo "Usage: visual-analyze compare [component1] [component2]"
            exit 1
        fi
        
        echo "🔄 Component Comparison: $COMPONENT1 vs $COMPONENT2"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        FILE1="apps/web/src/components/$COMPONENT1.tsx"
        FILE2="apps/web/src/components/$COMPONENT2.tsx"
        
        if [[ ! -f "$FILE1" ]]; then
            echo "❌ Component not found: $FILE1"
            exit 1
        fi
        
        if [[ ! -f "$FILE2" ]]; then
            echo "❌ Component not found: $FILE2"
            exit 1
        fi
        
        # File metrics comparison
        echo "📊 Metrics Comparison:"
        SIZE1=$(wc -c < "$FILE1")
        SIZE2=$(wc -c < "$FILE2")
        LINES1=$(wc -l < "$FILE1")
        LINES2=$(wc -l < "$FILE2")
        
        printf "  %-20s %10s %10s\n" "Metric" "$COMPONENT1" "$COMPONENT2"
        printf "  %-20s %10s %10s\n" "────────────────────" "──────────" "──────────"
        printf "  %-20s %10d %10d\n" "File size (bytes)" "$SIZE1" "$SIZE2"
        printf "  %-20s %10d %10d\n" "Lines of code" "$LINES1" "$LINES2"
        
        echo ""
        
        # Pattern comparison
        echo "🔍 Pattern Comparison:"
        
        # Hooks usage
        HOOKS1=$(grep -c "use[A-Z]" "$FILE1" 2>/dev/null || echo "0")
        HOOKS2=$(grep -c "use[A-Z]" "$FILE2" 2>/dev/null || echo "0")
        printf "  %-20s %10d %10d\n" "React hooks" "$HOOKS1" "$HOOKS2"
        
        # Props
        PROPS1=$(grep -c "Props\|props\." "$FILE1" 2>/dev/null || echo "0")
        PROPS2=$(grep -c "Props\|props\." "$FILE2" 2>/dev/null || echo "0")
        printf "  %-20s %10d %10d\n" "Props usage" "$PROPS1" "$PROPS2"
        
        # ARIA attributes
        ARIA1=$(grep -c "aria-" "$FILE1" 2>/dev/null || echo "0")
        ARIA2=$(grep -c "aria-" "$FILE2" 2>/dev/null || echo "0")
        printf "  %-20s %10d %10d\n" "ARIA attributes" "$ARIA1" "$ARIA2"
        
        echo ""
        
        # Show actual diff
        echo "📝 Code Differences:"
        echo "Use: /diff $FILE1 $FILE2"
        echo ""
        
        # Recommendations
        echo "💡 Comparison Insights:"
        
        if [[ $SIZE1 -gt $((SIZE2 * 2)) ]]; then
            echo "  • $COMPONENT1 is significantly larger - consider refactoring"
        elif [[ $SIZE2 -gt $((SIZE1 * 2)) ]]; then
            echo "  • $COMPONENT2 is significantly larger - consider refactoring"
        fi
        
        if [[ $ARIA1 -gt $ARIA2 ]]; then
            echo "  • $COMPONENT1 has better accessibility implementation"
        elif [[ $ARIA2 -gt $ARIA1 ]]; then
            echo "  • $COMPONENT2 has better accessibility implementation"
        fi
        
        echo "  • Look for shared patterns that could be extracted"
        echo "  • Consider creating a base component for common functionality"
        ;;
        
    "help"|"--help"|\"-h\"|*)
        echo "🔍 Visual Analysis Commands"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Usage: visual-analyze [action] [arguments]"
        echo ""
        echo "Analysis Types:"
        echo "  component [name]        Deep dive into component structure"
        echo "  design-system          Analyze design tokens and consistency"
        echo "  accessibility [name]    Check a11y compliance"
        echo "  performance [name]      Analyze performance patterns"
        echo "  compare [comp1] [comp2] Compare two components"
        echo ""
        echo "Examples:"
        echo "  /visual-analyze component Button"
        echo "  /visual-analyze design-system"
        echo "  /visual-analyze accessibility UserCard"
        echo "  /visual-analyze performance Dashboard"
        echo "  /visual-analyze compare Button IconButton"
        echo ""
        echo "💡 Features:"
        echo "  • Component structure analysis"
        echo "  • Design system consistency checks"
        echo "  • Accessibility compliance review"
        echo "  • Performance pattern detection"
        echo "  • Cross-component comparison"
        echo ""
        echo "Related Commands:"
        echo "  /visual-iterate start [component]  # Begin visual development"
        echo "  /storybook                        # Launch Storybook"
        echo "  /test frontend [component]        # Test components"
        ;;
esac
```

Comprehensive visual analysis tools for components, design systems, accessibility, and performance optimization.
**Security Note:** This command performs read-only analysis and does not modify files or execute unsafe operations.