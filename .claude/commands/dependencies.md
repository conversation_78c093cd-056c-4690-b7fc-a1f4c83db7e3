List all project dependencies, their purposes, and identify any outdated or security-risk packages. Analyze:

1. **Backend Dependencies**: Python packages in pyproject.toml
2. **Frontend Dependencies**: Node packages in package.json
3. **Security Vulnerabilities**: Check for known security issues
4. **Outdated Packages**: Identify packages that need updates
5. **Dependency Conflicts**: Find potential version conflicts
6. **License Compliance**: Review package licenses

Provide recommendations for updates and security improvements.