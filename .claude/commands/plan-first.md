Enforce planning before implementation to ensure thoughtful development. Execute:
```bash
# Comprehensive planning enforcement
PLAN_TYPE="${1:-feature}"
PLAN_FILE=".claude-plan-$(date +%Y%m%d-%H%M%S).md"

echo "📋 Creating Development Plan"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Check if exploration was done recently
if [[ ! -f ".last-explore" ]] || [[ $(find ".last-explore" -mmin +30 -print 2>/dev/null) ]]; then
    echo "⚠️  No recent exploration detected!"
    echo "💡 Run '/explore' first to understand the codebase"
    echo ""
    read -p "Have you explored the relevant code area? (y/N): " explored
    if [[ ! "$explored" =~ ^[Yy]$ ]]; then
        echo "❌ Planning cancelled. Please explore first!"
        exit 1
    fi
fi

# Create plan based on type
case "$PLAN_TYPE" in
    "bug"|"bugfix"|"fix")
        cat > "$PLAN_FILE" << 'EOF'
# 🐛 Bug Fix Plan

## Issue Description
**Bug**: [Describe the bug clearly]
**Affected Area**: [Component/Service/Feature]
**Severity**: [Critical/High/Medium/Low]

## Root Cause Analysis
- [ ] Identified the root cause
- [ ] Understood why it happens
- [ ] Found related code areas

## Reproduction Steps
1. [Step to reproduce]
2. [Another step]
3. [Expected vs Actual result]

## Solution Approach
- **Fix Strategy**: [How will you fix it?]
- **Test Strategy**: [How will you test it?]
- **Impact Analysis**: [What else might be affected?]

## Implementation Steps
1. [ ] Write failing test that reproduces the bug
2. [ ] Implement the fix
3. [ ] Verify test passes
4. [ ] Check for regressions
5. [ ] Update documentation if needed

## Rollback Plan
[How to rollback if the fix causes issues]

---
⏰ Estimated Time: [X hours]
📊 Confidence Level: [High/Medium/Low]
EOF
        ;;
        
    "feature"|"feat")
        cat > "$PLAN_FILE" << 'EOF'
# ✨ Feature Development Plan

## Feature Overview
**Name**: [Feature name]
**Purpose**: [Why is this needed?]
**Users**: [Who will use this?]

## Requirements
### Functional Requirements
- [ ] [Requirement 1]
- [ ] [Requirement 2]

### Non-Functional Requirements
- [ ] Performance: [Expectations]
- [ ] Security: [Considerations]
- [ ] Accessibility: [Requirements]

## Technical Design
### Architecture
[How does this fit into existing architecture?]

### Components
- **Frontend**: [UI components needed]
- **Backend**: [API endpoints, services]
- **Database**: [Schema changes]

### Dependencies
- External: [3rd party libraries/services]
- Internal: [Existing components to modify]

## Implementation Plan
### Phase 1: Foundation
1. [ ] Create database schema/migrations
2. [ ] Create API endpoints
3. [ ] Write unit tests

### Phase 2: Integration
4. [ ] Create UI components
5. [ ] Connect frontend to backend
6. [ ] Write integration tests

### Phase 3: Polish
7. [ ] Add error handling
8. [ ] Add logging/monitoring
9. [ ] Write documentation

## Testing Strategy
- Unit Tests: [What to test]
- Integration Tests: [Scenarios]
- E2E Tests: [User flows]

## Risks & Mitigation
- **Risk**: [Potential issue]
  **Mitigation**: [How to handle]

---
⏰ Estimated Time: [X days]
📊 Complexity: [High/Medium/Low]
EOF
        ;;
        
    "refactor"|"ref")
        cat > "$PLAN_FILE" << 'EOF'
# 🔧 Refactoring Plan

## Refactoring Overview
**Target**: [What code/component to refactor]
**Reason**: [Why refactor? Tech debt, performance, readability?]

## Current State Analysis
### Problems
- [ ] [Problem 1]
- [ ] [Problem 2]

### Code Smells
- [ ] [Smell 1: e.g., Large functions]
- [ ] [Smell 2: e.g., Duplicate code]

## Desired State
### Goals
- [ ] [Goal 1: e.g., Better separation of concerns]
- [ ] [Goal 2: e.g., Improved testability]

### Success Criteria
- [ ] All tests still pass
- [ ] Performance unchanged or improved
- [ ] Code coverage maintained or increased

## Refactoring Strategy
### Approach
[Incremental vs Big Bang? Why?]

### Steps
1. [ ] Ensure comprehensive test coverage exists
2. [ ] Create feature flag if needed
3. [ ] Refactor in small increments
4. [ ] Run tests after each change
5. [ ] Update documentation

### Specific Changes
- **Extract**: [Functions/Classes to extract]
- **Combine**: [Duplicate code to consolidate]
- **Simplify**: [Complex logic to simplify]
- **Rename**: [Unclear names to improve]

## Safety Measures
- [ ] All changes behind feature flag
- [ ] Incremental commits for easy rollback
- [ ] Performance benchmarks before/after
- [ ] Peer review at each stage

---
⏰ Estimated Time: [X hours]
⚠️  Risk Level: [High/Medium/Low]
EOF
        ;;
        
    "performance"|"perf"|"optimize")
        cat > "$PLAN_FILE" << 'EOF'
# 🚀 Performance Optimization Plan

## Performance Issue
**Problem**: [What is slow?]
**Target Metric**: [What needs improvement?]
**Current Performance**: [Baseline measurements]
**Goal**: [Target performance]

## Root Cause Analysis
### Profiling Results
- [ ] Database queries analyzed
- [ ] CPU profiling completed
- [ ] Memory usage checked
- [ ] Network calls reviewed

### Bottlenecks Identified
1. [Bottleneck 1]
2. [Bottleneck 2]

## Optimization Strategy
### Quick Wins
- [ ] [Easy optimization 1]
- [ ] [Easy optimization 2]

### Major Changes
- [ ] [Significant change 1]
- [ ] [Significant change 2]

## Implementation Plan
1. [ ] Create performance benchmarks
2. [ ] Implement quick wins
3. [ ] Measure improvement
4. [ ] Implement major changes incrementally
5. [ ] Validate no functional regression

## Testing & Validation
- [ ] Load testing scenarios
- [ ] Performance benchmarks
- [ ] User experience testing
- [ ] Resource monitoring

## Rollback Strategy
[How to rollback if performance degrades in other areas]

---
⏰ Estimated Time: [X days]
📊 Expected Improvement: [X%]
EOF
        ;;
        
    *)
        echo "❌ Unknown plan type: $PLAN_TYPE"
        echo ""
        echo "Available plan types:"
        echo "  • bug/bugfix/fix - Bug fix planning"
        echo "  • feature/feat - New feature planning"
        echo "  • refactor/ref - Refactoring planning"
        echo "  • performance/perf - Performance optimization"
        exit 1
        ;;
esac

echo ""
echo "📝 Plan template created: $PLAN_FILE"
echo ""
echo "Please edit the plan to fill in details:"
echo "  1. Open $PLAN_FILE in your editor"
echo "  2. Fill in all sections thoughtfully"
echo "  3. Save the file when complete"
echo ""
echo "Once your plan is ready, I'll review it before we proceed with implementation."
echo ""
echo "💡 Tips for good planning:"
echo "  • Be specific about requirements"
echo "  • Consider edge cases"
echo "  • Think about testing strategy"
echo "  • Identify risks early"
echo "  • Break work into small steps"

# Mark that exploration should be done before next plan
touch .last-explore

echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🎯 Next Steps:"
echo "  1. Complete the plan in $PLAN_FILE"
echo "  2. Review with '/read $PLAN_FILE'"
echo "  3. Start implementation with TDD approach"
echo "  4. Use '/checkpoint' to save progress"
```

Create a structured plan before any implementation. Enforces thoughtful development and reduces rework.
**Security Note:** This command creates planning documents only and makes no code modifications.