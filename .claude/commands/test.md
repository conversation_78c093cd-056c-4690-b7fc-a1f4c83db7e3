Run tests with intuitive semantics and smart defaults. Execute:
```bash
# Common error handling function
check_test_results() {
  local test_name="$1"
  local frontend_failed="${2:-0}"
  local backend_failed="${3:-0}"
  
  if [[ $frontend_failed == 1 ]] || [[ $backend_failed == 1 ]]; then
    echo "❌ Some ${test_name} failed"
    exit 1
  fi
  echo "✅ All ${test_name} passed!"
}

# Unified test command with clear scope semantics
case "$1" in
  # Cross-application test types (most intuitive)
  "unit"|"u")
    shift
    echo "🧪 Running ALL unit tests (frontend + backend)..."
    echo "📦 Frontend unit tests:"
    ./scripts/run-frontend-tests.sh --unit "$@" || FRONTEND_FAILED=1
    echo ""
    echo "🐍 Backend unit tests:"
    ./scripts/run-backend-tests.sh --unit "$@" || BACKEND_FAILED=1
    
    check_test_results "unit tests" "$FRONTEND_FAILED" "$BACKEND_FAILED"
    ;;
    
  "integration"|"i")
    shift
    echo "🔗 Running ALL integration tests (frontend + backend)..."
    echo "📦 Frontend integration tests:"
    ./scripts/run-frontend-tests.sh --integration "$@" || FRONTEND_FAILED=1
    echo ""
    echo "🐍 Backend integration tests:"
    ./scripts/run-backend-tests.sh --integration "$@" || BACKEND_FAILED=1
    
    check_test_results "integration tests" "$FRONTEND_FAILED" "$BACKEND_FAILED"
    ;;
    
  "e2e")
    shift
    echo "🌐 Running ALL E2E tests..."
    # Frontend E2E tests (primary)
    ./scripts/run-frontend-tests.sh --e2e "$@" || FRONTEND_FAILED=1
    
    check_test_results "E2E tests" "$FRONTEND_FAILED" "0"
    ;;
  
  # Application scope (all test types for one app)
  "backend"|"b")
    # Check for specific test type as second argument
    case "$2" in
      "unit"|"u")
        shift 2
        echo "🐍 Running backend unit tests only..."
        ./scripts/run-backend-tests.sh --unit "$@"
        ;;
      "integration"|"i")
        shift 2
        echo "🐍 Running backend integration tests only..."
        ./scripts/run-backend-tests.sh --integration "$@"
        ;;
      "e2e")
        shift 2
        echo "🐍 Running backend e2e tests only..."
        ./scripts/run-backend-tests.sh --e2e "$@"
        ;;
      *)
        shift
        echo "🐍 Running ALL backend tests (unit + integration + e2e)..."
        ./scripts/run-backend-tests.sh "$@"
        ;;
    esac
    ;;
    
  "frontend"|"f")
    # Check for specific test type as second argument
    case "$2" in
      "unit"|"u")
        shift 2
        echo "📦 Running frontend unit tests only..."
        ./scripts/run-frontend-tests.sh --unit "$@"
        ;;
      "integration"|"i")
        shift 2
        echo "📦 Running frontend integration tests only..."
        ./scripts/run-frontend-tests.sh --integration "$@"
        ;;
      "e2e")
        shift 2
        echo "📦 Running frontend e2e tests only..."
        ./scripts/run-frontend-tests.sh --e2e "$@"
        ;;
      *)
        shift
        echo "📦 Running ALL frontend tests (unit + integration + e2e)..."
        ./scripts/run-frontend-tests.sh "$@"
        ;;
    esac
    ;;
  
  # Special modes
  "all")
    shift
    echo "🚀 Running COMPLETE test suite (everything)..."
    echo "📦 Frontend tests:"
    ./scripts/run-frontend-tests.sh "$@" || FRONTEND_FAILED=1
    echo ""
    echo "🐍 Backend tests:"
    ./scripts/run-backend-tests.sh "$@" || BACKEND_FAILED=1
    
    check_test_results "tests" "$FRONTEND_FAILED" "$BACKEND_FAILED"
    ;;
    
  "coverage"|"cov")
    shift
    echo "📊 Running tests with coverage..."
    ./scripts/run-backend-tests.sh --coverage "$@"
    ;;
    
  "watch"|"w")
    shift
    echo "👀 Running tests in watch mode..."
    ./scripts/run-frontend-tests.sh --watch "$@"
    ;;
    
  "parallel"|"p")
    shift
    echo "⚡ Running tests in parallel..."
    ./scripts/parallel-test-runner.sh "$@"
    ;;
    
  "smart"|"s")
    echo "🎯 Running smart test selection..."
    ./scripts/smart-test-selector.sh
    ;;
    
  "graphql"|"gql")
    echo "🔮 Running GraphQL tests..."
    ./scripts/run-backend-tests.sh tests/integration/graphql/
    ;;
  
  # Help and file handling
  "help"|"--help"|"-h")
    echo "Usage: test [scope] [type] [options]"
    echo ""
    echo "🎯 Test Types (Cross-Application):"
    echo "  unit, u              All unit tests (frontend + backend)"
    echo "  integration, i       All integration tests (frontend + backend)"
    echo "  e2e                  All end-to-end tests"
    echo ""
    echo "📱 Application Scope:"
    echo "  backend, b           All backend tests"
    echo "  frontend, f          All frontend tests"
    echo "  all                  Everything (complete test suite)"
    echo ""
    echo "🔧 Specific Combinations:"
    echo "  backend unit         Backend unit tests only"
    echo "  backend integration  Backend integration tests only"
    echo "  backend e2e          Backend e2e tests only"
    echo "  frontend unit        Frontend unit tests only"
    echo "  frontend integration Frontend integration tests only"
    echo "  frontend e2e         Frontend e2e tests only"
    echo ""
    echo "⚡ Special Modes:"
    echo "  coverage, cov        Run with coverage reporting"
    echo "  watch, w             Run in watch mode"
    echo "  parallel, p          Run tests in parallel"
    echo "  smart, s             Run only affected tests"
    echo "  graphql, gql         Run GraphQL tests"
    echo ""
    echo "💡 Common Workflows:"
    echo "  test                 # Quick feedback (backend unit tests)"
    echo "  test unit            # All unit tests before commit"
    echo "  test all             # Full confidence before PR"
    echo "  test backend         # Test backend changes"
    echo "  test watch           # Development with auto-reload"
    echo "  test coverage        # Check test coverage"
    ;;
    
  # Default and file handling
  "")
    # Smart default: backend unit tests for quick feedback
    echo "⚡ Quick test (backend unit tests)..."
    ./scripts/run-backend-tests.sh --unit
    ;;
    
  *)
    if [[ -f "$1" ]] || [[ "$1" == *"test"* ]] || [[ "$1" == *".py" ]] || [[ "$1" == *".ts"* ]]; then
      # Run specific test file - detect frontend vs backend
      if [[ "$1" == *".ts"* ]] || [[ "$1" == *".tsx"* ]] || [[ "$1" == *"apps/web"* ]]; then
        echo "📦 Running specific frontend test: $1"
        cd apps/web && bun test "$@"
      else
        echo "🐍 Running specific backend test: $1"
        ./scripts/run-backend-tests.sh "$@"
      fi
    else
      echo "❌ Unknown test target: $1"
      echo "💡 Use 'test help' to see available options"
      exit 1
    fi
    ;;
esac
```
Unified test command with intuitive semantics and smart defaults. 

**Quick Reference:**
- `test` → Quick feedback (backend unit tests)
- `test unit` → ALL unit tests (frontend + backend) 
- `test all` → Complete test suite
- `test backend` → All backend tests
- `test frontend` → All frontend tests

**Security Note:** This command orchestrates test scripts that handle their own validation and security checks.