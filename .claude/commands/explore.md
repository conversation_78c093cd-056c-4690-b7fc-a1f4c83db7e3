Deep dive into any area of the codebase before making changes. Execute:
```bash
# Comprehensive codebase exploration
TARGET="${1:-.}"
DEPTH="${2:-3}"

echo "🔍 Exploring: $TARGET"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# 1. Directory Structure
echo ""
echo "📁 Directory Structure (depth: $DEPTH):"
find "$TARGET" -type d -name "node_modules" -prune -o -type d -name ".git" -prune -o -type d -name "__pycache__" -prune -o -type d -print 2>/dev/null | head -20 | sed 's|^|  |'

# 2. Key Files
echo ""
echo "📄 Key Files:"
find "$TARGET" -type f \( -name "*.py" -o -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" \) -not -path "*/node_modules/*" -not -path "*/.git/*" 2>/dev/null | head -15 | sed 's|^|  |'

# 3. Configuration Files
echo ""
echo "⚙️  Configuration Files:"
find "$TARGET" -maxdepth 2 -type f \( -name "package.json" -o -name "pyproject.toml" -o -name "tsconfig.json" -o -name ".env*" -o -name "docker-compose*.yml" \) 2>/dev/null | sed 's|^|  |'

# 4. Import Analysis (if Python file)
if [[ -f "$TARGET" ]] && [[ "$TARGET" == *.py ]]; then
    echo ""
    echo "📦 Python Imports:"
    grep -E "^(import|from)" "$TARGET" 2>/dev/null | sort -u | head -10 | sed 's|^|  |'
fi

# 5. Import Analysis (if TypeScript/JavaScript file)
if [[ -f "$TARGET" ]] && [[ "$TARGET" == *.ts || "$TARGET" == *.tsx || "$TARGET" == *.js || "$TARGET" == *.jsx ]]; then
    echo ""
    echo "📦 JavaScript/TypeScript Imports:"
    grep -E "^import|^const.*require" "$TARGET" 2>/dev/null | head -10 | sed 's|^|  |'
fi

# 6. Related Files
echo ""
echo "🔗 Related Files (by name similarity):"
if [[ -f "$TARGET" ]]; then
    BASE_NAME=$(basename "$TARGET" | sed 's/\.[^.]*$//')
    find "$(dirname "$TARGET")" -name "*${BASE_NAME}*" -not -path "*/node_modules/*" 2>/dev/null | grep -v "^$TARGET$" | head -5 | sed 's|^|  |'
else
    echo "  (Analyzing directory - checking for index files)"
    find "$TARGET" -name "index.*" -not -path "*/node_modules/*" 2>/dev/null | head -5 | sed 's|^|  |'
fi

# 7. Recent Git Activity
if git rev-parse --git-dir > /dev/null 2>&1; then
    echo ""
    echo "📊 Recent Git Activity:"
    git log --oneline --max-count=5 -- "$TARGET" 2>/dev/null | sed 's|^|  |'
fi

# 8. Pattern Detection
echo ""
echo "🎯 Detected Patterns:"

# Check for test files
if find "$TARGET" -name "*test*" -o -name "*spec*" 2>/dev/null | grep -q .; then
    echo "  ✓ Test files present"
fi

# Check for TypeScript
if find "$TARGET" -name "*.ts" -o -name "*.tsx" 2>/dev/null | grep -q .; then
    echo "  ✓ TypeScript project"
fi

# Check for Python
if find "$TARGET" -name "*.py" 2>/dev/null | grep -q .; then
    echo "  ✓ Python project"
fi

# Check for Docker
if find "$TARGET" -name "Dockerfile" -o -name "docker-compose*.yml" 2>/dev/null | grep -q .; then
    echo "  ✓ Docker configuration present"
fi

# 9. Documentation
echo ""
echo "📚 Documentation:"
find "$TARGET" -name "README*" -o -name "*.md" -not -path "*/node_modules/*" 2>/dev/null | head -5 | sed 's|^|  |'

# 10. Suggestions
echo ""
echo "💡 Exploration Suggestions:"
echo "  • Use '/grep \"pattern\" $TARGET' to search for specific code"
echo "  • Use '/test $TARGET' to run related tests"
echo "  • Use '/read [file]' to examine specific files"
echo "  • Use '/track-decision' to document architectural choices"

echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🎯 Next Steps:"
echo "  1. Read key files to understand implementation"
echo "  2. Check tests to understand expected behavior"
echo "  3. Review recent commits for context"
echo "  4. Plan changes with '/plan-first'"
```

Deep analysis of codebase structure, patterns, and relationships. Always use before making changes.
**Security Note:** This command only reads and analyzes files, making no modifications.