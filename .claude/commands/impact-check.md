Analyze the impact of proposed changes on files, tests, and dependencies. Execute:
```bash
# Impact analysis system for understanding change effects
ACTION="${1:-help}"
TARGET="${2:-}"
IMPACT_CACHE_DIR=".claude-impact-cache"
DEPENDENCY_MAP="$IMPACT_CACHE_DIR/dependency-map.json"

case "$ACTION" in
    "file"|"analyze")
        if [[ -z "$TARGET" ]]; then
            echo "❌ File path required"
            echo "Usage: impact-check file [path/to/file]"
            exit 1
        fi
        
        echo "🔍 Impact Analysis: $TARGET"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        # Check if file exists
        if [[ ! -f "$TARGET" ]]; then
            echo "❌ File not found: $TARGET"
            exit 1
        fi
        
        # Create cache directory
        mkdir -p "$IMPACT_CACHE_DIR"
        
        # Determine file type and language
        FILE_EXT="${TARGET##*.}"
        FILE_NAME=$(basename "$TARGET")
        FILE_DIR=$(dirname "$TARGET")
        
        echo "📁 File Information:"
        echo "  • Path: $TARGET"
        echo "  • Type: .$FILE_EXT"
        echo "  • Directory: $FILE_DIR"
        echo ""
        
        # Analyze direct imports/dependencies
        echo "📊 Direct Dependencies:"
        
        case "$FILE_EXT" in
            "ts"|"tsx"|"js"|"jsx")
                # TypeScript/JavaScript analysis
                echo "  Analyzing TypeScript/JavaScript dependencies..."
                
                # Find imports
                IMPORTS=$(grep -E "^import|^const.*require" "$TARGET" 2>/dev/null | head -10)
                if [[ -n "$IMPORTS" ]]; then
                    echo "$IMPORTS" | sed 's/^/    /'
                fi
                
                # Find files that import this file
                echo ""
                echo "📥 Files That Import This Module:"
                
                # Extract possible import patterns
                MODULE_NAME=$(basename "$TARGET" .tsx)
                MODULE_NAME=$(basename "$MODULE_NAME" .ts)
                
                # Search for imports of this file
                IMPORTERS=$(find . -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | \
                    xargs grep -l "$MODULE_NAME" 2>/dev/null | \
                    grep -v "$TARGET" | \
                    grep -v node_modules | \
                    head -10)
                
                if [[ -n "$IMPORTERS" ]]; then
                    echo "$IMPORTERS" | sed 's/^/    • /'
                else
                    echo "    • No direct importers found"
                fi
                ;;
                
            "py")
                # Python analysis
                echo "  Analyzing Python dependencies..."
                
                # Find imports
                IMPORTS=$(grep -E "^import|^from.*import" "$TARGET" 2>/dev/null | head -10)
                if [[ -n "$IMPORTS" ]]; then
                    echo "$IMPORTS" | sed 's/^/    /'
                fi
                
                # Find files that import this module
                echo ""
                echo "📥 Files That Import This Module:"
                
                MODULE_NAME=$(basename "$TARGET" .py)
                IMPORTERS=$(find . -name "*.py" | \
                    xargs grep -l "from.*$MODULE_NAME.*import\|import.*$MODULE_NAME" 2>/dev/null | \
                    grep -v "$TARGET" | \
                    grep -v __pycache__ | \
                    head -10)
                
                if [[ -n "$IMPORTERS" ]]; then
                    echo "$IMPORTERS" | sed 's/^/    • /'
                else
                    echo "    • No direct importers found"
                fi
                ;;
        esac
        
        echo ""
        
        # Find related test files
        echo "🧪 Related Test Files:"
        
        # Common test file patterns
        TEST_PATTERNS=(
            "${FILE_NAME%.tsx}.test.tsx"
            "${FILE_NAME%.ts}.test.ts"
            "${FILE_NAME%.jsx}.test.jsx"
            "${FILE_NAME%.js}.test.js"
            "${FILE_NAME%.py}_test.py"
            "test_${FILE_NAME%.py}.py"
            "${FILE_NAME%.tsx}.spec.tsx"
            "${FILE_NAME%.ts}.spec.ts"
        )
        
        FOUND_TESTS=false
        for pattern in "${TEST_PATTERNS[@]}"; do
            # Look in common test directories
            TEST_LOCATIONS=(
                "$FILE_DIR/__tests__/$pattern"
                "$FILE_DIR/../__tests__/$pattern"
                "$FILE_DIR/tests/$pattern"
                "$FILE_DIR/../tests/$pattern"
                "$FILE_DIR/$pattern"
                "tests/$pattern"
                "test/$pattern"
            )
            
            for location in "${TEST_LOCATIONS[@]}"; do
                if [[ -f "$location" ]]; then
                    echo "  • $location"
                    FOUND_TESTS=true
                fi
            done
        done
        
        if [[ "$FOUND_TESTS" == "false" ]]; then
            # Try to find tests that might reference this file
            FILE_BASENAME=$(basename "$TARGET")
            POTENTIAL_TESTS=$(find . -name "*test*" -o -name "*spec*" | \
                xargs grep -l "$FILE_BASENAME" 2>/dev/null | \
                grep -v node_modules | \
                grep -v __pycache__ | \
                head -5)
            
            if [[ -n "$POTENTIAL_TESTS" ]]; then
                echo "$POTENTIAL_TESTS" | sed 's/^/  • /'
            else
                echo "  • No test files found"
            fi
        fi
        
        echo ""
        
        # Component/Class analysis
        echo "🏗️ Exported Elements:"
        
        case "$FILE_EXT" in
            "ts"|"tsx"|"js"|"jsx")
                # Find exports
                EXPORTS=$(grep -E "^export|export {|export default" "$TARGET" 2>/dev/null | head -10)
                if [[ -n "$EXPORTS" ]]; then
                    echo "$EXPORTS" | sed 's/^/  /'
                else
                    echo "  • No exports found"
                fi
                ;;
                
            "py")
                # Find classes and functions
                ELEMENTS=$(grep -E "^class |^def |^async def " "$TARGET" 2>/dev/null | head -10)
                if [[ -n "$ELEMENTS" ]]; then
                    echo "$ELEMENTS" | sed 's/^/  /'
                else
                    echo "  • No classes or functions found"
                fi
                ;;
        esac
        
        echo ""
        
        # Impact summary
        echo "💥 Potential Impact:"
        
        # Count dependencies
        IMPORT_COUNT=$(echo "$IMPORTERS" | grep -v "^$" | wc -l)
        
        if [[ $IMPORT_COUNT -gt 5 ]]; then
            echo "  ⚠️  HIGH IMPACT - $IMPORT_COUNT+ files depend on this module"
        elif [[ $IMPORT_COUNT -gt 2 ]]; then
            echo "  ⚡ MEDIUM IMPACT - $IMPORT_COUNT files depend on this module"
        elif [[ $IMPORT_COUNT -gt 0 ]]; then
            echo "  ✓ LOW IMPACT - $IMPORT_COUNT file(s) depend on this module"
        else
            echo "  ✓ MINIMAL IMPACT - No direct dependencies found"
        fi
        
        # Suggest actions
        echo ""
        echo "🎯 Recommended Actions:"
        echo "  1. Review all importing files before making changes"
        echo "  2. Run related tests after modifications"
        echo "  3. Consider backward compatibility for exported elements"
        echo "  4. Update documentation if public API changes"
        
        # Save impact analysis
        cat > "$IMPACT_CACHE_DIR/last-impact-$$.json" << EOF
{
  "file": "$TARGET",
  "timestamp": "$(date -Iseconds)",
  "importers": $(echo "$IMPORTERS" | grep -v "^$" | wc -l),
  "has_tests": $FOUND_TESTS,
  "file_type": "$FILE_EXT"
}
EOF
        ;;
        
    "function"|"method")
        TARGET_FILE="${2:-}"
        FUNCTION_NAME="${3:-}"
        
        if [[ -z "$TARGET_FILE" ]] || [[ -z "$FUNCTION_NAME" ]]; then
            echo "❌ File path and function name required"
            echo "Usage: impact-check function [file] [function-name]"
            exit 1
        fi
        
        echo "🔍 Function Impact Analysis: $FUNCTION_NAME"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "📁 Source: $TARGET_FILE"
        echo ""
        
        # Search for function usage across codebase
        echo "📍 Function Usage Locations:"
        
        # Find all calls to this function
        USAGE_LOCATIONS=$(find . -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" -o -name "*.py" | \
            xargs grep -n "$FUNCTION_NAME(" 2>/dev/null | \
            grep -v "$TARGET_FILE" | \
            grep -v node_modules | \
            grep -v __pycache__ | \
            head -15)
        
        if [[ -n "$USAGE_LOCATIONS" ]]; then
            echo "$USAGE_LOCATIONS" | while IFS=: read -r file line content; do
                echo "  • $file:$line"
                echo "    $(echo "$content" | sed 's/^[[:space:]]*//' | head -c 60)..."
            done
        else
            echo "  • No usage found in codebase"
        fi
        
        echo ""
        
        # Check for test coverage
        echo "🧪 Test Coverage:"
        
        TEST_REFS=$(find . -name "*test*" -o -name "*spec*" | \
            xargs grep -l "$FUNCTION_NAME" 2>/dev/null | \
            grep -v node_modules | \
            head -5)
        
        if [[ -n "$TEST_REFS" ]]; then
            echo "$TEST_REFS" | sed 's/^/  • /'
        else
            echo "  • No test references found"
            echo "  ⚠️  Consider adding tests for this function"
        fi
        ;;
        
    "component"|"class")
        TARGET_FILE="${2:-}"
        COMPONENT_NAME="${3:-}"
        
        if [[ -z "$TARGET_FILE" ]] || [[ -z "$COMPONENT_NAME" ]]; then
            echo "❌ File path and component/class name required"
            echo "Usage: impact-check component [file] [component-name]"
            exit 1
        fi
        
        echo "🔍 Component/Class Impact Analysis: $COMPONENT_NAME"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "📁 Source: $TARGET_FILE"
        echo ""
        
        # Find component usage
        echo "📍 Component Usage:"
        
        # For React components, look for JSX usage
        COMPONENT_USAGE=$(find . -name "*.tsx" -o -name "*.jsx" | \
            xargs grep -n "<$COMPONENT_NAME\|<$COMPONENT_NAME " 2>/dev/null | \
            grep -v "$TARGET_FILE" | \
            grep -v node_modules | \
            head -10)
        
        if [[ -n "$COMPONENT_USAGE" ]]; then
            echo "$COMPONENT_USAGE" | while IFS=: read -r file line content; do
                echo "  • $file:$line"
            done
        else
            # Try Python class usage
            CLASS_USAGE=$(find . -name "*.py" | \
                xargs grep -n "$COMPONENT_NAME(" 2>/dev/null | \
                grep -v "$TARGET_FILE" | \
                grep -v __pycache__ | \
                head -10)
            
            if [[ -n "$CLASS_USAGE" ]]; then
                echo "$CLASS_USAGE" | while IFS=: read -r file line content; do
                    echo "  • $file:$line"
                done
            else
                echo "  • No direct usage found"
            fi
        fi
        
        echo ""
        
        # Props/Interface analysis
        echo "🔧 Interface Analysis:"
        
        # Look for prop types or interfaces
        grep -A 5 -B 2 "interface.*${COMPONENT_NAME}Props\|type.*${COMPONENT_NAME}Props" "$TARGET_FILE" 2>/dev/null || \
            echo "  • No props interface found"
        
        echo ""
        
        # Inheritance/Extension check
        echo "🧬 Inheritance Chain:"
        
        # Check if this component extends others
        EXTENDS=$(grep -E "extends |implements " "$TARGET_FILE" | grep "$COMPONENT_NAME" | head -3)
        if [[ -n "$EXTENDS" ]]; then
            echo "$EXTENDS" | sed 's/^/  /'
        fi
        
        # Check if other components extend this
        EXTENDED_BY=$(find . -name "*.tsx" -o -name "*.ts" -o -name "*.py" | \
            xargs grep -l "extends.*$COMPONENT_NAME\|implements.*$COMPONENT_NAME" 2>/dev/null | \
            grep -v "$TARGET_FILE" | \
            head -5)
        
        if [[ -n "$EXTENDED_BY" ]]; then
            echo "  Extended by:"
            echo "$EXTENDED_BY" | sed 's/^/    • /'
        fi
        ;;
        
    "changes"|"diff")
        echo "🔍 Analyzing Current Changes Impact"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        # Get list of modified files
        MODIFIED_FILES=$(git status --porcelain 2>/dev/null | grep -E "^ M|^M" | awk '{print $2}')
        
        if [[ -z "$MODIFIED_FILES" ]]; then
            echo "ℹ️  No modified files detected"
            echo "This command analyzes the impact of uncommitted changes."
            exit 0
        fi
        
        echo "📝 Modified Files:"
        echo "$MODIFIED_FILES" | sed 's/^/  • /'
        echo ""
        
        # Analyze each modified file
        echo "💥 Impact Analysis:"
        echo ""
        
        TOTAL_IMPACT=0
        
        echo "$MODIFIED_FILES" | while read -r file; do
            if [[ -f "$file" ]]; then
                echo "📁 $file"
                
                # Find importers
                FILE_NAME=$(basename "$file")
                MODULE_NAME="${FILE_NAME%.*}"
                
                IMPORTERS=$(find . -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" -o -name "*.py" | \
                    xargs grep -l "$MODULE_NAME" 2>/dev/null | \
                    grep -v "$file" | \
                    grep -v node_modules | \
                    wc -l)
                
                echo "  • Imported by: $IMPORTERS files"
                
                # Check for test files
                TEST_COUNT=$(find . -name "*test*" -o -name "*spec*" | \
                    xargs grep -l "$FILE_NAME" 2>/dev/null | \
                    wc -l)
                
                echo "  • Test files: $TEST_COUNT"
                
                # Show specific changes
                CHANGES=$(git diff --stat "$file" 2>/dev/null | tail -1)
                echo "  • Changes: $CHANGES"
                
                TOTAL_IMPACT=$((TOTAL_IMPACT + IMPORTERS))
                echo ""
            fi
        done
        
        # Summary
        echo "📊 Impact Summary:"
        echo "  • Total files modified: $(echo "$MODIFIED_FILES" | wc -l)"
        echo "  • Total dependent files: $TOTAL_IMPACT"
        
        if [[ $TOTAL_IMPACT -gt 10 ]]; then
            echo "  • Risk Level: ⚠️  HIGH - Many dependencies affected"
        elif [[ $TOTAL_IMPACT -gt 5 ]]; then
            echo "  • Risk Level: ⚡ MEDIUM - Several dependencies affected"
        else
            echo "  • Risk Level: ✓ LOW - Few dependencies affected"
        fi
        
        echo ""
        echo "🧪 Recommended Tests to Run:"
        
        # Find all test files that might be affected
        echo "$MODIFIED_FILES" | while read -r file; do
            FILE_NAME=$(basename "$file")
            TEST_FILES=$(find . -name "*test*" -o -name "*spec*" | \
                xargs grep -l "$FILE_NAME" 2>/dev/null | \
                head -3)
            
            if [[ -n "$TEST_FILES" ]]; then
                echo "$TEST_FILES" | sed 's/^/  • /'
            fi
        done
        ;;
        
    "project"|"full")
        echo "🔍 Full Project Impact Analysis"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "📊 Building dependency graph..."
        
        mkdir -p "$IMPACT_CACHE_DIR"
        
        # Analyze project structure
        echo ""
        echo "📁 Project Structure Analysis:"
        
        # Count files by type
        TS_COUNT=$(find . -name "*.ts" -o -name "*.tsx" | grep -v node_modules | wc -l)
        PY_COUNT=$(find . -name "*.py" | grep -v __pycache__ | wc -l)
        TEST_COUNT=$(find . -name "*test*" -o -name "*spec*" | grep -v node_modules | wc -l)
        
        echo "  • TypeScript/React files: $TS_COUNT"
        echo "  • Python files: $PY_COUNT"
        echo "  • Test files: $TEST_COUNT"
        echo ""
        
        # Find highly connected modules
        echo "🔗 Highly Connected Modules:"
        echo "  (Files imported by many others)"
        echo ""
        
        # Create temporary file for import counts
        TEMP_IMPORT_FILE=$(mktemp)
        
        # Analyze TypeScript/JavaScript imports
        if [[ $TS_COUNT -gt 0 ]]; then
            find . -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | \
                grep -v node_modules | \
                while read -r file; do
                    IMPORTS=$(grep -E "^import.*from|require\(" "$file" 2>/dev/null | \
                        grep -oE "'[^']+'\"|\"[^\"]+\"" | \
                        tr -d "'\""")
                    
                    echo "$IMPORTS" | while read -r import; do
                        [[ -n "$import" ]] && echo "$import"
                    done
                done | sort | uniq -c | sort -rn | head -10 > "$TEMP_IMPORT_FILE"
            
            if [[ -s "$TEMP_IMPORT_FILE" ]]; then
                cat "$TEMP_IMPORT_FILE" | while read -r count module; do
                    echo "  • $module (imported $count times)"
                done
            fi
        fi
        
        echo ""
        
        # Find entry points
        echo "🚪 Entry Points:"
        ENTRY_POINTS=$(find . -name "main.*" -o -name "index.*" -o -name "app.*" | \
            grep -v node_modules | \
            grep -v __pycache__ | \
            head -10)
        
        if [[ -n "$ENTRY_POINTS" ]]; then
            echo "$ENTRY_POINTS" | sed 's/^/  • /'
        else
            echo "  • No standard entry points found"
        fi
        
        echo ""
        
        # Critical paths
        echo "⚡ Critical Paths:"
        echo "  (Changes here affect many files)"
        
        # Look for core modules
        CORE_PATTERNS=(
            "*/models/*"
            "*/services/*"
            "*/utils/*"
            "*/types/*"
            "*/schemas/*"
            "*/components/ui/*"
            "*/api/*"
        )
        
        for pattern in "${CORE_PATTERNS[@]}"; do
            MATCHES=$(find . -path "$pattern" -name "*.ts" -o -path "$pattern" -name "*.tsx" -o -path "$pattern" -name "*.py" | \
                grep -v node_modules | \
                head -3)
            
            if [[ -n "$MATCHES" ]]; then
                echo "  $pattern:"
                echo "$MATCHES" | sed 's/^/    • /'
            fi
        done
        
        echo ""
        
        # Test coverage overview
        echo "🧪 Test Coverage Overview:"
        
        # Find components/modules without tests
        echo "  Components without tests:"
        
        # For TypeScript/React
        find . -name "*.tsx" | grep -v test | grep -v node_modules | while read -r component; do
            COMPONENT_NAME=$(basename "$component" .tsx)
            if ! find . -name "*test*" -o -name "*spec*" | xargs grep -q "$COMPONENT_NAME" 2>/dev/null; then
                echo "    ⚠️  $component"
            fi
        done | head -5
        
        echo ""
        
        # Save project analysis
        cat > "$IMPACT_CACHE_DIR/project-analysis.json" << EOF
{
  "timestamp": "$(date -Iseconds)",
  "typescript_files": $TS_COUNT,
  "python_files": $PY_COUNT,
  "test_files": $TEST_COUNT,
  "analysis_complete": true
}
EOF
        
        echo "💡 Recommendations:"
        echo "  1. Focus testing on highly connected modules"
        echo "  2. Be extra careful when modifying core paths"
        echo "  3. Ensure critical modules have comprehensive tests"
        echo "  4. Consider splitting highly coupled modules"
        
        # Clean up
        rm -f "$TEMP_IMPORT_FILE"
        ;;
        
    "clear-cache"|"clean")
        echo "🧹 Clearing Impact Analysis Cache"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        
        if [[ -d "$IMPACT_CACHE_DIR" ]]; then
            rm -rf "$IMPACT_CACHE_DIR"
            echo "✅ Cache cleared successfully"
        else
            echo "ℹ️  No cache to clear"
        fi
        ;;
        
    "help"|"--help"|\"-h\"|*)
        echo "🔍 Impact Analysis Commands"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Usage: impact-check [action] [arguments]"
        echo ""
        echo "Analysis Types:"
        echo "  file [path]                    Analyze impact of changing a file"
        echo "  function [file] [name]         Track function usage across codebase"
        echo "  component [file] [name]        Analyze component dependencies"
        echo "  changes                        Impact of current uncommitted changes"
        echo "  project                        Full project dependency analysis"
        echo ""
        echo "Utilities:"
        echo "  clear-cache                    Clear analysis cache"
        echo "  help                           Show this help"
        echo ""
        echo "Examples:"
        echo "  /impact-check file src/components/Button.tsx"
        echo "  /impact-check function utils.py calculate_total"
        echo "  /impact-check component UserCard.tsx UserCard"
        echo "  /impact-check changes"
        echo "  /impact-check project"
        echo ""
        echo "💡 Features:"
        echo "  • Identifies dependent files and imports"
        echo "  • Finds related test files"
        echo "  • Analyzes function and component usage"
        echo "  • Evaluates change risk levels"
        echo "  • Maps project dependencies"
        echo "  • Suggests which tests to run"
        echo ""
        echo "Use before making changes to understand ripple effects!"
        ;;
esac
```

Comprehensive impact analysis tool that identifies all files, tests, and dependencies affected by proposed changes.
**Security Note:** This command performs read-only analysis and does not modify any files.