Create a high-level architecture diagram and explanation of how components interact. Document:

1. **System Overview**: Main components and their relationships
2. **Data Flow**: How data moves through the system
3. **API Structure**: REST and GraphQL endpoint organization
4. **Database Design**: Table relationships and data models
5. **Authentication**: User auth flow and security layers
6. **Deployment**: Infrastructure and deployment architecture

Create both visual representation and detailed explanations of the architecture.