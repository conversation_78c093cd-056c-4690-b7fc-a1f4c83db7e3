Find and fix issue #$ARGUMENTS. Follow these steps:
1. Search for the issue in code comments, TODOs, or error messages
2. Understand the root cause by examining relevant code
3. Implement a solution following project conventions
4. Add or update tests to prevent regression
5. Run relevant tests to verify the fix
6. Prepare a concise summary of the changes
**Security Note:** This command passes arguments to a script that handles its own validation and security checks.
