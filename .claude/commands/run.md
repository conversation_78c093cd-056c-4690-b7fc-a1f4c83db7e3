Run common development tasks with smart defaults. Execute:
```bash
# Smart run command for development workflows
case "$1" in
  # Development servers
  "dev"|"d")
    echo "🚀 Starting development environment..."
    docker compose -f docker-compose.dev.yml up -d
    echo "✅ Dev environment running at:"
    echo "   Frontend: https://localhost:5173"
    echo "   Backend:  https://localhost:8000"
    echo "   Storybook: https://localhost:6006"
    ;;
  
  # Test execution
  "test"|"t")
    shift
    if [[ -z "$1" ]]; then
      ./scripts/run-backend-tests.sh
    else
      ./scripts/run-backend-tests.sh "$@"
    fi
    ;;
  
  # Migrations
  "migrate"|"m")
    ./scripts/db-migrate.sh
    ;;
  
  # Code generation
  "codegen"|"c")
    echo "🔧 Running code generation..."
    cd apps/web && bun run codegen
    echo "✅ TypeScript types generated"
    ;;
  
  # Quick checks
  "check")
    ./scripts/precommit-check.sh --quick
    ;;
  
  # Storybook
  "storybook"|"sb")
    echo "📚 Starting Storybook..."
    cd apps/web && bun run storybook
    ;;
  
  # Backend only
  "backend"|"b")
    docker compose up -d backend db redis
    ;;
  
  # Frontend only
  "frontend"|"f")
    docker compose up -d frontend
    ;;
  
  # Help
  *)
    echo "Usage: run [task] [options]"
    echo ""
    echo "Tasks:"
    echo "  dev, d         Start full dev environment"
    echo "  test, t        Run tests (with optional args)"
    echo "  migrate, m     Run database migrations"
    echo "  codegen, c     Generate TypeScript types"
    echo "  check          Quick code quality check"
    echo "  storybook, sb  Start Storybook server"
    echo "  backend, b     Start backend services only"
    echo "  frontend, f    Start frontend only"
    echo ""
    echo "Examples:"
    echo "  run dev              # Start everything"
    echo "  run test unit        # Run unit tests"
    echo "  run codegen          # Generate types"
    echo "  run backend          # Backend only"
    echo ""
    echo "Common workflows:"
    echo "  run dev && run migrate    # Fresh start"
    echo "  run test && run check     # Pre-commit"
    ;;
esac
```
Quick runner for common tasks. Use `run dev` to start development or `run` alone for help.
**Security Note:** This command starts services and runs migrations. Ensure your environment is properly configured.