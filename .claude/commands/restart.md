Restart Docker containers. Execute:
```bash
# Smart environment detection
if [[ "$1" == "dev" ]]; then
    shift
    docker compose -f docker-compose.dev.yml restart "$@"
elif [[ "$1" == "test" ]]; then
    shift
    docker compose -f docker-compose.test.yml restart "$@"
else
    docker compose restart $ARGUMENTS
fi
```
Restarts all containers or specific service. Use `restart dev` for development or `restart test` for test environment. Specify service after environment (e.g., `restart dev backend`).
**Security Note:** This command passes arguments to docker compose which handles its own validation.
