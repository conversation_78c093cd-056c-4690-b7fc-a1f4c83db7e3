Analyze performance bottlenecks and suggest optimizations for $ARGUMENTS. Focus on:

1. **Database Queries**: Identify N+1 queries and slow operations
2. **Memory Usage**: Find memory leaks and excessive allocations
3. **API Performance**: Analyze response times and throughput
4. **Frontend Performance**: Bundle size, loading times, rendering
5. **Caching Opportunities**: Identify what can be cached
6. **Algorithmic Improvements**: More efficient algorithms or data structures

Provide specific, actionable optimization recommendations with impact estimates.
**Security Note:** This command passes arguments to a script that handles its own validation and security checks.
