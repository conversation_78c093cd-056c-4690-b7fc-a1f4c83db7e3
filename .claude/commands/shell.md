Open shell in container. Execute:
```bash
# Smart environment detection
if [[ "$1" == "dev" ]]; then
    shift
    SERVICE="${1:-backend}"
    shift
    docker compose -f docker-compose.dev.yml exec "$SERVICE" bash "$@"
elif [[ "$1" == "test" ]]; then
    shift
    SERVICE="${1:-backend}"
    shift
    docker compose -f docker-compose.test.yml exec "$SERVICE" bash "$@"
else
    SERVICE="${1:-backend}"
    shift
    docker compose exec "$SERVICE" bash "$@"
fi
```
Provides interactive shell access to containers. Use `shell dev frontend` or `shell test db`. Default service is backend if not specified.
**Security Note:** This command passes arguments to docker compose which handles its own validation.
