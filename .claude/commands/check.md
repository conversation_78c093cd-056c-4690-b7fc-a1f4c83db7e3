Code quality checks with smart subcommands. Execute:
```bash
# Smart check command
case "$1" in
  # Full checks
  "all"|"")
    ./scripts/precommit-check.sh
    ;;
  
  # Specific checks
  "lint"|"l")
    shift
    ./scripts/precommit-check.sh --skip-tests "$@"
    ;;
  "format"|"f")
    # Python formatting
    cd apps/backend && black . && isort .
    # TypeScript formatting
    cd ../../apps/web && bun run format
    ;;
  "types"|"t")
    # Python type checking
    cd apps/backend && mypy .
    # TypeScript type checking
    cd ../../apps/web && bun run type-check
    ;;
  "security"|"s")
    ./scripts/run-security-review.sh
    ;;
  
  # Fix mode
  "fix")
    # Auto-fix what we can
    echo "🔧 Auto-fixing code issues..."
    cd apps/backend && black . && isort . && ruff --fix .
    cd ../../apps/web && bun run lint:fix && bun run format
    echo "✅ Auto-fix complete. Run 'check' to verify."
    ;;
  
  # Quick checks
  "quick"|"q")
    ./scripts/precommit-check.sh --quick
    ;;
  
  # Help
  *)
    echo "Usage: check [command] [options]"
    echo ""
    echo "Commands:"
    echo "  all            Run all checks (default)"
    echo "  lint, l        Run linters only"
    echo "  format, f      Check formatting"
    echo "  types, t       Run type checking"
    echo "  security, s    Run security checks"
    echo "  fix            Auto-fix issues"
    echo "  quick, q       Quick checks only"
    echo ""
    echo "Examples:"
    echo "  check              # Run all checks"
    echo "  check lint         # Linting only"
    echo "  check fix          # Auto-fix issues"
    echo "  check security     # Security scan"
    echo ""
    echo "Pre-commit workflow:"
    echo "  1. check           # Find issues"
    echo "  2. check fix       # Auto-fix"
    echo "  3. check           # Verify"
    ;;
esac
```
Unified code quality command. Run `check` alone for all checks or use subcommands for specific checks.
**Security Note:** This command modifies code when using 'fix'. Review changes before committing.
