Create a changelog entry based on recent commits and changes. Include:

1. **Version**: Determine appropriate version bump (major, minor, patch)
2. **Added**: New features and functionality
3. **Changed**: Modifications to existing functionality
4. **Deprecated**: Features marked for removal
5. **Removed**: Deleted features
6. **Fixed**: Bug fixes and patches
7. **Security**: Security-related changes

Follow semantic versioning and project changelog conventions.