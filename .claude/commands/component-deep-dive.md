Create comprehensive documentation and analysis of component $ARGUMENTS. Process:

1. **Research the component**:
```
mcp__think-tank__exa_search
mcp__think-tank__exa_answer
```

2. **Open related knowledge nodes**:
```
mcp__think-tank__open_nodes
```

3. **Perform structured analysis**:
```
mcp__think-tank__think
```

4. **Store findings and relationships**:
```
mcp__think-tank__upsert_entities
mcp__think-tank__create_relations
```

Creates a complete knowledge profile of any system component or technology.
**Security Note:** This command passes arguments to a script that handles its own validation and security checks.
