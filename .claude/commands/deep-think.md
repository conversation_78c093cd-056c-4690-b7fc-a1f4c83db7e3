Perform deep analysis and structured reasoning about the provided arguments. Execute:
```
mcp__think-tank__think
```
Use advanced reasoning with options for:
- Research integration during thinking
- Self-reflection on conclusions
- Memory storage of insights
- Multi-step problem breakdown
- Associating thoughts with specific entities

Ideal for complex architectural decisions or challenging technical problems.
**Security Note:** This command passes arguments to a script that handles its own validation and security checks.
