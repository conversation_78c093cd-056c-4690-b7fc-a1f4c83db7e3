Run tests with smart defaults and unified interface. Execute:
```bash
# Smart test command with environment detection
case "$1" in
  "all")
    shift
    echo "Running comprehensive test suite..."
    ./scripts/run-full-check.sh "$@"
    ;;
  "backend")
    shift
    echo "Running backend tests..."
    ./scripts/run-backend-tests.sh "$@"
    ;;
  "frontend")
    shift
    echo "Running frontend tests..."
    ./scripts/run-frontend-tests.sh "$@"
    ;;
  "unit")
    shift
    echo "Running unit tests..."
    ./scripts/run-backend-tests.sh --unit "$@"
    ;;
  "integration")
    shift
    echo "Running integration tests..."
    ./scripts/run-backend-tests.sh --integration "$@"
    ;;
  "e2e")
    shift
    echo "Running e2e tests..."
    ./scripts/run-frontend-tests.sh --e2e "$@"
    ;;
  "coverage")
    shift
    echo "Running tests with coverage..."
    ./scripts/run-backend-tests.sh --coverage "$@"
    ;;
  "parallel")
    shift
    echo "Running tests in parallel..."
    ./scripts/parallel-test-runner.sh "$@"
    ;;
  "graphql")
    shift
    echo "Running GraphQL tests..."
    ./scripts/run-backend-tests.sh tests/integration/graphql "$@"
    ;;
  "performance"|"perf")
    shift
    echo "Running performance tests..."
    ./scripts/run-backend-tests.sh tests/performance "$@"
    ;;
  "security"|"sec")
    shift
    echo "Running security tests..."
    ./scripts/run-backend-tests.sh tests/security "$@"
    ;;
  "-k"|"--pattern")
    # Pattern matching
    ./scripts/run-backend-tests.sh "$@"
    ;;
  "help"|"--help"|"-h")
    echo "Usage: test [type] [options]"
    echo ""
    echo "Types:"
    echo "  all         - Run comprehensive test suite"
    echo "  backend     - Run all backend tests"
    echo "  frontend    - Run all frontend tests"
    echo "  unit        - Run unit tests only"
    echo "  integration - Run integration tests only"
    echo "  e2e         - Run end-to-end tests"
    echo "  coverage    - Run with coverage report"
    echo "  parallel    - Run tests in parallel"
    echo "  graphql     - Run GraphQL tests"
    echo "  performance - Run performance tests"
    echo "  security    - Run security tests"
    echo ""
    echo "Examples:"
    echo "  test                                    # Smart test selection"
    echo "  test all                                # Full test suite"
    echo "  test backend                            # All backend tests"
    echo "  test unit                               # Unit tests only"
    echo "  test tests/unit/test_user.py            # Specific file"
    echo "  test tests/unit/test_user.py::test_create  # Specific test"
    echo "  test -k 'user and create'               # Pattern match"
    echo "  test parallel backend                   # Backend tests in parallel"
    ;;
  *)
    # Smart default behavior
    if [[ -f "$1" ]] || [[ "$1" == *".py"* ]] || [[ "$1" == *".ts"* ]] || [[ "$1" == *".tsx"* ]]; then
      # Looks like a file path - run specific test
      if [[ "$1" == *".ts"* ]] || [[ "$1" == *".tsx"* ]]; then
        echo "Running specific frontend test: $1"
        cd apps/web && bun test "$@"
      else
        echo "Running specific backend test: $1"
        ./scripts/run-backend-tests.sh "$@"
      fi
    elif [[ -z "$1" ]]; then
      # No arguments - use smart test selection
      echo "Running smart test selection based on recent changes..."
      ./scripts/smart-test-selector.sh
    else
      # Unknown argument - pass through to backend tests
      ./scripts/run-backend-tests.sh "$@"
    fi
    ;;
esac
```

Smart unified test runner with intelligent defaults:
- No args: Runs smart test selection based on git changes
- File paths: Automatically detects backend (.py) vs frontend (.ts/.tsx)
- Keywords: all, backend, frontend, unit, integration, e2e, coverage, parallel
- Pattern matching: Supports -k for pytest pattern matching
- Help: Built-in help with examples

**Security Note:** This command passes arguments to scripts that handle their own validation and security checks.