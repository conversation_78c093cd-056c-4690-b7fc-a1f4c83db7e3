Quick development environment control. Execute:
```bash
case "$1" in
  "up")
    docker compose -f docker-compose.dev.yml up -d
    ;;
  "down")
    docker compose -f docker-compose.dev.yml down
    ;;
  "logs")
    shift
    docker compose -f docker-compose.dev.yml logs -f --tail=100 "$@"
    ;;
  "shell")
    shift
    SERVICE="${1:-backend}"
    shift
    docker compose -f docker-compose.dev.yml exec "$SERVICE" bash "$@"
    ;;
  *)
    echo "Usage: dev [up|down|logs|shell] [service]"
    echo "Examples:"
    echo "  dev up        # Start dev environment"
    echo "  dev down      # Stop dev environment"
    echo "  dev logs      # View all logs"
    echo "  dev logs frontend  # View frontend logs"
    echo "  dev shell     # Backend shell"
    echo "  dev shell frontend  # Frontend shell"
    ;;
esac
```
Shortcut for common dev environment operations. Much faster than typing environment each time.