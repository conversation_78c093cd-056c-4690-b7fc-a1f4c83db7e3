Rapid visual iteration and feedback for frontend development. Execute:
```bash
# Visual iteration system for UI development
ACTION="${1:-help}"
COMPONENT="${2:-}"
VISUAL_STATE_FILE=".visual-iteration-state"

case "$ACTION" in
    "start"|"begin")
        if [[ -z "$COMPONENT" ]]; then
            echo "❌ Component name required"
            echo "Usage: visual-iterate start [component-name]"
            exit 1
        fi
        
        echo "🎨 Starting Visual Iteration: $COMPONENT"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        
        # Save iteration state
        cat > "$VISUAL_STATE_FILE" << EOF
COMPONENT_NAME=$COMPONENT
ITERATION_COUNT=0
START_TIME=$(date +%s)
STORYBOOK_RUNNING=false
DEV_SERVER_RUNNING=false
LAST_CHANGE_TIME=$(date +%s)
EOF
        
        echo ""
        echo "🚀 Setting up visual development environment..."
        echo ""
        
        # Check if frontend dev server is running
        if ! curl -s http://localhost:5173 > /dev/null 2>&1; then
            echo "📦 Starting frontend dev server..."
            cd apps/web
            bun run dev -- --host 0.0.0.0 &
            DEV_SERVER_PID=$!
            echo "DEV_SERVER_PID=$DEV_SERVER_PID" >> "../$VISUAL_STATE_FILE"
            sed -i.bak 's/DEV_SERVER_RUNNING=false/DEV_SERVER_RUNNING=true/' "../$VISUAL_STATE_FILE" && rm -f "../$VISUAL_STATE_FILE.bak"
            sleep 3
            cd ..
        else
            echo "✅ Frontend dev server already running"
            sed -i.bak 's/DEV_SERVER_RUNNING=false/DEV_SERVER_RUNNING=true/' "$VISUAL_STATE_FILE" && rm -f "$VISUAL_STATE_FILE.bak"
        fi
        
        # Check if Storybook is available
        if [[ -f "apps/web/.storybook/main.ts" ]]; then
            echo "📚 Storybook available - starting..."
            cd apps/web
            bun run storybook &
            STORYBOOK_PID=$!
            echo "STORYBOOK_PID=$STORYBOOK_PID" >> "../$VISUAL_STATE_FILE"
            sed -i.bak 's/STORYBOOK_RUNNING=false/STORYBOOK_RUNNING=true/' "../$VISUAL_STATE_FILE" && rm -f "../$VISUAL_STATE_FILE.bak"
            cd ..
        fi
        
        echo ""
        echo "🎯 Visual Development Environment Ready:"
        echo "  • Dev Server: http://localhost:5173"
        [[ -f "apps/web/.storybook/main.ts" ]] && echo "  • Storybook: http://localhost:6006"
        echo "  • Component: $COMPONENT"
        echo ""
        echo "💡 Next Steps:"
        echo "  1. Make changes to your component"
        echo "  2. Use /visual-iterate feedback to capture state"
        echo "  3. Use /visual-iterate compare to track changes"
        echo "  4. Use /visual-iterate snapshot for important states"
        ;;
        
    "feedback"|"capture")
        if [[ ! -f "$VISUAL_STATE_FILE" ]]; then
            echo "❌ No active visual iteration. Start with: /visual-iterate start [component]"
            exit 1
        fi
        
        source "$VISUAL_STATE_FILE"
        ITERATION_COUNT=$((ITERATION_COUNT + 1))
        
        echo "📸 Capturing Visual Feedback - Iteration #$ITERATION_COUNT"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Component: $COMPONENT_NAME"
        echo ""
        
        # Update iteration count
        sed -i.bak "s/ITERATION_COUNT=.*/ITERATION_COUNT=$ITERATION_COUNT/" "$VISUAL_STATE_FILE" && rm -f "$VISUAL_STATE_FILE.bak"
        sed -i.bak "s/LAST_CHANGE_TIME=.*/LAST_CHANGE_TIME=$(date +%s)/" "$VISUAL_STATE_FILE" && rm -f "$VISUAL_STATE_FILE.bak"
        
        # Create feedback directory if it doesn't exist
        mkdir -p ".visual-iterations/$COMPONENT_NAME"
        
        # Capture current state
        FEEDBACK_FILE=".visual-iterations/$COMPONENT_NAME/iteration-$ITERATION_COUNT.md"
        cat > "$FEEDBACK_FILE" << EOF
# Visual Iteration #$ITERATION_COUNT - $COMPONENT_NAME

**Timestamp:** $(date)
**Duration:** $(($(date +%s) - START_TIME))s

## Current State

### Visual Changes
- [ ] Layout adjustments needed
- [ ] Color scheme updates
- [ ] Typography improvements
- [ ] Spacing/padding fixes
- [ ] Responsive behavior
- [ ] Animation/interaction

### Technical Notes
- Component file: apps/web/src/components/...
- Story file: apps/web/src/components/....stories.tsx
- Test file: apps/web/src/components/__tests__/....test.tsx

### Next Iteration Goals
1. 
2. 
3. 

### Screenshots/Notes
(Add visual references here)

EOF
        
        echo "✅ Feedback captured: $FEEDBACK_FILE"
        echo ""
        echo "🎯 Quick Actions:"
        echo "  • Edit feedback: /edit $FEEDBACK_FILE"
        echo "  • Run tests: /test frontend $COMPONENT_NAME"
        echo "  • Take snapshot: /visual-iterate snapshot"
        echo "  • Compare states: /visual-iterate compare"
        echo ""
        echo "💡 Keep iterating! Each change builds toward the perfect UI."
        ;;
        
    "snapshot"|"save")
        if [[ ! -f "$VISUAL_STATE_FILE" ]]; then
            echo "❌ No active visual iteration. Start with: /visual-iterate start [component]"
            exit 1
        fi
        
        source "$VISUAL_STATE_FILE"
        SNAPSHOT_NAME="${2:-$(date +%Y%m%d-%H%M%S)}"
        
        echo "📷 Creating Visual Snapshot: $SNAPSHOT_NAME"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Component: $COMPONENT_NAME"
        echo "Iteration: #$ITERATION_COUNT"
        echo ""
        
        # Create snapshot directory
        SNAPSHOT_DIR=".visual-iterations/$COMPONENT_NAME/snapshots"
        mkdir -p "$SNAPSHOT_DIR"
        
        # Copy relevant files
        if [[ -f "apps/web/src/components/$COMPONENT_NAME.tsx" ]]; then
            cp "apps/web/src/components/$COMPONENT_NAME.tsx" "$SNAPSHOT_DIR/$SNAPSHOT_NAME.tsx"
        fi
        
        if [[ -f "apps/web/src/components/$COMPONENT_NAME.stories.tsx" ]]; then
            cp "apps/web/src/components/$COMPONENT_NAME.stories.tsx" "$SNAPSHOT_DIR/$SNAPSHOT_NAME.stories.tsx"
        fi
        
        # Create snapshot metadata
        cat > "$SNAPSHOT_DIR/$SNAPSHOT_NAME.json" << EOF
{
  "component": "$COMPONENT_NAME",
  "snapshot_name": "$SNAPSHOT_NAME",
  "iteration": $ITERATION_COUNT,
  "timestamp": "$(date -Iseconds)",
  "duration": $(($(date +%s) - START_TIME)),
  "description": "Visual snapshot at iteration #$ITERATION_COUNT"
}
EOF
        
        echo "✅ Snapshot saved: $SNAPSHOT_DIR/$SNAPSHOT_NAME"
        echo ""
        echo "📦 Snapshot includes:"
        echo "  • Component source code"
        echo "  • Storybook story"
        echo "  • Metadata and timestamp"
        echo ""
        echo "🔄 Use /visual-iterate restore $SNAPSHOT_NAME to revert"
        ;;
        
    "compare"|"diff")
        if [[ ! -f "$VISUAL_STATE_FILE" ]]; then
            echo "❌ No active visual iteration. Start with: /visual-iterate start [component]"
            exit 1
        fi
        
        source "$VISUAL_STATE_FILE"
        
        echo "🔍 Visual Iteration Comparison"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Component: $COMPONENT_NAME"
        echo "Current Iteration: #$ITERATION_COUNT"
        echo "Total Duration: $(($(date +%s) - START_TIME))s"
        echo ""
        
        # Show iteration history
        ITERATIONS_DIR=".visual-iterations/$COMPONENT_NAME"
        if [[ -d "$ITERATIONS_DIR" ]]; then
            echo "📊 Iteration History:"
            for file in "$ITERATIONS_DIR"/iteration-*.md; do
                if [[ -f "$file" ]]; then
                    ITER_NUM=$(basename "$file" | sed 's/iteration-\([0-9]*\)\.md/\1/')
                    ITER_TIME=$(stat -c %Y "$file" 2>/dev/null || stat -f %m "$file" 2>/dev/null)
                    echo "  #$ITER_NUM - $(date -d @$ITER_TIME 2>/dev/null || date -r $ITER_TIME 2>/dev/null)"
                fi
            done
            echo ""
        fi
        
        # Show available snapshots
        SNAPSHOTS_DIR="$ITERATIONS_DIR/snapshots"
        if [[ -d "$SNAPSHOTS_DIR" ]]; then
            echo "📷 Available Snapshots:"
            for snapshot in "$SNAPSHOTS_DIR"/*.json; do
                if [[ -f "$snapshot" ]]; then
                    SNAP_NAME=$(basename "$snapshot" .json)
                    echo "  📸 $SNAP_NAME"
                fi
            done
            echo ""
        fi
        
        # Quick comparison with latest files
        if [[ -f "apps/web/src/components/$COMPONENT_NAME.tsx" ]]; then
            echo "🔄 Recent Changes:"
            echo "  • Component: $(stat -c %y "apps/web/src/components/$COMPONENT_NAME.tsx" 2>/dev/null || echo "Modified recently")"
            if [[ -f "apps/web/src/components/$COMPONENT_NAME.stories.tsx" ]]; then
                echo "  • Story: $(stat -c %y "apps/web/src/components/$COMPONENT_NAME.stories.tsx" 2>/dev/null || echo "Modified recently")"
            fi
        fi
        
        echo ""
        echo "💡 Commands:"
        echo "  /visual-iterate snapshot [name]  # Save current state"
        echo "  /visual-iterate restore [name]   # Restore snapshot"
        echo "  /diff [file1] [file2]           # Compare files directly"
        ;;
        
    "restore"|"revert")
        if [[ ! -f "$VISUAL_STATE_FILE" ]]; then
            echo "❌ No active visual iteration. Start with: /visual-iterate start [component]"
            exit 1
        fi
        
        source "$VISUAL_STATE_FILE"
        SNAPSHOT_NAME="${2:-}"
        
        if [[ -z "$SNAPSHOT_NAME" ]]; then
            echo "❌ Snapshot name required"
            echo "Usage: visual-iterate restore [snapshot-name]"
            echo ""
            echo "Available snapshots:"
            SNAPSHOTS_DIR=".visual-iterations/$COMPONENT_NAME/snapshots"
            if [[ -d "$SNAPSHOTS_DIR" ]]; then
                for snapshot in "$SNAPSHOTS_DIR"/*.json; do
                    if [[ -f "$snapshot" ]]; then
                        SNAP_NAME=$(basename "$snapshot" .json)
                        echo "  📸 $SNAP_NAME"
                    fi
                done
            fi
            exit 1
        fi
        
        SNAPSHOT_DIR=".visual-iterations/$COMPONENT_NAME/snapshots"
        
        if [[ ! -f "$SNAPSHOT_DIR/$SNAPSHOT_NAME.tsx" ]]; then
            echo "❌ Snapshot not found: $SNAPSHOT_NAME"
            exit 1
        fi
        
        echo "🔄 Restoring Visual Snapshot: $SNAPSHOT_NAME"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Component: $COMPONENT_NAME"
        echo ""
        
        # Restore files
        if [[ -f "$SNAPSHOT_DIR/$SNAPSHOT_NAME.tsx" ]]; then
            cp "$SNAPSHOT_DIR/$SNAPSHOT_NAME.tsx" "apps/web/src/components/$COMPONENT_NAME.tsx"
            echo "✅ Restored component file"
        fi
        
        if [[ -f "$SNAPSHOT_DIR/$SNAPSHOT_NAME.stories.tsx" ]]; then
            cp "$SNAPSHOT_DIR/$SNAPSHOT_NAME.stories.tsx" "apps/web/src/components/$COMPONENT_NAME.stories.tsx"
            echo "✅ Restored story file"
        fi
        
        echo ""
        echo "🎯 Snapshot restored successfully!"
        echo "Check your browser to see the restored state."
        ;;
        
    "complete"|"finish"|"done")
        if [[ ! -f "$VISUAL_STATE_FILE" ]]; then
            echo "❌ No active visual iteration"
            exit 1
        fi
        
        source "$VISUAL_STATE_FILE"
        
        echo "🏁 Visual Iteration Complete"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Component: $COMPONENT_NAME"
        echo "Total Iterations: $ITERATION_COUNT"
        echo "Total Duration: $(($(date +%s) - START_TIME))s"
        echo ""
        
        # Stop development servers if we started them
        if [[ -n "${DEV_SERVER_PID:-}" ]]; then
            kill "$DEV_SERVER_PID" 2>/dev/null || true
            echo "🛑 Stopped dev server"
        fi
        
        if [[ -n "${STORYBOOK_PID:-}" ]]; then
            kill "$STORYBOOK_PID" 2>/dev/null || true
            echo "🛑 Stopped Storybook"
        fi
        
        # Archive iteration state
        mv "$VISUAL_STATE_FILE" ".visual-complete-$(date +%Y%m%d-%H%M%S)"
        
        echo ""
        echo "📊 Session Summary:"
        echo "  • $ITERATION_COUNT iterations completed"
        echo "  • Visual feedback captured"
        echo "  • Snapshots available for future reference"
        echo ""
        echo "🚀 Next Steps:"
        echo "  • Run component tests: /test frontend $COMPONENT_NAME"
        echo "  • Update documentation if needed"
        echo "  • Consider creating new story variants"
        ;;
        
    "status"|"state")
        if [[ ! -f "$VISUAL_STATE_FILE" ]]; then
            echo "❌ No active visual iteration"
            echo "Start with: /visual-iterate start [component-name]"
            exit 1
        fi
        
        source "$VISUAL_STATE_FILE"
        
        echo "🎨 Visual Iteration Status"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Component: $COMPONENT_NAME"
        echo "Iterations: #$ITERATION_COUNT"
        echo "Duration: $(($(date +%s) - START_TIME))s"
        echo ""
        echo "Environment:"
        [[ "$DEV_SERVER_RUNNING" == "true" ]] && echo "  ✅ Dev server running" || echo "  ❌ Dev server stopped"
        [[ "$STORYBOOK_RUNNING" == "true" ]] && echo "  ✅ Storybook running" || echo "  ❌ Storybook not running"
        echo ""
        echo "Last change: $(($(date +%s) - LAST_CHANGE_TIME))s ago"
        echo ""
        echo "💡 Actions:"
        echo "  /visual-iterate feedback   # Capture current state"
        echo "  /visual-iterate snapshot   # Save important version"
        echo "  /visual-iterate compare    # View iteration history"
        ;;
        
    "help"|"--help"|\"-h\"|*)
        echo "🎨 Visual Iteration Commands"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Usage: visual-iterate [action] [arguments]"
        echo ""
        echo "Workflow:"
        echo "  start [component]       Begin visual iteration session"
        echo "  feedback                Capture current visual state"
        echo "  snapshot [name]         Save important version"
        echo "  compare                 View iteration history"
        echo "  restore [name]          Restore saved snapshot"
        echo "  complete                Finish iteration session"
        echo ""
        echo "Status:"
        echo "  status                  Show current session state"
        echo "  help                    Show this help"
        echo ""
        echo "Example Workflow:"
        echo "  /visual-iterate start UserCard"
        echo "  # Make visual changes"
        echo "  /visual-iterate feedback"
        echo "  # Continue iterating"
        echo "  /visual-iterate snapshot final-version"
        echo "  /visual-iterate complete"
        echo ""
        echo "💡 Features:"
        echo "  • Automatic dev server management"
        echo "  • Storybook integration"
        echo "  • Visual state tracking"
        echo "  • Snapshot and restore functionality"
        echo "  • Iteration history"
        ;;
esac
```

Rapid visual development with iteration tracking, snapshot management, and automatic environment setup.
**Security Note:** This command manages development servers and file snapshots but does not execute unsafe operations.