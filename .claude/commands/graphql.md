GraphQL operations with smart subcommands. Execute:
```bash
# Smart GraphQL command
case "$1" in
  # Code generation
  "generate"|"gen"|"g"|"")
    cd apps/web && bun run codegen
    ;;
  
  # Schema updates
  "update"|"u")
    ./scripts/update-graphql-schema.sh
    ;;
  
  # Testing
  "test"|"t")
    ./scripts/run-backend-tests.sh tests/integration/graphql/
    ;;
  "test-gen")
    cd apps/web && bun run codegen:test
    ;;
  
  # Validation
  "validate"|"v")
    cd apps/web && bun run graphql:validate
    ;;
  
  # Schema inspection
  "schema"|"s")
    cat apps/backend/src/a2a_platform/api/graphql/schema.graphql
    ;;
  
  # Introspection
  "introspect"|"i")
    cd apps/web && bun run codegen:introspection
    ;;
  
  # Development
  "playground"|"p")
    echo "GraphQL Playground available at:"
    echo "  Development: https://localhost:8000/graphql"
    echo "  Production: https://api.vedavivi.app/graphql"
    ;;
  
  # Help
  *)
    echo "Usage: graphql [command] [options]"
    echo ""
    echo "Commands:"
    echo "  generate, gen, g  Generate TypeScript types (default)"
    echo "  update, u         Update schema from backend"
    echo "  test, t           Run GraphQL tests"
    echo "  test-gen          Generate with test schema"
    echo "  validate, v       Validate GraphQL operations"
    echo "  schema, s         View current schema"
    echo "  introspect, i     Run introspection"
    echo "  playground, p     Show playground URLs"
    echo ""
    echo "Examples:"
    echo "  graphql              # Generate types"
    echo "  graphql update       # Update schema"
    echo "  graphql test         # Run tests"
    echo "  graphql validate     # Validate queries"
    echo ""
    echo "Workflow:"
    echo "  1. graphql update    # Get latest schema"
    echo "  2. graphql generate  # Generate types"
    echo "  3. graphql test      # Verify everything"
    ;;
esac
```
Unified GraphQL command. Run `graphql` alone to generate types or use subcommands for specific operations.
**Security Note:** This command modifies generated code files. Ensure schema changes are reviewed.