Enable environment-aware safety restrictions to prevent dangerous operations. Execute:
```bash
# Environment-aware safety system
MODE="${1:-status}"
SAFE_MODE_FILE=".safe-mode-config"

case "$MODE" in
    "production"|"prod")
        echo "🔒 Enabling Production Safe Mode"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        
        cat > "$SAFE_MODE_FILE" << 'EOF'
SAFE_MODE=production
RESTRICTIONS=high
CONFIRMATIONS=required
DRY_RUN_ENFORCED=true
DANGEROUS_COMMANDS_BLOCKED=true
AUTO_BACKUP=true
ENVIRONMENT=production
EOF
        
        echo ""
        echo "🛡️  Production Safety Features Enabled:"
        echo "  ✅ Database operations require confirmation"
        echo "  ✅ Destructive commands blocked"
        echo "  ✅ Auto-backup before changes"
        echo "  ✅ Dry-run enforced for risky operations"
        echo "  ✅ Migration safety checks"
        echo "  ✅ Deployment approval gates"
        echo ""
        echo "❌ Blocked Commands:"
        echo "  • /db reset (completely blocked)"
        echo "  • /fresh (requires confirmation)"
        echo "  • /down --volumes (requires confirmation)"
        echo "  • Direct database modifications"
        echo ""
        echo "⚠️  All changes will require manual approval"
        ;;
        
    "staging"|"stage")
        echo "🔍 Enabling Staging Safe Mode"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        
        cat > "$SAFE_MODE_FILE" << 'EOF'
SAFE_MODE=staging
RESTRICTIONS=medium
CONFIRMATIONS=important
DRY_RUN_ENFORCED=false
DANGEROUS_COMMANDS_BLOCKED=false
AUTO_BACKUP=true
ENVIRONMENT=staging
EOF
        
        echo ""
        echo "⚖️  Staging Safety Features Enabled:"
        echo "  ✅ Important operations require confirmation"
        echo "  ✅ Auto-backup before destructive changes"
        echo "  ⚠️  Dangerous commands warn before execution"
        echo "  ✅ Database migration safety checks"
        echo ""
        echo "🟡 Warning Commands:"
        echo "  • /db reset (shows warning)"
        echo "  • /fresh (shows impact)"
        echo "  • /down --volumes (warns about data loss)"
        echo ""
        echo "💡 Moderate safety for testing environment"
        ;;
        
    "development"|"dev")
        echo "🚀 Enabling Development Safe Mode"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        
        cat > "$SAFE_MODE_FILE" << 'EOF'
SAFE_MODE=development
RESTRICTIONS=low
CONFIRMATIONS=destructive
DRY_RUN_ENFORCED=false
DANGEROUS_COMMANDS_BLOCKED=false
AUTO_BACKUP=false
ENVIRONMENT=development
EOF
        
        echo ""
        echo "🛠️  Development Safety Features:"
        echo "  ⚠️  Basic warnings for destructive operations"
        echo "  💡 Educational tips for best practices"
        echo "  🔄 Quick reset and refresh allowed"
        echo ""
        echo "🟢 Allowed Commands:"
        echo "  • /db reset (with confirmation)"
        echo "  • /fresh (immediate)"
        echo "  • /down --volumes (with warning)"
        echo ""
        echo "🎯 Minimal safety for fast development"
        ;;
        
    "off"|"disable")
        if [[ -f "$SAFE_MODE_FILE" ]]; then
            CURRENT_MODE=$(grep "SAFE_MODE=" "$SAFE_MODE_FILE" | cut -d'=' -f2)
            rm "$SAFE_MODE_FILE"
            echo "🔓 Safe Mode Disabled"
            echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
            echo ""
            echo "Previous mode: $CURRENT_MODE"
            echo ""
            echo "⚠️  All safety restrictions removed"
            echo "💡 Be careful with destructive operations"
        else
            echo "❌ Safe mode is not currently enabled"
        fi
        ;;
        
    "status"|"check"|"")
        if [[ -f "$SAFE_MODE_FILE" ]]; then
            source "$SAFE_MODE_FILE"
            echo "🛡️  Safe Mode Status"
            echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
            echo ""
            echo "Mode: $SAFE_MODE"
            echo "Environment: $ENVIRONMENT"
            echo "Restrictions: $RESTRICTIONS"
            echo ""
            case "$SAFE_MODE" in
                "production")
                    echo "🔒 Maximum safety enabled"
                    echo "  • Destructive commands blocked"
                    echo "  • All changes require confirmation"
                    echo "  • Auto-backup enabled"
                    ;;
                "staging")
                    echo "⚖️  Moderate safety enabled"
                    echo "  • Important operations confirmed"
                    echo "  • Dangerous commands warn"
                    echo "  • Auto-backup for destructive changes"
                    ;;
                "development")
                    echo "🛠️  Basic safety enabled"
                    echo "  • Destructive operations confirmed"
                    echo "  • Educational warnings shown"
                    ;;
            esac
            echo ""
            echo "Disable with: /safe-mode off"
        else
            echo "🔓 Safe Mode: Disabled"
            echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
            echo ""
            echo "No safety restrictions active"
            echo ""
            echo "Enable with:"
            echo "  /safe-mode production  # Maximum safety"
            echo "  /safe-mode staging     # Moderate safety"
            echo "  /safe-mode dev         # Basic safety"
        fi
        ;;
        
    "test"|"verify")
        echo "🧪 Testing Safe Mode Configuration"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        
        if [[ -f "$SAFE_MODE_FILE" ]]; then
            source "$SAFE_MODE_FILE"
            echo ""
            echo "Current configuration:"
            cat "$SAFE_MODE_FILE" | sed 's/^/  /'
            echo ""
            echo "✅ Safe mode configuration valid"
        else
            echo ""
            echo "❌ No safe mode configuration found"
            echo "Safe mode is disabled"
        fi
        ;;
        
    "help"|"--help"|"-h")
        echo "🛡️  Safe Mode Commands"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Usage: safe-mode [environment]"
        echo ""
        echo "Environments:"
        echo "  production, prod    Maximum safety (blocks destructive ops)"
        echo "  staging, stage      Moderate safety (warns and confirms)"
        echo "  development, dev    Basic safety (confirms destructive ops)"
        echo "  off, disable        Disable all safety restrictions"
        echo ""
        echo "Status Commands:"
        echo "  status, check       Show current safe mode state"
        echo "  test, verify        Test configuration"
        echo ""
        echo "Examples:"
        echo "  /safe-mode prod     # Enable production safety"
        echo "  /safe-mode status   # Check current mode"
        echo "  /safe-mode off      # Disable safety"
        echo ""
        echo "💡 Safe mode integrates with:"
        echo "  • /dry-run commands"
        echo "  • Database operations"
        echo "  • Docker commands"
        echo "  • Deployment commands"
        ;;
        
    *)
        echo "❌ Unknown safe mode: $MODE"
        echo ""
        echo "Available modes:"
        echo "  production  - Maximum safety"
        echo "  staging     - Moderate safety"
        echo "  development - Basic safety"
        echo "  off         - Disable safety"
        echo ""
        echo "Use '/safe-mode help' for more information"
        exit 1
        ;;
esac

# Update shell prompt to show safe mode (if in interactive shell)
if [[ -f "$SAFE_MODE_FILE" ]] && [[ "$MODE" != "status" ]] && [[ "$MODE" != "test" ]]; then
    echo ""
    echo "💡 Tip: Your shell prompt will show safe mode status"
    echo "🔄 Restart your shell or run 'source ~/.bashrc' to see changes"
fi
```

Environment-aware safety system that restricts dangerous operations based on context.
**Security Note:** This command manages safety configuration and does not execute restricted operations.