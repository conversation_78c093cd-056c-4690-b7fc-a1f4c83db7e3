Run docker compose with full flexibility. Execute:
```bash
docker compose "$@"
```
Direct access to docker compose with all flags and options. Examples:
- `dc -f docker-compose.dev.yml up -d`
- `dc -f docker-compose.test.yml logs backend`
- `dc ps`
- `dc -f docker-compose.dev.yml exec frontend bash`

**Security Note:** This command passes all arguments directly to docker compose which handles its own validation.