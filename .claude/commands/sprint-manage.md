Manage sprint tasks and progression for $ARGUMENTS. Workflow:

1. **View current tasks**:
```
mcp__think-tank__list_tasks
```

2. **Get next priority task**:
```
mcp__think-tank__next_task
```

3. **Update task status**:
```
mcp__think-tank__update_tasks
```

4. **Complete finished tasks**:
```
mcp__think-tank__complete_task
```

Provides structured task management for sprint planning and execution with priority-based work queues.
**Security Note:** This command passes arguments to a script that handles its own validation and security checks.
