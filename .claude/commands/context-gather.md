Automatically gather and read all relevant files for a development task. Execute:
```bash
# Context gathering system for comprehensive task understanding
ACTION="${1:-help}"
TASK_TYPE="${2:-}"
TARGET="${3:-}"
CONTEXT_DIR=".claude-context"
CONTEXT_FILE="$CONTEXT_DIR/gathered-context-$$.md"

case "$ACTION" in
    "feature"|"component")
        if [[ -z "$TARGET" ]]; then
            echo "❌ Component/feature name required"
            echo "Usage: context-gather feature [component-name]"
            exit 1
        fi
        
        echo "📚 Gathering Context for Feature: $TARGET"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        # Create context directory
        mkdir -p "$CONTEXT_DIR"
        
        # Initialize context file
        cat > "$CONTEXT_FILE" << EOF
# Context Gathered for: $TARGET
Generated: $(date)
Task Type: Feature/Component Development

## Overview
Gathering all relevant files for working on $TARGET feature/component.

EOF
        
        echo "🔍 Searching for relevant files..."
        echo ""
        
        # Search for component files
        echo "📁 Component Files:"
        COMPONENT_FILES=$(find . -name "*$TARGET*" -type f | \
            grep -E "\.(tsx?|jsx?|py)$" | \
            grep -v node_modules | \
            grep -v __pycache__ | \
            head -10)
        
        if [[ -n "$COMPONENT_FILES" ]]; then
            echo "$COMPONENT_FILES" | while read -r file; do
                echo "  • $file"
                echo "### File: $file" >> "$CONTEXT_FILE"
                echo '```' >> "$CONTEXT_FILE"
                head -50 "$file" >> "$CONTEXT_FILE" 2>/dev/null
                echo '```' >> "$CONTEXT_FILE"
                echo "" >> "$CONTEXT_FILE"
            done
        else
            echo "  • No direct component files found"
        fi
        
        echo ""
        
        # Search for related types/interfaces
        echo "📐 Type Definitions:"
        TYPE_FILES=$(find . -path "*/types/*" -o -path "*/interfaces/*" | \
            xargs grep -l "$TARGET" 2>/dev/null | \
            grep -v node_modules | \
            head -5)
        
        if [[ -n "$TYPE_FILES" ]]; then
            echo "$TYPE_FILES" | while read -r file; do
                echo "  • $file"
                echo "### Type Definition: $file" >> "$CONTEXT_FILE"
                echo '```typescript' >> "$CONTEXT_FILE"
                grep -A 10 -B 2 "$TARGET" "$file" >> "$CONTEXT_FILE" 2>/dev/null
                echo '```' >> "$CONTEXT_FILE"
                echo "" >> "$CONTEXT_FILE"
            done
        else
            # Try to find inline type definitions
            INLINE_TYPES=$(find . -name "*.ts" -o -name "*.tsx" | \
                xargs grep -l "interface.*$TARGET\|type.*$TARGET" 2>/dev/null | \
                grep -v node_modules | \
                head -3)
            
            if [[ -n "$INLINE_TYPES" ]]; then
                echo "$INLINE_TYPES" | while read -r file; do
                    echo "  • $file (inline types)"
                done
            else
                echo "  • No type definitions found"
            fi
        fi
        
        echo ""
        
        # Search for test files
        echo "🧪 Test Files:"
        TEST_FILES=$(find . -name "*test*" -o -name "*spec*" | \
            grep -i "$TARGET" | \
            grep -v node_modules | \
            head -5)
        
        if [[ -n "$TEST_FILES" ]]; then
            echo "$TEST_FILES" | while read -r file; do
                echo "  • $file"
                echo "### Test File: $file" >> "$CONTEXT_FILE"
                echo '```' >> "$CONTEXT_FILE"
                head -30 "$file" >> "$CONTEXT_FILE" 2>/dev/null
                echo '```' >> "$CONTEXT_FILE"
                echo "" >> "$CONTEXT_FILE"
            done
        else
            echo "  • No test files found - consider TDD approach"
        fi
        
        echo ""
        
        # Search for related API endpoints
        echo "🌐 API Endpoints:"
        API_FILES=$(find . -path "*/api/*" -o -path "*/routes/*" -o -path "*/endpoints/*" | \
            xargs grep -l "$TARGET" 2>/dev/null | \
            grep -v node_modules | \
            head -5)
        
        if [[ -n "$API_FILES" ]]; then
            echo "$API_FILES" | while read -r file; do
                echo "  • $file"
                echo "### API Endpoint: $file" >> "$CONTEXT_FILE"
                echo '```' >> "$CONTEXT_FILE"
                grep -A 20 -B 5 "$TARGET" "$file" >> "$CONTEXT_FILE" 2>/dev/null
                echo '```' >> "$CONTEXT_FILE"
                echo "" >> "$CONTEXT_FILE"
            done
        fi
        
        echo ""
        
        # Search for styles
        echo "🎨 Style Files:"
        STYLE_FILES=$(find . -name "*.css" -o -name "*.scss" -o -name "*.less" | \
            xargs grep -l "$TARGET" 2>/dev/null | \
            grep -v node_modules | \
            head -3)
        
        if [[ -n "$STYLE_FILES" ]]; then
            echo "$STYLE_FILES" | while read -r file; do
                echo "  • $file"
            done
        fi
        
        echo ""
        
        # Search for related documentation
        echo "📖 Documentation:"
        DOC_FILES=$(find . -name "*.md" | \
            xargs grep -l "$TARGET" 2>/dev/null | \
            grep -v node_modules | \
            grep -v "$CONTEXT_DIR" | \
            head -3)
        
        if [[ -n "$DOC_FILES" ]]; then
            echo "$DOC_FILES" | while read -r file; do
                echo "  • $file"
                echo "### Documentation: $file" >> "$CONTEXT_FILE"
                echo '```markdown' >> "$CONTEXT_FILE"
                grep -A 5 -B 5 "$TARGET" "$file" >> "$CONTEXT_FILE" 2>/dev/null
                echo '```' >> "$CONTEXT_FILE"
                echo "" >> "$CONTEXT_FILE"
            done
        fi
        
        echo ""
        echo "✅ Context gathered successfully!"
        echo "📄 Full context saved to: $CONTEXT_FILE"
        echo ""
        echo "🎯 Next Steps:"
        echo "  1. Review the gathered context"
        echo "  2. Use /plan-first to create implementation plan"
        echo "  3. Use /impact-check to understand dependencies"
        echo "  4. Start with /tdd for test-driven development"
        ;;
        
    "bug"|"fix")
        if [[ -z "$TARGET" ]]; then
            echo "❌ Bug description or error message required"
            echo "Usage: context-gather bug [error-keyword]"
            exit 1
        fi
        
        echo "🐛 Gathering Context for Bug: $TARGET"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        mkdir -p "$CONTEXT_DIR"
        
        # Initialize bug context file
        cat > "$CONTEXT_FILE" << EOF
# Bug Investigation Context: $TARGET
Generated: $(date)
Task Type: Bug Fix

## Bug Description
Investigating issue related to: $TARGET

EOF
        
        echo "🔍 Searching for error occurrences..."
        echo ""
        
        # Search for error in code
        echo "💥 Error Locations in Code:"
        ERROR_LOCATIONS=$(find . -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" -o -name "*.py" | \
            xargs grep -n "$TARGET" 2>/dev/null | \
            grep -v node_modules | \
            head -10)
        
        if [[ -n "$ERROR_LOCATIONS" ]]; then
            echo "$ERROR_LOCATIONS" | while IFS=: read -r file line content; do
                echo "  • $file:$line"
                
                # Add to context with surrounding code
                echo "### Error in: $file (line $line)" >> "$CONTEXT_FILE"
                echo '```' >> "$CONTEXT_FILE"
                # Get 10 lines before and after
                sed -n "$((line-10)),$((line+10))p" "$file" 2>/dev/null >> "$CONTEXT_FILE"
                echo '```' >> "$CONTEXT_FILE"
                echo "" >> "$CONTEXT_FILE"
            done
        else
            echo "  • No direct error matches found"
        fi
        
        echo ""
        
        # Search logs
        echo "📋 Log Files:"
        LOG_FILES=$(find . -name "*.log" -o -name "*error*" | \
            grep -v node_modules | \
            head -5)
        
        if [[ -n "$LOG_FILES" ]]; then
            echo "$LOG_FILES" | while read -r file; do
                if grep -q "$TARGET" "$file" 2>/dev/null; then
                    echo "  • $file (contains error)"
                    echo "### Log Entry: $file" >> "$CONTEXT_FILE"
                    echo '```' >> "$CONTEXT_FILE"
                    grep -A 5 -B 5 "$TARGET" "$file" | head -20 >> "$CONTEXT_FILE" 2>/dev/null
                    echo '```' >> "$CONTEXT_FILE"
                    echo "" >> "$CONTEXT_FILE"
                fi
            done
        fi
        
        echo ""
        
        # Search for related tests
        echo "🧪 Related Test Files:"
        TEST_FILES=$(find . -name "*test*" -o -name "*spec*" | \
            xargs grep -l "$TARGET" 2>/dev/null | \
            grep -v node_modules | \
            head -5)
        
        if [[ -n "$TEST_FILES" ]]; then
            echo "$TEST_FILES" | while read -r file; do
                echo "  • $file"
                echo "### Test File: $file" >> "$CONTEXT_FILE"
                echo '```' >> "$CONTEXT_FILE"
                grep -A 10 -B 10 "$TARGET" "$file" >> "$CONTEXT_FILE" 2>/dev/null
                echo '```' >> "$CONTEXT_FILE"
                echo "" >> "$CONTEXT_FILE"
            done
        else
            echo "  • No related tests found"
        fi
        
        echo ""
        
        # Search for error handling patterns
        echo "🛡️ Error Handling Patterns:"
        ERROR_HANDLERS=$(find . -name "*.ts" -o -name "*.tsx" -o -name "*.py" | \
            xargs grep -l "catch\|except\|error\|Error" 2>/dev/null | \
            xargs grep -l "$TARGET" 2>/dev/null | \
            grep -v node_modules | \
            head -5)
        
        if [[ -n "$ERROR_HANDLERS" ]]; then
            echo "$ERROR_HANDLERS" | while read -r file; do
                echo "  • $file"
            done
        fi
        
        echo ""
        echo "✅ Bug context gathered!"
        echo "📄 Full context saved to: $CONTEXT_FILE"
        echo ""
        echo "🎯 Debug Steps:"
        echo "  1. Review error locations and patterns"
        echo "  2. Check test files for reproduction"
        echo "  3. Use /impact-check on affected files"
        echo "  4. Create failing test before fixing"
        ;;
        
    "api"|"endpoint")
        if [[ -z "$TARGET" ]]; then
            echo "❌ API endpoint or route required"
            echo "Usage: context-gather api [endpoint-name]"
            exit 1
        fi
        
        echo "🌐 Gathering Context for API: $TARGET"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        mkdir -p "$CONTEXT_DIR"
        
        # Initialize API context
        cat > "$CONTEXT_FILE" << EOF
# API Context: $TARGET
Generated: $(date)
Task Type: API Development

## API Overview
Gathering context for API endpoint: $TARGET

EOF
        
        echo "🔍 Searching for API definitions..."
        echo ""
        
        # Search for route definitions
        echo "🛣️ Route Definitions:"
        ROUTE_FILES=$(find . -path "*/routes/*" -o -path "*/api/*" -o -path "*/endpoints/*" | \
            xargs grep -l "$TARGET" 2>/dev/null | \
            grep -v node_modules | \
            head -5)
        
        if [[ -n "$ROUTE_FILES" ]]; then
            echo "$ROUTE_FILES" | while read -r file; do
                echo "  • $file"
                echo "### Route Definition: $file" >> "$CONTEXT_FILE"
                echo '```' >> "$CONTEXT_FILE"
                grep -A 20 -B 5 "$TARGET" "$file" >> "$CONTEXT_FILE" 2>/dev/null
                echo '```' >> "$CONTEXT_FILE"
                echo "" >> "$CONTEXT_FILE"
            done
        fi
        
        echo ""
        
        # Search for controllers/handlers
        echo "🎮 Controllers/Handlers:"
        CONTROLLER_FILES=$(find . -path "*/controllers/*" -o -path "*/handlers/*" -o -path "*/services/*" | \
            xargs grep -l "$TARGET" 2>/dev/null | \
            grep -v node_modules | \
            head -5)
        
        if [[ -n "$CONTROLLER_FILES" ]]; then
            echo "$CONTROLLER_FILES" | while read -r file; do
                echo "  • $file"
                echo "### Controller: $file" >> "$CONTEXT_FILE"
                echo '```' >> "$CONTEXT_FILE"
                grep -A 30 -B 10 "$TARGET" "$file" >> "$CONTEXT_FILE" 2>/dev/null
                echo '```' >> "$CONTEXT_FILE"
                echo "" >> "$CONTEXT_FILE"
            done
        fi
        
        echo ""
        
        # Search for models/schemas
        echo "📊 Models/Schemas:"
        MODEL_FILES=$(find . -path "*/models/*" -o -path "*/schemas/*" | \
            xargs grep -l "$TARGET" 2>/dev/null | \
            grep -v node_modules | \
            head -5)
        
        if [[ -n "$MODEL_FILES" ]]; then
            echo "$MODEL_FILES" | while read -r file; do
                echo "  • $file"
                echo "### Model/Schema: $file" >> "$CONTEXT_FILE"
                echo '```' >> "$CONTEXT_FILE"
                cat "$file" | head -50 >> "$CONTEXT_FILE" 2>/dev/null
                echo '```' >> "$CONTEXT_FILE"
                echo "" >> "$CONTEXT_FILE"
            done
        fi
        
        echo ""
        
        # Search for middleware
        echo "🔧 Middleware:"
        MIDDLEWARE_FILES=$(find . -path "*/middleware/*" | \
            xargs grep -l "$TARGET\|auth\|validate" 2>/dev/null | \
            grep -v node_modules | \
            head -3)
        
        if [[ -n "$MIDDLEWARE_FILES" ]]; then
            echo "$MIDDLEWARE_FILES" | while read -r file; do
                echo "  • $file"
            done
        fi
        
        echo ""
        
        # Search for API tests
        echo "🧪 API Tests:"
        API_TEST_FILES=$(find . -name "*test*" -o -name "*spec*" | \
            xargs grep -l "$TARGET\|request\|supertest" 2>/dev/null | \
            grep -v node_modules | \
            head -5)
        
        if [[ -n "$API_TEST_FILES" ]]; then
            echo "$API_TEST_FILES" | while read -r file; do
                echo "  • $file"
                echo "### API Test: $file" >> "$CONTEXT_FILE"
                echo '```' >> "$CONTEXT_FILE"
                grep -A 20 -B 5 "$TARGET" "$file" >> "$CONTEXT_FILE" 2>/dev/null
                echo '```' >> "$CONTEXT_FILE"
                echo "" >> "$CONTEXT_FILE"
            done
        fi
        
        echo ""
        
        # Search for OpenAPI/Swagger docs
        echo "📚 API Documentation:"
        DOC_FILES=$(find . -name "*.yaml" -o -name "*.yml" -o -name "*.json" | \
            xargs grep -l "$TARGET\|swagger\|openapi" 2>/dev/null | \
            grep -v node_modules | \
            head -3)
        
        if [[ -n "$DOC_FILES" ]]; then
            echo "$DOC_FILES" | while read -r file; do
                echo "  • $file"
            done
        fi
        
        echo ""
        echo "✅ API context gathered!"
        echo "📄 Full context saved to: $CONTEXT_FILE"
        echo ""
        echo "🎯 Next Steps:"
        echo "  1. Review route and controller implementations"
        echo "  2. Check models and validation schemas"
        echo "  3. Write API tests first (TDD approach)"
        echo "  4. Update API documentation"
        ;;
        
    "refactor"|"optimize")
        if [[ -z "$TARGET" ]]; then
            echo "❌ Module or component name required"
            echo "Usage: context-gather refactor [module-name]"
            exit 1
        fi
        
        echo "♻️ Gathering Context for Refactoring: $TARGET"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        mkdir -p "$CONTEXT_DIR"
        
        # Initialize refactor context
        cat > "$CONTEXT_FILE" << EOF
# Refactoring Context: $TARGET
Generated: $(date)
Task Type: Code Refactoring

## Refactoring Overview
Analyzing $TARGET for refactoring opportunities.

EOF
        
        echo "🔍 Analyzing code structure..."
        echo ""
        
        # Find the main module file
        echo "📁 Main Module Files:"
        MODULE_FILES=$(find . -name "*$TARGET*" -type f | \
            grep -E "\.(tsx?|jsx?|py)$" | \
            grep -v test | \
            grep -v node_modules | \
            head -5)
        
        if [[ -n "$MODULE_FILES" ]]; then
            echo "$MODULE_FILES" | while read -r file; do
                echo "  • $file"
                
                # Analyze file metrics
                LINE_COUNT=$(wc -l < "$file")
                echo "    Lines: $LINE_COUNT"
                
                # Add full file to context for refactoring
                echo "### Current Implementation: $file" >> "$CONTEXT_FILE"
                echo "Lines of code: $LINE_COUNT" >> "$CONTEXT_FILE"
                echo '```' >> "$CONTEXT_FILE"
                cat "$file" >> "$CONTEXT_FILE" 2>/dev/null
                echo '```' >> "$CONTEXT_FILE"
                echo "" >> "$CONTEXT_FILE"
                
                # Code smell detection
                echo ""
                echo "  🔍 Code Analysis:"
                
                # Check for long functions
                LONG_FUNCTIONS=$(grep -n "function\|const.*=.*=>" "$file" | \
                    awk -F: '{print $1}' | \
                    while read -r start; do
                        end=$(tail -n +$((start+1)) "$file" | grep -n "^}" | head -1 | cut -d: -f1)
                        if [[ -n "$end" ]] && [[ $end -gt 30 ]]; then
                            echo "Long function at line $start (${end} lines)"
                        fi
                    done)
                
                if [[ -n "$LONG_FUNCTIONS" ]]; then
                    echo "    ⚠️  $LONG_FUNCTIONS"
                fi
                
                # Check for code duplication indicators
                DUPLICATE_PATTERNS=$(grep -o "[a-zA-Z_][a-zA-Z0-9_]*" "$file" | \
                    sort | uniq -c | sort -rn | \
                    awk '$1 > 10 {print $2}' | head -3)
                
                if [[ -n "$DUPLICATE_PATTERNS" ]]; then
                    echo "    📋 Frequently repeated: $DUPLICATE_PATTERNS"
                fi
            done
        fi
        
        echo ""
        
        # Find usage patterns
        echo "📊 Usage Analysis:"
        USAGE_COUNT=$(find . -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" -o -name "*.py" | \
            xargs grep -l "$TARGET" 2>/dev/null | \
            grep -v "$TARGET" | \
            grep -v node_modules | \
            wc -l)
        
        echo "  • Used in $USAGE_COUNT files"
        
        # Show top usage files
        echo "  • Top usage locations:"
        find . -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" -o -name "*.py" | \
            xargs grep -l "$TARGET" 2>/dev/null | \
            grep -v "$TARGET" | \
            grep -v node_modules | \
            head -5 | \
            sed 's/^/    - /'
        
        echo ""
        
        # Find related tests for safety
        echo "🧪 Test Coverage:"
        TEST_FILES=$(find . -name "*test*" -o -name "*spec*" | \
            xargs grep -l "$TARGET" 2>/dev/null | \
            grep -v node_modules | \
            head -5)
        
        if [[ -n "$TEST_FILES" ]]; then
            echo "$TEST_FILES" | while read -r file; do
                echo "  • $file"
                echo "### Test File: $file" >> "$CONTEXT_FILE"
                echo '```' >> "$CONTEXT_FILE"
                cat "$file" >> "$CONTEXT_FILE" 2>/dev/null
                echo '```' >> "$CONTEXT_FILE"
                echo "" >> "$CONTEXT_FILE"
            done
            echo "  ✅ Tests found - safe to refactor"
        else
            echo "  ⚠️  No tests found - add tests before refactoring!"
        fi
        
        echo ""
        
        # Design patterns and similar code
        echo "🎨 Similar Patterns in Codebase:"
        
        # Find similar file structures
        if [[ -n "$MODULE_FILES" ]]; then
            BASE_DIR=$(dirname "$(echo "$MODULE_FILES" | head -1)")
            SIMILAR_FILES=$(find "$BASE_DIR" -name "*.${FILE_EXT}" | \
                grep -v "$TARGET" | \
                grep -v test | \
                head -3)
            
            if [[ -n "$SIMILAR_FILES" ]]; then
                echo "  Similar modules for pattern reference:"
                echo "$SIMILAR_FILES" | sed 's/^/  • /'
            fi
        fi
        
        echo ""
        echo "✅ Refactoring context gathered!"
        echo "📄 Full context saved to: $CONTEXT_FILE"
        echo ""
        echo "🎯 Refactoring Checklist:"
        echo "  1. ✓ Review current implementation"
        echo "  2. ✓ Identify code smells and patterns"
        echo "  3. ✓ Ensure test coverage exists"
        echo "  4. → Plan refactoring approach"
        echo "  5. → Update tests for new structure"
        echo "  6. → Refactor incrementally"
        echo "  7. → Run all tests after each change"
        ;;
        
    "task"|"general")
        SEARCH_TERMS="${@:2}"
        
        if [[ -z "$SEARCH_TERMS" ]]; then
            echo "❌ Search terms required"
            echo "Usage: context-gather task [search terms...]"
            exit 1
        fi
        
        echo "📋 Gathering Context for Task: $SEARCH_TERMS"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        mkdir -p "$CONTEXT_DIR"
        
        # Initialize task context
        cat > "$CONTEXT_FILE" << EOF
# Task Context: $SEARCH_TERMS
Generated: $(date)
Task Type: General Development

## Task Overview
Gathering context for: $SEARCH_TERMS

EOF
        
        echo "🔍 Searching across codebase..."
        echo ""
        
        # Search for each term
        for term in $SEARCH_TERMS; do
            echo "🔎 Results for '$term':"
            
            # Code files
            echo "  📄 Code Files:"
            CODE_FILES=$(find . -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" -o -name "*.py" | \
                xargs grep -l "$term" 2>/dev/null | \
                grep -v node_modules | \
                head -5)
            
            if [[ -n "$CODE_FILES" ]]; then
                echo "$CODE_FILES" | while read -r file; do
                    echo "    • $file"
                    echo "### File containing '$term': $file" >> "$CONTEXT_FILE"
                    echo '```' >> "$CONTEXT_FILE"
                    grep -A 5 -B 5 "$term" "$file" | head -20 >> "$CONTEXT_FILE" 2>/dev/null
                    echo '```' >> "$CONTEXT_FILE"
                    echo "" >> "$CONTEXT_FILE"
                done
            else
                echo "    • No code files found"
            fi
            
            # Documentation
            echo "  📚 Documentation:"
            DOC_FILES=$(find . -name "*.md" | \
                xargs grep -l "$term" 2>/dev/null | \
                grep -v node_modules | \
                grep -v "$CONTEXT_DIR" | \
                head -3)
            
            if [[ -n "$DOC_FILES" ]]; then
                echo "$DOC_FILES" | sed 's/^/    • /'
            else
                echo "    • No documentation found"
            fi
            
            echo ""
        done
        
        # Find intersection of terms
        echo "🔗 Files containing multiple terms:"
        INTERSECT_FILES=""
        for term in $SEARCH_TERMS; do
            if [[ -z "$INTERSECT_FILES" ]]; then
                INTERSECT_FILES=$(find . -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" -o -name "*.py" | \
                    xargs grep -l "$term" 2>/dev/null | \
                    grep -v node_modules)
            else
                INTERSECT_FILES=$(echo "$INTERSECT_FILES" | \
                    xargs grep -l "$term" 2>/dev/null)
            fi
        done
        
        if [[ -n "$INTERSECT_FILES" ]]; then
            echo "$INTERSECT_FILES" | head -5 | sed 's/^/  • /'
        else
            echo "  • No files contain all terms"
        fi
        
        echo ""
        echo "✅ Task context gathered!"
        echo "📄 Full context saved to: $CONTEXT_FILE"
        echo ""
        echo "🎯 Next Steps:"
        echo "  1. Review gathered context"
        echo "  2. Use /explore for deeper analysis"
        echo "  3. Use /plan-first to structure approach"
        echo "  4. Use /impact-check before changes"
        ;;
        
    "review"|"show")
        echo "📚 Recent Context Files"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        if [[ -d "$CONTEXT_DIR" ]]; then
            CONTEXT_FILES=$(find "$CONTEXT_DIR" -name "*.md" | sort -r | head -10)
            
            if [[ -n "$CONTEXT_FILES" ]]; then
                echo "Recent context gathering sessions:"
                echo "$CONTEXT_FILES" | while read -r file; do
                    TIMESTAMP=$(stat -c %y "$file" 2>/dev/null || stat -f %Sm "$file" 2>/dev/null)
                    FIRST_LINE=$(head -1 "$file" | sed 's/# //')
                    echo "  • $FIRST_LINE"
                    echo "    File: $file"
                    echo "    Time: $TIMESTAMP"
                    echo ""
                done
                
                echo "💡 To view a context file:"
                echo "  cat [file-path]"
            else
                echo "No context files found"
            fi
        else
            echo "No context directory found"
            echo "Run a context-gather command first"
        fi
        ;;
        
    "clean"|"clear")
        echo "🧹 Cleaning Context Files"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        
        if [[ -d "$CONTEXT_DIR" ]]; then
            # Keep recent files, clean old ones
            find "$CONTEXT_DIR" -name "*.md" -mtime +7 -delete 2>/dev/null
            echo "✅ Cleaned context files older than 7 days"
            
            REMAINING=$(find "$CONTEXT_DIR" -name "*.md" | wc -l)
            echo "📁 Remaining context files: $REMAINING"
        else
            echo "ℹ️  No context directory to clean"
        fi
        ;;
        
    "help"|"--help"|\"-h\"|*)
        echo "📚 Context Gathering Commands"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Usage: context-gather [type] [target]"
        echo ""
        echo "Context Types:"
        echo "  feature [name]         Gather context for feature/component work"
        echo "  bug [error-text]       Gather context for debugging"
        echo "  api [endpoint]         Gather context for API development"
        echo "  refactor [module]      Gather context for refactoring"
        echo "  task [keywords...]     General task context gathering"
        echo ""
        echo "Management:"
        echo "  review                 Show recent context files"
        echo "  clean                  Remove old context files"
        echo "  help                   Show this help"
        echo ""
        echo "Examples:"
        echo "  /context-gather feature UserProfile"
        echo "  /context-gather bug \"undefined is not a function\""
        echo "  /context-gather api /users/profile"
        echo "  /context-gather refactor AuthService"
        echo "  /context-gather task authentication jwt token"
        echo ""
        echo "💡 Features:"
        echo "  • Automatically finds relevant files"
        echo "  • Gathers code, tests, docs, and configs"
        echo "  • Creates comprehensive context documents"
        echo "  • Identifies patterns and relationships"
        echo "  • Saves context for future reference"
        echo ""
        echo "Context files are saved in: $CONTEXT_DIR/"
        ;;
esac
```

Intelligent context gathering system that automatically finds and reads all files relevant to your development task.
**Security Note:** This command performs read-only file analysis and creates local context documents. No modifications to source files.