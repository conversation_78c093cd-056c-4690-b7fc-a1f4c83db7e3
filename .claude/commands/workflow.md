Get step-by-step guidance for common development scenarios. Execute:
```bash
# Scenario-based workflow guidance
SCENARIO="${1:-help}"

case "$SCENARIO" in
    "bug"|"fix"|"fix-bug"|"bug-fix")
        echo "🐛 Bug Fix Workflow"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Step 1: 🔍 Explore & Understand"
        echo "  /explore [affected-area]     # Understand the codebase"
        echo "  /grep \"error message\"       # Find where error occurs"
        echo "  /read [suspicious-file]      # Examine specific files"
        echo ""
        echo "Step 2: 📋 Plan the Fix"
        echo "  /plan-first bug             # Create bug fix plan"
        echo "  /remember \"bug details\"     # Store important context"
        echo ""
        echo "Step 3: 🧪 Test First (TDD)"
        echo "  # Write a test that reproduces the bug"
        echo "  /test [test-file]           # Verify test fails"
        echo ""
        echo "Step 4: 🔧 Implement Fix"
        echo "  # Make minimal changes to fix the issue"
        echo "  /test [test-file]           # Verify test passes"
        echo ""
        echo "Step 5: ✅ Validate"
        echo "  /test unit                  # Run all unit tests"
        echo "  /test integration           # Check integrations"
        echo "  /check all                  # Quality checks"
        echo ""
        echo "Step 6: 📦 Prepare for Commit"
        echo "  /precommit                  # Final validation"
        echo "  /wrap-it-up                 # Document context"
        echo ""
        echo "💡 Remember: Always explore → plan → test → fix → validate"
        ;;
        
    "feature"|"add-feature"|"new-feature")
        echo "✨ Feature Development Workflow"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Step 1: 🔍 Research & Explore"
        echo "  /explore [target-area]      # Understand existing code"
        echo "  /research \"best practices\"  # Research approaches"
        echo "  /patterns                   # Check project patterns"
        echo ""
        echo "Step 2: 📋 Design & Plan"
        echo "  /plan-first feature         # Create feature plan"
        echo "  /track-decision \"approach\"  # Document decisions"
        echo ""
        echo "Step 3: 🏗️ Foundation"
        echo "  /db migrate                 # Database changes first"
        echo "  # Create data models/schemas"
        echo "  # Write model tests"
        echo ""
        echo "Step 4: 🔧 Backend Implementation"
        echo "  # Create API endpoints"
        echo "  /test backend unit          # Test as you go"
        echo "  # Add service layer"
        echo "  /test backend integration   # Test integrations"
        echo ""
        echo "Step 5: 🎨 Frontend Implementation"
        echo "  # Create UI components"
        echo "  /storybook                  # Develop in isolation"
        echo "  # Connect to backend"
        echo "  /test frontend unit         # Test components"
        echo ""
        echo "Step 6: 🔗 Integration"
        echo "  /test e2e                   # End-to-end testing"
        echo "  /visual-iterate             # UI refinement"
        echo "  /performance                # Check performance"
        echo ""
        echo "Step 7: 📚 Documentation"
        echo "  # Update API docs"
        echo "  # Add usage examples"
        echo "  /document                   # Generate docs"
        echo ""
        echo "💡 Build incrementally, test continuously"
        ;;
        
    "refactor"|"cleanup"|"tech-debt")
        echo "🔧 Refactoring Workflow"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Step 1: 📊 Assess Current State"
        echo "  /explore [target-code]      # Understand structure"
        echo "  /test                       # Ensure tests pass"
        echo "  /performance                # Baseline performance"
        echo ""
        echo "Step 2: 📋 Plan Refactoring"
        echo "  /plan-first refactor        # Create refactor plan"
        echo "  /track-decision \"why\"       # Document reasoning"
        echo ""
        echo "Step 3: 🛡️ Safety Net"
        echo "  # Ensure test coverage"
        echo "  /test coverage              # Check coverage"
        echo "  # Add missing tests"
        echo "  # Create feature flag"
        echo ""
        echo "Step 4: 🔄 Incremental Changes"
        echo "  # Small, safe changes"
        echo "  /test                       # Test after each change"
        echo "  /checkpoint                 # Save progress points"
        echo "  # Commit frequently"
        echo ""
        echo "Step 5: ✅ Validation"
        echo "  /test all                   # Comprehensive testing"
        echo "  /performance                # Compare performance"
        echo "  /check all                  # Code quality"
        echo ""
        echo "💡 Small steps, test often, keep tests green"
        ;;
        
    "optimize"|"performance"|"slow")
        echo "🚀 Performance Optimization Workflow"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Step 1: 📊 Measure & Profile"
        echo "  /performance                # Run benchmarks"
        echo "  /logs [env]                # Check for errors"
        echo "  # Profile database queries"
        echo "  # Monitor resource usage"
        echo ""
        echo "Step 2: 🔍 Identify Bottlenecks"
        echo "  /explore [slow-area]        # Examine code"
        echo "  /plan-first performance     # Plan optimization"
        echo ""
        echo "Step 3: 🎯 Quick Wins"
        echo "  # Add caching"
        echo "  # Optimize queries"
        echo "  # Fix N+1 problems"
        echo "  /test                       # Ensure correctness"
        echo ""
        echo "Step 4: 🏗️ Major Optimizations"
        echo "  # Refactor algorithms"
        echo "  # Add pagination"
        echo "  # Implement lazy loading"
        echo "  /performance                # Measure improvement"
        echo ""
        echo "Step 5: ✅ Validate"
        echo "  /test all                   # No regressions"
        echo "  /check all                  # Code quality"
        echo "  # Load testing"
        echo ""
        echo "💡 Measure → Optimize → Measure again"
        ;;
        
    "hotfix"|"emergency"|"urgent")
        echo "🚨 Emergency Hotfix Workflow"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Step 1: 🔥 Assess Situation"
        echo "  /logs production            # Check prod logs"
        echo "  /debug                      # Diagnose issue"
        echo "  /safe-mode production       # Enable safety"
        echo ""
        echo "Step 2: 🎯 Minimal Fix"
        echo "  /explore [problem-area]     # Quick analysis"
        echo "  # Implement minimal fix"
        echo "  /test [specific-area]       # Test the fix"
        echo ""
        echo "Step 3: 🚀 Deploy Path"
        echo "  /test [critical-paths]      # Test critical paths"
        echo "  /deploy-dev                 # Test in dev"
        echo "  # Monitor closely"
        echo ""
        echo "Step 4: 📋 Follow Up"
        echo "  /track-decision \"hotfix\"    # Document decision"
        echo "  /plan-first bug             # Plan proper fix"
        echo "  /remember \"technical debt\"  # Track for later"
        echo ""
        echo "💡 Fix fast, test critical paths, plan proper fix"
        ;;
        
    "review"|"pr"|"pull-request")
        echo "👀 Code Review Workflow"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Step 1: 📊 Understand Changes"
        echo "  /explore [changed-files]    # Understand context"
        echo "  # Read PR description"
        echo "  # Check linked issues"
        echo ""
        echo "Step 2: 🧪 Test Changes"
        echo "  # Pull branch locally"
        echo "  /test all                   # Run full test suite"
        echo "  /performance                # Check performance"
        echo ""
        echo "Step 3: 🔍 Review Code"
        echo "  # Check logic correctness"
        echo "  # Review test coverage"
        echo "  # Verify best practices"
        echo "  /check all                  # Automated checks"
        echo ""
        echo "Step 4: 💬 Provide Feedback"
        echo "  # Be constructive"
        echo "  # Suggest improvements"
        echo "  # Acknowledge good work"
        echo ""
        echo "💡 Test locally, be thorough, be kind"
        ;;
        
    "onboard"|"learn"|"new")
        echo "🎓 New Developer Onboarding Workflow"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Step 1: 🚀 Get Started"
        echo "  /getting-started            # Interactive guide"
        echo "  /explore                    # Explore codebase"
        echo "  # Read CLAUDE.md"
        echo ""
        echo "Step 2: 🛠️ Set Up Environment"
        echo "  /up dev                     # Start development"
        echo "  /db migrate                 # Setup database"
        echo "  /test                       # Verify setup"
        echo ""
        echo "Step 3: 📚 Learn the Codebase"
        echo "  /explore apps/backend       # Explore backend"
        echo "  /explore apps/web          # Explore frontend"
        echo "  /patterns                   # Learn patterns"
        echo ""
        echo "Step 4: 🏃 First Task"
        echo "  /workflow bug               # Learn bug workflow"
        echo "  /plan-first bug             # Practice planning"
        echo "  # Make first contribution"
        echo ""
        echo "💡 Take time to explore, ask questions, learn patterns"
        ;;
        
    "help"|"--help"|"-h"|*)
        echo "🔄 Workflow Guidance"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Usage: workflow [scenario]"
        echo ""
        echo "Available Scenarios:"
        echo "  bug, fix           🐛 Fix a bug"
        echo "  feature            ✨ Add new feature"
        echo "  refactor          🔧 Refactor code"
        echo "  optimize          🚀 Improve performance"
        echo "  hotfix            🚨 Emergency fix"
        echo "  review, pr        👀 Review code"
        echo "  onboard, new      🎓 New developer"
        echo ""
        echo "Examples:"
        echo "  /workflow bug      # Bug fix guidance"
        echo "  /workflow feature  # Feature development"
        echo "  /workflow optimize # Performance tuning"
        echo ""
        echo "💡 Each workflow provides:"
        echo "  • Step-by-step instructions"
        echo "  • Relevant commands to use"
        echo "  • Best practices tips"
        echo "  • Common pitfalls to avoid"
        ;;
esac

echo ""
echo "Need more help? Try:"
echo "  /getting-started    # General onboarding"
echo "  /help [command]     # Command-specific help"
echo "  /recall workflow    # Previous workflow tips"
```

Step-by-step guidance for common development scenarios. Shows which commands to use and when.
**Security Note:** This command provides guidance only and does not execute other commands.