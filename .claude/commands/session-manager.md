Advanced session management with templates, checkpoints, and context preservation. Execute:
```bash
# Session management system for development workflows
ACTION="${1:-help}"
SESSION_NAME="${2:-}"
SESSION_DIR=".claude-sessions"
TEMPLATE_DIR="$SESSION_DIR/templates"
CHECKPOINT_DIR="$SESSION_DIR/checkpoints"

case "$ACTION" in
    "start"|"begin"|"new")
        if [[ -z "$SESSION_NAME" ]]; then
            echo "❌ Session name required"
            echo "Usage: session-manager start [session-name] [template]"
            exit 1
        fi
        
        TEMPLATE="${3:-default}"
        
        echo "🚀 Starting Development Session: $SESSION_NAME"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        # Create session directories
        mkdir -p "$SESSION_DIR/$SESSION_NAME"
        
        # Check if session already exists
        if [[ -f "$SESSION_DIR/$SESSION_NAME/session.json" ]]; then
            echo "⚠️  Session '$SESSION_NAME' already exists"
            echo "Would you like to:"
            echo "  1. Resume existing session: /session-manager resume $SESSION_NAME"
            echo "  2. Create new checkpoint: /session-manager checkpoint $SESSION_NAME"
            echo "  3. Override (data will be lost): /session-manager start $SESSION_NAME --force"
            exit 1
        fi
        
        # Load template
        TEMPLATE_FILE="$TEMPLATE_DIR/$TEMPLATE.json"
        if [[ -f "$TEMPLATE_FILE" ]]; then
            echo "📋 Loading template: $TEMPLATE"
            TEMPLATE_DATA=$(cat "$TEMPLATE_FILE")
        else
            echo "📋 Creating session with default template"
            TEMPLATE_DATA='{
                "type": "default",
                "description": "General development session",
                "initial_tasks": [],
                "environment_setup": [],
                "git_workflow": "feature-branch"
            }'
        fi
        
        # Create session metadata
        cat > "$SESSION_DIR/$SESSION_NAME/session.json" << EOF
{
  "session_name": "$SESSION_NAME",
  "template": "$TEMPLATE",
  "created_at": "$(date -Iseconds)",
  "last_active": "$(date -Iseconds)",
  "status": "active",
  "git_branch": "$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo 'unknown')",
  "git_commit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
  "template_data": $TEMPLATE_DATA,
  "checkpoints": [],
  "tasks_completed": [],
  "context": {
    "working_directory": "$(pwd)",
    "environment_variables": {},
    "active_services": []
  }
}
EOF
        
        # Create session log
        echo "$(date -Iseconds) - Session '$SESSION_NAME' started with template '$TEMPLATE'" > "$SESSION_DIR/$SESSION_NAME/session.log"
        
        # Apply template setup
        echo "🔧 Applying template setup..."
        
        # Parse template and execute setup commands
        SETUP_COMMANDS=$(echo "$TEMPLATE_DATA" | grep -o '"environment_setup":\s*\[[^]]*\]' | sed 's/.*\[\(.*\)\].*/\1/' | tr ',' '\n' | sed 's/[",]//g' | grep -v '^$')
        
        if [[ -n "$SETUP_COMMANDS" ]]; then
            echo "$SETUP_COMMANDS" | while read -r cmd; do
                if [[ -n "$cmd" ]]; then
                    echo "  Running: $cmd"
                    eval "$cmd" || echo "  ⚠️  Command failed: $cmd"
                fi
            done
        fi
        
        echo ""
        echo "✅ Session '$SESSION_NAME' started successfully!"
        echo ""
        echo "📊 Session Details:"
        echo "  • Name: $SESSION_NAME"
        echo "  • Template: $TEMPLATE"
        echo "  • Git Branch: $(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo 'unknown')"
        echo "  • Working Directory: $(pwd)"
        echo ""
        echo "🎯 Next Steps:"
        echo "  • Add tasks: /session-manager task $SESSION_NAME add 'Task description'"
        echo "  • Create checkpoint: /session-manager checkpoint $SESSION_NAME"
        echo "  • View status: /session-manager status $SESSION_NAME"
        echo ""
        echo "💡 Session active! Use checkpoints to save progress."
        ;;
        
    "resume"|"continue")
        if [[ -z "$SESSION_NAME" ]]; then
            echo "❌ Session name required"
            echo "Usage: session-manager resume [session-name]"
            exit 1
        fi
        
        SESSION_FILE="$SESSION_DIR/$SESSION_NAME/session.json"
        
        if [[ ! -f "$SESSION_FILE" ]]; then
            echo "❌ Session not found: $SESSION_NAME"
            echo ""
            echo "Available sessions:"
            find "$SESSION_DIR" -name "session.json" -exec dirname {} \; | xargs -I {} basename {} | head -10
            exit 1
        fi
        
        echo "🔄 Resuming Session: $SESSION_NAME"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        # Load session data
        CREATED_AT=$(grep created_at "$SESSION_FILE" | cut -d'"' -f4)
        LAST_ACTIVE=$(grep last_active "$SESSION_FILE" | cut -d'"' -f4)
        STATUS=$(grep '"status"' "$SESSION_FILE" | cut -d'"' -f4)
        GIT_BRANCH=$(grep git_branch "$SESSION_FILE" | cut -d'"' -f4)
        
        echo "📊 Session Information:"
        echo "  • Created: $CREATED_AT"
        echo "  • Last Active: $LAST_ACTIVE"
        echo "  • Status: $STATUS"
        echo "  • Git Branch: $GIT_BRANCH"
        echo ""
        
        # Update last active time
        TEMP_FILE=$(mktemp)
        sed "s/\"last_active\": \"[^\"]*\"/\"last_active\": \"$(date -Iseconds)\"/" "$SESSION_FILE" > "$TEMP_FILE"
        mv "$TEMP_FILE" "$SESSION_FILE"
        
        # Update status to active
        TEMP_FILE=$(mktemp)
        sed 's/"status": "[^"]*"/"status": "active"/' "$SESSION_FILE" > "$TEMP_FILE"
        mv "$TEMP_FILE" "$SESSION_FILE"
        
        # Show recent activity
        echo "📋 Recent Activity:"
        if [[ -f "$SESSION_DIR/$SESSION_NAME/session.log" ]]; then
            tail -5 "$SESSION_DIR/$SESSION_NAME/session.log" | sed 's/^/  /'
        fi
        
        echo ""
        
        # Show active checkpoints
        CHECKPOINT_COUNT=$(find "$CHECKPOINT_DIR/$SESSION_NAME" -name "*.json" 2>/dev/null | wc -l)
        if [[ $CHECKPOINT_COUNT -gt 0 ]]; then
            echo "💾 Available Checkpoints: $CHECKPOINT_COUNT"
            find "$CHECKPOINT_DIR/$SESSION_NAME" -name "*.json" 2>/dev/null | sort -r | head -3 | while read -r checkpoint; do
                CHECKPOINT_NAME=$(basename "$checkpoint" .json)
                CHECKPOINT_TIME=$(grep timestamp "$checkpoint" | cut -d'"' -f4 2>/dev/null || echo "unknown")
                echo "  • $CHECKPOINT_NAME ($CHECKPOINT_TIME)"
            done
            echo "  Use: /session-manager restore $SESSION_NAME [checkpoint-name]"
        fi
        
        echo ""
        echo "✅ Session '$SESSION_NAME' resumed!"
        echo ""
        echo "🎯 Available Actions:"
        echo "  /session-manager status $SESSION_NAME     # Show current status"
        echo "  /session-manager checkpoint $SESSION_NAME # Save current state"
        echo "  /session-manager task $SESSION_NAME list  # View tasks"
        
        # Log resumption
        echo "$(date -Iseconds) - Session '$SESSION_NAME' resumed" >> "$SESSION_DIR/$SESSION_NAME/session.log"
        ;;
        
    "checkpoint"|"save")
        if [[ -z "$SESSION_NAME" ]]; then
            echo "❌ Session name required"
            echo "Usage: session-manager checkpoint [session-name] [checkpoint-name]"
            exit 1
        fi
        
        CHECKPOINT_NAME="${3:-$(date +%Y%m%d-%H%M%S)}"
        
        SESSION_FILE="$SESSION_DIR/$SESSION_NAME/session.json"
        
        if [[ ! -f "$SESSION_FILE" ]]; then
            echo "❌ Session not found: $SESSION_NAME"
            exit 1
        fi
        
        echo "💾 Creating Checkpoint: $CHECKPOINT_NAME"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        # Create checkpoint directory
        mkdir -p "$CHECKPOINT_DIR/$SESSION_NAME"
        
        # Gather system state
        echo "📊 Gathering system state..."
        
        # Git state
        GIT_STATUS=$(git status --porcelain 2>/dev/null || echo "")
        GIT_COMMIT=$(git rev-parse HEAD 2>/dev/null || echo "unknown")
        GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
        
        # Environment state
        DOCKER_PS=$(docker ps --format "table {{.Names}}\t{{.Status}}" 2>/dev/null || echo "Docker not available")
        
        # Development servers
        DEV_SERVER_STATUS="stopped"
        STORYBOOK_STATUS="stopped"
        
        curl -s http://localhost:5173 > /dev/null 2>&1 && DEV_SERVER_STATUS="running"
        curl -s http://localhost:6006 > /dev/null 2>&1 && STORYBOOK_STATUS="running"
        
        # Create checkpoint metadata
        cat > "$CHECKPOINT_DIR/$SESSION_NAME/$CHECKPOINT_NAME.json" << EOF
{
  "checkpoint_name": "$CHECKPOINT_NAME",
  "session_name": "$SESSION_NAME",
  "timestamp": "$(date -Iseconds)",
  "git_state": {
    "commit": "$GIT_COMMIT",
    "branch": "$GIT_BRANCH",
    "status": "$(echo "$GIT_STATUS" | head -10 | tr '\n' '|')",
    "has_uncommitted": $([ -n "$GIT_STATUS" ] && echo true || echo false)
  },
  "environment": {
    "working_directory": "$(pwd)",
    "dev_server": "$DEV_SERVER_STATUS",
    "storybook": "$STORYBOOK_STATUS",
    "docker_containers": "$(echo "$DOCKER_PS" | tail -n +2 | wc -l || echo 0)"
  },
  "files_changed": [
$(echo "$GIT_STATUS" | head -10 | sed 's/^[^/]*\(.*\)$/    "\1"/' | sed '$!s/$/,/')
  ],
  "description": "Checkpoint created during active development"
}
EOF
        
        # Update session with checkpoint reference
        TEMP_FILE=$(mktemp)
        python3 -c "
import json
import sys

try:
    with open('$SESSION_FILE', 'r') as f:
        session = json.load(f)
    
    if 'checkpoints' not in session:
        session['checkpoints'] = []
    
    session['checkpoints'].append({
        'name': '$CHECKPOINT_NAME',
        'timestamp': '$(date -Iseconds)',
        'git_commit': '$GIT_COMMIT'
    })
    
    # Keep only last 10 checkpoints
    session['checkpoints'] = session['checkpoints'][-10:]
    
    with open('$TEMP_FILE', 'w') as f:
        json.dump(session, f, indent=2)
        
except Exception as e:
    print(f'Error updating session: {e}', file=sys.stderr)
    sys.exit(1)
" && mv "$TEMP_FILE" "$SESSION_FILE"
        
        echo "✅ Checkpoint '$CHECKPOINT_NAME' created!"
        echo ""
        echo "💾 Checkpoint Details:"
        echo "  • Name: $CHECKPOINT_NAME"
        echo "  • Git Branch: $GIT_BRANCH"
        echo "  • Git Commit: ${GIT_COMMIT:0:8}"
        echo "  • Dev Server: $DEV_SERVER_STATUS"
        echo "  • Modified Files: $(echo "$GIT_STATUS" | wc -l)"
        echo ""
        echo "🔄 Restore with: /session-manager restore $SESSION_NAME $CHECKPOINT_NAME"
        
        # Log checkpoint
        echo "$(date -Iseconds) - Checkpoint '$CHECKPOINT_NAME' created" >> "$SESSION_DIR/$SESSION_NAME/session.log"
        ;;
        
    "restore"|"load")
        if [[ -z "$SESSION_NAME" ]]; then
            echo "❌ Session name required"
            echo "Usage: session-manager restore [session-name] [checkpoint-name]"
            exit 1
        fi
        
        CHECKPOINT_NAME="${3:-}"
        
        if [[ -z "$CHECKPOINT_NAME" ]]; then
            echo "❌ Checkpoint name required"
            echo ""
            echo "Available checkpoints for $SESSION_NAME:"
            find "$CHECKPOINT_DIR/$SESSION_NAME" -name "*.json" 2>/dev/null | sort -r | head -5 | while read -r checkpoint; do
                CHECKPOINT_FILE=$(basename "$checkpoint" .json)
                CHECKPOINT_TIME=$(grep timestamp "$checkpoint" | cut -d'"' -f4 2>/dev/null || echo "unknown")
                echo "  • $CHECKPOINT_FILE ($CHECKPOINT_TIME)"
            done
            exit 1
        fi
        
        CHECKPOINT_FILE="$CHECKPOINT_DIR/$SESSION_NAME/$CHECKPOINT_NAME.json"
        
        if [[ ! -f "$CHECKPOINT_FILE" ]]; then
            echo "❌ Checkpoint not found: $CHECKPOINT_NAME"
            exit 1
        fi
        
        echo "🔄 Restoring Checkpoint: $CHECKPOINT_NAME"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        # Load checkpoint data
        CHECKPOINT_COMMIT=$(grep '"commit"' "$CHECKPOINT_FILE" | cut -d'"' -f4)
        CHECKPOINT_BRANCH=$(grep '"branch"' "$CHECKPOINT_FILE" | cut -d'"' -f4)
        CHECKPOINT_TIME=$(grep timestamp "$CHECKPOINT_FILE" | cut -d'"' -f4)
        
        echo "📊 Checkpoint Information:"
        echo "  • Created: $CHECKPOINT_TIME"
        echo "  • Git Branch: $CHECKPOINT_BRANCH"
        echo "  • Git Commit: ${CHECKPOINT_COMMIT:0:8}"
        echo ""
        
        # Warn about current changes
        CURRENT_STATUS=$(git status --porcelain 2>/dev/null || echo "")
        if [[ -n "$CURRENT_STATUS" ]]; then
            echo "⚠️  Warning: You have uncommitted changes!"
            echo "Current uncommitted files:"
            echo "$CURRENT_STATUS" | head -5 | sed 's/^/  /'
            echo ""
            echo "Recommended actions:"
            echo "  1. Commit current changes: git add . && git commit -m 'Save before restore'"
            echo "  2. Stash changes: git stash"
            echo "  3. Create checkpoint: /session-manager checkpoint $SESSION_NAME current-state"
            echo ""
            echo "Continue restore anyway? [y/N]"
            read -r response
            if [[ ! "$response" =~ ^[Yy]$ ]]; then
                echo "Restore cancelled"
                exit 1
            fi
        fi
        
        # Restore git state
        echo "🔄 Restoring git state..."
        
        # Switch to checkpoint branch if different
        CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
        if [[ "$CURRENT_BRANCH" != "$CHECKPOINT_BRANCH" ]]; then
            echo "  Switching to branch: $CHECKPOINT_BRANCH"
            git checkout "$CHECKPOINT_BRANCH" 2>/dev/null || echo "  ⚠️  Failed to switch branch"
        fi
        
        # Reset to checkpoint commit
        echo "  Resetting to commit: ${CHECKPOINT_COMMIT:0:8}"
        git reset --hard "$CHECKPOINT_COMMIT" 2>/dev/null || echo "  ⚠️  Failed to reset to commit"
        
        echo ""
        echo "✅ Checkpoint '$CHECKPOINT_NAME' restored!"
        echo ""
        echo "🎯 Next Steps:"
        echo "  • Check git status: git status"
        echo "  • Start development: /run dev"
        echo "  • Create new checkpoint after changes"
        
        # Log restoration
        echo "$(date -Iseconds) - Checkpoint '$CHECKPOINT_NAME' restored" >> "$SESSION_DIR/$SESSION_NAME/session.log"
        ;;
        
    "task"|"tasks")
        if [[ -z "$SESSION_NAME" ]]; then
            echo "❌ Session name required"
            echo "Usage: session-manager task [session-name] [action] [task-description]"
            exit 1
        fi
        
        TASK_ACTION="${3:-list}"
        TASK_DESCRIPTION="${4:-}"
        
        SESSION_FILE="$SESSION_DIR/$SESSION_NAME/session.json"
        
        if [[ ! -f "$SESSION_FILE" ]]; then
            echo "❌ Session not found: $SESSION_NAME"
            exit 1
        fi
        
        case "$TASK_ACTION" in
            "add"|"create")
                if [[ -z "$TASK_DESCRIPTION" ]]; then
                    echo "❌ Task description required"
                    echo "Usage: session-manager task $SESSION_NAME add 'Task description'"
                    exit 1
                fi
                
                echo "✅ Adding Task: $TASK_DESCRIPTION"
                
                # Add task to session file (simplified approach)
                echo "$(date -Iseconds) - TASK_ADDED: $TASK_DESCRIPTION" >> "$SESSION_DIR/$SESSION_NAME/session.log"
                echo "Task added to session log"
                ;;
                
            "complete"|"done")
                if [[ -z "$TASK_DESCRIPTION" ]]; then
                    echo "❌ Task description required"
                    echo "Usage: session-manager task $SESSION_NAME complete 'Task description'"
                    exit 1
                fi
                
                echo "🎉 Completing Task: $TASK_DESCRIPTION"
                echo "$(date -Iseconds) - TASK_COMPLETED: $TASK_DESCRIPTION" >> "$SESSION_DIR/$SESSION_NAME/session.log"
                echo "Task marked as completed"
                ;;
                
            "list"|*)
                echo "📋 Session Tasks: $SESSION_NAME"
                echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
                echo ""
                
                if [[ -f "$SESSION_DIR/$SESSION_NAME/session.log" ]]; then
                    echo "📝 Added Tasks:"
                    grep "TASK_ADDED" "$SESSION_DIR/$SESSION_NAME/session.log" | sed 's/.*TASK_ADDED: /  • /' | tail -10
                    echo ""
                    echo "✅ Completed Tasks:"
                    grep "TASK_COMPLETED" "$SESSION_DIR/$SESSION_NAME/session.log" | sed 's/.*TASK_COMPLETED: /  • /' | tail -10
                else
                    echo "No tasks recorded yet"
                fi
                ;;
        esac
        ;;
        
    "status"|"info")
        if [[ -z "$SESSION_NAME" ]]; then
            echo "📊 Session Manager Status"
            echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
            echo ""
            
            # List all sessions
            echo "🗂️ All Sessions:"
            if [[ -d "$SESSION_DIR" ]]; then
                find "$SESSION_DIR" -name "session.json" | while read -r session_file; do
                    SESSION_PATH=$(dirname "$session_file")
                    SESSION=$(basename "$SESSION_PATH")
                    STATUS=$(grep '"status"' "$session_file" | cut -d'"' -f4 2>/dev/null || echo "unknown")
                    LAST_ACTIVE=$(grep last_active "$session_file" | cut -d'"' -f4 2>/dev/null || echo "unknown")
                    
                    if [[ "$STATUS" == "active" ]]; then
                        echo "  🟢 $SESSION (last active: $LAST_ACTIVE)"
                    else
                        echo "  ⚪ $SESSION (last active: $LAST_ACTIVE)"
                    fi
                done
            else
                echo "  No sessions found"
            fi
            
            echo ""
            echo "💡 Quick Actions:"
            echo "  /session-manager start [name]        # Create new session"
            echo "  /session-manager resume [name]       # Resume existing session"
            echo "  /session-manager status [name]       # Show session details"
            
            exit 0
        fi
        
        SESSION_FILE="$SESSION_DIR/$SESSION_NAME/session.json"
        
        if [[ ! -f "$SESSION_FILE" ]]; then
            echo "❌ Session not found: $SESSION_NAME"
            exit 1
        fi
        
        echo "📊 Session Status: $SESSION_NAME"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        # Parse session data
        CREATED_AT=$(grep created_at "$SESSION_FILE" | cut -d'"' -f4)
        LAST_ACTIVE=$(grep last_active "$SESSION_FILE" | cut -d'"' -f4)
        STATUS=$(grep '"status"' "$SESSION_FILE" | cut -d'"' -f4)
        GIT_BRANCH=$(grep git_branch "$SESSION_FILE" | cut -d'"' -f4)
        
        echo "📋 Session Information:"
        echo "  • Name: $SESSION_NAME"
        echo "  • Status: $STATUS"
        echo "  • Created: $CREATED_AT"
        echo "  • Last Active: $LAST_ACTIVE"
        echo "  • Git Branch: $GIT_BRANCH"
        echo ""
        
        # Show checkpoints
        CHECKPOINT_COUNT=$(find "$CHECKPOINT_DIR/$SESSION_NAME" -name "*.json" 2>/dev/null | wc -l)
        echo "💾 Checkpoints: $CHECKPOINT_COUNT"
        if [[ $CHECKPOINT_COUNT -gt 0 ]]; then
            find "$CHECKPOINT_DIR/$SESSION_NAME" -name "*.json" 2>/dev/null | sort -r | head -3 | while read -r checkpoint; do
                CHECKPOINT_NAME=$(basename "$checkpoint" .json)
                CHECKPOINT_TIME=$(grep timestamp "$checkpoint" | cut -d'"' -f4 2>/dev/null || echo "unknown")
                echo "  • $CHECKPOINT_NAME ($CHECKPOINT_TIME)"
            done
        fi
        
        echo ""
        
        # Show recent activity
        echo "📋 Recent Activity:"
        if [[ -f "$SESSION_DIR/$SESSION_NAME/session.log" ]]; then
            tail -5 "$SESSION_DIR/$SESSION_NAME/session.log" | sed 's/^/  /'
        else
            echo "  No activity recorded"
        fi
        ;;
        
    "template"|"templates")
        TEMPLATE_ACTION="${2:-list}"
        
        case "$TEMPLATE_ACTION" in
            "create"|"new")
                TEMPLATE_NAME="${3:-}"
                if [[ -z "$TEMPLATE_NAME" ]]; then
                    echo "❌ Template name required"
                    echo "Usage: session-manager template create [template-name]"
                    exit 1
                fi
                
                echo "📋 Creating Session Template: $TEMPLATE_NAME"
                echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
                echo ""
                
                mkdir -p "$TEMPLATE_DIR"
                
                # Create template file
                cat > "$TEMPLATE_DIR/$TEMPLATE_NAME.json" << EOF
{
  "type": "$TEMPLATE_NAME",
  "description": "Custom development session template",
  "initial_tasks": [
    "Set up development environment",
    "Review requirements",
    "Create feature branch"
  ],
  "environment_setup": [
    "/run dev",
    "/db migrate"
  ],
  "git_workflow": "feature-branch",
  "suggested_commands": [
    "/explore",
    "/plan-first",
    "/test"
  ],
  "checkpoint_intervals": "15-minutes"
}
EOF
                
                echo "✅ Template '$TEMPLATE_NAME' created!"
                echo "📝 Edit template: /edit $TEMPLATE_DIR/$TEMPLATE_NAME.json"
                ;;
                
            "list"|*)
                echo "📋 Available Session Templates"
                echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
                echo ""
                
                if [[ -d "$TEMPLATE_DIR" ]]; then
                    find "$TEMPLATE_DIR" -name "*.json" | while read -r template_file; do
                        TEMPLATE_NAME=$(basename "$template_file" .json)
                        DESCRIPTION=$(grep description "$template_file" | cut -d'"' -f4 2>/dev/null || echo "No description")
                        echo "  📋 $TEMPLATE_NAME"
                        echo "     $DESCRIPTION"
                    done
                else
                    echo "  No custom templates found"
                fi
                
                echo ""
                echo "💡 Built-in templates:"
                echo "  📋 default - General development session"
                echo "  📋 feature - Feature development workflow"
                echo "  📋 bugfix - Bug fixing workflow"
                echo "  📋 review - Code review session"
                ;;
        esac
        ;;
        
    "help"|"--help"|\"-h\"|*)
        echo "🗂️ Session Manager Commands"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Usage: session-manager [action] [arguments]"
        echo ""
        echo "Session Management:"
        echo "  start [name] [template]     Create new development session"
        echo "  resume [name]               Resume existing session"
        echo "  status [name]               Show session information"
        echo "  status                      List all sessions"
        echo ""
        echo "Checkpoints:"
        echo "  checkpoint [name] [label]   Save current state"
        echo "  restore [name] [checkpoint] Restore saved state"
        echo ""
        echo "Task Management:"
        echo "  task [name] add [task]      Add task to session"
        echo "  task [name] complete [task] Mark task as completed"
        echo "  task [name] list            Show session tasks"
        echo ""
        echo "Templates:"
        echo "  template list               Show available templates"
        echo "  template create [name]      Create custom template"
        echo ""
        echo "Example Workflow:"
        echo "  /session-manager start feature-auth feature"
        echo "  /session-manager task feature-auth add 'Implement login'"
        echo "  /session-manager checkpoint feature-auth initial-setup"
        echo "  # Work on feature..."
        echo "  /session-manager checkpoint feature-auth login-complete"
        echo "  /session-manager task feature-auth complete 'Implement login'"
        echo ""
        echo "💡 Features:"
        echo "  • Template-based session creation"
        echo "  • Git state preservation"
        echo "  • Checkpoint and restore functionality"
        echo "  • Task tracking and progress"
        echo "  • Development environment integration"
        ;;
esac
```

Advanced session management system with templates, checkpoints, task tracking, and context preservation for development workflows.
**Security Note:** This command manages session state and git operations safely with user confirmation for destructive actions.