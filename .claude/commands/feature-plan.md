Create a comprehensive plan for implementing feature $ARGUMENTS. Execute:

1. Break down the feature into tasks:
```
mcp__think-tank__plan_tasks
```

2. Track progress with task management:
```
mcp__think-tank__list_tasks
mcp__think-tank__next_task
mcp__think-tank__complete_task
```

Creates a structured approach to feature development with trackable milestones.
**Security Note:** This command passes arguments to a script that handles its own validation and security checks.
