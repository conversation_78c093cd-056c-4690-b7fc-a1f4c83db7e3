Generate user story and QA testing specification files from user story input. Execute:
```bash
# When followed by a user story, generate specification files
# Following steps 2 and 3 from ./templates/README.md
echo "Generating user story and QA testing specification files..."

# Check if templates exist
if [[ ! -f "./templates/user-story-specification.md" ]] || [[ ! -f "./templates/qa-testing-specification.md" ]]; then
    echo "❌ Template files not found. Please ensure templates exist in ./templates/"
    exit 1
fi

# Parse user story from arguments
USER_STORY="$*"
if [[ -z "$USER_STORY" ]]; then
    echo "❌ Please provide a user story after 'spec this'"
    echo "Usage: spec-this [user story description]"
    exit 1
fi

echo "📝 Processing user story: $USER_STORY"
echo "🎯 Generating specification files based on templates..."

# Generate a simple US ID from timestamp
US_ID="US$(date +%Y%m%d.%H%M)"
TIMESTAMP=$(date +"%Y-%m-%d %H:%M:%S")

# Create user story specification
cat > "./specs/${US_ID}-user-story.md" << EOF
# 📋 ${US_ID}: ${USER_STORY}

**Story:** As a user, I want ${USER_STORY} so that I can achieve my goal.
**Steps:** Step 1 → Step 2 → Step N
**Epic:** 🎯 [To be determined]

## ✅ AC
- [ ] Given [condition], When [action], Then [outcome]
- [ ] Given [condition], When [action], Then [outcome]
- [ ] Given [error condition], When [action], Then [error handling]

## 🔗 Deps
**Stories:** None | **Ext:** None | **Tech:** ⚙️ [To be determined] | **Data:** 🗄️ [To be determined] | **Team:** 👥 [To be determined]

## 💰 Value
**Biz:** 📈 [Business value] | **UX:** ✨ [User experience improvement] | **KPI:** 📊 [Key metrics] | **Why:** 🎯 [Priority reason]

## 🛠️ Impl
**Arch:** 🏛️ [Architecture/patterns] | **Data:** 🗄️ [Schema changes] | **API:** 🔌 [Endpoints] | **Sec:** 🔒 [Auth/authz] | **Perf:** ⚡ [Requirements]

## 🧪 Test
**Unit:** 🔬 [Functions to test] | **Int:** 🔗 [Systems to integrate] | **E2E:** 🎭 [User journeys]

## ✅ DoD
- [ ] AC met | [ ] Tests pass | [ ] Security review | [ ] Perf benchmarks | [ ] Docs updated | [ ] Code review | [ ] QA complete

---
*Generated: ${TIMESTAMP}*
*Template: Use placeholders marked with [] to complete specification*
EOF

# Create QA testing specification
cat > "./specs/${US_ID}-qa-spec.md" << EOF
# 🧪 QA: ${US_ID}: ${USER_STORY}

**Goal:** 🎯 Validate that ${USER_STORY} works correctly and meets all requirements

## 🎯 Objectives
**Functional:** ⚙️ Core functionality validation | **NFR:** 📏 Performance/security/a11y | **Edge:** ⚠️ Boundary/error cases | **Security:** 🔒 Auth/authz validation

## 📋 Strategy
**Scope:** 🎯 [Define in/out scope] | **Env:** 🐳 Docker(default)/.env.test | **Tools:** 🛠️ pytest/bun/Docker/Cypress | **Data:** 📊 [Test data needs]

## 📋 Test Cases

### 🧪 Functional Tests
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| ${US_ID}-TC-01 | [Test case description] | [Steps] | [Expected outcome] | [Type] |

### ❌ Negative & Error
| ID | Description | Steps | Expected | Type |
|:---|:------------|:------|:---------|:-----|
| ${US_ID}-ERR-01 | [Error scenario] | [Steps] | [Error response] | Negative |

## 🔧 Setup
**Backend:** 🖥️ apps/backend + ./scripts/run-backend-tests.sh + pytest
**Frontend:** 🌐 bun + ./scripts/run-frontend-tests.sh + port:5173

## 📦 Deliverables
📄 QA spec + execution log + bug reports + sign-off report

---
*Generated: ${TIMESTAMP}*
*Template: Replace [] placeholders with actual test details*
EOF

echo "✅ User story and QA testing specifications written to files:"
echo "   - ./specs/${US_ID}-user-story.md"
echo "   - ./specs/${US_ID}-qa-spec.md"
```

Generate comprehensive user story and QA testing specification files following project templates.
**Security Note:** This command processes user story text and generates specification files safely.