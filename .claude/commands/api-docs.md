Create OpenAPI/GraphQL documentation for all endpoints. Generate:

1. **GraphQL Schema**: Document queries, mutations, and subscriptions
2. **REST API**: OpenAPI specification for REST endpoints
3. **Authentication**: Document auth requirements and flows
4. **Examples**: Provide request/response examples
5. **Error Codes**: Document error responses and status codes
6. **Rate Limiting**: Document any rate limiting policies

Create comprehensive, up-to-date API documentation for developers.