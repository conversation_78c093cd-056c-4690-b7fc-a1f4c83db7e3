Visual regression testing and screenshot comparison for UI components. Execute:
```bash
# Visual testing and regression detection system
ACTION="${1:-help}"
TARGET="${2:-}"
BASELINE_DIR=".visual-tests/baselines"
CURRENT_DIR=".visual-tests/current"
DIFF_DIR=".visual-tests/diffs"

case "$ACTION" in
    "capture"|"screenshot")
        if [[ -z "$TARGET" ]]; then
            echo "❌ Component or page name required"
            echo "Usage: visual-test capture [component/page-name]"
            exit 1
        fi
        
        echo "📸 Capturing Visual Test: $TARGET"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        # Create directories
        mkdir -p "$BASELINE_DIR" "$CURRENT_DIR" "$DIFF_DIR"
        
        # Check if dev server is running
        if ! curl -s http://localhost:5173 > /dev/null 2>&1; then
            echo "⚠️  Frontend dev server not running"
            echo "Starting dev server..."
            cd apps/web
            bun run dev -- --host 0.0.0.0 &
            DEV_PID=$!
            sleep 5
            cd ../..
            echo "✅ Dev server started (PID: $DEV_PID)"
        else
            echo "✅ Dev server is running"
        fi
        
        # Check if Play<PERSON> is available
        if ! command -v npx > /dev/null 2>&1; then
            echo "❌ npx not found. Install Node.js and Playwright"
            exit 1
        fi
        
        echo ""
        echo "📷 Capturing screenshots..."
        
        # Create a simple Playwright script for screenshot capture
        cat > ".visual-tests/capture-script.js" << 'EOF'
const { chromium } = require('playwright');

async function captureScreenshots() {
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  const target = process.argv[2];
  const outputDir = process.argv[3];
  
  try {
    // Navigate to the component in Storybook or main app
    const storybookUrl = `http://localhost:6006/iframe.html?id=${target}`;
    const appUrl = `http://localhost:5173`;
    
    // Try Storybook first
    try {
      await page.goto(storybookUrl, { waitUntil: 'networkidle' });
      console.log(`✅ Captured from Storybook: ${target}`);
    } catch (e) {
      // Fallback to main app
      await page.goto(appUrl, { waitUntil: 'networkidle' });
      console.log(`✅ Captured from main app: ${target}`);
    }
    
    // Wait for component to load
    await page.waitForTimeout(1000);
    
    // Capture full page
    await page.screenshot({ 
      path: `${outputDir}/${target}-full.png`,
      fullPage: true 
    });
    
    // Capture viewport
    await page.screenshot({ 
      path: `${outputDir}/${target}-viewport.png` 
    });
    
    // Capture mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.screenshot({ 
      path: `${outputDir}/${target}-mobile.png` 
    });
    
    console.log(`📸 Screenshots saved to ${outputDir}`);
    
  } catch (error) {
    console.error('❌ Screenshot capture failed:', error.message);
    process.exit(1);
  } finally {
    await browser.close();
  }
}

captureScreenshots();
EOF
        
        # Check if playwright is installed
        if ! npm list playwright > /dev/null 2>&1; then
            echo "Installing Playwright..."
            cd apps/web
            bun add -D playwright
            bunx playwright install chromium
            cd ../..
        fi
        
        # Capture screenshots
        cd apps/web
        node "../../.visual-tests/capture-script.js" "$TARGET" "../../$CURRENT_DIR"
        cd ../..
        
        echo ""
        echo "✅ Visual capture complete!"
        echo ""
        echo "📁 Screenshots saved:"
        echo "  • Full page: $CURRENT_DIR/$TARGET-full.png"
        echo "  • Viewport: $CURRENT_DIR/$TARGET-viewport.png"
        echo "  • Mobile: $CURRENT_DIR/$TARGET-mobile.png"
        echo ""
        echo "🔄 Next steps:"
        echo "  /visual-test baseline $TARGET    # Set as baseline"
        echo "  /visual-test compare $TARGET     # Compare with baseline"
        ;;
        
    "baseline"|"set-baseline")
        if [[ -z "$TARGET" ]]; then
            echo "❌ Component or page name required"
            echo "Usage: visual-test baseline [component/page-name]"
            exit 1
        fi
        
        echo "📌 Setting Visual Baseline: $TARGET"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        # Check if current screenshots exist
        if [[ ! -f "$CURRENT_DIR/$TARGET-full.png" ]]; then
            echo "❌ No current screenshots found for $TARGET"
            echo "Run: /visual-test capture $TARGET first"
            exit 1
        fi
        
        # Copy current to baseline
        mkdir -p "$BASELINE_DIR"
        
        cp "$CURRENT_DIR/$TARGET-full.png" "$BASELINE_DIR/$TARGET-full.png"
        cp "$CURRENT_DIR/$TARGET-viewport.png" "$BASELINE_DIR/$TARGET-viewport.png"
        cp "$CURRENT_DIR/$TARGET-mobile.png" "$BASELINE_DIR/$TARGET-mobile.png"
        
        # Create metadata
        cat > "$BASELINE_DIR/$TARGET-metadata.json" << EOF
{
  "component": "$TARGET",
  "baseline_date": "$(date -Iseconds)",
  "screenshots": [
    "$TARGET-full.png",
    "$TARGET-viewport.png", 
    "$TARGET-mobile.png"
  ],
  "git_commit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
  "created_by": "visual-test baseline command"
}
EOF
        
        echo "✅ Baseline set for $TARGET"
        echo ""
        echo "📁 Baseline files:"
        echo "  • Full page: $BASELINE_DIR/$TARGET-full.png"
        echo "  • Viewport: $BASELINE_DIR/$TARGET-viewport.png"
        echo "  • Mobile: $BASELINE_DIR/$TARGET-mobile.png"
        echo "  • Metadata: $BASELINE_DIR/$TARGET-metadata.json"
        echo ""
        echo "💡 This baseline will be used for future comparisons"
        ;;
        
    "compare"|"diff")
        if [[ -z "$TARGET" ]]; then
            echo "❌ Component or page name required"
            echo "Usage: visual-test compare [component/page-name]"
            exit 1
        fi
        
        echo "🔍 Visual Comparison: $TARGET"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        # Check if baseline and current exist
        if [[ ! -f "$BASELINE_DIR/$TARGET-full.png" ]]; then
            echo "❌ No baseline found for $TARGET"
            echo "Run: /visual-test baseline $TARGET first"
            exit 1
        fi
        
        if [[ ! -f "$CURRENT_DIR/$TARGET-full.png" ]]; then
            echo "❌ No current screenshots found for $TARGET"
            echo "Run: /visual-test capture $TARGET first"
            exit 1
        fi
        
        mkdir -p "$DIFF_DIR"
        
        echo "📊 Comparing screenshots..."
        
        # Create ImageMagick comparison script
        cat > ".visual-tests/compare-script.sh" << 'EOF'
#!/bin/bash
TARGET="$1"
BASELINE_DIR="$2"
CURRENT_DIR="$3"
DIFF_DIR="$4"

# Function to compare images
compare_images() {
    local variant="$1"
    local baseline="$BASELINE_DIR/$TARGET-$variant.png"
    local current="$CURRENT_DIR/$TARGET-$variant.png"
    local diff="$DIFF_DIR/$TARGET-$variant-diff.png"
    
    if command -v magick > /dev/null 2>&1; then
        # Use ImageMagick to create diff
        magick compare "$baseline" "$current" "$diff" 2>/dev/null
        local exit_code=$?
        
        if [[ $exit_code -eq 0 ]]; then
            echo "  ✅ $variant: No differences"
            return 0
        else
            echo "  ❌ $variant: Differences detected"
            echo "     Diff saved: $diff"
            return 1
        fi
    else
        # Fallback: just check file sizes
        local size1=$(wc -c < "$baseline")
        local size2=$(wc -c < "$current")
        local diff_percent=$(( (size1 - size2) * 100 / size1 ))
        
        if [[ $diff_percent -lt 5 && $diff_percent -gt -5 ]]; then
            echo "  ✅ $variant: Likely no differences (size check)"
            return 0
        else
            echo "  ⚠️  $variant: Possible differences (size diff: ${diff_percent}%)"
            return 1
        fi
    fi
}

# Compare all variants
DIFFERENCES=0

compare_images "full" || DIFFERENCES=$((DIFFERENCES + 1))
compare_images "viewport" || DIFFERENCES=$((DIFFERENCES + 1))
compare_images "mobile" || DIFFERENCES=$((DIFFERENCES + 1))

exit $DIFFERENCES
EOF
        
        chmod +x ".visual-tests/compare-script.sh"
        
        # Run comparison
        if ./.visual-tests/compare-script.sh "$TARGET" "$BASELINE_DIR" "$CURRENT_DIR" "$DIFF_DIR"; then
            echo ""
            echo "🎉 Visual Test Passed!"
            echo "No visual regressions detected for $TARGET"
        else
            DIFF_COUNT=$?
            echo ""
            echo "⚠️  Visual Test Failed!"
            echo "$DIFF_COUNT differences detected for $TARGET"
            echo ""
            echo "📁 Diff files created in: $DIFF_DIR"
            echo ""
            echo "🔍 Next steps:"
            echo "  • Review diff images manually"
            echo "  • If changes are intentional: /visual-test baseline $TARGET"
            echo "  • If changes are bugs: fix and re-test"
        fi
        
        # Create comparison report
        cat > "$DIFF_DIR/$TARGET-report.json" << EOF
{
  "component": "$TARGET",
  "comparison_date": "$(date -Iseconds)",
  "baseline_date": "$(cat "$BASELINE_DIR/$TARGET-metadata.json" | grep baseline_date | cut -d'"' -f4 2>/dev/null || echo 'unknown')",
  "differences_found": $DIFF_COUNT,
  "git_commit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
  "status": "$([ $DIFF_COUNT -eq 0 ] && echo 'passed' || echo 'failed')"
}
EOF
        ;;
        
    "report"|"status")
        echo "📊 Visual Test Report"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        # Count baselines
        BASELINE_COUNT=0
        if [[ -d "$BASELINE_DIR" ]]; then
            BASELINE_COUNT=$(find "$BASELINE_DIR" -name "*-metadata.json" | wc -l)
        fi
        
        echo "📁 Test Coverage:"
        echo "  • Components with baselines: $BASELINE_COUNT"
        
        if [[ $BASELINE_COUNT -gt 0 ]]; then
            echo "  • Baseline directory: $BASELINE_DIR"
            echo ""
            echo "🏷️ Baseline Components:"
            find "$BASELINE_DIR" -name "*-metadata.json" | while read -r file; do
                COMP_NAME=$(basename "$file" -metadata.json)
                BASELINE_DATE=$(grep baseline_date "$file" | cut -d'"' -f4 2>/dev/null || echo 'unknown')
                echo "  • $COMP_NAME (baseline: $BASELINE_DATE)"
            done
        fi
        
        echo ""
        
        # Recent comparisons
        if [[ -d "$DIFF_DIR" ]]; then
            RECENT_TESTS=$(find "$DIFF_DIR" -name "*-report.json" -mtime -1 | wc -l)
            echo "🔍 Recent Tests (24h): $RECENT_TESTS"
            
            if [[ $RECENT_TESTS -gt 0 ]]; then
                echo ""
                echo "📋 Latest Results:"
                find "$DIFF_DIR" -name "*-report.json" -mtime -1 | head -5 | while read -r report; do
                    COMP=$(grep component "$report" | cut -d'"' -f4)
                    STATUS=$(grep status "$report" | cut -d'"' -f4)
                    DATE=$(grep comparison_date "$report" | cut -d'"' -f4)
                    
                    if [[ "$STATUS" == "passed" ]]; then
                        echo "  ✅ $COMP ($DATE)"
                    else
                        echo "  ❌ $COMP ($DATE)"
                    fi
                done
            fi
        fi
        
        echo ""
        echo "💡 Quick Actions:"
        echo "  /visual-test capture [component]   # Take new screenshots"
        echo "  /visual-test compare [component]   # Check for differences"
        echo "  /visual-test cleanup              # Remove old test files"
        ;;
        
    "cleanup"|"clean")
        echo "🧹 Cleaning Visual Test Files"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        # Count files before cleanup
        CURRENT_COUNT=$(find "$CURRENT_DIR" -name "*.png" 2>/dev/null | wc -l)
        DIFF_COUNT_FILES=$(find "$DIFF_DIR" -name "*.png" 2>/dev/null | wc -l)
        
        echo "📁 Files to clean:"
        echo "  • Current screenshots: $CURRENT_COUNT"
        echo "  • Diff images: $DIFF_COUNT_FILES"
        echo ""
        
        # Clean current screenshots (keep baselines)
        if [[ -d "$CURRENT_DIR" ]]; then
            rm -rf "$CURRENT_DIR"/*
            echo "✅ Cleaned current screenshots"
        fi
        
        # Clean old diff files (older than 7 days)
        if [[ -d "$DIFF_DIR" ]]; then
            find "$DIFF_DIR" -name "*.png" -mtime +7 -delete
            find "$DIFF_DIR" -name "*.json" -mtime +7 -delete
            echo "✅ Cleaned old diff files"
        fi
        
        # Clean capture script
        rm -f ".visual-tests/capture-script.js"
        rm -f ".visual-tests/compare-script.sh"
        
        echo ""
        echo "🎯 Cleanup complete!"
        echo "Baselines preserved for future testing"
        ;;
        
    "help"|"--help"|\"-h\"|*)
        echo "📸 Visual Testing Commands"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Usage: visual-test [action] [arguments]"
        echo ""
        echo "Core Actions:"
        echo "  capture [component]     Take screenshots of component/page"
        echo "  baseline [component]    Set current screenshots as baseline"
        echo "  compare [component]     Compare current vs baseline"
        echo "  report                  Show testing status and coverage"
        echo "  cleanup                 Clean old test files"
        echo ""
        echo "Workflow Example:"
        echo "  /visual-test capture Button"
        echo "  /visual-test baseline Button"
        echo "  # Make changes to Button component"
        echo "  /visual-test capture Button"
        echo "  /visual-test compare Button"
        echo ""
        echo "💡 Features:"
        echo "  • Multi-viewport testing (desktop, mobile)"
        echo "  • Automatic diff generation"
        echo "  • Baseline management"
        echo "  • Integration with Storybook"
        echo "  • Git commit tracking"
        echo ""
        echo "Prerequisites:"
        echo "  • Node.js and npm/bun installed"
        echo "  • Playwright for screenshot capture"
        echo "  • ImageMagick for diff generation (optional)"
        echo ""
        echo "Related Commands:"
        echo "  /visual-iterate start [component]  # Rapid development"
        echo "  /storybook                        # Component showcase"
        echo "  /test frontend                    # Unit/integration tests"
        ;;
esac
```

Visual regression testing system with screenshot capture, baseline management, and automated comparison.
**Security Note:** This command uses Playwright for screenshots and ImageMagick for comparisons. All operations are local and safe.