Intelligent feedback collection and learning system for continuous improvement. Execute:
```bash
# Feedback and learning system for development workflows
ACTION="${1:-help}"
FEEDBACK_DIR=".claude-feedback"
LEARNING_DIR="$FEEDBACK_DIR/learning"
METRICS_DIR="$FEEDBACK_DIR/metrics"
SUGGESTIONS_DIR="$FEEDBACK_DIR/suggestions"

case "$ACTION" in
    "collect"|"gather")
        CATEGORY="${2:-general}"
        
        echo "📝 Collecting Development Feedback"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        # Create feedback directories
        mkdir -p "$FEEDBACK_DIR" "$LEARNING_DIR" "$METRICS_DIR" "$SUGGESTIONS_DIR"
        
        # Generate unique feedback ID
        FEEDBACK_ID="feedback-$(date +%Y%m%d-%H%M%S)"
        
        echo "🎯 Feedback Category: $CATEGORY"
        echo "📊 Gathering development metrics..."
        echo ""
        
        # Collect system metrics
        GIT_STATUS=$(git status --porcelain 2>/dev/null | wc -l)
        GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
        GIT_COMMITS_TODAY=$(git log --since="midnight" --oneline 2>/dev/null | wc -l)
        
        # Development environment metrics
        DEV_SERVER_RUNNING=$(curl -s http://localhost:5173 > /dev/null 2>&1 && echo "true" || echo "false")
        STORYBOOK_RUNNING=$(curl -s http://localhost:6006 > /dev/null 2>&1 && echo "true" || echo "false")
        
        # Recent command usage (from bash history)
        RECENT_COMMANDS=$(history | tail -20 | grep -E "/(test|db|check|run|visual)" | wc -l 2>/dev/null || echo "0")
        
        # File activity metrics
        FILES_MODIFIED_TODAY=$(find . -name "*.tsx" -o -name "*.ts" -o -name "*.py" -newermt "midnight" 2>/dev/null | wc -l)
        
        # Test execution metrics
        TEST_FILES_COUNT=$(find . -name "*test*" -o -name "*spec*" | wc -l 2>/dev/null)
        
        # Create feedback metadata
        cat > "$FEEDBACK_DIR/$FEEDBACK_ID.json" << EOF
{
  "feedback_id": "$FEEDBACK_ID",
  "category": "$CATEGORY",
  "timestamp": "$(date -Iseconds)",
  "session_context": {
    "git_branch": "$GIT_BRANCH",
    "uncommitted_changes": $GIT_STATUS,
    "commits_today": $GIT_COMMITS_TODAY,
    "dev_server_active": $DEV_SERVER_RUNNING,
    "storybook_active": $STORYBOOK_RUNNING
  },
  "activity_metrics": {
    "files_modified_today": $FILES_MODIFIED_TODAY,
    "recent_claude_commands": $RECENT_COMMANDS,
    "test_files_available": $TEST_FILES_COUNT
  },
  "environment": {
    "working_directory": "$(pwd)",
    "node_version": "$(node --version 2>/dev/null || echo 'not available')",
    "python_version": "$(python3 --version 2>/dev/null || echo 'not available')"
  }
}
EOF
        
        echo "📊 Development Metrics Collected:"
        echo "  • Git Branch: $GIT_BRANCH"
        echo "  • Uncommitted Changes: $GIT_STATUS files"
        echo "  • Commits Today: $GIT_COMMITS_TODAY"
        echo "  • Files Modified Today: $FILES_MODIFIED_TODAY"
        echo "  • Recent Claude Commands: $RECENT_COMMANDS"
        echo "  • Test Files: $TEST_FILES_COUNT"
        echo ""
        
        # Prompt for qualitative feedback based on category
        case "$CATEGORY" in
            "workflow"|"process")
                echo "🔄 Workflow Feedback Questions:"
                echo ""
                echo "1. How smooth was your development workflow today? (1-5)"
                echo "2. Which commands were most helpful?"
                echo "3. What slowed you down or caused friction?"
                echo "4. Any workflow improvements you'd suggest?"
                ;;
                
            "tools"|"commands")
                echo "🛠️ Tools & Commands Feedback:"
                echo ""
                echo "1. Which Claude commands did you use most?"
                echo "2. Were any commands confusing or hard to use?"
                echo "3. What commands are missing that you needed?"
                echo "4. How was the command documentation?"
                ;;
                
            "quality"|"testing")
                echo "✅ Quality & Testing Feedback:"
                echo ""
                echo "1. How confident are you in your code quality? (1-5)"
                echo "2. Were tests easy to write and run?"
                echo "3. Did quality checks catch real issues?"
                echo "4. Any quality process improvements needed?"
                ;;
                
            "learning"|"knowledge")
                echo "📚 Learning & Knowledge Feedback:"
                echo ""
                echo "1. How much did you learn today? (1-5)"
                echo "2. Were you blocked by knowledge gaps?"
                echo "3. Which resources were most helpful?"
                echo "4. What would help you learn faster?"
                ;;
                
            *)
                echo "📝 General Development Feedback:"
                echo ""
                echo "1. Overall development experience today? (1-5)"
                echo "2. What went well?"
                echo "3. What was challenging?"
                echo "4. Any suggestions for improvement?"
                ;;
        esac
        
        echo ""
        echo "💡 Answer these questions and save your responses:"
        echo "  /edit $FEEDBACK_DIR/$FEEDBACK_ID-responses.md"
        echo ""
        echo "When done, process feedback with:"
        echo "  /feedback-system analyze $FEEDBACK_ID"
        ;;
        
    "analyze"|"process")
        FEEDBACK_ID="${2:-}"
        
        if [[ -z "$FEEDBACK_ID" ]]; then
            echo "❌ Feedback ID required"
            echo "Usage: feedback-system analyze [feedback-id]"
            echo ""
            echo "Recent feedback:"
            find "$FEEDBACK_DIR" -name "feedback-*.json" | sort -r | head -5 | while read -r file; do
                ID=$(basename "$file" .json)
                TIMESTAMP=$(grep timestamp "$file" | cut -d'"' -f4)
                CATEGORY=$(grep category "$file" | cut -d'"' -f4)
                echo "  • $ID ($CATEGORY, $TIMESTAMP)"
            done
            exit 1
        fi
        
        FEEDBACK_FILE="$FEEDBACK_DIR/$FEEDBACK_ID.json"
        RESPONSES_FILE="$FEEDBACK_DIR/$FEEDBACK_ID-responses.md"
        
        if [[ ! -f "$FEEDBACK_FILE" ]]; then
            echo "❌ Feedback not found: $FEEDBACK_ID"
            exit 1
        fi
        
        echo "🔍 Analyzing Feedback: $FEEDBACK_ID"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        # Load feedback data
        CATEGORY=$(grep category "$FEEDBACK_FILE" | cut -d'"' -f4)
        TIMESTAMP=$(grep timestamp "$FEEDBACK_FILE" | cut -d'"' -f4)
        
        echo "📊 Feedback Analysis:"
        echo "  • ID: $FEEDBACK_ID"
        echo "  • Category: $CATEGORY"
        echo "  • Collected: $TIMESTAMP"
        echo ""
        
        # Analyze metrics
        echo "📈 Quantitative Analysis:"
        
        COMMITS_TODAY=$(grep commits_today "$FEEDBACK_FILE" | cut -d':' -f2 | tr -d ' ,')
        FILES_MODIFIED=$(grep files_modified_today "$FEEDBACK_FILE" | cut -d':' -f2 | tr -d ' ,')
        CLAUDE_COMMANDS=$(grep recent_claude_commands "$FEEDBACK_FILE" | cut -d':' -f2 | tr -d ' ,')
        
        echo "  • Development Activity Level: $([ $COMMITS_TODAY -gt 3 ] && echo "High" || [ $COMMITS_TODAY -gt 1 ] && echo "Medium" || echo "Low")"
        echo "  • File Modification Count: $FILES_MODIFIED"
        echo "  • Claude Command Usage: $CLAUDE_COMMANDS"
        
        # Check for patterns
        if [[ $CLAUDE_COMMANDS -gt 10 ]]; then
            echo "  • Pattern: Heavy Claude integration usage"
        elif [[ $CLAUDE_COMMANDS -lt 3 ]]; then
            echo "  • Pattern: Light Claude usage - may need better discovery"
        fi
        
        echo ""
        
        # Analyze qualitative responses if available
        if [[ -f "$RESPONSES_FILE" ]]; then
            echo "📝 Qualitative Analysis:"
            
            # Look for key sentiment indicators
            if grep -qi "smooth\|great\|excellent\|easy" "$RESPONSES_FILE"; then
                echo "  • Sentiment: Positive workflow experience"
            elif grep -qi "difficult\|confusing\|slow\|frustrated" "$RESPONSES_FILE"; then
                echo "  • Sentiment: Workflow friction detected"
            else
                echo "  • Sentiment: Neutral feedback"
            fi
            
            # Extract specific mentions
            echo "  • Key Topics:"
            grep -i "test\|quality" "$RESPONSES_FILE" > /dev/null && echo "    - Testing/Quality mentioned"
            grep -i "command\|tool" "$RESPONSES_FILE" > /dev/null && echo "    - Tools/Commands mentioned"
            grep -i "learn\|knowledge\|understand" "$RESPONSES_FILE" > /dev/null && echo "    - Learning/Knowledge mentioned"
            
        else
            echo "💡 No qualitative responses found"
            echo "  Add responses: /edit $RESPONSES_FILE"
        fi
        
        echo ""
        
        # Generate insights and suggestions
        echo "💡 Generated Insights:"
        
        # Create insights based on metrics
        INSIGHTS_FILE="$LEARNING_DIR/insights-$(date +%Y%m%d).md"
        
        cat >> "$INSIGHTS_FILE" << EOF

## Feedback Analysis: $FEEDBACK_ID ($TIMESTAMP)

### Metrics Summary
- Commits: $COMMITS_TODAY
- Files Modified: $FILES_MODIFIED  
- Claude Commands: $CLAUDE_COMMANDS
- Category: $CATEGORY

### Automated Insights
EOF
        
        # Generate specific insights
        if [[ $COMMITS_TODAY -eq 0 ]]; then
            echo "  • Consider smaller, more frequent commits"
            echo "- Suggestion: Use more frequent commits for better progress tracking" >> "$INSIGHTS_FILE"
        fi
        
        if [[ $CLAUDE_COMMANDS -lt 5 ]] && [[ $FILES_MODIFIED -gt 10 ]]; then
            echo "  • High file activity but low Claude usage - explore automation opportunities"
            echo "- Suggestion: More Claude commands could automate repetitive tasks" >> "$INSIGHTS_FILE"
        fi
        
        if [[ "$CATEGORY" == "workflow" ]]; then
            echo "  • Workflow feedback - focus on process improvements"
            echo "- Focus: Process optimization and workflow smoothness" >> "$INSIGHTS_FILE"
        fi
        
        echo ""
        echo "✅ Analysis complete!"
        echo "📚 Insights saved to: $INSIGHTS_FILE"
        echo ""
        echo "🎯 Next Steps:"
        echo "  • Review insights: /read $INSIGHTS_FILE"
        echo "  • Generate suggestions: /feedback-system suggest"
        echo "  • Update learning: /feedback-system learn"
        ;;
        
    "suggest"|"recommendations")
        echo "💡 Development Recommendations"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        # Analyze recent feedback patterns
        RECENT_FEEDBACK=$(find "$FEEDBACK_DIR" -name "feedback-*.json" -mtime -7 | wc -l)
        
        if [[ $RECENT_FEEDBACK -eq 0 ]]; then
            echo "🎯 Getting Started Recommendations:"
            echo ""
            echo "Since you haven't provided feedback yet:"
            echo "  • Start collecting feedback: /feedback-system collect workflow"
            echo "  • Try the exploration workflow: /workflow feature"
            echo "  • Set up session management: /session-manager start my-project"
            echo "  • Use visual development: /visual-iterate start [component]"
            exit 0
        fi
        
        echo "📊 Based on Recent Feedback ($RECENT_FEEDBACK sessions):"
        echo ""
        
        # Analyze feedback patterns
        WORKFLOW_FEEDBACK=$(find "$FEEDBACK_DIR" -name "*workflow*.json" -mtime -7 | wc -l)
        TOOLS_FEEDBACK=$(find "$FEEDBACK_DIR" -name "*tools*.json" -mtime -7 | wc -l)
        QUALITY_FEEDBACK=$(find "$FEEDBACK_DIR" -name "*quality*.json" -mtime -7 | wc -l)
        
        echo "🔄 Workflow Optimization:"
        if [[ $WORKFLOW_FEEDBACK -gt 0 ]]; then
            echo "  ✅ You're tracking workflow feedback"
            echo "  • Consider: /session-manager for better project organization"
            echo "  • Try: /plan-first before starting features"
        else
            echo "  💡 Start tracking workflow feedback"
            echo "  • Run: /feedback-system collect workflow"
        fi
        
        echo ""
        echo "🛠️ Tool Usage:"
        if [[ $TOOLS_FEEDBACK -gt 0 ]]; then
            echo "  ✅ You're evaluating tool effectiveness"
            echo "  • Review: Command usage patterns in feedback"
            echo "  • Optimize: Create aliases for frequent commands"
        else
            echo "  💡 Track which tools help most"
            echo "  • Run: /feedback-system collect tools"
        fi
        
        echo ""
        echo "✅ Quality Practices:"
        if [[ $QUALITY_FEEDBACK -gt 0 ]]; then
            echo "  ✅ You're monitoring code quality"
            echo "  • Continue: Regular quality feedback collection"
            echo "  • Enhance: Use /tdd for test-driven development"
        else
            echo "  💡 Start tracking quality metrics"
            echo "  • Run: /feedback-system collect quality"
        fi
        
        echo ""
        
        # Generate specific suggestions based on project patterns
        echo "🎯 Personalized Suggestions:"
        
        # Check project structure for specific recommendations
        if [[ -d "apps/web" ]]; then
            echo "  🎨 Frontend Development:"
            echo "    • Use /visual-iterate for rapid UI development"
            echo "    • Try /visual-test for regression testing"
            echo "    • Run /visual-analyze for design consistency"
        fi
        
        if [[ -d "apps/backend" ]]; then
            echo "  🐍 Backend Development:"
            echo "    • Use /tdd for test-driven API development"
            echo "    • Try /explore before making changes"
            echo "    • Run /check for code quality validation"
        fi
        
        if [[ -f "docker-compose.yml" ]]; then
            echo "  🐳 Docker Workflow:"
            echo "    • Use /run dev for quick environment startup"
            echo "    • Try /safe-mode for production deployments"
            echo "    • Run /dry-run before destructive operations"
        fi
        
        echo ""
        echo "📚 Learning Opportunities:"
        echo "  • Weekly feedback review: /feedback-system report"
        echo "  • Command discovery: /getting-started commands"
        echo "  • Workflow patterns: /workflow [scenario]"
        ;;
        
    "learn"|"update-knowledge")
        echo "📚 Updating Development Knowledge"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        # Analyze all feedback to extract learning patterns
        FEEDBACK_COUNT=$(find "$FEEDBACK_DIR" -name "feedback-*.json" | wc -l)
        
        if [[ $FEEDBACK_COUNT -eq 0 ]]; then
            echo "❌ No feedback data available for learning"
            echo "Start collecting feedback first: /feedback-system collect"
            exit 1
        fi
        
        echo "📊 Analyzing $FEEDBACK_COUNT feedback sessions..."
        echo ""
        
        # Create learning summary
        LEARNING_FILE="$LEARNING_DIR/learning-summary-$(date +%Y%m%d).md"
        
        cat > "$LEARNING_FILE" << EOF
# Development Learning Summary - $(date +%Y-%m-%d)

## Feedback Analysis
- Total Sessions: $FEEDBACK_COUNT
- Analysis Date: $(date)

## Key Patterns Identified

EOF
        
        # Analyze command usage patterns
        echo "🔍 Command Usage Patterns:"
        TOTAL_COMMANDS=0
        find "$FEEDBACK_DIR" -name "feedback-*.json" | while read -r file; do
            COMMANDS=$(grep recent_claude_commands "$file" | cut -d':' -f2 | tr -d ' ,')
            echo "  Session: $(basename "$file" .json) - $COMMANDS commands"
            TOTAL_COMMANDS=$((TOTAL_COMMANDS + COMMANDS))
        done
        
        # Add to learning file
        echo "### Command Usage" >> "$LEARNING_FILE"
        echo "- Average commands per session: $((TOTAL_COMMANDS / FEEDBACK_COUNT))" >> "$LEARNING_FILE"
        echo "" >> "$LEARNING_FILE"
        
        # Analyze development velocity
        echo "📈 Development Velocity Patterns:"
        find "$FEEDBACK_DIR" -name "feedback-*.json" | while read -r file; do
            COMMITS=$(grep commits_today "$file" | cut -d':' -f2 | tr -d ' ,')
            FILES=$(grep files_modified_today "$file" | cut -d':' -f2 | tr -d ' ,')
            DATE=$(grep timestamp "$file" | cut -d'"' -f4)
            echo "  $DATE: $COMMITS commits, $FILES files"
        done
        
        echo ""
        echo "🎯 Learning Insights Generated:"
        
        # Generate insights based on patterns
        if [[ $TOTAL_COMMANDS -gt 50 ]]; then
            echo "  • High Claude integration - you're power user!"
            echo "- Pattern: Heavy Claude usage indicates good tool adoption" >> "$LEARNING_FILE"
        else
            echo "  • Room to grow Claude usage - explore more commands"
            echo "- Opportunity: Increase command usage for better productivity" >> "$LEARNING_FILE"
        fi
        
        # Check for quality feedback
        QUALITY_SESSIONS=$(find "$FEEDBACK_DIR" -name "*quality*.json" | wc -l)
        if [[ $QUALITY_SESSIONS -gt 0 ]]; then
            echo "  • Quality focus - good testing practices"
            echo "- Strength: Regular quality feedback collection" >> "$LEARNING_FILE"
        fi
        
        # Add recommendations to learning file
        cat >> "$LEARNING_FILE" << EOF

## Recommended Focus Areas
1. Continue regular feedback collection
2. Experiment with underused commands
3. Focus on workflow optimization
4. Build consistent development habits

## Next Learning Goals
- [ ] Try 3 new Claude commands this week
- [ ] Improve development velocity metrics
- [ ] Enhance code quality processes
- [ ] Streamline repetitive tasks

EOF
        
        echo ""
        echo "✅ Learning update complete!"
        echo "📚 Summary saved: $LEARNING_FILE"
        echo ""
        echo "🎯 Next Steps:"
        echo "  • Review learning: /read $LEARNING_FILE"
        echo "  • Set goals: Edit learning file with specific targets"
        echo "  • Track progress: Regular feedback collection"
        ;;
        
    "report"|"summary")
        echo "📊 Feedback System Report"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        
        # Overall statistics
        TOTAL_FEEDBACK=$(find "$FEEDBACK_DIR" -name "feedback-*.json" | wc -l)
        RECENT_FEEDBACK=$(find "$FEEDBACK_DIR" -name "feedback-*.json" -mtime -7 | wc -l)
        INSIGHTS_COUNT=$(find "$LEARNING_DIR" -name "insights-*.md" | wc -l)
        
        echo "📈 System Statistics:"
        echo "  • Total Feedback Sessions: $TOTAL_FEEDBACK"
        echo "  • Recent Sessions (7 days): $RECENT_FEEDBACK"
        echo "  • Generated Insights: $INSIGHTS_COUNT"
        echo ""
        
        # Category breakdown
        echo "📋 Feedback Categories:"
        find "$FEEDBACK_DIR" -name "feedback-*.json" | while read -r file; do
            CATEGORY=$(grep category "$file" | cut -d'"' -f4 2>/dev/null || echo "unknown")
            echo "$CATEGORY"
        done | sort | uniq -c | while read -r count category; do
            echo "  • $category: $count sessions"
        done
        
        echo ""
        
        # Recent activity
        echo "🕒 Recent Activity:"
        find "$FEEDBACK_DIR" -name "feedback-*.json" -mtime -7 | sort -r | head -5 | while read -r file; do
            TIMESTAMP=$(grep timestamp "$file" | cut -d'"' -f4)
            CATEGORY=$(grep category "$file" | cut -d'"' -f4)
            ID=$(basename "$file" .json)
            echo "  • $TIMESTAMP - $CATEGORY ($ID)"
        done
        
        echo ""
        
        # Learning progress
        if [[ -d "$LEARNING_DIR" ]]; then
            echo "📚 Learning Progress:"
            LATEST_LEARNING=$(find "$LEARNING_DIR" -name "learning-summary-*.md" | sort -r | head -1)
            if [[ -f "$LATEST_LEARNING" ]]; then
                echo "  • Latest Summary: $(basename "$LATEST_LEARNING")"
                echo "  • View: /read $LATEST_LEARNING"
            else
                echo "  • No learning summaries yet"
                echo "  • Generate: /feedback-system learn"
            fi
        fi
        
        echo ""
        echo "🎯 Recommendations:"
        
        if [[ $RECENT_FEEDBACK -eq 0 ]]; then
            echo "  • Start collecting feedback regularly"
            echo "  • Try: /feedback-system collect workflow"
        elif [[ $RECENT_FEEDBACK -lt 3 ]]; then
            echo "  • Increase feedback frequency for better insights"
            echo "  • Goal: 3-5 sessions per week"
        else
            echo "  • Great feedback collection frequency!"
            echo "  • Focus: Quality improvement and learning application"
        fi
        
        echo ""
        echo "💡 Quick Actions:"
        echo "  /feedback-system collect [category]  # Collect new feedback"
        echo "  /feedback-system suggest            # Get recommendations"
        echo "  /feedback-system learn              # Update knowledge base"
        ;;
        
    "help"|"--help"|\"-h\"|*)
        echo "📝 Feedback System Commands"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo ""
        echo "Usage: feedback-system [action] [arguments]"
        echo ""
        echo "Feedback Collection:"
        echo "  collect [category]          Gather development feedback"
        echo "  analyze [feedback-id]       Process and analyze feedback"
        echo ""
        echo "Learning & Insights:"
        echo "  suggest                     Get personalized recommendations"
        echo "  learn                       Update knowledge from feedback"
        echo "  report                      Show system status and progress"
        echo ""
        echo "Categories:"
        echo "  workflow                    Development process feedback"
        echo "  tools                       Command and tool effectiveness"
        echo "  quality                     Code quality and testing"
        echo "  learning                    Knowledge and skill development"
        echo "  general                     Overall development experience"
        echo ""
        echo "Example Workflow:"
        echo "  /feedback-system collect workflow"
        echo "  # Answer feedback questions"
        echo "  /feedback-system analyze feedback-20240101-120000"
        echo "  /feedback-system suggest"
        echo "  /feedback-system learn"
        echo ""
        echo "💡 Benefits:"
        echo "  • Continuous improvement tracking"
        echo "  • Personalized development insights"
        echo "  • Command usage optimization"
        echo "  • Workflow pattern recognition"
        echo "  • Learning progress monitoring"
        ;;
esac
```

Intelligent feedback collection and learning system that analyzes development patterns and provides personalized improvement recommendations.
**Security Note:** This command only collects development metrics and user feedback - no sensitive data is accessed or stored.