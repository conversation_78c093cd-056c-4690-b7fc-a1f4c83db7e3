Quick test environment control. Execute:
```bash
case "$1" in
  "up")
    docker compose -f docker-compose.test.yml up -d
    ;;
  "down")
    shift
    docker compose -f docker-compose.test.yml down "$@"
    ;;
  "logs")
    shift
    docker compose -f docker-compose.test.yml logs -f --tail=100 "$@"
    ;;
  "shell")
    shift
    SERVICE="${1:-backend}"
    shift
    docker compose -f docker-compose.test.yml exec "$SERVICE" bash "$@"
    ;;
  "clean")
    docker compose -f docker-compose.test.yml down --volumes
    ;;
  *)
    echo "Usage: test-env [up|down|logs|shell|clean] [service]"
    echo "Examples:"
    echo "  test-env up     # Start test environment"
    echo "  test-env down   # Stop test environment"
    echo "  test-env clean  # Stop and remove volumes"
    echo "  test-env logs   # View all logs"
    echo "  test-env logs backend  # View backend test logs"
    echo "  test-env shell db  # Database shell"
    ;;
esac
```
Shortcut for test environment operations. Includes `clean` to remove test data.