Fix GraphQL enum type mismatch for $ARGUMENTS. Steps:
1. Find the enum definition in Python backend code
2. Check the corresponding GraphQL schema definition
3. Ensure they match exactly (case-sensitive)
4. Regenerate GraphQL types if needed
5. Update any tests that use the enum
Common issues: Python uses UPPER_CASE while GraphQL uses PascalCase or different naming.
**Security Note:** This command passes arguments to a script that handles its own validation and security checks.
