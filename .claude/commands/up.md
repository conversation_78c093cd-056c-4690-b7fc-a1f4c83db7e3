Start Docker containers. Execute:
```bash
# Smart environment detection
if [[ "$1" == "dev" ]]; then
    shift
    docker compose -f docker-compose.dev.yml up -d "$@"
elif [[ "$1" == "test" ]]; then
    shift
    docker compose -f docker-compose.test.yml up -d "$@"
else
    docker compose up -d $ARGUMENTS
fi
```
Starts all services in detached mode. Use `up dev` for development environment or `up test` for test environment. Specify service names after environment to start only specific containers (e.g., `up dev frontend`).
**Security Note:** This command passes arguments to docker compose which handles its own validation.
