Database operations with smart subcommands. Execute:
```bash
# Smart database command
case "$1" in
  # Interactive console
  "console"|"c"|"")
    docker compose exec db psql -U ${POSTGRES_USER} ${POSTGRES_DB}
    ;;
  
  # Migrations
  "migrate"|"m")
    shift
    ./scripts/db-migrate.sh "$@"
    ;;
  "rollback"|"r")
    shift
    cd apps/backend && alembic downgrade "${1:--1}"
    ;;
  "history"|"h")
    cd apps/backend && alembic history
    ;;
  
  # Database management
  "reset")
    echo "⚠️  This will delete all data. Continue? (y/N)"
    read -r response
    if [[ "$response" == "y" ]]; then
      ./scripts/reset-test-db.sh
    fi
    ;;
  "seed"|"s")
    shift
    ./scripts/seed.sh "$@"
    ;;
  "backup"|"b")
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    docker compose exec db pg_dump -U ${POSTGRES_USER} ${POSTGRES_DB} > "backup_${TIMESTAMP}.sql"
    echo "✅ Backup saved to backup_${TIMESTAMP}.sql"
    ;;
  
  # Environment-specific
  "test")
    docker compose -f docker-compose.test.yml exec db psql -U postgres a2a_platform_test
    ;;
  "dev")
    docker compose -f docker-compose.dev.yml exec db psql -U ${POSTGRES_USER} ${POSTGRES_DB}
    ;;
  
  # Help
  *)
    echo "Usage: db [command] [options]"
    echo ""
    echo "Commands:"
    echo "  console, c     Open database console (default)"
    echo "  migrate, m     Run migrations"
    echo "  rollback, r    Rollback migration (default: -1)"
    echo "  history, h     Show migration history"
    echo "  reset          Reset database (requires confirmation)"
    echo "  seed, s        Seed database with test data"
    echo "  backup, b      Create database backup"
    echo "  test           Connect to test database"
    echo "  dev            Connect to dev database"
    echo ""
    echo "Examples:"
    echo "  db                    # Open console"
    echo "  db migrate           # Run migrations"
    echo "  db rollback          # Rollback one migration"
    echo "  db rollback -2       # Rollback two migrations"
    echo "  db seed users        # Seed user data"
    echo "  db test              # Connect to test DB"
    ;;
esac
```
Unified database command. Run `db` alone to open console or use subcommands for specific operations.
**Security Note:** This command requires database credentials and handles sensitive operations.