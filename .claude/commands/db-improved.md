Unified database operations command. Execute:
```bash
# Smart database command with environment detection
ENV="${DB_ENV:-dev}"  # Default to dev environment

# Check for environment override
if [[ "$1" == "dev" ]] || [[ "$1" == "test" ]] || [[ "$1" == "prod" ]]; then
  ENV="$1"
  shift
fi

# Set compose file based on environment
case "$ENV" in
  "test")
    COMPOSE_FILE="docker-compose.test.yml"
    DB_NAME="a2a_test_db"
    ;;
  "prod")
    echo "Warning: Production database operations require additional confirmation"
    COMPOSE_FILE="docker-compose.yml"
    DB_NAME="a2a_db"
    ;;
  *)
    COMPOSE_FILE="docker-compose.dev.yml"
    DB_NAME="a2a_db"
    ;;
esac

# Database operations
case "$1" in
  "connect"|"")
    shift
    echo "Connecting to $ENV database..."
    docker compose -f "$COMPOSE_FILE" exec postgres psql -U a2a_user -d "$DB_NAME" "$@"
    ;;
  "migrate"|"upgrade")
    shift
    echo "Running migrations on $ENV database..."
    docker compose -f "$COMPOSE_FILE" exec backend alembic upgrade head "$@"
    ;;
  "rollback"|"downgrade")
    shift
    STEPS="${1:-1}"
    echo "Rolling back $STEPS migration(s) on $ENV database..."
    docker compose -f "$COMPOSE_FILE" exec backend alembic downgrade "-$STEPS"
    ;;
  "reset")
    shift
    echo "WARNING: This will delete all data in $ENV database!"
    read -p "Are you sure? (y/N) " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
      ./scripts/db-reset.sh "$@"
    fi
    ;;
  "seed")
    shift
    echo "Seeding $ENV database..."
    docker compose -f "$COMPOSE_FILE" exec backend python -m scripts.seed_database "$@"
    ;;
  "backup")
    shift
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_FILE="backup_${ENV}_${TIMESTAMP}.sql"
    echo "Backing up $ENV database to $BACKUP_FILE..."
    docker compose -f "$COMPOSE_FILE" exec postgres pg_dump -U a2a_user "$DB_NAME" > "$BACKUP_FILE"
    echo "Backup completed: $BACKUP_FILE"
    ;;
  "restore")
    shift
    if [[ -z "$1" ]]; then
      echo "Usage: db restore <backup_file>"
      echo "Available backups:"
      ls -la backup_*.sql 2>/dev/null || echo "No backup files found"
      exit 1
    fi
    echo "WARNING: This will overwrite the $ENV database with $1!"
    read -p "Are you sure? (y/N) " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
      docker compose -f "$COMPOSE_FILE" exec -T postgres psql -U a2a_user -d "$DB_NAME" < "$1"
      echo "Database restored from $1"
    fi
    ;;
  "status")
    shift
    echo "Database status for $ENV environment:"
    docker compose -f "$COMPOSE_FILE" exec postgres psql -U a2a_user -d "$DB_NAME" -c "\l" -c "\dt" -c "SELECT version();"
    ;;
  "shell")
    shift
    echo "Opening database shell for $ENV..."
    docker compose -f "$COMPOSE_FILE" exec postgres bash
    ;;
  "logs")
    shift
    echo "Showing database logs for $ENV..."
    docker compose -f "$COMPOSE_FILE" logs -f --tail=100 postgres
    ;;
  "help"|"--help"|"-h")
    echo "Usage: db [env] [command] [options]"
    echo ""
    echo "Environments:"
    echo "  dev  - Development database (default)"
    echo "  test - Test database"
    echo "  prod - Production database (requires confirmation)"
    echo ""
    echo "Commands:"
    echo "  connect     - Open psql console (default)"
    echo "  migrate     - Run pending migrations"
    echo "  rollback    - Rollback migrations (default: 1 step)"
    echo "  reset       - Reset database (requires confirmation)"
    echo "  seed        - Seed database with test data"
    echo "  backup      - Create timestamped backup"
    echo "  restore     - Restore from backup file"
    echo "  status      - Show database status"
    echo "  shell       - Open bash shell in postgres container"
    echo "  logs        - Show database logs"
    echo ""
    echo "Examples:"
    echo "  db                      # Connect to dev database"
    echo "  db test                 # Connect to test database"
    echo "  db migrate              # Run migrations on dev"
    echo "  db test migrate         # Run migrations on test"
    echo "  db backup               # Backup dev database"
    echo "  db restore backup.sql   # Restore from backup"
    echo "  db rollback 2           # Rollback 2 migrations"
    ;;
  *)
    echo "Unknown command: $1"
    echo "Use 'db help' for usage information"
    exit 1
    ;;
esac
```

Unified database command with smart environment detection:
- Environment aware: Automatically uses correct database for dev/test/prod
- Safety checks: Confirmations for destructive operations
- Convenient defaults: No args connects to database
- Backup/restore: Built-in database backup functionality
- Migration management: Easy upgrade/rollback operations

**Security Note:** This command includes confirmation prompts for destructive operations and validates environment selection.