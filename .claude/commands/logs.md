Show Docker container logs. Execute:
```bash
# Smart environment detection
if [[ "$1" == "dev" ]]; then
    shift
    docker compose -f docker-compose.dev.yml logs -f --tail=100 "$@"
elif [[ "$1" == "test" ]]; then
    shift
    docker compose -f docker-compose.test.yml logs -f --tail=100 "$@"
else
    docker compose logs -f --tail=100 $ARGUMENTS
fi
```
Displays recent log output from containers. Use `logs dev` or `logs test` for specific environments. Filter by service name (e.g., `logs dev backend`, `logs test db`).
**Security Note:** This command passes arguments to docker compose which handles its own validation.
