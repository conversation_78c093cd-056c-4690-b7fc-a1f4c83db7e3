Track an architectural or technical decision about $ARGUMENTS. Follow these steps:

1. Create entities for the decision, alternatives considered, and rationale:
```
mcp__think-tank__upsert_entities
```

2. Create relationships between the decision and affected components:
```
mcp__think-tank__create_relations
```

3. Add observations about implementation details and outcomes:
```
mcp__think-tank__add_observations
```

This creates a searchable record of why technical decisions were made.
**Security Note:** This command passes arguments to a script that handles its own validation and security checks.
