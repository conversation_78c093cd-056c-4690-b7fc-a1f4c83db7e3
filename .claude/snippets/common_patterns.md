# A2A Platform Common Code Patterns

This document outlines the standard coding patterns used throughout the A2A Platform to ensure consistency and maintainability.

## Table of Contents

1. [Database Access Patterns](#database-access-patterns)
2. [Authentication and Authorization](#authentication-and-authorization)
3. [Error Handling](#error-handling)
4. [State Management](#state-management)
5. [API Design](#api-design)

## Database Access Patterns

### Using SQLAlchemy ORM with FastAPI

```python
from fastapi import Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from apps.backend.api.deps import get_db
from apps.backend.models import Item
from apps.backend.schemas import ItemCreate, ItemResponse

async def create_item(
    item_in: ItemCreate, 
    db: AsyncSession = Depends(get_db)
):
    # Create new item instance
    db_item = Item(**item_in.dict())
    
    # Add to session and commit
    db.add(db_item)
    await db.commit()
    await db.refresh(db_item)
    
    return db_item

async def get_item(
    item_id: int,
    db: AsyncSession = Depends(get_db)
):
    # Use select() with future=True
    result = await db.execute(
        select(Item).where(Item.id == item_id)
    )
    db_item = result.scalars().first()
    
    if not db_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Item not found"
        )
    
    return db_item
```

### Filtering and Pagination

```python
async def get_items(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    # Query with offset and limit
    result = await db.execute(
        select(Item)
        .offset(skip)
        .limit(limit)
        .order_by(Item.created_at.desc())
    )
    
    return result.scalars().all()
```

### Transaction Management

```python
from sqlalchemy.ext.asyncio import AsyncSession

async def perform_transaction(db: AsyncSession):
    try:
        # Create object 1
        obj1 = Model1(name="test1")
        db.add(obj1)
        await db.flush()  # Flush to get ID but don't commit yet
        
        # Create related object depending on obj1
        obj2 = Model2(parent_id=obj1.id, name="test2")
        db.add(obj2)
        
        # Commit the transaction
        await db.commit()
        return True
    except Exception as e:
        # Rollback in case of error
        await db.rollback()
        raise e
```

## Authentication and Authorization

### Clerk Auth Integration

```python
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

from apps.backend.core.config import settings
from apps.backend.api.deps import get_current_user, get_db
from apps.backend.models import User

security = HTTPBearer()

async def get_current_active_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
):
    user = await get_current_user(credentials, db)
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Inactive user"
        )
    
    return user

# In FastAPI route
@router.get("/me", response_model=UserSchema)
async def read_users_me(
    current_user: User = Depends(get_current_active_user)
):
    """Get current user information."""
    return current_user
```

### Role-Based Authorization

```python
from enum import Enum
from typing import List
from fastapi import Depends, HTTPException, status

class Role(str, Enum):
    ADMIN = "admin"
    MANAGER = "manager"
    USER = "user"

def has_role(required_roles: List[Role]):
    async def _has_role(
        current_user: User = Depends(get_current_active_user)
    ):
        if current_user.role not in required_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions. Required roles: {required_roles}"
            )
        return current_user
    return _has_role

# In FastAPI route
@router.delete("/users/{user_id}")
async def delete_user(
    user_id: int,
    current_user: User = Depends(has_role([Role.ADMIN]))
):
    """Delete user (admin only)."""
    # Implementation
```

## Error Handling

### API Error Handling

```python
from fastapi import FastAPI, Request, status
from fastapi.responses import JSONResponse
from sqlalchemy.exc import IntegrityError, DBAPIError

app = FastAPI()

@app.exception_handler(IntegrityError)
async def integrity_error_handler(request: Request, exc: IntegrityError):
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content={"message": "Database integrity error", "detail": str(exc)},
    )

@app.exception_handler(DBAPIError)
async def db_error_handler(request: Request, exc: DBAPIError):
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"message": "Database error", "detail": str(exc)},
    )
```

### Custom Error Classes

```python
from fastapi import HTTPException, status

class NotFoundError(HTTPException):
    def __init__(self, entity: str, entity_id: str):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"{entity} with id {entity_id} not found"
        )

class ForbiddenError(HTTPException):
    def __init__(self, message: str = "Forbidden"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=message
        )

# Usage
if not user:
    raise NotFoundError(entity="User", entity_id=user_id)
```

### Structured API Responses

```python
from typing import Any, Dict, Generic, List, Optional, TypeVar
from pydantic import BaseModel
from pydantic.generics import GenericModel

DataT = TypeVar("DataT")

class APIResponse(GenericModel, Generic[DataT]):
    success: bool
    message: str
    data: Optional[DataT] = None
    errors: Optional[List[Dict[str, Any]]] = None

# Usage in FastAPI
@router.get("/items/{item_id}", response_model=APIResponse[ItemResponse])
async def get_item(item_id: int, db: AsyncSession = Depends(get_db)):
    try:
        item = await get_item_by_id(item_id, db)
        return APIResponse(
            success=True,
            message="Item retrieved successfully",
            data=item
        )
    except Exception as e:
        return APIResponse(
            success=False,
            message="Failed to retrieve item",
            errors=[{"detail": str(e)}]
        )
```

## State Management

### React Context Provider Pattern

```tsx
// Create a context with type safety
import { createContext, useContext, useState, ReactNode } from 'react';

interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  login: (token: string) => Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthState | undefined>(undefined);

// Provider component
export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);

  const login = async (token: string) => {
    // Implementation
    setIsAuthenticated(true);
    // Set user from token
  };

  const logout = () => {
    setIsAuthenticated(false);
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, user, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook for easy consumption
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
```

### Apollo Client Integration

```tsx
import { ApolloClient, InMemoryCache, ApolloProvider, createHttpLink } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';

// Create HTTP link
const httpLink = createHttpLink({
  uri: '/graphql',
});

// Auth link for adding token
const authLink = setContext((_, { headers }) => {
  // Get token from local storage
  const token = localStorage.getItem('token');
  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : "",
    }
  };
});

// Create Apollo client
const client = new ApolloClient({
  link: authLink.concat(httpLink),
  cache: new InMemoryCache()
});

// Provider setup
const App = () => (
  <ApolloProvider client={client}>
    <Router>
      <AuthProvider>
        <AppRoutes />
      </AuthProvider>
    </Router>
  </ApolloProvider>
);
```

## API Design

### GraphQL API Structure

```graphql
# Schema structure
type Query {
  # Get single item by ID
  item(id: ID!): Item
  
  # Get multiple items with filtering and pagination
  items(
    filter: ItemFilter
    orderBy: ItemOrderBy
    skip: Int
    limit: Int
  ): ItemConnection!
}

type Mutation {
  # Create operations
  createItem(input: CreateItemInput!): ItemPayload!
  
  # Update operations
  updateItem(input: UpdateItemInput!): ItemPayload!
  
  # Delete operations
  deleteItem(id: ID!): DeletePayload!
}

# Standard connection type for pagination
type ItemConnection {
  edges: [ItemEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type ItemEdge {
  node: Item!
  cursor: String!
}

type PageInfo {
  hasNextPage: Boolean!
  hasPreviousPage: Boolean!
  startCursor: String
  endCursor: String
}

# Standard response payload pattern
interface MutationPayload {
  success: Boolean!
  message: String
  errors: [Error!]
}

type ItemPayload implements MutationPayload {
  success: Boolean!
  message: String
  errors: [Error!]
  item: Item
}
```

### REST API Design

```python
@router.get(
    "/items",
    response_model=List[ItemSchema],
    response_model_exclude_unset=True,
    status_code=status.HTTP_200_OK,
)
async def list_items(
    response: Response,
    skip: int = 0,
    limit: int = 100,
    sort_by: str = "created_at",
    sort_desc: bool = True,
    db: AsyncSession = Depends(get_db),
):
    """
    List items with pagination and sorting.
    
    Headers:
    - X-Total-Count: Total number of items
    - Link: Pagination links
    """
    # Get total count
    count_result = await db.execute(select(func.count()).select_from(Item))
    total_count = count_result.scalar()
    
    # Set headers for pagination
    response.headers["X-Total-Count"] = str(total_count)
    
    # Generate Link header for HATEOAS
    base_url = "/items"
    links = []
    if skip + limit < total_count:
        next_link = f'<{base_url}?skip={skip+limit}&limit={limit}>; rel="next"'
        links.append(next_link)
    if skip > 0:
        prev_skip = max(0, skip - limit)
        prev_link = f'<{base_url}?skip={prev_skip}&limit={limit}>; rel="prev"'
        links.append(prev_link)
    
    if links:
        response.headers["Link"] = ", ".join(links)
    
    # Build query with dynamic sorting
    query = select(Item)
    if hasattr(Item, sort_by):
        sort_column = getattr(Item, sort_by)
        query = query.order_by(sort_column.desc() if sort_desc else sort_column.asc())
    
    query = query.offset(skip).limit(limit)
    result = await db.execute(query)
    
    return result.scalars().all()
```
