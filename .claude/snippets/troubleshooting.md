# Troubleshooting Guide

## Common Issues and Solutions

### Database Connection Issues

**Symptoms:**
- `Connection refused` errors
- `SQLSTATE[08006]` errors
- Timeout errors

**Solutions:**
```bash
# Check database container status
docker compose ps postgres

# View database logs
docker compose logs postgres --tail=50

# Restart database
docker compose restart postgres && sleep 5

# Verify connection
docker compose exec backend python check_db.py

# Check environment variables
docker compose exec backend env | grep POSTGRES

# Reset database (WARNING: destroys data)
./scripts/db-reset.sh
```

### GraphQL Errors

**Symptoms:**
- Syntax errors in generated files
- Multiline string issues
- Import errors

**Solutions:**
```bash
# Regenerate with test schema
cd apps/web && bun run codegen:test

# Check schema syntax
cd apps/backend && python -m graphql.utilities --schema schema.graphql

# Manually fix generated files
# Look for broken template literals in src/generated/

# Clear and regenerate
rm -rf apps/web/src/generated
cd apps/web && bun run codegen
```

### Permission Errors

**Symptoms:**
- `EACCES` errors
- Cannot write to directories
- Permission denied

**Solutions:**
```bash
# Fix frontend permissions
./scripts/fix-web-permissions.sh

# Fix backend permissions
sudo chown -R $(id -u):$(id -g) apps/backend/

# Fix SSL certificate permissions
./scripts/generate-ssl-certs.sh

# Fix Docker socket permissions
sudo usermod -aG docker $USER
# Then logout and login again
```

### Container Issues

**Symptoms:**
- Container exits immediately
- Restart loop
- Cannot find module

**Solutions:**
```bash
# Check container logs
docker compose logs [service] --tail=100

# Shell into failing container
docker compose run --rm [service] bash

# Rebuild container
docker compose build [service] --no-cache

# Clean restart
docker compose down
docker compose up -d

# Remove all containers and volumes (WARNING: destroys data)
docker compose down -v
docker system prune -af
```

### Test Failures

**Symptoms:**
- Tests pass locally but fail in CI
- Flaky tests
- Database state issues

**Solutions:**
```bash
# Run tests in Docker (matches CI)
./scripts/run-docker-tests.sh

# Run with fresh database
./scripts/reset-test-db.sh
./scripts/run-backend-tests.sh

# Check for timing issues
./scripts/run-backend-tests.sh --slow

# Run specific test with verbose output
./scripts/run-backend-tests.sh -vv tests/path/to/test.py::test_function

# Check test database
docker compose exec postgres psql -U a2a_user -d a2a_test_db
```

### Build Errors

**Symptoms:**
- Module not found
- TypeScript errors
- Build hangs

**Solutions:**
```bash
# Clear node_modules and reinstall
cd apps/web
rm -rf node_modules bun.lockb
bun install

# Clear Python cache
find . -type d -name __pycache__ -exec rm -rf {} +
find . -type f -name "*.pyc" -delete

# Rebuild all containers
docker compose build --no-cache

# Check for port conflicts
lsof -i :5173  # Frontend
lsof -i :8001  # Backend
```

### Migration Issues

**Symptoms:**
- Migration conflicts
- Cannot upgrade database
- Alembic errors

**Solutions:**
```bash
# Check current migration status
cd apps/backend
alembic current

# Show migration history
alembic history

# Rollback last migration
alembic downgrade -1

# Fix migration conflicts
# 1. Note the conflicting revision
# 2. Edit the migration file
# 3. Update the down_revision

# Generate new migration
alembic revision -m "Description"

# Force database to specific revision (CAREFUL)
alembic stamp head
```

### Performance Issues

**Symptoms:**
- Slow queries
- High memory usage
- Container crashes

**Solutions:**
```bash
# Check container resources
docker stats

# Analyze slow queries
docker compose exec postgres psql -U a2a_user -d a2a_db -c "SELECT * FROM pg_stat_statements ORDER BY mean_exec_time DESC LIMIT 10;"

# Check for missing indexes
./scripts/test-performance-benchmark.sh

# Increase container resources in docker-compose.yml
# Add under service:
#   deploy:
#     resources:
#       limits:
#         memory: 2G
```

### HTTPS/SSL Issues

**Symptoms:**
- Certificate errors
- Mixed content warnings
- WebSocket connection failures

**Solutions:**
```bash
# Regenerate certificates
./scripts/generate-ssl-certs.sh

# Check certificate validity
openssl x509 -in ssl-certs/cert.pem -text -noout

# Test HTTPS endpoints
curl -k https://localhost:8001/health

# Fix mixed content
# Ensure all resources use https:// or protocol-relative URLs
```

### Debugging Tips

1. **Always check logs first**
   ```bash
   docker compose logs -f --tail=100
   ```

2. **Use the debug script**
   ```bash
   ./scripts/diagnose-deploy-errors.sh
   ```

3. **Interactive debugging**
   ```bash
   # Python
   docker compose exec backend python -m pdb

   # Node.js
   docker compose exec web node --inspect
   ```

4. **Database queries**
   ```bash
   docker compose exec postgres psql -U a2a_user -d a2a_db
   ```

5. **Environment variables**
   ```bash
   docker compose exec [service] env | sort
   ```