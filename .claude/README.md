# Claude Code Integration v2.0

This directory contains configuration, templates, and documentation to enhance the Claude Code experience for the A2A Platform.

## Quick Start

### Core Unified Commands (Smart Defaults)

```bash
# Testing - Intuitive cross-application semantics
/test               # Quick feedback (backend unit tests)
/test unit          # ALL unit tests (frontend + backend)
/test integration   # ALL integration tests (frontend + backend)
/test backend       # All backend tests
/test frontend      # All frontend tests
/test all           # Complete test suite

# Database - All database operations unified
/db                 # Open console (default)
/db migrate         # Run migrations
/db reset           # Reset database
/db seed            # Seed test data
/db backup          # Create backup

# Code Quality - Unified quality checks
/check              # Run all checks (default)
/check lint         # Linting only
/check format       # Format check
/check security     # Security scan
/check fix          # Auto-fix issues

# GraphQL - All GraphQL operations
/graphql            # Generate types (default)
/graphql update     # Update schema
/graphql test       # Run GraphQL tests
/graphql validate   # Validate queries
```

### Docker Environment Commands

```bash
# Smart environment detection (dev/test/default)
/up dev             # Start development environment
/down test          # Stop test environment
/logs dev frontend  # View dev frontend logs
/shell test db      # Access test database shell

# Quick shortcuts
/dev up             # Start dev environment
/test-env clean     # Clean test environment
/dc ps              # Direct docker compose access
```

### Essential Development Commands

```bash
# Getting Started & Planning
/getting-started    # Interactive guide for new developers
/explore [path]     # Deep dive into codebase area before changes
/plan-first [type]  # Create structured plan before coding
/workflow [scenario] # Get step-by-step guidance for tasks

# Development workflow
/run dev            # Start full dev environment
/run test unit      # Run specific tests
/fresh              # Clean restart
/build              # Build Docker images

# Debugging & Analysis
/debug              # Diagnose deployment errors
/performance        # Performance analysis
/security           # Security review
```

### Workflow Shortcuts

```bash
# Development shortcuts
/spec-this [story]  # Generate user story and QA specs
/precommit          # Run precommit checks with auto-fix
/wrap-it-up         # Save context for LLM handoff
/unwrap-it          # Load saved context and continue
```

## What's New in v2.0

### 🎯 Unified Commands
- **7 test commands → 1**: `/test` with smart subcommands
- **4 database commands → 1**: `/db` for all database operations
- **Multiple quality commands → 1**: `/check` for all code quality
- **GraphQL operations → 1**: `/graphql` for schema management

### 🚀 Smart Defaults
- Commands do the right thing without arguments
- `/test` runs backend tests, `/db` opens console
- Add subcommands only when needed

### 🐳 Simple Docker Environments
- Use `dev` or `test` instead of `-f docker-compose.dev.yml`
- Example: `/up dev` instead of complex file paths
- Environment shortcuts: `/dev up`, `/test-env clean`

### 📚 Built-in Help
- Every command shows usage when given invalid input
- Consistent patterns across all commands
- Progressive disclosure: simple for common, powerful when needed

## Directory Structure

```
.claude/
├── settings.json      # Claude behavior configuration
├── commands/          # Individual command files (60+ commands)
│   ├── test.md       # Unified test command
│   ├── db.md         # Unified database command
│   ├── check.md      # Unified quality command
│   ├── graphql.md    # Unified GraphQL command
│   └── ...           # Other commands
├── templates/         # Code generation templates
├── workflows/         # Step-by-step guides
├── SECURITY.md       # Security guidelines
└── README.md         # This file
```

## Key Features

### 1. Unified Smart Commands

Core commands with intelligent subcommands:
- `/test [target]` - All testing operations
- `/db [command]` - All database operations
- `/check [type]` - All code quality checks
- `/graphql [action]` - All GraphQL operations
- `/run [task]` - Common development tasks

### 2. AI-Enhanced Commands

Leverage Think-Tank MCP integration:
- `/deep-think` - Advanced reasoning with research
- `/research` - Get authoritative answers
- `/track-decision` - Record architectural decisions
- `/feature-plan` - Create implementation plans
- `/remember` & `/recall` - Knowledge management

### 3. Templates

Pre-configured templates for common patterns:
- FastAPI routes
- GraphQL schemas and resolvers
- React components (styled and standard)
- SQLAlchemy models
- Test files

### 4. Workflows

Detailed guides for complex operations:
- Feature development
- GraphQL updates
- Container debugging
- Performance optimization
- Production deployment

### 5. Patterns

Best practices and common patterns:
- Codebase exploration
- Context management
- Testing strategies
- Security practices
- Performance optimization

## Common Workflows

### Quick Development Start

```bash
/run dev            # Start full dev environment
/db migrate         # Apply migrations
/graphql update     # Update GraphQL schema
/test               # Run tests
```

### Pre-commit Workflow

```bash
/check              # Run all quality checks
/check fix          # Auto-fix issues
/test unit          # Run unit tests
/test frontend      # Run frontend tests
```

### Database Development

```bash
/db                 # Open console
/db migrate         # Run migrations  
/db seed users      # Seed test data
/db backup          # Create backup
```

### Environment-Specific Testing

```bash
/up test            # Start test environment
/test all           # Run complete test suite
/logs test backend  # Check test logs
/down test --volumes # Clean up
```

### Feature Development

```bash
/feature-plan       # Plan implementation
/test smart         # Test only affected code
/graphql            # Generate types
/check fix          # Auto-fix issues
/test all           # Final verification
```

## Command Migration Guide

### From Old to New

| Old Command | New Command | Notes |
|-------------|-------------|-------|
| `/test-backend` | `/test` | Default runs backend |
| `/test-frontend` | `/test frontend` | Explicit frontend |
| `/test-all` | `/test all` | All test suites |
| `/migrate` | `/db migrate` | Under db command |
| `/reset-db` | `/db reset` | With confirmation |
| `/lint` | `/check lint` | Part of check |
| `/graphql-update` | `/graphql update` | Clearer naming |
| `/seed` | `/db seed` | Database operation |

### Best Practices with New Commands

#### Smart Defaults
- Run commands without arguments for common operations
- `/test` → backend tests, `/db` → console, `/check` → all checks

#### Progressive Enhancement
- Start simple: `/test`
- Add specificity when needed: `/test unit`
- Use full power when required: `/test backend --coverage --slow`

#### Environment Awareness
- Use environment keywords: `/up dev`, `/logs test`
- Shortcuts for efficiency: `/dev up`, `/test-env clean`

#### Built-in Help
- Invalid input shows usage: `/test help`
- Consistent help across all commands
- Examples included in help text

## Troubleshooting

See `.claude/snippets/troubleshooting.md` for solutions to common issues:
- Database connection problems
- GraphQL generation errors
- Container failures
- Test issues
- Permission errors

## Extending

### Adding Commands

Create a new file in `commands/[command-name].md`:
```markdown
Command description. Execute:
\```bash
# Command logic here
case "$1" in
  "subcommand")
    # Handle subcommand
    ;;
  *)
    # Show help
    ;;
esac
\```
Brief explanation of what the command does.
**Security Note:** Any security considerations.
```

### Command Design Guidelines

1. **Smart Defaults**: Make the command do something useful without arguments
2. **Subcommands**: Use case statements for related operations
3. **Help Text**: Show usage when given invalid input
4. **Consistency**: Follow patterns from existing unified commands
5. **Security**: Add appropriate security notes

### Adding Templates

Create files in `templates/` with clear examples and placeholders.

### Adding Workflows

Create markdown files in `workflows/` with step-by-step guides.

## Tips for v2.0 Commands

1. **Use Smart Defaults**: Just `/test`, `/db`, `/check` - no arguments needed
2. **Environment Keywords**: `dev` and `test` instead of file paths
3. **Explore Subcommands**: Run commands with no args to see options
4. **Chain Commands**: `/check fix && /test unit`
5. **Use Shortcuts**: `/dev up` instead of `/up dev`
6. **Built-in Help**: Any invalid input shows usage

## Support

For issues or suggestions:
1. Check troubleshooting guide
2. Review existing patterns
3. Consult team documentation
4. Ask Claude for help with `/think`