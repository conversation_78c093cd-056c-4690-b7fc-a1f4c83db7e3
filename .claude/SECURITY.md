# Claude Commands Security Guidelines

This document outlines security guidelines for Claude commands and scripts in the A2A Platform.

## Key Principles

1. **Input Validation**: All commands that accept `$ARGUMENTS` must validate and sanitize input
2. **Least Privilege**: Commands should only have access to the minimum resources needed
3. **Audit Trail**: All commands that modify system state should log their actions
4. **Fail Safe**: Commands should fail securely and provide clear error messages
5. **No Secret Exposure**: Commands must never log or expose sensitive information

## Security Patterns

### Safe Argument Handling
- Always quote variables: `"$ARGUMENTS"` not `$ARGUMENTS`
- Validate input before processing: check for expected patterns
- Use parameter expansion safely: `${ARGUMENTS:-default}`
- Avoid direct shell interpretation of user input

### Command Construction
- Prefer direct command execution over `eval` when possible
- If `eval` is necessary, ensure the command is built from trusted components only
- Use arrays for complex commands instead of string concatenation
- Validate all components before executing

### Script Security
- Start all scripts with `set -euo pipefail` (Bash) or `set -e` (other shells)
- Use absolute paths for critical operations
- Check for required environment variables before proceeding
- Implement proper error handling and cleanup

## Script Requirements

### Required Headers
All shell scripts must include:
```bash
#!/bin/bash
set -euo pipefail
```

### Documentation Requirements
Commands that use `$ARGUMENTS` must include:
- A "Security Note" explaining argument handling
- Clear documentation of expected input format
- Examples of safe usage

### Validation Requirements
- Check file existence before operations
- Validate environment variables
- Confirm destructive operations with user prompts
- Implement timeouts for long-running operations

## Dangerous Patterns to Avoid

### Shell Injection
- `eval $ARGUMENTS` ❌
- `bash -c "$ARGUMENTS"` ❌
- `$(echo $ARGUMENTS)` ❌
- `` `$ARGUMENTS` `` ❌

### File Operations
- `rm $ARGUMENTS` ❌ (use `rm "$ARGUMENTS"` ✅)
- `cd $ARGUMENTS` ❌ (use `cd "$ARGUMENTS"` ✅)
- Direct file writes without validation ❌

### Command Execution
- Unquoted variable substitution in commands ❌
- Dynamic command construction from user input ❌
- Missing error handling ❌

## Security Testing

### Automated Validation
Run the security validation script regularly:
```bash
./scripts/validate-claude-commands.sh
```

This script checks for:
- Dangerous shell injection patterns
- Missing script references
- Unquoted argument usage
- Script security best practices
- Required security documentation

### Manual Review Process
1. Review all new commands for security implications
2. Test commands with malicious input patterns
3. Verify proper error handling
4. Check for information disclosure
5. Validate against OWASP command injection guidelines

## Incident Response

### If a Security Issue is Found
1. Immediately disable the affected command
2. Document the issue and potential impact
3. Implement a fix following these guidelines
4. Test the fix thoroughly
5. Re-enable the command after validation

### Reporting Security Issues
- Security issues should be reported immediately
- Include details about the vulnerability
- Provide recommendations for fixes
- Test proposed solutions before implementation

## Maintenance

### Regular Security Updates
- Review and update these guidelines quarterly
- Update validation scripts to catch new patterns
- Review all commands for compliance with current standards
- Update documentation to reflect security best practices

### Training and Awareness
- All developers must understand these security guidelines
- Regular security training on command injection prevention
- Code review process must include security considerations
- Security testing must be part of the development workflow

## References

- [OWASP Command Injection Prevention](https://owasp.org/www-community/attacks/Command_Injection)
- [Bash Security Best Practices](https://mywiki.wooledge.org/BashGuide/Practices#Security)
- [Shell Script Security Guidelines](https://google.github.io/styleguide/shellguide.html#security)

---

This document is a living guideline and should be updated as new security patterns emerge or requirements change.